<!DOCTYPE html>
<html lang="en">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="DMO_Styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
    
    <script>
        function open_DMO()
		{
			var DMO_Val = document.getElementById("DMO_Number").value ;

			if (DMO_Val != '')
			{
				document.getElementById("dmo_admin").src="DMO_Admin_Modification_Form.php?dmo=" + DMO_Val;
			} else {
				document.getElementById("dmo_admin").src="";
			}
		}
	</script>

    </script>
</head>


<!-------------------->
<!-- INITI MSG_USER -->
<!-------------------->
<?php  $msg_conf=""; ?>

<!---------------------------------------->
<!-- CREATION & SUPPRESSION UTILISATEUR -->
<!---------------------------------------->
<?php
    // ---------------------
    // ---  SUPPRESSION  ---
    // ---------------------

    if (isset ($_POST['Delete_user']) && isset ($_POST['Picked_UserName']) && (($_POST['Picked_UserName'])!="") )
    {
        //Préparation des inputs de l'utilisateur
        $user=$_POST['Picked_UserName'];
        
        //Connexion à la Base
        include('../DMO_Connexion_DB.php');
        
        //Préparation de la requete SQL
        $sql = 'DELETE FROM tbl_user WHERE Fullname ="'.$user.'";';
        
        // Query execution
        $result = $mysqli_dmo->query($sql);
        mysqli_close($mysqli_dmo);
        
        $msg_conf='User '.$user.' deleted!';
    }


    // ------------------
    // ---  CREATION  ---
    // ------------------
        
    if (isset($_POST['Create_user']) && (isset ($_POST['SCM_ID'])) && (($_POST['SCM_ID'])!="") && ((isset($_POST['Fullname'])))  && (($_POST['Fullname'])!="")  && ((isset($_POST['email'])))  && (($_POST['email'])!=""))
    {
        
       include('../DMO_Connexion_DB.php');

        $SCM_ID=$_POST['SCM_ID'];
        $email=$_POST['email'];
        $Fullname=$_POST['Fullname'];
        $Department=$_POST['Department_user'];
        
        
        if (($_POST['ID_PC'])!="")
        {
            $ID_PC=$_POST['ID_PC'].'$';
        } else {
                $ID_PC="";
        }
        
        $sql_1 = 'INSERT INTO tbl_user VALUES ("0","'.$SCM_ID.'","'.$Fullname.'","'.$email.'","'.$ID_PC.'","'.$Department.'");';

        
		$resultat = $mysqli_dmo->query($sql_1); 

        mysqli_close($mysqli_dmo);
 
        $msg_conf='New User '.$Fullname.' created!';
            
    } 

?>

<!------------------------->
<!-- GESTION DEPARTEMENT -->
<!------------------------->

    <!-------------->
    <!-- CREATION -->
    <!-------------->
    <?php

    if ((isset($_POST['Department_Update']) && ($_POST['department_input'])!=""))
    {

        include('../SCM_Connexion_DB.php');

        $department=$_POST['department_input'];
        $sql_1 = 'INSERT INTO tbl_department
                VALUES ("0","'.$department.'");';

        $resultat = $mysqli_scm->query($sql_1);

         mysqli_close($mysqli_scm);

        $msg_conf='New department '.$department.' created!';

    }


    // ---------------------
    // ---  SUPRRESSION  ---
    // ---------------------

    if ((isset($_POST['Department_Delete']) && ($_POST['department_input'])!=""))
    {

        //Connexion à BD
        include('../SCM_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $department=$_POST['department_input'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_department WHERE Department = "'.$department.'";';

        $resultat = $mysqli_scm->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli_scm);

        // Message confirmation
        $msg_conf='Departement '.$department.' deleted !';

    }
    ?>

<!------------------->
<!-- PRODUCT RANGE -->
<!------------------->

    <!-------------->
    <!-- CREATION -->
    <!-------------->
    <?php
        
        if (isset($_POST['Create_product_range']) && (isset ($_POST['New_product_range'])) && (($_POST['New_product_range'])!="") && (($_POST['New_product_range_division'])!="") && (($_POST['New_product_range_division'])!="%")){
            
            //Connexion à BD
            include('../DMO_Connexion_DB.php');
            
            $new_PR=$_POST['New_product_range'];
            $new_PR_Div=$_POST['New_product_range_division'];
            
            //On prépare la commande sql d'insertion
            //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
            $sql_1 = 'INSERT INTO tbl_product_range VALUES ("0","'.$new_PR.'","'.$new_PR_Div.'")';
            //print_r($sql_1);
            
            $resultat = $mysqli_dmo->query($sql_1); 
        
            // on ferme la connexion
            mysqli_close($mysqli_dmo);
            

            // Message confirmation
            $msg_conf='Product range '.$new_PR.' created !';
        }

        
        // -----------------------
		// ---   SUPPRESSION   ---
		// -----------------------

            if (isset($_POST['Delete_product_range']) && isset ($_POST['Picked_Product_Range']) && (($_POST['Picked_Product_Range'])!=""))
            {
                //Préparation des inputs de l'utilisateur
                $Product_Range=$_POST['Picked_Product_Range'];
                
                //Connexion à la Base
                include('../DMO_Connexion_DB.php');
                
                //Préparation de la requete SQL
                $sql = 'DELETE FROM tbl_product_range WHERE Product_Range ="'.$Product_Range.'";';
                
                // Query execution
                $result = $mysqli_dmo->query($sql);
                mysqli_close($mysqli_dmo);
                
                $msg_conf= $Product_Range . 'deleted';
            }
        ?>

<!------------->
<!-- PROJET -->
<!------------>

    <!-------------->
    <!-- DELETION -->
    <!-------------->
    <?php
    if (isset ($_POST['Delete_OTP']) && isset ($_POST['Picked_OTP']) && (($_POST['Picked_OTP'])!="") )
    {
        //Préparation des inputs de l'utilisateur
        $OTP=$_POST['Picked_OTP'];
        
        //Connexion à la Base
        include('../SCM_Connexion_DB.php');
        
        //Préparation de la requete SQL
        $sql = 'DELETE FROM tbl_project WHERE OTP ="'.$OTP.'";';
        
        // Query execution
        $result = $mysqli_scm->query($sql);
        mysqli_close($mysqli_scm);
        
        $msg_conf = $OTP .' deleted';
    }



    // ------------------
    // ---  CREATION  ---
    // ------------------
        
    if (isset($_POST['Create_Project']) && (isset ($_POST['Create_Project'])) && (($_POST['OTP'])!="") && (isset($_POST['OTP']))  && (($_POST['Title'])!="")  && (isset($_POST['Title'])))
    {
        
        //Connexion à BD
        include('../SCM_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        // Get user input values and prepare them for SQL query
        $OTP=$_POST['OTP'];
        $Title=$_POST['Title'];
        $Project_Manager=$_POST['PM'];
        
        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_project VALUES ("0","'.$OTP.'","'.$Title.'","'.$Project_Manager.'");';
        //print_r($sql_1);
        
        $resultat = $mysqli_scm->query($sql_1); 

        // on ferme la connexion
        mysqli_close($mysqli_scm);
        
        // Confirmation à l'utilisateur du numéro de DMO créée
        $msg_conf = ' New project '.$OTP.' created!';
            
    } 
    ?>
    
<!------------------->
<!--  CATEGORY EX  -->
<!------------------->

    <!--------------->
    <!--  CREATION -->
    <!--------------->
    <?php
	
    if (isset($_POST['Create_Ex']) && (isset ($_POST['New_Ex'])) && (($_POST['New_Ex'])!="")){
        
        //Connexion à BD
        include('../DMO_Connexion_DB.php');
        
        $new_Ex=$_POST['New_Ex'];
        
        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_ex VALUES ("0","'.$new_Ex.'")';
        //print_r($sql_1);
        
        $resultat = $mysqli_dmo->query($sql_1); 
    
        // on ferme la connexion
        mysqli_close($mysqli_dmo);
        
        // Confirmation à l'utilisateur du numéro de DMO créée
        $msg_conf = ' New category '.$new_Ex.'created !';
    }

    
    
    // ------------------
    // --  SUPPRESSION --
    // ------------------

        if (isset($_POST['Delete_Ex']) && isset ($_POST['Picked_Ex']) && (($_POST['Picked_Ex'])!=""))
        {
            //Préparation des inputs de l'utilisateur
            $Ex=$_POST['Picked_Ex'];
            
            //Connexion à la Base
            include('../DMO_Connexion_DB.php');
            
            //Préparation de la requete SQL
            $sql = 'DELETE FROM tbl_ex WHERE Ex ="'.$Ex.'";';
            
            // Query execution
            $result = $mysqli_dmo->query($sql);
            mysqli_close($mysqli_dmo);
            
            $msg_conf = 'Categroy ' . $Ex .' deleted';
        }
    ?>

<!---------------->
<!--  DOCUMENT  -->
<!---------------->

    <!--------------->
    <!--  CREATION -->
    <!--------------->
    <?php
	
    if (isset($_POST['Create_doc_type']) && (isset ($_POST['Doc_Name'])) && (($_POST['Doc_Name'])!="")){
        
        //Connexion à BD
        include('../DMO_Connexion_DB.php');
        
        $doc_name=$_POST['Doc_Name'];
		$doc_type=$_POST['Doc_Type'];
		$doc_desc=$_POST['Doc_Description'];
		$doc_div=$_POST['Doc_Division'];
        
        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_document VALUES ("0","'.$doc_name.'","'.$doc_type.'","'.$doc_desc.'","'.$doc_div.'")';
        //print_r($sql_1);
        
        $resultat = $mysqli_dmo->query($sql_1); 
    
        // on ferme la connexion
        mysqli_close($mysqli_dmo);
        
        // Confirmation à l'utilisateur du numéro de DMO créée
        $msg_conf = ' New document '.$doc_name.' created for '.$doc_type.'!';
    }

    
    
    // ------------------
    // --  SUPPRESSION --
    // ------------------

        if (isset($_POST['Delete_Doc']) && isset ($_POST['Picked_Doc']) && (($_POST['Picked_Doc'])!=""))
        {
            //Préparation des inputs de l'utilisateur
            $doc_type_id=$_POST['Picked_Doc'];
            
            //Connexion à la Base
            include('../DMO_Connexion_DB.php');
            
            //Préparation de la requete SQL
            $sql = 'DELETE FROM tbl_document WHERE ID ="'.$doc_type_id.'";';
            
            // Query execution
            $result = $mysqli_dmo->query($sql);
            mysqli_close($mysqli_dmo);
            
            $msg_conf = 'Document deleted';
        }
    ?>

<!---------------->
<!--  TYPE  -->
<!---------------->

    <!--------------->
    <!--  CREATION -->
    <!--------------->
    <?php
	
    if (isset($_POST['Create_doc_type']) && (isset ($_POST['Type_Name'])) && (($_POST['Type_Name'])!="")){
        
        $type_name=$_POST['Type_Name'];
		$type_desc=$_POST['Type_Description'];
		$type_div=$_POST['Type_Division'];
        
		include('../DMO_Connexion_DB.php');
        $sql_1 = 'INSERT INTO tbl_type VALUES ("0","'.$type_name.'","'.$type_desc.'","'.$type_div.'")';
        $resultat = $mysqli_dmo->query($sql_1); 
    
        mysqli_close($mysqli_dmo);
        
        $msg_conf = ' New type '.$type_name.'created for '.$type_div.' division!';
    }

    
    
    // ------------------
    // --  SUPPRESSION --
    // ------------------

        if (isset($_POST['Delete_Doc_Type']) && isset ($_POST['Picked_Doc_Type']) && (($_POST['Picked_Doc_Type'])!=""))
        {
            $doc_type_id=$_POST['Picked_Doc_Type'];

            $sql = 'DELETE FROM tbl_type WHERE ID ="'.$doc_type_id.'";';
            
            include('../DMO_Connexion_DB.php');
            $result = $mysqli_dmo->query($sql);
            mysqli_close($mysqli_dmo);
            
            $msg_conf = 'Type deleted.';
        }
    ?>


<body>

<form name="name_form" method="post" action="" enctype="multipart/form-data">
    <table id="t10" border=0>
		 <tr>    
            <td style="padding-bottom:10px">
				<div id="Section_Title" style="font-style: small-caps;font-size:15px;">
					Administrator form
				</div>
            </td>
			<td colspan=3>
                <div id="FilterTitle" style="font-style:italic;color:red;font-weight:bold;text-align:right">
                    <?php if ($msg_conf!=""){echo $msg_conf;}?>
                </div>
            </td>
		</tr>
		
		<tr>    
            <td>
				<div id="Section_Title">
					DMO Management
				</div>
            </td>
			<td colspan=3></td>
			<td style="width:calc(100vw - 700px);padding-left:30px;vertical-align:top;" rowspan=47>
				<iframe id="dmo_admin"	frameborder=0 style="border-left:1px solid black;background-color:transparent;width:100%;height:calc(100vh + 150px)"></iframe>
			</td>
        </tr>

		
        <tr>
            <td style="width: 200px">
                <div id="Body" style="text-indent:25px;">
                    Pick a DMO to edit or delete it:
                </div>
                
            </td>
            <td>
                <div id="InpBox">
                    <select id="DMO_Number" onchange="open_DMO()" style="text-align:center;height:21px;width:140px" name="DMO_Number" type="submit">
                    <option value=""></option><br/> 
                    <?php
                        include('../DMO_Connexion_DB.php');
                        $requete = "SELECT DISTINCT DMO FROM tbl_dmo ORDER BY dmo DESC;";
                        $resultat = $mysqli_dmo->query($requete);
                        while ($row = $resultat->fetch_assoc())
                        {
							$sel="";
							if (isset($_POST['DMO_Number']) && $row['DMO']==$_POST['DMO_Number'])
							{
								$sel="SELECTED";
							} 
                            echo'<option value ="'.$row['DMO'].'" '.$sel.'>'.$row['DMO'].'</option><br/>'; 
                        }
                        mysqli_close($mysqli_dmo);
                    ?>
                    </select>
                </div>
            </td>
            
        </tr>

		<tr>
			<td colspan=4><hr></td>
		</tr>
			
        <tr>
            <td colspan=4>
                <div id="body" style="font-weight:bold">
                    Departement
                </div>
            </td>
        </tr>

        <tr>
            <td style="vertical-align:middle">
                <div id="Body" style="text-indent:25px;">
                    Departments <font color=red> *</font>:
                </div>
            </td>
            <td style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="department" name="department_input" style="font-size:12px;background-color:white;height:16px;width:150px">
                    <datalist name="" id="department">
                        <?php
                        include('../SCM_Connexion_DB.php');
                        $requete_dep = 'SELECT Department
                            FROM tbl_department
                            ORDER BY department ASC';
                        $resu = $mysqli_scm->query($requete_dep);
                        while ($row = $resu->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Department'].'">'.$row['Department'].'</option><br/>';
                        }
                        $mysqli_scm->close();
                        ?>
                    </datalist>

                </div>
            </td>
			<td>
                <input type="submit"  class="btn blue2" name="Department_Update" value="Create" title="Création d'un nouveau département" style="font-size:12px; width: 80px;"/>
            </td>
            <td>
				<input type="submit" class="btn white_red" name="Department_Delete" value="Delete" style="text-align:center;vertical-align:middle;width:80px;height:20px" title="Supprime le département sélectionné"/>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <hr>
            </td>
        </tr>	
		
        <tr>
            <td colspan=4>
                <div id="Body">
                    <b>User Management</b>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="Body" style="text-indent:25px;">
                    SCM ID<font color=red> *</font>:
                </div>
            </td>
            <td>
                <div id="body">
                    <input type="text" style="font-size:12px;height:20px" size=16 name="SCM_ID" id="SCM_ID_id" placeholder="XXXXX" title="SCM ID - Mandatory">
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="Boody" style="text-indent:25px;">
                    Nom<font color=red> *</font>:
                </div>
            </td>
            <td>
                <div id="body">
                    <input type="text" style="font-size:12px;height:20px" size=16 name="Fullname" id="Fullname" placeholder="DUPONT M." title="Nom de famille en majuscule, espace, premiere lettre du prenom en majuscule, point - Mandatory">
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="body"  style="text-indent:25px">
                    Adresse Email<font color=red> *</font>:
                </div>
            </td>
            <td>
                <div id="body">
                    <input type="email" style="font-size:12px;height:20px" size=26 name="email" id="email" title="full email address - Mandatory" placeholder="<EMAIL>">
                </div>
            </td>
        </tr>
		<tr>
            <td>
                <div id="body" style="text-indent:25px">
                    Departement<font color=red> *</font>:
                </div>
            </td>
            <td>
                <div id="body">
                    <select type="submit" style="font-size:12px;height:24px;width:130px" name="Department_user"  id="Department_user_id" title="Département de rattachement de l'utilisateur - Mandatory">
                        <option value=""></option>
                        <?php
                            include('../DMO_Connexion_DB.php');
                            $requete = "SELECT DISTINCT Department FROM tbl_department ORDER BY Department ASC;";
                            $resultat = $mysqli_dmo->query($requete);
                            while ($row = $resultat->fetch_assoc())
                            {
                                echo'<option value ="'.$row['Department'].'">'.$row['Department'].'</option><br/>'; 
                            }
                            mysqli_close($mysqli_dmo);
                        ?>
                    </select>
                </div>
            </td>
			<td></td>
			<td>
                <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2"  id="Create_user_id" name="Create_user" value="Create" title="Créer le nouvel utilsateur avec les informations renseignées."/>
            </td>
        </tr>
		<tr HIDDEN>
			<td>
				<div id="FilterTitle_User">
					PC ID:
				</div>
			</td>
			<td>
				<div id="InpBox_User">
					<input type="text" size=16 name="ID_PC" title="PC ID - Optional" value="" placeholder="XXXXXXXX">
				</div>
			</td>
			
		</tr>

		<tr style="padding-bottom:20px">
			<td></td>
			<td colspan=2><hr style="margin-top:-5px;margin-bottom:-5px">
			</td>
		</tr>

        <tr>
            <td>
                <div id="body" style="text-indent:25px">
                    Username<font color=red> *</font>:
                </div>
            </td>
            <td>
                <div id="InpBox_User">
                    <select type="submit" style="font-size:12px;width:130px;height:23px" name="Picked_UserName"  id="Picked_UserName" title="Username of the user - Mandatory">
                        <option value=""></option><br/>
                        <?php
                            include('../DMO_Connexion_DB.php');
                            $requete_1 = 'SELECT Fullname, Department FROM tbl_user GROUP BY Fullname ORDER BY Fullname ASC;';
                            $resultat_1 = $mysqli_dmo->query($requete_1);
                            while ($row_1 = $resultat_1->fetch_assoc())
                            {
                                echo'<option value ="'.$row_1['Fullname'].'" title="'.$row_1['Department'].'">'.$row_1['Fullname'].'</option><br/>'; 
                            }
                            mysqli_close($mysqli_dmo);
                        ?>
                    </select>
                </div>
            </td>
			<td>
			</td>
           <td>
                <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn white_red"  id="" name="Delete_user" value="Delete" title="Supprimer l'utilsateur sélectionné."/>
            </td>
        </tr>

		<tr>
            <td colspan=4>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="Body">
                    <b>Product Range Management</b>
                </div>
            </td>
        </tr>
       
        <tr>
            <td>
                <div id="body"  style="text-indent:25px">
                    Indicate a new range to create:
                </div>
            </td>
            <td style="vertical-align:middle">
                <div id="InpBox">
                    <input type="text" size=16 style="width:80px;height:17px" name="New_product_range" value="" placeholder="range name">

                    <select name="New_product_range_division" type="submit" style="width:100px; height:23px" placeholder="Division"> 
                    <option value=""></option>

                    <?php
                        include('../SCM_Connexion_DB.php');
                        $requete = "SELECT DISTINCT Division, Description FROM tbl_division ORDER BY Division ASC;";
                        print_r($requete);
                        $resultat = $mysqli_scm->query($requete);
                        while ($row = $resultat->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Division'].'" title="'.$row['Description'].'">'.$row['Division'].'</option><br/>'; 
                        }
                        mysqli_close($mysqli_scm);
                    ?>
                    </select>
                </div>
            </td>
			<td></td>
            <td>
                <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px"  class="btn blue2" name="Create_product_range" value="Create" title="Create the product range"/>
            </td>
        </tr>
		<tr>
				<td></td>
				<td colspan=2><hr style="margin-top:0px;margin-bottom:0px"></td>
			</tr>
		 <tr>
            <td>
                <div id="body" style="text-indent:25px">
                    Pick a product range to delete:
                </div>
            </td>
            <td>
                <div id="InpBox">
                    <select name="Picked_Product_Range" type="submit" style="width:120px;height:23px"> 
                    <option value=""></option>
                    <?php
                        include('../DMO_Connexion_DB.php');
                        $requete = "SELECT DISTINCT Product_Range FROM tbl_product_range ORDER BY Product_Range ASC;";
                        $resultat = $mysqli_dmo->query($requete);
                        while ($row = $resultat->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Product_Range'].'">'.$row['Product_Range'].'</option><br/>'; 
                        }
                        mysqli_close($mysqli_dmo);
                    ?>
                    </select>
                </div>
            </td>
			<td>
			</td>
			<td>
                <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn white_red"  id="" name="Delete_product_range" value="Delete" title="Supprimer la gamme de produit selectionné."/>
            </td>
        </tr>

		<tr>
            <td colspan=4>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="Body">
					<b>Project Management:</b>
                </div>
            </td>

        </tr>    
		
		
        <tr>
            <td>
                <div id="body" style="text-indent:25px">
                    OTP:
                </div>
            </td>
            <td>
                <div id="body">
                    <input type="text" size=10 name="OTP" placeholder="PXXXXXX" title="format PYYXXX - Mandatory">
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="body"  style="text-indent:25px">
                    Project Title:
                </div>
            </td>
            <td>
                <div id="body">
                    <input type="text" style="height:16px;size=16" name="Title">
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="body"  style="text-indent:25px">
                    Project Manager:
                </div>
            </td>
            <td>
                <div id="InpBox_User">
                <select name="PM" type="submit" style="height:23px"> 
                <option value=""></option>
                <?php
                    include('../SCM_Connexion_DB.php');
                    $requete = "SELECT DISTINCT Fullname FROM tbl_user ORDER BY Fullname ASC;";
                    $resultat = $mysqli_scm->query($requete);
                    while ($row = $resultat->fetch_assoc())
                    {
                        echo'<option value ="'.$row['Fullname'].'">'.$row['Fullname'].'</option><br/>'; 
                    }
                    mysqli_close($mysqli_scm);
                ?>
                </select>
                </div>
            </td>
			<td>
			</td>
            <td>
                <input type="submit" class="btn blue2" style="width:80px;height:20px" name="Create_Project" value="Create" title="Create the project"/>
            </td>
        </tr>
		
		
		
		<tr>
            <td colspan=4>
                <hr>
            </td>
        </tr>
		
		<tr>
			<td colspan=4>
				<div id="Body">
					<b>Document Management</b>
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<div id="Body" style="text-indent:25px;">
					Document Name<font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="body">
					<input type="text" style="font-size:12px;height:20px" size=16 name="Doc_Name" id="Doc_Name_id" placeholder="NU, FI, etc..." title="Document name such as NU, FI, drawing, DEO, Drawing etc...">
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<div id="body" style="text-indent:25px">
					Document Type<font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="body">
					<select type="submit" style="font-size:12px;height:24px;width:130px" name="Doc_Type"  id="Doc_Type_id" title="Engineering, Assy. Method, Lab. Method document - Mandatory">
						<option value=""></option>
						<?php
							include('../DMO_Connexion_DB.php');
							$requete = "SELECT DISTINCT Type, Description FROM tbl_type ORDER BY Type ASC;";
							$resultat = $mysqli_dmo->query($requete);
							while ($row = $resultat->fetch_assoc())
							{
								echo'<option value ="'.$row['Type'].'" title="'.$row['Description'].'">'.$row['Type'].'</option><br/>'; 
							}
							mysqli_close($mysqli_dmo);
						?>
					</select>
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<div id="Body" style="text-indent:25px;">
					Description <font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="body">
					<input type="text" style="font-size:12px;height:19px" size=20 name="Doc_Description" id="Doc_Description_id" placeholder="" title="Description of the document. Ex: NU = Installation procedure, Drawing = single part, BoM, Datasheet ...">
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<div id="body" style="text-indent:25px">
					Division <font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="body">
					<select type="submit" style="font-size:12px;height:24px;width:130px" name="Doc_Division"  id="Doc_Division_id" title="Division the document is owned by - Default = ALL ">
						<option value=""></option>
						<?php
							include('../SCM_Connexion_DB.php');
							$requete = "SELECT DISTINCT * FROM tbl_division ORDER BY Division ASC;";
							$resultat = $mysqli_scm->query($requete);
							while ($row = $resultat->fetch_assoc())
							{
								echo'<option value ="'.$row['Division'].'" title="'.$row['Description'].'">'.$row['Division'].'</option><br/>'; 
							}
							mysqli_close($mysqli_scm);
						?>
						<option value="ALL" SELECTED>ALL</option>
					</select>
				</div>
			</td>
			<td></td>
			<td>
				<input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2"  id="Create_doc_type_id" name="Create_doc_type" value="Create" title="Créer le nouveau type de document avec les informations renseignées."/>
			</td>
		</tr>

		<tr>
			<td></td>
			<td colspan=2><hr style="margin-top:5px;margin-bottom:5px"></td>
		</tr>

		<tr>
			<td>
				<div id="body" style="text-indent:25px">
					Document<font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="InpBox_User">
					<select type="submit" style="font-size:12px;width:250px;height:23px" name="Picked_Doc"  id="Picked_Doc_id" title="Document you want to remove from the list">
						<option value=""></option><br/>
						<?php
							include('../DMO_Connexion_DB.php');
							$requete_1 = 'SELECT * FROM tbl_document ORDER BY Type ASC, Document ASC;';
							$resultat_1 = $mysqli_dmo->query($requete_1);
							while ($row_1 = $resultat_1->fetch_assoc())
							{
								echo'<option value ="'.$row_1['ID'].'" title="'.$row_1['Document'].'">'.$row_1['Type'].' - '.$row_1['Document'] .'</option><br/>'; 
							}
							mysqli_close($mysqli_dmo);
						?>
					</select>
				</div>
			</td>
			<td>
			</td>
		   <td>
				<input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn white_red"  id="" name="Delete_Doc" value="Delete" title="Supprimer le document sélectionné."/>
			</td>
		</tr>
	

		
		<tr>
			<td colspan=4><hr></td>
		</tr>
			<td colspan=4>
				<div id="Body">
					<b>Type Management</b>
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<div id="Body" style="text-indent:25px;">
					Type <font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="body">
					<input type="text" style="font-size:12px;height:19px" size=15 name="Type_Name" id="Type_Name_id" placeholder="Engineering, Method Lab., Method Assy, Method Machining " title="Name of the type. Ex: Engineering, Method Lab., Method Assy, Method Machining ...">
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<div id="Body" style="text-indent:25px;">
					Description <font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="body">
					<input type="text" style="font-size:12px;height:19px" size=35 name="Type_Description" id="Type_Description_id" placeholder="" title="full description of the type. Ex: Engineering Department">
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<div id="body" style="text-indent:25px">
					Division <font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="body">
					<select type="submit" style="font-size:12px;height:24px;width:130px" name="Type_Division"  id="Type_Division_id" title="Division the type of doc is owned by - Default = ALL ">
						<option value=""></option>
						<?php
							include('../SCM_Connexion_DB.php');
							$requete = "SELECT DISTINCT * FROM tbl_division ORDER BY Division ASC;";
							$resultat = $mysqli_scm->query($requete);
							while ($row = $resultat->fetch_assoc())
							{
								echo'<option value ="'.$row['Division'].'" title="'.$row['Description'].'">'.$row['Division'].'</option><br/>'; 
							}
							mysqli_close($mysqli_scm);
						?>
						<option value="ALL" SELECTED>ALL</option>
					</select>
				</div>
			</td>
			<td></td>
			<td>
				<input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2"  id="Create_doc_type_id" name="Create_doc_type" value="Create" title="Créer le nouveau type de document avec les informations renseignées."/>
			</td>
		</tr>
		<tr>
			<td></td>
			<td colspan=2><hr style="margin-top:5px;margin-bottom:5px"></td>
		</tr>
		<tr>
			<td>
				<div id="body" style="text-indent:25px">
					Type<font color=red> *</font>:
				</div>
			</td>
			<td>
				<div id="InpBox_User">
					<select type="submit" style="font-size:12px;width:110px;height:23px" name="Picked_Doc_Type"  id="Picked_Doc_Type_id" title="Type/Category you want to remove from the list">
						<option value=""></option><br/>
						<?php
							include('../DMO_Connexion_DB.php');
							$requete_1 = 'SELECT * FROM tbl_type ORDER BY Type ASC;';
							$resultat_1 = $mysqli_dmo->query($requete_1);
							while ($row_1 = $resultat_1->fetch_assoc())
							{
								echo'<option value ="'.$row_1['ID'].'" title="'.$row_1['Description'].'">'.$row_1['Type'].'</option><br/>'; 
							}
							mysqli_close($mysqli_dmo);
						?>
					</select>
				</div>
			</td>
			<td>
			</td>
		   <td>
				<input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn white_red"  id="" name="Delete_Doc_Type" value="Delete" title="Supprimer le type sélectionné."/>
			</td>
		</tr>		
		
		
		<tr>    
			<td colspan=4><hr></td>
		</tr>
		
		
        <tr>
            <td colspan=2>
                <div id="Body">
                    <b>Ex Category Management:</b>
                </div>
            </td>
        </tr>		
		<tr>
			<td>
				<div id="body"  style="text-indent:25px">
					Type in a Ex category to create:
				</div>
			</td>
			<td>
				<div id="body">
					<input type="text" size=16 style="height:19px" name="New_Ex" value="">
				</div>
			</td>
			<td></td>
			<td>
				<input type="submit" class="btn blue2" name="Create_Ex" style="width:80px" value="Create" title="Create the Ex category"/>
			</td>
		</tr>
		<tr>
			<td></td>
			<td colspan=2><hr style="margin-top:5px;margin-bottom:5px"></td>
		</tr>
		<tr>
            <td>
                <div id="body"  style="text-indent:25px">
                    Pick a EX category to delete:
                </div>
            </td>
            <td>
                <div id="body">
                    <select name="Picked_Ex" type="submit" style="height:21px"> 
                    <option value=""></option>
                    <?php
                        include('../DMO_Connexion_DB.php');
                        $requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex ASC;";
                        $resultat = $mysqli_dmo->query($requete);
                        while ($row = $resultat->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Ex'].'">'.$row['Ex'].'</option><br/>'; 
                        }
                        mysqli_close($mysqli_dmo);
                    ?>
                    </select>
                </div>
            </td>
			<td></td>
			<td>
				<input type="submit" class="btn white_red" style="width:80px" name="Delete_Ex" value="Delete" title="Delete the Ex category"/>
			</td>
		</tr>	
			
		<tr>
			<td colspan=4>
				<hr>
			</td>
		</tr>
		
		<tr>    
			<td>
				<div id="Section_Title">
					DMO Documents: 
				</div>
			</td>
			<td colspan=3>
				<a href="Resources\2021_07_27_Archives_Prise_numero_DM_Offshore.xlsx" target="new">Before-Jul-2021 DMO Excel File</a>
				\ 
				<a href="Resources\Archives\2021_07_27_Archives_Prise_numero_DM_Offshore.htm" target="_blank">HTML Format</a>
				 \ 
				<a href="Resources\BE-13_A Gestion_outil_DMO.pdf" target="_blank">Good Practices DMO tool management</a>
			</td>
		</tr>
    </table>
</form>




</body>
</html>
