<!DOCTYPE html>

<html translate="no">

<!--<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">-->
<meta charset="UTF-8">

<link rel="stylesheet" type="text/css" href="DMO_Modification_form_styles.css">
<!--<link rel="stylesheet" type="text/css" href="DMO_styles.css">-->
<link rel="icon" type="image/png" href="/Common_Resources/icon_DMO_3.png"/>
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">


<head>
	<meta charset="utf-8">

	<script>
		
		function time_int()
		{
			const x=document.getElementById("Spent_Time").value;
			if (parseInt(x)!=x)
			{
				document.getElementById("Spent_Time").value=parseInt(x);
			} 
		}
		
		function delete_form()
		{
			let text = "\u26a0 The DMO is about to be definitively deleted from the database \u26a0\n__________________________________________________________________________\n\n          \u21DB Do you confirm you want the DMO to be deleted?";
			if (confirm(text) == true) {
				return true;
			} else {
				return false;
			}
		}

	</script>


</head>

<?php $txt_update=""; ?>

<?php	
	if (isset($_POST['Update']))
	{
		
		$DMO=$_POST['DMO'];
		$Description=htmlspecialchars($_POST['Description'], ENT_QUOTES);
		$Requestor=$_POST['Requestor_Name'];
		$Product_Range=$_POST['Product_Range'];
		$Division=$_POST['Division'];
		$Status=$_POST['Status'];
		$Decision=$_POST['Decision'];
		$Department=$_POST['Department'];
		$Ex=$_POST['Ex'];
		$Indus_Related=$_POST['Indus_Related'];
		$Eng_Owner=$_POST['Eng_Owner'];
		$PR_Number=$_POST['PR_Number'];
		$Issue_Date=$_POST['Issue_Date'];
		$End_Date=$_POST['End_Date'];
		$Project=$_POST['Project'];
		$EX_Assessment=$_POST['EX_Assessment'];
		$Spent_Time=intval($_POST['Spent_Time']);
		$dmo_type=$_POST['DMO_Type'];
		$document=$_POST['Document'];
		
		$txt_update=' successfully updated';
		if (strlen(htmlspecialchars($_POST['Comment'], ENT_QUOTES))>0)
		{
			$Comment=date("Y-m-d").': '.htmlspecialchars($_POST['New_Comment'], ENT_QUOTES)." - Admin\r\n".htmlspecialchars($_POST['Comment'], ENT_QUOTES);
		} else {
				$Comment=date("Y-m-d").': '.htmlspecialchars($_POST['New_Comment'], ENT_QUOTES).' - Admin';
				}
		
		$Last_Update_Date = date("Y-m-d");
		$Last_Update_TE_ID= "Admin";
		
		
		include('../DMO_Connexion_DB.php');			
		
		$sql = 'UPDATE tbl_dmo SET Description="'.$Description.
								 '",Product_Range="'.$Product_Range.
								 '",Division="'.$Division.
								 '",Project="'.$Project.
								 '",Decision="'.$Decision.
								 '",Status="'.$Status.
								 '",Ex="'.$Ex.
								 '",Indus_Related="'.$Indus_Related.
								 '",Eng_Owner="'.$Eng_Owner.
								 '",PR_Number="'.$PR_Number.
								 '",Comment="'.$Comment.
								 '",Last_Update_Date="'.$Last_Update_Date.
								 '",Last_Update_TE_ID="'.$Last_Update_TE_ID.
								 '",End_Date="'.$End_Date.
								 '",EX_Assessment="'.$EX_Assessment.
								 '",Spent_Time="'.$Spent_Time.
								 '",Document="'.$document.
								 '",Type="'.$dmo_type.
								 '" WHERE DMO ="'.$DMO.
								 '";';
		
		$result = $mysqli_dmo->query($sql);
		mysqli_close($mysqli_dmo);
		
		// Justification File upload
		if (isset($_FILES['justiffichiers']) )
		{
			$nbLignes=count($_FILES['justiffichiers']['name']);
			
			include('../DMO_Connexion_DB.php');	
			include('Generic_Attachment_Folder.php'); 
			
			$path_attachment=$Path_Folder . $Attachment_Folder; 
			mysqli_close($mysqli_dmo); 
			$nom = $path_attachment . substr($DMO,3,5); // Le nom du répertoire à créer				
			
			
			if (is_dir($nom))   // Vérifie si le répertoire existe creation si non existant
				{
				} else { 
					mkdir($nom,4);     
				}
			for($i=0 ; $i<=$nbLignes-1 ; $i++)
				{
				// test de la taille du fichier
				if ($_FILES['justiffichiers']['size'][$i] <= 10000000)
					{
						$tmp_file = $_FILES['justiffichiers']['tmp_name'][$i];	
						move_uploaded_file($_FILES['justiffichiers']['tmp_name'][$i], $nom.'/' .$Justification_File_Name.basename($_FILES['justiffichiers']['name'][$i]));
					} else {
							echo 'Le fichier ' . $_FILES['justiffichiers']['name'][$i] . 'est trop gros';
						}
				}
		}
	}
	
	if (isset ($_POST['Delete']))
	{
		
		$DMO=$_POST['DMO'];
		
		$sql = 'DELETE FROM tbl_dmo WHERE DMO ="'.$DMO.'";';
		
		include('../DMO_Connexion_DB.php');
		$result = $mysqli_dmo->query($sql);

		
		// SUPPRESSION DOSSIER ASSOCIE SI EXISTANT
		include('Generic_Attachment_Folder.php'); 
		$path_attachment=$Path_Folder . $Attachment_Folder; 
		mysqli_close($mysqli_dmo); 
		$nom = $path_attachment . substr($DMO,3,5);
		
		if (!is_dir($nom)) {
			rmdir($nom);
		}
		$txt_update=" - DMO SUCCESSFULLY EXECUTED";
		
	}
?>

	
<body>

	<form name="Update" method="post" action="" enctype="multipart/form-data">

		<table border=0 style="width:100%">
			<tr style="vertical-align:middle;">
				<th colspan=6 style="height:30px;border-radius:5px;padding-bottom:10px;">
					<div id="Top_Title">
						<?php echo $_GET['dmo'] ?> - ADMIN Modification Form
					</div>
				</th>
			</tr>
			<?php
				include('../DMO_Connexion_DB.php');
				$requete = 'SELECT * FROM tbl_dmo where DMO="'.$_GET['dmo'].'";';
				$resultat = $mysqli_dmo->query($requete);
				$rowcount=mysqli_num_rows($resultat);
				if ($rowcount>=1)
				{
					while ($ligne = $resultat->fetch_assoc())
					{
						echo '<tr>';
						echo '<td style="width:17%"><div id="Body">DMO: </div></td><td style="width:12%"><div id="InpBox"><input style="border:transparent;border:none;width:80px;font-size:8pt;font-family:Tahoma, sans-serif;" type="text" name="DMO" id="DMO_choice" value="'.$ligne['DMO'].'"readonly></div></td>';
						echo '<td style="width:16%"><div id="Body">Division: </div></td><td style="width:20%"><div id="InpBox"><select name="Division" type="submit" style="width:80px;font-size:8pt;font-family:Tahoma, sans-serif;">';
						echo '<option value=""></option>';
							include('../SCM_Connexion_DB.php');
							$requete = "SELECT DISTINCT * FROM tbl_division ORDER BY Division ASC;";
							$resultat = $mysqli_scm->query($requete);
							$in='0';
							while ($row = $resultat->fetch_assoc())
							{
								if ($row['Division']==$ligne['Division'])
								{
									echo'<option value ="'.$row['Division'].'" title="'.$row['Description'].'" Selected>'.$ligne['Division'].'</option><br/>';
									$in='1';
								} else {
										echo'<option value ="'.$row['Division'].'" title="'.$row['Description'].'">'.$row['Division'].'</option><br/>';
										}
							}
							if ($in=='0')
								{
									echo'<option value ="'.$ligne['Division'].'" Selected>'.$ligne['Division'].'</option><br/>';
								}
							$mysqli_scm->close();
						echo '</td>';
						
						//echo '<td><div id="Body">Issue Date: </div></td><td><div id="InpBox">'.$ligne['Issue_Date'].'</div></td>';
						echo '<td style="width:18%"><div id="Body">Issue Date: </div></td><td ><div id="InpBox"><input style="width:65px;text-align:center;font-size:8pt;font-family:Tahoma, sans-serif;" type="text" name="Issue_Date" value="'.$ligne['Issue_Date'].'"</div></td>';
							
						echo '</tr>';
						
						echo '<tr>';
						//echo '<td><div id="Body">Requestor: </div></td><td><div id="InpBox">'.$ligne['Requestor_Name'].'</div></td>';
						echo '<td  style="width:85px;"><div id="Body">Requestor: </div></td><td><div id="InpBox"><select name="Requestor_Name" type="submit" style="width:110px;font-size:8pt;font-family:Tahoma, sans-serif;">';
							echo '<option value=""></option>';
								$requete = "SELECT Fullname FROM tbl_user ORDER BY Fullname ASC;";
								$resultat = $mysqli_dmo->query($requete);
								$in='0';
								while ($row = $resultat->fetch_assoc())
								{
									if ($row['Fullname']==$ligne['Requestor_Name'])
									{
										echo'<option value ="'.$row['Fullname'].'" Selected>'.$ligne['Requestor_Name'].'</option><br/>';
										$in='1';
									} else {
											echo'<option value ="'.$row['Fullname'].'">'.$row['Fullname'].'</option><br/>';
											}
								}
								if ($in=='0')
									{
										echo'<option value ="'.$ligne['Requestor_Name'].'" Selected>'.$ligne['Requestor_Name'].'</option><br/>';
									}
						echo '</td>';
						
						
						//echo '<td><div id="Body">Status: </div></td><td><div id="InpBox"><input style="border:none;font-size:8pt;font-family:Tahoma, sans-serif;" readonly type="text" size=16  name="Status" value="'.$ligne['Status'].'"></div></td>';
						echo '<td><div id="Body">Status: </div></td><td><div id="InpBox"><select name="Status" type="submit" style="width:80px;font-size:8pt;font-family:Tahoma, sans-serif;">';
							echo '<option value=""></option>';
								$requete = "SELECT DISTINCT Status FROM tbl_status ORDER BY Status ASC;";
								$resultat = $mysqli_dmo->query($requete);
								$in='0';
								while ($row = $resultat->fetch_assoc())
								{
									if ($row['Status']==$ligne['Status'])
									{
										echo'<option value ="'.$row['Status'].'" Selected>'.$ligne['Status'].'</option><br/>';
										$in='1';
									} else {
											echo'<option value ="'.$row['Status'].'">'.$row['Status'].'</option><br/>';
											}
								}
								if ($in=='0')
									{
										echo'<option value ="'.$ligne['Status'].'" Selected>'.$ligne['Status'].'</option><br/>';
									}
							echo '</td>';
						
						
						
						//echo '<td><div id="Body">Last Update on: </div></td><td style="width:85px;"><div id="InpBox">'.$ligne['Last_Update_Date'].'</div></td>';
						echo '<td style="width:120px"><div id="Body">Last Update on: </div></td><td style="width:85px;"><div id="InpBox"><input style="width:65px; text-align:center;font-size:8pt;font-family:Tahoma, sans-serif;" type="text" name="IssueLast_Update_Date_Date" value="'.$ligne['Last_Update_Date'].'"</div></td>';

						echo '</tr>';
						
						echo '<tr>';
						//echo '<td><div id="Body">Department: </div></td><td><div id="InpBox">'.$ligne['Requestor_Dpt'].'</div></td>';
						echo '<td><div id="Body">Department: </div></td><td><div id="InpBox"><select name="Department" type="submit" style="width:110px;font-size:8pt;font-family:Tahoma, sans-serif;">';
							echo '<option value=""></option>';
								$requete = "SELECT DISTINCT Department FROM tbl_department ORDER BY Department ASC;";
								$resultat = $mysqli_dmo->query($requete);
								$in='0';
								while ($row = $resultat->fetch_assoc())
								{
									if ($row['Department']==$ligne['Requestor_Dpt'])
									{
										echo'<option value ="'.$row['Department'].'" Selected>'.$ligne['Requestor_Dpt'].'</option><br/>';
										$in='1';
									} else {
											echo'<option value ="'.$row['Department'].'">'.$row['Department'].'</option><br/>';
											}
								}
								if ($in=='0')
									{
										echo'<option value ="'.$ligne['Department'].'" Selected>'.$ligne['Requestor_Dpt'].'</option><br/>';
									}
						echo '</td>';
						
						echo '<td><div id="Body">Decision: </div></td><td><div id="InpBox"><select name="Decision" type="submit" style="width:90px;font-size:8pt;font-family:Tahoma, sans-serif;">';
							echo '<option value=""></option>';
								$requete = "SELECT Decision FROM tbl_decision ORDER BY Decision ASC;";
								$resultat = $mysqli_dmo->query($requete);
								$in='0';
								while ($row = $resultat->fetch_assoc())
								{
									if ($row['Decision']==$ligne['Decision'])
									{
										echo'<option value ="'.$row['Decision'].'" Selected>'.$ligne['Decision'].'</option><br/>';
										$in='1';
									} else {
											echo'<option value ="'.$row['Decision'].'">'.$row['Decision'].'</option><br/>';
											}
								}
								if ($in=='0')
									{
										echo'<option value ="'.$ligne['Decision'].'" Selected>'.$ligne['Decision'].'</option><br/>';
									}
						echo '</td>';
						
						echo '<td><div id="Body">End Date: </div></td><td><div id="InpBox"><input style="width:65px;text-align:center;font-size:8pt;font-family:Tahoma, sans-serif;"  type="text" size=16  name="End_Date" value="'.$ligne['End_Date'].'"</div></td>';	
							
						echo '</tr>';
						
						
						echo '<tr>';
						echo '<td><div id="Body">Description: </div></td><td colspan=5><div id="InpBox"><textarea rows="6" name="Description" style="width:95%;font-size:9pt;">'.$ligne['Description'].'</textarea></div></td>';
						echo '</tr>';
						
						echo '<tr>';
						echo '<td style="height:35px"><div id="Body">Indus. Resp.: </div></td><td><div id="InpBox"><select name="Indus_Related" type="submit" style="font-size:9pt;">'.
							 '<option value="'.$ligne['Indus_Related'].'">'.$ligne['Indus_Related'].'</option>';
						switch ($ligne['Indus_Related']) {
							case "Yes":
								echo '<option value="No">No</option>';
								echo '<option value=""></option>';
								break;
							case "No":
								echo '<option value="Yes">Yes</option>';
								echo '<option value=""></option>';
								break;
							case "":
								echo '<option value="Yes">Yes</option>',
									 '<option value="No">No</option>';
								break;
						}
						echo '</div></td>';
						echo '<td><div id="Body">DMO Owner: </div></td><td><div id="InpBox"><select name="Eng_Owner" type="submit" style="font-size:9pt;">';
						$requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Industry' or Department like 'Aerospace' or Department like 'Method' ORDER BY Fullname ASC;";
								$resultat = $mysqli_dmo->query($requete);
								echo '<option value=""></option>';
								$in='0';
								while ($row = $resultat->fetch_assoc())
								{
									if ($row['Fullname']==$ligne['Eng_Owner'])
									{
										echo'<option value ="'.$row['Fullname'].'" Selected>'.$ligne['Eng_Owner'].'</option><br/>';
										$in='1';
									} else {
											echo'<option value ="'.$row['Fullname'].'">'.$row['Fullname'].'</option><br/>';
										 }
								}
						if ($in=='0' && $ligne['Eng_Owner']!="")
						{
							echo'<option value ="'.$ligne['Eng_Owner'].'" Selected>'.$ligne['Eng_Owner'].'</option><br/>';
						}
						echo '</div></td>';
						echo '<td rowspan=2 colspan=2>';
							include('Generic_Attachment_Folder.php'); 
							$path_attachment=$Path_Folder . $Attachment_Folder . substr($_GET['dmo'],-5)."\\";
							echo '<div id="InpBox">';
							include('Generic_Attachment_List.php');
							echo '</div>';
						echo '</td>';						
						echo '</tr>';
						
						echo '<tr>';
						echo '<td style="height:35px"><div id="Body">Ex Rated: </div></td><td><div id="InpBox"><select name="Ex" type="submit" style="font-size:9pt;">';
						echo '<option value=""></option>';
								$requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex ASC;";
								$resultat = $mysqli_dmo->query($requete);
								$in='0';
								while ($row = $resultat->fetch_assoc())
								{
									if ($row['Ex']==$ligne['Ex'])
									{
										echo'<option value ="'.$row['Ex'].'" Selected>'.$ligne['Ex'].'</option><br/>';
										$in='1';
									} else {
											echo'<option value ="'.$row['Ex'].'">'.$row['Ex'].'</option><br/>';
											}
								}
						if ($in=='0' && $ligne['Ex']!="")
						{
							echo'<option value ="'.$ligne['Ex'].'" Selected>'.$ligne['Ex'].'</option><br/>';
						}

						echo '</div></td>';
						echo '<td><div id="Body">Product Range: </div></td><td><div id="InpBox"><select name="Product_Range" type="submit" style="width:110px;font-size:9pt;font-family:Tahoma, sans-serif;">';
						echo '<option value=""></option>';
							$requete = 'SELECT DISTINCT Product_Range FROM tbl_product_range ORDER BY Product_Range ASC;';
							$resultat = $mysqli_dmo->query($requete);
							$in='0';
							while ($row = $resultat->fetch_assoc())
							{
								if ($row['Product_Range']==$ligne['Product_Range'])
								{
									echo'<option value ="'.$row['Product_Range'].'" Selected>'.$ligne['Product_Range'].'</option><br/>';
									$in='1';
								} else {
										echo'<option value ="'.$row['Product_Range'].'">'.$row['Product_Range'].'</option><br/>';
										}
							}
							if ($in=='0' && $ligne['Product_Range']!="")
								{
									echo'<option value ="'.$ligne['Product_Range'].'" Selected>'.$ligne['Product_Range'].'</option><br/>';
								}	
						echo '</div></td>';
						echo '</tr>';

						echo '<tr>';
						echo '<td style="height:35px;"><div id="Body">Diffusion #: </div></td><td><div id="InpBox"><input type="text" size=8 name="PR_Number" style="font-size:8pt;height:&&px" placeholder="NA, XXXXXX" value="'.$ligne['PR_Number'].'">';
						if ($ligne['PR_Number']!="")
								{
									$link='https://frscmbe.scmlemans.com/REL/REL_Pack_Overview.php?ID='.$ligne['PR_Number'];	
									echo '&nbsp<a href="'.$link.'" style="text-decoration:none;font-size:15px; vertical-align:top;font-weight:bold; color:navy;vertical-align:top; text-align:center"  target="_blank" title="go to release package detail">&#9658</a>';
								}
						
						echo '</div></td>';
						
						
						/*echo '<td><div id="Body">Project: </div></td><td><div id="InpBox"><input type="text" size=8 name="Project" style="font-size:8pt;height:8pt;" value="'.$ligne['Project'].'"></div></td>';	*/
						
						
						
						echo '<td><div id="Body">Project : </div></td><td><div id="InpBox"><select name="Project" type="submit" style="font-size:9pt;font-family:Tahoma, sans-serif;width:80px">';
						echo '<option value=""></option>';
						
							include('../SCM_Connexion_DB.php');
							$requete = "SELECT DISTINCT OTP, Title FROM tbl_project ORDER BY OTP DESC;";
							$resultat = $mysqli_scm->query($requete);
							$in='0';
							while ($row = $resultat->fetch_assoc())
							{
								if ($row['OTP']==$ligne['Project'])
								{
									echo'<option value ="'.$row['OTP'].'" Selected>'.$ligne['Project'].'</option><br/>';
									$in='1';
								} else {
										echo'<option value ="'.$row['OTP'].'">'.$row['OTP'].'</option><br/>';
										}
							}
							if ($in=='0' && $ligne['Project']!="")
								{
									echo'<option value ="'.$ligne['Project'].'" Selected>'.$ligne['Project'].'</option><br/>';
								}	
						mysqli_close($mysqli_scm);
						echo '</div></td>';
						
						
						
						echo '<td colspan=2 rowspan=2><div style="font-weight:bold;text-indent:20px">Justif. files from DMO Owner</div>';
						if  ($ligne['Status']=="Open")
						{
							echo '<input id="file" name="justiffichiers[]" style="font-size:10px;text-indent:20px" type="file" class="" multiple>';
						}
						include('Generic_Attachment_Folder.php'); 
						$path_attachment=$Path_Folder . $Attachment_Folder . substr($_GET['dmo'],-5)."\\";
						echo '<div id="InpBox">';
						include('Generic_Attachment_Justification.php');
						echo '</div>';
						echo '</td>';
						
						
						echo '</tr>';
						echo '<td  style="height:35px"><div id="Body">DMO Type: </div></td>';
						echo '<td><div id="InpBox"><select name="DMO_Type" id="DMO_Type_ID" type="submit" style="font-size:9pt;font-family:Tahoma, sans-serif;width:100px">';
						echo '<option value=""></option>';
							$requete_8 = 'SELECT DISTINCT Type FROM tbl_type WHERE Division like "'.$ligne['Division'].'" OR Division like "ALL" ORDER BY Type DESC;';
							$resultat = $mysqli_dmo->query($requete_8);
							$in='0';
							while ($row = $resultat->fetch_assoc())
							{
								if ($row['Type']==$ligne['Type'])
								{
									echo'<option value ="'.$row['Type'].'" Selected>'.$ligne['Type'].'</option><br/>';
									$in='1';
								} else {
								echo '<option value ="'.$row['Type'].'">'.$row['Type'].'</option><br/>';
										}
							}
							if ($in=='0' && $ligne['Type']!="")
								{
									echo'<option value ="'.$ligne['Type'].'" Selected>'.$ligne['Type'].'</option><br/>';
								}	
						echo '</div>';
						echo '</td>';
						
						
						
						echo '<td><div id="Body">Document : </div></td><td><div id="InpBox"><select name="Document" type="submit" style="font-size:9pt;font-family:Tahoma, sans-serif;width:140px">';
						echo '<option value=""></option>';
						
							//$type_filter=$ligne['Type'];
							//$requete_9 = 'SELECT DISTINCT * FROM tbl_document WHERE Type like "'.$type_filter.'"  ORDER BY Document ASC';
							$requete_9 = 'SELECT DISTINCT * FROM tbl_document ORDER BY Document ASC;';
							$resultat = $mysqli_dmo->query($requete_9);
							$in='0';
							while ($row = $resultat->fetch_assoc())
							{
								if ($row['Document']==$ligne['Document'])
								{
									echo'<option value ="'.$row['Document'].'" title="'.$row['Description'].'" Selected>'.$ligne['Document'].' - ' . $row['Type'].'</option><br/>';
									$in='1';
								} else {
										echo'<option value ="'.$row['Document'].'" title="'.$row['Description'].'">'.$row['Document'].' - ' . $row['Type']. '</option><br/>';
										}
							}
							if ($in=='0' && $ligne['Document']!="")
								{
									echo'<option value ="'.$ligne['Document'].'"Selected>'.$ligne['Document'].'</option><br/>';
								}	

						echo '</div></td>';				
						echo '</tr>';
							 
						$nb_ligne=substr_count($ligne['Comment'], ':')+3;
						if ($nb_ligne>8) { $nb_ligne=8;}
						
						echo '<tr><td><div id="Body">Previous Comments:</b></td></div><td colspan=5><div id="InpBox"><textarea rows="'.$nb_ligne.'"  style="width:95%;font-size:9pt;" name="Comment">'.$ligne['Comment'].'</textarea></div></td></tr>';
						
						echo '<tr><td><div id="Body">Comment (*):</b></div></td><td colspan=5><div id="InpBox"><textarea required id="added_comments" rows="4" name="New_Comment" style="width:95%;font-size:9pt;" placeholder="Indicate the reson for the update or the change"></textarea></div></td></tr>';
					


					echo '<tr>';
					echo '<td><div id="Body">EX Assessment:</b></div></td><td colspan=3><div id="InpBox"><textarea rows="2" cols="50" name="EX_Assessment" style="font-size:9pt;" placeholder="If an Ex product is impacted, please indicate the assessment for Ex function">'.$ligne['EX_Assessment'].'</textarea></div></td>';
					echo '<td><div id="Body">Spent Time (h):</b></div></td><td><div id="InpBox"><input type="text" size=4 id="Spent_Time" name="Spent_Time" style="font-size:8pt;height:11px" Title="Time spent by engineering to review, decide and implement the change - in hours" placeholder="" onchange="time_int()" value="'.$ligne['Spent_Time'].'"></div></td>';
					
					$decision_value=$ligne['Decision'];
					$status_value=$ligne['Status'];
				}	


					$mysqli_dmo->close();
					?>
				<tr>

					
					<td colspan=6 style="text-align:right;">
						<input style="color:white;width:100px; height:25px" type="submit" name="Update" class="btn blue" onclick="return update_form()" value="Update" title="Update the DMO"/>						
					</td>
				</tr>
				<tr>
					<td colspan=6 style="text-align:right;">
						<input style="width:100px; height:25px" type="submit" name="Delete" class="btn white_red" onclick="return delete_form()" value="Delete" title="Delete the DMO"/>
					</td>
				</tr>
				<?php 
					if (isset($_POST['DMO']))
					{
						echo '<tr>
							<td id="status_bar" style="border-radius:10px;background-color:#EBF5FB ;color:#1F618D;font-style:italic;font-weight:bold ;text-align:right;padding-right:30px" colspan=6>
								The '.$_POST['DMO'].' has been successfully '. $txt_update .'.
							</td>
						</tr>';
					}
				
				} else {
					echo '<tr>
							<td style="text-indent:40px;">
								<font style="text-indent:40px;font-family: Arial black, monospace;font-size:18pt;color:blue;font-weight:bold">
									DMO NOT EXISTING '. $txt_update. '
								</font>
							</td>
						</tr>';
				}
					
			?>
					
	</table>
	</form>

</body> 

</html>
