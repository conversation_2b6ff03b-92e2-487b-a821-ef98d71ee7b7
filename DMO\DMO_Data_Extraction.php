<!DOCTYPE html>
<html>

<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="DMO_Styles.css">

<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="DMO_vertical_tab.css">


<head>

<script>

var historic_checked_list=[];

function all_fields()
{
	const all_fields=document.getElementsByClassName("chckbox");
	for(i=0;i<all_fields.length;i++) 
	{		
		if (all_fields[i].name!="DMO")
		{
			if (all_fields[i].checked==true && document.getElementById("all").checked==true)
			{
				historic_checked_list.push(all_fields[i].name);
			} else if (all_fields[i].checked==true && document.getElementById("all").checked==false)
			{
				if (historic_checked_list.length>0)
				{
					for(k=1;k<historic_checked_list.length;k++) 
					{	
						if (historic_checked_list[k].value==all_fields[i].name)
						{
							all_fields[i].checked=true;
						}
					}
				}
			}
			
			all_fields[i].checked=document.getElementById("all").checked;
		} else {
			all_fields[i].checked=true;
		}
	}

	list_overview_update();
}



function list_overview_update()
{
	const xhttp = new XMLHttpRequest();
	xhttp.onload = function() 
	{
		const raw_result=this.responseText.trim();

		if (raw_result.length>0)
		{
			var txt='<table id="t06" style="border-collapse: collapse;" border=0>';
			
			txt=txt + '<tr><td><table style="width:100%"><tr>';
			for(var k=0;k<all_fields.length;k++)
			{
				if (all_fields[k].name!='all_btn' && all_fields[k].checked==true)
				{
					txt=txt + '<th style="width:calc(100% / '+ nb_param +'">' + all_fields[k].name + '</th>'; 
				}
			}
			txt=txt + '</tr></table></td></tr>';
			
			var row_value=raw_result.split("|");
			for(var i=0;i<row_value.length;i++)
			{
				txt=txt + '<tr><td><table style="width:100%"><tr>';
				var record_value=row_value[i].split("__");
				for (var j=0;j<record_value.length;j++)
				{
					txt=txt + '<td style="width:calc(100% / '+ record_value.length +'">' + record_value[j].substr(0,20) + '</td>';
				}
				txt=txt + '</tr></table></td></tr>';				
			}
			txt=txt + '</table>';
			document.getElementById("extract_list_overview").innerHTML=txt;
		}
	}


	const all_fields=document.getElementsByClassName("chckbox");
	var param="";
	var parm_sep="__";
	var nb_param=1;
	var url="DMO_Data_Extraction_List.php?Param=DMO";
	
	for(i=0;i<all_fields.length;i++) 
	{
		if (all_fields[i].checked==true & all_fields[i].name!="all_btn" & all_fields[i].name!="DMO")
		{
			nb_param=nb_param+1;
			param=param + parm_sep + all_fields[i].name;
		}
	}

	if (param.length>0)
	{
		url=url + param;
		xhttp.open("GET", url);
		xhttp.send();
	} 
	
}
</script>

</head>
<body>

<?php
$msg="";
if (isset ($_POST['validate']))
{
    $i=1;
    if (isset($_POST['DMO']))
    {
      $selected_fields[$i] = "DMO";
      $i=$i+1;
    }
    if (isset($_POST['Issue_Date']))
    {
      $selected_fields[$i] = "Issue_Date";
      $i=$i+1;
    }
    if (isset($_POST['Product_Range']))
    {
      $selected_fields[$i] = "Product_Range";
      $i=$i+1;
    }
    if (isset($_POST['Status']))
    {
      $selected_fields[$i] = "Status";
      $i=$i+1;
    }
    if (isset($_POST['Decision']))
    {
      $selected_fields[$i] = "Decision";
      $i=$i+1;
    }
    if (isset($_POST['End_Date']))
    {
      $selected_fields[$i] = "End_Date";
      $i=$i+1;
    }
    if (isset($_POST['Project']))
    {
      $selected_fields[$i] = "Project";
      $i=$i+1;
    }
    if (isset($_POST['Ex']))
    {
      $selected_fields[$i] = "Ex";
      $i=$i+1;
    }
    if (isset($_POST['PR_Number']))
    {
      $selected_fields[$i] = "PR_Number";
      $i=$i+1;
    }
    if (isset($_POST['Indus_Related']))
    {
      $selected_fields[$i] = "Indus_Related";
      $i=$i+1;
    }
    if (isset($_POST['Eng_Owner']))
    {
      $selected_fields[$i] = "Eng_Owner";
      $i=$i+1;
    }
    if (isset($_POST['Requestor_Name']))
    {
      $selected_fields[$i] = "Requestor_Name";
      $i=$i+1;
    }
    if (isset($_POST['Requestor_Dpt']))
    {
      $selected_fields[$i] = "Requestor_Dpt";
      $i=$i+1;
    }
    if (isset($_POST['Description']))
    {
      $selected_fields[$i] = "Description";
      $i=$i+1;
    }
    if (isset($_POST['Comment']))
    {
      $selected_fields[$i] = "Comment";
      $i=$i+1;
    }
    if (isset($_POST['EX_Assessment']))
    {
      $selected_fields[$i] = "EX_Assessment";
      $i=$i+1;
    }
    if (isset($_POST['Spent_Time']))
    {
      $selected_fields[$i] = "Spent_Time";
      $i=$i+1;
    }
	if (isset($_POST['Division']))
    {
      $selected_fields[$i] = "Division";
      $i=$i+1;
    }
	if (isset($_POST['Type']))
    {
      $selected_fields[$i] = "Type";
      $i=$i+1;
    }
	if (isset($_POST['Document']))
    {
      $selected_fields[$i] = "Document";
      $i=$i+1;
    }

    //query result to extract in csv
    $query_1 = 'SELECT * FROM tbl_dmo ORDER BY DMO DESC;'; 
    $query_2 = 'SHOW Columns from  tbl_dmo;'; 

    //Count of the number of fields
    $selected_fields_num = count($selected_fields);

    // Report folder name 
    include('../DMO_Connexion_DB.php');
    include('Generic_Attachment_Folder.php'); // --|A FAIRE le REPORT du chemin ci-dessous doit etre integrer avec le $Report_Folder_Name aui viens du generic attachement folder.php 

    //File name defintion
    $csv_file_name='.\Report\\'.date("Y_m_d_H_i_s").'_query_report.csv';

    //CSV file opening
	  $fp = fopen($csv_file_name, 'w');

    //Column headers creation in the csv report
    $selected_fields_array=array();
    for ($i = 1; $i <= $selected_fields_num; $i++)
        {
        array_push($selected_fields_array,$selected_fields[$i]);
        }
    $list = array($selected_fields_array);         

    foreach ($list as $fields)
    {
        fputcsv($fp, $fields, ';');
    }

    //query execution
	  $resultat = $mysqli_dmo->query($query_1);

    //Actual data  print out in the csv file
	while ($ligne = $resultat->fetch_assoc())
	{

        $selected_fields_array=array();
        for ($i = 1; $i <= $selected_fields_num; $i++)
		{
            array_push($selected_fields_array,$ligne[$selected_fields[$i]]);
		}

        $list = array($selected_fields_array);            
        foreach ($list as $fields)
        {
            fputcsv($fp, $fields, ';');
        }

    }
    fclose($fp);

    //Actual data  print out in the csv file
    if (mysqli_num_rows($resultat) > 0) {
      while ($row = mysqli_fetch_assoc($resultat)) {
        echo '<br />- '.$row['Field'];
     }
   }

    mysqli_close($mysqli_dmo);
    $msg= "<a  target='_blank' href='" .$csv_file_name. "'>".$csv_file_name. "</a> created !";
}
?>


<!--<form name="csv" method="post" action="DMO_KPI_Extract.php">-->
<form name="csv" method="post" action="">
	
	<table border=0>
		<tr>
			<td colspan=6 style="padding-top:10px;height:30px">
				<div id="Section_Title">
					Pick the fields you want to extract:
				</div>
			</td>
		</tr>
		<tr>
			<td colspan=6>
				<input class="chckbox" type="checkbox"  name="all_btn" id="all" value="0" onclick="all_fields()"> <label for="all" class="chckbox_label">All fields</label></br>
			</td>
		</tr>
		<tr>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="DMO" id="DMO_id" value="1" checked> <label for="DMO_id" class="chckbox_label">DMO</label></br>
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Issue_Date" id="Issue_Date_id" value="2" <?php if (isset($_POST['Eng_Owner'])){echo "checked";} ?>> <label for="Issue_Date_id" class="chckbox_label">Issue_Date</label></br>
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Product_Range" id="Product_Range_id" value="3" <?php if (isset($_POST['Product_Range'])){echo "checked";} ?>> <label for="Product_Range_id" class="chckbox_label">Product_Range</label></br>
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Status" id="Status_id" value="4" <?php if (isset($_POST['Status'])){echo "checked";} ?>> <label for="Status_id" class="chckbox_label">Status</label></br>
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Decision" id="Decision_id" value="5" <?php if (isset($_POST['Decision'])){echo "checked";} ?>> <label for="Decision_id" class="chckbox_label">Decision</label></br>
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="End_Date" id="End_Date_id" value="6" <?php if (isset($_POST['End_Date'])){echo "checked";} ?>> <label for="End_Date_id" class="chckbox_label">End Date</label></br>
			</td>
		</tr>
		<tr>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Project" id="Project_id" value="7" <?php if (isset($_POST['Project'])){echo "checked";} ?>> <label for="Project_id" class="chckbox_label">Project</label></br> 
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Ex" id="Ex_id" value="8" <?php if (isset($_POST['Ex'])){echo "checked";} ?>> <label for="Ex_id" class="chckbox_label">Ex</label></br>  
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="PR_Number" id="PR_Number_id" value="9" <?php if (isset($_POST['PR_Number'])){echo "checked";} ?>> <label for="PR_Number_id" class="chckbox_label">Release Package Number</label></br>   
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Indus_Related" id="Indus_Related_id" value="10" <?php if (isset($_POST['Indus_Related'])){echo "checked";} ?>> <label for="Indus_Related_id" class="chckbox_label">Indus.</label></br>
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Eng_Owner" id="Eng_Owner_id" value="11" <?php if (isset($_POST['Eng_Owner'])){echo "checked";} ?>> <label for="Eng_Owner_id" class="chckbox_label">Eng. Owner</label></br>  
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Requestor_Name" id="Requestor_Name_id" value="12" <?php if (isset($_POST['Requestor_Name'])){echo "checked";} ?>> <label for="Requestor_Name_id" class="chckbox_label">Requestor</label></br> 
			</td>
		</tr>
		<tr>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Requestor_Dpt" id="Requestor_Dpt_id" value="13" <?php if (isset($_POST['Requestor_Dpt'])){echo "checked";} ?>> <label for="Requestor_Dpt_id" class="chckbox_label">Requestor Dpt</label></br> 
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Description" id="Description_id" value="14" <?php if (isset($_POST['Description'])){echo "checked";} ?>> <label for="Description_id" class="chckbox_label">Description</label></br> 
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Comment" id="Comment_id" value="15" <?php if (isset($_POST['Comment'])){echo "checked";} ?>> <label for="Comment_id" class="chckbox_label">Comment</label></br>
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="EX_Assessment" id="EX_Assessment_id" value="18" <?php if (isset($_POST['EX_Assessment'])){echo "checked";} ?>> <label for="EX_Assessment_id" class="chckbox_label">Ex Assessment</label></br> 
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Spent_Time" id="Spent_Time_id" value="19" <?php if (isset($_POST['Spent_Time'])){echo "checked";} ?>> <label for="Spent_Time_id" class="chckbox_label">Spent Time</label></br> 
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Division" id="Division_id" value="20" <?php if (isset($_POST['Division'])){echo "checked";} ?>> <label for="Division_id" class="chckbox_label" >Division</label></br> 
			</td>
		</tr>
		<tr>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Type" id="Type_id" value="21" <?php if (isset($_POST['Type'])){echo "checked";} ?>> <label for="Type_id" class="chckbox_label">Type</label></br> 
			</td>
			<td>
				<input class="chckbox" type="checkbox" onclick="list_overview_update()" name="Document" id="Document_id" value="22" <?php if (isset($_POST['Document'])){echo "checked";} ?>> <label for="Document_id" class="chckbox_label">Document</label></br> 
			</td>
		</tr>
		<tr>
			<td colspan=4>
				<?php echo $msg?>
			</td>
			<td colspan=2 style="text-align:right;padding-right:20px;padding-top:10px">
				<input type="submit" name="validate" style="margin-left:5px;width:150px;height:25px;border-radius:10px;" class="btn blue2" value="Generate the report" title="Generate the report including the fields you picked above."/>
			</td>
		</tr>
	</table>
</form>

<div id="Section_Title" style="font-weight:normal;font-style:italic;margin-left:10px;">
	Below will be showing up the first 10 rows of the extraction you are preparing...
</div>
<div style="margin-top:10px" id="extract_list_overview"></div>



</body>

</html>