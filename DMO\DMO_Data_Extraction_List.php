<?php 

if (isset($_GET['Param']))
{
	$param_list=explode("__", $_GET['Param']);
	$param_sql="";
	foreach ($param_list as &$value) {
		if (strlen($param_sql)>0)
		{
			$param_sql=$param_sql . ',';
		}
		$param_sql=$param_sql . $value;
	}
	
	
	$requete = 'SELECT ' . $param_sql .'
				FROM tbl_dmo
				ORDER BY DMO DESC
				LIMIT 10';

	include('../DMO_Connexion_DB.php');
	$resultat = $mysqli_dmo->query($requete);
	$rowcount=mysqli_num_rows($resultat);

	$output_value="";
	if ($rowcount>0)
	{
		while ($row = $resultat->fetch_assoc())
		{
			$tmp_output="";
			foreach ($param_list as &$value) {
				if ($tmp_output=="")
				{
					$tmp_output=$tmp_output.$row[$value];
				} else {
					$tmp_output=$tmp_output."__".$row[$value];
				}
			}
			if ($output_value=="")
			{
				$output_value=$tmp_output;
			} else {
				$output_value=$output_value . "|" . $tmp_output;
			}
		}
	}
		
	echo $output_value;
	$mysqli_dmo->close();
	
} else {
	echo "";
}