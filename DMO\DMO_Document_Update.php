<?php 

if (($_GET['eng']!="") || ($_GET['method']!=""))
{

	if (($_GET['eng'])!="")
	{
		$condition=$_GET['eng'];
	} elseif (($_GET['method'])!="")
	{
		$condition=$_GET['method'];
	} else {
		$condition='%';
	}
	$requete = 'SELECT DISTINCT Document, Type, Description
				FROM tbl_document
				WHERE Type like "'.$condition.'"
				ORDER BY Document ASC';

	include('../DMO_Connexion_DB.php');
	$resultat = $mysqli_dmo->query($requete);
	$rowcount=mysqli_num_rows($resultat);

	$output_value="";
	
	if ($rowcount>0)
	{
		while ($row = $resultat->fetch_assoc())
		{
			if ($output_value!="")
			{
				$output_value=$output_value."|".$row['Document']."__".$row['Description'];
			} else {
				$output_value=$row['Document']."__".$row['Description'];
			}
		}
	}
		
	echo $output_value;

	$mysqli_dmo->close();
	
} else {
	echo "";
}