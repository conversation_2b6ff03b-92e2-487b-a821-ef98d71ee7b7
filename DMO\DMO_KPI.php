<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="DMO_Styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<?php	

	// PREPARATION DONNEES GRAPHIQUES
	// ##############################

	// NOMBRE D'ANNEE DE DMO VISIBLE AU MOIS PAR MOIS DANS LES GRAPHIQUES
	$visible_datailed_history=3;
	
	// NOMBRE DE DMO OUVERTES SUR L'ANNEE EN COURS - ENGINEERING 
	$query_1='
			SELECT  
				year(`t2`.`Issue_Date`) as "FY",
				DATE_FORMAT((`t2`.`Issue_Date`) , "%b") as "Month", 
				count( case when `t2`.`Division`="Energy" THEN 1 END) as "Energy",
				count( case when `t2`.`Division`="Industry" THEN 1 END) as "Industry",
				count( case when `t2`.`Division`="Aerospace" THEN 1 END) as "Aerospace"	
			FROM 
				`tbl_dmo` AS `t2`
			WHERE
				(YEAR(NOW()) - YEAR(Issue_Date))<='. $visible_datailed_history .'
				AND `t2`.`Type` like "Engineering"
			GROUP BY year(`t2`.`Issue_Date`), 
					 month(`t2`.`Issue_Date`)
			ORDER BY 
					year(`t2`.`Issue_Date`) ASC, 
					month(`t2`.`Issue_Date`) ASC';

	// NOMBRE DE DMO CLOTUREES SUR L'ANNEE EN COURS - ENGINEERING
	$query_2='
			SELECT  
				year(`t2`.`End_Date`) as "FY",
				DATE_FORMAT((`t2`.`End_Date`) , "%b") as "Month",			
				count( case when `t2`.`Division`="Energy" THEN 1 END) as "Energy",
				count( case when `t2`.`Division`="Industry" THEN 1 END) as "Industry",
				count( case when `t2`.`Division`="Aerospace" THEN 1 END) as "Aerospace"	
			FROM 
				`tbl_dmo` AS `t2`
			WHERE
				Status like "Closed" AND (YEAR(NOW()) - YEAR(Issue_Date))<='.$visible_datailed_history.'
				AND `t2`.`Type` like "Engineering"
			GROUP BY year(`t2`.`End_Date`), 
					 month(`t2`.`End_Date`)
			ORDER BY 
					year(`t2`.`End_Date`) ASC, 
					month(`t2`.`End_Date`) ASC';
	
	// NOMBRE DE DMO ACTUELLEMENT OUVERTES PAR DIVISION
	// $query_3='SELECT 
				// Division, 
				// count( case when `t2`.`Status`="Open" THEN 1 END) as "Open" ,			
				// count( case when `t2`.`Status`="Closed" THEN 1 END) as "Closed"
			// FROM 
				// `tbl_dmo` AS `t2`
			// GROUP BY Division';
	
	// NOMBRE DE DMO CLOTUREES SUR L'ANNEE EN COURS - METHOD
	$query_4='
			SELECT  
				year(`t2`.`Issue_Date`) as "FY",
				DATE_FORMAT((`t2`.`Issue_Date`) , "%b") as "Month", 
				count( case when `t2`.`Type`="Method Assy." THEN 1 END) as "Method Assy.",
				count( case when `t2`.`Type`="Method Lab." THEN 1 END) as "Method Lab."	
			FROM 
				`tbl_dmo` AS `t2`
			WHERE
				(YEAR(NOW()) - YEAR(Issue_Date))<='. $visible_datailed_history .'
			GROUP BY year(`t2`.`Issue_Date`), 
					 month(`t2`.`Issue_Date`)
			ORDER BY 
					year(`t2`.`Issue_Date`) ASC, 
					month(`t2`.`Issue_Date`) ASC';
					
	// NOMBRE DE DMO CLOTUREES SUR L'ANNEE EN COURS - METHOD 
	$query_5='
			SELECT  
				year(`t2`.`End_Date`) as "FY",
				DATE_FORMAT((`t2`.`End_Date`) , "%b") as "Month",			
				count( case when `t2`.`Type`="Method Assy." THEN 1 END) as "Method Assy.",
				count( case when `t2`.`Type`="Method Lab." THEN 1 END) as "Method Lab."	
			FROM 
				`tbl_dmo` AS `t2`
			WHERE
				Status like "Closed" AND (YEAR(NOW()) - YEAR(Issue_Date))<='.$visible_datailed_history.'
			GROUP BY year(`t2`.`End_Date`), 
					 month(`t2`.`End_Date`)
			ORDER BY 
					year(`t2`.`End_Date`) ASC, 
					month(`t2`.`End_Date`) ASC';

	

	// SUR LES DERNIERES ANNEES
	// NOMBRE DE DMO OUVERTE
	// TEMPS D'ATTENTE DES DMO TOUJOURS OUVERTES
	// TEMPS DE TRAITEMENT MOYEN DES DMO CLOTUREES
	// $visible_datailed_history=2;
	// $query_5='SELECT 
				// year(Issue_Date), 
				// "-" as "Month(Issue_Date)", 
				// Division, 
				// count( case when Status="Open" THEN 1 END) as "Open", 
				// ROUND(AVG( case when Status="Closed" AND Issue_date not like Now() and (YEAR(NOW()) - YEAR(Issue_Date)) >'.$visible_datailed_history.' THEN DATEDIFF(end_date, issue_date) END)) as "REACTIVITY", 
				// ROUND(AVG( case when Status ="Open" AND Issue_date not like Now() and (YEAR(NOW()) - YEAR(Issue_Date))>'.$visible_datailed_history.' THEN DATEDIFF(NOW(), issue_date) END)) as "WAITING TIME"
			// FROM 
				// tbl_dmo
			// WHERE 
				// (YEAR(NOW()) - YEAR(Issue_Date))>'.$visible_datailed_history.'
			// GROUP BY 
				// Division, 
				// YEAR(Issue_Date)
			 
			// UNION
			 
			// SELECT 
				// YEAR(Issue_Date), 
				// MONTH(Issue_Date), 
				// Division, 
				// count( case when `t2`.`Status`="Open" THEN 1 END) as "Open", 
				// ROUND(AVG( case when `t2`.`Status`="Closed" AND Issue_date not like Now() and (YEAR(NOW()) - YEAR(Issue_Date))<='.$visible_datailed_history.' THEN DATEDIFF(end_date, issue_date) END)) as "LEADTIME", 
				// ROUND(AVG( case when `t2`.`Status`="Open" AND Issue_date not like Now() and (YEAR(NOW()) - YEAR(Issue_Date))<='.$visible_datailed_history.' THEN DATEDIFF(NOW(), issue_date) END)) as "WAITING TIME"
			 // FROM 
				// `tbl_dmo` AS `t2` 
			 // WHERE 
				// (YEAR(NOW()) - YEAR(Issue_Date))<='.$visible_datailed_history.'
			 // GROUP BY
				// Division, 
				// YEAR(Issue_Date), 
				// MONTH(Issue_Date)';
			
	include('../DMO_Connexion_DB.php');

	// DATA GRAPH 1
	$data_graph_1="['Period','Eng. Energy','Eng. Industry','Eng. Aero.','Cumul'],";
	$cumulative_1=0;
	$tmp_year=0;
	$cumulative_1_yearly=0;
	$resultat_1 = $mysqli_dmo->query($query_1);
	while ($row_1 = $resultat_1->fetch_assoc())
	{
		
		if($tmp_year!=0)
		{
			if($tmp_year!=$row_1['FY'])
			{
				$tmp_year=$row_1['FY'];
				$cumulative_1_yearly=$row_1['Energy']+$row_1['Industry']+$row_1['Aerospace'];
			} else {
				$cumulative_1_yearly=$cumulative_1_yearly+$row_1['Energy']+$row_1['Industry']+$row_1['Aerospace'];
			}
		} else {
			$tmp_year=$row_1['FY'];
			$cumulative_1_yearly=$cumulative_1_yearly+$row_1['Energy']+$row_1['Industry']+$row_1['Aerospace'];
		}
		$data_graph_1=$data_graph_1 ."['".$row_1['FY'].'-'.$row_1['Month']."',".$row_1['Energy'].",".$row_1['Industry'].",".$row_1['Aerospace'].",".$cumulative_1_yearly."],";
	}
	$data_graph_1=substr($data_graph_1, 0, -1);
	// ------------

	
	// DATA GRAPH 2
	$data_graph_2="['Period','Eng. Energy','Eng. Industry','Eng. Aero.','Cumul'],";
	$cumulative_2=0;
	$tmp_year=0;
	$cumulative_2_yearly=0;
	$resultat_2 = $mysqli_dmo->query($query_2);
	while ($row_2 = $resultat_2->fetch_assoc())
	{	
		if($tmp_year!=0)
		{
			if($tmp_year!=$row_2['FY'])
			{
				$tmp_year=$row_2['FY'];
				$cumulative_2_yearly=$row_2['Energy']+$row_2['Industry']+$row_2['Aerospace'];
			} else {
				$cumulative_2_yearly=$cumulative_2_yearly+$row_2['Energy']+$row_2['Industry']+$row_2['Aerospace'];
			}
		} else {
			$tmp_year=$row_2['FY'];
			$cumulative_2_yearly=$cumulative_2_yearly+$row_2['Energy']+$row_2['Industry']+$row_2['Aerospace'];
		}

		$cumulative_2=$cumulative_2+$row_2['Energy']+$row_2['Industry']+$row_2['Aerospace'];
		$data_graph_2=$data_graph_2 ."['".$row_2['FY'].'-'.$row_2['Month']."',".$row_2['Energy'].",".$row_2['Industry'].",".$row_2['Aerospace'].",".$cumulative_2_yearly."],";
	}
	$data_graph_2=substr($data_graph_2, 0, -1);
	// -----------


	// DATA GRAPH 3
	// $bar_color=['','#b87333','silver','gold',''];
	// $data_graph_3="['Division', ''],";
	// $resultat_3 = $mysqli_dmo->query($query_3);
	// $result_3_count=0;
	// while ($row_3 = $resultat_3->fetch_assoc())
	// {	
		// $data_graph_3=$data_graph_3 ."['".$row_3['Division']."',".$row_3['Open']."],";
		// $result_3_count=$result_3_count+1;
	// }

	// $data_graph_3=substr($data_graph_3, 0, -1);
	
	
	
	// DATA GRAPH 4
	// ------------
	$data_graph_4="['Period','Method Assy.','Method Lab.','Cumul'],";
	$cumulative_4=0;
	$tmp_year=0;
	$cumulative_4_yearly=0;
	$resultat_4 = $mysqli_dmo->query($query_4);
	while ($row_4 = $resultat_4->fetch_assoc())
	{	
		if($tmp_year!=0)
		{
			if($tmp_year!=$row_4['FY'])
			{
				$tmp_year=$row_4['FY'];
				$cumulative_4_yearly=$row_4['Method Assy.']+$row_4['Method Lab.'];
			} else {
				$cumulative_4_yearly=$cumulative_4_yearly+$row_4['Method Assy.']+$row_4['Method Lab.'];
			}
		} else {
			$tmp_year=$row_4['FY'];
			$cumulative_4_yearly=$cumulative_4_yearly+$row_4['Method Assy.']+$row_4['Method Lab.'];
		}

		$cumulative_4=$cumulative_4+$row_4['Method Assy.']+$row_4['Method Lab.'];
		$data_graph_4=$data_graph_4 ."['".$row_4['FY'].'-'.$row_4['Month']."',".$row_4['Method Assy.'].",".$row_4['Method Lab.'].",".$cumulative_4_yearly."],";
	}
	$data_graph_4=substr($data_graph_4, 0, -1);
	// -----------

	// ------------


	// DATA GRAPH 5
	// ------------
	$data_graph_5="['Period','Method Assy.','Method Lab.','Cumul'],";
	$cumulative_5=0;
	$tmp_year=0;
	$cumulative_5_yearly=0;
	$resultat_5 = $mysqli_dmo->query($query_5);
	while ($row_5 = $resultat_5->fetch_assoc())
	{	
		if($tmp_year!=0)
		{
			if($tmp_year!=$row_5['FY'])
			{
				$tmp_year=$row_5['FY'];
				$cumulative_5_yearly=$row_5['Method Assy.']+$row_5['Method Lab.'];
			} else {
				$cumulative_5_yearly=$cumulative_5_yearly+$row_5['Method Assy.']+$row_5['Method Lab.'];
			}
		} else {
			$tmp_year=$row_5['FY'];
			$cumulative_5_yearly=$cumulative_5_yearly+$row_5['Method Assy.']+$row_5['Method Lab.'];
		}

		$cumulative_5=$cumulative_5+$row_5['Method Assy.']+$row_5['Method Lab.'];
		$data_graph_5=$data_graph_5 ."['".$row_5['FY'].'-'.$row_5['Month']."',".$row_5['Method Assy.'].",".$row_5['Method Lab.'].",".$cumulative_5_yearly."],";
	}
	$data_graph_5=substr($data_graph_5, 0, -1);
	// ------------

	$mysqli_dmo->close();

?>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>


<script>
	window.onresize = drawVisualization;

	google.charts.load('current', {'packages':['corechart']});
	google.charts.setOnLoadCallback(drawVisualization);
	

	function drawVisualization() 
	{
		//Y_axis_consolidation();
			

		var data_1 = google.visualization.arrayToDataTable([
		  <?php echo $data_graph_1 ?>
		]);

		var data_2 = google.visualization.arrayToDataTable([
		  <?php echo $data_graph_2 ?>
		]);
		
		var data_4 = google.visualization.arrayToDataTable([
		  <?php echo $data_graph_4 ?>
		]);
		
		var data_5 = google.visualization.arrayToDataTable([
		  <?php echo $data_graph_5 ?>
		]);


		var options_1 = 
		{
			backgroundColor: 'transparent',
			legend: { position: 'top', alignment: 'center' },
			fontSize:13,
			series: {
				0: {axis: 'month', targetAxisIndex: 0, type: 'bars',  color:'#00333F'},
				1: {axis: 'month', targetAxisIndex: 0, type: 'bars',  color:'#00677E'},
				2: {axis: 'month', targetAxisIndex: 0, type: 'bars',  color:'#0089A8'},
				3: {axis: 'year', targetAxisIndex: 1, type: 'line', color:'#A81F00'}
			}, 
			isStacked: true,
			vAxes: {
					0: {
						title: 'Monthly Creation',
						viewWindow: {
							min: 0,
							max: 100
						} ,
					},
					1: {
						title: 'Yearly',
						viewWindow: {
								min: 0,
								max: 500
							},
						},
					},
			bar: { groupWidth: '85%' },
			chartArea:{width:'75%', height:'75%'},
		};

		var options_2 = 
		{

			backgroundColor: 'transparent',
			legend: { position: 'top', alignment: 'center' },
			fontSize:13,
			series: {
				0: {targetAxisIndex: 0, type: 'bars', color:'#333F00'},
				1: {targetAxisIndex: 0, type: 'bars', color:'#677E00'},
				2: {targetAxisIndex: 0, type: 'bars', color:'#89A800'},
				3: {targetAxisIndex: 1, type: 'line', color:'#2D552D'}
				},
			isStacked: true,					
			vAxes: {
					0: {
						title: 'Monthly Closure',
						TextStyle: {
									color: 'red', 
									fontSize:'30' 
									},
						viewWindow: {
										min: 0,
										max: 100,
									},
						
					},
					1: {
						title: 'Yearly',
						viewWindow: {
										min: 0,
										max: 500
									},
						}
					},
			bar: { groupWidth: '85%' },
			chartArea:{width:'80%', height:'75%'},
		};

		// var options_3 = 
		// {
			// isStacked: true,
			// backgroundColor: 'transparent',
			// legend: { position: 'labeled', alignment: 'center', position: 'right'},
			// pieSliceText: 'value',
			// slices: {
					// 0: {offset: 0.2, color:'#246896'},
					// 1: {offset: 0.2, color:'#2A80B9'},
					// 2: {offset: 0.2, color:'#B4B4B4'},
			// },
			// pieSliceBorderColor: 'black',
			// pieHole: 0.45,
			// fontSize:14,
			// pieStartAngle: 90,
			// chartArea:{left:20, top:20, width:'90%', height:'90%'},
		// };
		
		var options_4 = 
		{
			backgroundColor: 'transparent',
			legend: { position: 'top', alignment: 'center' },
			fontSize:13,
			series: {
				0: {axis: 'month', targetAxisIndex: 0, type: 'bars',   color:'#9F9060'},
				1: {axis: 'month', targetAxisIndex: 0, type: 'bars',   color:'#CEAA30'},
				//2: {axis: 'month', targetAxisIndex: 0, type: 'bars',   color:'#FDC300'},
				2: {axis: 'year', targetAxisIndex: 1, type: 'line', color:'#A81F00'}
			}, 
			isStacked: true,
			vAxes: {
					0: {
						title: 'Monthly Creation',
						viewWindow: {
							min: 0,
							max: 10
						} ,
						
					},
					1: {
						title: 'Yearly',
						viewWindow: {
								min: 0,
								max: 100
						},
					},
					},
			bar: { groupWidth: '85%' },
			chartArea:{width:'75%',height:'75%'},
		};
		
		var options_5 = 
		{

			backgroundColor: 'transparent',
			legend: { position: 'top', alignment: 'center' },
			fontSize:13,
			series: {
				0: {targetAxisIndex: 0, type: 'bars', color:'#606F9F'},
				1: {targetAxisIndex: 0, type: 'bars', color:'#3054CE'},
				//2: {targetAxisIndex: 0, type: 'bars', color:'#003AFD'},
				2: {targetAxisIndex: 1, type: 'line', color:'#065163'}
				},
			isStacked: true,					
			vAxes: {
					0: {
						title: 'Monthly Closure',
						TextStyle: {
									color: 'red', 
									fontSize:'30' 
									},
						viewWindow: {
										min: 0,
										max: 10,
									},
						
					},
					1: {
						title: 'Yearly',
						viewWindow: {
										min: 0,
										max: 100
									},
						}
					},
			bar: { groupWidth: '85%' },
			chartArea:{width:'80%', height:'75%'},
		};

		var chart_1 = new google.visualization.ComboChart(document.getElementById('chart_div_1'));
		chart_1.draw(data_1, options_1, {responsive: true});
		
		var chart_2 = new google.visualization.ComboChart(document.getElementById('chart_div_2'));
		chart_2.draw(data_2, options_2, {responsive: true});
		
		//var chart_3 = new google.visualization.PieChart(document.getElementById('chart_div_3'));
		//chart_3.draw(data_3, options_3, {responsive: true});
		
		var chart_4 = new google.visualization.ComboChart(document.getElementById('chart_div_4'));
		chart_4.draw(data_4, options_4);
		 
		var chart_5 = new google.visualization.ComboChart(document.getElementById('chart_div_5'));
		chart_5.draw(data_5, options_5);

			// var chart_5 = new google.visualization.PieChart(document.getElementById('chart_div_5'));
			// chart_5.draw(data_5, options_5);
			
		
			 // var dataView_1 = new google.visualization.DataView(data_1);
	  // dataView_1.setColumns([
		// reference existing columns by index
		// 0,4,
		// add function for line color
		// {
		  // calc: function(data, row) {
			// var colorDown = 'navy';
			// var colorUp = 'red';

			// if ((row === 0) && (data.getValue(row, 1) < data.getValue(row + 1, 1))) {
			  // return colorDown;
			// } else if ((row >= 4) && (data.getValue(row - 1, 1) < data.getValue(row, 1))) {
			  // return colorDown;
			// }
			// return colorUp;
		  // },
		  // type: 'string',
		  // role: 'style'
		// }
	  // ]);



	  //var chart = new google.visualization.LineChart(document.getElementById('chart_div'));
	  //chart.draw(dataView, options);

		
		
	}
</script>		



</head>

<title>

</title>



<body style="text-indent:20px">

<div id="Section_Title" colspan=3>
	KPI / Key indicators
</div>

<table id="t07">

	<tr>
		<td rowspan=2 style="text-orientation: mixed;writing-mode: vertical-rl;width:40px;font-weight:bold;font-size:16">
			ENGINERING
		</td>
		<td style="width:25%">
			<div style="margin-left:15px;text-align:justify">
				<div>
					Number of <font style="text-decoration:underline">DMO opened per issue date </font>each year, month and division over the current year and the last <?php echo $visible_datailed_history; ?> years.
				</div>
				<div>
					A yearly cumulative total shows up as the curve on the graph.
				</div>
				<br>
			</div>
			<div style="margin-left:-15px">
				<?php
					$nb_month_displayed=18;
					
					$header=explode("'],['", $data_graph_1);
					$table_data_1_2_header=str_replace("['","<table id=\"t08\" ><tr><th>", $header[0]);
					$table_data_1_2_header=str_replace("','","</th><th>", $table_data_1_2_header);
					$table_data_1_2_header=$table_data_1_2_header."</th></tr>";
							
					$header=explode("'],['", $data_graph_1);
					$table_data_raw=explode("],['", $header[1]);
					$table_data_1="";
					for($i = 0; $i < count($table_data_raw); ++$i) {
						if ($i>= (count($table_data_raw) - $nb_month_displayed))
						{
							$table_data_raw[$i]=str_replace("'","", $table_data_raw[$i]);
							$table_data_raw[$i]=str_replace(",","</td><td>", $table_data_raw[$i]);
							$table_data_raw[$i]=str_replace("]","", $table_data_raw[$i]);
							$table_data_raw[$i]="<tr><td>".$table_data_raw[$i]."</td></tr>";
							$table_data_1=$table_data_raw[$i].$table_data_1;
						}
					}
					$table_data_1=$table_data_1."</table>";
					echo $table_data_1_2_header; 
					echo $table_data_1;
				?>
			</div>
		</td>
		<td>
			<div id="chart_div_1" style="width:100%;height:400px;"></div>
		</td>
	</tr>
	<tr>
		<td>
			<div style="margin-left:15px;text-align:justify">
				<div>
					Number of <font style="text-decoration:underline">DMO closed</font> each month of the last <?php echo $visible_datailed_history; ?> years.
				</div>
				<div>
					A yearly cumulative total shows up as the curve on the graph.
				</div>
				<br>
			</div>
			<div>
				<?php				
					$header=explode("'],['", $data_graph_2);
					$table_data_raw=explode("],['", $header[1]);
					$table_data_2="";
					for($i = 0; $i < count($table_data_raw); ++$i) {
						if ($i>= (count($table_data_raw) - $nb_month_displayed))
						{
							$table_data_raw[$i]=str_replace("'","", $table_data_raw[$i]);
							$table_data_raw[$i]=str_replace(",","</td><td>", $table_data_raw[$i]);
							$table_data_raw[$i]=str_replace("]","", $table_data_raw[$i]);
							$table_data_raw[$i]="<tr><td>".$table_data_raw[$i]."</td></tr>";
							$table_data_2=$table_data_raw[$i].$table_data_2;
						}
					}
					$table_data_2=$table_data_2."</table>";
					echo $table_data_1_2_header; 
					echo $table_data_2 ;
				?>
			</div>
		</td>
		<td>
			<div id="chart_div_2" style="width:100%;height:400px;"></div>
		</td>
	</tr>
	
	<!--<tr>
		
		<td>
			<div style="margin-left:15px;text-align:justify">
				<div>
					Number of <font style="text-decoration:underline">DMO closed</font> each month of the last X years.
				</div>
				<div>
					A yearly cumulative total shows up as the curve on the graph.
				</div>
				<br>
			</div>
			<div>
				
				
				
			</div>
		</td>
		<td>
			<div id="chart_div_3" style="width:100%;height:400px;"></div>
		</td>
	</tr>-->
	
	<tr>
		<td rowspan=2 style="text-orientation: mixed;writing-mode: vertical-rl;width:20px;font-weight:bold;font-size:16">
			ASSEMBLY & LABORATORY METHOD
		</td>
		<td>
			<div style="margin-left:15px;text-align:justify">
				<div>
						Number of <font style="text-decoration:underline">DMO opened per issue date </font>each year, month and division over the current year and the last <?php echo $visible_datailed_history; ?> years.
				</div>
				<div>
					A yearly cumulative total shows up as the curve on the graph.
				</div>
				<br>
			</div>
			<div>
				<?php				
					$header=explode("'],['", $data_graph_4);
					$table_data_4_5_header=str_replace("['","<table id=\"t08\" ><tr><th>", $header[0]);
					$table_data_4_5_header=str_replace("','","</th><th>", $table_data_4_5_header);
					$table_data_4_5_header=$table_data_4_5_header."</th></tr>";
				
					
					$table_data_raw=explode("],['", $header[1]);
					$table_data_4="";
					for($i = 0; $i < count($table_data_raw); ++$i) {
						if ($i>= (count($table_data_raw) - $nb_month_displayed))
						{
							$table_data_raw[$i]=str_replace("'","", $table_data_raw[$i]);
							$table_data_raw[$i]=str_replace(",","</td><td>", $table_data_raw[$i]);
							$table_data_raw[$i]=str_replace("]","", $table_data_raw[$i]);
							$table_data_raw[$i]="<tr><td>".$table_data_raw[$i]."</td></tr>";
							$table_data_4=$table_data_raw[$i].$table_data_4;
						}
					}
					$table_data_4=$table_data_4."</table>";
					echo $table_data_4_5_header; 
					echo $table_data_4 ;
				?>
			</div>
		</td>
		<td>
			<div id="chart_div_4" style="width:100%;height:400px;"></div>
		</td>
	</tr>
	
	<tr>
		<td>
			<div style="margin-left:15px;text-align:justify">
				<div>
					Number of <font style="text-decoration:underline">DMO closed</font> each month of the last <?php echo $visible_datailed_history; ?> years.
				</div>
				<div>
					A yearly cumulative total shows up as the curve on the graph.
				</div>
				<br>
			</div>
			<div>
				<?php				
					$header=explode("'],['", $data_graph_5);
					$table_data_raw=explode("],['", $header[1]);
					$table_data_5="";
					for($i = 0; $i < count($table_data_raw); ++$i) {
						if ($i>= (count($table_data_raw) - $nb_month_displayed))
						{
							$table_data_raw[$i]=str_replace("'","", $table_data_raw[$i]);
							$table_data_raw[$i]=str_replace(",","</td><td>", $table_data_raw[$i]);
							$table_data_raw[$i]=str_replace("]","", $table_data_raw[$i]);
							$table_data_raw[$i]="<tr><td>".$table_data_raw[$i]."</td></tr>";
							$table_data_5=$table_data_raw[$i].$table_data_5;
						}
					}
					$table_data_5=$table_data_5."</table>";
					echo $table_data_4_5_header; 
					echo $table_data_5 ;
				?>
			</div>
		</td>
		<td>
			<div id="chart_div_5" style="width:100%;height:400px;"></div>
		</td>
	</tr>
	
	
	
	<!--<tr>
		<td colspan=3 style="text-align:center;">
			<?php include('DMO_KPI_Overview.php') ?>
		</td>
	</tr>-->
</table>
</body>

</html>