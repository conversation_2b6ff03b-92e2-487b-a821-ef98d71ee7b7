
<?php 
function zerotodash($val)
{
	if  ($val==0)
	{
		$val="-";
	} 
return $val;
}
?>


	
	<table id="t10" style="width:70%;margin-left:40px" border=0>


		<tr style="text-align:middle">
			<td colspan=12>
				<div id="Body">
					Number of DMO per month & year of issue date:
				</div>
			</td>
		</tr>
		<tr style="border-bottom:1px black solid;border-top:1.5px black solid;text-align:center;background:#B7C4D5">
			<td  style="width:30px;text-align:center;border-left:1px black solid;"><strong> Year </strong></td>	
			<td  style="width:30px;text-align:center;border-left:1px black solid;"><strong> Month </strong></td>
			<td  style="width:30px;text-align:center;border-left:1.5px black solid;"><strong> Closed </strong></td>
			<td  style="width:30px;text-align:center;border-left:1px black solid;"><strong> Open </strong></td>
			<td  style="width:30px;text-align:center;border-left:1.5px black solid;"><strong> Created </strong></td>
			<td  style="width:30px;text-align:center;border-left:1px black solid;"><strong> Under Review </strong></td>
			<td  style="width:30px;text-align:center;border-left:1px black solid;"><strong> Accepted </strong></td>
			<td  style="width:30px;text-align:center;border-left:1px black solid;"><strong> Rejected </strong></td>
			<td  style="width:30px;text-align:center;border-left:1.5px black solid;border-right:1.5px black solid;"><strong> Total </strong></td>
			<td  style="width:30px;text-align:center;border-left:1px black solid;border-right:1px black solid;"><strong> Indus. Resp. </strong></td>
			<td  style="width:30px;text-align:center;border-left:1px black solid;border-right:1px black solid;"><strong> Ex </strong></td>
			<td  style="width:30px;text-align:center;border-left:1px black solid;border-right:1px black solid;"><strong> Avg. Leadtime <font size=1>(Calendar days)</font></strong></td>
		</tr>

		<?php
			include('../DMO_Connexion_DB.php');
			////DATE_FORMAT(`t1`.`Issue_Date`,'%M'), to replace  month(`t1`.`Issue_Date`) as 'Month', 
			$requete = "SELECT  year(`t2`.`Issue_Date`) as FY,
								month(`t2`.`Issue_Date`) as 'Month', 
								count( case when `t2`.`Status`='Closed' THEN 1 END) as 'Closed' ,
								count( case when `t2`.`Status`='Open' THEN 1 END) as 'Open',
								count( case when `t2`.`Decision`='CREATED' THEN 1 END) as 'Created',
								count( case when `t2`.`Decision`='UNDER REVIEW' THEN 1 END) as 'Under Review',
								count( case when `t2`.`Decision`='ACCEPTED' THEN 1 END) as 'Accepted',
								count( case when `t2`.`Decision`='REJECTED' THEN 1 END) as 'Rejected',
								count( case when `t2`.`Status`<>'' THEN 1 END) as 'Total',
								count( case when `t2`.`Indus_Related`='Yes' THEN 1 END) as 'Indus. Resp.',
								count( case when (`t2`.`Ex`<>'NO' && `t2`.`Ex`<>'') THEN 1 END) as 'Ex',
								(ROUND(sum(case when ((`t2`.`Decision`='ACCEPTED' || `t2`.`Decision`='REJECTED') && `t2`.`End_Date`<>'0000-00-00') THEN DATEDIFF(`t2`.`End_Date`, `t2`.`Issue_Date` ) END)/count(case when (`t2`.`Decision`='ACCEPTED' && `t2`.`End_Date`<>'0000-00-00') THEN 1 END))) as 'Leadtime'
						FROM `tbl_dmo` AS `t2`
						GROUP BY year(`t2`.`Issue_Date`), 
								month(`t2`.`Issue_Date`) 
							WITH ROLLUP
						ORDER BY year(`t2`.`Issue_Date`) DESC, 
								month(`t2`.`Issue_Date`) DESC";
							
			// SQL SPENT TIME : SELECT Year(`Issue_Date`) AS "YEAR" , month(`Issue_Date`) AS "MONTH", SUM(`Spent_Time`)
			//                  FROM `tbl_dmo` 
			//                  GROUP by year(`Issue_Date`), month(`Issue_Date`)
			//                  ORDER BY  year(`Issue_Date`) DESC,  month(`Issue_Date`) DESC;

			$resultat = $mysqli_dmo->query($requete);
			$gr_y_total=0;
			while ($row = $resultat->fetch_assoc())
			{
				if (is_null($row['FY'])==true) 						// Grand total since the creation of the tool
				{
					$border_year="border-bottom:1px black solid;border-top:2px black double";
					$Mont="Total";
					$Mont_KPI="%";
					$Ye="Grand";
					$Ye_KPI="%";
				} elseif (is_null($row['Month'])==true){ 			// Total for each year
						$border_year="border-bottom:2px black double;border-top:2px black double;background-color:#E1E6EA";
						$Mont="Total";
						$Mont_KPI="%";
						$Ye=$row['FY'];
						$Ye_KPI=$row['FY'];
				} else {											// regular style for each month
					$border_year="border-bottom:1px black solid;";
					$Mont=$row['Month'];
					$Mont_KPI=$row['Month'];
					$Ye=$row['FY'];
					$Ye_KPI=$row['FY'];
				}
				

				$Closed=zerotodash($row['Closed']);
				$Open=zerotodash($row['Open']);
				$Created=zerotodash($row['Created']);
				$Review=zerotodash($row['Under Review']);
				$Accepted=zerotodash($row['Accepted']);
				$Rejected=zerotodash($row['Rejected']);
				$Total=zerotodash($row['Total']);
				$Indus=zerotodash($row['Indus. Resp.']);
				$Ex=zerotodash($row['Ex']);
				$Leadtime=zerotodash($row['Leadtime']);

					echo '<tr><td style="border-left:1px black solid;text-align:center;'.$border_year.'">'.$Ye.'</td>
						<td style="border-left:1px black solid;text-align:center;'.$border_year.';">'.$Mont.'</td>
						<td style="background-color:#E7EDF2;border-left:1.5px black solid;text-align:center;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=%&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=Closed&Indus=%&Ex=%">'.$Closed.'</a></td>
						<td style="background-color:#E7EDF2;border-left:1px black dotted;text-align:center;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=%&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=Open&Indus=%&Ex=%">'.$Open.'</a></td>
						<td style="border-left:1.5px black solid;text-align:center;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=CREATED&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=%&Indus=%&Ex=%">'.$Created.'</a></td>
						<td style="border-left:1px black dotted;text-align:center;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=UNDER REVIEW&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=%&Indus=%&Ex=%">'.$Review.'</td>
						<td style="border-left:1px black dotted;text-align:center;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=ACCEPTED&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=%&Indus=%&Ex=%">'.$Accepted.'</a></td>
						<td style="border-left:1px black dotted;text-align:center;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=REJECTED&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=%&Indus=%&Ex=%">'.$Rejected.'</a></td>
						<td style="border-left:1.5px black solid;text-align:center;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=%&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=%&Indus=%&Ex=%"><strong>'.$Total.'</strong>'.'</a></td>
						<td style="border-left:1.5px black solid;text-align:center;border-right:1px black solid;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=%&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=%&Indus=Yes&Ex=%">'.$Indus.'</a></td>
						<td style="border-left:1.5px black solid;text-align:center;border-right:1px black solid;'.$border_year.';"><a href="DMO_KPI_Table_Focus.php?Decision=%&Issue_Year='.$Ye_KPI.'&Issue_Month='.$Mont_KPI.'&Status=%&Indus=%&Ex=Y">'.$Ex.'</a></td>
						<td style="border-left:1.5px black solid;text-align:center;border-right:1px black solid;'.$border_year.';">'.$Leadtime.'</a></td>
						</tr>';

					 
				$year_tmp=$row['FY']; 
			}
			mysqli_close($mysqli_dmo);
		?>

	

	</table>

