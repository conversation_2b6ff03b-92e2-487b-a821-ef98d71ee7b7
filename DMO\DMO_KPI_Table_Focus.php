<!DOCTYPE html>

<!--<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">-->
<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<link rel="stylesheet" type="text/css" href="DMO_List_styles.css">
<head>

<style>
.dropdown {
position: relative;
display: inline-block;
}

.dropdown-content {
		display: none;
		position: absolute;
		background-color: #f9f9f9;
		min-width: 200px;
		box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
		padding: 5px 5px 5px 5px;
		z-index: 1;
		}

.dropdown:hover .dropdown-content {
display: block;
}
</style>

</head>
<html>

<title>DMO KPI Focus</title>



<?php

$Status_choice=$_GET['Status'];;
$EngOwner_choice="%";
$Decision_choice=$_GET['Decision'];
$DMO_filter="%";
if ($_GET['Ex']=="Y")
{
	$Ex_choice_1="YES";
	$Ex_choice_2="ATEX";
	$Ex_choice_3="EX";
	$Ex_choice_4="IECEX";
	$Ex_choice_5="CSA";

} else {
	$Ex_choice_1="%";
	$Ex_choice_2="%";
	$Ex_choice_3="%";
	$Ex_choice_4="%";
	$Ex_choice_5="%";
}
$Indus_choice=$_GET['Indus'];
$Issue_year=$_GET['Issue_Year'];
$Issue_month=$_GET['Issue_Month'];

$query_1 = 'SELECT * FROM tbl_dmo where 
									Status like "'.$Status_choice.'" 
								 AND Decision like "'.$Decision_choice.'" 
								 AND DMO like "'.$DMO_filter.'"
								 AND year(Issue_Date) like "'.$Issue_year.'"
								 AND month(Issue_Date) like "'.$Issue_month.'"
								 AND (Ex like "'.$Ex_choice_1.'"
								 OR Ex like "'.$Ex_choice_2.'"
								 OR Ex like "'.$Ex_choice_3.'"
								 OR Ex like "'.$Ex_choice_4.'"
								 OR Ex like "'.$Ex_choice_5.'")
								 AND Indus_Related like "'.$Indus_choice.'"
								 ORDER BY DMO DESC;';
//print_r($query_1);
	
	include('../DMO_Connexion_DB.php');
	$resultat = $mysqli_dmo->query($query_1);
	$rowcount=mysqli_num_rows($resultat);



		echo '<table id="t01">';
		echo '<thead>';
		echo '<tr style="height:40px">';
		echo '	<th style="width:70px;">DMO</th>';
		echo '	<th style="width:100px;">Issue Date</th>';
		echo '	<th style="width:500px;">Description</th>';
		echo '	<th style="width:65px;">Range</th>';
		echo '	<th style="width:40px;">Project</th>';
		echo '	<th style="width:80px;">Requestor Name</th>';
		echo '	<th style="width:65px;">Requestor Department</th>';
		echo '	<th style="width:70px;">Decision</th>';
		echo '	<th style="width:35px;">Status</th>';
		echo '	<th style="width:35px;">Ex?</th>';
		echo '	<th style="width:35px;">Indus?</th>';
		echo '	<th style="width:120px;">Eng. Owner</th>';
		echo '<th style="width:65px;">End Date</th>'; 
		echo '<th style="width:40px;">Diff/PR</th>';

		echo '	<th style="width:450px;">Comments</th>';
		echo '</tr>';
	echo '</thead>';

	echo '<tbody>';
	
	while ($ligne = $resultat->fetch_assoc())
	{
		

		// Determination du statut pour application de la couleur de fond associee (vert = closed / bland ou gris = open)
		// -------------
		if (strtoupper($ligne['Status'])=="CLOSED")
			{	
			echo '<tr style="background-color:#E8F5E9;">';
			} else
				{
				echo '<tr>';	
				}
		// -------------

		// determination du nombre de PJ pour afficher l'icone trombone dans le tableau si nbre>=1
		// ------------------------
		$path_attachment=".\\DMO_Attachment\\DMO_".substr($ligne['DMO'],-5)."\\";
		include('Generic_Attachment_Count.php');

		echo '<td><div id="Table_results"><strong><a href="DMO_Modification_form.php?ID='.$ligne['DMO'].'&Decision=&Status=" target="_blank">'.$ligne['DMO']."</a></strong>";
		if ($attachment_count!=0)
		{
			echo "<BR><img src='.\\Resources\\Attachment.png'/>";
		}
		echo "</div></td>";
		// ------------------------
		
		echo '<td><div id="Table_results">'.$ligne['Issue_Date'].'</div></td>';
		$nbre_lignes = count (explode ("\n", htmlspecialchars($ligne['Description'], ENT_QUOTES)));
		$nmax=180;
		echo '<td><div id="Table_results_left">';
		
		if (((strlen($ligne['Description'])>=$nmax) && ($nbre_lignes>3)) || ($nbre_lignes>3) )
			{
				echo substr(nl2br($ligne['Description']),0,$nmax);
				echo '<div class="dropdown">';
				echo '<span>[...]</span>';
				echo '<div class="dropdown-content">';
				echo '<p>'.nl2br($ligne['Description']).'</p>';
				echo '</div>';
				echo '</div>';
			} else
			{
				echo substr(nl2br($ligne['Description']),0,$nmax);
				}
		echo '</div></td>';
		

		echo '<td><div id="Table_results">'.$ligne['Product_Range'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Project'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Requestor_Name'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Requestor_Dpt'].'</div></td>';		
		echo '<td><div id="Table_results">'.$ligne['Decision'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Status'].'</div></td>';	
		echo '<td><div id="Table_results">'.$ligne['Ex'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Indus_Related'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Eng_Owner'].'</div></td>';	
		echo '<td><div id="Table_results">'.$ligne['End_Date'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['PR_Number'].'</div></td>';
		
		$nbre_lignes = substr_count(nl2br($ligne['Comment']), "\n");
		echo '<td><div id="Table_results_left">';
		$nmax=150;
		if ((strlen($ligne['Comment'])>$nmax)  )
			{
				echo substr(nl2br($ligne['Comment']),0,$nmax);
				echo '<div class="dropdown">';
				echo '<span>[...]</span>';
				echo '<div class="dropdown-content">';
				echo '<p>'.nl2br($ligne['Comment']).'</p>';
				echo '</div>';
				echo '</div>';
			} else {
					echo substr(nl2br($ligne['Comment']),0,$nmax);
				}
		echo '</div>';
		echo '</td>';
		echo '</tr>';
		
	}
	echo '</tbody>';



?>

</table>

</body>
</html>