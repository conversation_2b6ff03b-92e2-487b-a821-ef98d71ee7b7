<!DOCTYPE html>

<!--<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">-->
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="DMO_List_styles.css">
<link rel="stylesheet" type="text/css" href="DMO_Styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<script>	

	// DETECTION DE L'EVENEMENT DE REDIMENSIONNEMENT DE LA FENETRE
	// CALCUL DU NOMBRE :
	// 		- D'ENREGISTREMENTS AFFICHES
	// 		- DE PAGE TOTAL
	// -----------------------------------------------------------
	window.onresize = Table_Update;
	
	function Table_Update(forced)
	{

		var previous_height=document.getElementById("previous_height").value;
		
		if (previous_height!=window.innerHeight || forced==1)
		{
			
			var status=document.getElementById("Status_choice").value;
			var dmo=document.getElementById("DMO_choice").value;
			var decision=document.getElementById("Decision_choice").value;
			var description=document.getElementById("Description_choice").value;
			var division=document.getElementById("Division_choice").value;
			var product_range=document.getElementById("Product_Range_choice").value;
			var requestor=document.getElementById("Requestor_choice").value;
			var ex=document.getElementById("Ex_choice").value;
			var engowner=document.getElementById("EngOwner_choice").value;
			var project=document.getElementById("Project_choice").value;
			var doc=document.getElementById("Document_choice_id").value;
			var type=document.getElementById("DMO_Type_choice_id").value;
			
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() 
				{
					const raw_result=this.responseText.trim();
					if (raw_result!="")
					{
						let total_rc=parseInt(this.responseText.trim());
						let results_per_page=Math.floor((window.innerHeight-68-30-35)/60);
						let nb_page=Math.ceil(total_rc/results_per_page);
						
						document.getElementById("nb_pages").innerHTML=nb_page;
						document.getElementById("results_per_page").innerHTML=results_per_page;
						document.getElementById("total_records").innerHTML=total_rc;
								
						var start_record=0;
						
						page_change(-2);
						list_record_refresh(start_record, results_per_page);
						
						document.getElementById("previous_height").value=window.innerHeight;
						
					}
				}
			var url = 'DMO_List_Results.php?dmo=' + dmo + '&status=' + status + '&decision=' + decision + '&description=' + description + '&division=' + division + '&product_range=' + product_range + '&requestor=' + requestor + '&ex=' + ex + '&engowner=' + engowner + '&project=' + project + '&document=' + doc + '&type=' + type;
			xhttp.open("GET", url);
			xhttp.send();	
		}
	}
	// -----------------------------------------------------------	
	
	// MISE A JOUR DE LA LISTE DE RESULTATS
	// ------------------------------------
	function list_record_refresh(start_record, nb_record)
	{
		var status=document.getElementById("Status_choice").value;
		var dmo=document.getElementById("DMO_choice").value;
		var decision=document.getElementById("Decision_choice").value;
		var description=document.getElementById("Description_choice").value;
		var division=document.getElementById("Division_choice").value;
		var product_range=document.getElementById("Product_Range_choice").value;
		var requestor=document.getElementById("Requestor_choice").value;
		var ex=document.getElementById("Ex_choice").value;
		var engowner=document.getElementById("EngOwner_choice").value;
		var project=document.getElementById("Project_choice").value;
		var doc=document.getElementById("Document_choice_id").value;
		var type=document.getElementById("DMO_Type_choice_id").value;
		if (isNaN(nb_record))
		{
			
		} else {
			var url='DMO_List_Table.php?dmo=' + dmo + '&status=' + status + '&decision=' + decision + '&description=' + description + '&division=' + division + '&product_range=' + product_range + '&requestor=' + requestor + '&ex=' + ex + '&engowner=' + engowner + '&project=' + project + '&document=' + doc + '&type=' + type + '&start_record=' + start_record + '&nb_record=' + nb_record;
			//alert("list_refresh() : + " + url);
			document.getElementById("DMO_Table_ID").src=url;
		}
	}		
	// ------------------------------------
	
	// CHARGE LA PAGE DE RETOUR DE LA PAGE "DEMANDE DE MODIFICATION" - NOT USED
	// ------------------------------------------------------------------------
	function go_to_right_page()
	{
		var called_url = document.location.href;
		alert(called_url.substr(-1));
		if (called_url.substr(-1)!="p")
		{
			document.getElementById("current_page_td").innerText=10;
			document.getElementById("previous_page_td").innerText=9;
			document.getElementById("total_records").innerHTML=1148;
			document.getElementById("results_per_page").innerHTML=12;
			page_change(0);
		}
	}

	// FILTRE RAPIDE POUR FILTRER SUR LES DMO OUVERTES SEULEMENT
	// ---------------------------------------------------------
	function Opened_Only()
	{	
		document.getElementById("Status_choice").value="Open";
		Table_Update(1);
	}
	// ---------------------------------------------------------
	
	// MISE A JOUR DE LA PAGINATION ET DE LA LISTE DE RESULTATS ASSOCIEE
	// -----------------------------------------------------------------
	function page_change(direction)
	{

		var current_page=parseInt(document.getElementById("current_page_td").innerText);
		var previous_page=parseInt(document.getElementById("previous_page_td").innerText);
		var total_record=parseInt(document.getElementById("total_records").innerHTML);
		
		//var nb_per_page=parseInt(document.getElementsByName("nb_per_page")[0].innerText.split("- ")[1]);
		var nb_per_page=parseInt(document.getElementById("results_per_page").innerHTML);

		var total_page=Math.ceil(total_record/nb_per_page);
		var calculated_page=0;
		
		if (current_page>=1 && direction>0 && current_page<total_page || current_page>=1 && direction <1 && previous_page>0)
		{
			switch (direction)
			{
			case -1:
			case 1:
				calculated_page=current_page + direction;
				previous_page=calculated_page-1;
				next_page=calculated_page+1;
				break;
			case -2:
				calculated_page=1;
				previous_page="";
				next_page=2;
				break;
			case 2:
				calculated_page=total_page;
				next_page="";
				previous_page=total_page-1;
				break;
			case 0:
				calculated_page=current_page;
				previous_page=previous_page;
				next_page=calculated_page+1;
				break;
			}
			
			if (previous_page==0)
			{
				previous_page="";
			}
			if (next_page>total_page)
			{
				next_page="";
			}

			document.getElementById("previous_page_td").innerText=previous_page;
			document.getElementById("next_page_td").innerText=next_page;
			document.getElementById("current_page_td").innerText=calculated_page;
			
			var start_record=(calculated_page-1)*nb_per_page;
			
			list_record_refresh(start_record, nb_per_page);
			
		}
	}
	// -----------------------------------------------------------------
	
	
	function type_doc_change(element)
	{
		alert(this.value);
		alert(this.name);
		var doc=document.getElementById("Document_choice_id").value;
		var type=document.getElementById("DMO_Type_choice_id").value;
		
	}
	
</script>

<head>


</head>
<html>

<title>DMO Table</title>
	
<body onload="Table_Update(1)">

<!--<form onsubmit="Table_Update(0)" method=POST action="">-->
	<table id="t00" border=0>
		<tr>
			<td HIDDEN>
				<input type="text" size=1 id="previous_height">
			</td>
			<td>
				<table id="t00" border=0>
					<th>
						DMO #:
					</th>
					<td>
						<input type="text" size=5  id="DMO_choice" name="DMO_choice" style="font-size:9pt; height:9pt;width:65pt;" onblur="Table_Update(1)"
							<?php 
								if (isset($_GET['dmo']))
								{
									echo 'value="'.$_GET['dmo'].'"';
								} else {
									echo 'value=""';
								}
							?>
						>
					</td>
					<th>
						Status: 
					</th>
					<td>
						<SELECT name="Status_choice" id="Status_choice" size="1" style="font-size:9pt;" onchange="Table_Update(1)" >
						<OPTION value="" ></OPTION>

						<?php
						include('../DMO_Connexion_DB.php');
						$requete = "SELECT DISTINCT Status FROM tbl_status ORDER BY Status ASC";
						$resultat = $mysqli_dmo->query($requete);
						while ($row = $resultat->fetch_assoc())
						{
							$sel='';
							if (isset($_GET['status']))
							{ 
								if ($_GET['status']==$row['Status'])
								{
									$sel='SELECTED';
								}
							}
							echo'<OPTION value ="'.$row['Status'].'" '.$sel.'>'.$row['Status'].'</option><br/>'; 
						}
						mysqli_close($mysqli_dmo);
						?>
						</SELECT>
					</td>
					<th>
						Decision:
					</th>
					<td>
						<SELECT name="Decision_choice" id="Decision_choice" size="1" style="font-size:9pt;" onchange="Table_Update(1)">
							<option value=""></option>
							<?php
							include('../DMO_Connexion_DB.php');
							$requete = "SELECT DISTINCT Decision FROM tbl_decision ORDER BY Decision ASC";
							$resultat = $mysqli_dmo->query($requete);
							while ($row = $resultat->fetch_assoc())
							{
								$sel='';
								if (isset($_GET['decision']))
								{ 
									if ($_GET['decision']==$row['Decision'])
									{
										$sel='SELECTED';
									}
								}
								echo'<OPTION value ="'.$row['Decision'].'" '.$sel.' >'.$row['Decision'].'</option><br/>'; 
							}
							mysqli_close($mysqli_dmo);
						?>
						</SELECT>
					</td>
					<th>
						Description:
					</th>
					<td>
						<input type="text" size=20  id="Description_choice" name="Description_choice" style="font-size:9pt;height:9pt;width:150pt;"  onblur="Table_Update(1)"
						<?php 
							if (isset($_GET['description']))
							{ echo 'value="'.$_GET['description'].'"' ;} 
							else 
							{ echo 'value=""';}
						?>
						>
					</td>	
					<th>
						Division
					</th>
					<td>
						<select tabindex="3" name="Division_choice" id="Division_choice" title="Division the product is handled by" onchange="Table_Update(1)">
						<option value=""></option>
						
						<!--LISTE DEROULANTE DYNAMIQUE-->
						<!------------------------------>
						<?php
							include('../SCM_Connexion_DB.php');
							$requete = "SELECT DISTINCT * FROM tbl_division ORDER BY Division ASC;";
							
							$resultat = $mysqli_scm->query($requete);
							while ($row = $resultat->fetch_assoc())
							{
								$sel='';
								if (isset($_GET['division']))
								{ 
									if ($_GET['division']==$row['Division'])
									{
										$sel='SELECTED';
									}
								}
								echo'<option value ="'.$row['Division'].'" title="'.$row['Description'].'" '.$sel.'>'.$row['Division'].'</option><br/>'; 
							}
							mysqli_close($mysqli_scm);
						?>
						<!------------------------------>
						</select>
					</td>	
					<th>
						Product Range:
					</th>
					<td>
						<select name="Product_Range_choice" id="Product_Range_choice"  style="font-size:9pt;width:110px;" onchange="Table_Update(1)">
							<option value=""></option><br/>;
							<?php
								include('../DMO_Connexion_DB.php');
								$requete = "SELECT DISTINCT Product_Range FROM tbl_product_range ORDER BY Product_Range ASC";
								$resultat = $mysqli_dmo->query($requete);
								while ($row = $resultat->fetch_assoc())
								{
									$sel='';
									if (isset($_GET['product_range']))
									{ 
										if ($_GET['product_range']==$row['Product_Range'])
										{
											$sel='SELECTED';
										}
									}
									echo'<option value ="'.$row['Product_Range'].'" '.$sel.'>'.$row['Product_Range'].'</option><br/>'; 
								}
								mysqli_close($mysqli_dmo);
							?>
						</select>
					</td>
					
				</tr>
			</table>
			<td rowspan=2 style="padding-left:5px;padding-right:5px;">
						<input type="button" value="Opened Only" title="Show opened DMO only" style="text-align:center;width:90px;height:25px;border-radius:10px;" class="btn blue2" onclick="Opened_Only()"/>
					</td>
		</tr>
		<tr>
			<td>
				<table id="t00" border=0>
					<tr>
						<th>
							Requestor:
						</th>
						<td>
							<select name="Requestor_choice" id="Requestor_choice" style="font-size:9pt;" onchange="Table_Update(1)">
							<option value=""></option><br/>;
							<?php
								include('../DMO_Connexion_DB.php');
								$requete = "SELECT DISTINCT Fullname FROM tbl_user order by Fullname asc";
								$resultat = $mysqli_dmo->query($requete);
								while ($row = $resultat->fetch_assoc())
								{
									$sel='';
									if (isset($_GET['requestor']))
									{ 
										if ($_GET['requestor']==$row['Fullname'])
										{
											$sel='SELECTED';
										}
									}
									echo'<option value ="'.$row['Fullname'].'" '.$sel.' >'.$row['Fullname'].'</option><br/>'; 
								}
								mysqli_close($mysqli_dmo);
							?>
							</select>
						</td>
						<th>
							Ex:
						</th>
						<td>
							<select name="Ex_choice" id="Ex_choice" style="font-size:9pt;" onchange="Table_Update(1)">
							<option value=""></option><br/>;
							<?php
								include('../DMO_Connexion_DB.php');
								$requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex";
								$resultat = $mysqli_dmo->query($requete);
								while ($row = $resultat->fetch_assoc())
								{
									$sel='';
									if (isset($_GET['ex']))
									{ 
										if ($_GET['ex']==$row['Ex'])
										{
											$sel='SELECTED';
										}
									}
									echo '<option value ="'.$row['Ex'].'" '.$sel.' >'.$row['Ex'].'<br/>'; 
								}
								
								echo '<option value ="YES"';
								if (isset($_GET['ex']))
								{ 
									if ($_GET['ex']=='YES')
									{
										echo 'SELECTED';
									}
								}
								echo '>YES</option>'; 
								
								mysqli_close($mysqli_dmo);
							?>
							</select>
						</td>
						<th>
							Eng. Owner:
						</th>
						<td>
							<select name="EngOwner_choice" id="EngOwner_choice" style="font-size:9pt;" onchange="Table_Update(1)"> 
							<option value=""></option><br/>;
							<?php
								include('../DMO_Connexion_DB.php');
								$requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Industry' or Department like 'Aerospace' or Department like 'Method' order by  Fullname asc ";
								$resultat = $mysqli_dmo->query($requete);
								while ($row = $resultat->fetch_assoc())
								{
									$sel='';
									if (isset($_GET['engowner']))
									{ 
										if ($_GET['engowner']==$row['Fullname'])
										{
											$sel='SELECTED';
										}
									}
									echo'<option value ="'.$row['Fullname'].'" '.$sel.'>'.$row['Fullname'].'<br/>'; 
								}
								mysqli_close($mysqli_dmo);
								?>
								</select>
						</td>
						
						<th>
							Document :
						</th>
						<td>
							<select name="Document_choice" id="Document_choice_id" style="font-size:9pt;width:205px"  onchange="Table_Update(1)">
								<option value=""></option><br/>;
								<?php
									include('../DMO_Connexion_DB.php');
									$requete = "SELECT DISTINCT Document, Type, Description FROM tbl_document order by  type desc , Document ASC ";
									$resultat = $mysqli_dmo->query($requete);
									while ($row = $resultat->fetch_assoc())
									{
										$sel='';
										if (isset($_GET['Document']))
										{ 
											if ($_GET['Document']==$row['Document'])
											{
												$sel='SELECTED';
											}
										}
										echo'<option value ="'.$row['Type'].' - '.$row['Document'].'" '.$sel.' title="'.$row['Description'].' - '.$row['Type'].'">'.$row['Type'].' - ' . $row['Document'].'<br/>'; 
									}
									mysqli_close($mysqli_dmo);
								?>
							</select>
						</td>
						
						<th>
							Type :
						</th>
						<td>
							<select name="DMO_Type_choice" id="DMO_Type_choice_id" style="font-size:9pt;width:105px" onchange="Table_Update(1)">
								<option value=""></option><br/>;
								<?php
									include('../DMO_Connexion_DB.php');

									$requete_1 = "SELECT DISTINCT Type, Description, Division FROM tbl_type order by Type ASC ";
									$resultat = $mysqli_dmo->query($requete_1);
									while ($row = $resultat->fetch_assoc())
									{
										$sel='';
										if (isset($_GET['DMO_Type']))
										{ 
											if ($_GET['DMO_Type']==$row['Type'])
											{
												$sel='SELECTED';
											}
										}
										echo'<option value ="'.$row['Type'].'" '.$sel.' title="'.$row['Description'].' - Division: '.$row['Division'].'">'.$row['Type'].'<br/>'; 
									}
									mysqli_close($mysqli_dmo);
								?>
							</select>
						</td>			

									

						<th>
							Project:
						</th>
						<td>
							<select name="Project_choice" id="Project_choice" style="font-size:9pt;width:65px" onchange="Table_Update(1)">
								<option value=""></option><br/>;
								<?php
									include('../SCM_Connexion_DB.php');

									$requete = "SELECT DISTINCT OTP, Title FROM tbl_project order by  OTP DESC ";
									$resultat = $mysqli_scm->query($requete);
									while ($row = $resultat->fetch_assoc())
									{
										$sel='';
										if (isset($_GET['project']))
										{ 
											if ($_GET['project']==$row['OTP'])
											{
												$sel='SELECTED';
											}
										}
										echo'<option value ="'.$row['OTP'].'" '.$sel.'>'.$row['OTP'].' - '.$row['Title'].'<br/>'; 
									}
									mysqli_close($mysqli_scm);
								?>
							</select>
						</td>	
					</tr>
				</table>
			</td>
		</tr>
		<tr>
			<td colspan=5 style="text-align:left;padding-left:10px;">
				<table border=0>
					<tr>
						<td style="padding-left:5px;width:40px">Page: </td>
						<td style="width:20px;font-size:7pt;padding-top:3px;text-align:right" id="previous_page_td">
						<?php if (isset($_GET['previous_page'])){echo intval($_GET['previous_page']);} else {echo '';}?>
						</td>
						<!--1--><td style="width:20px;text-align:center;font-weight:550" id="current_page_td">
						
						<?php if (isset($_GET['current_page'])){echo intval($_GET['current_page']);} else {echo '1';}?>
						</td>
						<!--2--><td style="width:20px;font-size:7pt;padding-top:3px;text-align:left" id="next_page_td">
						
						<?php if (isset($_GET['next_page'])){echo intval($_GET['next_page']);} else {echo '2';}?>
						</td>
						<td style="width:30px">
							<input type="image" src="\Common_Resources\full_arrow_icon.png" style="padding-top:3px; height:12px;cursor:pointer" title="Go to the FIRST page"  onclick="page_change(-2)"</input>
							<input type="image" src="\Common_Resources\arrow_icon.png" style="padding-bottom:3px; height:12px; transform: rotate(180deg);cursor:pointer" title="Go to the PREVIOUS page" onclick="page_change(-1)"></input>
						</td>
						<td style="width:30px">
							<input type="image" src="\Common_Resources\arrow_icon.png" style="padding-top:3px; height:12px;cursor:pointer" title="Go to the NEXT page" onclick="page_change(1)"></input>
							<input type="image" src="\Common_Resources\full_arrow_icon.png" style="padding-bottom:3px; height:12px; transform: rotate(180deg);cursor:pointer" title="Go to the LAST page" onclick="page_change(2)"></input>
						</td>
						
						<td style="width:365px" name="total_record" style="text-align:left">&nbsp- Total : 
							<span id="total_records"></span> resultats | <span id="nb_pages"></span> pages
							&nbsp-
							<span id="results_per_page"></span>
							resultats per page
						</td>
					</tr>
				</table>
			</td>
		</tr>
	</table>


<iframe
	name="DMO_Table" 
	id="DMO_Table_ID" 
	class="DMO_Table_Frame" 
	frameborder=0
	
	scrolling=yes
	src=""
	style="background-image: url(/Common_Resources/logo_scm_zoom_left_transparent.jpg);
		   background-repeat: no-repeat;
		   background-position: left bottom;
		   margin-left:-15px;
		   background-color:transparent;
		   z-index:-1"
>

</body>
</html>