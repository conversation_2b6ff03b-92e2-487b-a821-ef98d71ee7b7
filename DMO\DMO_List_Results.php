<?php

if ($_GET['status']=="" || isset($_GET['status'])==false)
{ 
	$Status_choice="%";
}	else  { 
			$Status_choice=$_GET['status'];
		}
		

if ($_GET['engowner']=="" || isset($_GET['engowner'])==false)
{ 
	$EngOwner_choice="%";
}	else  { 
			$EngOwner_choice=$_GET['engowner'];
		}

if ($_GET['decision']=="" || isset($_GET['decision'])==false)
{ 
	$Decision_choice="%";
}	else  { 
			$Decision_choice=$_GET['decision'];
		}

if ($_GET['requestor']=="" || isset($_GET['requestor'])==false)
	{ 
		$Requestor_choice="%";
	}	else  { 
			$Requestor_choice=$_GET['requestor'];
			}

if ($_GET['description']=="" || isset($_GET['description'])==false)
{ 
	$Description_filter="%";
}	else   {
			if (strlen($_GET['description'])>0)
				{ 
					$Description_filter=str_replace("*","%",$_GET['description']);
				} else 
					{
						$Description_filter="%";
					}
				}
if ($_GET['dmo']=="" || isset($_GET['dmo'])==false)
{ 
	$DMO_filter="%";
}	else  { 
			if (strlen($_GET['dmo'])>0)
			{ 
				$DMO_filter="%".str_replace("*","%",$_GET['dmo']);
				//$DMO_filter="%".str_replace("*","%",$_GET['dmo'])."%";
			} else 
				{
					$DMO_filter="%";
				}
			}
if ($_GET['product_range']=="" || isset($_GET['product_range'])==false)
{ 
	$Product_Range_choice="%";
}	else  { 
			$Product_Range_choice=$_GET['product_range'];
		}

if ($_GET['division']=="" || isset($_GET['division'])==false)
{ 
	$Division_choice="%";
}	else  { 
			$Division_choice=$_GET['division'];
		}

if ($_GET['document']=="" || isset($_GET['document'])==false)
	{ 
		$Document_choice="%";
	}	else  { 
				if ($_GET['document']!="Other")
				{
					$split_doc = explode(" - ", $_GET['document']);
					$Document_choice=$split_doc[1];
					$Type_choice=$split_doc[0];
				} else {
					$Document_choice=$_GET['document'];
					$Type_choice="%";
				}
			}

	if ($_GET['type']=="" || isset($_GET['type'])==false)
	{ 
		$Type_choice="%";
	}	else  { 
				if (isset($Type_choice)==false)
				{
					$Type_choice=$_GET['type'];
				}
			}

if ($_GET['ex']=="" || isset($_GET['ex'])==false)
{ 
	$Ex_choice="%";
}	else  { 
			if ($_GET['ex']!="YES")
			{
				$Ex_choice=$_GET['ex'];
			} else if ($_GET['ex']=="YES") {
				$Ex_choice="CSA\" OR Ex like \"ATEX\" OR Ex like \"YES\" OR Ex like \"IECEX\" OR Ex like \"EX";
			}
			
}


if ($_GET['project']=="")
{ 
	$Project_choice="%";
}	else  { 
			$Project_choice=$_GET['project'];
}	
	
	
	// $query_1 = 'SELECT * 
				// FROM tbl_dmo 
				// WHERE 
					// Status like "'.$Status_choice.'" && 
					// Decision like "'.$Decision_choice.'" && 
					// DMO like "'.$DMO_filter.'" && 
					// Requestor_Name like "'.$Requestor_choice.'" && 
					// Product_Range like "'.$Product_Range_choice.'" && 
					// Description like "'.$Description_filter.'" && 
					// Ex like "'.$Ex_choice.'" && 
					// Eng_Owner like "'.$EngOwner_choice.'" && 
					// Division like "'.$Division_choice.'" &&
					// Project like "'.$Project_choice.'"
				// ORDER BY DMO DESC;';
	
	$query_1 = 'SELECT * 
				FROM tbl_dmo 
				WHERE 
					Status like "'.$Status_choice.'" && 
					Decision like "'.$Decision_choice.'" && 
					DMO like "'.$DMO_filter.'" && 
					Requestor_Name like "'.$Requestor_choice.'" && 
					Product_Range like "'.$Product_Range_choice.'" && 
					Description like "'.$Description_filter.'" && 
					(Ex like "'.$Ex_choice.'") && 
					Eng_Owner like "'.$EngOwner_choice.'" && 
					Division like "'.$Division_choice.'" &&
					Project like "'.$Project_choice.'" &&
					Type like "'.$Type_choice.'" &&
					Document like "'.$Document_choice.'"
				ORDER BY DMO DESC;';
							

	include('../DMO_Connexion_DB.php');
	$resultat = $mysqli_dmo->query($query_1);
	$rowcount=mysqli_num_rows($resultat);
	if ($rowcount>0)
	{
		echo $rowcount;
	} else {
		echo 0;
	}
	mysqli_close($mysqli_dmo);


?>