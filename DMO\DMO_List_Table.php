<!DOCTYPE html>

<!--<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">-->
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link rel="stylesheet" type="text/css" href="DMO_List_styles.css">



<script>	

	function open_DMO(obj)
	{
		
		// var status=parent.document.getElementById("Status_choice").value;
		// var decision=parent.document.getElementById("Decision_choice").value;
		// var description=parent.document.getElementById("Description_choice").value;
		// var division=parent.document.getElementById("Division_choice").value;
		// var product_range=parent.document.getElementById("Product_Range_choice").value;
		// var requestor=parent.document.getElementById("Requestor_choice").value;
		// var ex=parent.document.getElementById("Ex_choice").value;
		// var engowner=parent.document.getElementById("EngOwner_choice").value;
		// var project=parent.document.getElementById("Project_choice").value;
		// var nb_record=parent.document.getElementById("results_per_page").innerHTML.trim();
		// var current_page=parent.document.getElementById("current_page_td").innerHTML.trim();
		// var dmo_filter=parent.document.getElementById("DMO_choice").value;
		// var total_record=parent.document.getElementById("total_records").innerHTML.trim();
		const dmo=obj.cells[0].textContent.trim();
		
		if (dmo!="")
		{

			//var url="DMO_Modification_form.php?dmo=" + dmo + "&status=" + status + "&decision=" + decision + "&description=" + description + "&division=" + division + "&product_range=" + product_range + "&requestor=" + requestor + "&ex=" + ex + "&engowner=" + engowner + "&project=" + project + "&nb_record=" + nb_record + "&current_page=" + current_page + "&dmo_filter=" + dmo_filter + "&total_record=" + total_record;
			var url="DMO_Modification_form.php?dmo=" + dmo + "&modif=1";
			window.open(url);
		}
	}
</script>

<head>


</head>
<html>




<?php

	if ($_GET['status']=="" || isset($_GET['status'])==false)
	{ 
		$Status_choice="%";
	}	else  { 
				$Status_choice=$_GET['status'];
			}			

	if ($_GET['engowner']=="" || isset($_GET['engowner'])==false)
	{ 
		$EngOwner_choice="%";
	}	else  { 
				$EngOwner_choice=$_GET['engowner'];
			}

	if ($_GET['decision']=="" || isset($_GET['decision'])==false)
	{ 
		$Decision_choice="%";
	}	else  { 
				$Decision_choice=$_GET['decision'];
			}

	if ($_GET['requestor']=="" || isset($_GET['requestor'])==false)
		{ 
			$Requestor_choice="%";
		}	else  { 
				$Requestor_choice=$_GET['requestor'];
				}

	if ($_GET['description']=="" || isset($_GET['description'])==false)
	{ 
		$Description_filter="%";
	}	else   {
				if (strlen($_GET['description'])>0)
					{ 
						$Description_filter=str_replace("*","%",$_GET['description']);
					} else 
						{
							$Description_filter="%";
						}
					}

	if ($_GET['dmo']=="" || isset($_GET['dmo'])==false)
	{ 
		$DMO_filter="%";
	}	else  { 
				if (strlen($_GET['dmo'])>0)
				{ 
					$DMO_filter="%".str_replace("*","%",$_GET['dmo']);
					//$DMO_filter="%".str_replace("*","%",$_GET['dmo'])."%";
				} else 
					{
						$DMO_filter="%";
					}
				}

	if ($_GET['product_range']=="" || isset($_GET['product_range'])==false)
	{ 
		$Product_Range_choice="%";
	}	else  { 
				$Product_Range_choice=$_GET['product_range'];
			}

	if ($_GET['division']=="" || isset($_GET['division'])==false)
	{ 
		$Division_choice="%";
	}	else  { 
				$Division_choice=$_GET['division'];
			}

	if ($_GET['ex']=="" || isset($_GET['ex'])==false)
	{ 
		$Ex_choice="%";
	}	else  { 
				if ($_GET['ex']!="YES")
				{
					$Ex_choice=$_GET['ex'];
				} else if ($_GET['ex']=="YES") {
					$Ex_choice="CSA\" OR Ex like \"ATEX\" OR Ex like \"YES\" OR Ex like \"IECEX\" OR Ex like \"EX";
				}
				
	}
	
	if ($_GET['document']=="" || isset($_GET['document'])==false)
	{ 
		$Document_choice="%";
	}	else  { 
				if ($_GET['document']!="Other")
				{
					$split_doc = explode(" - ", $_GET['document']);
					$Document_choice=$split_doc[1];
					$Type_choice=$split_doc[0];
				} else {
					$Document_choice=$_GET['document'];
					$Type_choice="%";
				}
			}

	if ($_GET['type']=="" || isset($_GET['type'])==false)
	{ 
		$Type_choice="%";
	}	else  { 
				if (isset($Type_choice)==false)
				{
					$Type_choice=$_GET['type'];
				}
			}

	if ($_GET['project']=="" || isset($_GET['project'])==false)
	{ 
		$Project_choice="%";
	}	else  { 
				$Project_choice=$_GET['project'];
	}
	
	if ($_GET['start_record']=="" || isset($_GET['start_record'])==false)
	{
		$start_record=0;
	} else {
			$start_record=intval($_GET['start_record']);
			}
	
	if ($_GET['nb_record']=="" || isset($_GET['nb_record'])==false)
	{
		$nb_record=10;
	} else {
			$nb_record=intval($_GET['nb_record']);
			}
				
	// $query_1 = 'SELECT * 
				// FROM tbl_dmo 
				// WHERE 
					// Status like "'.$Status_choice.'" && 
					// Decision like "'.$Decision_choice.'" && 
					// DMO like "'.$DMO_filter.'" && 
					// Requestor_Name like "'.$Requestor_choice.'" && 
					// Product_Range like "'.$Product_Range_choice.'" && 
					// Description like "'.$Description_filter.'" && 
					// Ex like "'.$Ex_choice.'" && 
					// Eng_Owner like "'.$EngOwner_choice.'" && 
					// Division like "'.$Division_choice.'" &&
					// Project like "'.$Project_choice.'"
				// ORDER BY DMO DESC
				// LIMIT '. $start_record . ' , ' . $nb_record;
	
	$query_1 = 'SELECT * 
				FROM tbl_dmo 
				WHERE 
					Status like "'.$Status_choice.'" && 
					Decision like "'.$Decision_choice.'" && 
					DMO like "'.$DMO_filter.'" && 
					Requestor_Name like "'.$Requestor_choice.'" && 
					Product_Range like "'.$Product_Range_choice.'" && 
					Description like "'.$Description_filter.'" && 
					(Ex like "'.$Ex_choice.'") && 
					Eng_Owner like "'.$EngOwner_choice.'" && 
					Division like "'.$Division_choice.'" &&
					Project like "'.$Project_choice.'" &&
					Type like "'.$Type_choice.'" &&
					Document like "'.$Document_choice.'"
				ORDER BY DMO DESC
				LIMIT '. $start_record . ' , ' . $nb_record;

	include('../DMO_Connexion_DB.php');
	$resultat = $mysqli_dmo->query($query_1);
	$rowcount=mysqli_num_rows($resultat);

	echo '<table id="t01">';
	echo '<thead>';
	echo '<tr style="height:30px">';
	echo '	<th style="width:70px;">DMO</th>';
	echo '	<th style="width:100px;">Issue Date</th>';
	echo '	<th style="width:500px;">Description</th>';
	echo '	<th style="width:55px;">Range</th>';
	echo '	<th style="width:40px;">Project</th>';
	echo '	<th style="width:70px;">Type/Doc<br>Division</th>';
	echo '	<th style="width:80px;">Requestor Name</th>';
	echo '	<th style="width:65px;">Requestor</th>';
	echo '	<th style="width:70px;">Decision <br> Status</th>';
	
//	echo '	<th style="width:35px;">Status</th>';
//	echo '	<th style="width:35px;">Indus?</th>';
	echo '	<th style="width:120px;">Eng. Owner</th>';
	if ((($Status_choice=='Closed') || ($Status_choice=="%")) )
		{
			echo '<th style="width:65px;">End Date</th>'; 
			echo '<th style="width:40px;">Diff/PR</th>';
		} 
	echo '	<th style="width:450px;">Comments</th>';
	echo '</tr>';
	echo '</thead>';

	echo '<tbody>';
	
	while ($ligne = $resultat->fetch_assoc())
	{
		

		// Determination du statut pour application de la couleur de fond associee (vert = closed / bland ou gris = open)
		// -------------
		if (strtoupper($ligne['Status'])=="CLOSED")
		{	
			echo '<tr style="background-color:#E8F5E9;" onclick="open_DMO(this)">';
		} else {
				echo '<tr onclick="open_DMO(this)">';	
				}
		// -------------

		// determination du nombre de PJ pour afficher l'icone trombone dans le tableau si nbre>=1
		// afficahge d'un icon Ex si la DMO  concerne une ref certifiée ou associée à un produit Ex
		// ------------------------
		$path_attachment=".\\DMO_Attachment\\DMO_".substr($ligne['DMO'],-5)."\\";
		include('Generic_Attachment_Count.php');
		echo '<td style="text-:center">';

		$dmo_val=$ligne['DMO'];
		$icon_tbl_strut='<table id="t_1_col" border=0><tr><th colspan=2>'.$dmo_val.'</th></tr>';
		$icon_tbl_content="";
		if ($attachment_count!=0)
		{
			$icon_tbl_content='<tr><td style="vertical-align:middle"><img height=100% src=".\\Resources\\Attachment.png"/></td>';
		}
		if ($ligne['Ex']!="NO" && $ligne['Ex']!="")
		{
			if ($icon_tbl_content=="")
			{
				$icon_tbl_content='<tr>';
			}
			$icon_tbl_content=$icon_tbl_content.'<td><img title="Impacting an Ex-certified product" src="\Common_Resources\EX_icon_4.png"  height="18px" stye="filter:saturate(100%);"></td>';
		}
		
		if ($icon_tbl_content!="")
		{
			$icon_tbl_strut=$icon_tbl_strut.$icon_tbl_content . '</tr></table>';
		} else {
			$icon_tbl_strut=$icon_tbl_strut. '<tr><td></td></tr></table>';
		}
		echo $icon_tbl_strut;
		echo '</td>';
		// ------------------------
		
		echo '<td><div id="Table_results">'.$ligne['Issue_Date'].'</div></td>';
		$nbre_lignes = count (explode ("\n", htmlspecialchars($ligne['Description'], ENT_QUOTES)));
		$nmax=140;
		echo '<td><div id="Table_results_left">';

		if (((strlen($ligne['Description'])>=$nmax)) || ($nbre_lignes>3) )
			{
				echo substr(nl2br($ligne['Description']),0,$nmax);
				echo '<div class="dropdown">';
				echo '<span>[...]</span>';
				echo '<div class="dropdown-content">';
				echo '<p>'.nl2br($ligne['Description']).'</p>';
				echo '</div>';
				echo '</div>';
			} else {
					echo substr(nl2br($ligne['Description']),0,$nmax);
					}
		echo '</div></td>';
		

		echo '<td><div id="Table_results">'.$ligne['Product_Range'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Project'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Type'].'<br>'.$ligne['Document'].'<br>'.$ligne['Division'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Requestor_Name'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Requestor_Dpt'].'</div></td>';		
		echo '<td><div id="Table_results">'.$ligne['Decision'].'<br>'.$ligne['Status'].'</div></td>';
		//echo '<td><div id="Table_results">'.$ligne['Status'].'</div></td>';	
		//echo '<td><div id="Table_results">'.$ligne['Indus_Related'].'</div></td>';
		echo '<td><div id="Table_results">'.$ligne['Eng_Owner'].'</div></td>';	

		if (($Status_choice=='Closed') || ($Status_choice=='%'))
		{
			$end_date=$ligne['End_Date'];
			if ($ligne['End_Date']=="0000-00-00")
			{
				$end_date="-";
			}
			echo '<td><div id="Table_results">'.$end_date.'</div></td>';
			echo '<td><div id="Table_results">'.$ligne['PR_Number'].'</div></td>';
		}
		
		$nbre_lignes = substr_count(nl2br($ligne['Comment']), "\n");
		echo '<td><div id="Table_results_left">';
		
		$nmax=150;
		
		if ((strlen($ligne['Comment'])>$nmax) || $nbre_lignes>3)
			{
				echo substr(nl2br($ligne['Comment']),0,$nmax);
				echo '<div class="dropdown">';
				echo '<span>[...]</span>';
				echo '<div class="dropdown-content">';
				echo '<p>'.nl2br($ligne['Comment']).'</p>';
				echo '</div>';
				echo '</div>';
			} else {
					echo substr(nl2br($ligne['Comment']),0,$nmax);
				}
		echo '</div>';
		echo '</td>';
		echo '</tr>';
		
	}
	echo '</tbody>';

?>

</table>

</body>
</html>