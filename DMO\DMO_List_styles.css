body {
color:black;
width:99%;
  font-size:8pt;
  font-family:<PERSON>homa, sans-serif;
}

div#Title {
	text-align:left;
	margin-bottom:11pt;
	font-size:12pt;
	font-weight:bolder;
	border-bottom: 1px solid black;
	font-variant: small-caps;
	}
	
div#Table_results {
	text-align:middle;
	margin-bottom:2pt;
	font-size:9pt;
	font-family:calibri, sans-serif;
	}
	
div#Table_results_left {
	text-align:left;
	margin-bottom:2pt;
	margin-left:6px;
	font-size:9pt;
	font-family:calibri, sans-serif;
	}
	
div#Filter {
	text-indent:5px;
	font-weight:normal;
	margin-left:5px;
	text-align:left;
	}

div#version{
	font-family: 'Segoe UI', sans-serif;
	letter-spacing: 1px;
	font-size:6pt;
	text-decoration:italic;
	font-weight:200;
	color:light gray;
	position:absolute;
	right:100px;
	top:38px;
	z-index:1;
}

div#Result_info {
	text-indent:10px;
	font-size:7pt;
	font-weight:normal;
	margin-left:8px;
	margin-bottom:8px;
	text-align:justify;
	}

.DMO_Table_Frame {
  position: absolute;
  z-index: 1;
  top: 76px;
  left: 15px;
  width: calc(100% - 5px);
  height: calc(100% - 68px - 15px);
  margin-left: -15px;
  background-color: transparent;
  transition: all 0.5s;
}


/*TABLEAU FILTRES DE RECHERCHE - DMO_LIST_MAIN */
/*-------------------------------------------- */
#t00 {
  width: calc(100% - 5px);
  border-collapse: collapse;
  vertical-align: middle;

}

#t00 th {
  font-weight:bold;
  background-color:transparent;
  color:black;
  margin-left:9px;
  font-size:8pt;
  font-family:Tahoma, sans-serif;
  text-align:right;
  padding-top:5px;
  padding-bottom:5px;
}

#t00 td {
 text-indent: 5%;
  margin-top: 10px;
  margin-bottom: 2px;
  text-align: left;
}

#t00 tr {
  height: 20px;
}


/*TABLEAU DES RESULTATS DE RECHERCHE - DMO_LIST_TABLE */
/*--------------------------------------------------- */
	
#t01 {
  width: 100%;
  border-collapse: collapse;
}


#t01 tbody{
	overflow-y: scroll;
	vertical-align:top;
  }

#t01 th {
	font-size:7pt;
	background-color:rgb(27,79,114);
	color:white;
	text-align: center;	
}

#t01 tr {
	border: 1px solid black;
	height:60px;
	}

#t01 tr:hover {
	background-color:rgb(203, 222, 250);  /*--> Bleu*/
	cursor:pointer;
	
}

#t01 td {
	font-size:9pt;
	text-align:center;
	}

// Set border-radius on the top-left and bottom-left of the first table data on the table row
#t01 td:first-child {
  border-radius: 10px 0 0 10px;
}

// Set border-radius on the top-right and bottom-right of the last table data on the table row
#t01 td:last-child {
  border-radius: 0 10px 10px 0;
}

/*--------------------------------------------------- */

#t_1_col
{
	width: 100%;
	height:50px;
	border-collapse: collapse;
	
}

#t_1_col th {
	color:blue;
	font-weight:bold;
	background-color:transparent;
	font-size:8pt;
	font-family:Tahoma, sans-serif;
}

#t_1_col tr {
	height:18px;
	border:none;
}
#t_1_col td {
	height:15px;
	border:none;
	vertical-align:middle;
}
	


.dropdown {
  position: relative;
  display: inline;
  float:none;
}

.dropdown-content {
  display: none;
  min-width:380px;
  min-height:50px;
  position: absolute;
  background-color: #212F3D;
  color:white;
  font-weight:normal;
  left:10px;
  top:5px;
  border-radius:5px;
  padding-left:10px;
  padding-right:6px;
  padding-top:3px;
  padding-bottom:6px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  text-align:left;
  font-size:11.5px;
 
  transition-property: display;
}

.dropdown:hover .dropdown-content {
  display: inline;
  z-index:99; 
}
