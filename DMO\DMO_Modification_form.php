<!DOCTYPE html>

<html translate="no">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta charset="UTF-8">

<link rel="stylesheet" type="text/css" href="DMO_Modification_form_styles.css">
<!--<link rel="stylesheet" type="text/css" href="DMO_styles.css">-->
<link rel="icon" type="image/png" href="\Common_Resources\icon_DMO_3.png"/>
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">


<head>


	<script>
		
		// 
		function time_int()
		{
			//x=document.forms["Update"]["Spent_Time"].value;
			//const x=document.getElementById("Spent_Time").value;
			//if (parseFloat(x)!=x)
			//{
			document.forms["Update"]["Spent_Time"].value=parseFloat(document.forms["Update"]["Spent_Time"].value);
			//} 
			
		}
		
		// Action = 1 = Closure
		// Action = 0 = Rejection
		function validateForm(action)
		{
			var t  =document.forms["Update"]["Spent_Time"].value;
			var u  =document.forms["Update"]["EX_Assessment"].value;
			var v = document.forms["Update"]["PR_Number"].value;
			var w = document.forms["Update"]["Eng_Owner"].value;
			var x = document.forms["Update"]["Indus_Related"].value;
			var y = document.forms["Update"]["Ex"].value;
			var z = document.forms["Update"]["Product_Range"].value;
			var doc = document.forms["Update"]["Document_ID"].value;
			var type = document.forms["Update"]["DMO_Type_ID"].value;
			

			if (action==1)
			{
				if (v == "" || w == "" || x == "" || y == "" || z == "" ||  t == "" || type == "" || type == "%" || doc == "" || doc == "%" || doc =="" || type == "")
					{
					alert("All the field must be filled to close a DMO");
					return false;
					}
				else if ((u == "") && (y != "NO"))
					{
						alert("In case of Ex product, an assessment must be made in the bottom field of the forms");
						return false;
					}
			} else if (action==0) {
				if (w.length == 0 )
					{
					alert("Please make sure to indicate the owner who could help the DMO creator to understand the reason(s) for rejection.");
					return false;
					}
			} else if (action == 2) {
				if (w.length == 0 )
					{
					alert("Please make sure to indicate the owner of the DMO prior to change the statut of the DMO.");
					return false;
					}
			}
				
		}
		
		// MAKE SURE COMMENTS ARE ADDED PRIOR TO ANY UPDATE
		// ------------------------------------------------
		function updateForm()
		{
			var added_comment=document.getElementById("added_comments").value;
			if (added_comments=="")
			{
				alert("Make sure to add a comment describing the update/action you achieved prior to click the update button");
				return false;
			}
		}
		// ------------------------------------------------
		
		function clean_list(x , y)
		{
			// Suppression de tous les elements de la liste
			for (let j = 0; j < y; j++)
			{
				x.remove(x[0]);
			}
			// Ajout d'un element vide en tête de liste
			var option = document.createElement("option");
			option.value="";
			option.text="";
			option.title="";
			x.add(option);
		}

		
		function type_to_doc()
		{
			
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() 
			{
				const raw_result=this.responseText.trim();
				if (raw_result.length>0)
				{
					const split_result=raw_result.split("|");
					clean_list(document.getElementById("Document_ID"), document.getElementById("Document_ID").length);
					
					split_result.unshift("__|");
					for (let i = 0; i < split_result.length; i++)
					{
						var option = document.createElement("option");
						const el=split_result[i].split("__");
						if (option.value=el[0]!="Other")
						{
							option.value=el[0];
							option.text=el[0];
							option.title=el[1];
							document.getElementById("Document_ID").add(option);
						}
					}
					//AJOUT D'UNE VALEUR AUTRE PERMETANT A L'UTILISATEUR DE SELECTIONNER UN DOC/SUJET QUI N'EST PAS LISTE
					var option = document.createElement("option");
					option.value="Other";
					option.text="Other";
					option.title="Other documents not listed above";
					document.getElementById("Document_ID").add(option);
					
					//document.getElementById("Document_ID").removeAttribute('DISABLED');
					
				} else {
					
					//document.getElementById("Document_ID").disabled = true;
					clean_list(document.getElementById("Document_ID"), document.getElementById("Document_ID").length);
				}
			}
			var type_select=document.getElementById("DMO_Type_ID").value;
			//alert(type_select.indexOf("Engineering"))
			if(type_select!="" && type_select!="%")
			{
				if (type_select.indexOf("Engineering")==0)
				{	
					var cond_eng="Engineering";
					var cond_method="";
				} else if (type_select.indexOf("Method Assy.")==0) 
					{
						var cond_eng="";
						var cond_method="Method Assy.";
					} else if (type_select.indexOf("Method Lab.")==0)
						{
							var cond_eng="";
							var cond_method="Method Lab.";	
						} else {
							var cond_eng="";
							var cond_method="";
						}
			//alert(cond_eng + "   " + cond_method);
			if (cond_eng!=""|| cond_method!="")
			{
				xhttp.open('GET', 'DMO_Document_Update.php?eng=' + cond_eng + '&method=' + cond_method);
				xhttp.send();	
			} else {
				//document.getElementById("Document_ID").disabled = true;
				document.getElementById("Document_ID").value="";
			}
		}
		}
		
		// CHANGE CURRENT URL TO THE MAIN PAGE LIST 
		// ----------------------------------------
		// function back_to_list()
		// {
			// var status=document.getElementById("Status_choice").value;
			// var dmo=document.getElementById("DMO_choice").value;
			// var dmo_filter=document.getElementById("dmo_filter").value;
			// var decision=document.getElementById("Decision_choice").value;
			// var description=document.getElementById("Description_choice").value;
			// var division=document.getElementById("Division_choice").value;
			// var product_range=document.getElementById("Product_Range_choice").value;
			// var requestor=document.getElementById("Requestor_choice").value;
			// var ex=document.getElementById("Ex_choice").value;
			// var engowner=document.getElementById("EngOwner_choice").value;
			// var project=document.getElementById("Project_choice").value;
			// var nb_record=parseInt(document.getElementById("Nb_record").value);
			// var current_page=parseInt(document.getElementById("Current_page").value);
			
			// var total_record=parseInt(document.getElementById("Total_record").value);
			
			
			// if (isNaN(nb_record)==false)
			// {
				
				// if((current_page*nb_record)>=total_record)
				// {
					// var next_page="";
					// var previous_page=current_page-1;
				// } else if (current_page>1)
				// {
					// var previous_page=current_page-1;
					// var next_page=current_page+1;
				// } else if (current_page==1)
				// {
					// var previous_page="";
					// var next_page=2;
				// }
				
				// var url='DMO_List_Main.php?dmo=' + dmo_filter + '&status=' + status + '&decision=' + decision + '&description=' + description + '&division=' + division + '&product_range=' + product_range + '&requestor=' + requestor + '&ex=' + ex + '&engowner=' + engowner + '&project=' + project + '&current_page=' + current_page + '&next_page=' + next_page + '&previous_page=' + previous_page;

				// document.location.replace(url);
			// }
		// }
		// ----------------------------------------
		
		
	</script>


</head>


<?php	
	if ( ((isset($_POST['Accept'])) || (isset ($_POST['Reject'])) || (isset ($_POST['Review'])) || (isset ($_POST['Update'])) || (isset ($_POST['Close']))) && (isset ($_POST['New_Comment'])) )
	{
		
		//Préparation des inputs de l'utilisateur
		$DMO=$_POST['DMO'];
		$Description=htmlspecialchars($_POST['Description'], ENT_QUOTES);
		$Product_Range=$_POST['Product_Range'];
		$Status=$_POST['Status'];
		$Ex=$_POST['Ex'];
		$Indus_Related=$_POST['Indus_Related'];
		$Eng_Owner=$_POST['Eng_Owner'];
		$PR_Number=$_POST['PR_Number'];
		$End_Date="0000-00-00";
		$Project=$_POST['Project'];
		$EX_Assessment=$_POST['EX_Assessment'];
		$Division=$_POST['Division'];
		$Spent_Time=intval($_POST['Spent_Time']);
		$Last_Update_Date = date("Y-m-d");
		$Last_Update_TE_ID= getenv('USERNAME') ?: getenv('USER');
		$txt_update='';
		$dmo_type=$_POST['DMO_Type'];
		$document=$_POST['Document'];
		
		if (strlen(htmlspecialchars($_POST['Comment'], ENT_QUOTES))>0)
		{
			//$Comment=$_POST['Comment']."\r\n".date("Y-m-d").': '.$_POST['New_Comment'];
			$Comment=date("Y-m-d").': '.htmlspecialchars($_POST['New_Comment'], ENT_QUOTES)."\r\n".htmlspecialchars($_POST['Comment'], ENT_QUOTES);
		} else {
				$Comment=date("Y-m-d").': '.htmlspecialchars($_POST['New_Comment'], ENT_QUOTES);
				}
		
		if (isset ($_POST['Accept'])){
			$Decision="ACCEPTED";
			$txt_update="accepted";
		}
		if (isset ($_POST['Reject'])){
			$Decision="REJECTED";
			$Status="Closed";
			$txt_update="rejected";
			$End_Date= date("Y-m-d");
		}
		if (isset ($_POST['Review'])){
			$Decision="UNDER REVIEW";
			$txt_update="set to under review";
		}
		if (isset ($_POST['Close'])){
			$Decision=$_POST['Decision'];
			$Status="Closed";
			$End_Date= date("Y-m-d");
			$txt_update="closed";
		}
		if (isset ($_POST['Update'])){
			$Decision=$_POST['Decision'];
			$txt_update="updated";
		}
		
		include('../DMO_Connexion_DB.php');			
		
		$sql = 'UPDATE tbl_dmo SET Description="'.$Description.
								 '",Product_Range="'.$Product_Range.
								 '",Division="'.$Division.
								 '",Project="'.$Project.
								 '",Decision="'.$Decision.
								 '",Status="'.$Status.
								 '",Ex="'.$Ex.
								 '",Indus_Related="'.$Indus_Related.
								 '",Eng_Owner="'.$Eng_Owner.
								 '",PR_Number="'.$PR_Number.
								 '",Comment="'.$Comment.
								 '",Last_Update_Date="'.$Last_Update_Date.
								 '",Last_Update_TE_ID="'.$Last_Update_TE_ID.
								 '",End_Date="'.$End_Date.
								 '",EX_Assessment="'.$EX_Assessment.
								 '",Spent_Time="'.$Spent_Time.
								 '",Document="'.$document.
								 '",Type="'.$dmo_type.
								 '" WHERE DMO ="'.$DMO.
								 '";';
		
		$result = $mysqli_dmo->query($sql);
		mysqli_close($mysqli_dmo);
		
		// Justification File upload
		if (isset($_FILES['justiffichiers']) )
		{
			$nbLignes=count($_FILES['justiffichiers']['name']);
			
			include('../DMO_Connexion_DB.php');	
			include('Generic_Attachment_Folder.php'); 
			
			$path_attachment=$Path_Folder . $Attachment_Folder; 
			mysqli_close($mysqli_dmo); 
			$nom = $path_attachment . substr($DMO,3,5); // Le nom du répertoire à créer				
			
			
			if (is_dir($nom))   // Vérifie si le répertoire existe creation si non existant
				{
				} else { 
					mkdir($nom,4);     
				}
			for($i=0 ; $i<=$nbLignes-1 ; $i++)
				{
				// test de la taille du fichier
				if ($_FILES['justiffichiers']['size'][$i] <= 10000000)
					{
						$tmp_file = $_FILES['justiffichiers']['tmp_name'][$i];	
						move_uploaded_file($_FILES['justiffichiers']['tmp_name'][$i], $nom.'/' .$Justification_File_Name.basename($_FILES['justiffichiers']['name'][$i]));
					} else {
							echo 'Le fichier ' . $_FILES['justiffichiers']['name'][$i] . 'est trop gros';
						}
				}
		}
	}
	
	
	
?>

	
<body>

	<form name="Update" method="post" action="" enctype="multipart/form-data">

		<table border=0>
			<tr hidden>
				<td colspan=6>
					<input type="text" size=2 id="dmo_filter" value="<?php if(isset($_GET['dmo_filter'])){echo $_GET['dmo_filter'];} ?>">
					<input type="text" size=2 id="Status_choice" value="<?php if(isset($_GET['status'])){echo $_GET['status'];} ?>">
					<input type="text" size=2 id="Decision_choice" value="<?php if(isset($_GET['decision'])){echo $_GET['decision'];} ?>">
					<input type="text" size=2 id="Description_choice" value="<?php if(isset($_GET['description'])){echo $_GET['description'];} ?>">
					<input type="text" size=2 id="Division_choice" value="<?php if(isset($_GET['division'])){echo $_GET['division'];} ?>">
					<input type="text" size=2 id="Product_Range_choice" value="<?php if(isset($_GET['product_range'])){echo $_GET['product_range'];} ?>">
					<input type="text" size=2 id="Requestor_choice" value="<?php if(isset($_GET['requestor'])){echo $_GET['requestor'];} ?>">
					<input type="text" size=2 id="Ex_choice" value="<?php if(isset($_GET['ex'])){echo $_GET['ex'];} ?>">
					<input type="text" size=2 id="EngOwner_choice" value="<?php if(isset($_GET['engowner'])){echo $_GET['engowner'];} ?>">
					<input type="text" size=2 id="Document_Choice" value="<?php if(isset($_GET['document'])){echo $_GET['document'];} ?>">
					<input type="text" size=2 id="Doc_Type_choice" value="<?php if(isset($_GET['doc_type'])){echo $_GET['doc_type'];} ?>">
					<input type="text" size=2 id="Project_choice" value="<?php if(isset($_GET['project'])){echo $_GET['project'];} ?>">
					<input type="text" size=2 id="Nb_record" value="<?php if(isset($_GET['nb_record'])){echo $_GET['nb_record'];} ?>">
					<input type="text" size=2 id="Current_page" value="<?php if(isset($_GET['current_page'])){echo $_GET['current_page'];} ?>">
					<input type="text" size=2 id="Total_record" value="<?php if(isset($_GET['total_record'])){echo $_GET['total_record'];} ?>">
				</td>
			</tr>
			<tr style="vertical-align:middle;">
				<!--<th colspan=6 style="height:30px;background-color:#2471A3;border-radius:5px">-->
				<th colspan=6 style="height:30px;border-radius:5px;padding-bottom:10px">
					<div id="Top_Title">
						<?php echo $_GET['dmo'] ?> - Modification Form
					</div>
				</th>
				<!--<td colspan=1 style="text-align:right;" >
					<table border=0 style="text-align:center;border-collaspe:collapse;">
						<tr>							
							<td hidden style="width:18px;text-align:center;background-color:lightblue;border-radius:50%;padding-left:2px;padding-right:2px;cursor:pointer">
								<input type="image" src="\Common_Resources\left_banner_slider_icon.png" style="padding-top:3px;height:14px;"  title="Back to the DMO List" onclick="back_to_list()"></input>
							</td>
						</tr>
					</table>
				</td>-->
			</tr>
				<?php
				include('../DMO_Connexion_DB.php');
				$requete = 'SELECT * FROM tbl_dmo where DMO="'.$_GET['dmo'].'";';
				$resultat = $mysqli_dmo->query($requete);
				while ($ligne = $resultat->fetch_assoc())
				{
					echo '<tr">';
					echo '<td><div id="Body">DMO: </div></td><td><div id="InpBox"><input style="border:transparent;border:none;width:80px;font-size:8pt;font-family:Tahoma, sans-serif;" type="text" name="DMO" id="DMO_choice" value="'.$ligne['DMO'].'"readonly></div></td>';
					echo '<td><div id="Body">Division: </div></td><td><div id="InpBox"><input style="border:transparent;border:none;width:80px;font-size:8pt;font-family:Tahoma, sans-serif;" type="text" name="Division" value="'.$ligne['Division'].'"readonly></div></td>';
					echo '<td><div id="Body">Issue Date: </div></td><td><div id="InpBox">'.$ligne['Issue_Date'].'</div></td>';
					echo '</tr>';
					
					echo '<tr>';
					echo '<td><div id="Body">Requestor: </div></td><td><div id="InpBox">'.$ligne['Requestor_Name'].'</div></td>';
					echo '<td><div id="Body">Status: </div></td><td><div id="InpBox"><input style="border:none;font-size:8pt;font-family:Tahoma, sans-serif;" readonly type="text" size=16  name="Status" value="'.$ligne['Status'].'"></div></td>';
					echo '<td><div id="Body">Last Update on: </div></td><td style="width:85px;"><div id="InpBox">'.$ligne['Last_Update_Date'].'</div></td>';
					echo '</tr>';
					
					echo '<tr>';
					echo '<td><div id="Body">Department: </div></td><td><div id="InpBox">'.$ligne['Requestor_Dpt'].'</div></td>';
					echo '<td><div id="Body">Decision: </div></td><td><div id="InpBox"><input style="border:none;;font-size:8pt;font-family:Tahoma, sans-serif;" readonly type="text" size=16  name="Decision" value="'.$ligne['Decision'].'"></div></td>';
					echo '<td><div id="Body">End Date: </div></td><td><div id="InpBox">'.$ligne['End_Date'].'</div></td>';
					echo '</tr>';
					
					
					echo '<tr>';
					echo '<td><div id="Body">Description: </div></td><td colspan=5><div id="InpBox"><textarea readonly rows="6" cols="90" name="Description" style="font-size:9pt;">'.$ligne['Description'].'</textarea></div></td>';
					echo '</tr>';
					
					echo '<tr>';
					echo '<td style="height:35px"><div id="Body">Indus. Responsible: </div></td><td><div id="InpBox"><select name="Indus_Related" type="submit" style="font-size:9pt;">'.
						 '<option value="'.$ligne['Indus_Related'].'">'.$ligne['Indus_Related'].'</option>';
					switch ($ligne['Indus_Related']) {
						case "Yes":
							echo '<option value="No">No</option>';
							echo '<option value=""></option>';
							break;
						case "No":
							echo '<option value="Yes">Yes</option>';
							echo '<option value=""></option>';
							break;
						case "":
							echo '<option value="Yes">Yes</option>',
								 '<option value="No">No</option>';
							break;
					}
					echo '</div></td>';
					echo '<td><div id="Body">DMO Owner: </div></td><td><div id="InpBox"><select name="Eng_Owner" type="submit" style="font-size:9pt;">';
					$requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Industry' or Department like 'Aerospace' or Department like 'Method' ORDER BY Fullname ASC;";
							$resultat = $mysqli_dmo->query($requete);
							echo '<option value=""></option>';
							$in='0';
							while ($row = $resultat->fetch_assoc())
							{
								if ($row['Fullname']==$ligne['Eng_Owner'])
								{
									echo'<option value ="'.$row['Fullname'].'" Selected>'.$ligne['Eng_Owner'].'</option><br/>';
									$in='1';
								} else {
										echo'<option value ="'.$row['Fullname'].'">'.$row['Fullname'].'</option><br/>';
									 }
							}
					if ($in=='0' && $ligne['Eng_Owner']!="")
					{
						echo'<option value ="'.$ligne['Eng_Owner'].'" Selected>'.$ligne['Eng_Owner'].'</option><br/>';
					}
					echo '</div></td>';
					echo '<td rowspan=2 colspan=2>';
						include('Generic_Attachment_Folder.php'); 
						$path_attachment=$Path_Folder . $Attachment_Folder . substr($_GET['dmo'],-5)."\\";
						echo '<div id="InpBox">';
						include('Generic_Attachment_List.php');
						echo '</div>';
					echo '</td>';						
					echo '</tr>';
					
					echo '<tr>';
					echo '<td style="height:35px"><div id="Body">Ex Rated (CSA,IECEX,ATEX): </div></td><td><div id="InpBox"><select name="Ex" type="submit" style="font-size:9pt;">';
					echo '<option value=""></option>';
							$requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex ASC;";
							$resultat = $mysqli_dmo->query($requete);
							$in='0';
							while ($row = $resultat->fetch_assoc())
							{
								if ($row['Ex']==$ligne['Ex'])
								{
									echo'<option value ="'.$row['Ex'].'" Selected>'.$ligne['Ex'].'</option><br/>';
									$in='1';
								} else {
										echo'<option value ="'.$row['Ex'].'">'.$row['Ex'].'</option><br/>';
										}
							}
					if ($in=='0' && $ligne['Ex']!="")
					{
						echo'<option value ="'.$ligne['Ex'].'" Selected>'.$ligne['Ex'].'</option><br/>';
					}

					echo '</div></td>';
					echo '<td><div id="Body">Product Range: </div></td><td><div id="InpBox"><select name="Product_Range" type="submit" style="width:110px;font-size:9pt;font-family:Tahoma, sans-serif;">';
					echo '<option value=""></option>';
						$requete = 'SELECT DISTINCT Product_Range FROM tbl_product_range WHERE Division like "'.$ligne['Division'].'" ORDER BY Product_Range ASC;';
						$resultat = $mysqli_dmo->query($requete);
						$in='0';
						while ($row = $resultat->fetch_assoc())
						{
							if ($row['Product_Range']==$ligne['Product_Range'])
							{
								echo'<option value ="'.$row['Product_Range'].'" Selected>'.$ligne['Product_Range'].'</option><br/>';
								$in='1';
							} else {
									echo'<option value ="'.$row['Product_Range'].'">'.$row['Product_Range'].'</option><br/>';
									}
						}
						if ($in=='0' && $ligne['Product_Range']!="")
							{
								echo'<option value ="'.$ligne['Product_Range'].'" Selected>'.$ligne['Product_Range'].'</option><br/>';
							}	
					echo '</div></td>';
					echo '</tr>';

					echo '<tr>';
					echo '<td style="height:35px;"><div id="Body">PR/Diffusion #: </div></td><td><div id="InpBox"><input type="text" size=8 name="PR_Number" style="font-size:8pt;height:&&px" placeholder="NA, XXXXXX" value="'.$ligne['PR_Number'].'">';
					if ($ligne['PR_Number']!="")
							{
								$link='https://frscmbe.scmlemans.com/REL/REL_Pack_Overview.php?ID='.$ligne['PR_Number'];	
								echo '&nbsp<a href="'.$link.'" style="text-decoration:none;font-size:15px; vertical-align:top;font-weight:bold; color:navy;vertical-align:top; text-align:center"  target="_blank" title="go to release package detail">&#9658</a>';
							}
					
					echo '</div></td>';
					
					
					/*echo '<td><div id="Body">Project: </div></td><td><div id="InpBox"><input type="text" size=8 name="Project" style="font-size:8pt;height:8pt;" value="'.$ligne['Project'].'"></div></td>';	*/
					
					
					
					echo '<td><div id="Body">Project : </div></td><td><div id="InpBox"><select name="Project" type="submit" style="font-size:9pt;font-family:Tahoma, sans-serif;width:80px">';
					echo '<option value=""></option>';
					
						include('../SCM_Connexion_DB.php');
						$requete = "SELECT DISTINCT OTP, Title FROM tbl_project ORDER BY OTP DESC;";
						$resultat = $mysqli_scm->query($requete);
						$in='0';
						while ($row = $resultat->fetch_assoc())
						{
							if ($row['OTP']==$ligne['Project'])
							{
								echo'<option value ="'.$row['OTP'].'" Selected>'.$ligne['Project'].'</option><br/>';
								$in='1';
							} else {
									echo'<option value ="'.$row['OTP'].'">'.$row['OTP'].'</option><br/>';
									}
						}
						if ($in=='0' && $ligne['Project']!="")
							{
								echo'<option value ="'.$ligne['Project'].'" Selected>'.$ligne['Project'].'</option><br/>';
							}	
					mysqli_close($mysqli_scm);
					echo '</div></td>';
					
					echo '<td colspan=2 rowspan=2><div style="font-weight:bold;text-indent:20px">Justification file from DMO Owner</div>';
					if  ($ligne['Status']=="Open")
					{
						echo '<input id="file" name="justiffichiers[]" style="font-size:10px;text-indent:20px" type="file" class="" multiple>';
					}
					include('Generic_Attachment_Folder.php'); 
					$path_attachment=$Path_Folder . $Attachment_Folder . substr($_GET['dmo'],-5)."\\";
					echo '<div id="InpBox">';
					include('Generic_Attachment_Justification.php');
					echo '</div>';
					echo '</td>';
					
					
					echo '</tr>';
					echo '<td  style="height:35px"><div id="Body">DMO Type : </div></td><td><div id="InpBox"><select name="DMO_Type" id="DMO_Type_ID" type="submit" style="font-size:9pt;font-family:Tahoma, sans-serif;width:100px" onchange="type_to_doc()">';
					echo '<option value=""></option>';
					
						$requete_8 = 'SELECT DISTINCT Type FROM tbl_type WHERE Division like "'.$ligne['Division'].'" OR Division like "ALL" ORDER BY Type DESC;';
						$resultat = $mysqli_dmo->query($requete_8);
						$in='0';
						while ($row = $resultat->fetch_assoc())
						{
							if ($row['Type']==$ligne['Type'])
							{
									echo'<option value ="'.$row['Type'].'" Selected>'.$ligne['Type'].'</option><br/>';
								$in='1';
							} else {
							echo '<option value ="'.$row['Type'].'">'.$row['Type'].'</option><br/>';
									}
						}
						if ($in=='0' && $ligne['Type']!="")
							{
								echo'<option value ="'.$ligne['Type'].'" Selected>'.$ligne['Type'].'</option><br/>';
							}
					echo '</div></td>';
					
					
					echo '<td><div id="Body">Document : </div></td><td><div id="InpBox"><select name="Document"  id="Document_ID" type="submit" style="font-size:9pt;font-family:Tahoma, sans-serif;width:110px">';
					echo '<option value=""></option>';
					
						$type_filter=$ligne['Type'];
						$requete_9 = 'SELECT DISTINCT * FROM tbl_document WHERE Type like "'.$type_filter.'"  ORDER BY Document ASC';
						$resultat = $mysqli_dmo->query($requete_9);
						$in='0';
						while ($row = $resultat->fetch_assoc())
						{
							if ($row['Document']==$ligne['Document'])
							{
								echo'<option value ="'.$row['Document'].'" title="'.$row['Description'].'" Selected>'.$ligne['Document'].'</option><br/>';
								$in='1';
							} else {
									echo'<option value ="'.$row['Document'].'" title="'.$row['Description'].'">'.$row['Document'].'</option><br/>';
									}
						}
						if ($in=='0' && $ligne['Document']!="")
							{
								echo'<option value ="'.$ligne['Document'].'"Selected>'.$ligne['Document'].'</option><br/>';
							}	

					echo '</div></td>';				
					echo '</tr>';
						 
					$nb_ligne=substr_count($ligne['Comment'], ':')+3;
					if ($nb_ligne>8) { $nb_ligne=8;}
					
					echo '<tr><td><div id="Body">Previous Comments:</b></td></div><td colspan=5><div id="InpBox"><textarea readonly rows="'.$nb_ligne.'" cols="90" style="font-size:9pt;" name="Comment">'.$ligne['Comment'].'</textarea></div></td></tr>';
					
					if ($ligne['Status']!="Closed")
						{
						echo '<tr><td><div id="Body">Comment (*):</b></div></td><td colspan=5><div id="InpBox"><textarea required id="added_comments" rows="4" cols="90" name="New_Comment" style="font-size:9pt;" placeholder="Indicate the reson for the update or the change"></textarea></div></td></tr>';
						}
				


				echo '<tr>';
				echo '<td><div id="Body">EX Assessment:</b></div></td><td colspan=3><div id="InpBox"><textarea rows="2" cols="50" name="EX_Assessment" style="font-size:9pt;" placeholder="If an Ex product is impacted, please indicate the assessment for Ex function">'.$ligne['EX_Assessment'].'</textarea></div></td>';
				echo '<td><div id="Body">Spent Time (h):</b></div></td><td><div id="InpBox"><input type="text" size=4 id="Spent_Time" name="Spent_Time" style="font-size:8pt;height:11px" Title="Time spent by engineering to review, decide and implement the change - in hours" placeholder="" onchange="time_int()" value="'.$ligne['Spent_Time'].'"></div></td>';
				
				$decision_value=$ligne['Decision'];
				$status_value=$ligne['Status'];
			}	


				$mysqli_dmo->close();
				?>
		<tr>

			
			<td colspan=6 style="text-align:right;padding-bottom:15px">
				
			<?php
				
				if (isset($_GET['modif']))
				{
					$accept_val="";
					$under_review_val="";
					$update_val="";
					$reject_val="";
					$close_val="";
					$intro_val=" Make sure to click one of the following button to accept, reject, update or finalize the DMO --  ";
					
										
						if ($decision_value=="CREATED")
						{
							//$update_val="HIDDEN";
							$close_val="HIDDEN";
							
						} else if (($decision_value=="ACCEPTED") && ($status_value=="Open"))
								{
									$under_review_val="HIDDEN";
									$accept_val="HIDDEN";
									
								} else if ((($decision_value=="ACCEPTED") && ($status_value=="Closed")) || ($decision_value=="REJECTED"))
										{
										$reject_val="HIDDEN";
										$accept_val="HIDDEN";
										$under_review_val="HIDDEN";
										$close_val="HIDDEN";
										$update_val="HIDDEN";
										$intro_val="This DMO is closed and cannot be modified anymore.";
										} else if ($decision_value=="UNDER REVIEW")
												{
													$under_review_val="HIDDEN";
													$close_val="HIDDEN";
													}
				?>
					<span><?php echo $intro_val ?></span>
					<span <?php echo $accept_val ?>><input style="color:white;width:50px" class="btn green" type="submit" name="Accept" onclick="return validateForm(2)" value="Accept" title="Accept the DMO - Turn the Decision to ACCEPTED"/> | </span>
					<span <?php echo $update_val ?>><input style="color:white;width:50px" type="submit" name="Update" class="btn blue" onclick="return updateForm()" value="Update" title="Update the DMO"/></span>
					<span <?php echo $reject_val ?>> | <input style="color:white;width:50px" class="btn orange" type="submit" name="Reject" onclick="return validateForm(0)" value="Reject" title="Reject the DMO - No possible turnback - Turn the Decision to Rejected and Status to CLOSED"/> | </span>
					<span <?php echo $under_review_val ?>><input type="submit" style="color:white;width:50px" class="btn grey2"  name="Review" onclick="return validateForm(2)" value="Review" title="Assessment being led - Turn the Decision to Under Review"/></span>
					<span <?php echo $close_val ?>><input type="submit" style="color:white;width:50px" class="btn blue2"  name="Close" onclick="return validateForm(1)" value="Finalize DMO" title="Turns the Status of the DMO to Closed - No possible turnback"/></span>
					<!--    <input type="submit" name="Delete" value="Delete" title="Delete the DMO from the database"/>      -->
				<?php } ?>
				
			</td>
		</tr>
		<?php 
			if (isset($_POST['DMO']))
			{
				echo '<tr>
					<td id="status_bar" style="border-radius:10px;background-color:#EBF5FB ;color:#1F618D;font-style:italic;font-weight:bold ;text-align:right;padding-right:30px" colspan=6>
						The '.$_POST['DMO'].' has been successfully '. $txt_update .'.
					</td>
				</tr>';
			}
			?>
	</table>
	</form>

</body> 

</html>
