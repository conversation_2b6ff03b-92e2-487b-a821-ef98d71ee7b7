body {
	color: black;
	font-size: 10pt;
	font-family: Tahoma, sans-serif;
	font-weight: normal;
	/* background-color:transparent;*/
	vertical-align:middle;
	height:calc(100vh - 20px);
	background-image: url(/Common_Resources/logo_scm_zoom_left_transparent.jpg);
	background-repeat: no-repeat;
	background-position: left bottom;
	background-color:transparent;
}

@font-face {
	font-family: "Conneqt";
	src: url("/Common_Resources/font_Conneqt.otf");
}


div#Top_Title
{
	font-size:20px;
	letter-spacing: -2px;
	font-variant:small-caps;	
	font-family: Conneqt, sans-serif;
	font-weight:bold;
	letter-spacing:2px;
	padding-bottom:7px;
	padding-top:5px;
	font-weight:900;
	color:#2471A3; 
/*	linear-gradient(132deg, rgba(93,109,126,1) 0%, rgba(93,109,126,1) 35%, rgba(93,109,126,0) 100%);*/
}
	
div#Body {
	text-indent:15px;
	font-weight:bold;
	margin-left:5px;
	margin-right:5px;
	text-align:left;
	vertical-align:top;
	}

div#InpBox {
	font-size:8pt;
	font-weight:normal;
	text-align:left;
	vertical-align:middle;
	margin-bottom:3px;
	margin-top:3px;
	margin-left:5px;
	}


table {
	border-collapse: collapse;
	background-color: transparent;	
	}
	
th {
	/*background: linear-gradient(90deg, rgba(2,0,36,0) 0%, rgba(84,84,110,0.5158438375350141) 69%, rgba(58,127,172,1) 100%);
	background: linear-gradient(132deg, rgba(93,109,126,1) 0%, rgba(93,109,126,1) 35%, rgba(93,109,126,0) 100%);*/
	font-family:calibri, sans-serif;
	text-align:left;
	text-indent:5px;
	font-weight:bold;

	}
	
td {
	text-align:left;
	vertical-align:middle;
	padding-top:5px;
	padding-bottom:3px;
	}

tr {
	height:20px;
	}
	
.inputfile {
	width: 0.1px;
	height:0.1px;
	opacity: 0;
	overflow: hidden;
	position: absolute;
	z-index: -1;
}

.inputfile + label {
	font-size: 7;
	color: white;
	background-color: white;
	display: inline-block;
	font-size:8pt;
	font-weight:bold;
	cursor: pointer;
}

div#justif_file {
	text-indent:20px;
	font-size:8pt;
	font-weight:bold;
	text-decoration:underline;
	color:blue;
	vertical-align:top;
	cursor: pointer;
	text-align:left;
	}