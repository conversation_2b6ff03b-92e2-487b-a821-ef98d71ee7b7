<!DOCTYPE html>

<html translate="no">
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" charset="utf-8" />

	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

	<link rel="stylesheet" type="text/css" href="DMO_Styles.css">

	<script>
	
		function clean_list(x , y)
		{
			// Suppression de tous les elements de la liste
			for (let j = 0; j < y; j++)
			{
				x.remove(x[0]);
			}
			// Ajout d'un element vide en tête de liste
			var option = document.createElement("option");
			option.value="";
			option.text="";
			option.title="";
			x.add(option);
		}
		
		function Product_Code_Update()
		{
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() 
			{
				const raw_result=this.responseText.trim();
				
				if (raw_result.length>0)
				{
					
					// Mise en forme des données receptionnées de la dbb
					const split_result=raw_result.split("|");
					
					// Suppression des valeurs deja presentes
					clean_list(document.getElementById("Product_Range_List"), document.getElementById("Product_Range_List").length);
					
					//Remplissage des nouvelles valeurs
					for (let i = 0; i < split_result.length; i++)
					{
						var option = document.createElement("option");
						option.value=split_result[i];
						option.text=split_result[i];
						option.title=split_result[i];
						document.getElementById("Product_Range_List").add(option);
					}
					document.getElementById("Product_Range_List").removeAttribute('DISABLED');
					
				} else {
					
					document.getElementById("Product_Range_List").disabled = true;
					clean_list(document.getElementById("Product_Range_List"), document.getElementById("Product_Range_List").length);
				}
			}

			if (document.getElementById("Division").value != "" && document.getElementById("Division").value != "%")
			{
				xhttp.open("GET", "DMO_Product_Code_Update.php?division=" + document.getElementById("Division").value);
				xhttp.send();	
			} else {
				document.getElementById("Product_Range_List").disabled = true;
				document.getElementById("Product_Range_List").value="";
				}
		}
		
		function Doc_Update()
		{
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() 
			{
				const raw_result=this.responseText.trim();
				
				if (raw_result.length>0)
					{
						const split_result=raw_result.split("|");
						clean_list(document.getElementById("Document"), document.getElementById("Document").length);
						
						split_result.unshift("__|");
						for (let i = 0; i < split_result.length; i++)
						{
							var option = document.createElement("option");
							const el=split_result[i].split("__");
							if (option.value=el[0]!="Other")
							{
								option.value=el[0];
								option.text=el[0];
								option.title=el[1];
								document.getElementById("Document").add(option);
							}
						}
						//AJOUT D'UNE VALEUR AUTRE PERMETANT A L'UTILISATEUR DE SELECTIONNER UN DOC/SUJET QUI N'EST PAS LISTE
						var option = document.createElement("option");
						option.value="Other";
						option.text="Other";
						option.title="Other documents not listed above";
						document.getElementById("Document").add(option);
						
						document.getElementById("Document").removeAttribute('DISABLED');
						
					} else {
						
						document.getElementById("Document").disabled = true;
						clean_list(document.getElementById("Document"), document.getElementById("Document").length);
					}
			}
			

			var option_list=document.getElementsByName("dmo_type");
			if(option_list[0].checked==true && option_list[1].checked==false && option_list[2].checked==false)
			{
				var cond_eng="Engineering";
				var cond_method="";
			} else if (option_list[0].checked==false && option_list[1].checked==true && option_list[2].checked==false) 
				{
					var cond_eng="";
					var cond_method="Method Assy.";
				} else if (option_list[0].checked==false && option_list[1].checked==false && option_list[2].checked==true)
					{
						var cond_eng="";
						var cond_method="Method Lab.";	
					} else {
						var cond_eng="";
						var cond_method="";
					}
					

			if (cond_eng!=""|| cond_method!="")
			{
				xhttp.open('GET', 'DMO_Document_Update.php?eng=' + cond_eng + '&method=' + cond_method);
				xhttp.send();	
			} else {
				document.getElementById("Document").disabled = true;
				document.getElementById("Document").value="";
			}
		}
		
		
	</script>
	
	<title></title>
	
	</head>
<body>

		
<form method="post" action="" enctype="multipart/form-data">
	<table id="tb_new_req" border=0>
		<tr>
			<td colspan=2>
				<div id="Section_Title">
					Fill in the following fields:
				</div>
			</td>
			<td hidden>
				<div id="curr_usr">
					Current User:
					<?php
						include('../DMO_Connexion_DB.php');
						$username = trim(getenv('USERNAME') ?: getenv('USER'));
						$username=trim($username);
						$query_user_dpt = "SELECT Fullname FROM tbl_user WHERE ID_PC='".$username."';";
						$result_user_dpt = $mysqli_dmo->query($query_user_dpt);
						while ($row = $result_user_dpt->fetch_assoc())
						{
							if (isset($row['Fullname']))
							{
							echo''.$row['Fullname'].' / '.$username;
							} else 
								{
									echo 'NA';
								}
						}
						mysqli_close($mysqli_dmo);
					?>
				</div>
			</td>
		</tr>
		<tr>
			<td style="width:400px;">
				<div id="FilterTitle">
					Requestor Name (*):
				</div>
			</td>
			<td>
				<div class="dropdown_checkbox_NR">
					<select  tabindex="1" name="Requestor_Name" type="submit" title="" REQUIRED>;
					<option value=""></option><br/>
					<?php
						include('../DMO_Connexion_DB.php');
						$requete = "SELECT DISTINCT Fullname FROM tbl_user ORDER BY Fullname ASC;";
						$resultat = $mysqli_dmo->query($requete);
						while ($row = $resultat->fetch_assoc())
						{
							echo'<option value ="'.$row['Fullname'].'" >'.$row['Fullname'].'</option><br/>'; 
						}
						mysqli_close($mysqli_dmo);
					?>
					</select>
					<div class="dropdown_checkbox_NR-content" style="text-align:center">
						<font style="font-weight:bold">Name of the person rising the request</font>
					</div>
				</div>
			</td>
		</tr>
		<tr>
			<td>
				<div id="FilterTitle">
					Requestor Department (*):
				</div>				
			</td>
			<td>
				<div class="dropdown_checkbox_NR">
					<select  tabindex="2" name="Requestor_Dpt" type="submit" style="width:120px" title="" REQUIRED> 
					<option value=""></option><br/>;
					<?php
						include('../DMO_Connexion_DB.php');
						$requete = "SELECT DISTINCT Department FROM tbl_department ORDER BY Department ASC;";
						$resultat = $mysqli_dmo->query($requete);
						while ($row = $resultat->fetch_assoc())
						{
							echo'<option value ="'.$row['Department'].'">'.$row['Department'].'</option><br/>'; 
						}
						mysqli_close($mysqli_dmo);
					?>
					</select>
					<div class="dropdown_checkbox_NR-content" style="text-align:center;">
							<font style="font-weight:bold">Department the requestor belongs to or the department bringing the request up.</font>
					</div>
				</div>
			</td>
		</tr>
		
		<tr>
			<td>
				<div id="FilterTitle">
					Modification Type (*):
				</div>				
			</td>
			<td>
				<div class="dropdown_checkbox_NR">
					<input type="radio" id="eng_type" name="dmo_type" onchange="Doc_Update()" value="Engineering"><label for="eng_type">Engineering</label>
					<div class="dropdown_checkbox_NR-content" style="text-align:left">
						<font style="font-weight:bold">Engineering modification examples: </font><br>
						- Product Drawings (part, assembly, FT, GA, PC, ID ...) <br> 
						- NU (Installation Procedure)<br> 
						- SOP (Scope of Prototype)<br>
						- SOD (Scope of Delivery)<br> 
						- ATEX Documentation <br> 
						- etc... <br><br>
						<font style="font-weight:bold">Will be held by <font style="text-decoration:underline">Engineering</font> department</font>
					</div>
				</div>
				<div class="dropdown_checkbox_NR">
					&nbsp&nbsp&nbsp&nbsp&nbsp
					<input type="radio" id="assy_method_type" name="dmo_type" onchange="Doc_Update()" value="Method Assy."><label for="assy_method_type">Assembly Method</label>
						<div class="dropdown_checkbox_NR-content" style="text-align:left">
							<font style="font-weight:bold">Assembly Method modification examples: </font><br>
							- Tools, specific packaging Drawings<br> 
							- FI (Assembly Instructions)<br> 
							- PHI (Packaging & Handling Instructions)<br>
							- WG, Tool kit, Spare Kit<br>
							- IRS, OSIR, COSIR<br>
							- etc... <br><br>
							<font style="font-weight:bold">Will be held by <font style="text-decoration:underline">Assembly Method</font> department</font>
						</div>
				</div>
				<div class="dropdown_checkbox_NR">
					&nbsp&nbsp&nbsp&nbsp&nbsp
					<input type="radio" id="lab_method_type" name="dmo_type" onchange="Doc_Update()" value="Method Lab."><label for="lab_method_type">Laboratory Method</label>
						<div class="dropdown_checkbox_NR-content" style="text-align:left">
							<font style="font-weight:bold">Method modification examples: </font><br>
							- Tools, Test setup<br> 
							- QPP, QPR (Qualification Program, Report)<br>
							- FATP, FATR (FAT Procedure, Report)<br>
							- Security Checklist, FUM<br>
							- etc... <br><br>
							<font style="font-weight:bold">Will be held by <font style="text-decoration:underline">Laboratory Method</font> department</font>
						</div>
				</div>
				<!--<div class="dropdown_checkbox_NR">
					&nbsp&nbsp&nbsp&nbsp&nbsp
					<input type="radio" id="osi_method_type" name="dmo_type" onchange="Doc_Update()" value="Method OSI"><label for="osi_method_type">OSI Method</label>
						<div class="dropdown_checkbox_NR-content" style="text-align:left">
							<font style="font-weight:bold">Method modification examples: </font><br>
							- OSIR, COSIR<br> 
							- NU<br>
							- IRS<br>
							<font style="font-weight:bold">Will be held by <font style="text-decoration:underline">Laboratory Method</font> department</font>
						</div>
				</div>-->
			</td>
		</tr>
		
		<tr>
			<td>
				<div id="FilterTitle">
					Document Type (*):
				</div> 
			</td>
			<td>
				<div class="dropdown_checkbox_NR">
					<select DISABLED tabindex="3" name="Document_name" id="Document" type="submit" style="width:120px" title="" onchange="" REQUIRED> 
					<option value=""></option>
					<?php
						include('../DMO_Connexion_DB.php');
						$requete = "SELECT DISTINCT * FROM tbl_document ORDER BY Document ASC;";
						// print_r($requete);
						$resultat = $mysqli_dmo->query($requete);
						while ($row = $resultat->fetch_assoc())
						{
							echo'<option value ="'.$row['Document'].'" title="'.$row['Description'].'">'.$row['Document'].'</option><br/>'; 
						}
						mysqli_close($mysqli_dmo);
					?>
					</select>
					<div class="dropdown_checkbox_NR">
						&nbsp&nbsp&nbsp&nbsp&nbsp
						<div class="dropdown_checkbox_NR-content" style="text-align:left">
							<font style="font-weight:bold">Type of document impacted by the modification.</font>
						</div>
					</div>
				</div>
			</td>
		</tr>
		
		<tr>
			<td>
				<div id="FilterTitle">
					Division (*):
				</div> 
			</td>
			<td>
				<div class="dropdown_checkbox_NR">
					<select tabindex="3" name="Division_Name" id="Division" type="submit" style="width:120px" title="" onchange="Product_Code_Update()" REQUIRED> 
					<option value=""></option>
					<?php
						include('../SCM_Connexion_DB.php');
						$requete = "SELECT DISTINCT * FROM tbl_division ORDER BY Division ASC;";
						$resultat = $mysqli_scm->query($requete);
						while ($row = $resultat->fetch_assoc())
						{
							echo'<option value ="'.$row['Division'].'" title="'.$row['Description'].'">'.$row['Division'].'</option><br/>'; 
						}
						mysqli_close($mysqli_scm);
					?>
					</select>
					<div class="dropdown_checkbox_NR-content" style="text-align:center;width:360px">
						<font style="font-weight:bold">Division the document or the product to modify belongs to</font>
					</div>
				</div>
			</td>
		</tr>
		
		<tr>
			<td>
				<div id="FilterTitle">
					Product Range :
				</div> 
			</td>
			<td>
				<div class="dropdown_checkbox_NR">
					<select name="Product_Range" id="Product_Range_List" type="submit" title="" DISABLED style="font-size:11;height:17;width:250px">
						<option value=""></option>
					</select>
					<div class="dropdown_checkbox_NR-content" style="text-align:center">
						<font style="font-weight:bold">Product range the modification impacts</font><br>
					</div>
				</div>
			</td>
		</tr>
		
		<tr>	
			<td>
				<div id="FilterTitle">
					Project (*):
				</div> 
			</td>
			<td>
				<div class="dropdown_checkbox_NR">
					<select tabindex="4" name="Project" type="submit" style="width:330px" title="" REQUIRED> 
					<option value=""></option>
					<?php
						include('../SCM_Connexion_DB.php');
						$requete = "SELECT DISTINCT OTP, Title FROM tbl_project ORDER BY OTP DESC;";
						$resultat = $mysqli_scm->query($requete);
						while ($row = $resultat->fetch_assoc())
						{
							echo'<option value ="'.$row['OTP'].'">'.$row['OTP'].' - '.$row['Title'].'</option><br/>'; 
						}
						mysqli_close($mysqli_scm);
					?>
					</select>
					<div class="dropdown_checkbox_NR-content" style="text-align:left;width:420px">
						<font style="font-weight:bold">- If the impacted document is project specific, indicate the projet code.</font><br>
						<font style="font-weight:bold">- If this is not project specific, pick "STAND" for standard product</font><br>
					</div>
				</div>
			</td>
		</tr>
		
		
		
		<tr>
			<td>
				<div id="FilterTitle">
					Attachment (10Mb total max):
				</div> 
			</td>
			<td style="text-indent:5px">
				<div class="dropdown_checkbox_NR">
					<input tabindex="6" type="file" name="mesfichiers[]" multiple><br />
					<div class="dropdown_checkbox_NR-content" style="text-align:left;width:330px">
						<font style="font-weight:bold;padding-bottom:2px">Files can be attached to support the request: </font><br>
						&nbsp- Annoted documents (scanned hard copies or electronic ones)<br> 
						&nbsp- Email chain<br> 
						&nbsp- Test results<br> 
						&nbsp- FNC (non-conformity document)<br> 
						&nbsp- Dimensional inspection report, DPA<br> 
						&nbsp- RCA, Quality document
						&nbsp- etc...<br><br>
						Multiple files can be uploaded -- <font style="font-weight:bold">WARNING: 10Mo max</font>
					</div>
				</div>
			</td>
		</tr>
		
		
		<tr>
			<td style="vertical-align:top;padding-top:5px">
				<div id="FilterTitle">
					Description (*): 
				</div>
			</td>
			<td style="padding-top:7px">
				<textarea rows="6" cols="80" name="Description_dmo" style="font-size: 10pt;font-family: Tahoma, sans-serif;font-weight: normal" title="Provide the details of the change you want to be applied - The more info, the better it is to exectue the change" REQUIRED/></textarea><br/>
			</td>
		</tr>
		<tr>
			<td>
				<div id="FilterTitle">
					Please make sure to fill the proper fields to create a new DMO
				</div>
			</td>
			<td style="">
				<input type="submit" name="validate" style="margin-left:5px;width:80px;height:25px;border-radius:10px;" class="btn blue2" value="Create" title="Create the new request based on the info you provided."/>
			</td>
		</tr>
	

</form>

<?php
	
if (isset ($_POST['validate']))
		{
			
			include('../DMO_Connexion_DB.php');
			
			$sql_numbering = 'SELECT MAX(DMO) from tbl_dmo';
			$dmo_num_raw = $mysqli_dmo->query($sql_numbering);
			$dmo_num = mysqli_fetch_row($dmo_num_raw);
			$dmo_num_new = substr(strval($dmo_num[0]),-5);
			$dmo_curr_year = substr(strval($dmo_num[0]),3,2);
			if (date("y")>$dmo_curr_year)
				{
				 $dmo_num_new =date("y").'001';
				} else {
						$dmo_num_new = intval($dmo_num_new) + 1;
						}			
			
			$DMO="DMO".$dmo_num_new;
			$Issue_Date = date("y-m-d");
			$Description=htmlspecialchars($_POST['Description_dmo'], ENT_QUOTES);
			$Product_Range=$_POST['Product_Range'];
			$Project=$_POST['Project'];
			$Requestor_Name=$_POST['Requestor_Name'];
            $Requestor_Dpt=$_POST['Requestor_Dpt'];
			$Decision="CREATED";
			$Status="OPEN";
			$Last_Update_Date="0000-00-00";
			$End_Date="0000-00-00";
			$Ex="";
			$EX_Assessment="";
			$Spent_Time=0;
			
			$Division=$_POST['Division_Name'];
			$dmo_type=$_POST['dmo_type'];
			$document=$_POST['Document_name'];
			
			$sql_1 = 'INSERT INTO tbl_dmo VALUES (
						 "0",
						"'.$DMO.'",
						"'.$Issue_Date.'",
						"'.$Description.'",
						"'.$Product_Range.'",
						"'.$Division.'",
						"'.$Project.'",
						"'.$Requestor_Name.'",
						"'.$Requestor_Dpt.'",
						"'.$Decision.'",
						"'.$Status.'",
						"'.$Ex.'",
						"",
						"",
						"'.$End_Date.'",
						"",
						"",
						"",
						"'.$Last_Update_Date.'",
						"'.$EX_Assessment.'",
						"'.$Spent_Time.'",
						"'.$document.'",
						"'.$dmo_type.'")';
			
			$resultat = $mysqli_dmo->query($sql_1);
            mysqli_close($mysqli_dmo);
			
			
			// echo ' New DMO created under number: DMO'.$dmo_num_new.'</br>';
			echo '<tr><td></td><td style="border-radius:10px;color:#0069b4;font-style:italic;font-weight:bold;text-align:right;padding-right:30px">'.
				 'New DMO created under number: DMO'.$dmo_num_new.'</td></tr>';
			
			// Traitement des pieces jointes
			if (isset($_FILES['mesfichiers']))
				{
					
					$nbLignes=count($_FILES['mesfichiers']['name']);
					include('Generic_Attachment_Folder.php'); 
					$path_attachment=$Path_Folder . $Attachment_Folder; 
					mysqli_close($mysqli_dmo); 
					$nom = $path_attachment . $dmo_num_new; // Le nom du répertoire à créer				
					// Vérifie si le répertoire existem creation si non existant
					if (is_dir($nom))
						{
						} else { 
							  mkdir($nom,4);     
						  }
					$file_loading_msg='';
					for($i=0 ; $i<=$nbLignes-1 ; $i++)
						{
						//// test de la taille du fichier
						if ($_FILES['mesfichiers']['size'][$i] <= 10000000)
							{
								//// Testons si l'extension est autorisée
								//// $infosfichier = pathinfo($_FILES['mesfichiers']['name'][$i]);
								//// $extension_upload = $infosfichier['extension'];
								//// $extensions_autorisees = array('jpg', 'jpeg', 'gif', 'png', 'pdf', 'php');
								//// if (in_array($extension_upload, $extensions_autorisees))
								//// {		
							
								////On peut valider le fichier et le stocker définitivement
								$tmp_file = $_FILES['mesfichiers']['tmp_name'][$i];	
								move_uploaded_file($_FILES['mesfichiers']['tmp_name'][$i], $nom.'/' . basename($_FILES['mesfichiers']['name'][$i]));
										
								////}
							} else {
									$file_loading_msg=$file_loading_msg. 'Le fichier ' . $_FILES['mesfichiers']['name'][$i] . 'est trop gros. Il n\'a pas été téléchargé.<br>';
								}
						}
						echo $file_loading_msg;
				} else {
					echo '';
				}
				
        } else {
			
			
			
			}
	
        
			
		
		
	?>	

</table>
				
		


	</body>
	
	
	
</table>
</html>