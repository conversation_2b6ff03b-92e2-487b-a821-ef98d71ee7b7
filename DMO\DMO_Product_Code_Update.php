<?php 

	if (isset($_GET['division']))
	{
		
		$requete = 'SELECT DISTINCT Product_Range 
					FROM tbl_product_range
					WHERE Division like "'.$_GET['division'].'"
					ORDER BY Product_Range ASC';
	
		include('../DMO_Connexion_DB.php');
		$resultat = $mysqli_dmo->query($requete);
		$rowcount=mysqli_num_rows($resultat);

		$output_value="";
		
		if ($rowcount>0)
		{
			while ($row = $resultat->fetch_assoc())
			{
				if ($output_value!="")
				{
					$output_value=$output_value."|".$row['Product_Range'];
				} else {
					$output_value=$row['Product_Range'];
				}
			}
		}
			
		echo $output_value;

		$mysqli_dmo->close();
	}
