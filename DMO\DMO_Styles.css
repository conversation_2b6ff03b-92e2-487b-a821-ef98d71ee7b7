body {
  color: black;
  font-size: 9pt;
  font-family: Tahoma, sans-serif;
  font-weight: normal;
  /*background-color:transparent;*/
}

@font-face {
	font-family: "Conneqt";
	src: url("/Common_Resources/font_Conneqt.otf");
}


div#FilterTitle {
  text-indent: 5px;
  margin-top: 10px;
  margin-bottom: 2px;
  text-align: justify;
}

div#News_Title {
  color: #1a5276;
  font-family: Trattatello, fantasy;
  margin-top: 15px;
  text-align: center;
  font-size: 18pt;
  font-weight: normal;
}

div#Section_Title {
	font-weight:bold;
	vertical-align:top;
	font-size: 10pt;
	}

div#sidepanel_left{
   width:11.5%;
   max-width:180px;
   transition: all 0.4s;
   display:block;
   border-right:0.25px solid #E4E4E4;
   z-index:98;
   /*box-shadow: 0 4px 4px 4px rgba(0, 0, 0, 0.4), 0 6px 20px 10px rgba(0, 0, 0, 0.19);*/
}

.input_welcome{
   font-size:12px;
   height:20px;
   margin-left:10%;
   width:45%;
   text-align:center;
   box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2), 0 6px 6px 0 rgba(0, 0, 0, 0.19);
  border-radius:5px;
}

.select_welcome{
  text-align:center;
   width:45%;
   font-size:11px;
   height:20px;
   box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius:5px;
}



.grey {
    background-color: #cccbcb; 
    color: black;
}

.grey:hover {
   background: rgb(151, 149, 149);
}

.blue {
   background-color: #2196F3;
} 
.blue:hover {
background: #0b7dda;
}



div#bottom_icon{
  bottom:0%;
  z-index:99;
  position:absolute;
  margin-bottom:70px;
  left:10px;
}

.title_expanded {
  margin-left: 28px;
}

.title_skrinked {
  margin-left: 0px;
}

span#version {
  position: absolute;
  bottom: 0;
  font-size: 80pt;
  color: grey;
  left: 0;
}

div#detail_page_title {
  font-weight: 550;
  font-variant: small-caps;
  font-size: 10pt;
  margin-top: 5px;
  margin-bottom: 2px;
}

/* FRAME PRINCIPALE DE LA PAGE - PN_WELCOME */
/* ---------------------------------------- */
.main_frame_reduced {
  position: absolute;
  z-index: 1;
  top: 58px;
  left: calc(11% + 20px);
  width: calc(100% - 12%);
  height: calc(100% - 58px - 4px);
  margin-left: -15px;
  background-color: transparent;
  transition: all 0.5s;
}

.main_frame_expanded {
  position: absolute;
  z-index: 1;
  top: 58px;
  left: 20px;
  width: calc(100% - 10px);
  height: calc(100% - 58px - 4px);
  margin-left: -15px;
  background-color: transparent;
  transition: all 0.5s;
}



/* BOUTON AVEC IMAGE */
/* ----------------- */
input[type="image"]:hover {
  border-bottom: 1px solid #bbbbbb;
  border-right: 1px solid #bbbbbb;
  margin-top: -1px;
  transform: scale(1.15);

  /*box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);*/
}


input[type="image"]:active {
  border-bottom: 1px solid #bbbbbb;
  border-right: 1px solid #bbbbbb;
  border-left: 1px solid #bbbbbb;
  border-top: 1px solid #bbbbbb;
  margin-top: -1px;
  transform: scale(0.95);
}

.chckbox{
	margin-left:20px;
}

.chckbox_label {
	font-size: 10pt;
  font-family: Tahoma, sans-serif;
  font-weight: normal;
}



/*TABLEAU RESULTAT RECHERCHE - XXXXXXX */
/*--------------------------------------------- */
#t01 {
  width: calc(100% - 10px);
  border-collapse: collapse;
  vertical-align: middle;
}

#t01 th {
  border: 1px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
}

#t01 td {
  text-align: left;
  vertical-align: middle;
}

#t01 tr {
  height: 20px;
}


/* --------------------------------------------------------- */
#t02 {
  margin-left: -6px;
  margin-top: -7px;
  border-collapse: collapse;
  vertical-align: top;
  width: calc(100vw - 3px);
  font-size: 8.5pt;
  text-align: left;
}

#t02 td {
  vertical-align: top;
}


/* --------------------------------------------- */
#t03 {
  border-collapse: collapse;
  width: calc(100%);
  font-size: 8.5pt;
  text-align: left;
}

#t03 th {
  background-color: rgba(244, 244, 244, 0.45);
  font-weight: 520;
}

#t03 td {
  vertical-align: middle;
}

#t03 tr {
  height: 20px;
}


/*------------------------------------------------- */
#t04 {
  border-collapse: collapse;
  width: calc(100vw - 2px);
  overflow-y: auto;
  margin-top: -7px;
  margin-left: -7px;
  font-size: 8.5pt;
}

#t04 th {
  border: 0.5px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
  font-family: Arial Helvetica, sans-serif;
  text-align: center;
}

#t04 td {
  text-align: center;
  vertical-align: middle;
  border-right: 0.5px solid black;
  border-left: 0.5px solid black;
  border-top: 0.5px solid black;
  border-bottom: 0.5px solid black;
}

#t04 tr {
  height: 20px;
}

#t04 tr:hover {
  background-color: rgb(76, 126, 160);
  color: white;
  cursor: pointer;
}


/*------------------------------------------------------ */
#t05 {
  border-collapse: collapse;
  width: calc(100% - 10px);
  font-size: 8.5pt;
  margin-top: 8px;
}

#t05 th {
  border-bottom: 0.5px solid black;
  background-color: transparent;
  font-family: Arial Helvetica, sans-serif;
  font-size: 8pt;
  text-align: center;
}

#t05 td {
  text-align: center;
  vertical-align: middle;
}

#t05 tr {
  height: 15px;
}


/*TABLEAU RESULTAT RECHERCHE - XXXXXXX */
/*--------------------------------------------- */
#t06 {
  width: calc(100% - 40px);
  border-collapse: collapse;
  vertical-align: middle;
  font-size:12px;
  margin-left:15px;
}

#t06 th {
  border: 0px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
  height:20px;
  border:none;
}

#t06 td {
  text-align: center;
  vertical-align: middle;
}

#t06 tr {
  height: 35px;
  border-bottom:1px solid gray;
}


/*TABLEAU KPI - DMO_KPI */
/*--------------------- */
#t07 {
  width: calc(100vw - 60px);
  border-collapse: collapse;
  vertical-align: middle;
  font-size:12px;
  margin-left:15px;
}

#t07 td {
  text-align: center;
  vertical-align: middle;
}

#t07 tr {
  height: 430px;
  border-bottom:1px solid black;
}

/*TABLEAU KPI TABLE VALEUR - DMO_KPI */
/*-------------------------------------- */
#t08 {
  border-collapse: collapse;
  vertical-align: middle;
  font-size:12px;
  margin-left:15px;
}

#t08 th {
  font-weight:bold;
  text-align:center;
}

#t08 td {
  text-align: center;
  vertical-align: middle;
  width:100px;
}

#t08 tr {
  height: 16px;
  border-bottom:1px solid black;
}

/*TABLEAU WAITING / LEADTIME WELCOME - DMO_WELCOME_FRAME.PHP*/
/*--------------------------------------------------------- */
#t09 {
  margin-top:-10px;
  margin-bottom:15px;
  vertical-align: middle;
  font-size:13	px;
  border-spacing:0px 5px;
  width:80%;
}


#t09 th {
  font-weight:bold;
  text-align:center;
  background-color:#A1A1A1;
  color:white;
  text-shadow: 0 0 3px #A1A1A1;
}

#t09 td {
  text-align: center;
  vertical-align: middle;
  width:100px;

}

#t09 tr {
  height: 33px;  
}

/*TABLEAU DETAIL DMO STATUT/DECISION - DMO_KPI.PHP*/
/*------------------------------------------------ */
#t10 {
	border-collapse:collapse;
  vertical-align: middle;
  font-size:14px;

}

#t10 th {
  font-weight:bold;
  text-align:center;
  background-color:#A1A1A1;
  color:white;
  text-shadow: 0 0 3px #A1A1A1;
}

#t10 td {
  text-align: center;
  vertical-align: middle;
  width:120px;

}

#t10 tr {
  height: 16px;  
}


/*TABLEAU DETAIL ADMI FORM - DMO_ADMIN_FORM.PHP*/
/*-------------------------------------------- */
#t10 {
	border-collapse:collapse;
	vertical-align: middle;
	font-size:12px;
}

#t10 th {
  font-weight:bold;
  text-align:left;
  margin-top:10px;

}

#t10 td {
  text-align: left;
  vertical-align: middle;

}

#t10 tr {
  height: 16px;  
}


/*TABLEAU TITRE DES MENU DE LA PAGE WELCOME - DMO_WELCOME */
/*------------------------------------------------------- */
#t_menu {
  border-collapse: collapse;
  width: 100%;
  height:100%;
  font-size:10px;
  margin-top:5px;
  margin-bottom:5px;
  font-family: Conneqt, sans-serif;
}

#t_menu th {
  width:25px;
  padding-right:5px;
}

#t_menu td {
  text-align: left;
  vertical-align: middle;
}

#t_menu:hover {
  /*font-weight:bold;
  color:white;*/
}

#t_menu tr {
  
}


/*DROPDOWN */
/*---------*/

.dropdown_checkbox_NR {
  position: relative;
  display: inline;
  float:none;

}

.dropdown_checkbox_NR-content {
  display: none;
  min-width:280px;
  position: absolute;
  background-color: #212F3D;
  color:white;
  font-weight:normal;
  left:80px;
  top:25px;
  border-radius:5px;
  padding-left:6px;
  padding-right:6px;
  padding-top:3px;
  padding-bottom:6px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  text-align:center;
  font-size:11.5px;
  transition-delay: 1s;
  transition-property: display;
  
}

.dropdown_checkbox_NR:hover .dropdown_checkbox_NR-content {
  display: inline;
  z-index:99;
}


.dropdown_checkbox_KPI {
  position: relative;
  display: inline;
  float:none;
}

.dropdown_checkbox_KPI-content {
  display: none;
  min-width:280px;
  position: absolute;
  background-color: #212F3D;
  color:white;
  font-weight:normal;
  right:0px;
  top:25px;
  border-radius:5px;
  padding-left:6px;
  padding-right:6px;
  padding-top:3px;
  padding-bottom:6px;
  box-shadow: 0px 8px 16px 0px rgba(0, 0, 0, 0.2);
  text-align:center;
  font-size:11.5px;
  transition-delay: 1s;
  transition-property: display;
  
}

.dropdown_checkbox_KPI:hover .dropdown_checkbox_KPI-content {
  display: inline;
  z-index:99;
}
/*---------------------*/



/* CALL OUT BOX*/
/*-------------*/
.callout {
  position: fixed;
  z-index:99;
  bottom: 35px;
  width: 70vw;
  margin-left: calc((100vw - 70vw) / 2);
  top:calc((100vh - 450px - 100px) / 2);
  display:none;
  font-family:Conneqt, sans-serif;
}

.callout-header {
  padding: 15px;
  text-indent:30px;
  border-radius: 20px 20px 0px 0px;
  background-color: #0089a8;
  font-size: 20px;
  color: white;
  text-align:center;
  border-bottom:3px solid white;
  border-top:1px solid white;
  border-left:1px solid white;
  border-right:1px solid white;
  font-weight:bold;
  box-shadow: rgba(0, 0, 0, 0.56) 0px 22px 70px 4px;
}

.callout-container {
  padding: 15px;
  height:350px;
  font-size:13px;
  background-color: #F4F4F4;
  color: black;
  opacity:0.98;
  border-radius: 0px 0px 20px 20px;
  border-bottom:1px solid white;
  border-left:1px solid white;
  border-right:1px solid white;
  box-shadow: rgba(0, 0, 0, 0.56) 0px 22px 70px 4px;
  background-image:url("/Common_Resources/logo_scm_tron_Transparent.png");
  background-repeat: no-repeat;
  background-position: right;
}

.closebtn {
  position: absolute;
  top: 8px;
  right: 15px;
  color: white;
  font-size: 30px;
  cursor: pointer;
}

.closebtn:hover {
  color: lightgrey;
}