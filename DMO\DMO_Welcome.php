<!DOCTYPE html>

<?php
	require('login.php');
	login(explode("\\", $_SERVER['REMOTE_USER']));
	$logged_user=explode("\\",$_SERVER['REMOTE_USER'])[1];
?>


<html>


<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="DMO_Styles.css">
<link rel="stylesheet" type="text/css" href="DMO_vertical_tab.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="icon" type="image/png" href="\Common_Resources\logo_scm_DMO_2.png" />


<head>
	<script>
	
		// AFFICHAGE DE MESSAGE SI PLANIFIE
		// --------------------------------
		function init_loading()
		{
			if (document.getElementById("callout_id"))
			{
				document.getElementById("callout_id").style.display="block";
			}
		}
		
	
		// MISE EN GRAS DU MENU SELECTIONNE
		//---------------------------------
		function focus_in(el) {
			let i = 1;
			const list_button = document.getElementsByClassName("w3-bar-item w3-button");
			while (i <= list_button.length) {
				document.getElementById("button_" + i).style.fontWeight = 100;
				document.getElementById("button_" + i).style.backgroundColor ="transparent";
				i++;
			}
			el.style.fontWeight = 700;
			el.style.backgroundColor = "#CCD6DD";
		}
		
		
		// TEMPORISATION DE L'APPARITION DE LA BANNIERE DE RECHERCHER APRES 250ms
		// -----------------------------------------------------------------------
		function banner_toggle_regul()
		{
			if(document.getElementById("sidepanel_left").style.width=="0%") // SI DEJA REDUIT
			{
				setTimeout(banner_toggle_action, 250); // Temporisation pour faire apparaitre la banniere de recherche
			} else {
				banner_toggle_action();
			}
		}
		// -----------------------------------------------------------------------
		
		// EXTENSION / REDUCTION DE LA BANNIERE DE RECHERCHE 
		// -------------------------------------------------
		function banner_toggle_action()
		{
			if(document.getElementById("left_panel_lock_id").className=="left_panel_slider")
			{
				if(document.getElementById("sidepanel_left").style.width=="0%") // SI BANDEAU DE RECHERCHER DEJA REDUIT
				{
					document.getElementById("sidepanel_left").style.width="11%";
					document.getElementById("main_frame_id").setAttribute("class","main_frame_reduced");
					document.getElementById("banner_toggle_btn_id").style.display="none";
					
					document.getElementById("title_banner").setAttribute("class","title_skrinked");
					
				} else {														// SI BANDEAU DE RECHERCHER DEJA ETENDU
					document.getElementById("sidepanel_left").style.width="0%";
					document.getElementById("main_frame_id").setAttribute("class","main_frame_expanded");
					document.getElementById("banner_toggle_btn_id").style.display="block";
					
					document.getElementById("title_banner").setAttribute("class","title_expanded");
				}
			}
		}
		// -------------------------------------------------
		
		// VERROUILLAGE DE LA RETRACTATION DU PANNEAU DE RECHERCHE
		// -------------------------------------------------------
		function left_panel_toogle()
		{
			var left_banner_locked_result=left_banner_locked();

			 if(left_banner_locked_result==0)
			 {
				document.getElementById("left_panel_lock_id").setAttribute("class","left_panel_locked");
				document.getElementById("left_panel_lock_id").src='/Common_Resources/pin_blue.png';
				document.getElementById("left_panel_lock_id").title="Panneau Figé - Click = Activer la retraction auto";
			 } else {
				document.getElementById("left_panel_lock_id").setAttribute("class","left_panel_slider");
				document.getElementById("left_panel_lock_id").src='/Common_Resources/pin_grey.png';
				document.getElementById("left_panel_lock_id").title="Rétractation Auto Activée - Click = Figer le panneau de recherche";
				
				let src_frame=document.getElementById("main_frame_id").src;
				//alert(src_frame);
				//if (src_frame.indexOf("=")!=-1)
				//{
					banner_toggle_action();
				//}
			 }
		}
		// -------------------------------------------------------
		
		// VERIFICATION DU VERROUILLLAGE DU PANNEAU DE RECHERCHE
		// -----------------------------------------------------
		function left_banner_locked()
		{
			if (document.getElementById("left_panel_lock_id").className=="left_panel_slider")
			{
				return 0;
			} else {
				return 1;
			}
		}
		//------------------------------------------------------

		// VERIFICATION DU PASSWORD RENTRE POUR ALLER DANS LA PAGE ADMINISTRATION
        // ----------------------------------------------------------------------
        function Admin_PWD_Check()
        {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function()
            {
				var mdp=document.getElementById("pass_input").value;
				if (mdp!="")
                {
                    pwd= this.responseText.trim();
                    if (mdp == pwd)
                    {
                        const url="DMO_Admin_Form.php";
						document.getElementById("pass_input").value="";
						document.getElementById("pass_zone").hidden=true;
						document.getElementById("admin_pass_button").hidden=false;
						document.getElementById("main_frame_id").src=url;
						document.getElementById("pass_input").style.background="transparent";
						//window.open(url);
                    } else if (mdp!="" && mdp!=null)
                    {
						document.getElementById("pass_input").style.background="#F5B7B1";
                    } else {
                    }
                }
            }
			
            xhttp.open("GET", "DMO_Admin_PWD.php");
            xhttp.send();
        }
		
		function pass_display()
		{
			if (document.getElementById("pass_input")!="")
			{
				document.getElementById("pass_zone").hidden=false;
				document.getElementById("admin_pass_button").hidden=true;
				document.getElementById("pass_input").focus();
			}
		}
		
		function password_field_toggle()
		{
			if(document.getElementById("pass_zone").hidden==false)
			{
				document.getElementById("pass_zone").hidden=true;
				document.getElementById("admin_pass_button").hidden=false;
				document.getElementById("pass_zone").hidden=true;
			}
		}
		
		function quick_search()
		{
			var dmo_select=document.getElementById("Quick_Search_SLT").value;
			//var dmo_txt="DMO"+document.getElementById("Quick_Search_TXT").value;
			var url="DMO_Modification_form.php?dmo=" + dmo_select;
			//if (confirm("Open the " + dmo_select + "?") == true)
			if (dmo_select != "")
			{
				document.getElementById("main_frame_id").src=url;
				//window.open(url);
			}  else {
				document.getElementById("main_frame_id").src="DMO_Welcome_Frame.php";
			}
		}
		
		function quick_search_txt()
		{
			if(document.getElementById("Quick_Search_TXT").value!="")
			{
				document.getElementById("Quick_Search_SLT").value="";
			}
		}
		
		
		
		// ONLY IF TEXT INPUT OFFERED FOR QUICK SEARCH
		// function quick_search_select()
		// {
			// if(document.getElementById("Quick_Search_SLT").value!="")
			// {
				// document.getElementById("Quick_Search_TXT").value="";
			// }
		// }
		
		
	</script>
	
<?php 
include('../Common_Resources/Tool_Banner_Deco_Period.php');
?>


<?php 
// ------------------
//  CALLOUT DE NEWS 
// ------------------
$origin = date_create('2024-02-01'); // DATE A PARTIR DE LAQUELLE LE MESSAGE S'AFFICHERA
$duration_news = 5; 						 // DUREE EN JOURS CALENDAIRES PENDANT LAQUELLE LE MESSAGE APPARAITTRA A CHAQUE LANCEMENT DE LA PAGE, INCLUANT LE DERNIER JOUR
// --------------------
	?>
</head>

	<title>DMO</title>

	<body onload="init_loading()">
		
		<!-- SEASONAL BANNER -->
		<?php  
		if ($interval_noel->format('%R%a')>=0 && $interval_noel->format('%R%a')<=$duration_noel ) // AFFICHE BANNIERE NOEL PENDANT LES $DURATION JOURS AVANT LA $DATE_NOEL
		{
			echo '<div class="w3-container w3-blue-xmas"><h3><div style="font-variant:small-caps;z-index:2" class="title_skrinked" id="title_banner" ><font style="letter-spacing: 3px;font-family:Conneqt, sans-serif;"><b>DMO</b></font> - <b>D</b>emande de <b>MO</b>dification</div></h3></div>';
			echo '<img src="\Common_Resources\Top_Banner_Xmas.png"  style="height:46px; repeat:repeat-x;position: absolute;z-index:2;opacity: 0.6; top:0px; right:0px; filter:grayscale(20%)"/ >';
		} elseif ($interval_hiver->format('%R%a')>0 && $interval_hiver->format('%R%a')<=$duration_hiver ) // AFFICHE BANNIERE HIVER PENDANT LES $DURATION JOURS APRES LA $DATE_HIVER
		{
			echo '<div class="w3-container w3-blue-scm4"><h3><div style="font-variant:small-caps;z-index:2" class="title_skrinked" id="title_banner" ><font style="letter-spacing: 3px;font-family:Conneqt, sans-serif;"><b>DMO</b></font> - <b>D</b>emande de <b>MO</b>dification</div></h3></div>';
			echo '<img src="\Common_Resources\snow02.gif"  style="height:100%; repeat:repeat-x;position: absolute;z-index:2;opacity: 0.6; top:0px; right:0px; filter:grayscale(20%)"/ >';
		}
		elseif ($interval_24_auto->format('%R%a')>0 && $interval_24_auto->format('%R%a')<=$duration_24_auto ) // AFFICHE BANNIERE 24H PENDANT LES $DURATION JOURS APRES LA $DATE_24H
		{
			echo '<div class="w3-container w3-blue-scm4"><h3><div style="font-variant:small-caps;z-index:2" class="title_skrinked" id="title_banner" ><font style="letter-spacing: 3px;font-family:Conneqt, sans-serif;"><b>DMO</b></font> - <b>D</b>emande de <b>MO</b>dification</div></h3></div>';
			echo '<span style="font-size:8px;color:white;position:absolute;top:46px;right:744px">D-'. (intval(substr($interval_24_auto->format('%R%a'), 1)) + 1) .'</span>';
			echo '<img src="\Common_Resources\24H_car.gif"  style="width:560px;repeat:repeat-x;position: absolute;z-index:98;opacity: 0.7;top:-142px; right:170px; filter:grayscale(40%)"/ >';
			echo '<img src="\Common_Resources\24H_Michelin.gif"  style="height:62px;repeat:repeat-x;position: absolute;z-index:99;opacity: 0.7; top:-2px; right:695px; filter:grayscale(60%)"/ >';
			echo '<img src="\Common_Resources\Checkered_Flag.jpg"  style="repeat:repeat-x;position: absolute;z-index:99;opacity: 0.2; width:calc(11% + 5px);max-width:180px;bottom:0px; left:0px; filter:grayscale(60%)"/ >';
			
			//echo '<img src="\Common_Resources\Checkered_Flag.jpg"  style="width:calc(89% - 5px);repeat:repeat-x;position: absolute;z-index:99;opacity: 0.2; bottom:0px; right:0; filter:grayscale(60%)"/ >';
			//echo '<img src="\Common_Resources\24H_Michelin.gif"  style=" height:160px;repeat:repeat-x;position: absolute;z-index:1;opacity: 0.6; top:-10px; right:610px;transform: rotate(10deg);filter:grayscale(20%)"/ >';
			//echo '<img src="\Common_Resources\24h_Track.gif"  style=" height:52px;repeat:repeat-x;position: absolute;z-index:1;opacity: 0.6; top:2px; right:650px;filter:grayscale(20%)"/ >';
			//echo '<img src="\Common_Resources\24H_Michelin.gif"  style=" height:250px;repeat:repeat-x;position: absolute;z-index:99;opacity: 0.6; bottom:-20px; left:-30px;filter:grayscale(20%)"/ >';
			echo '<img src="\Common_Resources\24h_Track.gif"  style=" width:31px;repeat:repeat-x;position: absolute;z-index:97;opacity: 0.7; top:0px; right:160px;transform: rotate(12deg);filter:grayscale(90%)"/ >';
		} else {
			echo '<div class="w3-container w3-blue-scm4">
					<h3 style="z-index:99">
						<div style="font-family:Conneqt, sans-serif;font-variant:small-caps;font-size:24px" class="title_skrinked" id="title_banner" >
							<font style="letter-spacing: 3px;"><b>DMO</b></font> - <b>D</b>emande de <b>MO</b>dification
						</div>
					</h3>
				  </div>';
		}
		?>
		<!------------------>
		
		
		
		
		<!-- CALLOUT NEWS -->
		<?php
		$target = date_create(date('Y-m-j'));
		$interval = date_diff($origin, $target);
		$array = array("Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Aout", "Septembre", "Octobre", "Novembre", "Décembre");
		if ($interval->format('%R%a')>=0 && $interval->format('%R%a')<=$duration_news ) // AFFICHE LE MESSAGE DURANT LA DUREE DEFINITI EN PARAMETRE $duration APRES LA DATE DE DEBUT DEFINIT EN PARAMETRE $origin
		{
			echo '
				<div id="callout_id" class="callout"><!-- &#127879 -->
				  <div class="callout-header">Bienvenue sur le nouvel outil de Demande de Modification ! </div>
					<img src="\Common_Resources\Logo_SCM_2024_W_1.png" height="46px" style="z-index:98;position:absolute; top:8px; left:15px;vertical-align:middle;filter:grayscale(20%)" >
					<span class="closebtn" onclick="this.parentElement.style.display=\'none\';" > ×</span>
				  <div class="callout-container">
					<p style="text-indent:20px;margin-bottom:15px">L\'outil DMO passe en version 2 ! </p>
					<p style="text-indent:20px;margin-bottom:15px">En plus des celles déjà présentes dans la version 1, de nouvelles fonctionnalités font leur apparition :</p>
					<p style="text-indent:40px"> &#11185   Possibilité d\'ouvrir des DMO à destination des méthodes Assemblage </p>
					<p style="text-indent:40px"> &#11185   Possibilité d\'ouvrir des DMO à destination des méthodes Laboratoire </p>
					<p style="text-indent:40px"> &#11185   Visualisation en temps réel du nombre de DMO ouvertes, des temps moyens de traitement (<i>Leadtime</i>) et d\'attente (<i>Waiting</i>)</p>
					<p style="text-indent:40px"> &#11185   Visualisation en temps réel du nombre de DMO ouvertes et fermées chaque mois de l\'année fiscale en cours</p>
					<p style="text-indent:40px"> &#11185   Mise à disposition de nouveaux indicateurs</p>
					<p style="text-indent:40px"> &#11185   etc...</p>';
					//<p style="text-indent:20px;margin-top:28px;"> N\'hésitez pas à consulter <a href="\Common_Resources\Logo_SCM_2024_W_1.png" target="_blank">le document résumant l\'utilisation de l\'outil</a></p>
			echo '		<p style="text-align:center;font-style:italic;font-weight:bold;margin-top:28px;font-size:12px"> Merci pour votre utilisation de l\'outil. N\'hésitez pas à remonter les éventuels problèmes ou idées d\'amélioration !</p>
					
				  </div>
				</div>';
	
		}
		?>
		
		<input  
			type="image"
			src="\Common_Resources\search_2_icon.png" 
			id="banner_toggle_btn_id" 
			name="banner_toggle_btn_name" 
			class="btn white" 
			style="
			left:10px;
			top:15px;
			font-size:11;
			position:absolute;
			z-index:99;
			width:28px;
			height:28px;
			border-radius:50%;
			border:0.75px solid #D9D9D9;
			background-color:#F4F4F4F0;
			display:none;
			transition: 0.5s;
			box-shadow: rgba(0, 0, 0, 0.07) 0px 1px 2px, rgba(0, 0, 0, 0.47) 0px 2px 4px, rgba(0, 0, 0, 0.07) 0px 4px 8px, rgba(0, 0, 0, 0.07) 0px 8px 16px, rgba(0, 0, 0, 0.07) 0px 16px 32px, rgba(0, 0, 0, 0.07) 0px 32px 64px;"  
			value="|||" 
			title="Menu de Recherche" 
			onmouseover="banner_toggle_regul()" 
			/>


		<a Target="main_iframe" href="DMO_Welcome_frame.php">
			<img src="\Common_Resources\Logo_SCM_2024_W_1.png" height="46px" style="z-index:98;position: absolute; top:5px; right:0px;padding-right:10px; filter:grayscale(20%)" >
		</a>
		
		<a  
			type="image"
			style="
			font-size:8pt;
			position: absolute; 
			top:40px; 
			left:calc(100vw - 150px);
			z-index:99;
			position:absolute;
			opacity:0.8;
			text-decoration:none;
			color:white"
			title="Version actuelle"
			href="Deployment_Files\Deployment_Log.txt"\
			target="_blank"
		/>V2.0</a>
			
		<input HIDDEN type="image" src="\Common_Resources\UK_FLAG.png" height="12px" style="border-radius:25%; position: absolute; top:10px; right:150px; box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); filter:grayscale(20%)" >
		<input HIDDEN type="image" src="\Common_Resources\FRA_FLAG.png" height="12px" style="border-radius:25%; position: absolute; top:10px; right:130px;  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19); filter:grayscale(20%)" >
		
		<span style="font-size:8px;font-weight:normal;font-style:italic;color:white;text-transform:uppercase;position: absolute;opacity:0.6; 
			top:43px; 
			right:200px;
			z-index:99;">	
			<!-- <img src="\Common_Resources\icon_user.png" height="15px" style="margin-right:5px;filter : grayscale(90%);z-index:99;"> -->
			 <?php echo $logged_user;?>
		</span>
			
		
		<div id="sidepanel_left" class="w3-sidebar w3-light-white w3-bar-block" onclick="password_field_toggle()">	   
		   
		   <!-- PIN TO TOGGLE LEFT MENU bar
		   <input  
			type="image"
			src="\Common_Resources\pin_blue.png" 
			id="left_panel_lock_id" 
			class="left_panel_locked"
			style="
			right:15px;
			width:20px;
			height:20px;
			top:calc(100% - 92px);
			z-index:99;
			position:absolute;
			opacity:0.8;"
			title="Panneau Figé - Click = Activer la retraction auto"
			onclick="left_panel_toogle()"
			label="Verrouillage du panneau"
			/>
			-->
			
			
			<?php 
				$i = 1;
				$var_but = "button_" . $i; 
			?>
			<a Target="main_iframe" href="DMO_List_Main.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="border-radius:25px;" onclick="focus_in(this)">
				<table id="t_menu">
					<tr>
						<th>
						  <img src="\Common_Resources\mof_icon.png" 
									style="width:25px;height:25px;" 
									title="Aide sur l'utilisation de l'outil"
									label="Access to all DMOs">
						</th>
						<td>
							DMO List
						</td>
					</tr>
				</table>
			</a>
			
			<?php 
				$i = $i + 1;
				$var_but = "button_" . $i;
			?>
			<a Target="main_iframe" href="DMO_New_Request_form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="border-radius:25px;" onclick="focus_in(this)">
				<table id="t_menu">
				
					<tr>
						
						<th>
						  <img src="\Common_Resources\icon_new.png" 
									style="width:25px;height:25px;" 
									title="Creation of a new modification request"
									label="Creation of a new modification request">
						</th>
						<td>
							New Request
						</td>
						
					</tr>
				</table>
			</a>
			
			<?php 
				$i = $i + 1;
				$var_but = "button_" . $i;
			?>
			<a Target="main_iframe" href="DMO_Data_Extraction.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="border-radius:25px;" onclick="focus_in(this)">
				<table id="t_menu">
					<tr>
						<th>
						  <img src="\Common_Resources\roue_crantee.png" 
									style="width:25px;height:25px;" 
									title="Data Extraction"
									label="Data Extraction">
						</th>
						<td>
							Data Extraction
						</td>
					</tr>
				</table>
			</a>
			
			<?php 
				$i = $i + 1;
				$var_but = "button_" . $i;
			?>
			<a Target="main_iframe" href="DMO_KPI.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="border-radius:25px;" onclick="focus_in(this)">
				<table id="t_menu">
					<tr>
						<th>
						  <img src="\Common_Resources\icon_graph.png" 
									style="width:25px;height:25px;" 
									title="KPI"
									label="KPI">
						</th>
						<td>
							Overview / KPI
						</td>
					</tr>
				</table>
			</a>
			
			<?php 
				$i = $i + 1;
				$var_but = "button_" . $i;
			?>
			<a Target="main_iframe" href=".\Resources\PR04-02_B Gestion des modifications.pdf" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="border-radius:25px;" onclick="focus_in(this)">
				<table id="t_menu" border=0>
					<tr>
						<th>
						  <img src="\Common_Resources\icon_info.png" 
									style="width:25px;height:25px;" 
									title="Procedure PR04-02"
									label="Procedure PR04-02">
						</th>
						<td>
							Process PR04-02
						</td>
					</tr>
				</table>
			</a>
			
			<hr>
			
			<div id="<?php echo $var_but; ?>" style="border-radius:25px; text-align:center" >
				<table id="t_menu" border=0>
					<tr>
						<th colspan=2>
						  Quick Search by DMO#
						</th>
					</tr>

					<tr>
						<td style="text-align:center;">
							<span hidden><input type="text" class="input_welcome" name="Search_Cust_Drawing" id="Quick_Search_TXT" title="Plan client associé à la référence" placeholder="" onfocus="quick_search_txt()" onchange="quick_search()">
							or</span>
							<select id="Quick_Search_SLT" name="Search_divison" class="select_welcome"  title="Division/activité auquel la référence ou le plan sont associés" onchange="quick_search()">
								<option value="" SELECTED></option>
								<?php
									include('../DMO_Connexion_DB.php');
									$requete = 'SELECT DISTINCT DMO
												FROM tbl_dmo								
												ORDER BY dmo desc';
									$resultat = $mysqli_dmo->query($requete);
									while ($row = $resultat->fetch_assoc())
									{
										echo'<option value ="'.$row['DMO'].'" >'.substr($row['DMO'],3,5).'</option><br/>';
									}
									mysqli_close($mysqli_dmo);
								?>
							</select>
						</td>
					</tr>
					<tr>
						<td style="text-align:center">
							<font style="text-align:center;font-size:9px;color:#A4A4A4;font-family:Arial;font-variant:small-caps;font-weight:bold">
								(Consultation only)
							</font>
						</td>
					</tr>
				</table>
			</div>
			
	
			
			
		</div>

		<div style="bottom:0%;margin-bottom:12px;z-index:99;position:absolute;left:15px;">
			
			<span HIDDEN id="pass_zone" style="font-size:9pt;font-weight:bold;font-style:italic">Password:<br> 
				<input type="password" value="" style="width:calc(11.5vw - 30px);max-width:145px;height:21px;font-size:9pt;" id="pass_input">
				<br><input type="button" class="btn grey" id="pass_btn_validation" onclick="Admin_PWD_Check()" style="font-size:7pt;width:35px;height:21px;vertical-align:middle;text-align:center" value="GO"/>
				<script>
					var pass_input_validation = document.getElementById("pass_input");
					pass_input_validation.addEventListener("keypress", function(event) {
					  if (event.key === "Enter") {
						event.preventDefault();
						document.getElementById("pass_btn_validation").click();
					  }
					});
				</script>
			</span>
			
			<input type="image" onclick="pass_display()" src="\Common_Resources\icon_admin.png" id="admin_pass_button" style="width:20px;height:20px;cursor:pointer" title="Administrateur" label="Administrateur">
			
		</div>



	<div class="w3-container" style="margin-left:0%;" id="main_area" >
		<iframe
			name="main_iframe" 
			id="main_frame_id" 
			class="main_frame_reduced" 
			frameborder=0
			scrolling=yes
			src="DMO_Welcome_Frame.php"
			style="background-image: url(/Common_Resources/logo_scm_zoom_left_transparent.jpg);
				   background-repeat: no-repeat;
				   background-position: left bottom;
				   margin-left:-15px;
				   background-color:transparent;
				   width:95;
				   z-index:98;"
		>
	</div>



</body>
</html>