<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="DMO_Styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<?php	
	// PREPARATION DONNEES GRAPHIQUES
	// ##############################
	
	// NOMBRE DE DMO OUVERTES SUR L'ANNEE EN COURS   // ---> AND Type like "Engineering"
	$query_1='
			SELECT  
				year(Issue_Date) as "FY",
				DATE_FORMAT((Issue_Date) , "%b") as "Month", 
				count( case when Division="Energy" AND Type like "Engineering"  THEN 1 END) as "Eng. Energy",
				count( case when Division="Industry" AND Type like "Engineering" THEN 1 END) as "Eng. Industry",
				count( case when Division="Aerospace" AND Type like "Engineering"  THEN 1 END) as "Eng. Aerospace",
				count( case when Type like "Method Lab." THEN 1 END) as "Method Lab.",	
				count( case when Type like "Method Assy." THEN 1 END) as "Method Assy."
			FROM 
				tbl_dmo
			WHERE
				YEAR(Issue_Date)=Year(Now())
			GROUP BY year(Issue_Date), 
					 month(Issue_Date)

			ORDER BY 
					year(Issue_Date) ASC, 
					month(Issue_Date) ASC';

	// NOMBRE DE DMO CLOTUREES SUR L'ANNEE EN COURS // ---> AND Type like "Engineering"
	$query_2='
			SELECT  
				year(End_Date) as "FY",
				DATE_FORMAT((End_Date) , "%b") as "Month",			
				count( case when Division="Energy" AND Type like "Engineering" AND Year(End_Date) like YEAR(Now()) AND Status like "Closed" THEN 1 END) as "Eng. Energy",
				count( case when Division="Industry" AND Type like "Engineering" AND Year(End_Date) like YEAR(Now()) AND Status like "Closed" THEN 1 END) as "Eng. Industry",
				count( case when Division="Aerospace" AND Type like "Engineering" AND Year(End_Date) like YEAR(Now()) AND Status like "Closed" THEN 1 END) as "Eng. Aerospace",
				count( case when Year(End_Date) like YEAR(Now()) AND Status like "Closed" AND Type like "Method Lab." THEN 1 END) as "Method Lab.",		
				count( case when Year(End_Date) like YEAR(Now()) AND Status like "Closed" AND Type like "Method Assy." THEN 1 END) as "Method Assy."
			FROM 
				tbl_dmo
			WHERE
				Year(End_Date) like YEAR(Now()) AND Status like "Closed"
			GROUP BY year(End_Date), 
					 month(End_Date)

			ORDER BY 
					year(End_Date) ASC, 
					month(End_Date) ASC';

	// NOMBRE DE DMO ACTUELLEMENT OUVERTES PAR DIVISION  ---> AND Type like "Engineering"
	$query_3='SELECT 
				Division as "Division" ,
				count( case when Status="Open" AND Type like "Engineering" THEN 1 END) as "Open" ,			
				count( case when Status="Closed" AND Type like "Engineering" THEN 1 END) as "Closed"
			FROM 
				tbl_dmo
			GROUP BY 
				Division
			UNION
			SELECT 
				Type ,
				count( case when Status="Open"  THEN 1 END) as "Open" ,			
				count( case when Status="Closed"  THEN 1 END) as "Closed"
			FROM 
				tbl_dmo
			GROUP BY 
				Type	
			ORDER BY 
				Division';
			

	// SUR LES 6 DERNIERS MOIS
	// NOMBRE DE DMO OUVERTE
	// TEMPS D'ATTENTE DES DMO TOUJOURS OUVERTES
	// TEMPS DE TRAITEMENT MOYEN DES DMO CLOTUREES
	$query_4='SELECT 
				Division,
				ROUND(AVG( case when Status="Closed" AND Type like "Engineering" AND DATEDIFF(NOW(), End_Date)<=360 THEN DATEDIFF(end_date, Issue_date) END)) as "LEADTIME", 
				ROUND(AVG( case when Status="Open" AND Type like "Engineering" AND Issue_date not like Now() THEN DATEDIFF(NOW(), Issue_date) END)) as "WAITING",
				ROUND(SUM(case when Status="Closed" AND Type like "Engineering" AND YEAR(End_Date) = YEAR(Now()) THEN 1 END)/SUM(case when Status="Open" AND Issue_date not like Now() THEN 1 END)*100) AS "RATE"
			FROM 
				tbl_dmo 
			GROUP BY 
				Division
		UNION 
			SELECT
				Type,
				ROUND(AVG( case when Status="Closed" AND DATEDIFF(NOW(), End_Date)<=360 THEN DATEDIFF(end_date, Issue_date) END)) as "LEADTIME",
				ROUND(AVG( case when Status="Open" AND Issue_date not like Now() THEN DATEDIFF(NOW(), Issue_date) END)) as "WAITING",
				ROUND(SUM( case when Status="Closed" AND DATEDIFF(NOW(), Issue_date) THEN 1 END)/SUM(case when Status="Open" AND Issue_date not like Now() THEN 1 END)*100) AS "RATE"
				
			FROM 
				tbl_dmo 
			GROUP BY 
				Type	
			ORDER BY 
				Division				
		';

	include('../DMO_Connexion_DB.php');
	
	// GRAPH 1
	// -------
	$data_graph_1="['Month', 'Eng. Energy',  'Eng. Industry', 'Eng. Aero.', 'Method Lab.', 'Method Assy.', 'Cumul'],";
	$cumulative_1=0;
	$tmp_year=0;
	$cumulative_1_yearly=0;
	$resultat_1 = $mysqli_dmo->query($query_1);
	while ($row_1 = $resultat_1->fetch_assoc())
	{
		
		if($tmp_year!=0)
		{
			if($tmp_year!=$row_1['FY'])
			{
				$tmp_year=$row_1['FY'];
				$cumulative_1_yearly=$row_1['Eng. Energy']+$row_1['Eng. Industry']+$row_1['Eng. Aerospace']+$row_1['Method Lab.']+$row_1['Method Assy.'];
			} else {
				$cumulative_1_yearly=$cumulative_1_yearly+$row_1['Eng. Energy']+$row_1['Eng. Industry']+$row_1['Eng. Aerospace']+$row_1['Method Lab.']+$row_1['Method Assy.'];
			}
		} else {
			$tmp_year=$row_1['FY'];
			$cumulative_1_yearly=$cumulative_1_yearly+$row_1['Eng. Energy']+$row_1['Eng. Industry']+$row_1['Eng. Aerospace']+$row_1['Method Lab.']+$row_1['Method Assy.'];
		}
		$data_graph_1=$data_graph_1 ."['".$row_1['FY'].'-'.$row_1['Month']."',".$row_1['Eng. Energy'].",".$row_1['Eng. Industry'].",".$row_1['Eng. Aerospace'].",".$row_1['Method Lab.'].",".$row_1['Method Assy.'].",".$cumulative_1_yearly."],";
	}
	$data_graph_1=substr($data_graph_1, 0, -1);
	
	// #######
	
	// GRAPH 2
	// -------
	$data_graph_2="['Month', 'Eng. Energy',  'Eng. Industry', 'Eng. Aero.', 'Method Lab.', 'Method Assy.','Cumul'],";
	$cumulative_2=0;
	$tmp_year=0;
	$cumulative_2_yearly=0;
	$resultat_2 = $mysqli_dmo->query($query_2);
	while ($row_2 = $resultat_2->fetch_assoc())
	{	
		if($tmp_year!=0)
		{
			if($tmp_year!=$row_2['FY'])
			{
				$tmp_year=$row_2['FY'];
				$cumulative_2_yearly=$row_2['Eng. Energy']+$row_2['Eng. Industry']+$row_2['Eng. Aerospace']+$row_2['Method Lab.']+$row_2['Method Assy.'];
			} else {
				$cumulative_2_yearly=$cumulative_2_yearly+$row_2['Eng. Energy']+$row_2['Eng. Industry']+$row_2['Eng. Aerospace']+$row_2['Method Lab.']+$row_2['Method Assy.'];
			}
		} else {
			$tmp_year=$row_2['FY'];
			$cumulative_2_yearly=$cumulative_2_yearly+$row_2['Eng. Energy']+$row_2['Eng. Industry']+$row_2['Eng. Aerospace']+$row_2['Method Lab.']+$row_2['Method Assy.'];
		}

		$cumulative_2=$cumulative_2+$row_2['Eng. Energy']+$row_2['Eng. Industry']+$row_2['Eng. Aerospace']+$row_2['Method Lab.']+$row_2['Method Assy.'];
		$data_graph_2=$data_graph_2 ."['".$row_2['FY'].'-'.$row_2['Month']."',".$row_2['Eng. Energy'].",".$row_2['Eng. Industry'].",".$row_2['Eng. Aerospace'].",".$row_2['Method Lab.'].",".$row_2['Method Assy.'].",".$cumulative_2_yearly."],";
	}
	$data_graph_2=substr($data_graph_2, 0, -1);
	// #######
	
	// GRAPH 3
	// -------
	//$bar_color=['','#b87333','silver','gold',''];
	$data_graph_3="['Division', ''],";
	$resultat_3 = $mysqli_dmo->query($query_3);
	$result_3_count=0;
	while ($row_3 = $resultat_3->fetch_assoc())
	{	
		if ($row_3['Division']!='Engineering')
		{
			$data_graph_3=$data_graph_3 ."['".$row_3['Division']."',".$row_3['Open']."],";
			$result_3_count=$result_3_count+1;
		}
	}
	$data_graph_3=str_replace("Aerospace","Eng. Aero.", $data_graph_3);
	$data_graph_3=str_replace("Industry","Eng. Industry", $data_graph_3);
	$data_graph_3=str_replace("Energy","Eng. Energy.", $data_graph_3);
	$data_graph_3=substr($data_graph_3, 0, -1);
	// #######
	
	// GRAPH 4
	// -------
	$data_graph_4="";
	$resultat_4 = $mysqli_dmo->query($query_4);
	while ($row_4 = $resultat_4->fetch_assoc())
	{	
		if ($row_4['Division']!='Engineering')
		{
			if ($row_4['RATE']=="")
			{
				$rate_val="-";
			} else {
				$rate_val=$row_4['RATE']."%";
			}
			if ($row_4['WAITING']=="")
			{
				$wait_val="-";
			} else {
				$wait_val=$row_4['WAITING'];
			}
			if ($row_4['LEADTIME']=="")
			{
				$lead_val="-";
			} else {
				$lead_val=$row_4['LEADTIME'];
			}
			$data_graph_4=$data_graph_4 .'<tr><td style="border-left: 1px solid black;border-top: 1px solid black;border-bottom: 1px solid black; border-radius: 10px 0px 0px 10px;">'.$row_4['Division'].'</td><td style="border-top: 1px solid black;border-bottom: 1px solid black;">'.$wait_val.'</td><td style="border-top: 1px solid black;border-bottom: 1px solid black;">'.$lead_val.'</td><td style="border-right: 1px solid black;border-top: 1px solid black; border-bottom: 1px solid black; border-radius: 0px 10px 10px 0px;">'.$rate_val.'</td></tr>';
		}
	}
	$data_graph_4=str_replace('Aerospace','<img width="50px" src="\Common_Resources\Activity_MOB_AERO.png" title="MOBILITY AEROSPACE">',$data_graph_4);
	$data_graph_4=str_replace('Industry','<img width="50px" src="\Common_Resources\Activity_MOB_INDUS.png" title="MOBILITY INDUSTRY">',$data_graph_4);
	$data_graph_4=str_replace('Energy','<img width="50px" src="\Common_Resources\Activity_ENERGY_RENEW.png" title="ENERGY">',$data_graph_4);
	$data_graph_4=str_replace('Method Lab.','<img width="50px" src="\Common_Resources\Activity_Method_Lab.png" title="METHOD LAB">',$data_graph_4);
	$data_graph_4=str_replace('Method Assy.','<img width="50px" src="\Common_Resources\Activity_Method_Assy.png" title="METHOD ASSEMBLY">',$data_graph_4);
	// #######


	$mysqli_dmo->close();	

?>

<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script>
	window.onresize = drawVisualization;

	google.charts.load('current', {'packages':['corechart']});
	google.charts.setOnLoadCallback(drawVisualization);
	
	
	function Y_axis_consolidation()
	{
		var max_open=46;
		var max_cumul_open=<?php echo $cumulative_1?>;
		var stop=0;

		while (stop!=1)
		{
			var ratio=max_cumul_open/max_open;
			if (Math.trunc(ratio)!= ratio)
			{
				max_open=max_open+1;
			} else {
					// if (Math.trunc(max_open/5)!= (max_open/5))
						// {
							// max_open=max_open+1;
						// }
						// else {
					stop=1;
						// }
			}
		}
		//alert(max_open + "    " + max_cumul_open/max_open);

	}

	function drawVisualization() 
	{
		
		//Y_axis_consolidation();
			
		// Some raw data (not necessarily accurate)
		var data_1 = google.visualization.arrayToDataTable([
		  <?php echo $data_graph_1 ?>
		]);

		var data_2 = google.visualization.arrayToDataTable([
		  <?php echo $data_graph_2 ?>
		]);
		
		var data_3 = google.visualization.arrayToDataTable([
		  <?php echo $data_graph_3 ?>
		]);

		var options_1 = 
		{
			backgroundColor: 'transparent',
			legend: { alignment: 'center', position: 'bottom',textStyle: {fontSize: 10}},
			fontSize:13,
			series: {
				0: {axis: 'month', targetAxisIndex: 0, type: 'bars',   color:'#3A85BB'},
				1: {axis: 'month', targetAxisIndex: 0, type: 'bars',   color:'#83B8DA'},
				2: {axis: 'month', targetAxisIndex: 0, type: 'bars',   color:'#C1E0F6'},
				3: {axis: 'month', targetAxisIndex: 0, type: 'bars',   color:'#DDDDE0'},
				4: {axis: 'month', targetAxisIndex: 0, type: 'bars',   color:'#787981'},
				5: {axis: 'year', targetAxisIndex: 1,  type: 'line',   color:'black'}
			}, 
			isStacked: true,
			vAxes: {
					0: {
						title: 'Monthly Creation',
						viewWindow: {
							min: 0,
							max: 100
						} ,
						
					},
					1: {
						title: 'Yearly',
						viewWindow: {
								min: 0,
								max: 500
						},
					},
					},
			bar: { groupWidth: '80%' },
			chartArea:{left:80,top:20,width:'80%',height:'75%'},
			
		};


		var options_2 = 
		{
			backgroundColor: 'transparent',
			legend: { alignment: 'center', position: 'bottom', textStyle: {fontSize: 10}},
			fontSize:13,
			series: {
				0: {targetAxisIndex: 0, type: 'bars', color:'#b87333'},
				1: {targetAxisIndex: 0, type: 'bars', color:'#fdc300'},
				2: {targetAxisIndex: 0, type: 'bars', color:'#FAD7A0'},
				3: {targetAxisIndex: 0, type: 'bars', color:'#afca0b'},
				4: {targetAxisIndex: 0, type: 'bars', color:'#919B54'},
				5: {targetAxisIndex: 1, type: 'line', color:'#454545'}
				},
			isStacked: true,					
			vAxes: {
					0: {	
						title: 'Monthly Closure',
						TextStyle: {
									color: 'red', 
									fontSize:'30' 
									},
						viewWindow: {
										min: 0,
										max: 100,
									},
						isStacked: true,
					},
					1: {
						title: 'Yearly',
						viewWindow: {
										min: 0,
										max: 500
									},
						}
					},
			bar: { groupWidth: '80%' },
			chartArea:{left:80,top:20,width:'80%',height:'75%'},
		};

		
		var options_3 = 
		{
			title: 'Opened DMO',
			backgroundColor: 'transparent',
			legend: { alignment: 'center', position: 'labeled',textStyle: {fontSize: 12}},
			pieSliceText: 'value',
			fontSize:13,
			slices: {
					0: {offset: 0.2, color:'#C1E0F6'},
					1: {offset: 0.2, color:'#83B8DA'},
					2: {offset: 0.2, color:'#3A85BB'},
					3: {offset: 0.2, color:'#787981'},
					4: {offset: 0.2, color:'#DDDDE0'},
			},
			pieSliceBorderColor: 'black',
			pieHole: 0.5,
			pieStartAngle: 90,
			chartArea:{left:20,top:10,width:'94%',height:'80%',},
		};

		var chart_1 = new google.visualization.ComboChart(document.getElementById('chart_div_1'));
		chart_1.draw(data_1, options_1, {responsive: true});

		var chart_2 = new google.visualization.ComboChart(document.getElementById('chart_div_2'));
		chart_2.draw(data_2, options_2, {responsive: true});
		
		var chart_3 = new google.visualization.PieChart(document.getElementById('chart_div_3'));
		chart_3.draw(data_3, options_3, {responsive: true});
	}
</script>	




</head>

<title>
    
</title>

<?php 
	// PARAMETRES A DEFINIR
	// --------------------
	$origin = date_create('2025-02-25');
	$duration = 5;
	$type_msg="COUPURE";
	// --------------------
		
	
	$target = date_create(date('Y-m-j'));
	$interval = date_diff($origin, $target);
	$array = array("Janvier", "Février", "Mars", "Avril", "Mai", "Juin", "Juillet", "Aout", "Septembre", "Octobre", "Novembre", "Décembre");
	if ($interval->format('%R%a')>=0 && $interval->format('%R%a')<=$duration ) // AFFICHE LE MESSAGE DURANT LA DUREE DEFINITI EN PARAMETRE $duration APRES LA DATE DE DEBUT DEFINIT EN PARAMETRE $origin
	{ 
		// echo '<div id="News_Title">
			// <font style="text-decoration:none; ">		
				// Nouveauté depuis le '.date_format($origin, 'd').' '.$array[intval(date_format($origin, 'm'))-1].' '.date_format($origin, 'Y').'
				// </font>
			// :
			// <font style="color:#393939;">
				// SITE EN COURS DE CREATION - NE PAS UTILISER.
			// </font>
			// </div>';
		// echo '<div id="News_Title">
			// <font style="text-decoration:none; ">		
				// L\'ouverture des pieces jointes de type email (\.msg) disponible en attachement des DMO ne fonctionne pas pour le moment.
				// </font>
			// </div>';
			
	}
?>



<body style="text-indent:20px">


<table border=0	>
	<tr>
		<td>
			<div style="font-size:14px;font-family:Arial;font-weight:bold;margin-top:15px;">
				Welcome on the Modification Request Tool!
			</div>
			<br><br>
			<div id="Body">
				The DMO (<i>Demande de Modification</i>) / Modification Request Tool gives a way to create requests to Engineering or Method departments to modify deliverables they are responsbile for.
			</div>

				<br><br>
			<div id="Body">
				Requests related to the following documents can be registered and tracked through this tool:
				<br>
			</div>
			<div id="Body">
				<p style="margin-left:45px">
				<b>Engineering: </b>Detailed Drawings, Datasheet (FT), ATEX documentation, User Manual (NU), Bill of Material (BoM), Scope of Prototype / Delivery (SoP, SoD)... 
				</p>
			</div>
			<div id="Body">
				<div style="margin-left:45px">
				<b>Method/Indus: </b>Assembly Procedure (FI), Packaging and handling Instructions (PHI), FAT Procedure (FATP), Qualification Program (QPP) etc...
				</div>
				<br>
			</div>
			<!--<div id="Body">
				<br>
				Regular reviews are held to make decisions on each new requests and provide update on opened ones. You will be notified by email when your request is accepted, rejected or closed.
				<br><br>
			</div>-->
			<div id="Body">
				<p style="font-weight:bold;color:#D35400;">Any modification to any Ex product documentation <u>MUST</u> be processed through a DMO request.</p>
			</div>

			<div id="Body" style="margin-top:10px;margin-bottom:10px">
				<font style="font-style:italic;"> Note: All DMO created before 2020 can be accessed in a <a href="/DMO/Resources/2021_07_27_Archives_Prise_numero_DM_Offshore.pdf" target="Blank" title="Display the list of DMO between 2016-2019">dedicated page</a>.</font>
			</div>
		<td>
			<!--<div style="border:1px solid black;border-radius:30px;padding-left:-60px;margin-right:40px;padding-bottom:5px;padding-top:20px">-->
				<div id="chart_div_3" style="width:calc(100vw / 4)"></div>
				<div style="text-align:center;margin-left:10px;font-weight:bold;font-style:italic;margin-top:10px">Number of Opened DMOs</div>
			<!--</div>-->
		</td>
		<td style="vertical-align:top;text-align:center;padding-left:20px;padding-top:30px">
				<table id="t09">
					<tr>
						<th style="border-radius:10px 0px 0px 10px">Division</th>
						<th>
							<div class="dropdown_checkbox_KPI">
								Waiting
								<div class="dropdown_checkbox_KPI-content" style="text-align:center;">
								<font style="font-weight:bold">Average waiting time for the opened DMO. <br>Time in calendar days.</font>
							</div>
							</div>
						</th>
						<th style="">
							<div class="dropdown_checkbox_KPI">
								Leadtime
								<div class="dropdown_checkbox_KPI-content" style="text-align:center;">
								<font style="font-weight:bold">Average leadtime for the closed-DMO to be treated, during the last 12 months. Time in calendar days.</font>
							</div>
							</div>
						</th>
						<th style="border-radius:0px 10px 10px 0px">
							<div class="dropdown_checkbox_KPI">
								Rate
								<div class="dropdown_checkbox_KPI-content" style="text-align:center;">
								<font style="font-weight:bold">
									DMO closing rate --> Number of DMO closed during the ongoing FY divided by the number of still-opened DMO.							
								</font>
							</div>
							</div>
						</th>
					</tr>
					<?php echo $data_graph_4 ?>
				</table>
		</td>
		
	</tr>
	<tr>
		<td colspan=3>
			<table border=0>
				<tr>
					<td style="width:calc(100vw / 2 - 20px);text-align:center;font-size:10pt">
						<div id="chart_div_1" style="text-algin:center;height:calc(100vh / 2.3)"></div>
						
					</td>
					<td style="width:calc(100vw / 2 - 20px);text-align:center;font-size:10pt">
						<div id="chart_div_2" style="height:calc(100vh / 2.3)" ></div>
						
					</td>
				</tr>
				<tr>
					<td style="text-align:center;">
						<div style="font-weight:bold;font-style:italic">Number of <font style="text-decoration:underline">DMO created</font>  per month and Cumulative over the year</div>
					</td>
					<td style="text-align:center;">
						<div style="font-weight:bold;font-style:italic">Number of <font style="text-decoration:underline">DMO closed</font> per month and Cumulative over the year</div>
					</td>
				</tr>
			</table>
		</td>
	</tr>
</table>
		
</body>

</html>