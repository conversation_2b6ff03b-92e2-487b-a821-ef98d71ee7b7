<!--DEFINE VALUE OF Attachment_Path_Root & Attachment_Folder_Name-->
<!----------------------------------------------------------------->
<?php
	include('../DMO_Connexion_DB.php');
	
	$Q_1 = "SELECT DISTINCT Parameter_Value FROM tbl_parameters WHERE Parameter like 'Attachment_Path_Root';";
	$Res_1 = $mysqli_dmo->query($Q_1);
	
	while ($row = $Res_1->fetch_assoc())
	{
		$Path_Folder=$row['Parameter_Value'] . '\\'; 
	}

	$Q_2 = "SELECT DISTINCT Parameter_Value FROM tbl_parameters WHERE Parameter like 'Attachment_Folder_Name';";
	$Res_2 = $mysqli_dmo->query($Q_2);
	while ($row = $Res_2->fetch_assoc())
	{
		$Attachment_Folder=$row['Parameter_Value']; 
	}

	$Q_3 = "SELECT DISTINCT Parameter_Value FROM tbl_parameters WHERE Parameter like 'Justification_File_Name';";
	$Res_3 = $mysqli_dmo->query($Q_3);
	while ($row = $Res_3->fetch_assoc())
	{
		$Justification_File_Name=$row['Parameter_Value']; 
	}
	
	$Q_4 = "SELECT DISTINCT Parameter_Value FROM tbl_parameters WHERE Parameter like 'Report_Folder';";
	$Res_4 = $mysqli_dmo->query($Q_4);
	while ($row = $Res_4->fetch_assoc())
	{
		$Report_Folder_Name=$row['Parameter_Value']; 
	}
	
	//mysqli_close($mysqli_dmo);
?>
<!------------------------------>