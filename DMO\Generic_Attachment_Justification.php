<?php
 if (is_dir($path_attachment))
	{
		
	if (((iterator_count($files))-2)>0)
		{
			foreach (new DirectoryIterator($path_attachment) as $fileInfo)
			{	
				if($fileInfo->isDot()) continue;		
				$tronca_value=35;
				$file_name=htmlspecialchars($fileInfo->getFilename(), ENT_QUOTES);
				
				if ((($file_name)!="Thumbs.db") &&  (substr($fileInfo->getFilename(),0,13)=="Justification"))
				{
					if (strlen($file_name)<=$tronca_value )
					{
						// echo "<a href='" . $path_attachment . $file_name . "'>" . substr($file_name,14) . "</a><br>\n";
						echo '<a href="' . $path_attachment . $file_name . '" download="'.$file_name.'">' .substr($file_name,14) . '</a><br>';

					} else {
						// echo "<a  href='" . $path_attachment . $file_name . "'>" .  substr($file_name,14,$tronca_value)  . "[...]." . $fileInfo->getExtension() . "</a><br>\n";
						echo '<a  href="' . $path_attachment . $file_name . '" download="'.$file_name.'">' .  substr($file_name,14,$tronca_value)   . '[...].' . $fileInfo->getExtension() . '</a><br>';
					}
				}
				 
			}
		} else {
				echo "No justification file";
				}

			


	} else {
			//echo '<div id="Body">Attachment(s) - 0';
			//echo ':</div>';			
			echo "No justification file";
			}
?>