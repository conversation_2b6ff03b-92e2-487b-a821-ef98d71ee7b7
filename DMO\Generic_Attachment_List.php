<?php
 if (is_dir($path_attachment))
	{
	echo '<div id="Body">Attachment(s) - ';
	$files = new RecursiveIteratorIterator(new RecursiveDirectoryIterator($path_attachment));
	echo iterator_count($files)-2;
	echo ':</div>';

	if (((iterator_count($files))-2)>0)
		{
			foreach (new DirectoryIterator($path_attachment) as $fileInfo)
			{	
				if($fileInfo->isDot()) continue;		
				$tronca_value=35;
				$file_name=htmlspecialchars($fileInfo->getFilename(), ENT_QUOTES);
				
				if ((($file_name)!="Thumbs.db") &&  (substr($file_name,0,13)!="Justification"))
				{
					if (strlen($file_name)<=$tronca_value )
					{
						echo '<a href="' . $path_attachment . $file_name . '" download="'.$file_name.'">' . $file_name . '</a><br>';
					} else {
						echo '<a  href="' . $path_attachment . $file_name . '" download="'.$file_name.'">' .  substr($file_name,0,$tronca_value)  . '[...].' . $fileInfo->getExtension() . '</a><br>';
					}
				}
				 
			}
		} else {
			echo "No attachment";
		}

	} else {
			echo '<div id="Body">Attachment(s) - 0';
			echo ':</div>';			
			echo "No attachment";
			}
?>