<?php

/**
 * Script d'analyse des state_timestamps actuels pour identifier les problèmes
 * et valider les améliorations nécessaires
 */

require_once __DIR__ . '/vendor/autoload.php';

use Doctrine\DBAL\Connection;
use Doctrine\DBAL\DriverManager;

class StateTimestampsAnalyzer
{
    private Connection $connection;
    private array $statistics = [];
    
    public function __construct()
    {
        // Configuration de la base de données (à adapter selon votre configuration)
        $connectionParams = [
            'dbname' => 'your_database_name',
            'user' => 'your_username',
            'password' => 'your_password',
            'host' => 'localhost',
            'driver' => 'pdo_mysql',
        ];
        
        $this->connection = DriverManager::getConnection($connectionParams);
    }
    
    /**
     * Analyser tous les aspects des state_timestamps
     */
    public function analyzeAll(): void
    {
        echo "=== Analyse des State Timestamps ===\n\n";
        
        $this->analyzeBasicStatistics();
        $this->analyzeDataCompleteness();
        $this->analyzeTransitionLogic();
        $this->analyzeFromStateAccuracy();
        $this->generateRecommendations();
    }
    
    /**
     * Analyser les statistiques de base
     */
    private function analyzeBasicStatistics(): void
    {
        echo "1. Statistiques de base\n";
        echo str_repeat("-", 30) . "\n";
        
        // Nombre total de documents
        $totalDocs = $this->connection->fetchOne('SELECT COUNT(*) FROM document');
        echo "Total documents: {$totalDocs}\n";
        
        // Documents avec state_timestamps
        $docsWithTimestamps = $this->connection->fetchOne(
            'SELECT COUNT(*) FROM document WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"'
        );
        echo "Documents avec state_timestamps: {$docsWithTimestamps}\n";
        
        // Pourcentage de couverture
        $coverage = $totalDocs > 0 ? round(($docsWithTimestamps / $totalDocs) * 100, 2) : 0;
        echo "Couverture: {$coverage}%\n\n";
        
        $this->statistics['total_docs'] = $totalDocs;
        $this->statistics['docs_with_timestamps'] = $docsWithTimestamps;
        $this->statistics['coverage'] = $coverage;
    }
    
    /**
     * Analyser la complétude des données
     */
    private function analyzeDataCompleteness(): void
    {
        echo "2. Complétude des données\n";
        echo str_repeat("-", 30) . "\n";
        
        // Récupérer tous les documents avec leurs state_timestamps
        $documents = $this->connection->fetchAllAssociative(
            'SELECT id, reference, state_timestamps FROM document 
             WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"'
        );
        
        $issues = [
            'missing_exit' => 0,
            'missing_from_state' => 0,
            'invalid_dates' => 0,
            'empty_states' => 0
        ];
        
        foreach ($documents as $doc) {
            $stateTimestamps = json_decode($doc['state_timestamps'], true);
            
            if (!is_array($stateTimestamps)) {
                $issues['empty_states']++;
                continue;
            }
            
            foreach ($stateTimestamps as $state => $entries) {
                if (!is_array($entries)) {
                    continue;
                }
                
                foreach ($entries as $entry) {
                    // Vérifier les exits manquants
                    if (!isset($entry['exit']) || $entry['exit'] === null) {
                        // C'est normal pour l'état actuel, mais problématique pour les états passés
                        if ($this->isHistoricalState($entry, $stateTimestamps)) {
                            $issues['missing_exit']++;
                        }
                    }
                    
                    // Vérifier les from_state manquants
                    if (!isset($entry['from_state']) || $entry['from_state'] === null) {
                        // C'est normal seulement pour le premier état (BE_0)
                        if ($state !== 'BE_0') {
                            $issues['missing_from_state']++;
                        }
                    }
                    
                    // Vérifier la validité des dates
                    if (isset($entry['enter']) && !$this->isValidDate($entry['enter'])) {
                        $issues['invalid_dates']++;
                    }
                    if (isset($entry['exit']) && $entry['exit'] !== null && !$this->isValidDate($entry['exit'])) {
                        $issues['invalid_dates']++;
                    }
                }
            }
        }
        
        echo "Problèmes identifiés:\n";
        echo "- Exits manquants: {$issues['missing_exit']}\n";
        echo "- From_state manquants: {$issues['missing_from_state']}\n";
        echo "- Dates invalides: {$issues['invalid_dates']}\n";
        echo "- États vides: {$issues['empty_states']}\n\n";
        
        $this->statistics['issues'] = $issues;
    }
    
    /**
     * Analyser la logique des transitions
     */
    private function analyzeTransitionLogic(): void
    {
        echo "3. Logique des transitions\n";
        echo str_repeat("-", 30) . "\n";
        
        $workflowOrder = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];
        
        $documents = $this->connection->fetchAllAssociative(
            'SELECT id, reference, state_timestamps FROM document 
             WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"
             LIMIT 100' // Limiter pour l'analyse
        );
        
        $transitionIssues = [
            'invalid_sequence' => 0,
            'missing_intermediate_states' => 0,
            'chronological_errors' => 0
        ];
        
        foreach ($documents as $doc) {
            $stateTimestamps = json_decode($doc['state_timestamps'], true);
            
            if (!is_array($stateTimestamps)) {
                continue;
            }
            
            // Analyser la séquence des états
            $stateSequence = $this->extractStateSequence($stateTimestamps);
            
            // Vérifier l'ordre chronologique
            if (!$this->isChronologicallyValid($stateSequence)) {
                $transitionIssues['chronological_errors']++;
            }
            
            // Vérifier la logique de workflow
            if (!$this->isWorkflowSequenceValid($stateSequence, $workflowOrder)) {
                $transitionIssues['invalid_sequence']++;
            }
        }
        
        echo "Problèmes de transitions:\n";
        echo "- Séquences invalides: {$transitionIssues['invalid_sequence']}\n";
        echo "- États intermédiaires manquants: {$transitionIssues['missing_intermediate_states']}\n";
        echo "- Erreurs chronologiques: {$transitionIssues['chronological_errors']}\n\n";
        
        $this->statistics['transition_issues'] = $transitionIssues;
    }
    
    /**
     * Analyser la précision des from_state
     */
    private function analyzeFromStateAccuracy(): void
    {
        echo "4. Précision des from_state\n";
        echo str_repeat("-", 30) . "\n";
        
        $documents = $this->connection->fetchAllAssociative(
            'SELECT id, reference, state_timestamps FROM document 
             WHERE state_timestamps IS NOT NULL AND state_timestamps != "{}"
             LIMIT 50' // Limiter pour l'analyse détaillée
        );
        
        $fromStateIssues = [
            'incorrect_from_state' => 0,
            'missing_from_state' => 0,
            'logical_inconsistencies' => 0
        ];
        
        foreach ($documents as $doc) {
            $stateTimestamps = json_decode($doc['state_timestamps'], true);
            
            if (!is_array($stateTimestamps)) {
                continue;
            }
            
            $stateSequence = $this->extractStateSequence($stateTimestamps);
            
            for ($i = 1; $i < count($stateSequence); $i++) {
                $currentState = $stateSequence[$i];
                $expectedFromState = $stateSequence[$i - 1]['state'];
                
                $actualFromState = $this->getFromStateForEntry($stateTimestamps, $currentState['state'], $currentState['enter']);
                
                if ($actualFromState === null) {
                    $fromStateIssues['missing_from_state']++;
                } elseif ($actualFromState !== $expectedFromState) {
                    $fromStateIssues['incorrect_from_state']++;
                }
            }
        }
        
        echo "Problèmes de from_state:\n";
        echo "- From_state incorrects: {$fromStateIssues['incorrect_from_state']}\n";
        echo "- From_state manquants: {$fromStateIssues['missing_from_state']}\n";
        echo "- Incohérences logiques: {$fromStateIssues['logical_inconsistencies']}\n\n";
        
        $this->statistics['from_state_issues'] = $fromStateIssues;
    }
    
    /**
     * Générer des recommandations d'amélioration
     */
    private function generateRecommendations(): void
    {
        echo "5. Recommandations d'amélioration\n";
        echo str_repeat("-", 40) . "\n";
        
        $issues = $this->statistics['issues'] ?? [];
        $transitionIssues = $this->statistics['transition_issues'] ?? [];
        $fromStateIssues = $this->statistics['from_state_issues'] ?? [];
        
        echo "Priorités d'amélioration:\n\n";
        
        // Priorité 1: Données manquantes critiques
        if (($issues['missing_exit'] ?? 0) > 0 || ($issues['missing_from_state'] ?? 0) > 0) {
            echo "🔴 PRIORITÉ HAUTE: Compléter les données manquantes\n";
            echo "   - Implémenter la logique de calcul automatique des exits\n";
            echo "   - Améliorer l'algorithme de détermination des from_state\n";
            echo "   - Recalculer les timestamps pour tous les documents existants\n\n";
        }
        
        // Priorité 2: Logique de workflow
        if (($transitionIssues['invalid_sequence'] ?? 0) > 0 || ($transitionIssues['chronological_errors'] ?? 0) > 0) {
            echo "🟡 PRIORITÉ MOYENNE: Corriger la logique de workflow\n";
            echo "   - Valider les séquences d'états selon les règles métier\n";
            echo "   - Implémenter des contrôles de cohérence chronologique\n";
            echo "   - Ajouter des validations lors de la migration\n\n";
        }
        
        // Priorité 3: Optimisations
        if (($this->statistics['coverage'] ?? 0) < 95) {
            echo "🟢 PRIORITÉ BASSE: Améliorer la couverture\n";
            echo "   - Traiter les documents sans state_timestamps\n";
            echo "   - Optimiser les performances de la migration\n";
            echo "   - Ajouter des métriques de suivi\n\n";
        }
        
        echo "Actions recommandées:\n";
        echo "1. Exécuter le script de migration amélioré\n";
        echo "2. Valider les résultats avec les tests automatisés\n";
        echo "3. Mettre en place un monitoring continu\n";
        echo "4. Documenter les règles de transition pour l'équipe\n\n";
    }
    
    // Méthodes utilitaires
    
    private function isHistoricalState(array $entry, array $allStates): bool
    {
        // Un état est historique s'il y a des états avec des dates postérieures
        $entryDate = $entry['enter'] ?? null;
        if (!$entryDate) return false;
        
        foreach ($allStates as $state => $entries) {
            foreach ($entries as $otherEntry) {
                $otherDate = $otherEntry['enter'] ?? null;
                if ($otherDate && $otherDate > $entryDate) {
                    return true;
                }
            }
        }
        return false;
    }
    
    private function isValidDate(?string $date): bool
    {
        if (!$date) return false;
        return (bool) strtotime($date);
    }
    
    private function extractStateSequence(array $stateTimestamps): array
    {
        $sequence = [];
        
        foreach ($stateTimestamps as $state => $entries) {
            foreach ($entries as $entry) {
                $sequence[] = [
                    'state' => $state,
                    'enter' => $entry['enter'] ?? null,
                    'exit' => $entry['exit'] ?? null,
                    'from_state' => $entry['from_state'] ?? null
                ];
            }
        }
        
        // Trier par date d'entrée
        usort($sequence, function($a, $b) {
            return strcmp($a['enter'] ?? '', $b['enter'] ?? '');
        });
        
        return $sequence;
    }
    
    private function isChronologicallyValid(array $stateSequence): bool
    {
        for ($i = 1; $i < count($stateSequence); $i++) {
            $prev = $stateSequence[$i - 1];
            $current = $stateSequence[$i];
            
            if (($prev['enter'] ?? '') > ($current['enter'] ?? '')) {
                return false;
            }
        }
        return true;
    }
    
    private function isWorkflowSequenceValid(array $stateSequence, array $workflowOrder): bool
    {
        // Vérification simplifiée - en réalité, le workflow peut avoir des branches parallèles
        $lastIndex = -1;
        
        foreach ($stateSequence as $entry) {
            $currentIndex = array_search($entry['state'], $workflowOrder);
            if ($currentIndex !== false) {
                if ($currentIndex < $lastIndex) {
                    // Retour en arrière détecté - peut être valide dans certains cas
                    // Pour l'instant, on considère cela comme suspect
                }
                $lastIndex = max($lastIndex, $currentIndex);
            }
        }
        
        return true; // Simplification pour l'analyse
    }
    
    private function getFromStateForEntry(array $stateTimestamps, string $state, ?string $enterDate): ?string
    {
        if (!isset($stateTimestamps[$state])) {
            return null;
        }
        
        foreach ($stateTimestamps[$state] as $entry) {
            if (($entry['enter'] ?? null) === $enterDate) {
                return $entry['from_state'] ?? null;
            }
        }
        
        return null;
    }
}

// Exécuter l'analyse
try {
    $analyzer = new StateTimestampsAnalyzer();
    $analyzer->analyzeAll();
} catch (Exception $e) {
    echo "Erreur lors de l'analyse: " . $e->getMessage() . "\n";
    echo "Veuillez vérifier la configuration de la base de données.\n";
}
