ok on va implementer :
1. Tableau de bord personnalisable 
2. Prévisions et tendances
3. Alertes et notifications

reste a faire: 


4. Comparaisons temporelles
5. Statistiques par utilisateur/département
6. Rapports exportables
9. Analyse des causes de retour



Idées de nouveaux widgets pour le tableau de bord

2. Activité récente
Un flux d'activité montrant les dernières actions effectuées sur les documents.

Qui a modifié quoi et quand
Changements d'état récents
Nouveaux commentaires ajoutés
Validations récentes

3. Mes tâches en attente
Un widget personnalisé montrant les documents qui attendent une action de l'utilisateur connecté.

Documents à valider
Documents à réviser
Commentaires à traiter
Priorisation automatique basée sur l'urgence

4. Performance par département
Un widget comparant les performances des différents départements.

Temps moyen de traitement par département
Nombre de documents traités par période
Taux de retour/rejet par département
Graphique comparatif




5. Indicateurs de qualité
Un widget affichant des métriques de qualité pour les documents.

Taux de rejet/retour
Nombre moyen de révisions par document
Conformité aux délais prévus
Score de qualité global avec tendance

6. Prévisions de charge de travail
Un widget prédisant la charge de travail à venir basée sur les tendances historiques.

Graphique de prévision pour les prochaines semaines
Comparaison avec la capacité disponible
Alertes en cas de surcharge prévue
Suggestions de répartition de charge

7. Recherche rapide
Un widget de recherche avancée permettant de trouver rapidement des documents.

Recherche par référence, titre, type, état
Filtres rapides prédéfinis (mes documents, documents récents, etc.)
Historique des recherches récentes
Suggestions intelligentes

8. Statistiques de validation
Un widget montrant des statistiques sur les validations de documents.

Temps moyen de validation par utilisateur/département
Taux d'approbation/rejet
Goulots d'étranglement dans le processus de validation
Tendances sur le temps

9. Carte thermique des délais
Une visualisation sous forme de carte thermique montrant les délais de traitement.

Par type de document
Par département
Par période de l'année
Identification des zones problématiques

10. Notifications personnalisées
Un widget permettant de configurer des alertes personnalisées.

Alertes pour certains types de documents
Notifications pour des étapes spécifiques du workflow
Rappels d'échéances personnalisés
Abonnements à des catégories de documents

13. Comparaison année par année
Un widget permettant de comparer les performances actuelles avec les années précédentes.

Graphique comparatif par mois/trimestre
Évolution des indicateurs clés
Identification des tendances saisonnières
Prévisions basées sur les données historiques

14. Suivi des objectifs
Un widget affichant les progrès vers les objectifs définis.

Objectifs de traitement de documents
Objectifs de qualité
Objectifs de délai
Représentation visuelle des progrès (jauges, barres de progression)











<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use App\Entity\Document;
use App\Entity\Visa;
use App\Entity\Commentaire;
use App\Entity\ReleasedPackage;
use App\Entity\Project;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Repository\ProjectRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\DBAL\Attribute\Connection as ConnectionName;

#[AsCommand(
    name: 'app:migrate-legacy',
    description: 'Migre les données de l’ancienne BD vers le nouveau schéma'
)]
class MigrateLegacyDataCommand extends Command
{
    protected static $defaultName = 'app:migrate-legacy';

    public function __construct(
        #[ConnectionName('legacy')]
        private Connection $oldDb,
        private EntityManagerInterface $em,
        private UserRepository $users,
        private ProjectRepository $projects
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('limit', null, InputOption::VALUE_OPTIONAL, 'Limiter le nombre d’enregistrements à migrer');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $limit = $input->getOption('limit');

        // Mapping pour associer les anciens IDs aux nouveaux packages
        $packageMapping = [];

        // 0. Nettoyer les tables existantes pour éviter les doublons
        $this->cleanExistingData($output);

        // 1. Créer les projets manquants
        $this->createMissingProjects($output);

        // 3. Migrer TOUS les ReleasedPackage d'abord (pas de limite pour éviter les références manquantes)
        $sql = 'SELECT * FROM tbl_released_package';
        $rows = $this->oldDb->fetchAllAssociative($sql);

        foreach ($rows as $r) {
            // on n’a plus le format prénom.nom, on garde juste le nom (avant l’espace)
            // Recherche flexible des utilisateurs
            $owner = $this->findUserFlexible($r['Rel_Pack_Owner']);
            $verif = $this->findUserFlexible($r['Verif_Req_Owner']);
            $valid = $this->findUserFlexible($r['BE_3_Req_Owner']);

            // Seul le owner est obligatoire, verif et valid peuvent être optionnels
            if (!$owner) {
                $output->writeln(sprintf(
                    '<error>Utilisateur introuvable pour package %s : owner=%s, verif=%s, valid=%s</error>',
                    $r['Rel_Pack_Num'], $r['Rel_Pack_Owner']
                ));
                continue;
            }

            // Log des utilisateurs manquants mais non bloquants
            if (!$verif && !empty($r['Verif_Req_Owner'])) {
                $output->writeln(sprintf(
                    '<comment>Verif introuvable pour package %s : verif=%s</comment>',
                    $r['Rel_Pack_Num'], $r['Verif_Req_Owner']
                ));
            }
            if (!$valid && !empty($r['BE_3_Req_Owner'])) {
                $output->writeln(sprintf(
                    '<comment>Valid introuvable pour package %s : valid=%s</comment>',
                    $r['Rel_Pack_Num'], $r['BE_3_Req_Owner']
                ));
            }

            $proj = $this->projects->findOneByOtp($r['Project']);
            if (!$proj) {
                $output->writeln(sprintf(
                    '<error>Project OTP introuvable : %s (package %s)</error>',
                    $r['Project'], $r['Rel_Pack_Num']
                ));
                continue;
            }

            // Créer l'entité ReleasedPackage avec l'ORM
            $package = new ReleasedPackage();
            $package->setOwner($owner);
            $package->setVerif($verif);
            $package->setValid($valid);
            $package->setProjectRelation($proj);
            $package->setDescription($r['Observations']);
            $package->setActivity($r['Activity']);
            $package->setEx($r['Ex']);

            // Conversion des dates si elles ne sont pas nulles et valides
            if (!empty($r['Reservation_Date']) && $this->isValidDate($r['Reservation_Date'])) {
                $package->setReservationDate(new \DateTime($r['Reservation_Date']));
            }
            if (!empty($r['Creation_Date']) && $this->isValidDate($r['Creation_Date'])) {
                $package->setCreationDate(new \DateTime($r['Creation_Date']));
            }

            $this->em->persist($package);

            // Stocker le mapping ancien ID -> nouveau package
            $packageMapping[$r['Rel_Pack_Num']] = $package;
        }

        // Flush des packages avant de migrer les documents
        $this->em->flush();
        $output->writeln('<info>Packages migrés avec succès.</info>');

        // 4. Migrer les Documents via ORM (appliquer la limite ici)
        $sql = 'SELECT * FROM tbl_released_drawing' . ($limit ? ' LIMIT ' . (int)$limit : '');
        $draws = $this->oldDb->fetchAllAssociative($sql);
        foreach ($draws as $d) {
            // Vérifier que le package existe dans notre mapping
            if (!isset($packageMapping[$d['Rel_Pack_Num']])) {
                $output->writeln(sprintf(
                    '<e>Package introuvable pour document %s (Rel_Pack_Num: %s)</e>',
                    $d['Reference'], $d['Rel_Pack_Num']
                ));
                continue;
            }

            $doc = new Document();
            // Utiliser le package du mapping au lieu de getReference
            $doc->setRelPack($packageMapping[$d['Rel_Pack_Num']]);

            $doc->setReference($d['Reference']);
            $doc->setRefRev($d['Ref_Rev']);
            $doc->setRefTitleFra($d['Ref_Title']);
            $doc->setProdDraw($d['Prod_Draw']);
            $doc->setProdDrawRev($d['Prod_Draw_Rev']);
            $doc->setAlias($d['Alias']);
            $doc->setDocType($d['Doc_Type']);
            $doc->setMaterialType($d['Material_Type']);
            $doc->setProcType($d['Proc_Type']);
            $doc->setInventoryImpact($d['Inventory_Impact']);
            $doc->setCustDrawing($d['Cust_Drawing']);
            $doc->setCustDrawingRev($d['Cust_Drawing_Rev']);
            $doc->setAction($d['Action']);
            $doc->setEx($d['Ex']);
            $doc->setWeight($d['Weight']);
            $doc->setWeightUnit($d['Weight_Unit']);
            $doc->setPlatingSurface($d['Plating_Surface']);
            $doc->setPlatingSurfaceUnit($d['Plating_Surface_Unit']);
            $doc->setInternalMachRec((bool)$d['Internal_Mach_Rec']);
            $doc->setCls($d['CLS']);
            $doc->setMoq($d['MOQ']);
            $doc->setProductCode($d['Product_Code']);
            $doc->setProdAgent($d['Prod_Agent']);
            $doc->setMof($d['MOF']);
            $doc->setCommodityCode($d['Commodity_Code']);
            $doc->setPurchasingGroup($d['Purchasing_Group']);
            $doc->setMatProdType($d['Mat_Prod_Type']);
            $doc->setUnit($d['Unit']);
            $doc->setLeadtime($d['leadtime']);
            $doc->setPrisDans1($d['Pris_Dans1']);
            $doc->setPrisDans2($d['Pris_Dans2']);
            $doc->setEccn($d['ECCN']);
            $doc->setRdo($d['RDO']);
            $doc->setHts($d['HTS']);
            $doc->setFia($d['FIA']);
            $doc->setMetroTime($d['Metro_Time']);
            // MetroControl doit être un array, pas une string
            if (!empty($d['Metro_Control'])) {
                // Si c'est une string, on la convertit en array
                $metroControlArray = is_string($d['Metro_Control']) ? [$d['Metro_Control']] : $d['Metro_Control'];
                $doc->setMetroControl($metroControlArray);
            }
            // QInspection doit être un array
            if (!empty($d['Q_Inspection'])) {
                $qInspectionArray = is_string($d['Q_Inspection']) ? [$d['Q_Inspection']] : $d['Q_Inspection'];
                $doc->setQInspection($qInspectionArray);
            }
            $doc->setQDynamization($d['Q_Dynamization']);
            // QDocRec doit être un array
            if (!empty($d['Q_Doc_Req'])) {
                $qDocRecArray = is_string($d['Q_Doc_Req']) ? [$d['Q_Doc_Req']] : $d['Q_Doc_Req'];
                $doc->setQDocRec($qDocRecArray);
            }
            $doc->setQControlRouting($d['Q_Control_Routing']);
            $doc->setCriticalComplete($d['Critical_Complete']);
            $doc->setSwitchAletiq((bool)$d['SWITCH_ALETIQ']);
            $doc->setIdAletiq($d['ID_ALETIQ']);
            $doc->setMaterial($d['Material']);
            $doc->setDocImpact((int)$d['Doc_Impact']);

            $this->em->persist($doc);

            // Commentaires
            if (!empty($d['Requestor_Comments'])) {
                $c = new Commentaire();
                $c->setUser($doc->getSuperviseur());
                $c->setDocument($doc);
                $c->setType('request');
                $c->setCommentaire($d['Requestor_Comments']);
                $this->em->persist($c);
            }

            // Visas
            foreach ([
                'Project','Inventory','Product','Quality','Method',
                'Finance','Prod','Supply','PUR_1','...'
            ] as $visaKey) {
                $visaCol = "VISA_{$visaKey}";
                $dateCol = "DATE_{$visaKey}";
                if (!empty($d[$visaCol]) && $this->isValidDate($d[$dateCol])) {
                    $v = new Visa();
                    $v->setName('visa_' . strtolower($visaKey));
                    $v->setDate(new \DateTime($d[$dateCol]));
                    $v->setStatus('valid');
                    $v->setReleasedDrawing($doc);
                    $this->em->persist($v);
                }
            }
        }

        $this->em->flush();
        $output->writeln('<info>Migration terminée.</info>');
        return Command::SUCCESS;
    }

    private function createMissingProjects(OutputInterface $output): void
    {
        // Récupérer tous les codes de projets uniques de l'ancienne base
        $projectCodes = $this->oldDb->fetchAllAssociative('SELECT DISTINCT Project FROM tbl_released_package WHERE Project IS NOT NULL');

        $createdCount = 0;
        foreach ($projectCodes as $row) {
            $otpCode = $row['Project'];

            // Vérifier si le projet existe déjà
            $existingProject = $this->projects->findOneByOtp($otpCode);
            if (!$existingProject) {
                // Créer le projet manquant
                $project = new Project();
                $project->setOTP($otpCode);
                $project->setTitle("Projet migré: " . $otpCode);
                $project->setStatus('active');

                $this->em->persist($project);
                $createdCount++;

                $output->writeln(sprintf('<info>Projet créé: %s</info>', $otpCode));
            }
        }

        if ($createdCount > 0) {
            $this->em->flush();
            $output->writeln(sprintf('<info>%d projets créés.</info>', $createdCount));
        } else {
            $output->writeln('<info>Aucun projet à créer.</info>');
        }
    }

    private function isValidDate(?string $date): bool
    {
        if (empty($date)) {
            return false;
        }

        // Vérifier si la date contient des années négatives ou invalides
        if (strpos($date, '-0001') !== false || strpos($date, '0000') !== false) {
            return false;
        }

        try {
            $dateTime = new \DateTime($date);
            // Vérifier que l'année est raisonnable (après 1900)
            return $dateTime->format('Y') >= 1900;
        } catch (\Exception $e) {
            return false;
        }
    }

    private function extractLastName(?string $fullName): ?string
    {
        if (empty($fullName)) {
            return null;
        }

        $fullName = trim($fullName);

        // Extraire le nom de famille (avant le premier espace)
        // Ex: "PISSOT T." -> "PISSOT", "GALIPAUD JF." -> "GALIPAUD"
        $parts = explode(' ', $fullName);
        return !empty($parts[0]) ? $parts[0] : null;
    }

    private function cleanExistingData(OutputInterface $output): void
    {
        $output->writeln('<info>Nettoyage des données existantes...</info>');

        // Supprimer dans l'ordre inverse des dépendances
        // 1. Supprimer les visas (dépendent des documents)
        $this->em->createQuery('DELETE FROM App\Entity\Visa')->execute();
        $output->writeln('<comment>Visas supprimés.</comment>');

        // 2. Supprimer les commentaires (dépendent des documents)
        $this->em->createQuery('DELETE FROM App\Entity\Commentaire c WHERE c.documents IS NOT NULL')->execute();
        $output->writeln('<comment>Commentaires de documents supprimés.</comment>');

        // 3. Supprimer les documents (dépendent des packages)
        $this->em->createQuery('DELETE FROM App\Entity\Document')->execute();
        $output->writeln('<comment>Documents supprimés.</comment>');

        // 4. Supprimer les packages (dépendent des projets et utilisateurs)
        $this->em->createQuery('DELETE FROM App\Entity\ReleasedPackage')->execute();
        $output->writeln('<comment>Packages supprimés.</comment>');

        // 5. Supprimer les projets créés par migration (garder ceux créés manuellement)
        $this->em->createQuery('DELETE FROM App\Entity\Project WHERE title LIKE :pattern')
            ->setParameter('pattern', 'Projet migré:%')
            ->execute();
        $output->writeln('<comment>Projets migrés supprimés.</comment>');

        $this->em->flush();
        $output->writeln('<info>Nettoyage terminé.</info>');
    }

private function findUserFlexible(?string $raw, OutputInterface $output): ?User
{
    $output->writeln("<info>→ Recherche user pour « $raw »</info>");

    if (empty($raw)) {
        $output->writeln("  (raw vide)");
        return null;
    }

    // 1) Normalisation
    $clean = $this->normalizeName($raw);
    $output->writeln("  Normalisé ➜ « $clean »");

    $parts    = preg_split('/\s+/', $clean);
    $lastName = $parts[0] ?? '';
    $firstPart = $parts[1] ?? '';
    $output->writeln("  Nom extrait = « $lastName », initiales/prénom = « $firstPart »");

    // 2) Exact match
    $allUsers = $this->users->findAll();
    $output->writeln("  Total users en BDD: ".count($allUsers));

    $matches = [];
    foreach ($allUsers as $u) {
        if ($this->normalizeName($u->getNom()) === $lastName) {
            $matches[] = $u;
        }
    }
    $output->writeln("  Exact match nom: ".count($matches)." résultat(s)");

    if (count($matches) === 1) {
        $output->writeln("    → on retourne " . $matches[0]->getNom() . " " . $matches[0]->getPrenom());
        return $matches[0];
    }

    // 3) Initiales prénom
    if ($firstPart && count($matches) > 1) {
        $output->writeln("  Tentative correspondance initiales prénom…");
        foreach ($matches as $u) {
            $prenomNorm = $this->normalizeName($u->getPrenom() ?? '');
            $initials = '';
            foreach (preg_split('/\s+/', $prenomNorm) as $p) {
                $initials .= mb_substr($p, 0, 1, 'UTF-8');
            }
            if ($initials === $firstPart) {
                $output->writeln("    → matched by initials: " . $u->getNom() . " " . $u->getPrenom());
                return $u;
            }
        }
    }

    // 4) Inclusion partielle
    $output->writeln("  Recherche inclusion partielle du nom…");
    foreach ($allUsers as $u) {
        if (mb_stripos($this->normalizeName($u->getNom()), $lastName, 0, 'UTF-8') !== false) {
            $output->writeln("    → matched by contains: " . $u->getNom() . " " . $u->getPrenom());
            return $u;
        }
    }

    // 5) Fallback fuzzy (Levenshtein)
    $output->writeln("  Fallback fuzzy (Levenshtein)…");
    $closest = null;
    $minDist = PHP_INT_MAX;
    foreach ($allUsers as $u) {
        $dist = levenshtein($lastName, $this->normalizeName($u->getNom()));
        if ($dist < $minDist) {
            $minDist = $dist;
            $closest = $u;
        }
    }
    $output->writeln("    Distance min = $minDist pour ". $closest->getNom());
    if ($closest && $minDist <= 2) {
        $output->writeln("    → on accepte fuzzy: " . $closest->getNom() . " " . $closest->getPrenom());
        return $closest;
    }

    $output->writeln("    Pas de match");
    return null;
}

    private function normalizeName(string $s): string
{
    // 1) trim + retirer tout ce qui n'est pas lettre/chiffre/espace
    $s = trim($s);
    $s = preg_replace('/[^\p{L}\p{N}\s]/u', '', $s);

    // 2) décomposer les accents (NFD) et supprimer les marques
    if (class_exists(\Normalizer::class)) {
        $s = \Normalizer::normalize($s, \Normalizer::FORM_D);
        $s = preg_replace('/\p{M}/u', '', $s);
    }

    // 3) retourner en majuscules
    return mb_strtoupper($s, 'UTF-8');
}


}
