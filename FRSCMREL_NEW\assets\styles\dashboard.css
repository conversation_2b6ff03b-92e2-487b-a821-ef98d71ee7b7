/* Styles pour le tableau de bord */

/* Styles généraux */
.dashboard-container {
    padding: 20px;
}

.card {
    border-radius: 10px;
    overflow: hidden;
    transition: transform 0.2s, box-shadow 0.2s;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-bottom: none;
}

.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Couleurs */
.bg-primary {
    background-color: #4e73df;
}

.bg-success {
    background-color: #1cc88a;
}

.bg-info {
    background-color: #36b9cc;
}

.bg-warning {
    background-color: #f6c23e;
}

.bg-danger {
    background-color: #e74a3b;
}

.bg-purple {
    background-color: #7952b3;
}

.text-primary {
    color: #4e73df;
}

.text-success {
    color: #1cc88a;
}

.text-info {
    color: #36b9cc;
}

.text-warning {
    color: #f6c23e;
}

.text-danger {
    color: #e74a3b;
}

.text-purple {
    color: #7952b3;
}

/* Widget d'activité récente */
.activity-list {
    max-height: 500px;
    overflow-y: auto;
}

.activity-list .list-group-item {
    transition: background-color 0.2s;
}

.activity-list .list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* Widget de tâches en attente */
.progress-circle {
    position: relative;
    display: inline-block;
}

.nav-tabs .nav-link {
    border: none;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1rem;
}

.nav-tabs .nav-link.active {
    color: #4e73df;
    border-bottom: 2px solid #4e73df;
    background-color: transparent;
}

.tab-content {
    padding-top: 1rem;
}

/* Widget de performance par département */
.btn-group .btn-outline-primary {
    border-color: #e0e0e0;
    color: #6c757d;
}

.btn-group .btn-outline-primary.active {
    background-color: #4e73df;
    border-color: #4e73df;
    color: white;
}

/* Responsive */
@media (max-width: 768px) {
    .card {
        margin-bottom: 20px;
    }
    
    .icon-circle {
        width: 30px;
        height: 30px;
    }
}
