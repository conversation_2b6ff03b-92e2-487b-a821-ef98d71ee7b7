# Service EmailService - Documentation

## Vue d'ensemble

Le service `EmailService` gère l'envoi automatique d'emails pour les notifications liées aux DMO (Demandes de Modification). Il utilise PHPMailer pour l'envoi des emails et Twig pour le rendu des templates.

## Configuration

### 1. Configuration SMTP

Le service est configuré pour utiliser le serveur SMTP Office365 :
- **Host :** smtp.office365.com
- **Port :** 587
- **Sécurité :** STARTTLS
- **Authentification :** Activée

### 2. Configuration dans services.yaml

```yaml
App\Service\EmailService:
    arguments:
        $fromEmail: '<EMAIL>'
        $fromName: 'Système DMO - SCM Le Mans'
```

### 3. Variables d'environnement

Pour utiliser un autre serveur SMTP en production, modifiez la variable `MAILER_DSN` dans le fichier `.env` :

```env
# Pour Office365
MAILER_DSN=smtp://<EMAIL>:<EMAIL>:587

# Pour un serveur local (développement)
MAILER_DSN=smtp://localhost:1025
```

## Fonctionnalités

### 1. Envoi d'emails de décision

La méthode `sendDecisionEmail()` envoie un email lors du changement de décision d'une DMO :

```php
$emailService->sendDecisionEmail($dmo, $oldDecision);
```

**Types de décisions supportées :**
- `CREATED` : Accusé de création
- `UNDER REVIEW` : DMO en cours de revue
- `ACCEPTED` : DMO acceptée
- `REJECTED` : DMO refusée

### 2. Envoi d'emails de clôture

La méthode `sendClosureEmail()` envoie un email lors de la clôture d'une DMO :

```php
$emailService->sendClosureEmail($dmo, $oldStatus);
```

## Templates d'emails

Les templates sont situés dans `templates/emails/dmo/` :

- `created.html.twig` : Email de création
- `under_review.html.twig` : Email de mise en revue
- `accepted.html.twig` : Email d'acceptation
- `rejected.html.twig` : Email de refus
- `closed.html.twig` : Email de clôture

### Structure des templates

Chaque template reçoit les variables suivantes :
- `dmo` : L'objet DMO complet
- `decision` : La décision actuelle
- `link` : Lien vers la DMO
- `reason` : Motif du refus (uniquement pour les rejets)

### Template de base

Un template de base `base_email.html.twig` est disponible pour maintenir une cohérence visuelle.

## Utilisation dans les contrôleurs

### Exemple d'intégration

```php
use App\Service\EmailService;

class DMOController extends AbstractController
{
    public function updateDecision(
        DMO $dmo,
        string $newDecision,
        EmailService $emailService
    ): Response {
        $oldDecision = $dmo->getDecision();
        $dmo->setDecision($newDecision);
        
        // Sauvegarder en base
        $this->entityManager->flush();
        
        // Envoyer l'email de notification
        $emailService->sendDecisionEmail($dmo, $oldDecision);
        
        return $this->json(['status' => 'success']);
    }
}
```

## Tests

### Contrôleur de test

Un contrôleur de test `EmailTestController` est disponible pour tester l'envoi d'emails :

- `/email-test/dmo/{id}` : Test d'email de décision
- `/email-test/closure/{id}` : Test d'email de clôture

### Utilisation

1. Accédez à l'URL de test avec l'ID d'une DMO existante
2. Vérifiez les logs pour confirmer l'envoi
3. Consultez la boîte email du demandeur

## Gestion des erreurs

Le service gère automatiquement les erreurs :
- Vérification de l'existence du demandeur et de son email
- Logging des erreurs et succès
- Gestion des exceptions PHPMailer

### Logs

Les logs sont enregistrés avec les niveaux suivants :
- `INFO` : Email envoyé avec succès
- `WARNING` : Demandeur ou email manquant
- `ERROR` : Erreur lors de l'envoi

## Personnalisation

### Modifier les templates

1. Éditez les fichiers dans `templates/emails/dmo/`
2. Utilisez les variables Twig disponibles
3. Respectez la structure HTML responsive

### Ajouter de nouveaux types d'emails

1. Ajoutez une constante dans `EmailService`
2. Créez le template correspondant
3. Ajoutez la logique dans la méthode appropriée

### Changer la configuration SMTP

Modifiez la méthode `createMailer()` dans `EmailService.php` :

```php
private function createMailer(): PHPMailer
{
    $mail = new PHPMailer(true);
    $mail->isSMTP();
    $mail->Host = 'votre-serveur-smtp.com';
    $mail->Port = 587;
    // ... autres configurations
    return $mail;
}
```

## Sécurité

- Les mots de passe SMTP sont stockés en dur (à améliorer en production)
- Validation des adresses email avant envoi
- Échappement automatique des variables Twig

## Améliorations futures

1. **Configuration par variables d'environnement** : Externaliser la configuration SMTP
2. **Queue d'emails** : Utiliser Symfony Messenger pour les envois asynchrones
3. **Templates personnalisables** : Interface d'administration pour modifier les templates
4. **Statistiques d'envoi** : Tracking des emails envoyés et ouverts
5. **Support multi-langues** : Templates en plusieurs langues
