# Migration du champ Material vers une entité relationnelle

## Vue d'ensemble

Cette migration transforme le champ `material` de type `string` dans l'entité `Document` en une relation `ManyToOne` vers une nouvelle entité `Material`. Cette approche permet une meilleure normalisation des données et une gestion plus efficace des matériaux.

## Changements apportés

### 1. Nouvelle entité Material

**Fichier:** `src/Entity/Material.php`

L'entité Material contient les champs suivants :
- `id` : Identifiant unique
- `reference` : Référence du matériau (ex: FMME 1001)
- `description` : Description du matériau
- `thicknessMin/Max` : Épaisseur minimale/maximale
- `thicknessUnit` : Unité d'épaisseur
- `density` : Densité
- `status` : Statut (ACTIVE/INACTIVE)
- `rohs` : Information RoHS
- `reach` : Information REACH
- `legacyId` : ID de l'ancienne base pour traçabilité

### 2. Repository Material

**Fichier:** `src/Repository/MaterialRepository.php`

Méthodes disponibles :
- `findByReference(string $reference)` : Trouve un matériau par sa référence
- `findActive()` : Trouve tous les matériaux actifs
- `search(string $term)` : Recherche par référence ou description
- `findByLegacyId(int $legacyId)` : Trouve par ancien ID (migration)

### 3. Modification de l'entité Document

**Changements dans:** `src/Entity/Document.php`

- Suppression du champ `material` (string)
- Ajout de la relation `materialEntity` (ManyToOne vers Material)
- Méthodes de compatibilité maintenues :
  - `getMaterial()` : Retourne la référence du matériau
  - `setMaterial()` : Méthode vide pour compatibilité
- Mise à jour de `toJson()` pour inclure les données du matériau

### 4. Configuration de base de données

**Nouvelle connexion:** `legacy_scm`
- Host: ************
- Database: db_scm
- Utilisée pour importer les données de `tbl_fxxx`

### 5. Commande de migration

**Fichier:** `src/Command/MigrateMaterialsCommand.php`

**Usage:**
```bash
# Test en mode dry-run
php bin/console app:migrate-materials --dry-run

# Migration réelle
php bin/console app:migrate-materials

# Force la réimportation
php bin/console app:migrate-materials --force
```

**Fonctionnalités:**
- Import de tous les matériaux depuis `db_scm.tbl_fxxx`
- Mise à jour automatique des relations dans les documents existants
- Gestion des matériaux manquants
- Mode dry-run pour tester sans modifier

## Migrations de base de données

### Migration 1: Version20250616080354
- Création de la table `material`
- Ajout de la colonne `material_entity_id` dans `document`
- Création de la contrainte de clé étrangère

### Migration 2: Version20250616081500
- Suppression de l'ancienne colonne `material` (string)

## Données migrées

- **640 matériaux** importés depuis `db_scm.tbl_fxxx`
- **1 document** avec matériau lié (FMTP909)
- **1 matériau manquant** créé automatiquement (FMTP909)

## Tests

**Commande de test:** `php bin/console app:test-material-relation`

Tests effectués :
1. Liste des matériaux
2. Récupération d'un document avec matériau
3. Sérialisation JSON
4. Recherche de matériaux
5. Comptage des matériaux actifs
6. Test des relations bidirectionnelles

## Compatibilité

### Code existant
Le code existant utilisant `$document->getMaterial()` continue de fonctionner grâce à la méthode de compatibilité qui retourne la référence du matériau.

### API JSON
La structure JSON a été mise à jour :
```json
{
  "material": {
    "id": 641,
    "reference": "FMTP909",
    "description": "Matériau FMTP909 (créé automatiquement)",
    "status": "ACTIVE"
  }
}
```

## Avantages

1. **Normalisation** : Élimination de la duplication des données matériaux
2. **Intégrité** : Contraintes de clé étrangère garantissent la cohérence
3. **Recherche** : Recherche avancée par référence, description, statut
4. **Extensibilité** : Facilite l'ajout de nouvelles propriétés matériaux
5. **Performance** : Requêtes optimisées avec jointures

## Utilisation

### Créer un document avec matériau
```php
$material = $materialRepository->findByReference('FMME 1001');
$document = new Document();
$document->setMaterialEntity($material);
```

### Rechercher des matériaux
```php
// Par référence exacte
$material = $materialRepository->findByReference('FMME 1001');

// Recherche textuelle
$materials = $materialRepository->search('Laiton');

// Matériaux actifs seulement
$activeMaterials = $materialRepository->findActive();
```

### Accéder au matériau d'un document
```php
// Nouvelle méthode (recommandée)
$materialEntity = $document->getMaterialEntity();
if ($materialEntity) {
    echo $materialEntity->getReference();
    echo $materialEntity->getDescription();
}

// Méthode de compatibilité
$materialRef = $document->getMaterial(); // Retourne la référence
```
