# Implémentation de la logique de réinitialisation lors du changement de doctype

## Vue d'ensemble

Cette implémentation reproduit fidèlement la logique de l'ancien système pour réinitialiser les champs d'un document lorsque son type (doctype) est modifié. Elle utilise les vraies places du workflow actuel et gère les changements de places + création/suppression des visas appropriés lors du changement de doctype.

## Fichiers créés/modifiés

### 1. Service principal : `src/Service/DoctypeResetService.php`

Ce service contient toute la logique de réinitialisation :

- **Méthode principale** : `resetDocumentForDoctypeChange()`
- **Modules gérés** : Quality, Production, Purchasing, Product
- **Logique spéciale** : Gestion du doctype "DOC" avec Material Type automatique
- **Traçabilité** : Ajout de commentaires d'historique pour chaque changement

### 2. Contrôleur modifié : `src/Controller/DocumentController.php`

- Intégration du service dans la méthode `updateDocument()`
- Appel du service pour tous les changements de doctype (y compris vers "DOC")
- Maintien de la logique de workflow existante

### 3. Configuration : `config/services.yaml`

- Enregistrement du service `app.doctype_reset_service`
- Configuration publique pour les tests
- Injection des dépendances (EntityManager, Logger)

## Logique de réinitialisation par place du workflow

### Place Quality
**Champs réinitialisés :**
- `procType` → `null`
- `metroTime` → `null`
- `metroControl` → `[]`

**Visas supprimés :**
- `visa_Quality`
- `visa_prod`
- `visa_Metro`

### Places Production (Assembly, Machining, Molding)
**Champs réinitialisés :**
- `procType` → `null`
- `leadtime` → `0`
- `mof` → `null`
- `prodAgent` → `null`
- `prisDans1` → `null`
- `prisDans2` → `null`

**Visas supprimés :**
- `visa_prod` (commun aux trois places de production)

### Places Purchasing (Achat_Rfq, Achat_F30, Achat_FIA, Achat_Hts, Achat_RoHs_REACH)
**Champs réinitialisés :**
- `commodityCode` → `null`
- `purchasingGroup` → `null`
- `procType` → `null`
- `prisDans1` → `null`
- `prisDans2` → `null`
- `fia` → `null`
- `metroTime` → `null`
- `metroControl` → `[]`

**Visas supprimés :**
- `visa_Achat_Rfq`
- `visa_Achat_F30`
- `visa_Achat_FIA`
- `visa_Achat_RoHs_REACH`
- `visa_Achat_Hts`

### Place Produit
**Visas supprimés :**
- `visa_Produit`

### Place Metro
**Champs réinitialisés :**
- `metroTime` → `null`
- `metroControl` → `[]`

**Visas supprimés :**
- `visa_Metro`

## Logique spéciale pour le doctype "DOC"

### Changement vers DOC
- `matProdType` → `'ROH'` (équivalent à "LITERATURE")
- Commentaire : "Document type changed to DOC: Material Type set to LITERATURE"

### Changement depuis DOC
- `matProdType` → `null` (si était sur "ROH")
- Commentaire : "Document type changed from DOC: Material Type reset"

## Changements de workflow et places

### Mapping doctype → places de destination
- **MACH** → `Machining`
- **MOLD** → `Molding`
- **ASSY** → `Assembly`
- **PUR** → `Quality` (puis workflow normal vers achat)
- **DOC** → `Quality`

### Logique de changement de places
1. **Suppression des visas** des anciennes places liées au doctype
2. **Changement des places actives** selon le nouveau doctype
3. **Préservation des places** non liées au doctype (BE_0, BE_1, BE, Produit, Project, Core_Data, GID, Costing)
4. **Création automatique** des nouvelles places de destination

### Mapping places → visas
- `Assembly`, `Machining`, `Molding` → `visa_prod`
- `Quality` → `visa_Quality`
- `Achat_Rfq` → `visa_Achat_Rfq`
- `Achat_F30` → `visa_Achat_F30`
- `Achat_FIA` → `visa_Achat_FIA`
- `Achat_Hts` → `visa_Achat_Hts`
- `Achat_RoHs_REACH` → `visa_Achat_RoHs_REACH`
- `Metro` → `visa_Metro`
- `Produit` → `visa_Produit`

## Correspondances ancien/nouveau système

| Ancien système | Nouveau système | Description |
|----------------|-----------------|-------------|
| `Proc_Type` | `procType` | Type de processus |
| `leadtime` | `leadtime` | Délai de livraison |
| `mof` | `mof` | MOF |
| `Prod_Agent` | `prodAgent` | Agent de production |
| `Pris_Dans1` | `prisDans1` | Pris dans 1 |
| `Pris_Dans2` | `prisDans2` | Pris dans 2 |
| `Commodity_Code` | `commodityCode` | Code marchandise |
| `Purchasing_Group` | `purchasingGroup` | Groupe d'achat |
| `FIA` | `fia` | FIA |
| `Metro_Time` | `metroTime` | Temps métrologie |
| `Metro_Control` | `metroControl` | Contrôle métrologie |
| `Material_Type` | `matProdType` | Type de matériau |
| `VISA_*` | Entités `Visa` | Visas de validation |

## Traçabilité

Chaque changement de doctype génère :

1. **Commentaires d'historique** par module affecté :
   - Format : `[YYYY-MM-DD HH:MM:SS] - [Module] : change of supply to [nouveau_doctype]`
   - Exemple : `[2025-06-18 08:13:47] - Quality : change of supply to MACH`

2. **Entrées dans l'historique des mises à jour** :
   - Type : `doctype_change`
   - Utilisateur : Utilisateur qui effectue le changement
   - Détails : Description du changement

## Places affectées selon le changement

Le système détermine automatiquement quelles places du workflow sont affectées :

- **Toujours affectées** : `Quality`, `Produit`
- **Si ancien doctype = PUR** : Places d'achat (`Achat_Rfq`, `Achat_F30`, `Achat_FIA`, `Achat_Hts`, `Achat_RoHs_REACH`)
- **Si ancien doctype ∈ {MACH, MOLD, ASSY}** : Places de production correspondantes (`Machining`, `Molding`, `Assembly`)
- **Si nouveau doctype = PUR** : Places d'achat
- **Si nouveau doctype ∈ {MACH, MOLD, ASSY}** : Places de production correspondantes
- **Places actives** : Le système vérifie les places actuellement actives dans le document pour déterminer les places d'achat à traiter

## Utilisation

Le service est automatiquement appelé lors de la modification du champ `doctype` via l'interface utilisateur. Aucune action supplémentaire n'est requise de la part de l'utilisateur.

## Tests

Le service a été testé avec succès pour tous les scénarios :
- PUR → MACH : Réinitialisation complète des champs d'achat et production
- MACH → DOC : Définition automatique du Material Type sur LITERATURE
- DOC → ASSY : Réinitialisation du Material Type et application des règles de production

## Compatibilité

Cette implémentation est entièrement compatible avec :
- Le système de workflow existant
- La logique de validation des visas
- L'historique des documents
- L'interface utilisateur actuelle
