# Résumé de l'implémentation - Nettoyage des données lors des transitions de doctype

## Vue d'ensemble

L'implémentation a été mise à jour pour prendre en compte les **places d'origine** du document dans le workflow, conformément aux spécifications détaillées fournies.

## Règles implémentées

### Transitions vers PUR

| Place d'origine | Transition | Champs nettoyés |
|----------------|------------|-----------------|
| **Machining** | MACH → PUR | matProdType, unit, leadtime, procType, prisDans1, prisDans2, **MOF** |
| **Molding** | MOLD → PUR | matProdType, unit, leadtime, procType, prisDans1, prisDans2, **MOF** |
| **Assembly** | ASSY → PUR | matProdType, unit, leadtime, procType *(sans MOF, sans pris dans)* |

### Transitions depuis PUR

| Place d'origine | Transition | Champs nettoyés |
|----------------|------------|-----------------|
| **Achat_F30** | PUR → MACH/MOLD/ASSY | matProdType, unit, commodityCode, purchasingGroup, procType |
| **Achat_Rfq** | PUR → MACH/MOLD/ASSY | matProdType, unit, commodityCode, purchasingGroup, procType |

## Architecture technique

### Méthodes principales

1. **`cleanDataForDoctypeTransition()`**
   - Point d'entrée principal
   - Détermine la place actuelle du document
   - Délègue vers les méthodes spécialisées

2. **`getCurrentWorkflowPlace()`**
   - Analyse les `currentSteps` du document
   - Priorise les places de production et d'achat
   - Retourne la place principale actuelle

3. **`handleTransitionToPur()`**
   - Gère les transitions vers PUR selon la place d'origine
   - Appelle les méthodes de nettoyage appropriées

4. **`handleTransitionFromPur()`**
   - Gère les transitions depuis PUR selon la place d'origine
   - Nettoie les champs RFQ si nécessaire

### Méthodes de nettoyage spécialisées

- **`cleanProductionFieldsAssembly()`** : Nettoyage partiel pour Assembly
- **`cleanProductionFieldsComplete()`** : Nettoyage complet pour Machining/Molding
- **`cleanRFQFields()`** : Nettoyage des champs d'achat

## Différences clés avec l'implémentation précédente

### Avant
- Nettoyage basé uniquement sur la transition de doctype
- Règles uniformes pour tous les types de production

### Maintenant
- **Nettoyage basé sur la place d'origine ET la transition**
- Règles différenciées selon la place actuelle :
  - Assembly : nettoyage partiel (sans MOF, sans pris dans)
  - Machining/Molding : nettoyage complet (avec MOF et pris dans)
  - Achat_F30/Achat_Rfq : nettoyage des champs RFQ

## Tests

### Tests unitaires (5 tests, 40 assertions)
- ✅ Nettoyage complet depuis Machining
- ✅ Nettoyage partiel depuis Assembly
- ✅ Nettoyage RFQ depuis Achat_Rfq
- ✅ Détection correcte des places d'origine
- ✅ Pas de nettoyage pour champs vides

### Tests manuels
- ✅ Simulation complète des 4 scénarios principaux
- ✅ Vérification des spécificités Assembly vs Machining/Molding

## Historique des modifications

Chaque nettoyage automatique génère une entrée d'historique avec :
- Type : "edit"
- Détails : "Nettoyage automatique lors du changement de type (depuis [PLACE]): [CHAMPS]"
- Utilisateur et timestamp

## Intégration

L'implémentation s'intègre parfaitement dans le flux existant :
1. Détection du changement de doctype
2. Service de réinitialisation existant
3. **Nouveau** : Nettoyage spécifique selon place d'origine
4. Mise à jour du workflow
5. Persistance en base

## Points d'attention

- La détection de place priorise les places de production/achat
- Les champs ne sont nettoyés que s'ils ont des valeurs non-nulles
- L'historique est automatiquement mis à jour avec la place d'origine
- Aucun impact sur les performances (tests validés)

## Prochaines étapes

1. Tests en environnement de développement
2. Validation avec des données réelles
3. Tests d'intégration avec l'interface utilisateur
4. Déploiement en production après validation
