<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250630083247 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE code (id INT AUTO_INCREMENT NOT NULL, phase_id INT NOT NULL, title VARCHAR(255) NOT NULL, code VARCHAR(255) NOT NULL, level INT DEFAULT NULL, status VARCHAR(255) DEFAULT NULL, work_center VARCHAR(255) DEFAULT NULL, INDEX IDX_7715309899091188 (phase_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE commentaire (id INT AUTO_INCREMENT NOT NULL, user_id INT DEFAULT NULL, documents_id INT DEFAULT NULL, dmo_id_id INT DEFAULT NULL, state VARCHAR(255) DEFAULT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', commentaire LONGTEXT NOT NULL, type VARCHAR(255) NOT NULL, INDEX IDX_67F068BCA76ED395 (user_id), INDEX IDX_67F068BC5F0F2752 (documents_id), INDEX IDX_67F068BC95289E08 (dmo_id_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE config (id INT AUTO_INCREMENT NOT NULL, periode DATE NOT NULL, date_deb DATE NOT NULL, date_fin DATE NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE dmo (id INT AUTO_INCREMENT NOT NULL, requestor_id INT DEFAULT NULL, eng_owner_id INT DEFAULT NULL, last_modificator_id INT DEFAULT NULL, product_range_id INT DEFAULT NULL, project_relation_id INT DEFAULT NULL, date_init DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', description LONGTEXT DEFAULT NULL, decision VARCHAR(255) NOT NULL, status TINYINT(1) NOT NULL, ex VARCHAR(255) DEFAULT NULL, indus_related TINYINT(1) DEFAULT NULL, date_end DATETIME DEFAULT NULL, pr_number INT DEFAULT NULL, last_update_date DATETIME NOT NULL, ex_assessment LONGTEXT DEFAULT NULL, spent_time INT NOT NULL, type VARCHAR(255) NOT NULL, document VARCHAR(255) DEFAULT NULL, legacy_id INT DEFAULT NULL, dmo VARCHAR(255) DEFAULT NULL, INDEX IDX_BDC1D3CDA7F43455 (requestor_id), INDEX IDX_BDC1D3CD4C484F77 (eng_owner_id), INDEX IDX_BDC1D3CD21AE072F (last_modificator_id), INDEX IDX_BDC1D3CDE132BA26 (product_range_id), INDEX IDX_BDC1D3CDFA9966A0 (project_relation_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document (id INT AUTO_INCREMENT NOT NULL, rel_pack_id INT DEFAULT NULL, product_code_id INT DEFAULT NULL, superviseur_id INT DEFAULT NULL, qual_owner_id INT DEFAULT NULL, reference VARCHAR(255) NOT NULL, ref_rev VARCHAR(255) NOT NULL, ref_title_fra VARCHAR(255) DEFAULT NULL, prod_draw VARCHAR(255) DEFAULT NULL, prod_draw_rev VARCHAR(255) DEFAULT NULL, alias VARCHAR(255) DEFAULT NULL, doc_type VARCHAR(255) DEFAULT NULL, proc_type VARCHAR(255) DEFAULT NULL, inventory_impact VARCHAR(255) DEFAULT NULL, current_steps JSON NOT NULL, id_aletiq INT DEFAULT NULL, cust_drawing VARCHAR(255) DEFAULT NULL, cust_drawing_rev VARCHAR(255) DEFAULT NULL, action VARCHAR(255) DEFAULT NULL, ex VARCHAR(255) DEFAULT NULL, weight INT DEFAULT NULL, weight_unit VARCHAR(255) DEFAULT NULL, plating_surface INT DEFAULT NULL, plating_surface_unit VARCHAR(255) DEFAULT NULL, internal_mach_rec TINYINT(1) DEFAULT NULL, cls INT DEFAULT NULL, moq INT DEFAULT NULL, prod_agent VARCHAR(255) DEFAULT NULL, mof VARCHAR(255) DEFAULT NULL, commodity_code VARCHAR(255) DEFAULT NULL, purchasing_group VARCHAR(255) DEFAULT NULL, mat_prod_type VARCHAR(255) DEFAULT NULL, unit VARCHAR(255) DEFAULT NULL, leadtime INT DEFAULT NULL, pris_dans1 VARCHAR(255) DEFAULT NULL, pris_dans2 VARCHAR(255) DEFAULT NULL, eccn VARCHAR(255) DEFAULT NULL, rdo VARCHAR(255) DEFAULT NULL, hts VARCHAR(255) DEFAULT NULL, fia VARCHAR(255) DEFAULT NULL, metro_time INT DEFAULT NULL, q_inspection JSON DEFAULT NULL, q_dynamization VARCHAR(255) DEFAULT NULL, q_doc_rec JSON DEFAULT NULL, q_control_routing VARCHAR(255) DEFAULT NULL, critical_complete INT DEFAULT NULL, switch_aletiq TINYINT(1) DEFAULT NULL, metro_control JSON DEFAULT NULL, doc_impact TINYINT(1) NOT NULL, state_timestamps JSON DEFAULT NULL, updates JSON DEFAULT NULL, INDEX IDX_D8698A7648236B59 (rel_pack_id), INDEX IDX_D8698A76F603E5A7 (product_code_id), INDEX IDX_D8698A76B7BB80FF (superviseur_id), INDEX IDX_D8698A76388B8856 (qual_owner_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_materials (document_id INT NOT NULL, material_id INT NOT NULL, INDEX IDX_892F3E14C33F7837 (document_id), INDEX IDX_892F3E14E308AC6F (material_id), PRIMARY KEY(document_id, material_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE document_status_history (id INT AUTO_INCREMENT NOT NULL, performed_by_id INT NOT NULL, document_id INT NOT NULL, previous_status VARCHAR(255) NOT NULL, new_status VARCHAR(255) NOT NULL, action VARCHAR(255) NOT NULL, action_date DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', comments LONGTEXT DEFAULT NULL, INDEX IDX_5527F2C22E65C292 (performed_by_id), INDEX IDX_5527F2C2C33F7837 (document_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE impute (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, code_id INT NOT NULL, nb_heures INT NOT NULL, created_at DATE NOT NULL, INDEX IDX_B0156FCFA76ED395 (user_id), INDEX IDX_B0156FCF27DAFE17 (code_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE material (id INT AUTO_INCREMENT NOT NULL, reference VARCHAR(255) NOT NULL, description VARCHAR(255) NOT NULL, thickness_min INT DEFAULT NULL, thickness_max INT DEFAULT NULL, thickness_unit VARCHAR(50) DEFAULT NULL, density INT DEFAULT NULL, status VARCHAR(50) NOT NULL, rohs VARCHAR(255) DEFAULT NULL, reach VARCHAR(255) DEFAULT NULL, legacy_id INT DEFAULT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE material_type_mapping (id INT AUTO_INCREMENT NOT NULL, display_label VARCHAR(255) NOT NULL, sap_code VARCHAR(10) NOT NULL, description VARCHAR(255) DEFAULT NULL, is_active TINYINT(1) NOT NULL, UNIQUE INDEX UNIQ_6FBFD77FFF74B03 (display_label), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE phase (id INT AUTO_INCREMENT NOT NULL, projet_id INT NOT NULL, title VARCHAR(255) NOT NULL, code VARCHAR(255) NOT NULL, level INT DEFAULT NULL, status TINYINT(1) NOT NULL, status_txt LONGTEXT DEFAULT NULL, status_manuel TINYINT(1) DEFAULT NULL, commentaire LONGTEXT DEFAULT NULL, INDEX IDX_B1BDD6CBC18272 (projet_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE product_code (id INT AUTO_INCREMENT NOT NULL, code VARCHAR(255) NOT NULL, description VARCHAR(255) DEFAULT NULL, etat TINYINT(1) DEFAULT 1 NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE product_range (id INT AUTO_INCREMENT NOT NULL, product_range VARCHAR(255) NOT NULL, division VARCHAR(255) NOT NULL, etat TINYINT(1) NOT NULL, PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE project (id INT AUTO_INCREMENT NOT NULL, project_manager_id INT DEFAULT NULL, otp VARCHAR(255) DEFAULT NULL, title VARCHAR(255) DEFAULT NULL, status VARCHAR(255) DEFAULT NULL, INDEX IDX_2FB3D0EE60984F51 (project_manager_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE released_package (id INT AUTO_INCREMENT NOT NULL, owner_id INT DEFAULT NULL, verif_id INT DEFAULT NULL, valid_id INT DEFAULT NULL, project_relation_id INT DEFAULT NULL, description LONGTEXT DEFAULT NULL, activity VARCHAR(255) DEFAULT NULL, ex VARCHAR(255) DEFAULT NULL, reservation_date DATETIME DEFAULT NULL, creation_date DATETIME DEFAULT NULL, INDEX IDX_5D4403867E3C61F9 (owner_id), INDEX IDX_5D44038683162937 (verif_id), INDEX IDX_5D440386E48CA644 (valid_id), INDEX IDX_5D440386FA9966A0 (project_relation_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE released_package_dmo (released_package_id INT NOT NULL, dmo_id INT NOT NULL, INDEX IDX_BB113AC1D9657415 (released_package_id), INDEX IDX_BB113AC1B940A464 (dmo_id), PRIMARY KEY(released_package_id, dmo_id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user (id INT AUTO_INCREMENT NOT NULL, email VARCHAR(180) NOT NULL, username VARCHAR(255) DEFAULT NULL, nom VARCHAR(255) DEFAULT NULL, prenom VARCHAR(255) DEFAULT NULL, roles JSON NOT NULL, password VARCHAR(255) NOT NULL, manager VARCHAR(255) DEFAULT NULL, titre VARCHAR(255) DEFAULT NULL, is_manager TINYINT(1) DEFAULT NULL, departement VARCHAR(255) NOT NULL, imputation TINYINT(1) DEFAULT NULL, ci TINYINT(1) DEFAULT NULL, sap TINYINT(1) DEFAULT NULL, work_center VARCHAR(255) DEFAULT NULL, managed_places JSON DEFAULT NULL, last_ldap_sync DATETIME DEFAULT NULL, UNIQUE INDEX UNIQ_IDENTIFIER_EMAIL (email), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE user_preference (id INT AUTO_INCREMENT NOT NULL, user_id INT NOT NULL, preference_key VARCHAR(255) NOT NULL, preference_value JSON NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', updated_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_FA0E76BFA76ED395 (user_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE visa (id INT AUTO_INCREMENT NOT NULL, released_drawing_id INT NOT NULL, validator_id INT NOT NULL, status VARCHAR(255) NOT NULL, name VARCHAR(255) NOT NULL, date_visa DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_16B1AB08CB7CF263 (released_drawing_id), INDEX IDX_16B1AB08B0644AEC (validator_id), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('CREATE TABLE messenger_messages (id BIGINT AUTO_INCREMENT NOT NULL, body LONGTEXT NOT NULL, headers LONGTEXT NOT NULL, queue_name VARCHAR(190) NOT NULL, created_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', available_at DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', delivered_at DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', INDEX IDX_75EA56E0FB7336F0 (queue_name), INDEX IDX_75EA56E0E3BD61CE (available_at), INDEX IDX_75EA56E016BA31DB (delivered_at), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE code ADD CONSTRAINT FK_7715309899091188 FOREIGN KEY (phase_id) REFERENCES phase (id)');
        $this->addSql('ALTER TABLE commentaire ADD CONSTRAINT FK_67F068BCA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE commentaire ADD CONSTRAINT FK_67F068BC5F0F2752 FOREIGN KEY (documents_id) REFERENCES document (id)');
        $this->addSql('ALTER TABLE commentaire ADD CONSTRAINT FK_67F068BC95289E08 FOREIGN KEY (dmo_id_id) REFERENCES dmo (id)');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CDA7F43455 FOREIGN KEY (requestor_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CD4C484F77 FOREIGN KEY (eng_owner_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CD21AE072F FOREIGN KEY (last_modificator_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CDE132BA26 FOREIGN KEY (product_range_id) REFERENCES product_range (id)');
        $this->addSql('ALTER TABLE dmo ADD CONSTRAINT FK_BDC1D3CDFA9966A0 FOREIGN KEY (project_relation_id) REFERENCES project (id)');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A7648236B59 FOREIGN KEY (rel_pack_id) REFERENCES released_package (id)');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A76F603E5A7 FOREIGN KEY (product_code_id) REFERENCES product_code (id)');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A76B7BB80FF FOREIGN KEY (superviseur_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE document ADD CONSTRAINT FK_D8698A76388B8856 FOREIGN KEY (qual_owner_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE document_materials ADD CONSTRAINT FK_892F3E14C33F7837 FOREIGN KEY (document_id) REFERENCES document (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE document_materials ADD CONSTRAINT FK_892F3E14E308AC6F FOREIGN KEY (material_id) REFERENCES material (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE document_status_history ADD CONSTRAINT FK_5527F2C22E65C292 FOREIGN KEY (performed_by_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE document_status_history ADD CONSTRAINT FK_5527F2C2C33F7837 FOREIGN KEY (document_id) REFERENCES document (id)');
        $this->addSql('ALTER TABLE impute ADD CONSTRAINT FK_B0156FCFA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE impute ADD CONSTRAINT FK_B0156FCF27DAFE17 FOREIGN KEY (code_id) REFERENCES code (id)');
        $this->addSql('ALTER TABLE phase ADD CONSTRAINT FK_B1BDD6CBC18272 FOREIGN KEY (projet_id) REFERENCES project (id)');
        $this->addSql('ALTER TABLE project ADD CONSTRAINT FK_2FB3D0EE60984F51 FOREIGN KEY (project_manager_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE released_package ADD CONSTRAINT FK_5D4403867E3C61F9 FOREIGN KEY (owner_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE released_package ADD CONSTRAINT FK_5D44038683162937 FOREIGN KEY (verif_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE released_package ADD CONSTRAINT FK_5D440386E48CA644 FOREIGN KEY (valid_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE released_package ADD CONSTRAINT FK_5D440386FA9966A0 FOREIGN KEY (project_relation_id) REFERENCES project (id)');
        $this->addSql('ALTER TABLE released_package_dmo ADD CONSTRAINT FK_BB113AC1D9657415 FOREIGN KEY (released_package_id) REFERENCES released_package (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE released_package_dmo ADD CONSTRAINT FK_BB113AC1B940A464 FOREIGN KEY (dmo_id) REFERENCES dmo (id) ON DELETE CASCADE');
        $this->addSql('ALTER TABLE user_preference ADD CONSTRAINT FK_FA0E76BFA76ED395 FOREIGN KEY (user_id) REFERENCES user (id)');
        $this->addSql('ALTER TABLE visa ADD CONSTRAINT FK_16B1AB08CB7CF263 FOREIGN KEY (released_drawing_id) REFERENCES document (id)');
        $this->addSql('ALTER TABLE visa ADD CONSTRAINT FK_16B1AB08B0644AEC FOREIGN KEY (validator_id) REFERENCES user (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE code DROP FOREIGN KEY FK_7715309899091188');
        $this->addSql('ALTER TABLE commentaire DROP FOREIGN KEY FK_67F068BCA76ED395');
        $this->addSql('ALTER TABLE commentaire DROP FOREIGN KEY FK_67F068BC5F0F2752');
        $this->addSql('ALTER TABLE commentaire DROP FOREIGN KEY FK_67F068BC95289E08');
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CDA7F43455');
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CD4C484F77');
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CD21AE072F');
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CDE132BA26');
        $this->addSql('ALTER TABLE dmo DROP FOREIGN KEY FK_BDC1D3CDFA9966A0');
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A7648236B59');
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A76F603E5A7');
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A76B7BB80FF');
        $this->addSql('ALTER TABLE document DROP FOREIGN KEY FK_D8698A76388B8856');
        $this->addSql('ALTER TABLE document_materials DROP FOREIGN KEY FK_892F3E14C33F7837');
        $this->addSql('ALTER TABLE document_materials DROP FOREIGN KEY FK_892F3E14E308AC6F');
        $this->addSql('ALTER TABLE document_status_history DROP FOREIGN KEY FK_5527F2C22E65C292');
        $this->addSql('ALTER TABLE document_status_history DROP FOREIGN KEY FK_5527F2C2C33F7837');
        $this->addSql('ALTER TABLE impute DROP FOREIGN KEY FK_B0156FCFA76ED395');
        $this->addSql('ALTER TABLE impute DROP FOREIGN KEY FK_B0156FCF27DAFE17');
        $this->addSql('ALTER TABLE phase DROP FOREIGN KEY FK_B1BDD6CBC18272');
        $this->addSql('ALTER TABLE project DROP FOREIGN KEY FK_2FB3D0EE60984F51');
        $this->addSql('ALTER TABLE released_package DROP FOREIGN KEY FK_5D4403867E3C61F9');
        $this->addSql('ALTER TABLE released_package DROP FOREIGN KEY FK_5D44038683162937');
        $this->addSql('ALTER TABLE released_package DROP FOREIGN KEY FK_5D440386E48CA644');
        $this->addSql('ALTER TABLE released_package DROP FOREIGN KEY FK_5D440386FA9966A0');
        $this->addSql('ALTER TABLE released_package_dmo DROP FOREIGN KEY FK_BB113AC1D9657415');
        $this->addSql('ALTER TABLE released_package_dmo DROP FOREIGN KEY FK_BB113AC1B940A464');
        $this->addSql('ALTER TABLE user_preference DROP FOREIGN KEY FK_FA0E76BFA76ED395');
        $this->addSql('ALTER TABLE visa DROP FOREIGN KEY FK_16B1AB08CB7CF263');
        $this->addSql('ALTER TABLE visa DROP FOREIGN KEY FK_16B1AB08B0644AEC');
        $this->addSql('DROP TABLE code');
        $this->addSql('DROP TABLE commentaire');
        $this->addSql('DROP TABLE config');
        $this->addSql('DROP TABLE dmo');
        $this->addSql('DROP TABLE document');
        $this->addSql('DROP TABLE document_materials');
        $this->addSql('DROP TABLE document_status_history');
        $this->addSql('DROP TABLE impute');
        $this->addSql('DROP TABLE material');
        $this->addSql('DROP TABLE material_type_mapping');
        $this->addSql('DROP TABLE phase');
        $this->addSql('DROP TABLE product_code');
        $this->addSql('DROP TABLE product_range');
        $this->addSql('DROP TABLE project');
        $this->addSql('DROP TABLE released_package');
        $this->addSql('DROP TABLE released_package_dmo');
        $this->addSql('DROP TABLE user');
        $this->addSql('DROP TABLE user_preference');
        $this->addSql('DROP TABLE visa');
        $this->addSql('DROP TABLE messenger_messages');
    }
}
