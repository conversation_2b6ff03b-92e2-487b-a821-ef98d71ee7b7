<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250711072123 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE document_statistics (id INT AUTO_INCREMENT NOT NULL, document_id INT NOT NULL, total_cycle_time INT DEFAULT NULL, completion_date DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', start_date DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', step_durations JSON DEFAULT NULL, bottleneck_steps JSON DEFAULT NULL, revision_count INT DEFAULT NULL, comment_count INT DEFAULT NULL, visa_count INT DEFAULT NULL, last_calculated DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', is_completed TINYINT(1) NOT NULL, performance_metrics JSON DEFAULT NULL, days_in_current_step INT DEFAULT NULL, current_bottleneck VARCHAR(255) DEFAULT NULL, UNIQUE INDEX UNIQ_43102074C33F7837 (document_id), INDEX idx_completion_date (completion_date), INDEX idx_total_cycle_time (total_cycle_time), INDEX idx_last_calculated (last_calculated), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB');
        $this->addSql('ALTER TABLE document_statistics ADD CONSTRAINT FK_43102074C33F7837 FOREIGN KEY (document_id) REFERENCES document (id)');
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('ALTER TABLE document_statistics DROP FOREIGN KEY FK_43102074C33F7837');
        $this->addSql('DROP TABLE document_statistics');
    }
}
