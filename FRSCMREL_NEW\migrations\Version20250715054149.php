<?php

declare(strict_types=1);

namespace DoctrineMigrations;

use Doctrine\DBAL\Schema\Schema;
use Doctrine\Migrations\AbstractMigration;

/**
 * Auto-generated Migration: Please modify to your needs!
 */
final class Version20250715054149 extends AbstractMigration
{
    public function getDescription(): string
    {
        return '';
    }

    public function up(Schema $schema): void
    {
        // this up() migration is auto-generated, please modify it to your needs

        // Drop table document_statistics if it exists
        if ($schema->hasTable('document_statistics')) {
            $this->addSql('ALTER TABLE document_statistics DROP FOREIGN KEY FK_43102074C33F7837');
            $this->addSql('DROP TABLE document_statistics');
        }

        // Drop indexes only if tables exist (safe approach for fresh database)
        if ($schema->hasTable('commentaire')) {
            $this->dropIndexIfExists($schema, 'commentaire', 'idx_commentaire_document_date');
            $this->dropIndexIfExists($schema, 'commentaire', 'idx_commentaire_user_date');
        }

        if ($schema->hasTable('document')) {
            $this->dropIndexIfExists($schema, 'document', 'idx_document_types');
            $this->dropIndexIfExists($schema, 'document', 'idx_document_superviseur');
            $this->dropIndexIfExists($schema, 'document', 'idx_document_product_code');
            $this->dropIndexIfExists($schema, 'document', 'idx_document_financial_metrics');
        }

        if ($schema->hasTable('document_materials')) {
            $this->dropIndexIfExists($schema, 'document_materials', 'idx_document_materials');
        }

        if ($schema->hasTable('document_status_history')) {
            $this->dropIndexIfExists($schema, 'document_status_history', 'idx_document_status_history');
        }

        if ($schema->hasTable('released_package')) {
            $this->dropIndexIfExists($schema, 'released_package', 'idx_released_package_owner');
            $this->dropIndexIfExists($schema, 'released_package', 'idx_released_package_project');
        }

        if ($schema->hasTable('visa')) {
            $this->dropIndexIfExists($schema, 'visa', 'idx_visa_be0');
            $this->dropIndexIfExists($schema, 'visa', 'idx_visa_document_name_status');
            $this->dropIndexIfExists($schema, 'visa', 'idx_visa_validator_date');
            $this->dropIndexIfExists($schema, 'visa', 'idx_visa_costing');
        }
    }

    private function dropIndexIfExists(Schema $schema, string $tableName, string $indexName): void
    {
        if ($schema->hasTable($tableName)) {
            $table = $schema->getTable($tableName);
            if ($table->hasIndex($indexName)) {
                $this->addSql("DROP INDEX {$indexName} ON {$tableName}");
            }
        }
    }

    public function down(Schema $schema): void
    {
        // this down() migration is auto-generated, please modify it to your needs
        $this->addSql('CREATE TABLE document_statistics (id INT AUTO_INCREMENT NOT NULL, document_id INT NOT NULL, total_cycle_time INT DEFAULT NULL, completion_date DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', start_date DATETIME DEFAULT NULL COMMENT \'(DC2Type:datetime_immutable)\', step_durations JSON DEFAULT NULL, bottleneck_steps JSON DEFAULT NULL, revision_count INT DEFAULT NULL, comment_count INT DEFAULT NULL, visa_count INT DEFAULT NULL, last_calculated DATETIME NOT NULL COMMENT \'(DC2Type:datetime_immutable)\', is_completed TINYINT(1) NOT NULL, performance_metrics JSON DEFAULT NULL, days_in_current_step INT DEFAULT NULL, current_bottleneck VARCHAR(255) CHARACTER SET utf8mb4 DEFAULT NULL COLLATE `utf8mb4_unicode_ci`, UNIQUE INDEX UNIQ_43102074C33F7837 (document_id), INDEX idx_completion_date (completion_date), INDEX idx_total_cycle_time (total_cycle_time), INDEX idx_last_calculated (last_calculated), PRIMARY KEY(id)) DEFAULT CHARACTER SET utf8mb4 COLLATE `utf8mb4_unicode_ci` ENGINE = InnoDB COMMENT = \'\' ');
        $this->addSql('ALTER TABLE document_statistics ADD CONSTRAINT FK_43102074C33F7837 FOREIGN KEY (document_id) REFERENCES document (id) ON UPDATE NO ACTION ON DELETE NO ACTION');
        $this->addSql('CREATE INDEX idx_commentaire_document_date ON commentaire (documents_id, created_at)');
        $this->addSql('CREATE INDEX idx_commentaire_user_date ON commentaire (user_id, created_at)');
        $this->addSql('CREATE INDEX idx_document_types ON document (doc_type, proc_type, mat_prod_type)');
        $this->addSql('CREATE INDEX idx_document_superviseur ON document (superviseur_id)');
        $this->addSql('CREATE INDEX idx_document_product_code ON document (product_code_id)');
        $this->addSql('CREATE INDEX idx_document_financial_metrics ON document (leadtime, cls, moq)');
        $this->addSql('CREATE INDEX idx_document_materials ON document_materials (document_id, material_id)');
        $this->addSql('CREATE INDEX idx_document_status_history ON document_status_history (document_id, action_date, new_status)');
        $this->addSql('CREATE INDEX idx_released_package_owner ON released_package (owner_id, creation_date)');
        $this->addSql('CREATE INDEX idx_released_package_project ON released_package (project_relation_id)');
        $this->addSql('CREATE INDEX idx_visa_be0 ON visa (name, status, date_visa)');
        $this->addSql('CREATE INDEX idx_visa_document_name_status ON visa (released_drawing_id, name, status, date_visa)');
        $this->addSql('CREATE INDEX idx_visa_validator_date ON visa (validator_id, date_visa, status)');
        $this->addSql('CREATE INDEX idx_visa_costing ON visa (name, status, date_visa)');
    }
}
