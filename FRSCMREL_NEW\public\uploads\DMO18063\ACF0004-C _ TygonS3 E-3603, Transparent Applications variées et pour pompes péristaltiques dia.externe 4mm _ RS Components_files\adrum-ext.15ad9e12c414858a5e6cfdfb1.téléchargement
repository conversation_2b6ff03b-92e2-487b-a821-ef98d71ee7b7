;/* Version 15ad9e12c414858a5e6cfdfb1f2331b1 v:4.5.2.69, c:9de6426bbc282a262d15b465236ed56fa8caa304, b:4.5.2.69 */(function(){/*


 Copyright (c) 2013, AppDynamics, Inc. All rights reserved.

 Derivative of Google Episodes:

 Copyright 2010 Google Inc.

 Licensed under the Apache License, Version 2.0 (the "License");
 you may not use this file except in compliance with the License.
 You may obtain a copy of the License at

 http://www.apache.org/licenses/LICENSE-2.0

 Unless required by applicable law or agreed to in writing, software
 distributed under the License is distributed on an "AS IS" BASIS,
 WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 See the License for the specific language governing permissions and
 limitations under the License.

 See the source code here:
 http://code.google.com/p/episodes/
*/
new function(){var g=window.ADRUM;if(g&&g.q&&!0!==window["adrum-disable"]){var x=window.console,B=x&&"function"==typeof x.log?x:{log:function(){}},u=this&&this.Zg||function(){var c=Object.Wj||{__proto__:[]}instanceof Array&&function(c,a){c.__proto__=a}||function(c,a){for(var b in a)a.hasOwnProperty(b)&&(c[b]=a[b])};return function(f,a){function b(){this.constructor=f}c(f,a);f.prototype=null===a?Object.create(a):(b.prototype=a.prototype,new b)}}();(function(c){(function(f){f.ia=function(a){function b(c){return c.replace(/\s/g,
"")===e}var k={a:[1,2,3,"str"]},e='{"a":[1,2,3,"str"]}';if("undefined"!==typeof JSON&&JSON&&f.isFunction(JSON.stringify)&&b(JSON.stringify(k)))return JSON.stringify(a);if("undefined"!==typeof Object&&Object&&f.isFunction(Object.toJSON)&&b(Object.toJSON(k)))return Object.toJSON(a);c.error("M120");return null};f.Ib=function(a,b,k){var e=a,d=k;"xhr"===k&&(e=document.createElement("div"),a.appendChild(e),d="xhr_"+a.getElementsByTagName("div").length);a=document.createElement("p");a.innerHTML="Script loaded from "+
c.conf.adrumExtUrl+". Metrics collected are:";e.appendChild(a);a=document.createElement("table");a.id="ADRUM_"+d;var d=document.createElement("tbody"),h;for(h in b){k=document.createElement("tr");var f=document.createElement("td");f.innerHTML=h;f.className="name";var l=document.createElement("td");l.innerHTML=String(b[h]);l.className="value";k.appendChild(f);k.appendChild(l);d.appendChild(k)}a.appendChild(d);e.appendChild(a)};f.dumpObject=function(){function c(b,k){try{if("object"!=typeof b)return String(b);
if(0<=f.Va(k,b))return"Already visited";k.push(b);var e;if(f.isArray(b)){for(var d="[ ",h=0;h<b.length;h++)0!=h&&(d+=", "),d+=c(b[h],k);e=d+" ]"}else{var d="{ ",h=!0,t;for(t in b)h?h=!1:d+=", ",d+=c(t,k)+" => "+c(b[t],k);e=d+" }"}return e}catch(l){return"dumpObject failed: "+l}}return function(b){return c(b,[])}}();f.Va=f.isFunction(Array.prototype.indexOf)?function(c,b){return c.indexOf(b)}:function(c,b){for(var k=0;k<c.length;k++)if(c[k]==b)return k;return-1};f.compareWindows=function(c,b){return c==
b};f.Qb=function(c,b,k,e,d){if(!f.isDefined(b))return b;if(f.isArray(b)){c=[];for(var h=0,t=b.length;h<t;h++)c[h]=f.Qb(h,b[h],k,e,d);return c}if(f.isFunction(b))return b;if(f.isObject(b)){c={};for(h in b)f.hasOwnPropertyDefined(b,h)&&(c[k(h)]=d&&d(h)?b[h]:f.Qb(h,b[h],k,e,d));return c}return e(c,b)};f.dj=function(c){var b=[],k;for(k in c)f.hasOwnPropertyDefined(c,k)&&b.push([k,c[k]]);return b};f.gk=function(){try{localStorage.setItem("try","try");var c=localStorage.getItem("try");localStorage.removeItem("try");
return"try"===c}catch(b){return!1}};f.fk=function(){try{var c=f.ia({v:1});return 1===JSON.parse(c).v}catch(b){return!1}};f.hk=function(){return f.isFunction(window.postMessage)};f.Ah=function(c,b){var k=document.createElement("div");k.innerHTML='<iframe id="'+b+'" src='+c+' width="0" height="0" tabindex="-1" title="empty" style="display: none;"></iframe>';document.body.appendChild(k);return document.getElementById(b)};f.ff=function(c,b){f.isDefined(b)&&(c.parentPageName=b)};f.Xb=function(c){return f.isNumber(c)&&
isFinite(c)&&Math.floor(c)===c};f.Cd=function(f){c.log(f);B.log(f)}})(c.utils||(c.utils={}))})(g||(g={}));(function(c){(function(c){c.of=function(c){return/^[0-9]+$/.test(c)?Number(c):null};c.he=function(c){return"number"===typeof c&&!isNaN(c)&&isFinite(c)}})(c.utils||(c.utils={}))})(g||(g={}));(function(c){(function(f){function a(d){var e=d.split("\r\n"),k=/^\s*ADRUM_(\d+): (.+)\s*$/i;d=[];for(var b=0;b<e.length;b++){var f=e[b];try{var a=k.exec(f);a&&d.push([Number(a[1]),a[2]])}catch(g){c.exception(g,
"M123",f)}}Array.prototype.sort.call(d,function(c,d){return c[0]-d[0]});a=[];for(e=0;e<d.length;e++)a.push(d[e][1]);return a}var b=c.utils.hasOwnPropertyDefined,k=null,e=null;f.Xd=function(){e||(e=f.ye(f.jc(f.cookieMetadataChunks),f.jc(c.footerMetadataChunks)));return e};f.getPageGUID=function(){var d=c.monitor.DOMEventsMonitor,e=c.utils.isObject(d.currentBasePage)&&c.utils.isFunction(d.currentBasePage.guid);if(!k){var b=f.Xd();k=b&&b.clientRequestGUID||(e?d.currentBasePage.guid():c.utils.generateGUID())}return k};
f.oi=function(d){d=a(d);c.log("M121",d);return f.ye(f.jc(d))};f.ye=function(d,e){function k(d){for(var e=0;e<d.length;e++){var h=d[e];h!=b&&0>c.utils.Va(q,h)&&q.push(h)}}if(!d||0>=d.ka.length)return null;e||(e=d);var b;if(0<e.ka.length){if(b=e.ka[0],0>c.utils.Va(d.ka,b))return null}else return c.error("M122"),null;var a=e.serverSnapshotType||d.serverSnapshotType,g=e.hasEntryPointErrors||d.hasEntryPointErrors,q=[];k(d.ka);k(e.ka);var p={clientRequestGUID:b,btGlobalAccountName:d.btGlobalAccountName};
0<q.length&&(p.otherClientRequestGUIDs=q);0<f.xe(d.btTime,e.btTime).length&&(p.btTime=f.xe(d.btTime,e.btTime));null!==a&&(p.serverSnapshotType=a);null!==g&&(p.hasEntryPointErrors=g);return p};f.xe=function(c,e){for(var k=c.concat(e),f={},a={},g=0;g<k.length;g++){var q=k[g];q.id in f||(f[q.id]=-1);f[q.id]=Math.max(f[q.id],q.duration);q.id in a||(a[q.id]=-1);a[q.id]=Math.max(a[q.id],q.ert)}var k=[],p;for(p in f)b(f,p)&&k.push({id:p,duration:f[p],ert:a[p]});return k};f.jc=function(d){if(!c.utils.isArray(d))return null;
for(var e=[],k=null,b=[],f=null,a=null,g=0;g<d.length;g++){var p=d[g];if("string"!==typeof p)return null;p=p.replace(/^"|"$/g,"");p=decodeURIComponent(p).split(",")[0].replace(/^\s+|\s+$/g,"").split(":");if(2===p.length){var m=p[1];switch(p[0]){case "clientRequestGUID":case "g":e.push(m);break;case "btId":case "i":b.push({id:m,duration:-1,ert:-1});break;case "btDuration":case "d":if(0===b.length)return null;p=c.utils.of(m);if(!c.utils.he(p)||-1>p)return null;b[b.length-1].duration=p;break;case "btERT":case "e":if(0===
b.length)return null;p=c.utils.of(m);if(!c.utils.he(p)||-1>p)return null;b[b.length-1].ert=p;break;case "serverSnapshotType":case "s":f=m;break;case "globalAccountName":case "n":k=m;break;case "hasEntryPointErrors":case "h":a=m}}}return 0===e.length?null:{ka:e,btGlobalAccountName:k,btTime:b,serverSnapshotType:f,hasEntryPointErrors:a}};f.ul=a})(c.correlation||(c.correlation={}))})(g||(g={}));(function(c){var f=c.conf||(c.conf={});f.Mk=3E3;f.useDebugBeaconParams=c.isDebug;f.Vg=1E3;f.ug=2;f.tg=5;f.og=
5;f.pg=20;f.Yc=5E3;f.va=512;f.Tf=10;f.Sf=64;f.Fg=10;f.Eg=10;f.Ag=300;f.xg=2048;f.wa=2048;f.xa=760;f.re={eumAppKey:"ky",userPageName:"un",clientRequestGUID:"cg",otherClientRequestGUIDs:"og",baseGUID:"bg",parentGUID:"mg",parentPageUrl:"mu",parentPageType:"mt",parentLifecyclePhase:"pp",pageType:"pt",pageUrl:"pu",pageReferrer:"pr",pageTitle:"pl",pageName:"pn",httpHeaders:"hh",parameter:"pa",navOrXhrMetrics:"mn",cookieMetrics:"mc",resourceTimingInfo:"rt",userData:"ud",userDataLong:"udl",userDataDouble:"udd",
userDataBoolean:"udb",userDataDate:"udt",errors:"er",ajaxError:"ae",xhrStatus:"xs",btTime:"bt",btGlobalAccountName:"btgan",serverSnapshotType:"ss",hasEntryPointErrors:"se",dataType:"dt",geoCountry:"gc",geoRegion:"gr",geoCity:"gt",localIP:"lp",ip:"ip",BEACONS:"B",ver:"vr",eom:"em",agentId:"ai",rootGUID:"rg",events:"es",guids:"gs",urlParts:"up",sequenceId:"si",eventType:"et",eventGUID:"eg",parentType:"at",serverMetadata:"sm",eventUrl:"eu",line:"ln",message:"dm",duration:"dn",id:"id",ert:"ert",parentUrl:"au",
parentPageName:"an",geo:"ge",metrics:"mx",timestamp:"ts",country:"c",region:"r",city:"t",method:"md",clientId:"ci"}})(g||(g={}));(function(c){c=c.beacons||(c.beacons={});c.numBeaconsSent=0;c.beaconsSent=[]})(g||(g={}));(function(c){(function(f){var a=function(){function b(){}b.prototype.send=function(k,e){var d=(e?c.conf.beaconUrlHttps:c.conf.beaconUrlHttp)+c.conf.corsEndpointPath+"/"+c.conf.appKey+"/adrum",b;c.utils.isFunction(c.xhrConstructor)&&c.utils.isFunction(c.xhrOpen)?(b=new c.xhrConstructor,
c.xhrOpen.call(b,"POST",d)):(b=new XMLHttpRequest,b.open("POST",d));b.setRequestHeader("Content-type","text/plain");var a=c.utils.ia(k);null!=a&&(c.utils.isFunction(c.xhrConstructor)&&c.utils.isFunction(c.xhrSend)?c.xhrSend.call(b,a):b.send(a),c.log("M124",d),c.log("\n"),c.log("<hr/>"),c.isDebug&&f.beaconsSent.push(k),f.numBeaconsSent+=1)};return b}();f.Of=a})(c.beacons||(c.beacons={}))})(g||(g={}));g||(g={});(function(c){var f=function(){function f(c,k,e){this.ab=c;this.zi=k;this.Ec=e}f.prototype.Ob=
function(){try{return f.Ud(this.zi.ADRUM)}catch(b){return c.error("M125"),f.Ud(c)}};f.Ud=function(c){return c.correlation.getPageGUID()};return f}();c.Lf=f})(g||(g={}));(function(c){c.beacons||(c.beacons={})})(g||(g={}));(function(c){(function(c){var a=function(){function b(){this.Xa=[];this.Jb={}}b.prototype.$a=function(k){if(!k)return"";if(c.hasOwnPropertyDefined(this.Jb,k))return this.Jb[k];var e=this.Xa.length;this.Jb[k]=e;this.Xa[e]=k;return e};return b}();c.Xc=a})(c.utils||(c.utils={}))})(g||
(g={}));(function(c){(function(f){var a=c.utils.map,b=function(){function c(){}c.bk=function(c){c=(c||"").match(/([^:\/?#]+:\/\/)?([^?#]+)?(\?[^#]*)?(#[\s\S]*)?/);var d={};c[1]&&(d.protocol=c[1].substring(0,c[1].length-3));c[2]&&(d.path=c[2]);c[3]&&(d.Ej=c[3]);c[4]&&(d.anchor=c[4]);return d};c.Mh=function(e,d){var b=c.bk(e),f="";b.protocol&&(f+=d.$a(b.protocol),f+="://");b.path&&(f+=a(b.path.split("/"),d.$a,d).join("/"));return f+=a([b.Ej,b.anchor],function(c){return c?c[0]+d.$a(c.slice(1)):""},d).join("")};
c.Kh=function(c,d){return""+d.$a(c)};return c}();f.Oc=b})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){(function(f){(function(f){function b(){var d=null,k=null;try{d=localStorage[e]}catch(b){}if(d)try{d=k=JSON.parse(d),c.utils.isObject(d)&&c.utils.isNumber(d[t])&&-1<d[t]?g=k:c.error("M126")}catch(f){c.exception(f,"M127");try{delete localStorage[e]}catch(h){c.exception(h,"M128")}}}function k(){try{var k=c.channel,b=g[d]||g[h],f=k&&k.M&&k.M.K&&k.M.K.id||(c.utils.isString(b)?b:c.utils.generateGUID());
g[d]=g[h]=f;localStorage[e]=c.utils.ia(g)}catch(a){}}var e="ADRUM_CLIENTINFO",d="agentId",h="clientId",t="seqId",g;b();g||(g={seqId:0});k();f.di=function(){b();var c=g[t]++;k();return c}})(f.zd||(f.zd={}))})(c.ef||(c.ef={}))})(g||(g={}));(function(c){(function(f){var a=function(){function b(){this.urlCapture={filterURLQuery:!1};this.Qe=/;jsessionid=[^/?]+/;f.mergeJSON(this.urlCapture,c.conf.userConf.urlCapture);f.isBoolean(this.urlCapture.filterURLQuery)||f.isArray(this.urlCapture.filterURLQuery)||
(c.log("M129"),this.urlCapture.filterURLQuery=!1)}b.$j=function(c){for(var e=0,d=0;d<c.length;d++)e=(e<<5)-e+c.charCodeAt(d),e|=0;return e};b.prototype.li=function(c){var e="",d,b;if(!f.isString(c))return"";d=c.indexOf("?")+1;0<d&&(b=c.indexOf("#"),0>b?e=c.substring(d):d<=b&&(e=c.substring(d,b)));return e};b.prototype.Qh=function(c){var e="",d=this.urlCapture.filterURLQuery,b={},a=0,g,r,s;if(!f.isString(c)||""===c)return"";if(!1===d)return c;g=this.li(c);if(""===g)return c;if(f.isArray(d)){for(s=
0;s<d.length;s++)b[d[s]]=!0;d=g+"&";for(s=0;s<d.length;s++)switch(d[s]){case "=":r=d.substring(a,s);break;case "&":case ";":r=r||d.substring(a,s),s++,b[r]||(e+=d.substring(a,s)),a=s,r=null}e=e.substring(0,e.length-1)}""==e&&(g="?"+g);return c.replace(g,e)};b.prototype.aa=function(c){if(null===c||void 0===c)return null;if(""===c)return"";var e=c.match(this.Qe),d=c;if(null!=e){var b=c.indexOf("?");if(0>b||b>e.index)d=c.replace(this.Qe,"")}return d=this.Qh(d)};b.prototype.Kd=function(c){if(null===c||
void 0===c)return null;var e=c.indexOf("?"),d=c.indexOf("#");0>e&&(e=Number.MAX_VALUE);0>d&&(d=Number.MAX_VALUE);return c.substring(0,Math.min(e,d))};b.prototype.oj=function(c){if(null===c||void 0===c)return null;var e=this.Kd(c);return e+"?"+b.$j(c.substring(e.length))};return b}();f.Qk=a;f.N=new a})(c.utils||(c.utils={}))})(g||(g={}));(function(c){(function(f){(function(f){var b=function(){function b(e,d,k){var f=this;this.Hj=0;this.nc=[];this.$d=!1;this.ti=k;c.utils.addEventListener(window,"message",
function(c){f.sj(c)});this.si=c.utils.Ah(d,e)}b.prototype.sj=function(e){var d;try{if(d=JSON.parse(e.data),"ADRUM_XD"!==d.name)c.log("M130");else if(!0===d.success)if(-1===d.id&&"iframe-ready"===d.data)this.$d=!0,this.ti();else{if(this.nc[d.id])this.nc[d.id](d.data)}else c.error("M131",e.data)}catch(b){c.log("M132",b)}};b.prototype.send=function(e,d,b,k){if(this.$d){var f=this.Hj++;this.nc[f]=k;this.si.contentWindow.postMessage(c.utils.ia({id:f,action:e,key:d,value:b}),"*")}};b.prototype.generateGUID=
function(c,d){this.send("generateGUID",c,null,d)};return b}();f.Qf=b})(f.nf||(f.nf={}))})(c.Cf||(c.Cf={}))})(g||(g={}));(function(c){(function(f){var a=function(){function e(){this.rd=new b(e.Gf);this.Bf=new b(e.If);this.Dk=new k(e.Hf)}e.prototype.init=function(d){this.channel=d;c.utils.gk()&&c.utils.fk()?c.utils.hk()&&e.Zi()?this.rk():this.wf():e.ze=!0};e.prototype.wf=function(){var d=this.rd.load();e.Xi(d)||(d={ver:c.conf.agentVer,id:c.utils.generateGUID()},this.rd.save(d));e.K=d};e.prototype.rk=
function(){var d=this,b=this.Bf.load();e.Yi(b)?e.K=b:(c.log("M133"),this.Dk.load(function(b){e.ke(b)?(b={ver:c.conf.agentVer,id:b,ttl:(new Date).getTime()+e.Uf},d.Bf.save(b),e.K=b,c.log("M134")):c.log("M135")}),c.utils.tryPeriodically(e.zb,function(){return d.isReady()},function(){return d.onReady()},function(){return d.ec()}))};e.Zi=function(){return c.conf.userConf&&c.conf.userConf.xd&&c.conf.userConf.xd.enable};e.Xi=function(c){return c&&c.id&&0<c.id.length};e.Yi=function(c){return c&&e.ke(c.id)&&
c.ttl>(new Date).getTime()};e.ke=function(d){return c.utils.isString(d)&&0<d.length&&0===d.indexOf("XD_")};e.prototype.isReady=function(){return e.ze||c.utils.isDefined(e.K)};e.prototype.onReady=function(){this.channel.onResolverReady()};e.prototype.ec=function(){c.log("M136");this.wf();this.channel.onResolverReady()};e.zb=function(c){return 10<c?-1:[1,50,100,500][c-1]||1E3};return e}();a.Gf="ADRUM_AGENT_INFO";a.If="ADRUM_XD_AGENT_INFO";a.Hf="ADRUM_XD_AGENT_ID";a.Uf=6048E5;a.ze=!1;f.M=a;var b=function(){function e(c){this.qd=
c}e.prototype.load=function(){var d=null;try{var e=localStorage.getItem(this.qd);e&&(d=JSON.parse(e))}catch(b){c.exception(b,"M137")}return d};e.prototype.save=function(d){try{var e=c.utils.ia(d);e&&localStorage.setItem(this.qd,e)}catch(b){c.exception(b,"M138")}};return e}(),k=function(){function e(c){this.gh=c}e.prototype.load=function(d){var e=this;try{var b=new c.Cf.nf.Qf("cross-domain-store-server-iframe",c.conf.adrumXdUrl,function(){b.generateGUID(e.gh,d)})}catch(k){c.exception(k,"M139")}};return e}()})(c.channel||
(c.channel={}))})(g||(g={}));(function(c){(function(f){var a=c.utils.isString,b=c.utils.isNumber,k=function(){function e(){}e.prototype.eb=function(d,b,k){d={ver:c.conf.agentVer,dataType:"R",rootGUID:k.Ob(),events:d};c.channel.M.K&&c.channel.M.K.id&&(d.agentId=c.channel.M.K.id);b&&(d.geo=b);return e.Bj(d)};e.Bj=function(d){c.isDebug||(d=e.uh(d));return d};e.uh=function(d){var k=new c.utils.Xc,g=new c.utils.Xc;d=c.utils.Qb(null,d,e.Pb,function(d,r){if(!a(r))return r;if(e.Wi(d)){var s=e.Qi(d)&&b(c.conf.userConf.maxUrlLength)?
c.conf.userConf.maxUrlLength:c.conf.va;r=e.Bc(r,s);r=f.Oc.Mh(r,k)}else r=e.Pi(d)?e.Bc(r,c.conf.xa):e.Bc(r,c.conf.xg);e.Ji(d)&&(r=f.Oc.Kh(r,g));return r},e.gj);d[e.Pb("guids")]=g.Xa;d[e.Pb("urlParts")]=k.Xa;return d};e.Wi=function(c){return"eventUrl"==c||"parentUrl"==c||"pageReferrer"==c||"pageUrl"==c||"u"==c};e.Ji=function(c){return"eventGUID"==c||"parentGUID"==c||"rootGUID"==c||"clientRequestGUID"==c};e.Pi=function(c){return"userPageName"==c};e.Bc=function(c,e){return c.length>e?c.substr(0,e-3)+
"...":c};e.Qi=function(c){return"eventUrl"==c||"parentUrl"==c||"pageReferrer"==c};e.Pb=function(d){if(c.conf.useDebugBeaconParams)return d;var e=c.conf.re[d];return"undefined"===typeof e?d:e};e.gj=function(c){c=""+c;return 0===c.indexOf("userData")||"parameter"===c};return e}();f.Nf=k})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){(function(f){var a=function(){function b(){}b.prototype.send=function(c,e){for(var d=0;d<c.length;d++)this.Sj(c[d],e)};b.prototype.Sj=function(b,e){var d=(e?c.conf.beaconUrlHttps:
c.conf.beaconUrlHttp)+c.conf.imageEndpointPath,a=document.createElement("img");try{a.src=d+b}catch(g){}c.isDebug&&f.beaconsSent.push(b);f.numBeaconsSent+=1};return b}();f.jg=a})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){(function(c){c.Na=function(c,b){if(3>=b)return"...";c.length>b&&(c=c.substring(0,b-3)+"...");return c};c.sf=function(c,b){if(3>=b)return"...";c.length>b&&(c=c.substring(0,(b-3)/2)+"..."+c.substring(c.length-(b-3)/2,c.length));return c};c.nk=function(a,b){a.length<=b||(a=
c.Na(a,b-1),a+=":");return a};c.lk=function(a,b,k,e,d,h){a=c.nk(a,e);k=c.Na(k,d);a.length+b.length+k.length>h-3&&(b=c.Na(b,h-3-a.length-k.length));return 0<k.length?a+"//"+b+":"+k:a+"//"+b};c.mk=function(a,b,k){a=c.map(a,function(e){return c.sf(e,b)});if(a.length>k){var e=a[0];a=a.slice(a.length-k);a.unshift("...");""==e&&a.unshift(e)}return a};c.ok=function(a,b,k,e,d){e=c.lk(a.protocol,a.hostname,a.port,e,d,b);k--;d=c.Na(a.search,b);k--;var h=c.Na(a.hash,b);k--;a=a.pathname.split("/");var g="";0<
a.length&&(g=a.pop(),g=c.sf(g,b),k--);a=c.mk(a,b,k);return 0==a.length?e+g+d+h:e+a.join("/")+"/"+g+d+h}})(c.utils||(c.utils={}))})(g||(g={}));(function(c){(function(c){var a=function(){function c(){}c.Vb=function(){for(var k=0;k<c.Vc.length;k++)c.X[c.Vc[k]]=c.Q.eg;for(k=0;k<c.Tc.length;k++)c.X[c.Tc[k]]=c.Q.Zf;c.X.js=c.Q.Pg;c.X.css=c.Q.CSS;c.X.svg=c.Q.Rg;c.X.html=c.Q.Uc;c.X.htm=c.Q.Uc};c.Mb=function(c){return c.duration?c.duration:c.responseEnd&&c.startTime?c.responseEnd-c.startTime:-1};c.Wd=function(k){var e;
return(k=k.pathname)&&-1!=(e=k.lastIndexOf("."))?c.X[k.substring(e+1,k.length).toLowerCase()]||c.Q.OTHER:c.Q.OTHER};return c}();a.Q={eg:"img",Pg:"script",CSS:"css",Rg:"svg",Uc:"html",Zf:"font",OTHER:"other"};a.Vc="bmp gif jpeg jpg png webp".split(" ");a.Tc=["ttf","woff","otf","eot"];a.X={};c.ya=a;a.Vb()})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(c){var a=function(){function c(k){function e(c){d.marks.push(0==k[c]?-1:Math.round(k[c]-d.Ee))}this.marks=[];var d=this;this.Ee=k.startTime;
this.Hi(k)?(this.Nd=c.Pc,e("startTime"),e("fetchStart")):(this.Nd=c.ed,e("startTime"),e("redirectStart"),e("redirectEnd"),e("fetchStart"),e("domainLookupStart"),e("domainLookupEnd"),e("connectStart"),e("secureConnectionStart"),e("connectEnd"),e("requestStart"),e("responseStart"));e("responseEnd")}c.fi=function(){var k={};k[c.ed]=c.Og;k[c.Pc]=c.Pf;return k};c.prototype.Hi=function(c){return this.ga(c.redirectStart)&&this.ga(c.redirectEnd)&&(this.ga(c.domainLookupStart)||this.ga(c.domainLookupEnd)||
this.ga(c.connectStart)||this.ga(c.connectEnd))&&this.ga(c.secureConnectionStart)};c.prototype.ga=function(c){return!c||0===c};return c}();a.ed=1;a.Pc=2;a.Og="startTime redirectStart redirectEnd fetchStart dnsLookupStart dnsLookupEnd connectStart secureConnectionStart connectEnd requestStart responseStart responseEnd".split(" ");a.Pf=["startTime","fetchStart","responseEnd"];c.dd=a})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(c){var a=function(){function c(){}c.prototype.ib=function(c,
e){if(c)return c.slice(0,e)};return c}();c.$f=a})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(c){var a=function(){function b(){}b.prototype.ib=function(b,e){if(b){if(b.length<=e)return b;for(var d=[],a=0;a<b.length;a++)d.push({oc:b[a],index:a});d.sort(function(e,d){return-(c.ya.Mb(e.oc)-c.ya.Mb(d.oc))});d=d.slice(0,e);d.sort(function(c,e){return c.index-e.index});for(var g=[],a=0;a<d.length;a++)g.push(d[a].oc);return g}};return b}();c.gd=a})(c.events||(c.events={}))})(g||(g={}));
(function(c){(function(c){var a=function(){function b(){}b.prototype.ib=function(b,e){if(b){if(b.length<=e)return b;for(var d=1,a=Math.floor(Number.MAX_VALUE/4),g=b.length;d<a;){for(var l=b.length-1;0<=l;l--)if(c.ya.Mb(b[l])<d&&(b.splice(l,1),g--),g<=e)return b;d*=4}return(new c.gd).ib(b,e)}};return b}();c.Ng=a})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(f){var a=c.utils.hasOwnPropertyDefined,b=function(){function e(d,b,a){this.version=2;this.resourceTimingInfo={};this.ce={};this.de=
{};this.We={};this.Xe={};this.qc=[];this.ah=function(e,d,b){if(e&&d&&0<d.length){this.jk=e;for(var a=new k,h=new k,g=0;g<d.length;g++){var t=d[g],l=c.utils.parseURI(c.utils.N.aa(t.name)),v=t.initiatorType,C=f.ya.Wd(l),w=new f.dd(t);this.qc.push({u:this.pk(l),i:a.Td(this.de,v),r:h.Td(this.Xe,C),f:w.Nd,o:this.Yh(0===g&&t.isBase,w.Ee,e,b),m:w.marks,e:c.utils.isDefined(t.encodedBodySize)?t.encodedBodySize:-1,d:c.utils.isDefined(t.decodedBodySize)?t.decodedBodySize:-1,s:c.utils.isDefined(t.transferSize)?
t.transferSize:-1,p:c.utils.isDefined(t.nextHopProtocol)?t.nextHopProtocol:""})}}};var g=e.Zh();this.rh(g,d);d=this.Oj(g,d);this.ah(b||c.monitor.perfMonitor.navTiming&&c.monitor.perfMonitor.navTiming.navigationStart,d,a);this.clearResourceTimings()}e.Vb=function(){var d=c.conf.userConf&&c.conf.userConf.resTiming&&c.conf.userConf.resTiming.sampler;e.Nj=d&&"FirstN"==d?new f.$f:d&&"TopN"==d?new f.gd:new f.Ng};e.prototype.ae=function(c,e){c[e]=c[e]?c[e]+1:1};e.prototype.pk=function(e){return c.utils.ok(e,
"number"===typeof c.conf.userConf.maxResUrlSegmentLength?c.conf.userConf.maxResUrlSegmentLength:c.conf.Sf,"number"===typeof c.conf.userConf.maxResUrlSegmentNumber?c.conf.userConf.maxResUrlSegmentNumber:c.conf.Tf,c.conf.Fg,c.conf.Eg)};e.prototype.rh=function(c,e){c&&this.be(c);if(e&&0<e.length)for(var b=0;b<e.length;b++)this.be(e[b])};e.prototype.be=function(e){var b=c.utils.parseURI(c.utils.N.aa(e.name)),b=f.ya.Wd(b);this.ae(this.ce,e.initiatorType);this.ae(this.We,b)};e.prototype.Oj=function(d,b){if(b&&
0<b.length){var k=c.conf.userConf&&c.conf.userConf.resTiming&&c.conf.userConf.resTiming.maxNum||c.conf.Ag;d&&k--;b=e.Nj.ib(b,k);d&&b.unshift(d)}return b};e.prototype.Yh=function(e,b,k,f){return e?1:c.utils.isDefined(f)?Math.round(b+k-f):Math.round(b)};e.prototype.build=function(){return 0==this.qc.length?null:{v:this.version,ic:this.ce,it:this.de,rc:this.We,rt:this.Xe,f:f.dd.fi(),t:this.jk,r:this.qc}};e.Zh=function(){var e=c.monitor.perfMonitor.navTiming,b=null;if(e){var b={},k;for(k in e)a(e,k)&&
(b[k]=e[k]);b.initiatorType="other";b.name=document.URL;b.navigationStart&&!b.startTime&&(b.startTime=b.navigationStart);b.isBase=!0}return b};e.prototype.clearResourceTimings=function(){c.conf.userConf&&c.conf.userConf.resTiming&&c.conf.userConf.resTiming.clearResTimingOnBeaconSend&&c.monitor.perfMonitor.clearResourceTimings()};return e}();f.ResourceTimingInfoBuilder=b;var k=function(){function c(){this.ij=1}c.prototype.Td=function(c,e){c[e]||(c[e]=this.ij++);return c[e]};return c}();b.Vb()})(c.events||
(c.events={}))})(g||(g={}));(function(c){c.events||(c.events={})})(g||(g={}));(function(c){(function(f){var a=function(){function b(){}b.prototype.n=function(b,e){void 0===e&&(e={});var d={eventGUID:b.guid(),eventType:b.type(),eventUrl:c.utils.N.aa(b.url()||document.URL),timestamp:c.utils.now()};b.type()!==c.EventType.PageView&&c.utils.mergeJSON(d,{parentGUID:b.parentGUID()||c.correlation.getPageGUID(),parentUrl:c.utils.N.aa(b.parentUrl()||document.URL),parentType:b.parentType()||(c.utils.compareWindows(top,
window)?c.EventType.PageView:c.EventType.IFRAME)});var a=c.EventType[b.type()],a=f.R.qi(a,c.conf.userConf,e,c.conf.wa),g;for(g in a)d[g]=a[g];return d};return b}();f.ta=a})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(f){var a=c.utils.isNumber,b=c.utils.isString,k=c.utils.isObject,e=c.utils.Cd,d=c.utils.Xb,h=c.utils.isBoolean,g=function(){function d(){}d.dc=function(e,d){var b=!1;if(k(e)){var f=c.utils.ia(e);if(!f||f.length<=d)b=!0}return b};d.zc=function(c,e,k){if(b(e))if(e.length>
k)d.pa(c,k,e.length);else return e;else d.$b(c,"string")};d.fb=function(c,e){return function(b,f,a){if(k(f)){var g,h;for(h in f)c(f[h])?(g=g||{},g[h]=f[h]):d.$b(b,e,h);if(g){if(d.dc(g,a))return g;d.pa(b,a)}}else d.$b(b,"object")}};d.$b=function(c,d,b){e(c+" dropped because it was not a "+d+(b?", field name "+b:""))};d.pa=function(c,d,b){e("User event info field "+c+" dropped because it is too long"+(b?" ("+b+" exceeds "+d+")":""))};return d}();g.qi=function(e,d,b,f){if(d=d&&d.userEventInfo&&d.userEventInfo[e]){try{var a=
c.utils.isFunction(d)?d.call(null,b):d}catch(h){c.exception(h,"M141",e)}if(k(a))return g.uk(a,f)}};g.uk=function(c,e){var d={},b;for(b in g.Ld)if(b in c){var k=g.Ld[b](b,c[b],e);k&&(d[b]=k)}return d};g.rj=g.fb(b,"string");g.qj=g.fb(d,"integer");g.Ae=g.fb(a,"number");g.pj=g.fb(h,"boolean");g.Ld={userPageName:g.zc,userData:g.rj,userDataLong:g.qj,userDataDouble:g.Ae,userDataBoolean:g.pj,userDataDate:g.Ae};f.R=g})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(f){var a=c.utils.isDefined,
b=function(b){function e(){return null!==b&&b.apply(this,arguments)||this}u(e,b);e.setPageName=function(c){e.ca=c};e.addUserData=function(c,b){e.Oa=e.Oa||{};e.Oa[c]=b};e.prototype.n=function(d){d=b.prototype.n.call(this,d||new f.PageView);var g=this.hi(),t=this.$h();c.conf.viz&&(g&&c.utils.Ib(document.getElementById(c.conf.viz),g,"navtime"),c.utils.Ib(document.getElementById(c.conf.viz),t,"cookie"));t.PLC=1;g&&(g.PLC=1);c.monitor.ErrorMonitor.hadErrors&&(t.EPM=1,g&&(g.EPM=1));c.utils.mergeJSON(d,
{eventGUID:c.correlation.getPageGUID(),eventType:c.utils.compareWindows(top,window)?c.EventType.PageView:c.EventType.IFRAME,cookieMetrics:t});var l=-1;g&&g.timestamp?l=g.timestamp:t&&t.timestamp&&(l=t.timestamp);0<l&&c.utils.mergeJSON(d,{timestamp:l});g&&(d.metrics=g);document.referrer&&null!==document.referrer&&0<document.referrer.length&&(d.pageReferrer=c.utils.N.aa(document.referrer));g=e.ii();0<g.length&&(d.pageTitle=g);g=c.correlation.Xd();null!==g&&(d.serverMetadata=g);c.conf.spa2?0<c.monitor.resourceMonitor.basePageResourceBuffer.length?
(g=(new f.ResourceTimingInfoBuilder(c.monitor.resourceMonitor.basePageResourceBuffer)).build(),c.monitor.resourceMonitor.basePageResourceBuffer=[]):g=null:g=c.monitor.perfMonitor.resTiming?(new f.ResourceTimingInfoBuilder(c.monitor.perfMonitor.resTiming)).build():null;null!==g&&(d.resourceTimingInfo=g);!a(d.userData)&&a(e.Oa)&&(f.R.dc(e.Oa,c.conf.wa)?d.userData=e.Oa:f.R.pa("userData",c.conf.wa));!a(d.userPageName)&&a(e.ca)&&(f.R.zc("userPageName",e.ca,c.conf.xa)?d.userPageName=e.ca:f.R.pa("userPageName",
c.conf.xa));e.userPageName=d.userPageName;return d};e.prototype.hi=function(){if(!c.monitor.perfMonitor.navTiming)return null;var d={},b=e.Vd(c.monitor.perfMonitor.navTiming,d,"NT"),k=f.metricSpec[c.EventType.PageView],a;for(a in k){var g=k[a];b.add(g.name,g.start,g.end)}d.timestamp=c.monitor.perfMonitor.navTiming.navigationStart;return d};e.prototype.$h=function(){var b={};c.commands.marks&&(e.Vd(c.commands.marks,b,"CK").add("PLT","starttime","onload").add("FBT","starttime","firstbyte").add("FET",
"firstbyte","onload").add("DRT","firstbyte","onready").add("PRT","onready","onload").add("DOM","starttime","onready"),b.timestamp=c.commands.marks.starttime?c.commands.marks.starttime:c.commands.marks.firstbyte);return b};e.ii=function(){var e=c.conf.userConf,e=e&&e.page,b;b=e&&"captureTitle"in e?e.captureTitle:!0;var k,f;e&&(k="title"in e,f=e.title);k=b?k?c.utils.isFunction(f)?f():f:document.title:"";if(c.utils.isString(k))return k;c.utils.Cd("Ignoring wrong type of page title: "+typeof k);return""};
e.$g=function(e,b,k,f,a,g){b=f[b];k=f[k];b&&k?a[e]=k-b:c.log("M142",g,e,b,k)};e.Vd=function(c,b,k){var f={add:function(a,g,q){e.$g(a,g,q,c,b,k);return f}};return f};return e}(f.ta);f.Y=b})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(f){(function(f){function b(d,f){var a,l=f[d],s=typeof l;m.push(d);var w=p[m.join(".")];if(c.utils.isFunction(w))a=w(l);else switch(s){case "string":a="number"==typeof w?r(l,e,w):e(l);break;case "number":a=t(l);break;case "object":if(l)if(c.utils.isArray(l))a=
g(l,!1,!1);else{a=[];for(var z in l)q(l,z)&&(s=b(z,l))&&(k(d)&&"timestamp"==z||a.push({cb:z,v:s}));if(k(d)){for(l=0;l<a.length;l++)a[l]=a[l].cb+e(":")+a[l].v;a=e("{")+a.join(e(","))+e("}")}else{for(l=0;l<a.length;l++)z=a[l],s=z.cb,c.conf.useDebugBeaconParams||((w=c.conf.re[z.cb])?s=w:c.error("M143",z.cb)),a[l]=s+"="+z.v;a=a.join("&")}}else a=null}m.pop();return a}function k(c){return"navOrXhrMetrics"==c||"cookieMetrics"==c}function e(c){return"undefined"===typeof c||null===c||0===c.length?null:encodeURIComponent(c)}
function d(c){return encodeURIComponent(encodeURIComponent(c))}function g(c,d,f){void 0===d&&(d=!1);void 0===f&&(f=!0);if(0===c.length)return null;var k=[];if(d)k=c;else for(d=0;d<c.length;d++)k.push(b(d,c));return e("[")+k.join(e(","))+e(f?">":"]")}function t(c){c=Math.round(c);c<f.bd&&(c=f.bd);c>f.Zc&&(c=f.Zc);return e(c)}function l(c,b){if(c>b||0>c)c=f.Kg;return e(c)}function r(e,b,d,f){void 0===f&&(f=!0);if("undefined"===typeof e||null===e||0===e.length)return null;var k=3<=d?"...":"";c.assert(d>=
k.length);for(var a=!1,g=null;;){try{g=b(e);if(null===g)return null;if(g.length<=d)break}catch(h){}var t;a?t=e.length-1:(a=!0,t=d-=k.length);var l=f?0:Math.max(e.length-t,0);e=e.substr(l,t)}a&&(g=f?g+k:k+g);return g}function s(e,b,d){if(0==d)return f.sb;if(b<e)return 0;b=f.sb+(b-e)/d;c.assert(b>=f.sb);c.log("M144",e,b);return b}var q=c.utils.hasOwnPropertyDefined;f.Kg=-1;f.va=180;f.Cg=50;f.Dg=50;f.wg=40;f.sb=50;f.kg=50;f.ad=128;f.rg=30;f.sg=30;f.qg=30;f.yg=8;f.bd=-99999;f.Zc=999999;f.lg=2E3;f.Bg=
2;f.mg=99999999;var p={".pageUrl":f.va,".parentPageUrl":f.va,".pageReferrer":f.va,".pageTitle":f.Cg,".userPageName":f.Dg,".geoCountry":f.rg,".geoRegion":f.sg,".geoCity":f.qg,".localIP":f.yg,".otherClientRequestGUIDs":function(c){c=c||[];var e=c.slice(0,f.Bg);return g(e,!1,e.length<c.length)},".btTime":function(e){e=e||[];for(var b=e.slice(0,c.conf.og),d=[],k=0;k<b.length;k++){var r=b[k];d.push(g([l(Number(r[0]),f.mg),t(r[1]),t(r[2])],!0,!1))}return g(d,!0,b.length<e.length)},".ajaxError":function(c){return g([d(c[0]),
r(c[1],d,f.kg)],!0,!1)},".userData":function(c){c=c||[];for(var e=!1,b=0,k=[],t=0;t<c.length;t++){var l=c[t];k[t]=g([d(l[0]),d(l[1])],!0,!1);b+=k[t].length;if(b>f.ad){e=!0;break}}for(;;){c=g(k,!0,e);if(null===c||c.length<=f.ad)return c;k.pop();e=!0}}},m=[];f.Jh=function(e,k){void 0===k&&(k=!0);m=[];var l=[];e.errors&&(l=e.errors,e.errors=null);var p=b("",{"":e});if(l&&0<l.length){for(var q=s(k?870:354,f.lg-p.length,l.length),w=[],z=0;z<l.length;z++){var u=l[z],x=c.utils.N.Kd(c.utils.N.aa(u[0]));w.push(g([r(x,
d,f.wg,!1),c.utils.Xb(u[1])?t(u[1]):-1,r(u[2],d,q)],!0,!1))}l=g(w,!0,!1);p+="&errors="+l}return p};f.el=b;f.El=e;f.element=d;f.Zk=g;f.al=t;f.tl=l;f.truncate=r;f.Al=s})(f.Wc||(f.Wc={}))})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){(function(f){var a=c.utils.hasOwnPropertyDefined,b=c.utils.Va,k=function(){function e(){}e.prototype.eb=function(b,k,a){for(var g=[],r=[],s=null,q=[],p=0;p<b.length;p++){var m=b[p];if(m.eventType===c.EventType.Error){var u=m;r.push([m.eventUrl+"",c.utils.Xb(u.line)?
u.line:void 0,u.message+""])}else{if(m.eventType===c.EventType.PageView||m.eventType===c.EventType.IFRAME)s=m;q.push(m)}}b=q;if(0<r.length)for(s&&(s.errors=r.splice(0,c.conf.ug));0<r.length;)m=r.splice(0,c.conf.tg),s=e.uj.n(),s.errors=m,s.isErrorEvent=!0,s.cookieMetrics=null,s.metrics=null,s.resourceTimingInfo=null,b.push(s);for(r=0;r<b.length;r++)m=b[r],c.log("M145"),m.resourceTimingInfo=null,e.Vh(m,k,a),s=e.transform(m),g.push(f.Wc.Jh(s,m.isErrorEvent));return g};e.Vh=function(e,b,f){e.ver=this.fg;
e.rootGUID=f.Ob();e.geo=b;e.dataType="R";e.eom=1;e.eumAppKey=c.conf.appKey;e.PLC=1};e.transform=function(c){var b={};e.zf("",{"":c},b);return b};e.zf=function(d,f,k){f=f[d];var g=e.ne[d];if("metrics"===d||"cookieMetrics"===d)k[g]=f;else if("btTime"===d&&c.utils.isArray(f)&&0<f.length){d=[];for(var r=0;r<f.length;r++)d.push([f[r].id,f[r].duration,f[r].ert]);k[g]=d}else if("userData"===d&&c.utils.isObject(f))k[g]=c.utils.dj(f);else if(!(d&&-1<b(["userDataLong","userDataDouble","userDataBoolean","userDataDate"],
d)))if(c.utils.isObject(f))for(r in f)a(f,r)&&e.zf(r,f,k);else e.ne[d]&&("eventType"===d&&f>c.EventType.Ajax&&(f=c.utils.compareWindows(top,window)?c.EventType.PageView:c.EventType.IFRAME),k[g]=f)};return e}();k.uj=new c.events.Y;k.fg=3;k.ne={eumAppKey:"eumAppKey",userPageName:"userPageName",rootGUID:"baseGUID",parentGUID:"parentGUID",parentUrl:"parentPageUrl",parentType:"parentPageType",parentLifecyclePhase:"parentLifecyclePhase",eventType:"pageType",eventUrl:"pageUrl",pageReferrer:"pageReferrer",
pageTitle:"pageTitle",metrics:"navOrXhrMetrics",xhrMetrics:"navOrXhrMetrics",resourceTimingInfo:"resourceTimingInfo",cookieMetrics:"cookieMetrics",userData:"userData",errors:"errors",ajaxError:"ajaxError",dataType:"dataType",country:"geoCountry",region:"geoRegion",city:"geoCity",localIP:"localIP",ver:"ver",eom:"eom",eventGUID:"clientRequestGUID",otherClientRequestGUIDs:"otherClientRequestGUIDs",btTime:"btTime",serverSnapshotType:"serverSnapshotType",hasEntryPointErrors:"hasEntryPointErrors"};f.ig=
k})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){(function(c){var a=function(){function c(){}c.prototype.send=function(){};return c}();c.Yf=a})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){(function(c){var a=function(){function c(){}c.prototype.eb=function(){return null};return c}();c.Xf=a})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){(function(f){var a=function(){function b(c){this.aj=c}b.prototype.send=function(b){try{this.aj.postMessage(c.utils.ia(b)),c.log("M146"),f.numBeaconsSent+=
1}catch(e){c.log("M147")}};return b}();f.cg=a})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){(function(f){var a=function(){function b(){}b.prototype.eb=function(b,e,d){b={ver:c.conf.agentVer,dataType:"R",rootGUID:d.Ob(),events:b};c.channel.M.K&&c.channel.M.K.id&&(b.agentId=c.channel.M.K.id);e&&(b.geo=e);return b};return b}();f.bg=a})(c.beacons||(c.beacons={}))})(g||(g={}));(function(c){var f=function(){function f(){}f.Bd=function(b,f){if(!b)return null;var e=b.ADRUM.lifecycle;if(!e||!e.getPhaseCallbackTime)return null;
var d=f.getPhaseCallbackTime("AT_ONLOAD"),e=e.getPhaseCallbackTime("AT_ONLOAD"),a=null==e;return null==d?(c.error("M148"),null):c.lifecycle.getPhaseID(a||d<=e?"AFTER_FIRST_BYTE":"AFTER_ONLOAD")};return f}();c.Mg=f;c.cPLPI=f.Bd})(g||(g={}));(function(c){(function(f){var a=function(){function b(b){this.ready=!1;this.geoResolverUrl=b;c.utils.isString(b)&&0<c.utils.trim(b).length&&(this.geoResolverUrl=b+"/resolve.js")}b.prototype.init=function(b){this.channel=b;if(this.Eb()&&(c.geo=this.Dc(),this.hh()||
this.gb()&&!this.Pd())){this.channel.uc(c.geo.result);return}this.Pd()?(this.gb()&&(this.geoResolverUrl+="?ip="+encodeURIComponent(c.conf.userConf.geo.localIP)),this.sk()):(this.ready=!0,this.channel.onResolverReady())};b.prototype.Eb=function(){return c.utils.isDefined(c.conf.userConf)&&c.utils.isDefined(c.conf.userConf.geo)};b.prototype.gb=function(){return this.Eb()&&c.utils.isDefined(c.conf.userConf.geo.localIP)&&!c.utils.isDefined(c.conf.userConf.geo.city)&&!c.utils.isDefined(c.conf.userConf.geo.region)&&
!c.utils.isDefined(c.conf.userConf.geo.country)};b.prototype.Dc=function(){return{failed:!1,result:{country:c.conf.userConf.geo.country,region:c.conf.userConf.geo.region,city:c.conf.userConf.geo.city,localIP:this.Lh(c.conf.userConf.geo.localIP)}}};b.prototype.hh=function(){return this.Eb()&&c.utils.isDefined(c.conf.userConf.geo.localIP)&&c.utils.isDefined(c.conf.userConf.geo.city)&&c.utils.isDefined(c.conf.userConf.geo.region)&&c.utils.isDefined(c.conf.userConf.geo.country)};b.prototype.Pd=function(){return c.utils.isString(this.geoResolverUrl)&&
0<c.utils.trim(this.geoResolverUrl).length};b.prototype.Lh=function(c){if(null==c||0==c.length)return null;c=c.split(".");if(4!=c.length)return null;for(var e=[],b=0;b<c.length;b++){var f=parseInt(c[b]);if(isNaN(f)||0>f||255<f)return null;e.push(("0"+f.toString(16)).slice(-2))}return e.join("")};b.prototype.sk=function(){c.geo={failed:!1,result:null};c.utils.loadScriptAsync(this.geoResolverUrl);var f=this;c.utils.tryPeriodically(b.zb,function(){return f.isReady()},function(){f.onReady()},function(){f.ec()})};
b.prototype.isReady=function(){this.ready||(this.ready=c.geo&&(c.geo.failed||null!==c.geo.result))||c.log("M149");return this.ready};b.prototype.onReady=function(){c.geo.failed?(c.log("M150"),this.gb()&&(c.geo=this.Dc())):(c.assert("object"===typeof c.geo.result),c.log("M151",c.geo.result));c.geo&&c.geo.result&&this.channel.uc(c.geo.result);this.channel.onResolverReady()};b.prototype.ec=function(){c.log("M152");this.gb()&&(c.geo=this.Dc());c.geo&&c.geo.result&&this.channel.uc(c.geo.result);this.ready=
!0;this.channel.onResolverReady()};b.zb=function(c){return 10<c?-1:[1,50,100,500][c-1]||1E3};return b}();f.ag=a})(c.channel||(c.channel={}))})(g||(g={}));(function(c){(function(f){var a=function(){function b(){this.ready=!1;this.channel=null}b.prototype.init=function(f){this.channel=f;b.O=b.O||b.Xh();this.ready=!b.O.ab;var e=this;if(b.O.ab)c.log("M153"),b.O.ab.ADRUM.command("listenForOkToSendChildFrameBeacons",function(){c.log("M154");e.ready=!0;e.onReady()});else e.onReady()};b.prototype.isReady=
function(){this.isReady||c.log("M155");return this.ready};b.prototype.onReady=function(){this.channel.Tj(b.O);this.channel.onResolverReady()};b.Xh=function(){for(var f=window,e,d=b.le(f),a=window;a.parent&&!c.utils.compareWindows(a,a.parent);){a=a.parent;d=d||b.le(a);try{a.ADRUM&&(e=e||a,f=a)}catch(g){}}return new c.Lf(e,f,d)};b.le=function(c){try{return 0==c.document.URL.indexOf("https:")}catch(e){}};return b}();a.O=null;f.Mf=a})(c.channel||(c.channel={}))})(g||(g={}));(function(c){(function(f){var a=
function(){function b(e,d,k){this.events=[];this.ea={};this.Ya=NaN;this.$=[];this.Lb=!1;this.bufferMode=c.conf.userConf&&c.conf.userConf.channel&&"undefined"!==typeof c.conf.userConf.channel.bufferMode?c.conf.userConf.channel.bufferMode:!0;this.jb=-1;this.kk=e;this.mh=d;this.$.push(new f.ag(k));this.$.push(new f.Mf);this.$.push(new f.M)}b.create=function(){var e,d;e=b.zj();c.utils.isDefined(e)?(e=new c.beacons.cg(e),d=new c.beacons.bg):b.Gi()&&!c.conf.sendImageBeacon?(e=new c.beacons.Of,d=new c.beacons.Nf):
b.Mi()?(e=new c.beacons.Yf,d=new c.beacons.Xf):(e=new c.beacons.jg,d=new c.beacons.ig);e=new b(e,d,c.conf.geoResolverUrl);e.init();return e};b.prototype.init=function(){for(var c=0;c<this.$.length;c++)this.$[c].init(this)};b.prototype.xk=function(b){return(b.parentType===c.EventType.VPageView||b.parentType===c.EventType.NG_VIRTUAL_PAGE)&&null!==this.ea[b.parentGUID]};b.prototype.lc=function(b){var d=b.parentGUID;if(!c.utils.isDefined(this.ea[d])){this.ea[d]=[];var f=this,k=b.eventGUID;c.utils.oSTO(function(){f.Hb(k)},
1E4)}this.ea[d].push(b)};b.prototype.report=function(e){e.sequenceId=c.ef.zd.di();e.timestamp=e.timestamp||c.utils.now();this.xk(e)?this.lc(e):this.events.push(e);1==this.events.length&&(this.Ya=c.utils.now());var d=e.parentGUID;d&&d!==c.correlation.getPageGUID()||this.Hb(e.eventGUID);b.Ki(e)&&(this.Lb=!0);this.Cc()};b.prototype.Hb=function(c){var b=this.ea[c];this.ea[c]=null;if(b&&0!=b.length)for(this.events=this.events.concat(b),c=0;c<b.length;c++)this.Hb(b[c].eventGUID)};b.prototype.Oi=function(b){return b.eventType===
c.EventType.PageView||b.eventType===c.EventType.IFRAME};b.prototype.nh=function(){for(var b=0;b<this.events.length;b++){var d=this.events[b];if(this.Oi(d)){var f=this.O.ab;f&&(d.parentLifecyclePhase=c.Mg.Bd(f,c.lifecycle),d.parentGUID=f.ADRUM.correlation.getPageGUID(),d.parentUrl=c.utils.N.aa(f.document.URL),c.utils.isDefined(f.ADRUM.events.Y.userPageName)&&(d.vj=f.ADRUM.events.Y.userPageName),d.parentType=c.utils.compareWindows(top,f)?c.EventType.PageView:c.EventType.IFRAME)}}};b.prototype.Qj=function(){if(0==
this.events.length)c.log("M156");else{this.nh();var b=this.Ec(this.events,this.O),d=this.mh.eb(this.events,this.Wh,this.O);this.kk.send(d,b);this.events=[];this.Ya=Number.POSITIVE_INFINITY}};b.prototype.onResolverReady=function(){this.Cc()};b.prototype.uc=function(c){this.Wh=c};b.prototype.Tj=function(c){this.O=c};b.prototype.addResolver=function(c){c.init(this);this.$.push(c)};b.prototype.Jj=function(){for(var c=0;c<this.$.length;c++)if(!this.$[c].isReady())return!1;return!0};b.prototype.me=function(){return this.Ya+
c.conf.Yc>c.utils.now()};b.prototype.Ti=function(){return this.events.length<c.conf.pg};b.prototype.Ec=function(b,d){if(d.Ec||c.conf.userConf.useHTTPSAlways)return!0;for(var f=0;f<b.length;f++){var k=b[f];if(k.eventUrl&&0==k.eventUrl.indexOf("https:"))return!0}return!1};b.prototype.getEventsWithParentGUID=function(c){return this.ea[c]||[]};b.prototype.Cc=function(){if(this.Jj())if(this.wk()){if(this.me()&&0<this.events.length&&0>this.jb){var b=this;this.jb=c.utils.oSTO(function(){b.Cc()},c.conf.Yc+
this.Ya-c.utils.now())}}else clearTimeout(this.jb),this.jb=-1,this.Lb=!1,this.Qj()};b.Ki=function(b){return b.eventType===c.EventType.PageView||b.eventType===c.EventType.IFRAME||b.eventType===c.EventType.VPageView};b.prototype.wk=function(){return this.bufferMode&&!this.Lb&&this.Ti()&&this.me()};b.Gi=function(){var c="undefined"!==typeof JSON&&!(!JSON||!JSON.stringify);return window.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest&&c};b.Aj=function(){if(c.utils.isDefined(window.webkit)&&c.utils.isDefined(window.webkit.messageHandlers)&&
c.utils.isDefined(window.webkit.messageHandlers.ADEUM_js_handler)&&c.utils.isFunction(window.webkit.messageHandlers.ADEUM_js_handler.postMessage))return window.webkit.messageHandlers.ADEUM_js_handler};b.yj=function(){if(c.utils.isDefined(window.ADEUM_js_handler)&&c.utils.isFunction(window.ADEUM_js_handler.postMessage))return window.ADEUM_js_handler};b.zj=function(){return b.yj()||b.Aj()};b.Mi=function(){return c.conf.userConf&&c.conf.userConf.beacon&&c.conf.userConf.beacon.neverSendImageBeacon};return b}();
f.Channel=a;var b=a.create();f.report=function(c){b.report(c)};f.addResolver=function(c){b.addResolver(c)};f.getEventsWithParentGUID=function(c){return b.getEventsWithParentGUID(c)}})(c.channel||(c.channel={}))})(g||(g={}));(function(c){var f=function(){function f(){}f.Ke=function(){return window.attachEvent?c.utils.isFunction(window.addEventListener)?this.Fc:this.pf:this.uf};f.Je=function(b){var k=null;switch(f.Ke()){case f.uf:k=b.parentPhase();break;case f.Fc:k=c.lifecycle.findPhaseAtNominalTime(b.getSendTime());
break;case f.pf:k=null}return c.lifecycle.getPhaseID(k)};return f}();f.uf="uCT";f.Fc="uNET";f.pf="tIA";c.Ta=f})(g||(g={}));(function(c){(function(f){var a=c.utils.hasOwnPropertyDefined,b=function(b){function e(){return null!==b&&b.apply(this,arguments)||this}u(e,b);e.prototype.n=function(d){var a=b.prototype.n.call(this,d,e.getContext(d)),g=d.url(),l=d.getSendTime(),r=d.getFirstByteTime(),s=d.getRespAvailTime(),q=d.getRespProcTime(),p=d.parentPhase();c.assert(null!==g&&null!==l&&null!==r&&null!==
s&&null!==q,"M157",g,l,r,s,q,p);if(null===g||null===l||null===r||null===s||null===q)return null;g=d.error();l={PLC:1,FBT:r-l,DDT:s-r,DPT:q-s,PLT:q-l,ARE:g?1:0};r=d.url();if(!e.$i(l,r))return null;c.conf.viz&&c.utils.Ib(document.getElementById(c.conf.viz),l,"xhr");c.utils.mergeJSON(a,{eventUrl:c.utils.N.aa(r),parentType:d.parentType()||c.EventType.PageView,parentLifecyclePhase:d.parentPhaseId(),metrics:l,method:d.method(),timestamp:d.timestamp(),ajaxError:g&&[g.status,g.msg||""],xhrStatus:d.xhrStatus()});
(d=d.parameter())&&f.R.dc(d,c.conf.wa)?a.parameter=d:d&&f.R.pa("parameter",c.conf.wa);a.parentType==c.EventType.VPageView&&c.conf.spa2?c.utils.ff(a,f.Qa.userPageName):c.utils.ff(a,f.Y.userPageName);return a};e.getContext=function(c){return{url:c.url()||"",method:c.method()||""}};e.$i=function(b,e){for(var f in b)if(a(b,f)){var k=b[f];if(0>k)return c.error("M158",f,k,e),!1}return!0};return e}(f.ta);f.kd=b})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(f){var a=function(b){function f(){return null!==
b&&b.apply(this,arguments)||this}u(f,b);f.prototype.n=function(e){var f=b.prototype.n.call(this,e);f&&(e=e.allResponseHeaders()||"",(e=c.correlation.oi(e))&&null!==e&&null!==e.clientRequestGUID&&(f.eventGUID=e.clientRequestGUID),null!==e&&(f.serverMetadata=e));return f};return f}(f.kd);f.Ff=a})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(f){var a=function(b){function k(){return null!==b&&b.apply(this,arguments)||this}u(k,b);k.prototype.n=function(e){var d=b.prototype.n.call(this,
e);c.utils.mergeJSON(d,{message:e.msg()||"",timestamp:e.timestamp(),line:e.line(),stack:e.stack(),parentPageName:d.parentType==c.EventType.VPageView&&c.conf.spa2?f.Qa.userPageName:f.Y.userPageName});c.utils.isString(d.userPageName)||(d.userPageName=d.vj);return d};return k}(f.ta);f.Lg=a})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(c){(function(a){a.build=function(b){var k={},e=c.metricSpec[b.type()],d;for(d in e){var a=e[d];if(null!==a.name&&b.perf.getEntryByName(a.start)&&b.perf.getEntryByName(a.end)){b.perf.measure(a.name,
a.start,a.end);var g=b.perf.getEntryByName(a.name);k[a.name]=g&&0<=g.duration&&g.duration||null}}for(d in k)k[d]=Math.round(k[d]);return k}})(c.tb||(c.tb={}))})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(c){var a=function(b){function a(){return null!==b&&b.apply(this,arguments)||this}u(a,b);a.prototype.n=function(e){var d=b.prototype.n.call(this,e),a=c.tb.build(e);a.PLC=1;d.metrics=a;d.timestamp=e.timestamp();e=(new c.ResourceTimingInfoBuilder(e.resTiming())).build();null!==e&&(d.resourceTimingInfo=
e);return d};return a}(c.ta);c.jd=a})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(f){var a=function(b){function a(){return null!==b&&b.apply(this,arguments)||this}u(a,b);a.setPageName=function(c){a.ca=c};a.prototype.n=function(e){var d=b.prototype.n.call(this,e),g=f.tb.build(e);g.PLT=e.timestamp()-e.startTime;g.PLC=1;d.metrics=g;d.timestamp=e.timestamp();g=(new f.ResourceTimingInfoBuilder(e.resTiming(),null,e.startTime)).build();null!==g&&(d.resourceTimingInfo=g);c.utils.isDefined(e.userPageName)&&
(d.userPageName=e.userPageName);!c.utils.isDefined(d.userPageName)&&c.utils.isDefined(a.ca)&&(f.R.zc("userPageName",a.ca,c.conf.xa)?d.userPageName=a.ca:f.R.pa("userPageName",c.conf.xa),a.ca=void 0);return d};return a}(f.ta);f.Qa=a})(c.events||(c.events={}))})(g||(g={}));(function(c){(function(c){var a=function(c){function f(){return null!==c&&c.apply(this,arguments)||this}u(f,c);f.prototype.n=function(e){var f=c.prototype.n.call(this,e);f&&(f.metrics.VDC=e.digestCount()||0);return f};return f}(c.jd);
c.Jg=a})(c.events||(c.events={}))})(g||(g={}));(function(c){var f=c.events||(c.events={});f.Re={};c=[{ja:f.PageView,ha:new f.Y},{ja:f.Ajax,ha:new f.kd},{ja:f.AdrumAjax,ha:new f.Ff},{ja:f.Error,ha:new f.Lg},{ja:f.VPageView,ha:new f.jd},{ja:f.AnySpaVPageView,ha:new f.Qa},{ja:c.ng.NgVPageView,ha:new f.Jg}];for(var a=0;a<c.length;a++){var b=c[a];b.ja.prototype._regId=a;f.Re[a]=b.ha}})(g||(g={}));(function(c){(function(f){f.reportEvent=function(f){var b=c.events.Re[f._regId];b?(b=b.n(f),c.utils.isDefined(b)&&
(c.log("M160",c.EventType[f.type()]),c.channel.report(b))):c.log("M159",c.EventType[c.eventType])}})(c.reporter||(c.reporter={}))})(g||(g={}));(function(c){(function(f){var a=function(){function b(){this.ready=!1}b.prototype.onReady=function(){this.ready=!0;this.channel.onResolverReady()};b.prototype.isReady=function(){this.ready||c.log("M161");return this.ready};b.prototype.init=function(c){this.channel=c};return b}();f.Xg=a})(c.commands||(c.commands={}))})(g||(g={}));(function(c){(function(f){var a=
new f.Xg;c.channel.addResolver(a);f.marks={};f.mark=function(b,a){c.log("M162",b,a);f.marks[b]=a};f.reportOnload=function(b){c.utils.oSTO(function(){c.log("M163");c.reporter.reportEvent(b);a.onReady()},c.conf.Vg)};f.reportEvent=function(){c.reporter.reportEvent.apply(c.reporter,arguments)};f.addResolver=function(b){c.channel.addResolver(b)};f.reportXhr=function(b){c.log("M164");c.Ta.Ke()==c.Ta.Fc?(c.log("M165"),c.utils.oSTO(function(){b.parentPhaseId(c.Ta.Je(b));c.reporter.reportEvent(b)},0)):(b.parentPhaseId(c.Ta.Je(b)),
c.reporter.reportEvent(b))};f.listenForOkToSendChildFrameBeacons=function(b){c.log("M166");try{b()}catch(f){c.exception(f,"M167")}};f.reportPageError=function(b){var f=b.url();c.log("M168",b.msg(),f,b.line(),b.stack());f&&0!==f.length||b.url("CROSSORIGIN");c.reporter.reportEvent(b)};f.setPageName=function(b){c.utils.isString(b)&&0<c.utils.trim(b).length&&c.events.Y.setPageName(b)};f.setVirtualPageName=function(b){c.utils.isString(b)&&0<c.utils.trim(b).length&&c.conf.spa2&&c.events.Qa.setPageName(b)};
f.addUserData=function(b,f){c.events.Y.addUserData(b,f)};f.call=function(c){c()}})(c.commands||(c.commands={}))})(g||(g={}));(function(c){(function(c){c.Jd=function(a){return encodeURIComponent(c.N.oj(a))}})(c.utils||(c.utils={}))})(g||(g={}));(function(c){(function(f){var a=function(){function b(){}b.prototype.setUp=function(){this.fj();c.utils.addEventListener(window,"pagehide",b.Kc);c.utils.addEventListener(window,"beforeunload",b.Kc);c.utils.addEventListener(window,"unload",b.Kc)};b.prototype.fj=
function(){(this.startTime=b.Th()||b.Sh())&&c.commands.mark("starttime",this.startTime)};b.Th=function(){var b;try{if(c.utils.isDefined(window.external)&&c.utils.isNumber(window.external.pageT))b=(new Date).getTime()-window.external.pageT;else if(c.utils.isDefined(window.gtbExternal)&&c.utils.isFunction(window.gtbExternal.pageT)){var e=window.gtbExternal.pageT();c.utils.isNumber(e)&&(b=(new Date).getTime()-e)}else c.utils.isDefined(window.chrome)&&c.utils.isFunction(window.chrome.csi)&&(e=window.chrome.csi(),
c.utils.isDefined(e)&&c.utils.isNumber(e.pageT)&&(b=(new Date).getTime()-e.pageT));b&&(b=Math.round(b),c.log("M169",b))}catch(f){c.exception(f,"M170")}return b};b.Sh=function(){var b=c.correlation.startTimeCookie;if(b){c.log("M171",b.startTime,b.startPage);var e=c.utils.Jd(document.referrer);if(e===b.startPage)if(isNaN(b.startTime))c.log("M172",b.startTime);else return b.startTime;else c.log("M173",e,b.startPage)}else c.log("M174")};b.Hh=function(b,e){var f=document.domain,a="https:"===document.location.protocol,
g="ADRUM=s="+Number(new Date)+"&r="+c.utils.Jd(document.location.href),l=g+";path=/";a&&(l+=";secure");c.log("M175",g);if(!c.conf.useStrictDomainCookies){for(var a=b(),f=f.split("."),r="",s=f.length-1;0<=s;s--){r="."+f[s]+r;c.log("M176",r);e(l+";domain="+r);var q=b();if(q!=a&&0<=q.indexOf(g)){c.log("M177");c.log("M178");return}}c.log("M179")}c.log("M180");e(l);c.log("M181")};return b}();a.Kc=function(){var c=!1;return function(){c||(c=!0,a.Hh(function(){return document.cookie},function(c){document.cookie=
c}))}}();f.Pk=a;f.dk=new a})(c.monitor||(c.monitor={}))})(g||(g={}));(function(c){c.log("M182");c.monitor.dk.setUp();c.q.processQ();c.initEXTDone=!0;c.log("M183")})(g||(g={}))}};})();
