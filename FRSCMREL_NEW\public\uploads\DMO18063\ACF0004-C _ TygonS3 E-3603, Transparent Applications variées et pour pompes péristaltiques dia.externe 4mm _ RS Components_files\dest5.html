<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<!-- saved from url=(0174)https://electro.demdex.net/dest5.html?d_nsid=0#https%3A%2F%2Ffr.rs-online.com%2Fweb%2Fp%2Fproducts%2F313-9482%2F%23%26intcmp%3Dfr-web-_-selection-guide-_-mar-18-_-saintgobain -->
<html lang="en-US"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><title>Adobe AudienceManager</title><script type="text/javascript">
var Demdex={version:"6.0",dest:"5",PROTOCOL:"https:"==document.location.protocol?"https:":"http:",COOKIE_DOMAIN:function(){var a=document.domain;/demdex\.net$/i.test(a)&&(a=".demdex.net");return a}(),SIX_MONTHS_IN_MINUTES:259200,THREAD_YIELDING_DELAY:100,errorReportingEnabled:!1,sent:[],errored:[],timesDextpWasCleared:0,dpids:null,cbmacros:["%timestamp%","%rnd%","%did%","%http_proto%"],validators:{isPopulatedString:function(a){return"string"==typeof a&&a.length}},addListener:function(){if(document.addEventListener)return function(a,
b,c){a.addEventListener(b,function(a){"function"==typeof c&&c(a)},!1)};if(document.attachEvent)return function(a,b,c){a.attachEvent("on"+b,function(a){"function"==typeof c&&c(a)})}}(),replaceMacro:function(a){var b;if("%rnd%"==a||"%timestamp%"==a)return""+(new Date).getTime();if("%did%"==a){if(this.dpids===Object(this.dpids)&&this.validators.isPopulatedString(this.dpids.uuid))return this.dpids.uuid;if(b=this.getCookie("demdex"))return b.replace(/==/g,"").replace(/:/,"-")}return"%http_proto%"==a?"https:"==
document.location.protocol?"https":"http":a},canSetCookie:function(){var a,b;return navigator.cookieEnabled&&(a=this.getCookie("demdex"),a||(this.setCookie("_dp","1",1,"/",this.COOKIE_DOMAIN,!1),b=this.getCookie("_dp")),a||b)?(this.setCookie("_dp","1",-1E3,"/",this.COOKIE_DOMAIN,!1),!0):!1},getCookie:function(a){a+="=";var b=document.cookie.split(";"),c,g,d;c=0;for(g=b.length;c<g;c++){for(d=b[c];" "==d.charAt(0);)d=d.substring(1,d.length);if(0==d.indexOf(a))return decodeURIComponent(d.substring(a.length,
d.length))}return null},setCookie:function(a,b,c,g,d,e){var f=new Date;c&&(c*=6E4);document.cookie=a+"="+b+(c?";expires="+(new Date(f.getTime()+c)).toUTCString():"")+(g?";path="+g:"")+(d?";domain="+d:"")+(e?";secure":"")},sendMessage:function(a){a=encodeURIComponent("---destpub-to-parent---")+a;this.xd.parentUrl||(this.xd.parentUrl=decodeURIComponent(document.location.hash.replace(/^#/,"")));this.xd.postMessage(a,this.xd.parentUrl,parent);return a},onMessage:function(a){try{var b=/^---destpub-debug---/,
c=/^---destpub---/,g=/^---destpub-combined---/,d=/^---destpub-clear-dextp---/;if("string"!==typeof a)return"Invalid message received";d.test(a)&&(this.setCookie("dextp","",-1E3,"/",this.COOKIE_DOMAIN,!1),this.timesDextpWasCleared++,a=a.replace(d,""));if(b.test(a)||c.test(a)||g.test(a)){if(b.test(a))this.errorReportingEnabled=!0,a=a.replace(b,"");else if(c.test(a))a=a.replace(c,"");else if(g.test(a)){a=a.replace(g,"");var e=a.split("%01");this.processThreadYieldedMessages(e);return}this.processMessage(a)}else return"Invalid message received"}catch(f){this.fireErrorPixel("Error in Demdex.onMessage function: "+
f.message)}},processThreadYieldedMessages:function(a){var b=this;this.processMessage(a.shift());a.length&&setTimeout(function(){b.processThreadYieldedMessages(a)},this.THREAD_YIELDING_DELAY)},processMessage:function(a){a=a.split("|");var b=decodeURIComponent,c=b(a[0]),b={destType:c,id:b(a[1]),tag:b(a[2]),urls:"string"==typeof a[3]?a[3].split(","):[],ttl:a[4]?b(a[4]):0,shouldFireWhenCantSet3rdPartyCookie:"true"===a[7]?!0:!1};b.urls.length&&("dests"==c?this.fireTags(b):"ibs"==c&&(b.dpType=1,this.setDPIds(a[6]),
this.processIBS(b)))},setDPIds:function(a){var b=decodeURIComponent,c=this.validators.isPopulatedString;c(a)&&(a=a.split(","),this.dpids={uuid:a[0]?b(a[0]):null,dpid:a[1]?b(a[1]):null,dpuuid:a[2]?b(a[2]):null})},fireTags:function(a,b){try{var c=this,g=a.tag,d=a.urls,e,f,h,k,l,p,m,n;if(d instanceof Array&&(k=d.length))for(h=0;h<k;h++)if(e=d[h]){e=decodeURIComponent(e);b&&this.protocolIsPrependable(e)&&(e=this.PROTOCOL+e);l=0;for(p=this.cbmacros.length;l<p;l++)m=this.cbmacros[l],e=e.replace(new RegExp(m,
"gi"),this.replaceMacro(m));n=function(a){return function(){c.errored.push(a)}}(e);"img"==g&&(f=new Image,c.addListener(f,"abort",n),c.addListener(f,"error",n),f.src=e,this.sent.push(e))}}catch(q){this.fireErrorPixel("Error in Demdex.fireTags function: "+q.message+";tag="+g+";url="+e)}},protocolIsPrependable:function(a){return/^\/\//.test(a)},processIBS:function(a){try{var b=this.getCookie("demdex")||"",c=this.getCookie("DexLifeCycle")||"",g=this.getCookie("dextp"),d=!1,e=!1,f,h,k,l,p,m,n;if(!(b.match(/^DID:/)||
b.match(/^NOTARGET:/)||b.match(/dv2:jY0wRKU\/M28\/lvlLHbINBA==/)||c.match(/^NOTARGET/)))if(Demdex.canSetThirdPartyCookies)if(g){f=g.split("|");k=0;for(l=f.length;k<l;k++)if(h=f[k],h.match("^"+a.id+"-"+a.dpType+"-")){d=!0;p=h.split("-")[2];m=new Date;m.setTime(p);n=(new Date).getTime();n-m.getTime()<6E4*a.ttl?e=!0:f.splice(k,1);break}d&&e||(this.fireTags(a,!0),f.push(a.id+"-"+a.dpType+"-"+(new Date).getTime()),this.setCookie("dextp",f.join("|"),this.SIX_MONTHS_IN_MINUTES,"/",this.COOKIE_DOMAIN,!1))}else this.fireTags(a,
!0),this.setCookie("dextp",a.id+"-"+a.dpType+"-"+(new Date).getTime(),this.SIX_MONTHS_IN_MINUTES,"/",this.COOKIE_DOMAIN,!1);else a.shouldFireWhenCantSet3rdPartyCookie&&this.fireTags(a,!0)}catch(q){this.fireErrorPixel("Error in Demdex.processIBS function: "+q.message)}},fireErrorPixel:function(a,b){try{if(!this.errorReportingEnabled)return"Destpub error reporting not enabled";if(!b){var c=encodeURIComponent;(new Image).src=this.PROTOCOL+"//error.demdex.net/event?d_px=14137&d_ld="+c("site="+c(document.location.href)+
"&message="+c(a)+"&_ts="+(new Date).getTime())}return"Destpub error report sent"}catch(g){}},xd:function(){var a=5,b,c,g=/^#?\d+&/,d,e;return{postMessage:function(a,b,c){b&&window.postMessage&&c.postMessage(a,b.replace(/([^:]+:\/\/[^\/]+).*/,"$1"))},receiveMessage:function(f,h){try{if(window.postMessage)if(f&&(d=function(a){if("string"===typeof h&&a.origin!==h||"[object Function]"===Object.prototype.toString.call(h)&&!1===h(a.origin))return!1;f(a)}),window.addEventListener)window[f?"addEventListener":
"removeEventListener"]("message",d,!1);else window[f?"attachEvent":"detachEvent"]("onmessage",d);else"function"==typeof f&&(setTimeout(function(){a=100},35E3),e=function(){b=document.location.hash;b!==c&&g.test(b)&&(c=b,f({data:b.replace(g,"")}));setTimeout(e,a)},e())}catch(k){Demdex.fireErrorPixel("Error in Demdex.xd.receiveMessage function: "+k.message)}}}}()};
(function(){try{Demdex.canSetThirdPartyCookies=Demdex.canSetCookie();Demdex.sendMessage(["canSetThirdPartyCookies",Demdex.canSetThirdPartyCookies].join("|"));var a=decodeURIComponent(document.location.hash.replace(/^#/,"")).split("/");Demdex.xd.demdexOrigin=a[0]+"//"+a[2];Demdex.xd.receiveMessage(function(a){Demdex.onMessage(a.data)},Demdex.xd.demdexOrigin)}catch(b){Demdex.fireErrorPixel("Error in anonymous function for initalizing Demdex.xd.receiveMessage function: "+b.message)}})();
</script></head><body></body></html>