Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){try{var products=window.rs.web.digitalData.products;var stringArray=[];for(var i=0;i<products.length;i++)stringArray.push(products[i].productId.replace("-",""));var productString=stringArray.join(",");return productString}catch(e){return"error"}},transform:function(val){return val?
val:""},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Product IDs",collection:"E-Commerce",source:"Manage",priv:"false"},{id:"49502"})},49502)},-1,-1);Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.on("click",".cartButton",function(){Bootstrapper.ensEvent.trigger("AddToCart - Product Page",this)},true)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){try{return window.rs?window.rs.web.digitalData.page_creation_date:""}catch(e){return"error"}},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Page Creation Date",collection:"Content Pages",source:"Manage",priv:"false"},
{id:"49383"})},49383)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){var DCMSource="";switch(window.location.hostname){case "st1-uk.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/Nxxx.xxxxxx.nso.codesrv/Bxxxxxxx;sz\x3d1x2;ord\x3d";break;case "es.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20963969.218170307;sz\x3d1x2;ord\x3d";break;
case "fr.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20927995.218158441;sz\x3d1x2;ord\x3d";break;case "it.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20931850.218168378;sz\x3d1x2;ord\x3d";break;case "pl.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20930725.218171026;sz\x3d1x2;ord\x3d";break;case "se.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20964455.218166038;sz\x3d1x2;ord\x3d";
break;case "befr.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20929759.218312838;sz\x3d1x2;ord\x3d";break;case "benl.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20929759.218312838;sz\x3d1x2;ord\x3d";break;case "kr.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20931052.218171038;sz\x3d1x2;ord\x3d";break;case "twen.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20963216.218312046;sz\x3d1x2;ord\x3d";
break;case "twcn.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20963216.218312046;sz\x3d1x2;ord\x3d";break;case "th.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20976864.218312847;sz\x3d1x2;ord\x3d";break;case "rsonline.cn":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20928298.218168551;sz\x3d1x2;ord\x3d";break;case "ch.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20929780.218170691;sz\x3d1x2;ord\x3d";
break;case "nz.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20976885.218179619;sz\x3d1x2;ord\x3d";break;case "za.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20964179.218172859;sz\x3d1x2;ord\x3d";break;case "ie.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20976603.218172901;sz\x3d1x2;ord\x3d";break;case "de.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20963243.218172925;sz\x3d1x2;ord\x3d";
break;case "pt.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20976894.218321469;sz\x3d1x2;ord\x3d";break;case "au.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20929807.218318721;sz\x3d1x2;ord\x3d";break;case "hken.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20963246.218172994;sz\x3d1x2;ord\x3d";break;case "hkcn.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20963246.218172994;sz\x3d1x2;ord\x3d";
break;case "at.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20962961.218321541;sz\x3d1x2;ord\x3d";break;case "cz.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20962955.218179631;sz\x3d1x2;ord\x3d";break;case "hu.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20963228.218171345;sz\x3d1x2;ord\x3d";break;case "dk.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20976588.218318595;sz\x3d1x2;ord\x3d";
break;case "nl.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20963231.218179547;sz\x3d1x2;ord\x3d";break;case "no.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20976594.218171098;sz\x3d1x2;ord\x3d";break;case "uk.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20930746.218171701;sz\x3d1x2;ord\x3d";break;case "jp.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20976600.218176220;sz\x3d1x2;ord\x3d";
break;case "my.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20964173.218318649;sz\x3d1x2;ord\x3d";break;case "ph.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20929789.218176199;sz\x3d1x2;ord\x3d";break;case "sg.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20929195.218313330;sz\x3d1x2;ord\x3d";break;case "sg.rs-online.com":DCMSource="https://ad.doubleclick.net/ddm/adj/N721988.197812NSO.CODESRV/B20929195.218313330;sz\x3d1x2;ord\x3d";
break}return DCMSource},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"DCM Natural Search",collection:"All Pages",source:"Manage",priv:"false"},{id:"54239"})},54239)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){try{return window.rs?window.rs.web.digitalData.page_modification_date:""}catch(e){return"error"}},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Page Modification Date",collection:"Content Pages",source:"Manage",priv:"false"},
{id:"49386"})},49386)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){try{var page=window.rs?window.rs.web.digitalData.page_type:"";var product_id=window.rs?window.rs.web.digitalData.product_page_id:"";if(page&&page.match("product")){if(!product_id.match(/^250/)){product_id=product_id.replace("-","");var product_zero_pad="0"+product_id.match(/\d+/)[0];
product_id=product_id.replace(/\d+/,product_zero_pad.slice(-7))}return product_id}}catch(e){return"error"}},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"ProductID - Bloomreach",collection:"Product Page",source:"Manage",priv:"false"},{id:"49485"})},49485)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){var _currentCurrency=Bootstrapper.data.resolve("50332");var _currentCurrencyLetters=Bootstrapper.data.resolve("13153");var cartLength=$(".dataRow").length;var grantTotal=$("table \x3e tbody \x3e tr:nth-child(4) \x3e td.orderValueCell.grandTotalCell").text().trim().split(_currentCurrency)[1];
var cartLines=[];var products=[];var attributes=[];var value=[];for(var i=0;i<cartLength;i++){var currentItem=$(".dataRow:eq("+i+")");var currentItemQty=currentItem.find(".quantityTd input").val();var currentItemValue=currentItem.find("div.lineUnitPricing:first").text().trim().split(_currentCurrency)[1];var currentLineValue=currentItem.find("td.costTd.last \x3e div").text().trim().split(_currentCurrency)[1];var productBrand=currentItem.find("td.descriptionTd \x3e div:nth-child(4)").text().trim().split("Brand")[1].trim();
var productID=currentItem.find("td.descriptionTd \x3e div:nth-child(2) \x3e span.textTitle").text();var productName=currentItem.find("td.descriptionTd \x3e div:nth-child(1) \x3e a").text();attributes[i]={brand:productBrand};products[i]={id:productID,name:productName,description:"",category:"",attributes:attributes[i],variants:[],value:{displayGross:currentItemValue},tax:{},stock:[]};cartLines[i]={id:"",addToCartMethod:"",product:products[i],quantity:currentItemQty,value:{displayGross:currentLineValue},
discount:{}}}var data={id:"",type:"",lines:cartLines,customerSoldTo:"",pmOrderApproval:"",currency:_currentCurrencyLetters,shipping:{},discount:{},value:{displayGross:grantTotal}};return data},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.immediateTrigger,dataDefName:"Cart DataLayer Population",collection:"All Pages",source:"Manage",priv:"false"},{id:"50698"})},50698)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){var site=rs.web.digitalData.store;var env=Bootstrapper.data.resolve("52149");if(site=="cn"&&env=="Production")return"94PUV-GH6RF-TE2Y6-EDBYT-GE5LZ";else if(site=="cn"&&env=="Development")return"SVY7X-M8WJJ-DXSFC-N5MQX-YZK6E";else if(env=="Production")return"3BX8K-35WY6-3F5UV-JX8Z7-DCJ3E";
else return"3BX8K-35WY6-3F5UV-JX8Z7-DCJ3E"},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.immediateTrigger,dataDefName:"Akamai mPulse - API Key",collection:"All Pages",source:"Manage",priv:"false"},{id:"52145"})},52145)},-1,-1);
Bootstrapper.bindDOMParsed(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;var floodlight=document.createElement("script");floodlight.src="//www.googletagmanager.com/gtag/js?id\x3d**********";floodlight.async=true;document.body.appendChild(floodlight);window.dataLayer=window.dataLayer||[];function gtag(){dataLayer.push(arguments)}gtag("js",new Date);gtag("config","**********");var page_type=function(){return Bootstrapper.data.resolve("13149")};
var floodlight_category=function(){return Bootstrapper.data.resolve("50169")};var productArr=function(){return Bootstrapper.data.resolve("44595")};productArr=productArr.call();var products=[];for(var i=0;i<productArr.length;i++){products[i]={};products[i].id=productArr[i].productId;products[i].price=productArr[i].price;products[i].quantity=productArr[i].orderQuantity}if(page_type.call()=="order confirmation")gtag("event","purchase",{"allow_custom_scripts":true,"send_to":"**********/sales0/"+floodlight_category.call()+
"+"+"items_sold","transaction_id":Bootstrapper.data.resolve("10921"),"value":Bootstrapper.data.resolve("14630"),"quantity":Bootstrapper.data.resolve("52538"),"items":products,"country":Bootstrapper.data.resolve("13145"),"language":"en"})},2368660,517347);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){if(window.location.hostname&&(window.location.hostname.match(/^\w{2,5}\.rs-online\.com$/)||window.location.hostname.match(/rs-components\.cn$/)||window.location.hostname.match(/^rsonline\.cn$/)))return"Production";else return"Development"},transform:function(val){return val?val:""},
load:"page",trigger:Bootstrapper.data.immediateTrigger,dataDefName:"Environment",collection:"All Pages",source:"Manage",priv:"false"},{id:"52149"})},52149)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){return window.location.search.replace("?","\x26").split("\x26gclid\x3d").pop().split("\x26").shift()},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Google Click ID - gclid",collection:"All Pages",source:"Manage",
priv:"false"},{id:"50195"})},50195)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){switch(rs.web.digitalData.store){case "at":return"de_AT";break;case "be01":return"fr_BE";break;case "be01":return"fr_BE";break;case "be02":return"nl_BE";break;case "cz":return"cs_CZ";break;case "dk":return"da_DK";break;case "f1":return"fr_FR";break;case "de":return"de_DE";break;case "hu":return"hu_HU";
break;case "ie":return"en_IE";break;case "it":return"it_IT";break;case "nl":return"nl_NL";break;case "no":return"no_NO";break;case "pt":return"pt_PT";break;case "es":return"es_ES";break;case "se":return"sv_SE";break;case "dech":return"de_CH";break;case "uk":return"en_GB";break;case "cn":return"zh_CN";break;case "hk01":return"en_HK";break;case "hk02":return"zh_HK";break;case "jp":return"ja_JP";break;case "kr":return"ko_KR";break;case "tw01":return"en_TW";break;case "tw02":return"zh_TW";break;case "sg":return"en_SG";
break;case "my":return"en_MY";break;case "ph":return"en_PH";break;case "th":return"th_TH";break;case "tw01":return"en_TW";break;case "au":return"en_AU";break;case "nz":return"en_NZ";break;case "za":return"en_ZA"}},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.immediateTrigger,dataDefName:"BazaarVoice - Locale",collection:"All Pages",source:"Manage",priv:"false"},{id:"53413"})},53413)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;var style=document.createElement("style");style.type="text/css";var css=decodeURIComponent("%2F*%0A%20%20%20%20this%20needs%20to%20be%20uploaded%20to%20the%20tag%20%22Recommendations%20-%20Custom%20Styles%22%20in%20Ensighten.%0A*%2F%0A%0A.recContainer%7B%0A%20%20%20%20border-top%3A%20dotted%201px%20%23ccc%3B%0A%20%20%20%20margin-top%3A%2030px%3B%0A%7D%0A.at-table%7B%0A%20%20%20%20display%3Atable%3B%0A%7D%0A.at-table-row%7B%0A%20%20%20%20display%3Atable-row%3B%0A%20%20%20%20%20width%3Aauto%3B%0A%20%20%20%20%20clear%3Aboth%3B%0A%20%20%20%20%20padding%3A10px%3B%0A%7D%0A.at-table-column%7B%0A%20%20%20%20float%3Aleft%3B%0A%20%20%20%20%20display%3Atable-column%3B%0A%20%20%20%20%20padding%3A10px%3B%0A%20%20%20%20%20max-width%3A%20300px%3B%0A%20%20%20%20%20word-wrap%3A%20break-word%3B%0A%7D%0A.at-thumbnail%7B%0A%20%20%20%20cursor%3Apointer%3B%0A%20%20%20%20%20float%3A%20left%3B%0A%20%20%20%20%20margin-right%3A%2015px%3B%0A%20%20%20%20%20height%3A%2050px%3B%0A%20%20%20%20%20width%3A%2060px%3B%0A%20%20%20%20%20background-repeat%3A%20no-repeat%3B%0A%20%20%20%20%20background-position%3A%20top%20center%3B%0A%20%20%20%20%20background-size%3A%20contain%3B%0A%20%20%20%20%20box-sizing%3A%20border-box%3B%0A%7D%0A.at-light%7B%0A%20%20%20%20font-weight%3A%20lighter%3B%0A%20%20%20%20%20margin-top%3A%20-10px%3B%0A%20%20%20%20%20size%3A%2010em%3B%0A%7D%0A.at-table-column%7B%0A%20%20%20%20width%3A%20230px%3B%0A%7D%0A.at-left-c%2C.at-right-c%7B%0A%20%20%20%20position%3A%20relative%3B%0A%20%20%20%20%20float%3A%20left%3B%0A%20%20%20%20%20height%3A%20100%25%3B%0A%7D%0A.at-left-c%7B%0A%20%20%20%20width%3A%2030%25%3B%0A%20%20%20%20%20margin-right%3A%205%25%3B%0A%20%20%20%20height%3A85px%3B%0A%7D%0A.at-left-c%20img%7B%0A%20%20%20%20cursor%3A%20pointer%3B%0A%7D%0A.at-right-c%7B%0A%20%20%20%20width%3A%2065%25%3B%0A%20%20%20%20%20font-size%3A%2011px%20!important%3B%0A%7D%0A.productTab%7B%0A%20%20%20%20color%3A%20%23000%3B%0A%20%20%20%20%20padding%3A%205px%205px%205px%200%3B%0A%20%20%20%20%20font-size%3A%2016px%3B%0A%20%20%20%20%20line-height%3A%2040px%3B%0A%20%20%20%20%20font-weight%3A%20bold%3B%0A%7D%0A.linkedPrice%7B%0A%20%20%20%20float%3A%20left%3B%0A%20%20%20%20%20margin-top%3A%205px%3B%0A%20%20%20%20%20font-size%3A%2016px%3B%0A%20%20%20%20%20font-weight%3A%20bold%3B%0A%7D");
if(style.styleSheet)style.styleSheet.cssText=css;else style.appendChild(document.createTextNode(css));var script=document.getElementsByTagName("script")[0];script.parentNode.insertBefore(style,script)},2416015,531170);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){try{var products=window.rs.web.digitalData.products;var stringArray=[];for(var i=0;i<products.length;i++)stringArray.push(products[i].price);var productString=stringArray.join(",");return productString}catch(e){return"error"}},transform:function(val){return val?val:""},load:"page",
trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Product Prices",collection:"E-Commerce",source:"Manage",priv:"false"},{id:"49505"})},49505)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;function sendRecommendationViewToAA(moduleArr){var aaArr=formatRecItems(moduleArr,"event57\x3d1");dmpgTools.sendDataToAA(window.s,"link","o","Recommendation View",{},{},{"event6":[],"event57":[]},aaArr)}function sendRecommendationClick(moduleArr){var aaArr=formatRecItems(moduleArr,"event58\x3d1");dmpgTools.sendDataToAA(window.s,"link","o","Recommendation Click",{},{},{"event58":[]},
aaArr)}function cleanseStr(str){var charsToRemove=[/[^\w\s]/gi];for(var x=0;x<charsToRemove.length;x++)str=str.replace(charsToRemove[x],"");return str}function formatRecItems(moduleArr,merchEvent){var aaArr=[];var aaStr=";";var merchStr="";var merchArr=[];for(i=0;i<moduleArr.length;i++){merchArr=[];merchArr.push(merchStr.concat("eVar83\x3d",moduleArr[i].position));merchArr.push(merchStr.concat("eVar85\x3d",cleanseStr(moduleArr[i].name)));merchArr.push(merchStr.concat("eVar84\x3d",moduleArr[i].modulePlacement));
merchArr.push(merchStr.concat("eVar82\x3d",moduleArr[i].moduleId,".",moduleArr[i].moduleName,".",moduleArr[i].modulePlacement,".",moduleArr[i].position,".",window.rs.web.digitalData.page_type));if(merchEvent.match(/event58/i)){merchArr.push(merchStr.concat("eVar87\x3d",moduleArr[i].moduleId,".",moduleArr[i].moduleName,".",moduleArr[i].modulePlacement,".",moduleArr[i].position,".",Bootstrapper.data.resolve("Manage.All Pages.PageType")));merchArr.push(merchStr.concat("eVar88\x3d",moduleArr[i].moduleId,
".",moduleArr[i].moduleName,".",moduleArr[i].modulePlacement,".",moduleArr[i].position,".",Bootstrapper.data.resolve("Manage.All Pages.PageType")));merchArr.push(merchStr.concat("eVar89\x3d",moduleArr[i].moduleId,".",moduleArr[i].moduleName,".",moduleArr[i].modulePlacement,".",moduleArr[i].position,".",Bootstrapper.data.resolve("Manage.All Pages.PageType")))}aaArr.push(aaStr.concat(moduleArr[i].id,";;;",merchEvent,";",merchArr.join("|")))}return aaArr}var dmpgTools={doesObjExist:function(variables){return typeof variables!=
"undefined"&&variables!==""?true:false},listenToEvent:function(arr,callback){arr.push=function(){for(i=0;i<arguments.length;i++){Array.prototype.push.call(arr,arguments[i]);callback(arr[arr.length-1])}}},sendVars:function(s,obj){var propEvarObj=obj;for(var propEVars in propEvarObj){var propEvarValue=propEvarObj[propEVars];s[propEVars]=propEvarValue}},sendProducts:function(s,productsArr){s["products"]=productsArr.join(",")},linkTrackObj:function(s,arr){var linkTrackVarsArr=[];for(i=0;i<arr.length;i++){var obj=
arr[i];for(var vars in obj)linkTrackVarsArr.push(vars)}linkTrackVarsArr.push("products");s["linkTrackVars"]=linkTrackVarsArr.join(",")},linkTrackEvents:function(s,obj){var linkTrackEventsArr=[];for(var event in obj)linkTrackEventsArr.push(event);s["linkTrackEvents"]=linkTrackEventsArr.join(",")},getEvents:function(s,obj){var eventObj=obj;var eventArr=[];for(var event in eventObj){var eventValue=eventObj[event];if(Array.isArray(eventValue))if(eventValue.length>1&&eventValue[0]=="numeric"){eventValue=
eventValue[1];eventArr.push(event.concat("\x3d",eventValue))}else{if(eventValue.length==1||eventValue.length===0){eventValue=event;eventArr.push(event)}}else{eventValue=event;eventArr.push(eventValue)}}return eventArr},sendDataToAA:function(s,callType,linkType,linkName,propObj,evarObj,eventObj,productsArr){if(typeof s!="undefined"&&window[s]!="undefined"){var events=typeof dmpgTools.getEvents(s,eventObj)!="undefined"&&dmpgTools.getEvents(s,eventObj).length>0?dmpgTools.getEvents(s,eventObj):"";dmpgTools.sendVars(s,
propObj);dmpgTools.sendVars(s,evarObj);dmpgTools.sendProducts(s,productsArr);if(events!=="")s["events"]=events.join(",");if(callType=="link"&&typeof linkType!="undefined"){dmpgTools.linkTrackObj(s,[evarObj,propObj]);dmpgTools.linkTrackEvents(s,eventObj);s.tl(this,linkType,linkName)}else if(callType=="view")s.t();s.clearVars()}},initialiseDatalayer:function(objName){window[objName]=window[objName]||{};window[objName].events=window[objName].events||[];window[objName].modules=window[objName].modules||
[];window[objName].assets=window[objName].assets||{};window[objName].assets.products=window[objName].assets.products||[]},processEvents:function(obj){if(typeof window[obj]!="undefined"&&typeof window[obj].events!="undefined")for(var i=0;i<window[obj].events.length;i++){var currentEventObj=window[obj].events[i];dmpgTools.checkFellowObjectsProps(currentEventObj)}dmpgTools.listenToEvent(window[obj].events,dmpgTools.checkEventName)},checkEventName:function(eventObj){console.log("Event Has Occured In The DataLayer");
if(dmpgTools.doesObjExist(eventObj)&&dmpgTools.doesObjExist(eventObj.eventName))switch(eventObj.eventName){case "module.load.auto.dataLayer.load":dmpgTools.checkFellowObjectsProps(eventObj);break;case "module_item.interact.auto.view":var myRecView=dmpgTools.sitchModuleItemsData(window.rsdl.modules,eventObj.moduleItem,"Recommendation",window.rsdl.assets.products);sendRecommendationViewToAA(myRecView);break;case "module_item.interact.manual.click":var myRecItems=[eventObj.moduleItem];var myRecClickItems=
dmpgTools.sitchModuleItemsData(window.rsdl.modules,myRecItems,"Recommendation",window.rsdl.assets.products);sendRecommendationClick(myRecClickItems);break}},checkFellowObjectsProps:function(eventObj){if(dmpgTools.doesObjExist(eventObj.module))if(window.rsdl.modules.length>0){var isModuleInModules=false;for(i=0;i<window.rsdl.modules.length;i++){var currentModule=window.rsdl.modules[i];if(currentModule.id==eventObj.module.id)isModuleInModules=true}if(isModuleInModules==false)window.rsdl.modules.push(eventObj.module)}else window.rsdl.modules.push(eventObj.module);
if(dmpgTools.doesObjExist(eventObj.assets))if(dmpgTools.doesObjExist(eventObj.assets.products))for(var i=0;i<eventObj.assets.products.length;i++){var currentAssetsProdIndx=eventObj.assets.products[i];window.rsdl.assets.products.push(currentAssetsProdIndx);window.rsdl.assets.products=dmpgTools.filterArrayOfObjects(window.rsdl.assets.products)}},filterArrayOfObjects:function(arr){var filteredarr=[];var includedids=[];for(var x=0;x<arr.length;x++)if(includedids.indexOf(arr[x].id)==-1){filteredarr.push(arr[x]);
includedids.push(arr[x].id)}return filteredarr},sitchModuleItemsData:function(moduleObj,moduleItemObj,moduleType,assetsProp){var modules=moduleObj;var myItems=moduleItemObj;var finalArr=[];for(i=0;i<modules.length;i++){var currentModule=modules[i];if(currentModule.type==moduleType)for(y=0;y<myItems.length;y++){var currentItemObj=myItems[y];if(currentModule.id==currentItemObj.moduleId)for(z=0;z<currentModule.items.length;z++){var mainModuleItem=currentModule.items[z];if(mainModuleItem.id==currentItemObj.id)if(typeof assetsProp==
"undefined")finalArr.push({"id":mainModuleItem.id,"moduleId":currentModule.id,"modulePlacement":currentModule.placement,"moduleName":currentModule.name,"moduleType":currentModule.type,"position":mainModuleItem.position,"type":mainModuleItem.type});else for(x=0;x<assetsProp.length;x++)if(currentItemObj.id==assetsProp[x].id)finalArr.push({"id":mainModuleItem.id,"name":assetsProp[x].name,"position":mainModuleItem.position,"type":mainModuleItem.type,"moduleId":currentModule.id,"modulePlacement":currentModule.placement,
"moduleName":currentModule.name,"moduleType":currentModule.type})}}}return finalArr}};dmpgTools.initialiseDatalayer("rsdl");dmpgTools.processEvents("rsdl")},2577163,509528);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){try{var products=window.rs.web.digitalData.products;var stringArray=[];for(var i=0;i<products.length;i++)stringArray.push(products[i].orderQuantity);var productString=stringArray.join(",");return productString}catch(e){return"error"}},transform:function(val){return val?val:""},load:"page",
trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Product Quantities",collection:"E-Commerce",source:"Manage",priv:"false"},{id:"49503"})},49503)},-1,-1);
Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;Bootstrapper.registerDataDefinition(function(){Bootstrapper.data.define({extract:function(){var date=new Date,hour=date.getHours().toString(),formatedHour=hour.length===1?"0"+hour:hour,minute=date.getMinutes().toString(),formatedMinute=minute.length===1?"0"+minute:minute,second=date.getSeconds().toString(),formatedSecond=second.length===1?"0"+second:second;return formatedHour+
":"+formatedMinute+":"+formatedSecond},transform:function(val){return val?val:""},load:"page",trigger:Bootstrapper.data.bottomOfBodyTrigger,dataDefName:"Current Time - HHMMSS",collection:"Global",source:"Manage",priv:"false"},{id:"55047"})},55047)},-1,-1);