/**
* Copyright (c) 2017-present, Facebook, Inc. All rights reserved.
*
* You are hereby granted a non-exclusive, worldwide, royalty-free license to use,
* copy, modify, and distribute this software in source code or binary form for use
* in connection with the web services and APIs provided by Facebook.
*
* As with any software that integrates with the Facebook platform, your use of
* this software is subject to the Facebook Platform Policy
* [http://developers.facebook.com/policy/]. This copyright notice shall be
* included in all copies or substantial portions of the software.
*
* THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
* IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
* FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR
* COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER
* IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN
* CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
*/

fbq.version="2.8.30";
fbq._releaseSegment = "stable";
fbq.pendingConfigs=["global_config"];
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});f.ensureModuleRegistered("SignalsFBEventsOptTrackingOptions",function(){return function(f,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";e.exports={AUTO_CONFIG_OPT_OUT:1<<0,AUTO_CONFIG:1<<1,CONFIG_LOADING:1<<2,SUPPORTS_DEFINE_PROPERTY:1<<3,SUPPORTS_SEND_BEACON:1<<4,HAS_INVALIDATED_PII:1<<5,SHOULD_PROXY:1<<6}})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsPlugin",function(){return function(f,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";function a(a){this.plugin=a;this.__fbEventsPlugin=1;return this}e.exports=a})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsProxyState",function(){return function(f,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=!1;e.exports={getShouldProxy:function(){return a},setShouldProxy:function(b){a=b}}})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEvents.plugins.opttracking",function(){return function(a,b,c,d){var e={exports:{}};e.exports;(function(){"use strict";var b=f.getFbeventsModules("SignalsFBEventsOptTrackingOptions"),c=f.getFbeventsModules("SignalsFBEventsPlugin"),d=f.getFbeventsModules("SignalsFBEventsProxyState"),g=!1;function h(){try{Object.defineProperty({},"test",{})}catch(a){return!1}return!0}function i(){return!!(a.navigator&&a.navigator.sendBeacon)}function j(a,b){return a?b:0}c=new c(function(a,c){if(g)return;var e={};a.on("pii_invalidated",function(a){a!=null&&(e[typeof a==="string"?a:a.id]=!0)});a.on("getCustomParameters",function(f){if(f==null)return{};var g=c.optIns,k=j(g.isOptedOut(f.id,"AutomaticSetup"),b.AUTO_CONFIG_OPT_OUT);g=j(g.isOptedIn(f.id,"AutomaticSetup"),b.AUTO_CONFIG);var l=j(a.disableConfigLoading!==!0,b.CONFIG_LOADING),m=j(h(),b.SUPPORTS_DEFINE_PROPERTY),n=j(i(),b.SUPPORTS_SEND_BEACON);f=j(f!=null&&e[f.id],b.HAS_INVALIDATED_PII);var o=j(d.getShouldProxy(),b.SHOULD_PROXY);k=k|g|l|m|n|f|o;return{o:k}});g=!0});c.OPTIONS=b;e.exports=c})();return e.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents.plugins.opttracking");f.registerPlugin&&f.registerPlugin("fbevents.plugins.opttracking",e.exports);f.ensureModuleRegistered("fbevents.plugins.opttracking",function(){return e.exports})})()})(window,document,location,history);
(function(a,b,c,d){var e={exports:{}};e.exports;(function(){var f=a.fbq;f.execStart=a.performance&&a.performance.now&&a.performance.now();if(!function(){var b=a.postMessage||function(){};if(!f){b({action:"FB_LOG",logType:"Facebook Pixel Error",logMessage:"Pixel code is not installed correctly on this page"},"*");"error"in console&&console.error("Facebook Pixel Error: Pixel code is not installed correctly on this page");return!1}return!0}())return;var g=function(){function a(a,b){var c=[],d=!0,e=!1,f=undefined;try{for(var a=a[typeof Symbol==="function"?Symbol.iterator:"@@iterator"](),g;!(d=(g=a.next()).done);d=!0){c.push(g.value);if(b&&c.length===b)break}}catch(a){e=!0,f=a}finally{try{!d&&a["return"]&&a["return"]()}finally{if(e)throw f}}return c}return function(b,c){if(Array.isArray(b))return b;else if((typeof Symbol==="function"?Symbol.iterator:"@@iterator")in Object(b))return a(b,c);else throw new TypeError("Invalid attempt to destructure non-iterable instance")}}(),h=typeof Symbol==="function"&&typeof (typeof Symbol==="function"?Symbol.iterator:"@@iterator")==="symbol"?function(a){return typeof a}:function(a){return a&&typeof Symbol==="function"&&a.constructor===Symbol&&a!==(typeof Symbol==="function"?Symbol.prototype:"@@prototype")?"symbol":typeof a},i=function(){function a(a,b){for(var c=0;c<b.length;c++){var d=b[c];d.enumerable=d.enumerable||!1;d.configurable=!0;"value"in d&&(d.writable=!0);Object.defineProperty(a,d.key,d)}}return function(b,c,d){c&&a(b.prototype,c);d&&a(b,d);return b}}();function j(a){if(Array.isArray(a)){for(var b=0,c=Array(a.length);b<a.length;b++)c[b]=a[b];return c}else return Array.from(a)}function k(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}f.__fbeventsModules||(f.__fbeventsModules={},f.__fbeventsResolvedModules={},f.getFbeventsModules=function(a){f.__fbeventsResolvedModules[a]||(f.__fbeventsResolvedModules[a]=f.__fbeventsModules[a]());return f.__fbeventsResolvedModules[a]},f.fbIsModuleLoaded=function(a){return!!f.__fbeventsModules[a]},f.ensureModuleRegistered=function(b,a){f.fbIsModuleLoaded(b)||(f.__fbeventsModules[b]=a)});f.ensureModuleRegistered("SignalsEventValidation",function(){return function(g,h,i,j){var k={exports:{}};k.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsLogging"),b=a.logUserError,c=/^[+-]?\d+(\.\d+)?$/,d="number",e="currency_code",g={AED:1,ARS:1,AUD:1,BOB:1,BRL:1,CAD:1,CHF:1,CLP:1,CNY:1,COP:1,CRC:1,CZK:1,DKK:1,EUR:1,GBP:1,GTQ:1,HKD:1,HNL:1,HUF:1,IDR:1,ILS:1,INR:1,ISK:1,JPY:1,KRW:1,MOP:1,MXN:1,MYR:1,NIO:1,NOK:1,NZD:1,PEN:1,PHP:1,PLN:1,PYG:1,QAR:1,RON:1,RUB:1,SAR:1,SEK:1,SGD:1,THB:1,TRY:1,TWD:1,USD:1,UYU:1,VEF:1,VND:1,ZAR:1};a={value:{type:d,isRequired:!0},currency:{type:e,isRequired:!0}};var h={PageView:{},ViewContent:{},Search:{},AddToCart:{},AddToWishlist:{},InitiateCheckout:{},PixelInitialized:{},AddPaymentInfo:{},Purchase:{validationSchema:a},Lead:{},CompleteRegistration:{},CustomEvent:{validationSchema:{event:{isRequired:!0}}}},i={agent:!0,automaticmatchingconfig:!0,codeless:!0},j=Object.prototype.hasOwnProperty;function l(){return{error:null,warnings:[]}}function m(a){return{error:a,warnings:[]}}function n(a){return{error:null,warnings:a}}function o(a){if(a){a=a.toLowerCase();var b=i[a];if(!b)return m({type:"UNSUPPORTED_METADATA_ARGUMENT",metadata:a})}return l()}function p(a){var b=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};if(!a)return m({type:"NO_EVENT_NAME"});var c=h[a];return!c?n([{type:"NONSTANDARD_EVENT",eventName:a}]):q(a,b,c)}function q(a,b,f){f=f.validationSchema;var h=[];for(var i in f)if(j.call(f,i)){var k=f[i],l=b[i];if(k){if(k.isRequired&&!j.call(b,i))return m({type:"REQUIRED_PARAM_MISSING",param:i,eventName:a});if(k.type&&typeof k.type==="string"){var o=!0;switch(k.type){case d:k=(typeof l==="string"||typeof l==="number")&&c.test(""+l);k&&Number(l)<0&&h.push({type:"NEGATIVE_EVENT_PARAM",param:i,eventName:a?a:"null"});o=k;break;case e:o=typeof l==="string"&&!!g[l.toUpperCase()];break}if(!o)return m({type:"INVALID_PARAM",param:i,eventName:a})}}}return n(h)}function r(a,c){a=p(a,c);a.error&&b(a.error);if(a.warnings)for(var c=0;c<a.warnings.length;c++)b(a.warnings[c]);return a}k.exports={validateMetadata:o,validateEvent:p,validateEventAndLog:r}})();return k.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsEvents",function(){return function(g,h,j,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsLogging");a=a.logError;var b=f.getFbeventsModules("SignalsFBEventsUtils"),c=b.keys,d=0;b=function(){function b(){var a=this;k(this,b);this._listeners={};this.on=function(){return a._on.apply(a,arguments)};this.once=function(){return a._once.apply(a,arguments)};this.trigger=function(b){for(var c=arguments.length,d=Array(c>1?c-1:0),e=1;e<c;e++)d[e-1]=arguments[e];return a._trigger.apply(a,[b].concat(d))};this.unsubscribe=function(){return a._unsubscribe.apply(a,arguments)}}i(b,[{key:"_on",value:function(a,b){var c=this,e=d++;this._listeners[a]||(this._listeners[a]={});this._listeners[a][e.toString()]=b;return function(){c.unsubscribe(a,e.toString())}}},{key:"_once",value:function(a,b){var c=arguments,d=this.on(a,function(){d();return b.apply(null,c)});return d}},{key:"_trigger",value:function(b){var d=this;for(var e=arguments.length,f=Array(e>1?e-1:0),g=1;g<e;g++)f[g-1]=arguments[g];return!this._listeners[b]?[]:c(this._listeners[b]).map(function(c){try{return!d._listeners[b][c]?[]:d._listeners[b][c].apply(null,f)}catch(b){a(b)}return null})}},{key:"_unsubscribe",value:function(a,b){var d=this._listeners[a];d&&d[b]&&(delete d[b],c(d).length===0&&delete this._listeners[a])}}]);return b}();b=new b();e.exports=b})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsConfigStore",function(){return function(f,g,h,j){var e={exports:{}};e.exports;(function(){"use strict";var a=Object.assign||function(a){for(var b=1;b<arguments.length;b++){var c=arguments[b];for(var d in c)Object.prototype.hasOwnProperty.call(c,d)&&(a[d]=c[d])}return a},b=function(){function b(){k(this,b),this._config={}}i(b,[{key:"_getPixelConfig",value:function(a){this._config[a]==null&&(this._config[a]={});return this._config[a]}},{key:"set",value:function(b,c,d){c==="automaticMatching"?this._getPixelConfig(b).automaticMatching=a({},d):c==="inferredEvents"&&(this._getPixelConfig(b).inferredEvents=a({},d));return this}},{key:"getAutomaticMatchingConfig",value:function(a){return this._getPixelConfig(a).automaticMatching}},{key:"getInferredEventsConfig",value:function(a){return this._getPixelConfig(a).inferredEvents}}]);return b}();e.exports=new b()})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsFBQ",function(){return function(g,h,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsEventValidation"),b=f.getFbeventsModules("SignalsFBEventsConfigStore"),c=f.getFbeventsModules("SignalsFBEventsFireLock"),d=f.getFbeventsModules("SignalsFBEventsJSLoader"),h=f.getFbeventsModules("SignalsFBEventsLogging"),l=f.getFbeventsModules("SignalsFBEventsOptIn"),m=f.getFbeventsModules("SignalsFBEventsQE"),n=f.getFbeventsModules("SignalsFBEventsUtils"),o=f.getFbeventsModules("SignalsPixelEndpoint"),p=n.each,q=n.keys,r=n.map,s=n.some,t=h.logError,u=h.logUserError,v={AutomaticMatching:!0,Dwell:!0,FirstPartyCookies:!0,Interaction:!0,InferredEvents:!0,Microdata:!0,MicrodataJsonLd:!0,ProxyEndpoint:!0,Sessions:!0,TimeSpent:!0,IWL:!0,IWLBootstrapper:!0,GA2FBQ:!0};n=["InferredEvents","Microdata"];var w={AutomaticSetup:n},x={AutomaticMatching:["inferredevents","identity"],Dwell:["dwell"],FirstPartyCookies:["cookie"],InferredEvents:["inferredevents","identity"],Interaction:["interaction","timespent"],Microdata:["microdata","identity"],MicrodataJsonLd:["jsonld_microdata"],ProxyEndpoint:["proxy"],Sessions:["sessions"],TimeSpent:["timespent"],IWL:["iwl"],IWLBootstrapper:["iwlbootstrapper"],GA2FBQ:["ga2fbq"]};function y(a){return!!(v[a]||w[a])}function z(a,b,c){d.loadJSFile(d.CONFIG.CDN_BASE_URL+"signals/config/"+a+"?v="+b+"&r="+c)}h=function(){function d(a,e){var g=this;k(this,d);this.VALID_FEATURES=v;this.optIns=new l(w);this.configsLoaded={};this._defaultExperiments=new m([]);this.locks=c.global;this.pluginConfig=b;this.disableFirstPartyCookies=!1;this.VERSION=a.version;this.RELEASE_SEGMENT=a._releaseSegment;this.pixelsByID=e;this.fbq=a;p(a.pendingConfigs||[],function(a){return g.locks.lockConfig(a)})}i(d,[{key:"optIn",value:function(a,b){var c=this,d=arguments.length>2&&arguments[2]!==undefined?arguments[2]:!1;if(typeof b!=="string"||!y(b))throw new Error('Invalid Argument: "'+b+'" is not a valid opt-in feature');y(b)&&(this.optIns.optIn(a,b,d),p([b].concat(j(w[b]||[])),function(a){x[a]&&p(x[a],function(a){return c.fbq.loadPlugin(a)})}));return this}},{key:"optOut",value:function(a,b){this.optIns.optOut(a,b);return this}},{key:"consent",value:function(a){a=="revoke"?this.locks.lockConsent():a=="grant"?this.locks.unlockConsent():u({type:"INVALID_CONSENT_ACTION",action:a});return this}},{key:"setUserProperties",value:function(a,b){if(!Object.prototype.hasOwnProperty.call(this.pixelsByID,a)){u({type:"PIXEL_NOT_INITIALIZED",pixelID:a});return}var c=this.getDefaultSendData(a,"UserProperties");c.customData=b;c.customParameters={es:"userProperties"};this.trackSingleSystem("user_properties",a,"UserProperties",b)}},{key:"trackSingle",value:function(b,c,d){a.validateEventAndLog(c,d);return this.trackSingleGeneric(b,c,d)}},{key:"trackSingleCustom",value:function(a,b,c){return this.trackSingleGeneric(a,b,c)}},{key:"trackSingleSystem",value:function(a,b,c,d){return this.trackSingleGeneric(b,c,d,a)}},{key:"trackSingleGeneric",value:function(a,b,c,d){a=typeof a==="string"?a:a.id;if(!Object.prototype.hasOwnProperty.call(this.pixelsByID,a)){var e={type:"PIXEL_NOT_INITIALIZED",pixelID:a};d==null?u(e):t(new Error(e.type+" "+e.pixelID));return this}e=this.getDefaultSendData(a,b);e.customData=c;d!=null&&(e.customParameters={es:d});this.fire(e,!1);return this}},{key:"_validateSend",value:function(b,c){if(!b.eventName||!b.eventName.length)throw new Error("Event name not specified");if(!b.pixelId||!b.pixelId.length)throw new Error("PixelId not specified");b.set&&p(r(q(b.set),function(b){return a.validateMetadata(b)}),function(a){if(a.error)throw new Error(a.error);a.warnings.length&&p(a.warnings,u)});if(c){c=a.validateEvent(b.eventName,b.customData||{});if(c.error)throw new Error(c.error);c.warnings&&c.warnings.length&&p(c.warnings,u)}return this}},{key:"fire",value:function(a){var b=arguments.length>1&&arguments[1]!==undefined?arguments[1]:!1;this._validateSend(a,b);if(a.userData&&q(a.userData).length>0&&!this.fbq.loadPlugin("identity")){g.fbq("fire",a);return this}var c=this.fbq.getEventCustomParameters(this.getPixel(a.pixelId)),d=a.customParameters;d&&p(q(d),function(a){if(c.containsKey(a))throw new Error("Custom parameter "+a+" already specified.");else c.append(a,d[a])});o.sendEvent(a.pixelId,a.eventName,a.customData,c);return this}},{key:"callMethod",value:function(a){var b=a[0];a=Array.prototype.slice.call(a,1);if(typeof b!=="string"){u({type:"FBQ_NO_METHOD_NAME"});return}if(typeof this[b]==="function")try{this[b].apply(this,a)}catch(a){t(a)}else u({type:"INVALID_FBQ_METHOD",method:b})}},{key:"getDefaultSendData",value:function(a,b){var c=this.getPixel(a);a={pixelId:a,eventName:b};c&&(c.userData&&(a.userData=c.userData),c.agent!=null&&c.agent!==""?a.set={agent:c.agent}:this.fbq.agent!=null&&this.fbq.agent!==""&&(a.set={agent:this.fbq.agent}));return a}},{key:"getOptedInPixels",value:function(a){var b=this;return this.optIns.listPixelIds(a).map(function(a){return b.pixelsByID[a]})}},{key:"getPixel",value:function(a){return this.pixelsByID[a]}},{key:"loadConfig",value:function(a){if(this.fbq.disableConfigLoading===!0||Object.prototype.hasOwnProperty.call(this.configsLoaded,a))return;this.locks.lockConfig(a);(!this.fbq.pendingConfigs||s(this.fbq.pendingConfigs,function(b){return b===a})==!1)&&z(a,this.VERSION,this.RELEASE_SEGMENT!=null?this.RELEASE_SEGMENT:"stable")}},{key:"setExperiments",value:function(a){this._defaultExperiments=new m(a)}},{key:"getExperiments",value:function(){return this._defaultExperiments}},{key:"configLoaded",value:function(a){this.configsLoaded[a]=!0,this.locks.releaseConfig(a),this.fbq.trigger("configLoaded",a)}}]);return d}();e.exports=h})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsFireLock",function(){return function(g,h,j,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsUtils"),b=a.each,c=a.keys;a=function(){function a(){k(this,a),this._locks={},this._callbacks=[]}i(a,[{key:"lock",value:function(a){this._locks[a]=!0}},{key:"release",value:function(a){Object.prototype.hasOwnProperty.call(this._locks,a)&&(delete this._locks[a],c(this._locks).length===0&&b(this._callbacks,function(b){return b(a)}))}},{key:"onUnlocked",value:function(a){this._callbacks.push(a)}},{key:"isLocked",value:function(){return c(this._locks).length>0}},{key:"lockPlugin",value:function(a){this.lock("plugin:"+a)}},{key:"releasePlugin",value:function(a){this.release("plugin:"+a)}},{key:"lockConfig",value:function(a){this.lock("config:"+a)}},{key:"releaseConfig",value:function(a){this.release("config:"+a)}},{key:"lockConsent",value:function(){this.lock("consent")}},{key:"unlockConsent",value:function(){this.release("consent")}}]);return a}();a.global=new a();e.exports=a})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsJSLoader",function(){return function(f,g,h,i){var j={exports:{}};j.exports;(function(){"use strict";var a={CDN_BASE_URL:"https://connect.facebook.net/"};function b(){var b=g.getElementsByTagName("script");for(var c=0;c<b.length;c++)if(b[c]&&b[c].src&&b[c].src.indexOf(a.CDN_BASE_URL)!==-1)return b[c];return null}function c(a){var c=g.createElement("script");c.src=a;c.async=!0;a=b();a&&a.parentNode?a.parentNode.insertBefore(c,a):g.head&&g.head.firstChild&&g.head.appendChild(c)}j.exports={CONFIG:a,loadJSFile:c}})();return j.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsLogging",function(){return function(g,h,i,j){var k={exports:{}};k.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsUtils"),b=a.isInstanceOf,c=a.sendPOST,d=f.getFbeventsModules("SignalsParamList"),e=!1;function h(){e=!0}var i=!0;function j(){i=!1}a="console";var l="warn",m=g[a]&&g[a][l]?g[a][l].bind(g[a]):function(){},n=!1;function o(){n=!0}function p(a){if(n)return;m("[Facebook Pixel] - "+a)}var q="Facebook Pixel Error",r=g.postMessage?g.postMessage.bind(g):function(){},s={};function t(a){switch(a.type){case"FBQ_NO_METHOD_NAME":return"You must provide an argument to fbq().";case"INVALID_FBQ_METHOD":var b=a.method;return"\"fbq('"+b+"', ...);\" is not a valid fbq command.";case"INVALID_PIXEL_ID":b=a.pixelID;return"Invalid PixelID: "+b+".";case"DUPLICATE_PIXEL_ID":b=a.pixelID;return"Duplicate Pixel ID: "+b+".";case"SET_METADATA_ON_UNINITIALIZED_PIXEL_ID":b=a.metadataValue;var c=a.pixelID;return"Trying to set argument "+b+" for uninitialized Pixel ID "+c+".";case"CONFLICTING_VERSIONS":return"Multiple pixels with conflicting versions were detected on this page.";case"MULTIPLE_PIXELS":return"Multiple pixels were detected on this page.";case"UNSUPPORTED_METADATA_ARGUMENT":b=a.metadata;return"Unsupported metadata argument: "+b+".";case"REQUIRED_PARAM_MISSING":c=a.param;b=a.eventName;return"Required parameter '"+c+"' is missing for event '"+b+"'.";case"INVALID_PARAM":c=a.param;b=a.eventName;return"Parameter '"+c+"' is invalid for event '"+b+"'.";case"NO_EVENT_NAME":return'Missing event name. Track events must be logged with an event name fbq("track", eventName)';case"NONSTANDARD_EVENT":c=a.eventName;return"You are sending a non-standard event '"+c+"'. The preferred way to send these events is using trackCustom. See 'https://developers.facebook.com/docs/ads-for-websites/pixel-events/#events' for more information.";case"NEGATIVE_EVENT_PARAM":b=a.param;c=a.eventName;return"Parameter '"+b+"' is negative for event '"+c+"'.";case"PII_INVALID_TYPE":b=a.key_type;c=a.key_val;return"An invalid "+b+" was specified for '"+c+"'. This data will not be sent with any events for this Pixel.";case"PII_UNHASHED_PII":b=a.key;return"The value for the '"+b+"' key appeared to be PII. This data will not be sent with any events for this Pixel.";case"INVALID_CONSENT_ACTION":c=a.action;return"\"fbq('"+c+"', ...);\" is not a valid fbq('consent', ...) action. Valid actions are 'await' and 'grant'.";case"INVALID_JSON_LD":b=a.jsonLd;return"Unable to parse JSON-LD tag. Malformed JSON found: '"+b+"'.";case"SITE_CODELESS_OPT_OUT":c=a.pixelID;return"Unable to open Codeless events interface for pixel as the site has opted out. Pixel ID: "+c+".";case"PIXEL_NOT_INITIALIZED":b=a.pixelID;return"Pixel "+b+" not found";default:w(new Error("INVALID_USER_ERROR - "+a.type+" - "+JSON.stringify(a)));return"Invalid User Error."}}function u(a,e){try{var f=Math.random(),h=g.fbq&&g.fbq._releaseSegment?g.fbq._releaseSegment:"unknown";if(i&&f<.01||h==="canary"){f=new d(null);f.append("p","pixel");f.append("v",g.fbq&&g.fbq.version?g.fbq.version:"unknown");f.append("e",a.toString());b(a,Error)&&(f.append("f",a.fileName),f.append("s",a.stackTrace||a.stack));f.append("ue",e?"1":"0");f.append("rs",h);c(f,"https://connect.facebook.net/log/error")}}catch(a){}}function v(a){var b=JSON.stringify(a);if(!Object.prototype.hasOwnProperty.call(s,b))s[b]=!0;else return;b=t(a);p(b);r({action:"FB_LOG",logType:q,logMessage:b},"*");u(new Error(b),!0)}function w(a){u(a,!1),e&&p(a.toString())}l={logError:w,logUserError:v,enableVerboseDebugLogging:h,disableAllLogging:o,disableSampling:j};k.exports=l})();return k.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsMobileAppBridge",function(){return function(g,i,j,k){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsTelemetry"),b=f.getFbeventsModules("SignalsFBEventsUtils"),c=b.each,d="fbmq-0.1",i={AddPaymentInfo:"fb_mobile_add_payment_info",AddToCart:"fb_mobile_add_to_cart",AddToWishlist:"fb_mobile_add_to_wishlist",CompleteRegistration:"fb_mobile_complete_registration",InitiateCheckout:"fb_mobile_initiated_checkout",Other:"other",Purchase:"fb_mobile_purchase",Search:"fb_mobile_search",ViewContent:"fb_mobile_content_view"},j={content_ids:"fb_content_id",content_type:"fb_content_type",currency:"fb_currency",num_items:"fb_num_items",search_string:"fb_search_string",value:"_valueToSum"},k={};function l(a){return"fbmq_"+a[1]}function m(a){if(Object.prototype.hasOwnProperty.call(k,[0])&&Object.prototype.hasOwnProperty.call(k[a[0]],a[1]))return!0;var b=g[l(a)];b=b&&b.getProtocol.call&&b.getProtocol()===d?b:null;b!==null&&(k[a[0]]=k[a[0]]||{},k[a[0]][a[1]]=b);return b!==null}function n(a){var b=[];a=k[a.id]||{};for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b.push(a[c]);return b}function o(a){return n(a).length>0}function p(a){return Object.prototype.hasOwnProperty.call(i,a)?i[a]:a}function q(a){return Object.prototype.hasOwnProperty.call(j,a)?j[a]:a}function r(a){if(typeof a==="string")return a;if(typeof a==="number")return isNaN(a)?undefined:a;try{return JSON.stringify(a)}catch(a){}return a.toString&&a.toString.call?a.toString():undefined}function s(a){var b={};if(a!=null&&(typeof a==="undefined"?"undefined":h(a))==="object")for(var c in a)if(Object.prototype.hasOwnProperty.call(a,c)){var d=r(a[c]);d!=null&&(b[q(c)]=d)}return b}var t=0;function u(){var b=t;t=0;a.logMobileNativeForwarding(b)}function v(a,b,d,e){c(n(a),function(c){return c.sendEvent(a.id,p(b),JSON.stringify(s(d)))}),t++,setTimeout(u,0)}e.exports={pixelHasActiveBridge:o,registerBridge:m,sendEvent:v}})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsOptIn",function(){return function(g,h,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsUtils"),b=a.each,c=a.filter,d=a.keys,g=a.some;function h(a){b(d(a),function(b){if(g(a[b],function(b){return Object.prototype.hasOwnProperty.call(a,b)}))throw new Error("Circular subOpts are not allowed. "+b+" depends on another subOpt")})}a=function(){function a(){var b=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};k(this,a);this._opts={};this._subOpts=b;h(this._subOpts)}i(a,[{key:"_getOpts",value:function(a){return[].concat(j(Object.prototype.hasOwnProperty.call(this._subOpts,a)?this._subOpts[a]:[]),[a])}},{key:"_setOpt",value:function(a,b,c){b=this._opts[b]||(this._opts[b]={});b[a]=c}},{key:"optIn",value:function(a,c){var d=this,e=arguments.length>2&&arguments[2]!==undefined?arguments[2]:!1;b(this._getOpts(c),function(b){var f=e==!0&&d.isOptedOut(a,c);f||d._setOpt(a,b,!0)});return this}},{key:"optOut",value:function(a,c){var d=this;b(this._getOpts(c),function(b){return d._setOpt(a,b,!1)});return this}},{key:"isOptedIn",value:function(a,b){return this._opts[b]!=null&&this._opts[b][a]===!0}},{key:"isOptedOut",value:function(a,b){return this._opts[b]!=null&&this._opts[b][a]===!1}},{key:"listPixelIds",value:function(a){var b=this._opts[a];return b!=null?c(d(b),function(a){return b[a]===!0}):[]}}]);return a}();e.exports=a})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsProxyState",function(){return function(f,g,h,i){var j={exports:{}};j.exports;(function(){"use strict";var a=!1;j.exports={getShouldProxy:function(){return a},setShouldProxy:function(b){a=b}}})();return j.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsQE",function(){return function(f,h,j,d){var e={exports:{}};e.exports;(function(){"use strict";var a=function(){return Math.random()};function b(c){var b=a();for(var d=0;d<c.length;d++){var e=c[d],f=e.passRate,h=g(e.range,2),i=h[0];h=h[1];if(f<0||f>1)throw new Error("passRate should be between 0 and 1 in "+e.name);if(b>=i&&b<h){i=a()<f;return{name:e.name,isInExperimentGroup:i,code:e.code+(i?"1":"0")}}}return null}var c=function(){function a(b){k(this,a),this._result=null,this._hasRolled=!1,this._groups=b}i(a,[{key:"get",value:function(a){if(!this._hasRolled){var c=b(this._groups);c!=null&&(this._result=c);this._hasRolled=!0}if(a==null||a==="")return this._result;return this._result!=null&&this._result.name===a?this._result:null}}]);return a}();e.exports=c})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsTelemetry",function(){return function(g,h,i,j){var k={exports:{}};k.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsLogging"),b=f.getFbeventsModules("SignalsFBEventsUtils"),c=b.sendPOST,d=f.getFbeventsModules("SignalsParamList");b=.01;var e=Math.random(),h=g.fbq&&g.fbq._releaseSegment?g.fbq._releaseSegment:"unknown",i=e<b||h==="canary";function j(event){var b=arguments.length>1&&arguments[1]!==undefined?arguments[1]:0,e=arguments.length>2&&arguments[2]!==undefined?arguments[2]:!1;if(!e&&!i)return;try{var f=new d(null);f.append("v",g.fbq&&g.fbq.version?g.fbq.version:"unknown");f.append("rs",h);f.append("e",event);f.append("p",b);c(f,"https://connect.facebook.net/log/fbevents_telemetry/")}catch(b){a.logError(b)}}function l(){j("COALESCE_INIT")}function m(a){j("COALESCE_COMPLETE",a)}function n(a){j("FBMQ_FORWARDED",a,!0)}k.exports={logStartBatch:l,logEndBatch:m,logMobileNativeForwarding:n}})();return k.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEventsUtils",function(){return function(g,j,c,d){var e={exports:{}};e.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsProxyState"),b=Object.prototype.toString,c=!("addEventListener"in j);function d(a,b){return typeof b==="function"&&a instanceof b}function l(a){return Array.isArray?Array.isArray(a):b.call(a)==="[object Array]"}function m(a){return typeof a==="number"||typeof a==="string"&&/^\d+$/.test(a)}var n=Number.isInteger||function(a){return typeof a==="number"&&isFinite(a)&&Math.floor(a)===a};function o(a,b,d){var e=c?"on"+b:b;b=c?a.attachEvent:a.addEventListener;var f=c?a.detachEvent:a.removeEventListener,g=function b(){f&&f.call(a,e,b,!1),d()};b&&b.call(a,e,g,!1)}var p=Object.prototype.hasOwnProperty,q=!{toString:null}.propertyIsEnumerable("toString"),r=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],s=r.length;function t(a){if(Object.keys)return Object.keys(a);if((typeof a==="undefined"?"undefined":h(a))!=="object"&&(typeof a!=="function"||a===null))throw new TypeError("Object.keys called on non-object");var b=[];for(var c in a)p.call(a,c)&&b.push(c);if(q)for(var d=0;d<s;d++)p.call(a,r[d])&&b.push(r[d]);return b}function u(a,b){if(Array.prototype.map)return Array.prototype.map.call(a,b);var c=void 0,d=void 0;if(a==null)throw new TypeError(" array is null or not defined");a=Object(a);var e=a.length>>>0;if(typeof b!=="function")throw new TypeError(b+" is not a function");c=new Array(e);d=0;while(d<e){var f;d in a&&(f=a[d],f=b.call(null,f,d,a),c[d]=f);d++}return c}function v(a){if(this==null)throw new TypeError("Array.prototype.some called on null or undefined");if(Array.prototype.some)return Array.prototype.some.call(this,a);if(typeof a!=="function")throw new TypeError();var b=Object(this),c=b.length>>>0,d=arguments.length>=2?arguments[1]:void 0;for(var e=0;e<c;e++)if(e in b&&a.call(d,b[e],e,b))return!0;return!1}function w(a){return t(a).length===0}function x(a){if(this===void 0||this===null)throw new TypeError();var b=Object(this),c=b.length>>>0;if(typeof a!=="function")throw new TypeError();var d=[],e=arguments.length>=2?arguments[1]:void 0;for(var f=0;f<c;f++)if(f in b){var g=b[f];a.call(e,g,f,b)&&d.push(g)}return d}var y=function(){function a(b){k(this,a),this.items=b||[]}i(a,[{key:"has",value:function(a){return v.call(this.items,function(b){return b===a})}},{key:"add",value:function(a){this.items.push(a)}}]);return a}();function z(b,c){return c!=null&&a.getShouldProxy()?c:b}function A(b,c,d){var e=b.toQueryString();e=z(c,d)+"?"+e;if(e.length<2048){var f=new Image();if(d!=null){var g=a.getShouldProxy();f.onerror=function(){a.setShouldProxy(!0),g||A(b,c,d)}}f.src=e;return!0}return!1}function B(b,c,d){var e="fb"+Math.random().toString().replace(".",""),f=j.createElement("form");f.method="post";f.action=z(c,d);f.target=e;f.acceptCharset="utf-8";f.style.display="none";var h=!!(g.attachEvent&&!g.addEventListener),i=j.createElement("iframe");h&&(i.name=e);i.src="about:blank";i.id=e;i.name=e;f.appendChild(i);o(i,"load",function(){b.each(function(a,b){var c=j.createElement("input");c.name=decodeURIComponent(a);c.value=b;f.appendChild(c)}),o(i,"load",function(){f.parentNode&&f.parentNode.removeChild(f)}),f.submit()});if(d!=null){var k=a.getShouldProxy();i.onerror=function(){a.setShouldProxy(!0),k||B(b,c,d)}}j.body!=null&&j.body.appendChild(f);return!0}function C(b,c,d){if(g.navigator&&g.navigator.sendBeacon){var e=g.navigator.sendBeacon(z(c,d),b.toFormData());if(d!=null&&!e){e=a.getShouldProxy();a.setShouldProxy(!0);e||C(b,c,d)}return!0}return!1}function D(a){return a}function E(a,b){if(!a||a.length===0)return"";var c="",d=0,e=0;while(d<a.length){var f=F(a,d);e>=0&&e<b&&(c+=f);d+=f.length;e+=1}return c}function F(a,b){var c=a.charCodeAt(b);if(c>=55296&&c<=56319&&a.length>b+1){c=a.charCodeAt(b+1);if(c>=56320&&c<=57343)return a.substring(b,b+2)}return a[b]}function G(a,b){if(typeof a!=="string")return"";if(a.length<b)return a;if(Array.from)return[].concat(Array.from(a)).slice(0,b).join("");else return E(a,b)}l={isArray:l,isEmptyObject:w,isNumber:m,isInteger:n,isInstanceOf:d,keys:t,listenOnce:o,map:u,unicodeSafeTruncate:G,unicodeSafeTruncateWithoutArraysFrom:E,sendGET:A,sendPOST:B,sendBeacon:C,FBSet:y,each:function(a,b){u.call(this,a,b)},some:function(a,b){return v.call(a,b)},filter:function(a,b){return x.call(a,b)},castTo:D};e.exports=l})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsParamList",function(){return function(f,g,j,d){var e={exports:{}};e.exports;(function(){"use strict";var a="deep",b="shallow";function c(a){if(JSON===undefined||JSON===null||!JSON.stringify)return Object.prototype.toString.call(a);else return JSON.stringify(a)}function d(a){if(a===null||a===undefined)return!0;a=typeof a==="undefined"?"undefined":h(a);return a==="number"||a==="boolean"||a==="string"}var f=function(){function e(a){k(this,e),this._params=[],this._piiTranslator=a}i(e,[{key:"containsKey",value:function(a){for(var b=0;b<this._params.length;b++)if(this._params[b].name===a)return!0;return!1}},{key:"get",value:function(a){a=a;for(var b=0;b<this._params.length;b++)if(this._params[b].name===a)return this._params[b].value;return null}},{key:"getAllParams",value:function(){return this._params}},{key:"addRange",value:function(a){var c=this;a.each(function(a,d){return c._append(a,d,b,!1)})}},{key:"append",value:function(b,c){var d=arguments.length>2&&arguments[2]!==undefined?arguments[2]:!1;this._append(encodeURIComponent(b),c,a,d);return this}},{key:"appendHash",value:function(b){var c=arguments.length>1&&arguments[1]!==undefined?arguments[1]:!1;for(var d in b)Object.prototype.hasOwnProperty.call(b,d)&&this._append(encodeURIComponent(d),b[d],a,c);return this}},{key:"_append",value:function(b,e,f,g){d(e)?this._appendPrimitive(b,e,g):f===a?this._appendObject(b,e,g):this._appendPrimitive(b,c(e),g)}},{key:"_translateValue",value:function(a,b,c){if(typeof b==="boolean")return b?"true":"false";if(!c)return""+b;if(!this._piiTranslator)throw new Error();return this._piiTranslator(a,""+b)}},{key:"_appendPrimitive",value:function(a,b,c){if(b!=null){b=this._translateValue(a,b,c);b!=null&&this._params.push({name:a,value:b})}}},{key:"_appendObject",value:function(a,c,d){var e=null;for(var f in c)if(Object.prototype.hasOwnProperty.call(c,f)){var g=a+"["+encodeURIComponent(f)+"]";try{this._append(g,c[f],b,d)}catch(a){e==null&&(e=a)}}if(e!=null)throw e}},{key:"each",value:function(a){for(var b=0;b<this._params.length;b++){var c=this._params[b],d=c.name;c=c.value;a(d,c)}}},{key:"toQueryString",value:function(){var a=[];this.each(function(b,c){a.push(b+"="+encodeURIComponent(c))});return a.join("&")}},{key:"toFormData",value:function(){var a=new FormData();this.each(function(b,c){a.append(b,c)});return a}}],[{key:"fromHash",value:function(a,b){return new e(b).appendHash(a)}}]);return e}();e.exports=f})();return e.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsPixelEndpoint",function(){return function(g,h,i,j){var k={exports:{}};k.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsEvents"),b=f.getFbeventsModules("SignalsFBEventsTelemetry"),c=f.getFbeventsModules("SignalsFBEventsUtils"),d=c.sendBeacon,e=c.sendGET,j=c.sendPOST,l=f.getFbeventsModules("SignalsParamList"),m=a.trigger,n={ENDPOINT:"https://www.facebook.com/tr/",PROXY_ENDPOINT:null,EXPERIMENT:null},o=g.top!==g,p=!1;c=function(a){p=a};function q(a,b,c,d){a.append("id",b);a.append("ev",c);a.append("dl",i.href);a.append("rl",h.referrer);a.append("if",o);a.append("ts",new Date().valueOf());a.append("cd",d);a.append("sw",g.screen.width);a.append("sh",g.screen.height);return a}var r=0;function s(){var a=r;r=0;b.logEndBatch(a)}function t(a,c,f,g,h){h=new l(h);q(h,a,c,f);g&&h.addRange(g);if(n.EXPERIMENT){a=n.EXPERIMENT.get();if(a!=null){var i=a.name==="send_coalescence_telemetry";i&&r===0&&a.isInExperimentGroup&&(b.logStartBatch(),setTimeout(s,0))}}r++;i=g&&g.containsKey("es")&&g.get("es")=="timespent";a=[h,n.ENDPOINT,n.PROXY_ENDPOINT];g=!1;if(n.EXPERIMENT){var k=n.EXPERIMENT.get();if(k!=null){var o=!u()&&k.name==="button_click_send_beacon",t=k.name==="button_click_send_beacon_all_browser",v=k.name==="all_event_send_beacon";v||c==="SubscribedButtonClick"&&(o||t)?(g=k.isInExperimentGroup,h.append("exp",k.code)):k.name==="test_experiment"&&h.append("exp",k.code)}}if((p||g||i)&&d.apply(undefined,a)){m("fired","BEACON",h,f);return}if(e.apply(undefined,a)){m("fired","GET",h,f);return}if(j.apply(undefined,a)){m("fired","POST",h,f);return}throw new Error("No working send method found for this fire.")}function u(){var a=g.chrome,b=g.navigator,c=b.vendor,d=g.opr!==undefined,e=b.userAgent.indexOf("Edge")>-1;b=b.userAgent.match("CriOS");return!b&&a!==null&&a!==undefined&&c==="Google Inc."&&d===!1&&e===!1}function v(a,b,c,e,f){if(g.navigator&&g.navigator.sendBeacon){f=new l(f);q(f,a,b,c);e&&f.addRange(e);d(f,n.ENDPOINT)}}k.exports={CONFIG:n,sendEvent:t,sendBeaconPII:v,setUseBeacon:c}})();return k.exports}(a,b,c,d)});f.ensureModuleRegistered("signalsFBEventsInjectMethod",function(){return function(g,h,i,j){var k={exports:{}};k.exports;(function(){"use strict";var a=f.getFbeventsModules("signalsFBEventsMakeSafe");function b(b,c,d){var e=b[c],f=a(d);b[c]=function(){var a=e.apply(this,arguments);f.apply(this,arguments);return a}}k.exports=b})();return k.exports}(a,b,c,d)});f.ensureModuleRegistered("signalsFBEventsMakeSafe",function(){return function(g,h,i,j){var k={exports:{}};k.exports;(function(){"use strict";var a=f.getFbeventsModules("SignalsFBEventsLogging");a=a.logError;function b(b){return typeof b!=="function"?b:function(){try{return b.apply(this,arguments)}catch(b){a(b)}return undefined}}k.exports=b})();return k.exports}(a,b,c,d)});f.ensureModuleRegistered("SignalsFBEvents",function(){return function(g,h,i,j){var k={exports:{}};k.exports;(function(){"use strict";var a=g.fbq;a.execStart=g.performance&&typeof g.performance.now==="function"?g.performance.now():null;var b=a.getFbeventsModules("SignalsParamList"),c=a.getFbeventsModules("SignalsPixelEndpoint"),d=a.getFbeventsModules("SignalsEvents"),e=a.getFbeventsModules("SignalsFBEventsUtils"),l=a.getFbeventsModules("SignalsFBEventsLogging"),m=a.getFbeventsModules("SignalsEventValidation"),n=a.getFbeventsModules("SignalsFBEventsFBQ"),o=a.getFbeventsModules("SignalsFBEventsJSLoader"),p=a.getFbeventsModules("SignalsFBEventsFireLock"),q=a.getFbeventsModules("SignalsFBEventsMobileAppBridge"),r=a.getFbeventsModules("signalsFBEventsInjectMethod"),s=a.getFbeventsModules("signalsFBEventsMakeSafe"),t=a.getFbeventsModules("SignalsFBEventsConfigStore"),u=d.on,v=d.once,w=d.trigger,x=e.each;d=e.FBSet;var y=e.isArray,z=e.isInteger,A=e.isEmptyObject,B=e.isNumber,C=e.keys,D=l.logError,E=l.logUserError,F=p.global;function G(b){return a.getFbeventsModules(b)}function aa(b){return a.fbIsModuleLoaded(b)}var H={},I=-1,ba=Array.prototype.slice,J=Object.prototype.hasOwnProperty,K=i.href,L=!1,M=!1,N=[],O={},P;h.referrer;var Q={PageView:new d(),PixelInitialized:new d()},R=new n(a,O);function ca(a){for(var b in a)J.call(a,b)&&(this[b]=a[b]);return this}function S(b){try{var c=ba.call(arguments);if(F.isLocked()&&c[0]!=="consent"){a.queue.push(arguments);return}var d=c.length===1&&y(c[0]);d&&(c=c[0]);typeof c[0]!=="string"&&E({type:"FBQ_NO_METHOD_NAME"});if(b.slice(0,6)==="report"){var e=b.slice(6);e==="CustomEvent"?(e=(c[1]||{}).event||e,c=["trackCustom",e].concat(c.slice(1))):c=["track",e].concat(c.slice(1))}b=c.shift();switch(b){case"addPixelId":L=!0;U.apply(this,c);break;case"init":M=!0;U.apply(this,c);break;case"set":T.apply(this,c);break;case"track":if(B(c[0])){ga.apply(this,c);break}if(d){W.apply(this,c);break}fa.apply(this,c);break;case"trackCustom":W.apply(this,c);break;case"send":X.apply(this,c);break;case"on":u.apply(null,c);break;case"loadPlugin":$(c[0]);break;default:R.callMethod(arguments);break}}catch(a){D(a)}}function T(b){for(var d=arguments.length,e=Array(d>1?d-1:0),f=1;f<d;f++)e[f-1]=arguments[f];switch(b){case"endpoint":var g=e[0];if(typeof g!=="string")throw new Error("endpoint value must be a string");c.CONFIG.ENDPOINT=g;break;case"cdn":var h=e[0];if(typeof h!=="string")throw new Error("cdn value must be a string");o.CONFIG.CDN_BASE_URL=h;break;case"releaseSegment":var i=e[0];if(typeof i!=="string")throw new Error("releaseSegment value must be a string");a._releaseSegment=i;break;case"proxy":var j=e[0];if(c.CONFIG.PROXY_ENDPOINT)throw new Error("proxy has already been set");if(typeof j!=="string")throw new Error("endpoint value must be a string");c.CONFIG.PROXY_ENDPOINT=j;break;case"autoConfig":var k=e[0],l=e[1],m=k===!0||k==="true"?"optIn":"optOut";if(typeof l!=="string")throw new Error("Invalid pixelID supplied to set autoConfig.");R.callMethod([m,l,"AutomaticSetup"]);break;case"firstPartyCookies":var n=e[0],p=e[1],r=n===!0||n==="true"?"optIn":"optOut";if(typeof p==="string")R.callMethod([r,p,"FirstPartyCookies"]);else if(p===undefined)R.disableFirstPartyCookies=!0;else throw new Error("Invalid pixelID supplied to set cookie controls.");break;case"experiments":var s=e[0],t=[],u=Object.keys(s);for(var v=0;v<u.length;v++)t.push(s[u[v]]);R.setExperiments(t);c.CONFIG.EXPERIMENT=R.getExperiments();break;case"mobileBridge":var w=e[0],x=e[1];if(typeof w!=="string")throw new Error("Invalid pixelID supplied to set call.");if(typeof x!=="string")throw new Error("Invalid appID supplied to set call.");q.registerBridge([w,x]);break;default:var y=e[0],z=e[1];if(typeof b!=="string")throw new Error("The metadata setting provided in the 'set' call is invalid.");if(typeof y!=="string")throw new Error("The metadata value must be a string.");if(typeof z!=="string")throw new Error("Invalid pixelID supplied to set call.");ea(b,y,z);break}}a._initHandlers=[];a._initsDone={};var da=function(a){return z(a)&&a>=0&&a<=Number.MAX_SAFE_INTEGER};function U(a,b,c){I=I===-1?Date.now():I;if(typeof a==="number")da(a)||E({type:"INVALID_PIXEL_ID",pixelID:a.toString()}),a=a.toString();else if(typeof a==="string"){var d=/^[1-9][0-9]{0,25}$/;d.test(a)||E({type:"INVALID_PIXEL_ID",pixelID:a})}else if(a===undefined)E({type:"INVALID_PIXEL_ID",pixelID:"undefined"});else if(a===null)E({type:"INVALID_PIXEL_ID",pixelID:"null"});else{typeof a.toString==="function"?E({type:"INVALID_PIXEL_ID",pixelID:a.toString()}):E({type:"INVALID_PIXEL_ID",pixelID:"[no toString]"});return}if(J.call(O,a)){b&&A(O[a].userData)?(O[a].userData=b,$("identity")):E({type:"DUPLICATE_PIXEL_ID",pixelID:a});return}d={agent:c?c.agent:null,id:a,userData:b||{},eventCount:0};N.push(d);O[a]=d;b!=null&&$("identity");V();R.loadConfig(a)}function V(){for(var b=0;b<a._initHandlers.length;b++){var c=a._initHandlers[b];a._initsDone[b]||(a._initsDone[b]={});for(var d=0;d<N.length;d++){var e=N[d];a._initsDone[b][e.id]||(a._initsDone[b][e.id]=!0,c(e))}}}function ea(a,b,c){var d=m.validateMetadata(a);d.error&&E(d.error);if(d.warnings)for(var e=0;e<d.warnings.length;e++)E(d.warnings[e]);if(J.call(O,c)){for(var e=0,d=N.length;e<d;e++)if(N[e].id===c){N[e][a]=b;break}}else E({type:"SET_METADATA_ON_UNINITIALIZED_PIXEL_ID",metadataValue:b,pixelID:c})}function fa(a,b){b=b||{},m.validateEventAndLog(a,b),a==="CustomEvent"&&typeof b.event==="string"&&(a=b.event),W.call(this,a,b)}function W(a,b){for(var c=0,d=N.length;c<d;c++){var e=N[c];if(!(a==="PageView"&&this.allowDuplicatePageViews)&&Object.prototype.hasOwnProperty.call(Q,a)&&Q[a].has(e.id))continue;Z(e,a,b);Object.prototype.hasOwnProperty.call(Q,a)&&Q[a].add(e.id)}}function ga(a,b){Z(null,a,b)}function X(a,b){for(var c=0,d=N.length;c<d;c++)Z(N[c],a,b)}function Y(c){var d=new b(a.piiTranslator);try{d.append("ud",c&&c.userData||{},!0)}catch(a){w("pii_invalidated",c)}d.append("v",a.version);a._releaseSegment&&d.append("r",a._releaseSegment);d.append("a",c&&c.agent?c.agent:a.agent);c&&(d.append("ec",c.eventCount),c.eventCount++);var e=w("getCustomParameters",c);x(e,function(a){return x(C(a),function(b){if(d.containsKey(b))throw new Error("Custom parameter "+b+" has already been specified.");else d.append(b,a[b])})});d.append("it",I);e=c&&c.codeless==="false";d.append("coo",e);return d}function Z(a,b,d){if(a!=null&&q.pixelHasActiveBridge(a)){q.sendEvent(a,b,d||{});return}c.sendEvent(a?a.id:null,b,d,Y(a))}function ha(){while(a.queue.length&&!F.isLocked()){var b=a.queue.shift();S.apply(a,b)}}function ia(a){return"fbevents.plugins."+a}function $(b){if(/^[a-zA-Z]\w+$/.test(b)===!1)throw new Error("Invalid plugin name: "+b);var c=ia(b);if(H[c])return!0;if(aa(c)){ja(c,G(c));return!0}b=o.CONFIG.CDN_BASE_URL+"signals/plugins/"+b+".js?v="+a.version;if(!H[c]){F.lockPlugin(c);o.loadJSFile(b);return!0}return!1}function ja(b,c){if(Object.prototype.hasOwnProperty.call(H,b))return;J.call(c,"__fbEventsPlugin")&&c.__fbEventsPlugin===1&&(H[b]=c,H[b].plugin(a,R,t),w("pluginLoaded",b));F.releasePlugin(b)}F.onUnlocked(function(){ha()});a.pixelId&&(L=!0,U(a.pixelId));(L&&M||g.fbq!==g._fbq)&&E({type:"CONFLICTING_VERSIONS"});N.length>1&&E({type:"MULTIPLE_PIXELS"});function ka(){if(a.disablePushState===!0)return;if(!j.pushState||!j.replaceState)return;var b=s(function(){P=K;K=i.href;if(K===P)return;var a=new ca({allowDuplicatePageViews:!0});S.call(a,"trackCustom","PageView")});r(j,"pushState",b);r(j,"replaceState",b);g.addEventListener("popstate",b,!1)}v("fired",function(){return ka()});function la(b){a._initHandlers.push(b),V()}function ma(){return{pixelInitializationTime:I,pixels:N}}function na(a){a.instance=R,a.callMethod=S,a.loadPlugin=$,a.registerPlugin=ja,a._initHandlers=[],a._initsDone={},a.on=u,a.once=v,a.send=X,a.trigger=w,a.getEventCustomParameters=Y,a.addInitHandler=la,a.getState=ma,a.init=U,a.set=T}na(g.fbq);ha();k.exports={doExport:na};w("execEnd");w("initialized",a)})();return k.exports}(a,b,c,d)});e.exports=f.getFbeventsModules("SignalsFBEvents");f.registerPlugin&&f.registerPlugin("fbevents",e.exports);f.ensureModuleRegistered("fbevents",function(){return e.exports})})()})(window,document,location,history);
fbq.registerPlugin("global_config", {__fbEventsPlugin: 1, plugin: function(fbq, instance, config) { fbq.loadPlugin("opttracking");
fbq.set("experiments", {"0":{"name":"beacon","range":[0,0],"code":"b","passRate":0.5},"1":{"name":"logDataLayer","range":[0,0],"code":"d","passRate":0},"2":{"name":"button_click_send_beacon","range":[0,0.1],"code":"e","passRate":0.5},"3":{"name":"button_click_send_beacon_all_browser","range":[0.11,0.12],"code":"f","passRate":0.5},"4":{"name":"all_event_send_beacon","range":[0.12,0.13],"code":"g","passRate":0.5},"5":{"name":"send_coalescence_telemetry","range":[0,0],"code":"h","passRate":0.5}});instance.configLoaded("global_config"); }});