Bootstrapper.bindDOMLoaded(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;var errorDiv=document.getElementsByClassName("svcErrorDiv")?document.getElementsByClassName("svcErrorDiv"):"";if(errorDiv&&errorDiv.length>0){s.eVar7="errors";s.eVar1="service:error";s.events="event1,event4";s.t();s.events="";s.linkTrackEvents="";s.eVar7="";s.eVar1="";s.pageName=""}},2228226,377236);
Bootstrapper.bindImmediate(function(){var ddConditions={"not":[null],"caseInsensitive":["ignore case"],"compareTo":["true"],"requiredData":["42638"],"comparators":["is"]};Bootstrapper.data.resolve(ddConditions.requiredData,function(){ddConditions.values=Array.prototype.slice.call(arguments,0);var Bootstrapper=window["Bootstrapper"];if(Bootstrapper.data.checkConditions(ddConditions))Bootstrapper.bindImmediate(function(){var Bootstrapper=window["Bootstrapper"];var ensightenOptions=Bootstrapper.ensightenOptions;
Bootstrapper.AF.push(["push","SiteCatalyst","ns","s"]);Bootstrapper.AF.push(["join","s","pre",[["events","event3"]]])},1999365,478001)})},-1,-1);