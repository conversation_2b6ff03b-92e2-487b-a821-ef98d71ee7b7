/***************************************
* @preserve
* ForeSee Web SDK: Trigger
* Built February 23, 18 13:09:14
* Code version: 19.6.1
* Template version: 19.6.1
***************************************/
_fsDefine(["require","fs",_fsNormalizeUrl("$fs.utils.js"),"triggerconfig"],function(e,t,i,config){var s={loadedEmitter:new i.FSEvent,initializedEmitter:new i.FSEvent,inviteShownEmitter:new i.FSEvent,inviteAcceptedEmitter:new i.FSEvent,inviteAbandonedEmitter:new i.FSEvent,inviteDeclinedEmitter:new i.FSEvent,trackerShownEmitter:new i.FSEvent,customInvitationRequested:new i.FSEvent,CPPS:null,_triggerResetLock:null,state:{didInvite:!1},inviteSetup:null},n={INVITE_SHOWN:"fs_inviteShown",INVITE_ACCEPTED:"fs_inviteAccepted",INVITE_DECLINED:"fs_inviteDeclined",INVITE_ABANDONED:"fs_inviteAbandoned",LINKS_CANCEL:"fs_linksCancel",TRACKER_SHOWN:"fs_trackerShown",TRACKER_CLICKED:"fs_trackerClicked",QUALIFIER_ACCEPTED:"fs_qualifierAccepted",QUALIFIER_DECLINED:"fs_qualifierDeclined",QUALIFIER_SHOWN:"fs_qualifierShown",REMINDER_SHOWN:"fs_reminderShown",REMINDER_ACCEPTED:"fs_reminderAccepted"};if(config&&config.surveydefs)for(var r=0;r<config.surveydefs.length;r++)t.isString(config.surveydefs[r])&&(config.surveydefs[r]=i.compile(i.b64DecodeUnicode(config.surveydefs[r])));var o=window,a=new i.Cookie({path:"/",secure:!1,encode:!0}),c=i.Compress;if(t.fsCmd("fstest"))return void e([t.makeURI("$fs.svadmin.js")],function(e){});if(t.fsCmd("fsoptout"))return void e([t.makeURI("$fs.optout.js")],function(e){});var f=function(e,i,s,n,r){var a={width:700,height:350,left:50,top:50,resizable:"no",scrollbar:"1",scrollbars:"1",toolbar:"no",menubar:"no",location:"0",directories:"no",status:"no"},c=t.ext(a,s),f="";for(var u in c)f+=u+"="+c[u]+",";var l=this._win=o.open(e,i,f);if(l&&r)if(l.blur(),l.opener.window.focus(),o.focus(),"Firefox"==n.browser.name){var d=o.open("about:blank");d.focus(),d.close()}else n.isIE&&setTimeout(function(){l.blur(),l.opener.window.focus(),o.focus()},1e3);return l},u=config.config.surveyAsyncCurl,l={SERVICE_TYPES:{mobileOnExitInitialize:{host:u,path:"/e",url:"/initialize"},mobileOnExitHeartbeat:{host:u,path:"/e",url:"/recordHeartbeat"}}};l.ping=function(e,t,s,n){var r=new i.ImageTransport,o="https://"+e.host+e.path+(e.url||"");r.send({url:o,success:s||function(){},failure:n||function(){},data:t})};var d=function(e,t,i,n,r,o,a){return s.inviteSetup?a.call(s.inviteSetup):s.inviteSetup=new g(e,t,i,n,r,o,a),s.inviteSetup},g=function(n,r,o,a,c,f,u){if(this.trig=n,this.browser=r,this.stg=o,this.cpps=a,this.displayoverride=c,this.jrny=f,this.resourcesready=new i.FSEvent,t.isDefined(this.trig.surveydef.inviteExclude)&&t.isDefined(this.trig.crit)&&this.trig.crit.runAllTests(this.trig.surveydef.inviteExclude,this.browser,!1,!0))return!1;var l=this;fsReady(function(){var i;if(n.invite&&n.invite.dispose(),r.isMobile&&n.cfg.config.pagesInviteAvailable)if(null===(i=o.get("pia")))o.set("pia",--n.cfg.config.pagesInviteAvailable);else if(i>0)o.set("pia",--i);else if(0===i)return;e([t.makeURI("$fs.invite.js")],function(e){var t=l.invite=n.invite=new e(config,n.surveydef,r,c,a,s);o.set("dn",t.display.displayname),a.set("dn",t.display.displayname),u&&u.call(l)}.bind(this))})},p=function(e,t,i){var s=new T(i,config,e.surveydef,e.cpps,e.stg.get("rid"),e.locale);e.stg.get("mhbi")?s.beginHeartbeat():s.init(t,function(){s.beginHeartbeat()})};g.prototype.initialize=function(){var e=this.trig,r=(this.browser,this.stg),o=this.cpps,a=(this.displayoverride,this.invite),c=this.jrny;this.didInitialize||(this.didInitialize=!0,c.addEventsDefault("properties",{fs_defName:[e.surveydef.name],fs_section:[e.surveydef.section],fs_displayName:[a.display.displayname],fs_pvInvited:[e.pageViewCount],fs_language:[a.locale],fs_samplePercentage:[e.surveydef.criteria.sp.reg],fs_loyaltyFactor:[e.surveydef.criteria.lf],fs_environment:[t.isProduction?"production":"staging"],fs_deployType:[t.isSelfHosted?"on-prem":"cloud"],fs_inviteType:["intercept"]}),a.loadResources(this.resourcesready),a.declined.subscribe(function(i){var a=t.isDefined(config.active_surveydef)&&t.isDefined(config.active_surveydef.repeatDays)?config.active_surveydef.repeatDays:config.config.repeatDays;r.set("i","d"),r.setMaxKeyExpiration(24*a.decline*60*60*1e3),c.addEventObj({name:n.INVITE_DECLINED,properties:{action:[i]}}),s.inviteDeclinedEmitter.fire(e.surveydef,r,config,o),e.surveydef.cxRecord&&s.rec&&"y"!=r.get("fbr")&&(s.rec.cancelRecord(),e.recordController=s.rec=null),s.state.inviteSituation="DECLINED"}),a.abandoned.subscribe(function(){c.addEventString(n.INVITE_ABANDONED),r.set("i","a"),s.state.inviteSituation="ABANDONED",s.inviteAbandonedEmitter.fire(e.surveydef,r,config,o),r.set("rw",i.now()+config.config.reinviteDelayAfterInviteAbandon)}),a.accepted.subscribe(function(f,u){var l=t.isDefined(config.active_surveydef)&&t.isDefined(config.active_surveydef.repeatDays)?config.active_surveydef.repeatDays:config.config.repeatDays;switch(r.setMaxKeyExpiration(24*l.accept*60*60*1e3),s.inviteAcceptedEmitter.fire(e.surveydef,r,config,o),e.surveydef.cxRecord&&s.rec&&s.rec.recorder&&s.rec.beginTransmitting(),c.addEventString(n.INVITE_ACCEPTED),r.set("i","x"),s.state.inviteSituation="ACCEPTED",r.set("ixw",i.now()),f){case"TRACKER":e.popTracker(a);break;case"INSESSION":e.popSurvey();break;case"SMS":case"EMAIL":case"SMSEMAIL":p(e,u,f),e.stg.set("mhbi",{ui:u,it:f})}}))},g.prototype.present=function(){var e=(this.invite,this.stg),r=this.jrny,o=this.trig,a=this.cpps;s.state.didInvite||(s.state.didInvite=!0,this.resourcesready.subscribe(function(){setTimeout(function(){this.invite.present(),"p"!==e.get("i")&&r.addEvent(n.INVITE_SHOWN),e.set("i","p"),s.state.inviteSituation="PRESENTED",s.inviteShownEmitter.fire(o.surveydef,e,config,a)}.bind(this),Math.max(0,config.config.inviteDelay-(i.now()-t.startTS)))}.bind(this),!0,!0))};var h=function(e,n,r,a,c,f,u){s.tracker&&(s.tracker.dispose(),s.tracker=null),s.tracker=this,t.ext(this,{template:e,def:n,cfg:r,disp:f,_fcBindings:[]}),this.cpps=c,this.br=u,this.stg=a,this.forceLong=!1,e&&s.trackerShownEmitter.fire(n,a,r,c);var l=t.proxy(function(e){this.stg.set("page_hb",i.now(),this.forceLong?r.config.trackerHeartbeatLongTimeout:r.config.trackerHeartbeatTimeout,!e),this.stg._sync()},this);this._heartbeat=setInterval(l,Math.round(.5*r.config.trackerHeartbeatTimeout)),l(!0),i.Bind(o,"unload",t.proxy(function(){this.forceLong=!0,this.stg.set("page_hb",i.now(),r.config.trackerHeartbeatLongTimeout,!0)},this));var d=t.enc;this._url=t.makeURI("$fs.tracker.html?uid="+d(a.uid||"")+"&sitekey="+d(t.config.siteKey)+"&domain="+d(i.getRootDomain())+"&gw="+d(t.makeURI("trigger/__gwtest__"))+"&brain_url="+d(t.config.brainUrl)+"&fsrlocale="+d(c.get("locale")||"en")+"&_svu_="+d(t.config.surveyUrl)+"&_cv_="+d(t.config.codeVer)+"&_issh_="+d(t.isSelfHosted)+"&_vt_="+d(t.tagVersion)+"&_au_="+d(t.config.analyticsUrl)+"&_pa_="+d(t.assetLocation)),this.cpps.onSet.subscribe(t.proxy(function(e,t){var i={};i[e]=t,this.stg.set("ckcpps",i,2e5,!1)},this)),this.stg.set("ckcpps",this.cpps.all(),2e5,!1),this._sendDefinition()};h.prototype._sendDefinition=function(){var e={method:"init",cfg:t.ext({},this.cfg,{globalConfig:t.config}),def:this.def};this.disp&&(e.display=this.disp),this.template&&(e.template=this.template),e.hb_i=this.cfg.config.trackerHeartbeatTimeout,e.cpps=this.cpps.all(),this.stg.set("page_hb",i.now(),this.cfg.config.trackerHeartbeatTimeout,!1),this.stg.set("trackerinfo",e,6e4,!1),this.stg.set("ckcpps",this.cpps.all(),2e5,!1)},h.prototype.show=function(e){this.wref=f(this._url,"fsTracker",{width:700,height:450},e,!0)},h.prototype.applyExisting=function(e,t){this.wref=t,t.location=this._url},h.prototype.dispose=function(){for(var e=0;e<this._fcBindings.length;e++)this._fcBindings[e].unsubscribe();this.stg=null,clearInterval(this._heartbeat)};var v=function(config,e,i,s){this.cfg=config,this.globalConfig=config.globalConfig||t.config,this.cpps=e,this.def=i,this.locale=e.get("locale")||"en",this.qual=s},m=function(e,t){var i,s,n=t.name||"";n+="-"+(t.section||""),n+="-"+(t.site||"");for(var r=0;r<e.defs.length;r++)if(s=e.defs[r],i=s.name||"",i+="-"+(s.section||""),(i+="-"+(s.site||""))===n)return{legacyChosen:s.modernPercentage<Math.floor(100*Math.random()),modernPercentage:s.modernPercentage};return{legacyChosen:!0,modernPercentage:0}};v.prototype.getUrl=function(){var e,s,n=this.def,r=i.now()+"_"+Math.round(1e13*Math.random()),o=n.name+"-"+(t.isDefined(n.site)?n.site+"-":"")+(t.isDefined(n.section)?this.def.section+"-":"")+this.locale,a=this.cfg.config.abSurveyType,c=a&&a.shouldTest&&this.globalConfig.modernSurveyUrl;this.qual&&(o+="-"+this.qual.qualifiesValue);var f={sid:o,cid:this.cfg.config.id,pattern:this.cpps.get(n.pattern)||n.pattern,a:r,b:i.hash(r),c:864e5};this.cfg.config.onlyModernSurvey?e=this.globalConfig.modernSurveyUrl:c?(s=m(a,this.cfg.active_surveydef),f.mp=s.modernPercentage,e=s.legacyChosen?this.globalConfig.surveyUrl:this.globalConfig.modernSurveyUrl):e=this.globalConfig.surveyUrl,e+="?";for(var u in f)e+=t.enc(u)+"="+t.enc(f[u])+"&";return e+=this.cpps.toQueryString()};var y=function(e,config){this.stg=e,this.cfg=config};y.prototype.calcReplayPoolStatus=function(e){var s,n,r,a=this.cfg.config,c=a.replay_pools,f=o.location.toString();if(c&&0!==c.length&&!0!==this.pooloverride){if(n=this.stg.get("pl"),!t.isDefined(n))for(s=0;s<c.length;s++)i.testAgainstSearch(c[s].path,f)&&(n=100*Math.random()<c[s].sp?1:0,this.stg.set("pl",n,144e5));if(r=a.replay_repools,0===n&&r&&r.length>0)for(s=0;s<r.length;s++)i.testAgainstSearch(r[s].path,f)&&(n=100*Math.random()<r[s].sp?1:0,this.stg.set("pl",n,144e5));e(!!n)}else e(!0)},y.prototype.optoutCheck=function(e,i){this.stg.ready.subscribe(t.proxy(function(){!0===this.stg.get("optout")?i():e()},this),!0,!0)},y.prototype.browserCheck=function(e,t){return!(!e.isMobile&&t.config.browser_cutoff[e.browser.name]&&e.browser.actualVersion<t.config.browser_cutoff[e.browser.name])},y.prototype.featureCheck=function(e,t){return!(t.config.persistence==i.storageTypes.DS&&!e.supportsLocalStorage)},y.prototype.platformCheck=function(e,t){return!(t.config.platform_cutoff[e.os.name]&&e.os.version<t.config.platform_cutoff[e.os.name])},y.prototype.checkDeviceBlacklist=function(e,i){for(var s=0;s<i.config.device_blacklist.length;s++)if(t.toLowerCase(e.agent).indexOf(t.toLowerCase(i.config.device_blacklist[s]))>-1)return!1;return!0},y.prototype._match=function(e,t,i){var s=e.include,n=e[i||"globalExclude"];if(e.criteria){if(!e.criteria.supportsSmartPhones&&!t.isTablet&&t.isMobile)return!1;if(!e.criteria.supportsTablets&&t.isTablet)return!1;if(!e.criteria.supportsDesktop&&!t.isMobile)return!1}if(n){if(this.runAllTests(n,t,!1,!0))return!1}return!s||this.runAllTests(s,t,!1,!0)},y.prototype.runAllTests=function(e,s,n,r){var a,c=new i.Cookie({}),f=o.location.href.toString(),u=document.referrer.toString(),l={urls:f,referrers:u,userAgents:o.navigator.userAgent};for(var d in e){var g=e[d];if(g.length>0){if(a=!1,l[d])a=function(e,t){Array.isArray(t)||(t=[t]);for(var s=0,n=t.length;s<n;s++)if("string"==typeof t[s]&&(t[s]=t[s].replace(/-_DS_-/gi,"$")),i.testAgainstSearch(t[s],e))return!0;return!1}(l[d],g);else if("browsers"==d)for(var p=s.browser.name,h=s.browser.actualVersion,v=0;v<g.length;v++)t.toLowerCase(p).indexOf(t.toLowerCase(g[v].name))>-1&&(g[v].comparison?"lt"==g[v].comparison&&h<g[v].version?a=!0:"eq"==g[v].comparison&&h==g[v].version?a=!0:"gt"==g[v].comparison&&h>g[v].version&&(a=!0):a=!0);else if("cookies"==d)for(var m=0;m<g.length;m++){var y=g[m],b=c.get(y.name);t.isDefined(y.value)&&b==y.value?a=!0:!t.isDefined(y.value)&&b&&(a=!0)}else if("variables"==d)for(var w=0;w<g.length;w++){var _,S=g[w],E=new[].constructor.constructor("var v1 = '';try { v1 = "+S.name+";}catch(err) {}return v1;"),I=E.call(o);I||(I="boolean"!=typeof I&&""),_=t.isDefined(S.value),_&&I===S.value?a=!0:_&&i.testAgainstSearch(S.value,I)?a=!0:!_&&I&&(a=!0)}if(!a&&n)return!0;if(a&&r)return!0}}return!1};var b=function(e){this.cfg=e};b.prototype._bindToLink=function(e,s){for(var n=document.querySelectorAll(e.selector),r=0;r<n.length;r++){var o,a=n[r],c=!0;if(e.attribute&&(o=a.getAttribute(e.attribute),c=!1,o&&(c=!0,e.patterns&&e.patterns.length>0))){c=!1;for(var f=0;f<e.patterns.length;f++)if(t.toLowerCase(o).indexOf(t.toLowerCase(e.patterns[f]))>-1){c=!0;break}}c&&i.Bind(a,"trigger:click",function(e,t,s){return function(n){t.preventDefault&&i.preventDefault(n),s.call(e,t)}}(this,e,s))}},b.prototype.performBindings=function(e){if(e&&this.cfg){var t,i=this.cfg;if(i.cancel&&i.cancel.length>0){var s=function(){e.cancelTracker(),e.jrny.addEventString(n.LINKS_CANCEL)};for(t=0;t<i.cancel.length;t++)this._bindToLink(i.cancel[t],s)}if(i.survey&&i.survey.length>0){var r=function(){e.popSurvey()};for(t=0;t<i.survey.length;t++)this._bindToLink(i.survey[t],r)}if(!e.browser.isMobile&&i.tracker&&i.tracker.length>0){var o=function(){e.popTracker()};for(t=0;t<i.tracker.length;t++)this._bindToLink(i.tracker[t],o)}}};var w=function(e,i,s,n,r){t.ext(this,{browser:s,gstg:n,journey:r,trigger:e,surveydef:i,mode:(i.mouseoff.mode||"off").toLowerCase()},!1)};w.prototype.initialize=function(){var e,t,s,n,r,o=0,a=0,c=i.now(),f=c,u=function(e,i,o,a){return r=window.pageYOffset,n=window.pageXOffset,pagewidth=window.innerWidth,t=(a-i)/(o-e),s=(r-(a-t*o))/t-n,isNaN(s)&&(s=o),a<i&&s>=0&&s<=pagewidth};this.mousemoveHandler=function(t){(c=i.now())-f>100&&(e=Math.sqrt(Math.pow(t.pageX-o,2)+Math.pow(t.pageY-a,2))/(c-f),o=t.pageX,a=t.pageY,f=c)}.bind(this),this.mouseleaveHandler=function(i){document.hasFocus()&&u(o,a,i.pageX,i.pageY)&&(this.dispose(),this.gstg._sync(function(){this.gstg.get("i")||(this.trigger.cpps.set("mouseoff",!0),this.journey.addEventsDefault("properties",{fs_inviteType:["mouseoff"]}),this.journey.addEventObj({name:"fs_mouseoff",metrics:{fs_mouseoff_speed:e,fs_mouseoff_slope:Math.max(t,-9999),fs_mouseoff_pagewidth:pagewidth,fs_mouseoff_xintercept:s,fs_mouseoff_xpercentage:s/pagewidth,fs_mouseoff_pagenumber:this.gstg.get("pv")}}),this.successFn&&this.successFn())}.bind(this)))}.bind(this)},w.prototype.checkCriteria=function(){if(this.mode&&"off"!==this.mode){var e=this.surveydef,i=this.gstg.get("pl"),s=this.gstg.get("pv"),n=e.mouseoff.sp||e.criteria.sp,r=e.mouseoff.lf||e.criteria.lf,o=100*Math.random();return n=t.isDefined(i)&&1!=i?n.outreplaypool||0:n.reg||0,s>=r&&o<=n}},w.prototype.startListening=function(e,t){this.startFn=e,this.successFn=t,"multitab"===this.mode&&this.startListeningMultiTab()},w.prototype.startListeningMultiTab=function(){var e,t=this.surveydef.mouseoff.minPageTime||0,s=this.surveydef.mouseoff.minSiteTime||0,n=this.gstg.get("sst")+s-i.now();e=Math.max(t,n),this.timeout=setTimeout(function(){this.startFn(),i.Bind(document.documentElement,"mouseoff:mousemove",this.mousemoveHandler,!0),i.Bind(document.documentElement,"mouseoff:mouseleave",this.mouseleaveHandler,!0),this.gstg.setUpdateInterval(1e4),this.gstg.watchForChanges(["i"],function(e,t,i){this.gstg.setUpdateInterval(6e4),this.dispose()}.bind(this),!0,!0)}.bind(this),e)},w.prototype.dispose=function(){i.Unbind("mouseoff:*"),clearTimeout(this.timeout)};var _,S=new i.FSEvent;t.API.expose("CPPS",{set:function(){S.subscribe(function(e){return function(){s.CPPS.set.apply(s.CPPS,e)}}(arguments),!0,!0)},get:function(e,t){t=t||function(){},S.subscribe(function(e){return function(){t(s.CPPS.get.apply(s.CPPS,e[0]))}}([arguments]),!0,!0)},all:function(e){e=e||function(){},S.subscribe(function(t){return function(){e(s.CPPS.all.apply(s.CPPS))}}(),!0,!0)}}),t.API.expose("clearState",function(){S.subscribe(function(){s.tracker&&s.tracker._heartbeat&&clearInterval(s.tracker._heartbeat),s.stg.reset(),s.rec&&s.rec.recorder&&s.rec.recorder.clearState()},!0,!0)}),t.API.expose("dispose",function(){S.subscribe(function(){s.trig&&s.trig.dispose()},!0,!0)}),t.API.expose("getState",function(e){S.subscribe(function(){e(s.state)},!0,!0)}),t.API.expose("getConfig",function(){return t.ext({},config,{global:t.config})}),t.API.expose("getConfigFormatted",function(){if(console&&console.info&&(console.info("************************** Trigger Configuration **************************"),console.info("Config: ",config.config),config.surveydefs&&config.surveydefs.length)){console.info("************************** Surveydefs Configuration **************************");for(var e=0;e<config.surveydefs.length;e++)console.info("************************** Surveydef "+(e+1)+" **************************"),console.info("Config: ",config.surveydefs[e])}}),t.API.expose("optOut",function(){var e=o.location.toString();o.location=e.indexOf("#")?e.substr(0,e.indexOf("#")-1)+"#fscommand=fsoptout":e+"#fscommand=fsoptout",o.location.reload()}),t.API.expose("test",function(){var e=o.location.toString();o.location=e.indexOf("#")?e.substr(0,e.indexOf("#")-1)+"#fscommand=fstest":e+"#fscommand=fstest",o.location.reload()});var E=function(){S.subscribe(function(){_&&(clearTimeout(_),_=null),_=setTimeout(function(){if(_=null,!s._triggerResetLock){s._triggerResetLock=!0;var e=s.trig;e&&(e.dispose(),s.trig=null),t.startTS=i.now(),t.nextTick(function(){R()})}},250)},!0,!0)};t.API.expose("run",E),t.API.expose("pageReset",E),t.API.expose("showInvite",function(e){S.subscribe(function(){var t=s.trig||D(s.stg,config,s.browser,s.crit,s.CPPS);if(t.init()&&t.surveydef){d(t,s.browser,s.stg,s.CPPS,e,s.jrny,function(){this.initialize(),this.present()})}},!0,!0)}),t.API.expose("onLoaded",s.loadedEmitter),t.API.expose("onInitialized",s.initializedEmitter),t.API.expose("onInviteShown",s.inviteShownEmitter),t.API.expose("onInviteAccepted",s.inviteAcceptedEmitter),t.API.expose("onInviteAbandoned",s.inviteAbandonedEmitter),t.API.expose("onInviteDeclined",s.inviteDeclinedEmitter),t.API.expose("onTrackerShown",s.trackerShownEmitter),t.API.expose("customInvitationRequested",s.customInvitationRequested),t.API.expose("Journey",{addEvent:function(){S.subscribe(function(e){return function(){s.jrny.addEvent.apply(s.jrny,e)}}(arguments),!0,!0)},addEventObj:function(){S.subscribe(function(e){return function(){s.jrny.addEventObj.apply(s.jrny,e)}}(arguments),!0,!0)},addEventString:function(){S.subscribe(function(e){return function(){s.jrny.addEventString.apply(s.jrny,e)}}(arguments),!0,!0)}}),t.API.expose("Storage",{get:function(e,t){t=t||function(){},S.subscribe(function(e){return function(){t(s.stg.get.apply(s.stg,e[0]))}}([arguments]),!0,!0)},all:function(e){e=e||function(){},S.subscribe(function(t){return function(){var t=s.stg.all();e(t)}}(),!0,!0)}}),t.API.expose("Cookie",{get:function(e,t){t=t||console.log||function(){},S.subscribe(function(e){return function(){try{t("_4c_"===e[0]?JSON.parse(c.decompress(decodeURIComponent(a.get(e[0])))):a.get(e[0]))}catch(t){console.error("trigger: couldn't read cookie",e[0])}}}(arguments),!0,!0)}});var I=function(e,i,n,r,o){t.ext(s,{CPPS:n,crit:i,stg:e,jrny:r,browser:o},!1),S.fire()},k=function(i,n,r,a,c,f){n&&a&&(t.isDefined(t.config.products.record)&&!1===t.config.products.record&&t.productConfig.record||e([t.makeURI("$fs.record.js")],function(e){r.set("rc","true"),s.RecordController=e,s.rec=e.getInstance(i,o,r,{id:config.config.id},c),f&&(f.recordController=rec)}))},D=function(e,config,t,i,s,n){return new C(e,config,t,i,s,n)},C=function(e,s,n,r,o,a){this.stg=e,this.cfg=s,this.browser=n,this.crit=r,this.cpps=o,this.jrny=a;var c,f,u=t.config.adobeRsid;if(!e.get("pv")){c={browser:n.browser.name+" "+n.browser.version,fp:n.fp,os:n.os.name,referrer:document.referrer.toString(),site:i.getRootDomain(),sitekey:s.config.site_key||"",terms:this.decodeReferrer()||""};for(f in c)c.hasOwnProperty(f)&&o.set(f,c[f]);i.INT.GA.has()&&setTimeout(t.proxy(function(){i.INT.GA.uid(function(e){e&&o.set("GA_UID",e)})},this),2e3);var l=function(e){o.set(e.name,e.value)};u&&(i.INT.OM.uid(u,l),i.INT.OM.mcid(u,l));var d=i.INT.OM.beacon();d&&o.set("OMTR_BEACON",d)}this.heartbeatExpired=new i.FSEvent};C.prototype.doesPassCriteria=function(){var e=this.crit,t=this.cfg,i=s.state,n="DIDNOTPASSCRITERIA";if(e.platformCheck(this.browser,t))if(e.browserCheck(this.browser,t))if(e.checkDeviceBlacklist(this.browser,t)){if(e.featureCheck(this.browser,t))return!0;i.inviteStatus=n,i.reason="BROWSER"}else i.inviteStatus=n,i.reason="DEVICE";else i.inviteStatus=n,i.reason="BROWSER";else i.inviteStatus=n,i.reason="PLATFORM";return!1},C.prototype.popTracker=function(e){var t=this;if(this.stg.set("i","x"),s.state.inviteSituation="ACCEPTED",this.didPopTrackerAlready="y"==this.stg.get("dt"),s.state.didInvite=!0,!this.didPopTrackerAlready){this.stg.set("dt","y");if(e)!function(){t.tracker=new h(e.template,t.surveydef,config,i.getBrainStorage(t.browser,t.stg.uid),t.cpps,e.display,t.browser),t.tracker.show(t.browser)}();else{var n=f("about:blank","fsTracker",{width:700,height:400},this.browser,!0);d(this,t.browser,t.stg,t.cpps,!1,t.jrny,function(){t.tracker=new h(this.invite.template,t.surveydef,config,i.getBrainStorage(t.browser,t.stg.uid),t.cpps,this.invite.display,t.browser),t.tracker.applyExisting(t.browser,n),t.surveydef.cxRecord&&s.rec&&s.rec.recorder&&s.rec.beginTransmitting()})}}},C.prototype.canDisplayInvitation=function(){return this.crit._match(this.cfg.config,this.browser,"inviteExclude")},C.prototype.popSurvey=function(e){if(this.stg.set("i","x"),s.state.inviteSituation="ACCEPTED",this.didPopTrackerAlready="y"==this.stg.get("dt"),s.state.didInvite=!0,this.didPopTrackerAlready)this.stg&&i.getBrainStorage(this.browser,this.stg.uid).set("trackercmd",{method:"survey"},6e4,!0);else{this.stg.set("dt","y");var t=new v(config,this.cpps,this.surveydef,null,e),n=t.getUrl();f(n,"acsSurvey",{width:700,height:400},this.browser,!1)}},C.prototype.init=function(){var e,i,s,n=this.cfg.surveydefs,r=this.stg.get("def");for(e=0;e<n.length;e++)s=n[e],i&&(s=t.ext(i,s),!n[e].site&&i.site&&delete s.site,!n[e].section&&i.section&&delete s.section,n[e]=s),i=t.ext({},s);if(t.isDefined(r)&&parseInt(r)>n.length-1&&(r=void 0),t.isDefined(r)&&"default"!=n[parseInt(r)].selectMode&&"pin"!=n[parseInt(r)].selectMode){if(t.isDefined(r)||"lock"==n[parseInt(r)].selectMode)return s=n[parseInt(r)],this.cfg.active_surveydef=s,this.surveydef=s,this._setupTrueConversionIfRequired(),this.locale=this._initLocale(),this.cpps.set("locale",this.locale),s.section&&this.cpps.set("section",s.section),s}else for(e=0;e<(t.isDefined(r)&&"default"!=n[parseInt(r)].selectMode?parseInt(r)+1:n.length);e++)if(s=n[e],t.isDefined(r)&&r==e&&"default"!=n[parseInt(r)].selectMode||this.crit._match(s,this.browser))return"x"===this.stg.get("i")&&this.stg.set("def",e,this.cfg.config.surveyDefResetTimeout||864e5),s.index=e,this.cfg.active_surveydef=s,this.surveydef=s,this._setupTrueConversionIfRequired(),this.locale=this._initLocale(),this.cpps.set("locale",this.locale),s.section&&this.cpps.set("section",s.section),this.inviteIndex=e,s;return!1},C.prototype._initLocale=function(){var e,s=this.surveydef,n=s.language;if(t.isDefined(n.src)&&t.isDefined(n.locales)){switch(n.src){case"variable":t.isDefined(n.name)&&(e=i.retrieveNestedVariable(window,n.name));break;case"cookie":if(t.isDefined(n.name)){e=new i.Cookie({}).get(n.name)}break;case"url":var r=n.locales;if(t.isDefined(r))for(var o=0,a=r.length;o<a;o++)if(t.isDefined(r[o].locale)&&t.isDefined(r[o].match)&&location.href.match(r[o].match))return this.locale=r[o].locale,r[o].criteria&&t.ext(this.surveydef.criteria,r[o].criteria),this.locale!==s.language.locale&&(s.language.locale=this.locale),r[o].locale}if(e)for(var c=0;c<n.locales.length;c++)if(n.locales[c].match==e)return n.locale=n.locales[c].locale,n.locales[c].criteria&&t.ext(this.surveydef.criteria,n.locales[c].criteria),n.locale}return n.locale||"en"},C.prototype.cancelTracker=function(){i.getBrainStorage(this.browser,this.stg.uid).set("trackercmd",{method:"close"},6e4,!0),this.stg.set("i","a"),s.state.inviteSituation="ABANDONED",t.isDefined(this.tracker)&&clearInterval(this.tracker._heartbeat)},C.prototype._setupTrueConversionIfRequired=function(){var i=(this.surveydef,this.cfg.config);i.trueconversion&&i.trueconversion.enabled&&e([t.makeURI("$fs.trueconversion.js")],t.proxy(function(e){this.trueconversion=new e(this)},this))},C.prototype.logState=function(){this.pageViewCount=(this.stg.get("pv")||0)+1,this.stg.set("pv",this.pageViewCount,config.config.pageViewsResetTimeout||864e5)},C.prototype.logDefState=function(){if(this.surveydef){var e=this.surveydef.name;e+=this.surveydef.section||"",e+=this.surveydef.site||"",this.defPageViewCount=(this.stg.get(e+"pv")||0)+1,this.stg.set(e+"pv",this.defPageViewCount,config.config.pageViewsResetTimeout||864e5)}},C.prototype.evalLoyaltySampling=function(){var e=this.surveydef,i=this.stg.get("pl"),s=t.isDefined(i)&&1!=i?e.criteria.sp.outreplaypool||0:e.criteria.sp.reg||0,n=100*Math.random();return this.defPageViewCount>=e.criteria.lf&&n<=s},C.prototype.decodeReferrer=function(){var e,t,i=document.referrer||"",s=null,n=["q","p","query"];for(t=0;t<n.length&&!(s=i.match(new RegExp("[?&]"+n[t]+"=([^&]*)")));t++);return s?(e=decodeURIComponent(s[1]),e&&(e=e.replace(/\+/g," ")),e):e},C.prototype.dispose=function(){this.disposed||(this.stg.save(!0),this.disposed=!0,this.trueconversion&&this.trueconversion.dispose(),this.invite&&this.invite.dispose(),this.mouseoff&&this.mouseoff.dispose(),s.rec&&(s.RecordController.disposeInstance(),s.RecordController=null,s.rec=null),i.Unbind("trigger:*"))};var T=function(e,i,s,n,r,o){this.itype=e,this.cfg=i,this.def=s,this.cpps=n,this.rid=r,this._measureName=this.def.name+"-"+(t.isDefined(this.def.site)?this.def.site+"-":"")+(t.isDefined(this.def.section)?this.def.section+"-":"")+(o||this.def.language.locale)};T.prototype.init=function(e,t){t=t||function(){};var s=i.now()+"_"+Math.round(1e13*Math.random());l.ping(l.SERVICE_TYPES.mobileOnExitInitialize,{a:s,notify:e,b:i.hash(s),c:864e5,cid:this.cfg.config.id,sid:this._measureName,rid:this.rid,uid:i.now(),support:"SMSEMAIL"==this.itype?"b":"EMAIL"==this.itype?"e":"s",cpps:"version="+encodeURIComponent(this.cfg.config.version)+"&"+this.cpps.toQueryString()},t,t)},T.prototype.beginHeartbeat=function(){this._timer&&(clearTimeout(this._timer),this._timer=null);var e=t.proxy(function(){l.ping(l.SERVICE_TYPES.mobileOnExitHeartbeat,{cid:this.cfg.config.id,sid:this._measureName,rid:this.rid,uid:i.now()},function(){},function(){})},this);this._timer=setInterval(e,config.config.onExitMobileHeartbeatInterval),e()},i.registerProduct("foresee",config);var A=window!=o.top;if(s.loadedEmitter.fire(),("dontRunOtherIframes"!==config.config.workInIframes&&config.config.workInIframes||!A)&&!(o.__fsrtracker||o.location.toString().indexOf("survey.foreseeresults.com")>-1)){var x={hash:o.location.hash,href:o.location.href,pathname:o.location.pathname},R=function(){if(s._triggerResetLock=!0,x.href.indexOf("fs.tracker.html")>-1)return void(s._triggerResetLock=!1);var e=new i.Browser;e.ready.subscribe(function(){var n,r=i.getGeneralStorage(e),c=new y(r,config),f=new i.CPPS(r,config.config.cppsResetTimeout);f.addToBlacklist(config.config.disable_default_cpps||config.config.disable_cpps||[]),r.ready.subscribe(t.proxy(function(){r.upgradeOldStorage(function(){var u=s._journey=new i.Journey(config.config.id,i.APPID.TRIGGER,r.uid,e);u.addEventsDefault("properties",{fs_site:[i.getRootDomain()],fs_repeatDaysAccept:[config.config.repeatDays.accept],fs_repeatDaysDecline:[config.config.repeatDays.decline],fs_reinviteDelayAfterInviteAbandon:[config.config.reinviteDelayAfterInviteAbandon]}),I(r,c,f,u,e);var l=r.get("i");setTimeout(t.proxy(function(){if(f.set("url",o.location.toString()),f.set("code",t.config.codeVer),f.set("tz",(new Date).getTimezoneOffset()),config.config.cpps){var g,v,m=config.config.cpps;for(var y in m){var _=m[y];if(t.isObject(_))switch(_.source){case"param":var S=t.getParam(_.val)||_.init||null;if(t.isDefined(_.mode)&&"append"==_.mode){var E,I=_.delimiter||",",C=f.get(y),T=C?C.split(I):[];S=S||"",T[T.length-1]!==S&&(T.push(S),E=T.join(I),f.set(y,E))}else t.isDefined(S)&&null!==S?f.set(y,S):f.get(y)||f.set(y,"");break;case"variable":if(t.isDefined(_.name)){var x;g=_.exists,x=i.retrieveNestedVariable(o,_.name),t.isDefined(g)?f.get(y)!==g.success&&f.set(y,x?g.success:g.init):x?f.set(y,x):f.get(y)||f.set(y,_.init||"")}break;case"cookie":var R=a.get(_.val),P=t.isDefined(R);g=_.exists,t.isDefined(g)?f.get(y)!==g.success&&f.set(y,P?g.success:g.init):t.isDefined(R)&&null!==R?f.set(y,R):f.get(y)||f.set(y,_.init||"");break;case"url":for(var L=0,N=_.patterns.length;L<N;L++){var M=_.patterns[L].regex||_.patterns[L].match;v=_.patterns[L].value,t.isString(location.href)&&i.testAgainstSearch(M,location.href)?f.set(y,v):f.get(y)||f.set(y,_.init||"")}break;case"function":if(t.isFunction(_.value))try{v=_.value.call(o),f.set(y,v)}catch(e){}}else f.set(y,_)}}var O;if(r.get("ovr")&&(O=JSON.parse(r.get("ovr"))),O){for(var V=0;V<config.surveydefs.length;V++){var j=config.surveydefs[V].name;O.sp[j]&&(config.surveydefs[V].criteria.sp=O.sp[j]),O.lf[j]&&(config.surveydefs[V].criteria.lf=O.lf[j])}!0===O.pooloverride&&(c.pooloverride=!0)}if(s.state.codeVer=t.config.codeVer,s.state.siteKey=config.config.site_id,s.state.didInvite="xda".indexOf(l)>-1,s.state.inviteSituation={x:"ACCEPTED",d:"DECLINED",a:"ABANDONED",p:"PRESENTED"}[l],"a"==l){parseInt(r.get("rw"))<i.now()&&(r.erase("i"),r.erase("rw"),l=null)}if("runRecordOnly"===config.config.workInIframes&&A){for(var U=!1,F=0;F<config.surveydefs.length;F++){if(config.surveydefs[F].cxRecord){U=!0;break}}return k(e,U,r,!0,f),void(s._triggerResetLock=!1)}if("d"!=l&&"a"!=l)c.calcReplayPoolStatus(function(o){o&&(s.state.isinpool=o),c.optoutCheck(t.proxy(function(){if(c._match(config.config,e,"globalExclude")&&"y"!=r.get("gx"))if(null===r.selectCookieDomain(t.config.cookieDomain,window.location.toString()))s._triggerResetLock=!1;else{var a=s.trig=D(r,config,e,c,f,u);if(a.logState(),f.set("pv",a.pageViewCount,config.config.pageViewsResetTimeout||864e5),a.init())if(s.initializedEmitter.fire(),a.doesPassCriteria())if(a.surveydef){if(a.logDefState(),k(e,a.surveydef.cxRecord,r,o,f),"x"!=l){var g;if(e.isMobile||e.isTablet||!a.surveydef.mouseoff||"off"===a.surveydef.mouseoff.mode||(1==r.get("pv")&&r.set("sst",i.now()),r.get("sst")&&(g=a.mouseoff=new w(a,a.surveydef,e,r,u))),a.canDisplayInvitation())if(a.evalLoyaltySampling()){r.set("def",a.inviteIndex,a.cfg.config.surveyDefResetTimeout||864e5);d(a,e,r,f,!1,u,function(){this.initialize(),this.present()})}else g&&g.checkCriteria()&&(r.set("def",a.inviteIndex,a.cfg.config.surveyDefResetTimeout||864e5),g.initialize(),g.startListening(function(){this.inviteSetup=d(a,e,r,f,!1,u,function(){this.initialize()})},function(){this.inviteSetup.present()}),s._triggerResetLock=!1);else s._triggerResetLock=!1}else{var v=a.stg.get("mhbi");v?p(a,v.ui,v.it):d(a,a.browser,a.stg,a.cpps,!1,a.jrny,function(){this.invite&&this.invite.display&&this.invite.template&&(a.tracker=new h(this.invite.template,a.surveydef,config,i.getBrainStorage(e,r.uid),f,this.invite.display,e))}),s._triggerResetLock=!1}a.surveydef.links&&(n=new b(a.surveydef.links),n.performBindings(a))}else s._triggerResetLock=!1;else s._triggerResetLock=!1;else s._triggerResetLock=!1}else r.set("gx","y"),s._triggerResetLock=!1},this),function(){s._triggerResetLock=!1})});else{if("a"==l){var B="true"==r.get("rc")||!0===r.get("rc");k(e,B,r,B,f)}s._triggerResetLock=!1}},this),Math.max(0,config.config.triggerDelay-(i.now()-t.startTS)))})},this),!0,!0)},!0,!0)};t.domReady(R);var P;config.config.ignoreNavigationEvents||i.pageNavEvent.subscribe(function(){var e=o,t=e.location,i=e[config.config.publicApiName||"FSR"],s=function(e){var t=e.split("#");return t.length>2?t[0]+t[1]:e.replace(/#/gi,"")},n=s(x.hash),r=s(t.hash);(i&&n!=r||x.pathname!=t.pathname)&&fsReady(function(){clearTimeout(P),P=setTimeout(function(){x={hash:o.location.hash,href:o.location.href,pathname:o.location.pathname},i.pageReset()},1e3)})},!1,!1)}});