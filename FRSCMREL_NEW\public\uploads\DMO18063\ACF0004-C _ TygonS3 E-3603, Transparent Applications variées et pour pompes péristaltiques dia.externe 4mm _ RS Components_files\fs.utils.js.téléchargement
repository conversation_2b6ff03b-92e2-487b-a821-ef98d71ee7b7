/***************************************
* @preserve
* ForeSee Web SDK: Utils Library
* Built February 23, 18 13:09:14
* Code version: 19.6.1
* Template version: 19.6.1
***************************************/
_fsDefine(["require","fs"],function(t,e){function i(t,e){var i=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(i>>16)<<16|65535&i}function r(t,e){return t<<e|t>>>32-e}function n(t,e,n,s,a,o){return i(r(i(i(e,t),i(s,o)),a),n)}function a(t,e,i,r,s,a,o){return n(e&i|~e&r,t,e,s,a,o)}function o(t,e,i,r,s,a,o){return n(e&r|i&~r,t,e,s,a,o)}function f(t,e,i,r,s,a,o){return n(e^i^r,t,e,s,a,o)}function h(t,e,i,r,s,a,o){return n(i^(e|~r),t,e,s,a,o)}function u(t,e){t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;var r,n,s,u,l,c=1732584193,d=-271733879,p=-1732584194,g=271733878;for(r=0;r<t.length;r+=16)n=c,s=d,u=p,l=g,c=a(c,d,p,g,t[r],7,-680876936),g=a(g,c,d,p,t[r+1],12,-389564586),p=a(p,g,c,d,t[r+2],17,606105819),d=a(d,p,g,c,t[r+3],22,-1044525330),c=a(c,d,p,g,t[r+4],7,-176418897),g=a(g,c,d,p,t[r+5],12,1200080426),p=a(p,g,c,d,t[r+6],17,-1473231341),d=a(d,p,g,c,t[r+7],22,-45705983),c=a(c,d,p,g,t[r+8],7,1770035416),g=a(g,c,d,p,t[r+9],12,-1958414417),p=a(p,g,c,d,t[r+10],17,-42063),d=a(d,p,g,c,t[r+11],22,-1990404162),c=a(c,d,p,g,t[r+12],7,1804603682),g=a(g,c,d,p,t[r+13],12,-40341101),p=a(p,g,c,d,t[r+14],17,-1502002290),d=a(d,p,g,c,t[r+15],22,1236535329),c=o(c,d,p,g,t[r+1],5,-165796510),g=o(g,c,d,p,t[r+6],9,-1069501632),p=o(p,g,c,d,t[r+11],14,643717713),d=o(d,p,g,c,t[r],20,-373897302),c=o(c,d,p,g,t[r+5],5,-701558691),g=o(g,c,d,p,t[r+10],9,38016083),p=o(p,g,c,d,t[r+15],14,-660478335),d=o(d,p,g,c,t[r+4],20,-405537848),c=o(c,d,p,g,t[r+9],5,568446438),g=o(g,c,d,p,t[r+14],9,-1019803690),p=o(p,g,c,d,t[r+3],14,-187363961),d=o(d,p,g,c,t[r+8],20,1163531501),c=o(c,d,p,g,t[r+13],5,-1444681467),g=o(g,c,d,p,t[r+2],9,-51403784),p=o(p,g,c,d,t[r+7],14,1735328473),d=o(d,p,g,c,t[r+12],20,-1926607734),c=f(c,d,p,g,t[r+5],4,-378558),g=f(g,c,d,p,t[r+8],11,-2022574463),p=f(p,g,c,d,t[r+11],16,1839030562),d=f(d,p,g,c,t[r+14],23,-35309556),c=f(c,d,p,g,t[r+1],4,-1530992060),g=f(g,c,d,p,t[r+4],11,1272893353),p=f(p,g,c,d,t[r+7],16,-155497632),d=f(d,p,g,c,t[r+10],23,-1094730640),c=f(c,d,p,g,t[r+13],4,681279174),g=f(g,c,d,p,t[r],11,-358537222),p=f(p,g,c,d,t[r+3],16,-722521979),d=f(d,p,g,c,t[r+6],23,76029189),c=f(c,d,p,g,t[r+9],4,-640364487),g=f(g,c,d,p,t[r+12],11,-421815835),p=f(p,g,c,d,t[r+15],16,530742520),d=f(d,p,g,c,t[r+2],23,-995338651),c=h(c,d,p,g,t[r],6,-198630844),g=h(g,c,d,p,t[r+7],10,1126891415),p=h(p,g,c,d,t[r+14],15,-1416354905),d=h(d,p,g,c,t[r+5],21,-57434055),c=h(c,d,p,g,t[r+12],6,1700485571),g=h(g,c,d,p,t[r+3],10,-1894986606),p=h(p,g,c,d,t[r+10],15,-1051523),d=h(d,p,g,c,t[r+1],21,-2054922799),c=h(c,d,p,g,t[r+8],6,1873313359),g=h(g,c,d,p,t[r+15],10,-30611744),p=h(p,g,c,d,t[r+6],15,-1560198380),d=h(d,p,g,c,t[r+13],21,1309151649),c=h(c,d,p,g,t[r+4],6,-145523070),g=h(g,c,d,p,t[r+11],10,-1120210379),p=h(p,g,c,d,t[r+2],15,718787259),d=h(d,p,g,c,t[r+9],21,-343485551),c=i(c,n),d=i(d,s),p=i(p,u),g=i(g,l);return[c,d,p,g]}function c(t){var e,i="";for(e=0;e<32*t.length;e+=8)i+=String.fromCharCode(t[e>>5]>>>e%32&255);return i}function d(t){var e,i=[];for(i[(t.length>>2)-1]=void 0,e=0;e<i.length;e+=1)i[e]=0;for(e=0;e<8*t.length;e+=8)i[e>>5]|=(255&t.charCodeAt(e/8))<<e%32;return i}function p(t){return c(u(d(t),8*t.length))}function g(t,e){var i,r,n=d(t),s=[],a=[];for(s[15]=a[15]=void 0,n.length>16&&(n=u(n,8*t.length)),i=0;i<16;i+=1)s[i]=909522486^n[i],a[i]=1549556828^n[i];return r=u(s.concat(d(e)),512+8*e.length),c(u(a.concat(r),640))}function y(t){var e,i,r="0123456789abcdef",n="";for(i=0;i<t.length;i+=1)e=t.charCodeAt(i),n+=r.charAt(e>>>4&15)+r.charAt(15&e);return n}function b(t){return decodeURIComponent(e.enc(t))}function w(t){return p(b(t))}function v(t){return y(w(t))}function m(t,e){return g(b(t),b(e))}function S(t,e){return y(m(t,e))}function x(t,e){var i,r,n,s=t.keys,a=e.keys;for(i in s)n=a[i],r=s[i],n?r.t<n.t&&(t.keys[i]=a[i]):r.t<e.when&&delete s[i];for(i in a)s.hasOwnProperty(i)||(n=a[i],n.t>t.when&&(t.keys[i]=a[i]));return t}var _="undefined"!=typeof Uint8Array,k=window,B={APPID:{TRIGGER:"funcxm",FEEDBACK:"funfbk",REPLAY:"funrep"}},T={StorageInstances:{}};e&&e.home&&e.home,B.md5=function(t,e,i){return e?i?m(e,t):S(e,t):i?w(t):v(t)},B.escapeRegExp=function(t){return(t||"").toString().replace(/([-.*+?^${}()|[\]\/\\])/g,"\\$1")},B.trim=function(t){return(t||"").toString().replace(/\s+/g," ").replace(/^\s+|\s+$/g,"")},B.stripHTML=function(t){return(t||"").replace(/(<([^>]+)>)/gi,"")},B.unlink=function(t){var i,r;if(e.isPlainObject(t)){i={};for(var n in t)i[n]=B.unlink(t[n])}else if(Array.isArray(t))for(i=[],r=0,l=t.length;r<l;r++)i[r]=B.unlink(t[r]);else i=t;return i};var I={};B.preventDefault=function(t){t&&t.preventDefault?t.preventDefault():window.event&&window.event.returnValue?window.eventReturnValue=!1:t.returnValue=!1};var A=[],E=function(t){var e="default";if(t.indexOf(":")>-1){var i=t.split(":");e=i[0],t=i[1]}return I[e]||(I[e]={}),I[e][t]||(I[e][t]=[]),{ns:e,en:t}};B.Bind=function(t,e,i,r){if(t&&e){var n=E(e);if(I[n.ns][n.en].push({elem:t,cb:i,ub:!!r}),e.indexOf("unload")>-1)return void A.push(i);"propertychange"!=n.en&&t.addEventListener?t.addEventListener(n.en,i,!r):t.attachEvent&&t.attachEvent("on"+n.en,i)}},B.BindOnce=function(t,e,i){if(t&&e){var r=E(e);if(t["_acsEvent"+r.en])return;t["_acsEvent"+r.en]=!0,B.Bind(t,e,i)}};var R=function(t,e,i,r){e&&(e.parentNode||e.window||9==e.nodeType)&&("propertychange"!=t&&e.removeEventListener?e.removeEventListener(t,i,!r):e.detachEvent&&e.detachEvent("on"+t,i))};B.Unbind=function(t,e,i,r){var n,s,a,o;if(e&&e.indexOf("unload")>-1){for(o=0;o<A.length;o++)if(A[o]==i){A.splice(o,1);break}}else if(0===arguments.length)for(var f in I)B.Unbind(f+":*"),delete I[f];else if("string"==typeof t)if(n=E(t),"default"==n.ns){for(var h in I)if(I.hasOwnProperty(h)){s=I[h];for(var u in s)if(s.hasOwnProperty(u)&&(u==n.en||"*"==n.en))for(o=0;o<s[u].length;o++)a=s[u][o],R(u,a.elem,a.cb,a.ub),s[u].splice(o--,1)}}else{s=I[n.ns];for(var l in s)if(s.hasOwnProperty(l)&&(l==n.en||"*"==n.en))for(o=0;o<s[l].length;o++)a=s[l][o],R(l,a.elem,a.cb,a.ub),s[l].splice(o--,1)}else if(t&&!e){for(var c in I)if(I.hasOwnProperty(c)){s=I[c];for(var d in s)if(s.hasOwnProperty(d))for(o=0;o<s[d].length;o++)a=s[d][o],a.elem===t&&(R(d,a.elem,a.cb,a.ub),s[d].splice(o--,1))}}else if(t&&e)if(n=E(e),"default"==n.ns){for(var p in I)if(I.hasOwnProperty(p)){s=I[p];for(var g in s)if(s.hasOwnProperty(g)&&(g==n.en||"*"==n.en))for(o=0;o<s[g].length;o++)a=s[g][o],a.elem===t&&(R(g,a.elem,i||a.cb,a.ub),s[g].splice(o--,1))}}else{n=E(e),s=I[n.ns];for(var y in s)if(s.hasOwnProperty(y)&&(y==n.en||"*"==n.en))for(o=0;o<s[y].length;o++)a=s[y][o],a.elem===t&&(R(y,a.elem,i||a.cb,a.ub),s[y].splice(o--,1))}};var D=!1;if(B.preventUnloadFlag=!1,B._preventUnloadFor=function(t){D=!0,setTimeout(function(){D=!1},t)},B.HandleUnload=function(){if(!D&&!B.preventUnloadFlag){for(var t=A.length-1;t>=0;t--)try{A[t].call()}catch(t){}e.dispose(A),B.Unbind()}},document.addEventListener?(window.addEventListener("beforeunload",B.HandleUnload,!0),window.addEventListener("pagehide",B.HandleUnload,!0),document.addEventListener("unload",B.HandleUnload,!0)):document.attachEvent&&window.attachEvent("onunload",B.HandleUnload),B.getKeyCode=function(t){return t&&void 0!==t.key?t.key.toLowerCase():{32:" ",13:"enter",37:"arrowleft",39:"arrowright"}[t.keyCode>0?t.keyCode:t.which]},B.FSEvent=function(){this.id="_"+Math.round(99999*Math.random()),this.subscriptions=[],this.didFire=!1},B.FSEvent.prototype.subscribe=function(t,e,i){return this.subscriptions.push({once:!!e,cb:t}),i&&this.didFire&&(this.prevArgs?this.fire.apply(this,this.prevArgs):this.fire()),{unsubscribe:function(t,e){return function(){t.unsubscribe(e)}}(this,t)}},B.FSEvent.prototype.unsubscribe=function(t){for(var e=0;e<this.subscriptions.length;e++)this.subscriptions[e].cb==t&&(this.subscriptions.splice(e,1),e--)},B.FSEvent.prototype.unsubscribeAll=function(){this.subscriptions=[]},B.FSEvent.prototype.fire=function(){this.didFire=!0,this.prevArgs=arguments;for(var t=0;t<this.subscriptions.length;t++){var e=this.subscriptions[t];e.once&&this.subscriptions.splice(t--,1),e.cb.apply(this,arguments)}},B.pageNavEvent=new B.FSEvent,history&&history.pushState){window.addEventListener("popstate",function(t){D||B.pageNavEvent.fire()});var C=history.pushState;history.pushState=function(){C.apply(history,arguments),D||B.pageNavEvent.fire()}}B.FSEvent.prototype.chain=function(t,i,r){t&&t.constructor===B.FSEvent&&t.subscribe(e.proxy(function(){this.fire.apply(this,arguments)},this),i,r)};var O=function(t){this.browser=t,this.sig="not detected",this.ready=new B.FSEvent,this._detect()};O.prototype._detect=function(){var t,i=e.proxy(function(t){this.sig=t,this.ready.fire(t)},this),r=[],n=navigator,s=this.browser;if(k!=k.top){var a=location.search.match(/uid=([\d\w]*)/i);a&&a[1]&&(t=a[1])}t&&"not detected"!=t||!s.supportsLocalStorage||(t=localStorage.getItem("_fsPRINT")),t||(r=B.trim(navigator.userAgent.replace(/[0-9\.\/\\\(\);_\-]*/gi,"")).split(" "),r.push(n.language||""),r.push(n.hardwareConcurrency||""),r.push(n.platform||""),r.push(n.vendor||""),r.push(n.appName||""),r.push(n.maxTouchPoints||""),r.push(n.doNotTrack||"false"),r.push(s.os.name||"false"),r.push(s.os.version||"false"),r.push(this._getCanvasPrint()),t=B.md5(r.join("")),this.sig=t,s.supportsLocalStorage&&localStorage.setItem("_fsPRINT",t)),e.nextTick(function(){i(t)})},O.prototype._getCanvasPrint=function(){try{var t=document.createElement("canvas"),e=t.getContext("2d"),i="ForeSee,CloudUser <canvas> 1.0";return t.width=250,t.height=30,e.textBaseline="top",e.font="14px 'Arial'",e.textBaseline="alphabetic",e.fillStyle="#f60",e.fillRect(125,1,62,20),e.fillStyle="#069",e.fillText(i,2,15),e.fillStyle="rgba(102, 204, 0, 0.7)",e.fillText(i,4,17),t.toDataURL()}catch(t){return"nocanvas"}},B.Fingerprint=O;var L=function(t){this.browser=t,this.ready=new B.FSEvent,this.ajax=new B.AjaxTransport,e.nextTick(e.proxy(function(){this.ready.fire()},this))};L.prototype.send=function(t){this.ready.subscribe(e.proxy(function(){if(this.ajax)this.ajax.send(t);else{var i=e.ext({method:"GET",contentType:"application/x-www-form-urlencoded; charset=UTF-8",success:function(){},failure:function(){}},t);this.fstg.ajax(i.method,i.url,i.data,function(t){return function(e,i){e?t.success(i):t.failure(i)}}(i),!e.isDefined(t.skipEncode)||!!t.skipEncode,i.contentType)}},this),!0,!0)},L.prototype.dispose=function(){this.ajax&&this.ajax.dispose()},B.CORS=L,B.storageTypes={CK:"COOKIE",MC:"MICROCOOKIE",CL:"COOKIELESS",DS:"DOMSTORAGE"};var N=function(t){var i=new Date;return{path:"/",domain:t.selectCookieDomain(e.config.cookieDomain,window.location.toString()),secure:!1,encode:!0,expires:new Date(i.getFullYear()+2,i.getMonth(),i.getDate()).toUTCString()}},U=function(t,i){B.storageTypes;this.pers=(e.config.storage||"").toUpperCase(),e.ext(this,{_storageKey:"_4c_",_microStorageKey:"_4c_mc_",isReady:!1,defaultExpire:7776e6,ready:new B.FSEvent,onCommit:new B.FSEvent,onSync:new B.FSEvent,_readyState:new B.FSEvent,maxExpire:-1,timeOffset:0,_keyEvents:{},_updateTimeout:6e4,_data:{when:0,keys:{}},isStale:!1,lock:null,lastMaint:B.now(),lastSave:B.now(),lastSync:B.now(),isSyncing:!1}),this.browser=t,B.Bind(window,"unload",function(){this.save(!0)}.bind(this))};U.prototype.selectCookieDomain=function(t,i){if(!e.isDefined(t)||!Array.isArray(t)||t.length<1)return B.getRootDomain();var r,n,s;for(n=0;n<t.length;n++)if((s=t[n])&&s.path&&s.domain&&B.testAgainstSearch(s.path,i)){r=s;break}return r&&e.isString(r.domain)?r.domain:null},U.prototype.upgradeOldStorage=function(i){var r=this.ckie,n=["fsr.r","fsr.s","_fsspl_","fsr.t","acs.t"],s=!1;this.pers===B.storageTypes.MC&&n.push("_4c_");for(var a=0;a<n.length;a++)if(r.get(n[a])){s=!0;break}s?t([e.makeURI("$fs.storageupgrade.js")],function(t){t(this,r,i)}.bind(this)):e.nextTick(i)},U.prototype.setUpdateInterval=function(t){t&&!isNaN(t)&&(this._updateTimeout=t,clearInterval(this._updateInterval),this._updateInterval=setInterval(function(){this._sync()}.bind(this),t))},U.prototype.stopUpdateInterval=function(){clearInterval(this._updateInterval),this._updateInterval=null},U.prototype._fireChangeEvents=function(t){var i,r=this;for(var n in t)(!(i=this._data.keys[n])||i.t<t[n].t||i.x!==t[n].x)&&(this._keyEvents[n]||(this._keyEvents[n]=new B.FSEvent),e.nextTick(function(e){return function(){r._keyEvents[e].fire(e,r._data.keys[e],t[e].v)}}(n)))},U.prototype.save=function(t){if(t)this._commit();else{var e=B.now();!this.lock&&this.isStale&&(this.lock=setTimeout(this._commit.bind(this),Math.max(0,this.cThreshold-(e-this.lastSave))))}},U.prototype._maint=function(t){var e,i=B.now(),r=!1,n=this._data.keys;if(i-this.lastMaint>5e3||t){for(var s in n)e=n[s],i-this.timeOffset>e.x&&(delete n[s],r=!0);this.lastMaint=i}!r||this.pers!=B.storageTypes.CK&&this.pers!=B.storageTypes.DS||this._commit()},U.prototype.set=function(t,i,r,n,s){this._readyState.subscribe(function(){this._data.keys||(this._data.keys={});var a=this._data.keys[t],o=B.now(),f=null;if(r)if("number"==typeof r)f=r,this.maxExpire>0&&this.maxExpire<r&&(f=r=this.maxExpire),r=o+r;else if(r instanceof Date&&(r=r.getTime()+r,this.maxExpire>0)){var h=r-o;h>this.maxExpire&&(f=r=this.maxExpire,r=o+r)}if(a){var u={};u[t]={v:i,x:r||a.x,t:o},this._fireChangeEvents(u),a.v="cp"==t?e.ext(a.v,i):"ckcpps"==t?e.ext(a.v,i):i,a.x=r||a.x,f&&(a.ttl=f),a.t=o}else{var l={};l[t]={v:i,x:r||this.defaultExpire+o,t:o},f&&(l[t].ttl=f),this._fireChangeEvents(l),this._data.keys[t]=l[t]}this.isStale=!0,s&&this.onCommit.subscribe(s,!0,!1),this._maint(),this.save(!!n)}.bind(this),!0,!0)},U.prototype.get=function(t){if(Array.isArray(t)){for(var e={},i=0;i<t.length;i++)e[t[i]]=this.get(t[i]);return e}return this._maint(),this._data.keys||(this._data.keys={}),(this._data.keys[t]||{v:null}).v},U.prototype.all=function(){return this._data.keys},U.prototype.erase=function(t,e,i){if(Array.isArray(t))for(var r=0;r<t.length;r++)this.erase(t[r]);else this._maint(),delete this._data.keys[t],e&&this.onCommit.subscribe(e,!0,!1),T.StorageInstances.brainStorage&&this._delete(t),this.pers==B.storageTypes.CL&&this.pers==B.storageTypes.MC||this.save(!!i)},U.prototype.reset=function(t,i,r){var n=document.getElementById("acsOverrideSettings"),s=document.getElementById("acsClearStateWaitMessage"),a=!!n&&!!s;if(this._data.keys={},t&&this.onCommit.subscribe(t,!0,!1),r||(this.ckie.kill(this._storageKey),this.ckie.kill(this._microStorageKey)),this.pers==B.storageTypes.CK){if(localStorage&&e.supportsDomStorage)for(var o in localStorage)/^(_fsr|__fsFr)/.test(o)&&localStorage.removeItem(o);this.onCommit.fire()}else this.pers==B.storageTypes.DS?(localStorage.removeItem(this._storageKey),this.onCommit.fire()):(this._readyState.didFire&&(this._readyState=new B.FSEvent),a&&(B.addClass(n,"acsNoDisplay"),B.removeClass(s,"acsNoDisplay")),this.cors.send({method:"DELETE",url:e.config.brainUrl+"/state/"+e.config.siteKey+"/"+this.uid,success:function(){a&&(B.removeClass(n,"acsNoDisplay"),B.addClass(s,"acsNoDisplay")),this.lastSync=B.now()-1e4,this.onCommit.fire(),this._readyState.fire(this)}.bind(this),failure:function(){a&&(B.removeClass(n,"acsNoDisplay"),B.addClass(s,"acsNoDisplay"),i&&i()),this._serverFails++,this._readyState.fire(this)}.bind(this)}))},U.prototype.setMaxKeyExpiration=function(t){this.maxExpire=this.defaultExpire=t;var e,i=B.now(),r=this._data.keys;for(var n in r){e=r[n];var s=e.x-i;(s>t||e.ttl>t)&&(e.ttl=t,e.x&&(e.x-=s-t))}this.save(!0)},U.prototype.getMaxKeyExpiration=function(){var t=B.now(),e=this._data.keys,i=0;for(var r in e)i=Math.max(i,e[r].x-t);return i},U.prototype.watchForChanges=function(t,e,i,r){Array.isArray(t)||(t=[t]);for(var n=0;n<t.length;n++){var s=t[n];this._keyEvents[s]||(this._keyEvents[s]=new B.FSEvent),this._keyEvents[s].subscribe(e,i,r)}},B.getGeneralStorage=function(t,i){var r,n=e.config.storage.toUpperCase(),s=T.StorageInstances,a=B.storageTypes;return t.supportsLocalStorage||n!=a.DS?t.isMobile&&n==a.CL&&(n=a.MC):n=a.CK,r=n==B.storageTypes.CK||n==B.storageTypes.DS,r?(s.generalStorage||(s.generalStorage=new M(t,i)),s.generalStorage):(s.brainStorage||(s.brainStorage=new F(t,i)),s.brainStorage)},B.getBrainStorage=function(t,e,i,r){var n=T.StorageInstances;return n.brainStorage||(n.brainStorage=new F(t,e,i,r)),n.brainStorage},B.INT={},B.getSize=function(t){var e=0,i=0,r=t.document,n=r.documentElement;return"number"==typeof t.innerWidth?(e=t.innerWidth,i=t.innerHeight):n&&(n.clientWidth||n.clientHeight)?(e=n.clientWidth,i=n.clientHeight):r.body&&(r.body.clientWidth||r.body.clientHeight)&&(e=r.body.clientWidth,i=r.body.clientHeight),{w:e,h:i}},B.getScroll=function(t){var e=0,i=0,r=t.document,n=r.documentElement;return"number"==typeof t.pageYOffset?(i=t.pageYOffset,e=t.pageXOffset):r.body&&(r.body.scrollLeft||r.body.scrollTop)?(i=r.body.scrollTop,e=r.body.scrollLeft):n&&(n.scrollLeft||n.scrollTop)&&(i=n.scrollTop,e=n.scrollLeft),{x:e,y:i}},B.setScroll=function(t,e,i){t.scrollTo(e,i)},B.getScreenResolution=function(){var t=window.screen;return e.isDefined(t)&&e.isDefined(t.width)&&"number"==typeof t.width?{w:t.width,h:t.height}:{w:0,h:0}},B.getFrameWindow=function(t){var e;return t&&t.contentWindow?e=t.contentWindow:t&&t.contentDocument&&t.contentDocument.defaultView&&(e=t.contentDocument.defaultView),e&&e!=e.top?e:null};var F=function(t,i,r,n){this.brainUrl=n||e.config.brainUrl,this.siteKey=r||e.config.siteKey,U.call(this,t,i),e.ext(this,{_serverFails:0,cThreshold:300});var s=B.storageTypes;t.ready.subscribe(function(){this.cors=new B.CORS(t),this.pers==s.MC?(this.ckie=new B.Cookie(N(this)),this.uid=i||this.ckie.get(this._microStorageKey),this.uid&&(this.uid.length>64||this.uid.indexOf("{")>-1)&&(this.uid=B.generateGUID(),this.ckie.set(this._microStorageKey,this.uid)),this.uid||(this.uid=B.generateGUID(),this.ckie.set(this._microStorageKey,this.uid))):this.pers==s.CL?(this.ckie=new B.Cookie(N(this)),this.uid=i||t.fp):i&&(this.uid=i),this._sync(function(){this.get("rid")?this.uid=this.get("rid"):(this.uid=this.uid||B.generateGUID(),this.set("rid",this.uid)),this.setUpdateInterval(this._updateTimeout),this._readyState.fire(this),this.ready.fire(this)}.bind(this))}.bind(this),!0,!0)};F.prototype=Object.create(U.prototype),F.prototype.constructor=U,F.prototype._sync=function(t){if(!this.isSyncing){this.isSyncing=!0,t=t||function(){};var i;if(this._serverFails>5)return;this.cors.send({method:"GET",url:this.brainUrl+"/state/"+this.siteKey+"/"+this.uid,success:function(r){this.lastSync=B.now(),i=JSON.parse(r),this.timeOffset=B.isNumeric(i._asof_)?B.now()-i._asof_:0,this._fireChangeEvents(i.keys),this.mergeBrainData(i),this.isSyncing=!1,e.nextTick(function(){this.onSync.fire(this),this._readyState.fire(this)}.bind(this)),t()}.bind(this),failure:function(){this.lastSync=B.now(),this.isSyncing=!1,this._serverFails++,this._readyState.fire(this)}.bind(this)})}},F.prototype._commit=function(){clearTimeout(this.lock),this.lock=null,this.lastSave=this._data.when=B.now(),this._serverFails>5||(this.cors.send({method:"POST",url:this.brainUrl+"/state/"+this.siteKey+"/"+this.uid,data:this._data,contentType:"application/json",success:function(t){this._lastSync=B.now(),this.mergeBrainData(JSON.parse(t)),this.onCommit.fire(this._data),this._readyState.fire(this)}.bind(this),failure:function(){this._serverFails++,this._readyState.fire(this)}.bind(this)}),this.isStale=!1)},F.prototype._delete=function(t){this.lastSave=this._data.when=B.now(),this.cors.send({method:"DELETE",url:this.brainUrl+"/state/"+this.siteKey+"/"+this.uid+"/"+e.enc(t),contentType:"application/json",success:function(t){this._lastSync=B.now(),this.onCommit.fire(this._data),this._readyState.fire(this)}.bind(this),failure:function(){this._serverFails++,this._readyState.fire(this)}.bind(this)})},F.prototype.mergeBrainData=function(t){return x(this._data,t)},B.AjaxTransport=function(t){var i={method:"POST",data:{},contentType:"application/x-www-form-urlencoded",success:function(){},failure:function(){}};this.options=e.ext(i,t)},B.AjaxTransport.prototype.send=function(t){var i=e.ext({},this.options,t||{});window.XDomainRequest&&-1==window.navigator.userAgent.indexOf("MSIE 10")?this._sendViaXDR(i):window.XMLHttpRequest&&this._sendViaXHR(i),i=null},B.AjaxTransport.prototype.dispose=function(){e.dispose(this.options)},B.AjaxTransport.isSupported=function(){return!0},B.AjaxTransport.initialize=function(t){t.call(B.AjaxTransport)},B.AjaxTransport.prototype._sendViaXHR=function(t){var i=new window.XMLHttpRequest,r=t.contentType?e.toLowerCase(t.contentType).indexOf("json")>-1?"application/json; charset=utf-8":t.contentType:"application/x-www-form-urlencoded",n=e.toLowerCase(r).indexOf("json")>-1,s=n?"GET"==t.method?e.enc(JSON.stringify(t.data)):JSON.stringify(t.data):e.isDefined(t.skipEncode)&&!0===t.skipEncode?t.data:e.toQueryString(t.data),a=t.url;t.failure=t.failure||function(){},"GET"==t.method&&s&&s.length>0&&(a.indexOf("?")>-1?a+="&":a+="?",a+=s);try{i.open(t.method,a,!0)}catch(t){return}if(i.setRequestHeader("Accept","*/*"),i.setRequestHeader("Content-Type",r),e.isObject(t.headers))for(var o in t.headers)e.isDefined(o)&&e.isDefined(t.headers[o])&&i.setRequestHeader(o,t.headers[o]);i.timeout=t.timeout||0,i.onreadystatechange=function(t,e){return function(){4==e.readyState&&200==e.status?t.success&&t.success.apply(t,[e.responseText]):4==e.readyState&&200!=e.status&&t.failure&&t.failure.apply(t,[e.responseText])}}(t,i),i.send(s)},B.AjaxTransport.prototype._sendViaXDR=function(t){var i=e.isDefined(t.skipEncode)&&!0===t.skipEncode&&"GET"!==t.method.toUpperCase()?t.data:e.toQueryString(t.data,null,!1),r=t.url;t.failure=t.failure||function(){},"GET"==t.method&&i&&i.length>0&&(i=i.replace("?",""),r.indexOf("?")>-1?r+="&":r+="?",r+=i);var n=new window.XDomainRequest;n.onerror=t.failure||function(){},n.ontimeout=t.failure||function(){},n.onprogress=function(){},n.onload=function(t,e){return function(){e.success(t.responseText),t=null,e=null}}(n,t),n.timeout=6e4;try{n.open(t.method,r)}catch(e){return void(t.failure&&t.failure(e))}e.nextTick(function(){i?(e.isString(i)||(i=JSON.stringify(i)),n.send(i)):n.send()})};var M=function(t,i){U.call(this,t,i),e.ext(this,{cThreshold:2e3});var r=B.storageTypes;t.ready.subscribe(function(){this.pers==r.CK?this.ckie=new B.Cookie(N(this)):this.pers==r.DS?this.cThreshold=500:i&&(this.uid=i),this._sync(function(){this.get("rid")?this.uid=this.get("rid"):(this.uid=this.uid||B.generateGUID(),this.set("rid",this.uid)),this.setUpdateInterval(this._updateTimeout),this._maint(!0),this._readyState.fire(this),this.ready.fire(this)}.bind(this))}.bind(this),!0,!0)};if(M.prototype=Object.create(U.prototype),M.prototype.constructor=U,M.prototype._sync=function(t){if(!this.isSyncing){this.isSyncing=!0,t=t||function(){};var i,r;if(this.pers==B.storageTypes.CK){if(i=this.ckie.get(this._storageKey))return i=q.decompress(i),this._lastSync=B.now(),r=JSON.parse(i),this._fireChangeEvents(r.keys),r.keys=r.keys||{},this._data=r,this.onSync.fire(this),this.isSyncing=!1,void e.nextTick(t);this.isSyncing=!1,e.nextTick(t)}else if(this.pers==B.storageTypes.DS){if(i=localStorage.getItem(this._storageKey)){if(i=q.decompress(i),this.lastSync=B.now(),r=JSON.parse(i),r.keys=r.keys||{},this._fireChangeEvents(r.keys),this._data=r,e.nextTick(function(){this.onSync.fire(this)}.bind(this)),B.now()-this._data.when<3e5)return this.isSyncing=!1,void e.nextTick(t);this.lastSync=B.now(),this._data={when:B.now(),keys:{}}}this.isSyncing=!1,e.nextTick(t)}}},M.prototype._commit=function(){clearTimeout(this.lock),this.lock=null,this.lastSave=B.now(),this._data.when=this.lastSave;var t="";try{t=JSON.stringify(this._data)}catch(t){return}if(this.pers==B.storageTypes.CK){var i=e.ext({},this._data);for(var r in i.keys)delete i.keys[r].t;t=JSON.stringify(i),this.ckie.set(this._storageKey,q.compress(t)),this.onCommit.fire(this._data)}else this.pers==B.storageTypes.DS&&(localStorage.setItem(this._storageKey,q.compress(t)),this.onCommit.fire(this._data));this.isStale=!1},B.testSameDomain=function(t,i){var r=document.createElement("a");r.href=location.href;var n=r.hostname,s=r.protocol;r.href=t;var a=r.hostname||n,o=0===r.protocol.indexOf("http")?r.protocol:s;r.href=i;var f=r.hostname||n,h=0===r.protocol.indexOf("http")?r.protocol:s;return e.toLowerCase(a)==e.toLowerCase(f)&&e.toLowerCase(o)==e.toLowerCase(h)},B.addParameterToURL=function(t,e){return t+=(t.split("?")[1]?"&":"?")+e},B.hash=function(t){var e=t.split("_");return 3*e[0]+1357+""+(9*e[1]+58)},B.hashCode=function(t){var e,i=0,r="";if(0===t.length)return i;for(e=0;e<t.length;e++)r=t.charCodeAt(e),i=(i<<5)-i+r,i&=i;return i},B.testAgainstSearch=function(t,i){if(null===t||"boolean"==typeof t||"boolean"==typeof i)return!1;if("."===t)return!0;if(t instanceof RegExp)return t.test(i);if(-1==t.indexOf("*")&&-1==t.indexOf("//")&&""!==t.trim())return i.indexOf(t)>-1;var r,n,s;if(t=e.toLowerCase(t.replace(/^\s+|\s+$/g,"").replace(/[\*]{2,}/g,"*")),i=e.toLowerCase(i),"*"==t)return!0;for(n=[];t.indexOf("*")>-1;)t.indexOf("*")>0&&n.push(t.substr(0,t.indexOf("*"))),n.push("*"),t=t.substr(t.indexOf("*")+1);for(t.length>0&&n.push(t),r=0!==n.length,s=0;s<n.length;s++)if("*"==(t=n[s])){if(n.length>s+1){if(s++,-1==i.indexOf(n[s])){r=!1;break}i=i.substr(i.indexOf(n[s])+n[s].length)}if(s==n.length-1&&"*"!==n[s]&&i!=n[s]&&i!=n[s]+"/"&&n[s]!=i+"/"&&i.length>0&&"/"!=i){r=!1;break}}else{if(i.substr(0,t.length)!=t&&i!=t+"/"&&t!=i+"/"){r=!1;break}if(i=i.substr(t.length),s==n.length-1&&i.length>0&&"/"!=i){r=!1;break}}return!!r},B.getRootDomain=function(t){t=e.toLowerCase(t||document.domain).replace("https://","").replace("http://","");for(var i,r=["/","?",":"],n=r.length,s=0;s<n;s++)(i=t.indexOf(r[s]))>-1&&(t=t.substr(0,i));if(t.indexOf("localhost")>-1||0===t.replace(/[0-9\.]/g,"").length)return t;var a=t.split("."),o=a.length;return o>2&&(function(t){return["com","co","org","gov","edu","net"].indexOf(t)>-1}(a[o-2])||function(t){return t.indexOf("qc.ca")>-1}(t))?a[o-3]+"."+a[o-2]+"."+a[o-1]:o>1?a[o-2]+"."+a[o-1]:t},B.FULL_DAY=864e5,B.now=function(){return+new Date},B.startTime=B.now(),!window.btoa){var P="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",j=new Array(-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,62,-1,-1,-1,63,52,53,54,55,56,57,58,59,60,61,-1,-1,-1,-1,-1,-1,-1,0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,-1,-1,-1,-1,-1,-1,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,-1,-1,-1,-1,-1);window.btoa=function(t){var e,i,r,n,s,a;for(r=t.length,i=0,e="";r>i;){if(n=255&t.charCodeAt(i++),i==r){e+=P.charAt(n>>2),e+=P.charAt((3&n)<<4),e+="==";break}if(s=t.charCodeAt(i++),i==r){e+=P.charAt(n>>2),e+=P.charAt((3&n)<<4|(240&s)>>4),e+=P.charAt((15&s)<<2),e+="=";break}a=t.charCodeAt(i++),e+=P.charAt(n>>2),e+=P.charAt((3&n)<<4|(240&s)>>4),e+=P.charAt((15&s)<<2|(192&a)>>6),e+=P.charAt(63&a)}return e},window.atob=function(t){var e,i,r,n,s,a,o;for(a=t.length,s=0,o="";a>s;){do{e=j[255&t.charCodeAt(s++)]}while(a>s&&-1==e);if(-1==e)break;do{i=j[255&t.charCodeAt(s++)]}while(a>s&&-1==i);if(-1==i)break;o+=String.fromCharCode(e<<2|(48&i)>>4);do{if(61==(r=255&t.charCodeAt(s++)))return o;r=j[r]}while(a>s&&-1==r);if(-1==r)break;o+=String.fromCharCode((15&i)<<4|(60&r)>>2);do{if(61==(n=255&t.charCodeAt(s++)))return o;n=j[n]}while(a>s&&-1==n);if(-1==n)break;o+=String.fromCharCode((3&r)<<6|n)}return o}}B.b64EncodeUnicode=function(t){return btoa(e.enc(t).replace(/%([0-9A-F]{2})/g,function(t,e){return String.fromCharCode("0x"+e)}))},B.b64DecodeUnicode=function(t){return decodeURIComponent(Array.prototype.map.call(atob(t).split(""),function(t){return"%"+("00"+t.charCodeAt(0).toString(16)).slice(-2)}).join(""))};var z={Util:{stringToByteArray:function(t){var e,i,r=t.split("");for(e=0,i=r.length;e<i;e++)r[e]=(255&r[e].charCodeAt(0))>>>0;return r}}};z.CompressionMethod={DEFLATE:8,RESERVED:15},z.BitStream=function(t,e){if(this.index="number"==typeof e?e:0,this.bitindex=0,this.buffer=t instanceof(_?Uint8Array:Array)?t:new(_?Uint8Array:Array)(z.BitStream.DefaultBlockSize),2*this.buffer.length<=this.index)throw new Error("invalid index");this.buffer.length<=this.index&&this.expandBuffer()},z.BitStream.DefaultBlockSize=32768,z.BitStream.prototype.expandBuffer=function(){var t,e=this.buffer,i=e.length,r=new(_?Uint8Array:Array)(i<<1);if(_)r.set(e);else for(t=0;t<i;++t)r[t]=e[t];return this.buffer=r},z.BitStream.prototype.writeBits=function(t,e,i){var r,n=this.buffer,s=this.index,a=this.bitindex,o=n[s];if(i&&e>1&&(t=e>8?function(t){return z.BitStream.ReverseTable[255&t]<<24|z.BitStream.ReverseTable[t>>>8&255]<<16|z.BitStream.ReverseTable[t>>>16&255]<<8|z.BitStream.ReverseTable[t>>>24&255]}(t)>>32-e:z.BitStream.ReverseTable[t]>>8-e),e+a<8)o=o<<e|t,a+=e;else for(r=0;r<e;++r)o=o<<1|t>>e-r-1&1,8==++a&&(a=0,n[s++]=z.BitStream.ReverseTable[o],o=0,s===n.length&&(n=this.expandBuffer()));n[s]=o,this.buffer=n,this.bitindex=a,this.index=s},z.BitStream.prototype.finish=function(){var t,e=this.buffer,i=this.index;return this.bitindex>0&&(e[i]<<=8-this.bitindex,e[i]=z.BitStream.ReverseTable[e[i]],i++),_?t=e.subarray(0,i):(e.length=i,t=e),t},z.BitStream.ReverseTable=function(t){return t}(function(){var t,e=new(_?Uint8Array:Array)(256);for(t=0;t<256;++t)e[t]=function(t){var e=t,i=7;for(t>>>=1;t;t>>>=1)e<<=1,e|=1&t,--i;return(e<<i&255)>>>0}(t);return e}()),z.Huffman={},z.Huffman.buildHuffmanTable=function(t){var e,i,r,n,s,a,o,f,h,u,l,c=t.length,d=0,p=Number.POSITIVE_INFINITY;for(f=0,h=c;f<h;++f)t[f]>d&&(d=t[f]),t[f]<p&&(p=t[f]);for(e=1<<d,i=new(_?Uint32Array:Array)(e),r=1,n=0,s=2;r<=d;){for(f=0;f<c;++f)if(t[f]===r){for(a=0,o=n,u=0;u<r;++u)a=a<<1|1&o,o>>=1;for(l=r<<16|f,u=a;u<e;u+=s)i[u]=l;++n}++r,n<<=1,s<<=1}return[i,d,p]},z.Heap=function(t){this.buffer=new(_?Uint16Array:Array)(2*t),this.length=0},z.Heap.prototype.getParent=function(t){return 2*((t-2)/4|0)},z.Heap.prototype.getChild=function(t){return 2*t+2},z.Heap.prototype.push=function(t,e){var i,r,n,s=this.buffer;for(i=this.length,s[this.length++]=e,s[this.length++]=t;i>0&&(r=this.getParent(i),s[i]>s[r]);)n=s[i],s[i]=s[r],s[r]=n,n=s[i+1],s[i+1]=s[r+1],s[r+1]=n,i=r;return this.length},z.Heap.prototype.pop=function(){var t,e,i,r,n,s=this.buffer;for(e=s[0],t=s[1],this.length-=2,s[0]=s[this.length],s[1]=s[this.length+1],n=0;;){if((r=this.getChild(n))>=this.length)break;if(r+2<this.length&&s[r+2]>s[r]&&(r+=2),!(s[r]>s[n]))break;i=s[n],s[n]=s[r],s[r]=i,i=s[n+1],s[n+1]=s[r+1],s[r+1]=i,n=r}return{index:t,value:e,length:this.length}},z.RawDeflate=function(t,e){this.compressionType=z.RawDeflate.CompressionType.DYNAMIC,this.lazy=0,this.freqsLitLen,this.freqsDist,this.input=_&&t instanceof Array?new Uint8Array(t):t,this.output,this.op=0,e&&(e.lazy&&(this.lazy=e.lazy),"number"==typeof e.compressionType&&(this.compressionType=e.compressionType),e.outputBuffer&&(this.output=_&&e.outputBuffer instanceof Array?new Uint8Array(e.outputBuffer):e.outputBuffer),"number"==typeof e.outputIndex&&(this.op=e.outputIndex)),this.output||(this.output=new(_?Uint8Array:Array)(32768))},z.RawDeflate.CompressionType={NONE:0,FIXED:1,DYNAMIC:2,RESERVED:3},z.RawDeflate.Lz77MinLength=3,z.RawDeflate.Lz77MaxLength=258,z.RawDeflate.WindowSize=32768,z.RawDeflate.MaxCodeLength=16,z.RawDeflate.HUFMAX=286,z.RawDeflate.FixedHuffmanTable=function(){var t,e=[];for(t=0;t<288;t++)switch(!0){case t<=143:e.push([t+48,8]);break;case t<=255:e.push([t-144+400,9]);break;case t<=279:e.push([t-256+0,7]);break;case t<=287:e.push([t-280+192,8]);break;default:throw"invalid literal: "+t}return e}(),z.RawDeflate.prototype.compress=function(){var t,e,i,r=this.input;switch(this.compressionType){case z.RawDeflate.CompressionType.NONE:for(e=0,i=r.length;e<i;)t=_?r.subarray(e,e+65535):r.slice(e,e+65535),e+=t.length,this.makeNocompressBlock(t,e===i);break;case z.RawDeflate.CompressionType.FIXED:this.output=this.makeFixedHuffmanBlock(r,!0),this.op=this.output.length;break;case z.RawDeflate.CompressionType.DYNAMIC:this.output=this.makeDynamicHuffmanBlock(r,!0),this.op=this.output.length;break;default:throw"invalid compression type"}
return this.output},z.RawDeflate.prototype.makeNocompressBlock=function(t,e){var i,r,n,s,a,o,f=this.output,h=this.op;if(_){for(f=new Uint8Array(this.output.buffer);f.length<=h+t.length+5;)f=new Uint8Array(f.length<<1);f.set(this.output)}if(i=e?1:0,r=z.RawDeflate.CompressionType.NONE,f[h++]=i|r<<1,n=t.length,s=65536+~n&65535,f[h++]=255&n,f[h++]=n>>>8&255,f[h++]=255&s,f[h++]=s>>>8&255,_)f.set(t,h),h+=t.length,f=f.subarray(0,h);else{for(a=0,o=t.length;a<o;++a)f[h++]=t[a];f.length=h}return this.op=h,this.output=f,f},z.RawDeflate.prototype.makeFixedHuffmanBlock=function(t,e){var i,r,n,s=new z.BitStream(_?new Uint8Array(this.output.buffer):this.output,this.op);return i=e?1:0,r=z.RawDeflate.CompressionType.FIXED,s.writeBits(i,1,!0),s.writeBits(r,2,!0),n=this.lz77(t),this.fixedHuffman(n,s),s.finish()},z.RawDeflate.prototype.makeDynamicHuffmanBlock=function(t,e){var i,r,n,s,a,o,f,h,u,l,c,d,p,g,y,b,w,v=new z.BitStream(_?new Uint8Array(this.output.buffer):this.output,this.op),m=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],S=new Array(19);for(i=e?1:0,r=z.RawDeflate.CompressionType.DYNAMIC,v.writeBits(i,1,!0),v.writeBits(r,2,!0),n=this.lz77(t),f=this.getLengths_(this.freqsLitLen,15),h=this.getCodesFromLengths_(f),u=this.getLengths_(this.freqsDist,7),l=this.getCodesFromLengths_(u),s=286;s>257&&0===f[s-1];s--);for(a=30;a>1&&0===u[a-1];a--);for(c=this.getTreeSymbols_(s,f,a,u),d=this.getLengths_(c.freqs,7),b=0;b<19;b++)S[b]=d[m[b]];for(o=19;o>4&&0===S[o-1];o--);for(p=this.getCodesFromLengths_(d),v.writeBits(s-257,5,!0),v.writeBits(a-1,5,!0),v.writeBits(o-4,4,!0),b=0;b<o;b++)v.writeBits(S[b],3,!0);for(b=0,w=c.codes.length;b<w;b++)if(g=c.codes[b],v.writeBits(p[g],d[g],!0),g>=16){switch(b++,g){case 16:y=2;break;case 17:y=3;break;case 18:y=7;break;default:throw"invalid code: "+g}v.writeBits(c.codes[b],y,!0)}return this.dynamicHuffman(n,[h,f],[l,u],v),v.finish()},z.RawDeflate.prototype.dynamicHuffman=function(t,e,i,r){var n,s,a,o,f,h,u,l;for(f=e[0],h=e[1],u=i[0],l=i[1],n=0,s=t.length;n<s;++n)if(a=t[n],r.writeBits(f[a],h[a],!0),a>256)r.writeBits(t[++n],t[++n],!0),o=t[++n],r.writeBits(u[o],l[o],!0),r.writeBits(t[++n],t[++n],!0);else if(256===a)break;return r},z.RawDeflate.prototype.fixedHuffman=function(t,e){var i,r,n;for(i=0,r=t.length;i<r;i++)if(n=t[i],z.BitStream.prototype.writeBits.apply(e,z.RawDeflate.FixedHuffmanTable[n]),n>256)e.writeBits(t[++i],t[++i],!0),e.writeBits(t[++i],5),e.writeBits(t[++i],t[++i],!0);else if(256===n)break;return e},z.RawDeflate.Lz77Match=function(t,e){this.length=t,this.backwardDistance=e},z.RawDeflate.Lz77Match.LengthCodeTable=function(t){return _?new Uint32Array(t):t}(function(){var t,e,i=[];for(t=3;t<=258;t++)e=function(t){switch(!0){case 3===t:return[257,t-3,0];case 4===t:return[258,t-4,0];case 5===t:return[259,t-5,0];case 6===t:return[260,t-6,0];case 7===t:return[261,t-7,0];case 8===t:return[262,t-8,0];case 9===t:return[263,t-9,0];case 10===t:return[264,t-10,0];case t<=12:return[265,t-11,1];case t<=14:return[266,t-13,1];case t<=16:return[267,t-15,1];case t<=18:return[268,t-17,1];case t<=22:return[269,t-19,2];case t<=26:return[270,t-23,2];case t<=30:return[271,t-27,2];case t<=34:return[272,t-31,2];case t<=42:return[273,t-35,3];case t<=50:return[274,t-43,3];case t<=58:return[275,t-51,3];case t<=66:return[276,t-59,3];case t<=82:return[277,t-67,4];case t<=98:return[278,t-83,4];case t<=114:return[279,t-99,4];case t<=130:return[280,t-115,4];case t<=162:return[281,t-131,5];case t<=194:return[282,t-163,5];case t<=226:return[283,t-195,5];case t<=257:return[284,t-227,5];case 258===t:return[285,t-258,0];default:throw"invalid length: "+t}}(t),i[t]=e[2]<<24|e[1]<<16|e[0];return i}()),z.RawDeflate.Lz77Match.prototype.getDistanceCode_=function(t){var e;switch(!0){case 1===t:e=[0,t-1,0];break;case 2===t:e=[1,t-2,0];break;case 3===t:e=[2,t-3,0];break;case 4===t:e=[3,t-4,0];break;case t<=6:e=[4,t-5,1];break;case t<=8:e=[5,t-7,1];break;case t<=12:e=[6,t-9,2];break;case t<=16:e=[7,t-13,2];break;case t<=24:e=[8,t-17,3];break;case t<=32:e=[9,t-25,3];break;case t<=48:e=[10,t-33,4];break;case t<=64:e=[11,t-49,4];break;case t<=96:e=[12,t-65,5];break;case t<=128:e=[13,t-97,5];break;case t<=192:e=[14,t-129,6];break;case t<=256:e=[15,t-193,6];break;case t<=384:e=[16,t-257,7];break;case t<=512:e=[17,t-385,7];break;case t<=768:e=[18,t-513,8];break;case t<=1024:e=[19,t-769,8];break;case t<=1536:e=[20,t-1025,9];break;case t<=2048:e=[21,t-1537,9];break;case t<=3072:e=[22,t-2049,10];break;case t<=4096:e=[23,t-3073,10];break;case t<=6144:e=[24,t-4097,11];break;case t<=8192:e=[25,t-6145,11];break;case t<=12288:e=[26,t-8193,12];break;case t<=16384:e=[27,t-12289,12];break;case t<=24576:e=[28,t-16385,13];break;case t<=32768:e=[29,t-24577,13];break;default:throw"invalid distance"}return e},z.RawDeflate.Lz77Match.prototype.toLz77Array=function(){var t,e=this.length,i=this.backwardDistance,r=[],n=0;return t=z.RawDeflate.Lz77Match.LengthCodeTable[e],r[n++]=65535&t,r[n++]=t>>16&255,r[n++]=t>>24,t=this.getDistanceCode_(i),r[n++]=t[0],r[n++]=t[1],r[n++]=t[2],r},z.RawDeflate.prototype.lz77=function(t){function e(t,e){var i,r,n=t.toLz77Array();for(i=0,r=n.length;i<r;++i)d[p++]=n[i];y[n[0]]++,b[n[3]]++,g=t.length+e-1,h=null}var i,r,n,s,a,o,f,h,u,l={},c=z.RawDeflate.WindowSize,d=_?new Uint16Array(2*t.length):[],p=0,g=0,y=new(_?Uint32Array:Array)(286),b=new(_?Uint32Array:Array)(30),w=this.lazy;if(!_){for(n=0;n<=285;)y[n++]=0;for(n=0;n<=29;)b[n++]=0}for(y[256]=1,i=0,r=t.length;i<r;++i){for(a=0,n=0,s=z.RawDeflate.Lz77MinLength;n<s&&i+n!==r;++n)a=a<<8|t[i+n];if(void 0===l[a]&&(l[a]=[]),o=l[a],g-- >0)o.push(i);else{for(;o.length>0&&i-o[0]>c;)o.shift();if(i+z.RawDeflate.Lz77MinLength>=r){for(h&&e(h,-1),n=0,s=r-i;n<s;++n)u=t[i+n],d[p++]=u,++y[u];break}o.length>0?(f=this.searchLongestMatch_(t,i,o),h?h.length<f.length?(u=t[i-1],d[p++]=u,++y[u],e(f,0)):e(h,-1):f.length<w?h=f:e(f,0)):h?e(h,-1):(u=t[i],d[p++]=u,++y[u]),o.push(i)}}return d[p++]=256,y[256]++,this.freqsLitLen=y,this.freqsDist=b,_?d.subarray(0,p):d},z.RawDeflate.prototype.searchLongestMatch_=function(t,e,i){var r,n,s,a,o,f,h=0,u=t.length;t:for(a=0,f=i.length;a<f;a++){if(r=i[f-a-1],s=z.RawDeflate.Lz77MinLength,h>z.RawDeflate.Lz77MinLength){for(o=h;o>z.RawDeflate.Lz77MinLength;o--)if(t[r+o-1]!==t[e+o-1])continue t;s=h}for(;s<z.RawDeflate.Lz77MaxLength&&e+s<u&&t[r+s]===t[e+s];)++s;if(s>h&&(n=r,h=s),s===z.RawDeflate.Lz77MaxLength)break}return new z.RawDeflate.Lz77Match(h,e-n)},z.RawDeflate.prototype.getTreeSymbols_=function(t,e,i,r){var n,s,a,o,f,h,u=new(_?Uint32Array:Array)(t+i),l=new(_?Uint32Array:Array)(316),c=new(_?Uint8Array:Array)(19);for(s=0,n=0;n<t;n++)u[s++]=e[n];for(n=0;n<i;n++)u[s++]=r[n];if(!_)for(n=0,o=c.length;n<o;++n)c[n]=0;for(f=0,n=0,o=u.length;n<o;n+=s){for(s=1;n+s<o&&u[n+s]===u[n];++s);if(a=s,0===u[n])if(a<3)for(;a-- >0;)l[f++]=0,c[0]++;else for(;a>0;)h=a<138?a:138,h>a-3&&h<a&&(h=a-3),h<=10?(l[f++]=17,l[f++]=h-3,c[17]++):(l[f++]=18,l[f++]=h-11,c[18]++),a-=h;else if(l[f++]=u[n],c[u[n]]++,--a<3)for(;a-- >0;)l[f++]=u[n],c[u[n]]++;else for(;a>0;)h=a<6?a:6,h>a-3&&h<a&&(h=a-3),l[f++]=16,l[f++]=h-3,c[16]++,a-=h}return{codes:_?l.subarray(0,f):l.slice(0,f),freqs:c}},z.RawDeflate.prototype.getLengths_=function(t,e){var i,r,n,s,a,o=t.length,f=new z.Heap(2*z.RawDeflate.HUFMAX),h=new(_?Uint8Array:Array)(o);if(!_)for(s=0;s<o;s++)h[s]=0;for(s=0;s<o;++s)t[s]>0&&f.push(s,t[s]);if(i=new Array(f.length/2),r=new(_?Uint32Array:Array)(f.length/2),1===i.length)return h[f.pop().index]=1,h;for(s=0,a=f.length/2;s<a;++s)i[s]=f.pop(),r[s]=i[s].value;for(n=this.reversePackageMerge_(r,r.length,e),s=0,a=i.length;s<a;++s)h[i[s].index]=n[s];return h},z.RawDeflate.prototype.reversePackageMerge_=function(t,e,i){function r(t){var i=d[t][p[t]];i===e?(r(t+1),r(t+1)):--l[i],++p[t]}var n,s,a,o,f,h=new(_?Uint16Array:Array)(i),u=new(_?Uint8Array:Array)(i),l=new(_?Uint8Array:Array)(e),c=new Array(i),d=new Array(i),p=new Array(i),g=(1<<i)-e,y=1<<i-1;for(h[i-1]=e,s=0;s<i;++s)g<y?u[s]=0:(u[s]=1,g-=y),g<<=1,h[i-2-s]=(h[i-1-s]/2|0)+e;for(h[0]=u[0],c[0]=new Array(h[0]),d[0]=new Array(h[0]),s=1;s<i;++s)h[s]>2*h[s-1]+u[s]&&(h[s]=2*h[s-1]+u[s]),c[s]=new Array(h[s]),d[s]=new Array(h[s]);for(n=0;n<e;++n)l[n]=i;for(a=0;a<h[i-1];++a)c[i-1][a]=t[a],d[i-1][a]=a;for(n=0;n<i;++n)p[n]=0;for(1===u[i-1]&&(--l[0],++p[i-1]),s=i-2;s>=0;--s){for(n=0,o=0,f=p[s+1],a=0;a<h[s];a++)o=c[s+1][f]+c[s+1][f+1],o>t[n]?(c[s][a]=o,d[s][a]=e,f+=2):(c[s][a]=t[n],d[s][a]=n,++n);p[s]=0,1===u[s]&&r(s)}return l},z.RawDeflate.prototype.getCodesFromLengths_=function(t){var e,i,r,n,s=new(_?Uint16Array:Array)(t.length),a=[],o=[],f=0;for(e=0,i=t.length;e<i;e++)a[t[e]]=1+(0|a[t[e]]);for(e=1,i=z.RawDeflate.MaxCodeLength;e<=i;e++)o[e]=f,f+=0|a[e],f<<=1;for(e=0,i=t.length;e<i;e++)for(f=o[t[e]],o[t[e]]+=1,s[e]=0,r=0,n=t[e];r<n;r++)s[e]=s[e]<<1|1&f,f>>>=1;return s};var H=z.Huffman.buildHuffmanTable;z.RawInflate=function(t,e){switch(this.buffer,this.blocks=[],this.bufferSize=ZLIB_RAW_INFLATE_BUFFER_SIZE,this.totalpos=0,this.ip=0,this.bitsbuf=0,this.bitsbuflen=0,this.input=_?new Uint8Array(t):t,this.output,this.op,this.bfinal=!1,this.bufferType=z.RawInflate.BufferType.ADAPTIVE,this.resize=!1,this.prev,!e&&(e={})||(e.index&&(this.ip=e.index),e.bufferSize&&(this.bufferSize=e.bufferSize),e.bufferType&&(this.bufferType=e.bufferType),e.resize&&(this.resize=e.resize)),this.bufferType){case z.RawInflate.BufferType.BLOCK:this.op=z.RawInflate.MaxBackwardLength,this.output=new(_?Uint8Array:Array)(z.RawInflate.MaxBackwardLength+this.bufferSize+z.RawInflate.MaxCopyLength);break;case z.RawInflate.BufferType.ADAPTIVE:this.op=0,this.output=new(_?Uint8Array:Array)(this.bufferSize),this.expandBuffer=this.expandBufferAdaptive,this.concatBuffer=this.concatBufferDynamic,this.decodeHuffman=this.decodeHuffmanAdaptive;break;default:throw new Error("invalid inflate mode")}},z.RawInflate.BufferType={BLOCK:0,ADAPTIVE:1},z.RawInflate.prototype.decompress=function(){for(;!this.bfinal;)this.parseBlock();return this.concatBuffer()},z.RawInflate.MaxBackwardLength=32768,z.RawInflate.MaxCopyLength=258,z.RawInflate.Order=function(t){return _?new Uint16Array(t):t}([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),z.RawInflate.LengthCodeTable=function(t){return _?new Uint16Array(t):t}([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258]),z.RawInflate.LengthExtraTable=function(t){return _?new Uint8Array(t):t}([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0]),z.RawInflate.DistCodeTable=function(t){return _?new Uint16Array(t):t}([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577]),z.RawInflate.DistExtraTable=function(t){return _?new Uint8Array(t):t}([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),z.RawInflate.FixedLiteralLengthTable=function(t){return t}(function(){var t,e,i=new(_?Uint8Array:Array)(288);for(t=0,e=i.length;t<e;++t)i[t]=t<=143?8:t<=255?9:t<=279?7:8;return H(i)}()),z.RawInflate.FixedDistanceTable=function(t){return t}(function(){var t,e,i=new(_?Uint8Array:Array)(30);for(t=0,e=i.length;t<e;++t)i[t]=5;return H(i)}()),z.RawInflate.prototype.parseBlock=function(){var t=this.readBits(3);switch(1&t&&(this.bfinal=!0),t>>>=1){case 0:this.parseUncompressedBlock();break;case 1:this.parseFixedHuffmanBlock();break;case 2:this.parseDynamicHuffmanBlock();break;default:throw new Error("unknown BTYPE: "+t)}},z.RawInflate.prototype.readBits=function(t){for(var e,i=this.bitsbuf,r=this.bitsbuflen,n=this.input,s=this.ip,a=n.length;r<t;){if(s>=a)throw new Error("input buffer is broken");i|=n[s++]<<r,r+=8}return e=i&(1<<t)-1,i>>>=t,r-=t,this.bitsbuf=i,this.bitsbuflen=r,this.ip=s,e},z.RawInflate.prototype.readCodeByTable=function(t){for(var e,i,r=this.bitsbuf,n=this.bitsbuflen,s=this.input,a=this.ip,o=s.length,f=t[0],h=t[1];n<h&&!(a>=o);)r|=s[a++]<<n,n+=8;return e=f[r&(1<<h)-1],i=e>>>16,this.bitsbuf=r>>i,this.bitsbuflen=n-i,this.ip=a,65535&e},z.RawInflate.prototype.parseUncompressedBlock=function(){var t,e,i,r=this.input,n=this.ip,s=this.output,a=this.op,o=r.length,f=s.length;if(this.bitsbuf=0,this.bitsbuflen=0,n+1>=o)throw new Error("invalid uncompressed block header: LEN");if(t=r[n++]|r[n++]<<8,n+1>=o)throw new Error("invalid uncompressed block header: NLEN");if(e=r[n++]|r[n++]<<8,t===~e)throw new Error("invalid uncompressed block header: length verify");if(n+t>r.length)throw new Error("input buffer is broken");switch(this.bufferType){case z.RawInflate.BufferType.BLOCK:for(;a+t>s.length;){if(i=f-a,t-=i,_)s.set(r.subarray(n,n+i),a),a+=i,n+=i;else for(;i--;)s[a++]=r[n++];this.op=a,s=this.expandBuffer(),a=this.op}break;case z.RawInflate.BufferType.ADAPTIVE:for(;a+t>s.length;)s=this.expandBuffer({fixRatio:2});break;default:throw new Error("invalid inflate mode")}if(_)s.set(r.subarray(n,n+t),a),a+=t,n+=t;else for(;t--;)s[a++]=r[n++];this.ip=n,this.op=a,this.output=s},z.RawInflate.prototype.parseFixedHuffmanBlock=function(){this.decodeHuffman(z.RawInflate.FixedLiteralLengthTable,z.RawInflate.FixedDistanceTable)},z.RawInflate.prototype.parseDynamicHuffmanBlock=function(){function t(t,e,i){var r,n,s,a=this.prev;for(s=0;s<t;)switch(r=this.readCodeByTable(e)){case 16:for(n=3+this.readBits(2);n--;)i[s++]=a;break;case 17:for(n=3+this.readBits(3);n--;)i[s++]=0;a=0;break;case 18:for(n=11+this.readBits(7);n--;)i[s++]=0;a=0;break;default:i[s++]=r,a=r}return this.prev=a,i}var e,i,r,n,s=this.readBits(5)+257,a=this.readBits(5)+1,o=this.readBits(4)+4,f=new(_?Uint8Array:Array)(z.RawInflate.Order.length);for(n=0;n<o;++n)f[z.RawInflate.Order[n]]=this.readBits(3);if(!_)for(n=o,o=f.length;n<o;++n)f[z.RawInflate.Order[n]]=0;e=H(f),i=new(_?Uint8Array:Array)(s),r=new(_?Uint8Array:Array)(a),this.prev=0,this.decodeHuffman(H(t.call(this,s,e,i)),H(t.call(this,a,e,r)))},z.RawInflate.prototype.decodeHuffman=function(t,e){var i=this.output,r=this.op;this.currentLitlenTable=t;for(var n,s,a,o,f=i.length-z.RawInflate.MaxCopyLength;256!==(n=this.readCodeByTable(t));)if(n<256)r>=f&&(this.op=r,i=this.expandBuffer(),r=this.op),i[r++]=n;else for(s=n-257,o=z.RawInflate.LengthCodeTable[s],z.RawInflate.LengthExtraTable[s]>0&&(o+=this.readBits(z.RawInflate.LengthExtraTable[s])),n=this.readCodeByTable(e),a=z.RawInflate.DistCodeTable[n],z.RawInflate.DistExtraTable[n]>0&&(a+=this.readBits(z.RawInflate.DistExtraTable[n])),r>=f&&(this.op=r,i=this.expandBuffer(),r=this.op);o--;)i[r]=i[r++-a];for(;this.bitsbuflen>=8;)this.bitsbuflen-=8,this.ip--;this.op=r},z.RawInflate.prototype.decodeHuffmanAdaptive=function(t,e){var i=this.output,r=this.op;this.currentLitlenTable=t;for(var n,s,a,o,f=i.length;256!==(n=this.readCodeByTable(t));)if(n<256)r>=f&&(i=this.expandBuffer(),f=i.length),i[r++]=n;else for(s=n-257,o=z.RawInflate.LengthCodeTable[s],z.RawInflate.LengthExtraTable[s]>0&&(o+=this.readBits(z.RawInflate.LengthExtraTable[s])),n=this.readCodeByTable(e),a=z.RawInflate.DistCodeTable[n],z.RawInflate.DistExtraTable[n]>0&&(a+=this.readBits(z.RawInflate.DistExtraTable[n])),r+o>f&&(i=this.expandBuffer(),f=i.length);o--;)i[r]=i[r++-a];for(;this.bitsbuflen>=8;)this.bitsbuflen-=8,this.ip--;this.op=r},z.RawInflate.prototype.expandBuffer=function(t){var e,i,r=new(_?Uint8Array:Array)(this.op-z.RawInflate.MaxBackwardLength),n=this.op-z.RawInflate.MaxBackwardLength,s=this.output;if(_)r.set(s.subarray(z.RawInflate.MaxBackwardLength,r.length));else for(e=0,i=r.length;e<i;++e)r[e]=s[e+z.RawInflate.MaxBackwardLength];if(this.blocks.push(r),this.totalpos+=r.length,_)s.set(s.subarray(n,n+z.RawInflate.MaxBackwardLength));else for(e=0;e<z.RawInflate.MaxBackwardLength;++e)s[e]=s[n+e];return this.op=z.RawInflate.MaxBackwardLength,s},z.RawInflate.prototype.expandBufferAdaptive=function(t){var e,i,r,n,s=this.input.length/this.ip+1|0,a=this.input,o=this.output;return t&&("number"==typeof t.fixRatio&&(s=t.fixRatio),"number"==typeof t.addRatio&&(s+=t.addRatio)),s<2?(i=(a.length-this.ip)/this.currentLitlenTable[2],n=i/2*258|0,r=n<o.length?o.length+n:o.length<<1):r=o.length*s,_?(e=new Uint8Array(r),e.set(o)):e=o,this.output=e,this.output},z.RawInflate.prototype.concatBuffer=function(){var t,e,i,r,n,s=0,a=this.totalpos+(this.op-z.RawInflate.MaxBackwardLength),o=this.output,f=this.blocks,h=new(_?Uint8Array:Array)(a);if(0===f.length)return _?this.output.subarray(z.RawInflate.MaxBackwardLength,this.op):this.output.slice(z.RawInflate.MaxBackwardLength,this.op);for(e=0,i=f.length;e<i;++e)for(t=f[e],r=0,n=t.length;r<n;++r)h[s++]=t[r];for(e=z.RawInflate.MaxBackwardLength,i=this.op;e<i;++e)h[s++]=o[e];return this.blocks=[],this.buffer=h,this.buffer},z.RawInflate.prototype.concatBufferDynamic=function(){var t,e=this.op;return _?this.resize?(t=new Uint8Array(e),t.set(this.output.subarray(0,e))):t=this.output.subarray(0,e):(this.output.length>e&&(this.output.length=e),t=this.output),this.buffer=t,this.buffer},z.RawInflate=function(t,e){switch(this.buffer,this.blocks=[],this.bufferSize=32768,this.totalpos=0,this.ip=0,this.bitsbuf=0,this.bitsbuflen=0,this.input=_?new Uint8Array(t):t,this.output,this.op,this.bfinal=!1,this.bufferType=z.RawInflate.BufferType.ADAPTIVE,this.resize=!1,this.prev,!e&&(e={})||(e.index&&(this.ip=e.index),e.bufferSize&&(this.bufferSize=e.bufferSize),e.bufferType&&(this.bufferType=e.bufferType),e.resize&&(this.resize=e.resize)),this.bufferType){case z.RawInflate.BufferType.BLOCK:this.op=z.RawInflate.MaxBackwardLength,this.output=new(_?Uint8Array:Array)(z.RawInflate.MaxBackwardLength+this.bufferSize+z.RawInflate.MaxCopyLength);break;case z.RawInflate.BufferType.ADAPTIVE:this.op=0,this.output=new(_?Uint8Array:Array)(this.bufferSize),this.expandBuffer=this.expandBufferAdaptive,this.concatBuffer=this.concatBufferDynamic,this.decodeHuffman=this.decodeHuffmanAdaptive}},z.RawInflate.BufferType={BLOCK:0,ADAPTIVE:1},z.RawInflate.prototype.decompress=function(){for(;!this.bfinal;)this.parseBlock();return this.concatBuffer()},z.RawInflate.MaxBackwardLength=32768,z.RawInflate.MaxCopyLength=258,z.RawInflate.Order=function(t){return _?new Uint16Array(t):t}([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),z.RawInflate.LengthCodeTable=function(t){return _?new Uint16Array(t):t}([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,258,258]),z.RawInflate.LengthExtraTable=function(t){return _?new Uint8Array(t):t}([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0]),z.RawInflate.DistCodeTable=function(t){return _?new Uint16Array(t):t}([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577]),z.RawInflate.DistExtraTable=function(t){return _?new Uint8Array(t):t}([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),z.RawInflate.FixedLiteralLengthTable=function(t){return t}(function(){var t,e,i=new(_?Uint8Array:Array)(288);for(t=0,e=i.length;t<e;++t)i[t]=t<=143?8:t<=255?9:t<=279?7:8;return H(i)}()),z.RawInflate.FixedDistanceTable=function(t){return t}(function(){var t,e,i=new(_?Uint8Array:Array)(30);for(t=0,e=i.length;t<e;++t)i[t]=5;return H(i)}()),z.RawInflate.prototype.parseBlock=function(){var t=this.readBits(3);switch(1&t&&(this.bfinal=!0),t>>>=1){case 0:this.parseUncompressedBlock();break;case 1:this.parseFixedHuffmanBlock();break;case 2:this.parseDynamicHuffmanBlock();break;default:throw new Error("unknown BTYPE: "+t)}},z.RawInflate.prototype.readBits=function(t){for(var e,i=this.bitsbuf,r=this.bitsbuflen,n=this.input,s=this.ip,a=n.length;r<t;){if(s>=a)throw new Error("input buffer is broken");i|=n[s++]<<r,r+=8}return e=i&(1<<t)-1,i>>>=t,r-=t,this.bitsbuf=i,this.bitsbuflen=r,this.ip=s,e},z.RawInflate.prototype.readCodeByTable=function(t){for(var e,i,r=this.bitsbuf,n=this.bitsbuflen,s=this.input,a=this.ip,o=s.length,f=t[0],h=t[1];n<h&&!(a>=o);)r|=s[a++]<<n,n+=8;return e=f[r&(1<<h)-1],i=e>>>16,this.bitsbuf=r>>i,this.bitsbuflen=n-i,this.ip=a,65535&e},z.RawInflate.prototype.parseUncompressedBlock=function(){var t,e,i,r=this.input,n=this.ip,s=this.output,a=this.op,o=r.length,f=s.length;if(this.bitsbuf=0,this.bitsbuflen=0,n+1>=o)throw new Error("invalid uncompressed block header: LEN");if(t=r[n++]|r[n++]<<8,n+1>=o)throw new Error("invalid uncompressed block header: NLEN");if(e=r[n++]|r[n++]<<8,t===~e)throw new Error("invalid uncompressed block header: length verify");if(n+t>r.length)throw new Error("input buffer is broken");switch(this.bufferType){case z.RawInflate.BufferType.BLOCK:for(;a+t>s.length;){if(i=f-a,t-=i,_)s.set(r.subarray(n,n+i),a),a+=i,n+=i;else for(;i--;)s[a++]=r[n++];this.op=a,s=this.expandBuffer(),a=this.op}break;case z.RawInflate.BufferType.ADAPTIVE:for(;a+t>s.length;)s=this.expandBuffer({fixRatio:2});break;default:throw new Error("invalid inflate mode")}if(_)s.set(r.subarray(n,n+t),a),a+=t,n+=t;else for(;t--;)s[a++]=r[n++];this.ip=n,this.op=a,this.output=s},z.RawInflate.prototype.parseFixedHuffmanBlock=function(){this.decodeHuffman(z.RawInflate.FixedLiteralLengthTable,z.RawInflate.FixedDistanceTable)},z.RawInflate.prototype.parseDynamicHuffmanBlock=function(){function t(t,e,i){var r,n,s,a=this.prev;for(s=0;s<t;)switch(r=this.readCodeByTable(e)){case 16:for(n=3+this.readBits(2);n--;)i[s++]=a;break;case 17:for(n=3+this.readBits(3);n--;)i[s++]=0;a=0;break;case 18:for(n=11+this.readBits(7);n--;)i[s++]=0;a=0;break;default:i[s++]=r,a=r}return this.prev=a,i}var e,i,r,n,s=this.readBits(5)+257,a=this.readBits(5)+1,o=this.readBits(4)+4,f=new(_?Uint8Array:Array)(z.RawInflate.Order.length);for(n=0;n<o;++n)f[z.RawInflate.Order[n]]=this.readBits(3);if(!_)for(n=o,o=f.length;n<o;++n)f[z.RawInflate.Order[n]]=0;e=H(f),i=new(_?Uint8Array:Array)(s),r=new(_?Uint8Array:Array)(a),this.prev=0,this.decodeHuffman(H(t.call(this,s,e,i)),H(t.call(this,a,e,r)))},z.RawInflate.prototype.decodeHuffman=function(t,e){var i=this.output,r=this.op;this.currentLitlenTable=t;for(var n,s,a,o,f=i.length-z.RawInflate.MaxCopyLength;256!==(n=this.readCodeByTable(t));)if(n<256)r>=f&&(this.op=r,i=this.expandBuffer(),r=this.op),i[r++]=n;else for(s=n-257,o=z.RawInflate.LengthCodeTable[s],z.RawInflate.LengthExtraTable[s]>0&&(o+=this.readBits(z.RawInflate.LengthExtraTable[s])),n=this.readCodeByTable(e),a=z.RawInflate.DistCodeTable[n],z.RawInflate.DistExtraTable[n]>0&&(a+=this.readBits(z.RawInflate.DistExtraTable[n])),r>=f&&(this.op=r,i=this.expandBuffer(),r=this.op);o--;)i[r]=i[r++-a];for(;this.bitsbuflen>=8;)this.bitsbuflen-=8,this.ip--;this.op=r},z.RawInflate.prototype.decodeHuffmanAdaptive=function(t,e){var i=this.output,r=this.op;this.currentLitlenTable=t;for(var n,s,a,o,f=i.length;256!==(n=this.readCodeByTable(t));)if(n<256)r>=f&&(i=this.expandBuffer(),f=i.length),i[r++]=n;else for(s=n-257,o=z.RawInflate.LengthCodeTable[s],z.RawInflate.LengthExtraTable[s]>0&&(o+=this.readBits(z.RawInflate.LengthExtraTable[s])),n=this.readCodeByTable(e),a=z.RawInflate.DistCodeTable[n],z.RawInflate.DistExtraTable[n]>0&&(a+=this.readBits(z.RawInflate.DistExtraTable[n])),r+o>f&&(i=this.expandBuffer(),f=i.length);o--;)i[r]=i[r++-a];for(;this.bitsbuflen>=8;)this.bitsbuflen-=8,this.ip--;this.op=r},z.RawInflate.prototype.expandBuffer=function(t){var e,i,r=new(_?Uint8Array:Array)(this.op-z.RawInflate.MaxBackwardLength),n=this.op-z.RawInflate.MaxBackwardLength,s=this.output;if(_)r.set(s.subarray(z.RawInflate.MaxBackwardLength,r.length));else for(e=0,i=r.length;e<i;++e)r[e]=s[e+z.RawInflate.MaxBackwardLength];if(this.blocks.push(r),this.totalpos+=r.length,_)s.set(s.subarray(n,n+z.RawInflate.MaxBackwardLength));else for(e=0;e<z.RawInflate.MaxBackwardLength;++e)s[e]=s[n+e];return this.op=z.RawInflate.MaxBackwardLength,s},z.RawInflate.prototype.expandBufferAdaptive=function(t){var e,i,r,n,s=this.input.length/this.ip+1|0,a=this.input,o=this.output;return t&&("number"==typeof t.fixRatio&&(s=t.fixRatio),"number"==typeof t.addRatio&&(s+=t.addRatio)),s<2?(i=(a.length-this.ip)/this.currentLitlenTable[2],n=i/2*258|0,r=n<o.length?o.length+n:o.length<<1):r=o.length*s,_?(e=new Uint8Array(r),e.set(o)):e=o,this.output=e,this.output},z.RawInflate.prototype.concatBuffer=function(){var t,e,i,r,n,s=0,a=this.totalpos+(this.op-z.RawInflate.MaxBackwardLength),o=this.output,f=this.blocks,h=new(_?Uint8Array:Array)(a);if(0===f.length)return _?this.output.subarray(z.RawInflate.MaxBackwardLength,this.op):this.output.slice(z.RawInflate.MaxBackwardLength,this.op);for(e=0,i=f.length;e<i;++e)for(t=f[e],r=0,n=t.length;r<n;++r)h[s++]=t[r];for(e=z.RawInflate.MaxBackwardLength,i=this.op;e<i;++e)h[s++]=o[e];return this.blocks=[],this.buffer=h,this.buffer},z.RawInflate.prototype.concatBufferDynamic=function(){var t,e=this.op;return _?this.resize?(t=new Uint8Array(e),t.set(this.output.subarray(0,e))):t=this.output.subarray(0,e):(this.output.length>e&&(this.output.length=e),t=this.output),this.buffer=t,this.buffer};var W=function(t,e){this.loadSuccess=new B.FSEvent,this.loadFailure=new B.FSEvent,this.st=document.createElement("script"),this.st.type="text/javascript",this.st.src=t,e&&(this.st.id=e),this.br=B.getBrowserInstance(),void 0!==this.st.addEventListener?this._loadOnOthers():void 0!==this.st.attachEvent&&this._loadOnIE()};W.prototype._loadOnIE=function(){var t=this,e=this.st;e.onreadystatechange=function(){3==e.readyState&&(e.onreadystatechange=function(){t.loadSuccess.fire(e.src),t.loadFailure=null},t.loadFailure&&t.loadFailure.fire(e.src))},document.body.appendChild(e)},W.prototype._loadOnOthers=function(){this.st.addEventListener("load",e.proxy(function(){this.loadSuccess.fire(this.st.src)},this),!1),this.st.addEventListener("error",e.proxy(function(){this.loadFailure.fire(this.st.src)},this),!1),document.body.appendChild(this.st)},B.Async=function(t,e,i){this.isParallel=!!t,this._queue=[],this.success=e,this.fail=i,this.isPending=!0},B.Async.prototype.enqueue=function(t){this._queue.push({fn:t,resolved:!1}),(this.isParallel||1==this._queue.length)&&t.apply(this,[{resolve:e.proxy(function(){e.nextTick(e.proxy(function(){this.ctx.resolve(this.cb)},this))},{cb:t,ctx:this}),error:e.proxy(function(){this.ctx.error(this.cb)},{cb:t,ctx:this})}])},B.Async.prototype.resolve=function(t){if(this.isPending){if(!t)throw new Error("Missing caller argument.");var i,r,n=!1;for(i=0;i<this._queue.length;i++)r=this._queue[i],r.fn===t?r.resolved=!0:r.resolved||(n=!0);if(!this.isParallel&&n){var s;for(i=0;i<this._queue.length;i++)if(r=this._queue[i],!1===r.resolved){s=r;break}if(s)return void s.fn.apply(this,[{resolve:e.proxy(function(){this.ctx.resolve(this.cb)},{cb:s.fn,ctx:this}),error:e.proxy(function(){this.ctx.error(this.cb)},{cb:s.fn,ctx:this})}])}n||(this.isPending=!1,this.success.call(this))}},B.Async.prototype.error=function(){this.isPending=!1,this.fail&&this.fail.call(this)},B.imgInfo=function(t,i){var r=function(){};i=i||r;var n=new Image;n.onload=function(){i(n.width,n.height)},n.onerror=function(){},t.indexOf("//")>-1?n.src=t:n.src=e.makeURI("$"+t),n.width&&(n.onload=n.onerror=r,i(n.width,n.height))},B.getHashParm=function(t){var i=window.location.hash.toString();if(i&&i.length>0)for(var r=i.split("&"),n=0;n<r.length;n++){var s=r[n].split("="),a=e.toLowerCase(s[0]).trim();if(a==e.toLowerCase(t)){if(s.length>1)return decodeURIComponent(s[1]);break}}},B.compile=function(t){var e,i=[].constructor.constructor;return delete[].constructor.constructor,e=new[].constructor.constructor("var v = ''; try { v = "+t+"} catch(err) {}return v;"),[].constructor.constructor=i,e.call(window)},B.retrieveNestedVariable=function(t,e){for(var i=t||window,r=e.split("."),n=0;n<r.length&&i;)i=i[r[n++]];return void 0!==i&&n===r.length?i:void 0},B.dedupe=function(t){var e,i;for(e=t.length-1;e>=0;e--)for(i=e-1;i>=0;i--)t[i]==t[e]&&t.splice(e,1);return t},B.arrayIndexOf=function(t,e){for(var i in e)if(e[i]===t)return i;return-1},B.inArray=function(t,e){return-1!=B.arrayIndexOf(t,e)},B.Browser=function(t){var i=this,r=t||navigator.userAgent,n=e.toLowerCase(r);e.ext(i,{agent:r,os:{name:"",version:0},browser:{name:"",version:0,actualVersion:0},isMobile:/iphone|ipad|ipod|android|kindle|silk|bntv|nook|blackberry|playbook|mini|windows\sce|windows\sphone|palm|bb10/i.test(r)||!!window.orientation,isTablet:/ipad|playbook|nook|bntv/i.test(r),isWinPhone:/Windows Phone/i.test(r),fp:"",supportsLocalStorage:!1,supportsPostMessage:!!window.postMessage,isIE:!1,isZoomable:!0,supportsSVG:document.implementation.hasFeature("http://www.w3.org/TR/SVG11/feature#BasicStructure","1.1"),isReady:!1,ready:new B.FSEvent,_internalReady:new B.FSEvent,isIos:!1,servUrl:location.protocol+"//device.4seeresults.com/detect?accessToken="});try{localStorage&&(localStorage.setItem("a","b"),localStorage.removeItem("a"),i.supportsLocalStorage=!0)}catch(t){}i._internalReady.subscribe(function(){i.fp=new O(i),i.fp.ready.subscribe(function(){i.fp=i.fp.sig,i.ready.fire(i)},!0,!0)}),i.isMobile&&/iphone|ipad|ipod/i.test(r)&&!i.isWinPhone&&(i.isIos=!0);var s=function(t){return n.indexOf(e.toLowerCase(t))>-1},a=function(t,e){return"IE"!=t?e:e>6&&e<10?s("Trident")||7!=e?s("Trident/5.0")&&e<=9?9:s("Trident/4.0")&&e<9?s("WOW64")?8:7==e?e:8:e:7:e},o=function(t,i){return i?s("Windows Phone")?"Winphone":s("iPod")?"iPod":s("iPad")?"iPad":s("iPhone")?"iPhone":(s("blackberry")||s("playbook")||s("BB10"))&&s("applewebkit")?"Blackberry":s("Kindle")||s("Silk")?"Kindle":s("BNTV")||s("Nook")?"Nook":s("Android")?"Android":e.isDefined(window.orientation)?"Mobile":"Other":s("Windows")?"Windows":s("OS X")?"Mac":s("Linux")||s("Googlebot")?"Linux":s("Mac")?"Mac":void 0},f=function(t,e){for(;t>=e;)t/=10;return t},h=function(t,i){var r,n,a,o,h,u;return s("windows phone")||!s("ipad")&&!s("iphone")?s("googlebot")?1:s("mac os x")?(r=/OS X ([0-9_]*)/gi.exec(t),n=r[1].split("_"),a=parseInt(n[0]),h=parseInt(n[1]),u=parseInt(n[2]),h+=f(u,1),a+f(h,1)):s("Windows NT")?(r=/Windows NT ([0-9\.]*)/gi.exec(t),n=r[1].split("."),a=parseInt(n[0]),h=parseInt(n[1]),a+f(h,1)):(r=t.match(/Windows Phone OS[\/\s](\d+\.?\d+)/)||t.match(/Windows Phone[\/\s](\d+\.?\d+)/)||t.match(/Android[\/\s](\d+\.?\d+)/),a=e.isDefined(r)?r[1]:1,o=parseFloat(a),!isNaN(o)&&o>0?o:a):(r=/OS ([0-9_]*) like/gi.exec(t),n=r[1].split("_"),a=parseInt(n[0]),h=parseInt(n[1]),a+f(h,1))},u=function(){if("Winphone"!=i.os.name){var t=document.querySelectorAll("head meta[name=viewport],head meta[name=VIEWPORT],head meta[name=Viewport]")||[];if(Array.isArray(t)||(t=[t]),t.length>0){for(var r=function(t,e){var i=new RegExp("[\\w\\W]*"+e+"[\\s]*=[\\s]*([^\\s,;]*)[\\w\\W]*","i");return t?t.match(i):null},n=0;n<t.length;n++){var s=t[n].content,a=r(s,"user-scalable"),o=r(s,"initial-scale"),f=r(s,"maximum-scale");if(a&&a.length>1&&("0"==a[1]||"no"==e.toLowerCase(a[1])))return!1;if(o&&f)return!(o.length>1&&f.length>1&&1==parseFloat(o[1])&&1==parseFloat(f[1]))}return!0}return!0}return!1},l=B._getBrowserNameAndVersion(r),c=function(){i.browser.name=l.name,i.browser.version=l.version,i.browser.actualVersion=a(i.browser.name,i.browser.version),i.os.name=o(0,i.isMobile),i.os.version=h(r,i.isMobile)},d=function(){i.isZoomable=u(),i.isReady=!0,i._internalReady.fire()},p=function(){c()};if(i.isMobile)if(i.isIos||""===i.servUrl||i.isTablet||i.isWinPhone)p(),d();else{var g,y=function(t){var e=JSON.parse(t);i.browser.name=e.browser.name,i.browser.version=i.browser.actualVersion=e.browser.version,i.os.name=e.os.name,i.os.version=parseFloat(e.os.version),i.isMobile=e.isMobile,i.isTablet=e.isTablet,d()},b=this.supportsLocalStorage;if(b&&!t&&(g=sessionStorage.getItem("ACS_BROWSER")),g)y(g);else{var w=function(t){b&&sessionStorage.setItem("ACS_BROWSER",t),y(t)},v=function(){p(),d()},m=function(){var t=new Date,e=t.getFullYear().toString(),i=(t.getMonth()+1).toString(),r=t.getDate().toString();return e+(i[1]?i:"0"+i[0])+(r[1]?r:"0"+r[0])},S={method:"GET",url:i.servUrl+function(){var t=m()+"ForeSee"+(location.origin||"null");return B.hashCode(t)}()+"&ua="+r,type:"*/*",contentType:"application/x-www-form-urlencoded",success:w,failure:v};new B.AjaxTransport(S,!0).send()}}else c(),i.isReady=!0,i.isIE="IE"==i.browser.name,i._internalReady.fire()},B.getBrowserInstance=function(){function t(){return e||(e=new B.Browser)}var e;return t}(),B._getBrowserNameAndVersion=function(t){var e,i,r="Unknown"
;return null!==(i=t.match(/Opera[\/\s](\d+\.\d+)/))?r="Opera":null!==(i=t.match(/Edge\/([0-9\.]*)/))?r="IE":null!==(i=t.match(/opr[\/\s](\d+\.\d+)/i))?r="Opera":null!==(i=t.match(/Windows Phone[\/\s](\d+\.\d+)/))?r="IEMobile":null!==(i=t.match(/MSIE (\d+\.\d+)/))?r="IE":null!==(i=t.match(/Navigator[\/\s](\d+\.\d+)/))?r="Netscape":null!==(i=t.match(/Chrome[\/\s](\d+\.\d+)/))?r="Chrome":null!==(i=t.match(/CriOS[\/\s](\d+\.\d+)/))?r="Chrome":null!==(i=t.match(/Version\/([0-9\.]*)[\w\W]*Safari/i))?r="Safari":null!==(i=t.match(/Firefox[\/\s](\d+\.\d+)/))?r="Firefox":null!==(i=t.match(/googlebot/gi))?(r="Chrome",e=44):Object.hasOwnProperty.call(window,"ActiveXObject")&&!window.ActiveXObject&&(r="IE",e=11),{name:r,version:e||(null!==i?parseFloat(i[1]):void 0)}};var J={},K=0;B.loadCSS=function(t,e,i,r){if(!r)throw new Error("loadCSS missing browser instance");var n=J[t];if(n){if(n.link.parentElement)return n.success.subscribe(e||function(){},!0,!0),n.fail.subscribe(i||function(){},!0,!0),n.link;delete J[t]}var s,a,o=!1;K++,s="fs-css-"+K,a=document.createElement("link"),a.setAttribute("id",s),a.setAttribute("rel","stylesheet"),a.setAttribute("type","text/css");var f={link:a,url:t,didfail:!1,didsucceed:!1,success:new B.FSEvent,fail:new B.FSEvent};f.success.subscribe(e,!0,!0),f.fail.subscribe(i,!0,!0),J[t]=f,a.addEventListener("load",function(){o=!0,f.didsucceed=!0,f.success.fire(a)},!1),a.addEventListener("error",function(){f.didfail=!0,f.fail.fire(a)},!1);var h=document.documentElement,u=document.getElementsByTagName("head");return u&&u.length>0&&(h=u[0]),h.appendChild(a),a.setAttribute("href",t),a},e.nextTick=function(t){setTimeout(t||function(){},0)},B.randomRange=function(t,e){return t+Math.random()*(e-t)},B.isNumeric=function(t){return!isNaN(parseFloat(t))&&isFinite(t)},B.products={},B.productArr=[],B.registerProduct=function(t,e){e=e||{},B.products[t]=e,B.productArr.push(t)};var V={_id:"",has:function(){try{return!!(window.s&&e.isFunction(s.c_r)&&s.c_r("s_vi").indexOf("[CE]")>-1)}catch(t){return!1}},intervals:{uid:"",mcid:""},sgi:function(t){return!(!t||"undefined"==typeof s_gi||!s_gi)&&(Array.isArray(t)&&2===t.length&&(t=t.join(",")),s_gi(t))},uid:function(t,e){var i,r,n=0;clearInterval(this.intervals.uid),this.intervals.uid=setInterval(function(){i=this.sgi(t),n++<10&&i?(i.visitorID?r={name:"OMTR_VID",value:i.visitorID}:i.analyticsVisitorID?r={name:"OMTR_VID",value:i.analyticsVisitorID}:i.fid&&(r={name:"OMTR_FID",value:i.fid}),r&&(e(r),clearInterval(this.intervals.uid))):clearInterval(this.intervals.uid)}.bind(this),1e3)},mcid:function(t,e){var i,r,n=0;clearInterval(this.intervals.mcid),this.intervals.mcid=setInterval(function(){i=this.sgi(t),n++<10&&i?(i.marketingCloudVisitorID&&(r={name:"OMTR_MCID",value:i.marketingCloudVisitorID}),r&&(e(r),clearInterval(this.intervals.mcid))):clearInterval(this.intervals.mcid)}.bind(this),1e3)},beacon:function(){function t(t,e){for(var i="",r=e.split("&"),n=0;n<r.length;n++)for(var s=r[n].split("="),a=0;a<t.length;a++)if(t[a]==s[0]){i+=s[0]+"="+s[1]+"&";break}return"&"==i.substr(i.length-1)&&(i=i.substr(0,i.length-1)),i}if(V._id)return V._id;var i,r,n,a,o=["AQB","mid","aid","vid","fid","AQE"],f="";for(var h in window)if("s_i_"==h.substring(0,4)&&window[h].src&&(i=window[h].src,i.indexOf("/b/ss/")>=0)){f=i;break}if(!f&&window.s_c_il&&window.s_c_il[0]&&window.s_c_il[0].eb&&(f=window.s_c_il[0].eb),!f&&window.document.images)for(var u=0;u<window.document.images.length;u++)if(i=window.document.images[u].src,i.indexOf("/b/ss/")>=0){f=i;break}if(f&&e.isString(f)){r=f.substring(0,f.indexOf("?")),n=f.substring(f.indexOf("?")+1),a=t(o,n),window.s&&s.trackingServerSecure&&(r="https://"+s.trackingServerSecure+f.substring(f.indexOf("/b/ss/"),f.indexOf("?")),n=f.substring(f.indexOf("?")+1),a=t(o,n));var l=r+"?"+a;return l.length<3?l=null:V._id=l,l}}};B.INT.OM=V;var G={has:function(){var t=window.ga;return"function"==typeof t&&t.getAll&&t.getAll().length},uid:function(t){var i=e.nextTick;G.has()?ga(function(e){i(function(){if(e)t(e.get("clientId"));else try{t(ga.getAll()[0].get("clientId"))}catch(e){t()}})}):i(function(){t()})}};B.INT.GA=G;var q={byteArrayToString:function(t){for(var e="",i=0;i<t.length;i++)e+=String.fromCharCode(t[i]);return e},stringToByteArray:function(t){for(var e=[],i=0;i<t.length;i++)e[e.length]=t.charCodeAt(i);return e},_utf8_encode:function(t){for(var e="",i=0;i<t.length;i++){var r=t.charCodeAt(i);r<128?e+=String.fromCharCode(r):r>127&&r<2048?(e+=String.fromCharCode(r>>6|192),e+=String.fromCharCode(63&r|128)):(e+=String.fromCharCode(r>>12|224),e+=String.fromCharCode(r>>6&63|128),e+=String.fromCharCode(63&r|128))}return e},compress:function(t,i){e.isFunction(i)||(i=q._utf8_encode);var r=i(t),n=q.stringToByteArray(r),s=new z.RawDeflate(n,{compressionType:2}).compress();return btoa(q.byteArrayToString(s))},fragmentAndCompress:function(t,e){e=e||1e5;for(var i="",r=parseInt(t.length/e)+1,n=0;n<r;n++){i+="_CMP_"+q.compress(t.substring(n*e,(n+1)*e))}return i},decompress:function(t,i){var r=q.stringToByteArray(atob(t)),n=new z.RawInflate(r,{index:0,bufferSize:41152,bufferType:z.RawInflate.BufferType.ADAPTIVE,resize:!0}),s=q.byteArrayToString(n.decompress());return e.isFunction(i)&&(s=i(s)),s}};B.Compress=q,B.addClass=function(t,i){var r,n,s,a;for(e.isDefined(t.length)||(t=[t]),r=0,n=t.length;r<n;r++)s=t[r],B.isElement(s)&&(a=s.className||"",new RegExp("\\b"+i+"\\b").test(a)||(s.className=(""===a?"":a+" ")+i))},B.removeClass=function(t,i){var r,n,s;for(e.isDefined(t.length)||(t=[t]),r=0,n=t.length;r<n;r++)s=t[r],B.isElement(s)&&s.className&&(s.className=s.className.replace(new RegExp("(\\s|^)"+i+"(\\s|$)")," ").replace(/^\s+|\s+$/g,""))},B.hasClass=function(t,e){return B.isElement(t)&&t.classList&&t.classList.contains(e)},B.css=function(t,i,r){if(t){e.isDefined(t.length)||(t=[t]);for(var n=0;n<t.length;n++)for(var s in i)s&&(-1=="zIndex".indexOf(s)&&"number"==typeof i[s]&&"opacity"!=s&&(i[s]+="px"),r?t[n].style.cssText+=";"+s+":"+i[s]+" !important":t[n].style[s]=i[s])}return t},B.attr=function(t,i){if(t){e.isDefined(t.length)||(t=[t]);for(var r=0;r<t.length;r++)for(var n in i)t[r].setAttribute(n,i[n])}return t},B.restrictFocus=function(t){for(var e=document.querySelectorAll("a, input[type=text], textarea, button, input[type=radio], select, *[tabIndex]",t).sort(function(t,e){return parseInt(t.tabIndex)>parseInt(e.tabIndex)}),i=0;i<e.length;i++){var r=e[i];B.Unbind(r,"keydown"),B.Bind(r,"keydown",function(t){return function(e){var i,r;if(9===e.keyCode)for(i=0;i<t.length;i++)if(t[i]===e.target){if(e.preventDefault?e.preventDefault():e.returnValue=!1,r=i,e.shiftKey)do{r=0===r?t.length-1:r-1}while((t[r].offsetLeft<=0||t[r].tabIndex<0)&&r!=i);else do{r=(r+1)%t.length}while((t[r].offsetLeft<=0||t[r].tabIndex<0)&&r!=i);t[r].focus();break}}}(e))}},B.hideAll=function(t){var e,i=document.body.querySelectorAll("*");for(e=0;e<i.length;e++)B.css(i[e],{display:"none"})},B.elindex=function(t){for(var e,i=t.parentNode.childNodes,r=0,n=0;(e=i.item(r++))&&e!=t;)1==e.nodeType&&n++;return n},B.isElement=function(t){return t&&t.nodeType&&(1==t.nodeType||11==t.nodeType||9==t.nodeType)},B.decodeHTMLEntities=function(t){return(new DOMParser).parseFromString(t,"text/html").documentElement.textContent},B.generateGUID=function(){return B.md5(B.now()+""+navigator.userAgent+window.location+(new Date).getTimezoneOffset()+Math.random())},B.ImageTransport=function(t){var i={data:{},success:function(){},failure:function(){}};this.options=e.ext(i,t)},B.ImageTransport.prototype.send=function(t){var i=e.ext(this.options,t),r=new Image;r.onerror=i.failure,r.onload=function(){i.success({width:r.width,height:r.height})},r.src=e.toQueryString(i.data,i.url,!1)},window.__fsJSONPCBr={},window.__fsJSONPCB=e.proxy(function(t){if(t){var e=t.filename,i=atob(t.contents);window.__fsJSONPCBr[e]&&window.__fsJSONPCBr[e].fire(i)}},this),B.JSONP=function(t){this._expireTimeout=null,this._networkError=new B.FSEvent,this.opts=e.ext({success:function(){},failure:function(){},timeout:5e3},t)},B.JSONP.prototype.get=function(t,i){var r=t.indexOf("?")>-1?t.substr(t.indexOf("?")+1):"",n=t.substr(0,t.lastIndexOf("/")+1),s=t.substr(t.lastIndexOf("/")+1),a=window.__fsJSONPCBr;this._expireTimeout=setTimeout(e.proxy(function(){this._networkError.fire({type:"timedout"})},this),this.opts.timeout),s.indexOf("?")>-1&&(s=s.substr(0,s.indexOf("?")));var o=(i||"")+s;if(!a[o]){a[o]=new B.FSEvent;var f=n+s.substr(0,s.lastIndexOf("."))+"___"+s.substr(s.lastIndexOf(".")+1)+".js"+(r.length>0?"?"+r:""),h=new W(f,"_fscl"+o);h.loadFailure.subscribe(e.proxy(function(){this.el.parentNode.removeChild(this.el),this.ctx._networkError.fire({type:"internalserror"})},{ctx:this,el:h.st}))}a[o].subscribe(e.proxy(function(t){this.ctx.opts.success(t),clearTimeout(this.ctx._expireTimeout);var e=document.getElementById(this.tgId);e&&e.parentNode.removeChild(e)},{ctx:this,tgId:"_fscl"+o}),!0,!0),this._networkError.subscribe(e.proxy(function(t){this.opts.failure(t),a[o].unsubscribeAll()},this),!0,!0)},B.sign=function(t){var i=(new Date).getTime(),r=t.substr(t.indexOf("/rec/")),n=B.md5((r+i).toString());return-1==t.indexOf("?")?t+="?":t+="&",t+"token="+i+"&sig="+e.enc(n)},B.Journey=function(t,i,r,n,s){this.threshold=s||400,this.browser=n,e.isString(i)||(i=""),this.cors=new B.AjaxTransport,this.url=e.config.analyticsUrl.replace(/^https?:/i,location.protocol),this.data={customerId:t,appId:i,userId:r||"0000-0000-0000-0000-0000",deviceProfile:{fs_os:n.os.name,fs_osVersion:n.os.version,fs_sdkVersion:e.config.codeVer,fs_browserName:n.browser.name,fs_browserVersion:n.browser.version,fs_timezone:(new Date).getTimezoneOffset(),fs_type:n.isMobile?"Mobile":n.isTablet?"Tablet":"Desktop",fs_productType:"web sdk"},events:[]},this.eventsDefault={properties:{fs_pageUrl:[location.href]}}},B.Journey.prototype._send=function(){this._svT=null,this.data.events.length>0&&this.cors.send({url:this.url,contentType:"application/json",headers:{"Request-API-Version":"1.0.0"},data:this.data,method:"POST",success:e.proxy(function(){this.data.events=[]},this)})},B.Journey.prototype.addEventsDefault=function(t,i){if(!t||["properties","metrics","data"].indexOf(t)<0)return this.eventsDefault;if(!$(t,i))return this.eventsDefault;for(var r in i)i.hasOwnProperty(r)&&Array.isArray(i[r])&&i[r].length>0&&(i[r]=i[r].filter(e.isDefined),i[r].length<1&&delete i[r]);return this.eventsDefault=this.eventsDefault||{},this.eventsDefault[t]=e.ext(this.eventsDefault[t],i),this.eventsDefault},B.Journey.prototype.setKey=function(t,i){return!!e.isObject(i)&&(this.data[t]=i,this.data.events.length||this.addEventString("fs_setKey"),!0)},B.Journey.prototype.addEvent=function(t){var e=typeof t;switch(e){case"string":this.addEventString(t);break;case"object":this.addEventObj(t);break;default:console.error("ForeSee: event is not a valid type: ",e)}},B.Journey.prototype.addEventObj=function(t){if(t.timestamp||(t.timestamp=(new Date).toISOString()),t.timezone||(t.timezone=(new Date).getTimezoneOffset()),!(t.name&&t.name.length>0&&$("properties",t.properties)&&$("metrics",t.metrics)&&$("data",t.data)))return void console.error("ForeSee: Invalid Event. For proper usage, please refer to http://developer.foresee.com/docs-articles/foresee-hosted-code/calling-api-methods/event-logging/");t=X(t,this.eventsDefault),this.data.events.push(t),Y(this)};var X=function(t,i){return["properties","metrics","data"].reduce(function(t,r){return i[r]&&(t[r]=e.ext(t[r],i[r])),t},t)};B.Journey.prototype.addEventString=function(t){this.data.events.push(X({name:t,timestamp:(new Date).toISOString(),timezone:(new Date).getTimezoneOffset()},this.eventsDefault)),Y(this)};var Y=function(t,i){i?t._send(!0):t._svT||(t._svT=setTimeout(e.proxy(function(){t._send(!0)},t),t.threshold))},$=function(t,e){var i;switch(t){case"properties":if(e)for(i in e)if(!Array.isArray(e[i]))return console.error("ForeSee: Invalid properties"),!1;break;case"metrics":if(e)for(i in e)if(!B.isNumeric(e[i]))return console.error("ForeSee: Invalid metrics"),!1}return!0};return B.Cookie=function(t){this.opts=t||{}},B.Cookie.prototype.set=function(t,i,r){var n,s=this.opts;r&&(s=e.ext({},s,r)),i=e.isDefined(s.encode)?e.enc(i):i,t=e.enc(t),"localhost"==s.domain&&delete s.domain,e.config.cookieSecure&&"false"!==e.config.cookieSecure&&"false"!==e.hasSSL&&(i+=";secure");for(var a in s)if(s[a])switch(n=s[a],i+=";"+("duration"==a?"expires":a),a){case"expires":i+="="+(e.isDate(n)?n.toUTCString():n)+";";break;case"duration":i+="="+new Date(B.now()+n*B.FULL_DAY).toUTCString()+";";break;default:i+="="+n}document.cookie=t+"="+i},B.Cookie.prototype.get=function(t){var e=document.cookie.match("(?:^|;)\\s*"+B.escapeRegExp(t)+"=([^;]*)");return e?decodeURIComponent(e[1]):null},B.Cookie.prototype.kill=function(t){var e=new Date;e.setTime(e.getTime()-9999),this.set(t,"",{expires:e.toUTCString()})},B.CPPS=function(t,e){this.gs=t,this.onSet=new B.FSEvent,this._blCPP=[],this.exp=e||864e5},B.CPPS.prototype={addToBlacklist:function(t){t=t||[];var e=this.all();this._blCPP=this._blCPP.concat(t);for(var i=0;i<t.length;i++)delete e[t[i]];this.gs.set("cp",e)},set:function(t,e){if(-1==this._blCPP.indexOf(t)){var i=this.all();i[t]=e+"",this.gs.set("cp",i,this.exp),this.onSet.fire(t,e)}},get:function(t){return this.all()[t]},all:function(){return this.gs.get("cp")||{}},toQueryString:function(){var t=[],i=this.all();for(var r in i)t.push("cpp["+e.enc(r)+"]="+e.enc(i[r]));return t.join("&")},erase:function(t){var e=this.all();delete e[t],this.gs.set("cp",e)},save:function(){this.gs.save()},append:function(t,e,i){var r,n,s,a=this.gs.get("cp")||{};-1==this._blCPP.indexOf(t)&&(a[t]=(a[t]||"")+","+e,i&&(r=a[t].split(","),n=r.length-1,s=r.length>i?r.length-i:0,a[t]=r.splice(s,n-s+1).join()),this.gs.set("cp",a))}},B.defaultConfigs={global:{storage:"COOKIE",alwaysOnLatest:1,cookieSecure:!1,deferredLoading:!1,products:{trigger:!1,feedback:!1,record:!1}},survey:{devices:{overridesEnabled:!0,desktop:{icon:"aspark100.png",fbtype:"badge",surveytype:"popup",disabled:!0,size:"medium",fbsize:"medium"},mobile:{icon:"aspark100.png",fbtype:"badge",surveytype:"popup",disabled:!0,size:"medium",fbsize:"medium"},tablet:{icon:"aspark100.png",fbtype:"badge",surveytype:"popup",popup:!0,disabled:!0,fbsize:"medium",size:"medium",fbdirection:"horizontal",fblocation:"middleleft"}},icon:"aspark100.png",delay:0,template:"default",fblocation:"bottomleft",fbtype:"badge",disabled:!1,fbanimate:!1,fbfixed:!0,fbdirection:"horizontal",surveytype:"popup",replay:!0,fbcolor:"#f1c40f",isExistingSurveyDisabled:!0,saved:!0,whitelistActive:!1,blacklistActive:!1,whitelistData:[],blacklistData:[]}},B.DomStorage=function(t,i,r,n){this.isLocal=!!r,t||(t="STORAGE"),this.guid="FSR_"+t.replace(/[- _.&]/g,"").toUpperCase(),this.StorageFull=new B.FSEvent,this.storageLimit=45e5,this.kill(),this.sync(),e.isDefined(i)&&!i||setTimeout(e.proxy(function(){B.Bind(window,"unload",e.proxy(function(){this.commit()},this))},this),100)},B.DomStorage.prototype.testStorageLimit=function(){return this.storageBytesObj+this.storageBytesBlob>=this.storageLimit&&(this.StorageFull.fire(this),!0)},B.DomStorage.prototype.dispose=function(t){this._data_obj[t]&&(delete this._data_obj[t],this.storageBytesObj=JSON.stringify(this._data_obj).length)},B.DomStorage.prototype.kill=function(){this.storageBytesObj=0,this.storageBytesBlob=0,this._data_obj={},this._data_blob="",this.isNewStorage=!0},B.DomStorage.prototype.get=function(t){return this._data_obj[t]},B.DomStorage.prototype.getBlob=function(){return this._data_blob},B.DomStorage.prototype.erase=function(t){delete this._data_obj[t],this.storageBytesObj=JSON.stringify(this._data_obj).length,this.isNewStorage=!1,this.testStorageLimit()},B.DomStorage.prototype.set=function(t,e){e&&(this._data_obj[t]=e,this.storageBytesObj=JSON.stringify(this._data_obj).length,this.isNewStorage=!1,this.testStorageLimit())},B.DomStorage.prototype.setBlob=function(t){this._data_blob=t,this.storageBytesBlob=this._data_blob.length,this.isNewStorage=!1,this.testStorageLimit()},B.DomStorage.prototype.isNew=function(){var t;return window.opener&&!this.get("isNew")&&(t=!0,this.set("isNew",t)),t||this.isNewStorage},B.DomStorage.initialize=function(t){t.apply(B.DomStorage)},B.DomStorage.isSupported=function(){return!!localStorage},B.DomStorage.prototype.sync=function(){var t;try{t=localStorage.getObject(this.guid+"_OBJ"),t&&t.length>0&&(this._data_obj=JSON.parse(t),this.storageBytesObj=t.length,this.isNewStorage=!1)}catch(t){}try{t=localStorage.getObject(this.guid+"_BLOB"),t&&t.length>0&&(this._data_blob=t,this.storageBytesBlob=t.length,this.isNewStorage=!1)}catch(t){}},B.DomStorage.prototype.commit=function(){try{localStorage.setItem(this.guid+"_OBJ",JSON.stringify(this._data_obj)),localStorage.setItem(this.guid+"_BLOB",this._data_blob)}catch(t){}},B.WindowStorage=function(t,i){t||(t="STORAGE"),this.guid="FSR_"+t.replace(/[- _.&]/g,"").toUpperCase(),this.storageLimit=5e6,this.StorageFull=new B.FSEvent,this.kill(),this.sync(),e.isDefined(i)&&!i||setTimeout(e.proxy(function(){B.Bind(window,"unload",e.proxy(function(){this.commit()},this))},this),100)},B.WindowStorage.prototype.testStorageLimit=function(){return this.storageBytesObj+this.storageBytesBlob>=this.storageLimit&&(this.StorageFull.fire(this),!0)},B.WindowStorage.prototype.dispose=function(t){this._data_obj[t]&&(delete this._data_obj[t],this.storageBytesObj=JSON.stringify(this._data_obj).length)},B.WindowStorage.prototype.kill=function(){this.storageBytesObj=0,this.storageBytesBlob=0,this._data_obj={},this._data_blob="",this.isNewStorage=!0},B.WindowStorage.prototype.get=function(t){return this._data_obj[t]},B.WindowStorage.prototype.getBlob=function(){return this._data_blob},B.WindowStorage.prototype.erase=function(t){delete this._data_obj[t],this.storageBytesObj=JSON.stringify(this._data_obj).length,this.isNewStorage=!1,this.testStorageLimit()},B.WindowStorage.prototype.set=function(t,e){e&&(this._data_obj[t]=e,this.storageBytesObj=JSON.stringify(this._data_obj).length,this.isNewStorage=!1,this.testStorageLimit())},B.WindowStorage.prototype.setBlob=function(t){this._data_blob=t,this.storageBytesBlob=this._data_blob.length,this.isNewStorage=!1,this.testStorageLimit()},B.WindowStorage.prototype.isNew=function(){return this.isNewStorage},B.WindowStorage.initialize=function(t){t.apply(B.WindowStorage)},B.WindowStorage.isSupported=function(){return!0},B.WindowStorage.prototype.sync=function(){var t=B.nameBackup||window.name||"",e=this.guid+"_",i="",r=t.indexOf(e+"BEGIN_OBJ");r>-1&&(i=t.substr(r+(e+"BEGIN_OBJ").length,t.indexOf(e+"END_OBJ")-(r+(e+"BEGIN_OBJ").length)));try{i.length>0&&(this._data_obj=JSON.parse(i),this.storageBytesObj=i.length,this.isNewStorage=!1)}catch(t){}i="",(r=t.indexOf(e+"BEGIN_BLOB"))>-1&&(i=t.substr(r+(e+"BEGIN_BLOB").length,t.indexOf(e+"END_BLOB")-(r+(e+"BEGIN_BLOB").length)));try{i.length>0&&(this._data_blob=i,this.storageBytesBlob=i.length,this.isNewStorage=!1)}catch(t){}},B.WindowStorage.prototype.commit=function(){var t=window.name;e.isDefined(t)||(t="");var i=this.guid+"_",r=t.indexOf(i+"BEGIN_OBJ"),n=JSON.stringify(this._data_obj),s=i+"BEGIN_OBJ"+n+i+"END_OBJ";r>-1?t=t.substr(0,r)+s+t.substr(t.indexOf(i+"END_OBJ")+(i+"END_OBJ").length):t+=s,r=t.indexOf(i+"BEGIN_BLOB"),s=i+"BEGIN_BLOB"+this._data_blob+i+"END_BLOB",r>-1?t=t.substr(0,r)+s+t.substr(t.indexOf(i+"END_BLOB")+(i+"END_BLOB").length):t+=s,window.name=B.nameBackup=t,this.storageBytes=window.name.length},B.nameBackup=window.name,B});