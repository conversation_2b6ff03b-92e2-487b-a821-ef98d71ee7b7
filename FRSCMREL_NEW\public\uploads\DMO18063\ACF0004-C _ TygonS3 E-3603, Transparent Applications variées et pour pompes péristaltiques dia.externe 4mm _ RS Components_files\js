
// Copyright 2012 Google Inc. All rights reserved.
(function(){

var data = {
"resource": {
  "version":"1",
  "macros":[],
  "tags":[],
  "predicates":[],
  "rules":[]
},
"runtime":[
[],[]
]
};
var da=this,ha=function(){if(null===ea){var a;a:{var b=da.document,c=b.querySelector&&b.querySelector("script[nonce]");if(c){var d=c.nonce||c.getAttribute("nonce");if(d&&fa.test(d)){a=d;break a}}a=null}ea=a||""}return ea},fa=/^[\w+/_-]+[=]{0,2}$/,ea=null,ia=function(a,b){function c(){}c.prototype=b.prototype;a.rf=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ye=function(a,c,g){for(var d=Array(arguments.length-2),e=2;e<arguments.length;e++)d[e-2]=arguments[e];return b.prototype[c].apply(a,
d)}};var f=function(a,b){this.C=a;this.wd=b};f.prototype.Kd=function(){return this.C};f.prototype.getType=f.prototype.Kd;f.prototype.getData=function(){return this.wd};f.prototype.getData=f.prototype.getData;var ka=function(a){return"number"===typeof a&&0<=a&&isFinite(a)&&0===a%1||"string"===typeof a&&"-"!==a[0]&&a===""+parseInt(a,10)},la=function(){this.ka={};this.Aa=!1};la.prototype.get=function(a){return this.ka["dust."+a]};la.prototype.set=function(a,b){!this.Aa&&(this.ka["dust."+a]=b)};la.prototype.has=function(a){return this.ka.hasOwnProperty("dust."+a)};var ma=function(a){var b=[],c;for(c in a.ka)a.ka.hasOwnProperty(c)&&b.push(c.substr(5));return b};
la.prototype.remove=function(a){!this.Aa&&delete this.ka["dust."+a]};la.prototype.M=function(){this.Aa=!0};var v=function(a){this.na=new la;this.i=[];a=a||[];for(var b in a)a.hasOwnProperty(b)&&(ka(b)?this.i[Number(b)]=a[Number(b)]:this.na.set(b,a[b]))};v.prototype.toString=function(){for(var a=[],b=0;b<this.i.length;b++){var c=this.i[b];null===c||void 0===c?a.push(""):a.push(c.toString())}return a.join(",")};v.prototype.set=function(a,b){if("length"==a){if(!ka(b))throw"RangeError: Length property must be a valid integer.";this.i.length=Number(b)}else ka(a)?this.i[Number(a)]=b:this.na.set(a,b)};
v.prototype.set=v.prototype.set;v.prototype.get=function(a){return"length"==a?this.length():ka(a)?this.i[Number(a)]:this.na.get(a)};v.prototype.get=v.prototype.get;v.prototype.length=function(){return this.i.length};v.prototype.T=function(){for(var a=ma(this.na),b=0;b<this.i.length;b++)a.push(b+"");return new v(a)};v.prototype.getKeys=v.prototype.T;v.prototype.remove=function(a){ka(a)?delete this.i[Number(a)]:this.na.remove(a)};v.prototype.remove=v.prototype.remove;v.prototype.pop=function(){return this.i.pop()};
v.prototype.pop=v.prototype.pop;v.prototype.push=function(a){return this.i.push.apply(this.i,Array.prototype.slice.call(arguments))};v.prototype.push=v.prototype.push;v.prototype.shift=function(){return this.i.shift()};v.prototype.shift=v.prototype.shift;v.prototype.splice=function(a,b,c){return new v(this.i.splice.apply(this.i,arguments))};v.prototype.splice=v.prototype.splice;v.prototype.unshift=function(a){return this.i.unshift.apply(this.i,Array.prototype.slice.call(arguments))};
v.prototype.unshift=v.prototype.unshift;v.prototype.has=function(a){return ka(a)&&this.i.hasOwnProperty(a)||this.na.has(a)};var na=function(){function a(a,b){c[a]=b}function b(){c={};g=!1}var c={},d,e={},g=!1,h={add:a,Wb:function(a,b,c){e[a]||(e[a]={});e[a][b]=c},create:function(e){var h={add:a,assert:function(a,b){if(!g){var h=c[a]||d;h&&h.apply(e,Array.prototype.slice.call(arguments,0))}},reset:b};h.add=h.add;h.assert=h.assert;h.reset=h.reset;return h},xc:function(a){return e[a]?(b(),c=e[a],!0):!1},oa:function(a){d=a},reset:b,Hc:function(a){g=a}};h.add=h.add;h.addToCache=h.Wb;h.loadFromCache=h.xc;h.registerDefaultPermission=
h.oa;h.reset=h.reset;h.setPermitAllAsserts=h.Hc;return h};var oa=function(){function a(a,c){if(b[a]){if(b[a].Pa+c>b[a].max)throw Error("Quota exceeded");b[a].Pa+=c}}var b={},c=void 0,d=void 0,e={ke:function(a){c=a},Xb:function(){c&&a(c,1)},me:function(a){d=a},W:function(b){d&&a(d,b)},He:function(a,c){b[a]=b[a]||{Pa:0};b[a].max=c},Jd:function(a){return b[a]&&b[a].Pa||0},reset:function(){b={}},qd:a};e.onFnConsume=e.ke;e.consumeFn=e.Xb;e.onStorageConsume=e.me;e.consumeStorage=e.W;e.setMax=e.He;e.getConsumed=e.Jd;e.reset=e.reset;e.consume=e.qd;return e};var pa=function(a,b,c){this.N=a;this.I=b;this.Z=c;this.i=new la};pa.prototype.add=function(a,b){this.i.Aa||(this.N.W(("string"===typeof a?a.length:1)+("string"===typeof b?b.length:1)),this.i.set(a,b))};pa.prototype.add=pa.prototype.add;pa.prototype.set=function(a,b){this.i.Aa||(this.Z&&this.Z.has(a)?this.Z.set(a,b):(this.N.W(("string"===typeof a?a.length:1)+("string"===typeof b?b.length:1)),this.i.set(a,b)))};pa.prototype.set=pa.prototype.set;
pa.prototype.get=function(a){return this.i.has(a)?this.i.get(a):this.Z?this.Z.get(a):void 0};pa.prototype.get=pa.prototype.get;pa.prototype.has=function(a){return!!this.i.has(a)||!(!this.Z||!this.Z.has(a))};pa.prototype.has=pa.prototype.has;pa.prototype.K=function(){return this.N};pa.prototype.M=function(){this.i.M()};var qa=function(){},ra=function(a){return"function"==typeof a},sa=function(a){return"string"==typeof a},ta=function(a){return"number"==typeof a&&!isNaN(a)},ua=function(a){return"[object Array]"==Object.prototype.toString.call(Object(a))},va=function(a,b){if(Array.prototype.indexOf){var c=a.indexOf(b);return"number"==typeof c?c:-1}for(var d=0;d<a.length;d++)if(a[d]===b)return d;return-1},xa=function(a,b){if(!ta(a)||!ta(b)||a>b)a=0,b=2147483647;return Math.floor(Math.random()*(b-a+1)+a)},ya=function(a){return Math.round(Number(a))||
0},Aa=function(a){return"false"==String(a).toLowerCase()?!1:!!a},Ba=function(a){var b=[];if(ua(a))for(var c=0;c<a.length;c++)b.push(String(a[c]));return b},Ca=function(){return new Date},Da=function(){this.prefix="gtm.";this.values={}};Da.prototype.set=function(a,b){this.values[this.prefix+a]=b};Da.prototype.get=function(a){return this.values[this.prefix+a]};Da.prototype.contains=function(a){return void 0!==this.get(a)};
var Ea=function(a,b,c){return a&&a.hasOwnProperty(b)?a[b]:c},Fa=function(a){var b=!1;return function(){if(!b)try{a()}catch(c){}b=!0}},Ga=function(a,b){for(var c in b)b.hasOwnProperty(c)&&(a[c]=b[c])},Ha=function(a){for(var b in a)if(a.hasOwnProperty(b))return!0;return!1};var w=function(a,b){la.call(this);this.yc=a;this.Gd=b};ia(w,la);var Ja=function(a,b){for(var c,d=0;d<b.length&&!(c=Ia(a,b[d]),c instanceof f);d++);return c},Ia=function(a,b){var c=a.get(String(b[0]));if(!(c&&c instanceof w))throw"Attempting to execute non-function "+b[0]+".";return c.o.apply(c,[a].concat(b.slice(1)))};w.prototype.toString=function(){return this.yc};w.prototype.getName=function(){return this.yc};w.prototype.getName=w.prototype.getName;w.prototype.T=function(){return new v(ma(this))};
w.prototype.getKeys=w.prototype.T;w.prototype.o=function(a,b){var c,d={F:function(){return a},evaluate:function(b){var c=a;return ua(b)?Ia(c,b):b},xa:function(b){return Ja(a,b)},K:function(){return a.K()},kb:function(){c||(c=a.I.create(d));return c}};a.K().Xb();return this.Gd.apply(d,Array.prototype.slice.call(arguments,1))};w.prototype.invoke=w.prototype.o;var Ka=function(){la.call(this)};ia(Ka,la);Ka.prototype.T=function(){return new v(ma(this))};Ka.prototype.getKeys=Ka.prototype.T;/*
 jQuery v1.9.1 (c) 2005, 2012 jQuery Foundation, Inc. jquery.org/license. */
var La=/\[object (Boolean|Number|String|Function|Array|Date|RegExp)\]/,Ma=function(a){if(null==a)return String(a);var b=La.exec(Object.prototype.toString.call(Object(a)));return b?b[1].toLowerCase():"object"},Na=function(a,b){return Object.prototype.hasOwnProperty.call(Object(a),b)},Oa=function(a){if(!a||"object"!=Ma(a)||a.nodeType||a==a.window)return!1;try{if(a.constructor&&!Na(a,"constructor")&&!Na(a.constructor.prototype,"isPrototypeOf"))return!1}catch(c){return!1}for(var b in a);return void 0===
b||Na(a,b)},Pa=function(a,b){var c=b||("array"==Ma(a)?[]:{}),d;for(d in a)if(Na(a,d)){var e=a[d];"array"==Ma(e)?("array"!=Ma(c[d])&&(c[d]=[]),c[d]=Pa(e,c[d])):Oa(e)?(Oa(c[d])||(c[d]={}),c[d]=Pa(e,c[d])):c[d]=e}return c};var Qa=function(a){if(a instanceof v){for(var b=[],c=a.length(),d=0;d<c;d++)a.has(d)&&(b[d]=Qa(a.get(d)));return b}if(a instanceof Ka){for(var e={},g=a.T(),h=g.length(),k=0;k<h;k++)e[g.get(k)]=Qa(a.get(g.get(k)));return e}return a instanceof w?function(){for(var b=Array.prototype.slice.call(arguments,0),c=0;c<b.length;c++)b[c]=Ra(b[c]);var d=new pa(oa(),na());return Qa(a.o.apply(a,[d].concat(b)))}:a},Ra=function(a){if(ua(a)){for(var b=[],c=0;c<a.length;c++)a.hasOwnProperty(c)&&(b[c]=Ra(a[c]));return new v(b)}if(Oa(a)){var d=
new Ka,e;for(e in a)a.hasOwnProperty(e)&&d.set(e,Ra(a[e]));return d}if("function"===typeof a)return new w("",function(b){for(var c=Array.prototype.slice.call(arguments,0),d=0;d<c.length;d++)c[d]=Qa(this.evaluate(c[d]));return Ra(a.apply(a,c))});var g=typeof a;if(null===a||"string"===g||"number"===g||"boolean"===g)return a};var Sa={control:function(a,b){return new f(a,this.evaluate(b))},fn:function(a,b,c){var d=this.F(),e=this.evaluate(b);if(!(e instanceof v))throw"Error: non-List value given for Fn argument names.";var g=Array.prototype.slice.call(arguments,2);this.K().W(a.length+g.length);return new w(a,function(){return function(a){for(var b=new pa(d.N,d.I,d),c=Array.prototype.slice.call(arguments,0),h=0;h<c.length;h++)if(c[h]=this.evaluate(c[h]),c[h]instanceof f)return c[h];for(var n=e.get("length"),p=0;p<n;p++)p<
c.length?b.set(e.get(p),c[p]):b.set(e.get(p),void 0);b.set("arguments",new v(c));var q=Ja(b,g);if(q instanceof f)return"return"===q.C?q.getData():q}}())},list:function(a){var b=this.K();b.W(arguments.length);for(var c=new v,d=0;d<arguments.length;d++){var e=this.evaluate(arguments[d]);"string"===typeof e&&b.W(e.length?e.length-1:0);c.push(e)}return c},map:function(a){for(var b=this.K(),c=new Ka,d=0;d<arguments.length-1;d+=2){var e=this.evaluate(arguments[d])+"",g=this.evaluate(arguments[d+1]),h=e.length;
h+="string"===typeof g?g.length:1;b.W(h);c.set(e,g)}return c},undefined:function(){}};var x=function(){this.N=oa();this.I=na();this.ya=new pa(this.N,this.I)};x.prototype.V=function(a,b){var c=new w(a,b);c.M();this.ya.set(a,c)};x.prototype.addInstruction=x.prototype.V;x.prototype.Vb=function(a,b){Sa.hasOwnProperty(a)&&this.V(b||a,Sa[a])};x.prototype.addNativeInstruction=x.prototype.Vb;x.prototype.K=function(){return this.N};x.prototype.getQuota=x.prototype.K;x.prototype.Wa=function(){this.N=oa();this.ya.N=this.N};x.prototype.resetQuota=x.prototype.Wa;
x.prototype.Ee=function(){this.I=na();this.ya.I=this.I};x.prototype.resetPermissions=x.prototype.Ee;x.prototype.L=function(a,b){var c=Array.prototype.slice.call(arguments,0);return this.yb(c)};x.prototype.execute=x.prototype.L;x.prototype.yb=function(a){for(var b,c=0;c<arguments.length;c++){var d=Ia(this.ya,arguments[c]);b=d instanceof f||d instanceof w||d instanceof v||d instanceof Ka||null===d||void 0===d||"string"===typeof d||"number"===typeof d||"boolean"===typeof d?d:void 0}return b};
x.prototype.run=x.prototype.yb;x.prototype.M=function(){this.ya.M()};x.prototype.makeImmutable=x.prototype.M;var Ta=function(a){for(var b=[],c=0;c<a.length();c++)a.has(c)&&(b[c]=a.get(c));return b};var Ua={Le:"concat every filter forEach hasOwnProperty indexOf join lastIndexOf map pop push reduce reduceRight reverse shift slice some sort splice unshift toString".split(" "),concat:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));for(d=1;d<arguments.length;d++)if(arguments[d]instanceof v)for(var e=arguments[d],g=0;g<e.length();g++)c.push(e.get(g));else c.push(arguments[d]);return new v(c)},every:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&
!b.o(a,this.get(d),d,this))return!1;return!0},filter:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&b.o(a,this.get(e),e,this)&&d.push(this.get(e));return new v(d)},forEach:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)this.has(d)&&b.o(a,this.get(d),d,this)},hasOwnProperty:function(a,b){return this.has(b)},indexOf:function(a,b,c){var d=this.length(),e=void 0===c?0:Number(c);0>e&&(e=Math.max(d+e,0));for(var g=e;g<d;g++)if(this.has(g)&&this.get(g)===
b)return g;return-1},join:function(a,b){for(var c=[],d=0;d<this.length();d++)c.push(this.get(d));return c.join(b)},lastIndexOf:function(a,b,c){var d=this.length(),e=d-1;void 0!==c&&(e=0>c?d+c:Math.min(c,e));for(var g=e;0<=g;g--)if(this.has(g)&&this.get(g)===b)return g;return-1},map:function(a,b){for(var c=this.length(),d=[],e=0;e<this.length()&&e<c;e++)this.has(e)&&(d[e]=b.o(a,this.get(e),e,this));return new v(d)},pop:function(){return this.pop()},push:function(a,b){return this.push.apply(this,Array.prototype.slice.call(arguments,
1))},reduce:function(a,b,c){var d=this.length(),e,g;if(void 0!==c)e=c,g=0;else{if(0==d)throw"TypeError: Reduce on List with no elements.";for(var h=0;h<d;h++)if(this.has(h)){e=this.get(h);g=h+1;break}if(h==d)throw"TypeError: Reduce on List with no elements.";}for(h=g;h<d;h++)this.has(h)&&(e=b.o(a,e,this.get(h),h,this));return e},reduceRight:function(a,b,c){var d=this.length(),e,g;if(void 0!==c)e=c,g=d-1;else{if(0==d)throw"TypeError: ReduceRight on List with no elements.";for(var h=1;h<=d;h++)if(this.has(d-
h)){e=this.get(d-h);g=d-(h+1);break}if(h>d)throw"TypeError: ReduceRight on List with no elements.";}for(h=g;0<=h;h--)this.has(h)&&(e=b.o(a,e,this.get(h),h,this));return e},reverse:function(){for(var a=Ta(this),b=a.length-1,c=0;0<=b;b--,c++)a.hasOwnProperty(b)?this.set(c,a[b]):this.remove(c);return this},shift:function(){return this.shift()},slice:function(a,b,c){var d=this.length();void 0===b&&(b=0);b=0>b?Math.max(d+b,0):Math.min(b,d);c=void 0===c?d:0>c?Math.max(d+c,0):Math.min(c,d);c=Math.max(b,
c);for(var e=[],g=b;g<c;g++)e.push(this.get(g));return new v(e)},some:function(a,b){for(var c=this.length(),d=0;d<this.length()&&d<c;d++)if(this.has(d)&&b.o(a,this.get(d),d,this))return!0;return!1},sort:function(a,b){var c=Ta(this);void 0===b?c.sort():c.sort(function(c,d){return Number(b.o(a,c,d))});for(var d=0;d<c.length;d++)c.hasOwnProperty(d)?this.set(d,c[d]):this.remove(d)},splice:function(a,b,c,d){return this.splice.apply(this,Array.prototype.splice.call(arguments,1,arguments.length-1))},toString:function(){return this.toString()},
unshift:function(a,b){return this.unshift.apply(this,Array.prototype.slice.call(arguments,1))}};var y={oc:{ADD:0,AND:1,APPLY:2,ASSIGN:3,BREAK:4,CASE:5,CONTINUE:6,CONTROL:49,CREATE_ARRAY:7,CREATE_OBJECT:8,DEFAULT:9,DEFN:50,DIVIDE:10,DO:11,EQUALS:12,EXPRESSION_LIST:13,FN:51,FOR:14,FOR_IN:47,GET:15,GET_CONTAINER_VARIABLE:48,GET_INDEX:16,GET_PROPERTY:17,GREATER_THAN:18,GREATER_THAN_EQUALS:19,IDENTITY_EQUALS:20,IDENTITY_NOT_EQUALS:21,IF:22,LESS_THAN:23,LESS_THAN_EQUALS:24,MODULUS:25,MULTIPLY:26,NEGATE:27,NOT:28,NOT_EQUALS:29,NULL:45,OR:30,PLUS_EQUALS:31,POST_DECREMENT:32,POST_INCREMENT:33,PRE_DECREMENT:34,
PRE_INCREMENT:35,QUOTE:46,RETURN:36,SET_PROPERTY:43,SUBTRACT:37,SWITCH:38,TERNARY:39,TYPEOF:40,UNDEFINED:44,VAR:41,WHILE:42}},Va="charAt concat indexOf lastIndexOf match replace search slice split substring toLowerCase toLocaleLowerCase toString toUpperCase toLocaleUpperCase trim".split(" "),Wa=new f("break"),Xa=new f("continue");y.add=function(a,b){return this.evaluate(a)+this.evaluate(b)};y.and=function(a,b){return this.evaluate(a)&&this.evaluate(b)};
y.apply=function(a,b,c){a=this.evaluate(a);b=this.evaluate(b);c=this.evaluate(c);if(!(c instanceof v))throw"Error: Non-List argument given to Apply instruction.";if(null===a||void 0===a)throw"TypeError: Can't read property "+b+" of "+a+".";if("boolean"==typeof a||"number"==typeof a){if("toString"==b)return a.toString();throw"TypeError: "+a+"."+b+" is not a function.";}if("string"==typeof a){if(0<=va(Va,b))return Ra(a[b].apply(a,Ta(c)));throw"TypeError: "+b+" is not a function";}if(a instanceof v){if(a.has(b)){var d=
a.get(b);if(d instanceof w){var e=Ta(c);e.unshift(this.F());return d.o.apply(d,e)}throw"TypeError: "+b+" is not a function";}if(0<=va(Ua.Le,b))return e=Ta(c),e.unshift(this.F()),Ua[b].apply(a,e)}if(a instanceof w||a instanceof Ka){if(a.has(b)){d=a.get(b);if(d instanceof w)return e=Ta(c),e.unshift(this.F()),d.o.apply(d,e);throw"TypeError: "+b+" is not a function";}if("toString"==b)return a instanceof w?a.getName():a.toString();if("hasOwnProperty"==b)return a.has.apply(a,Ta(c))}throw"TypeError: Object has no '"+
b+"' property.";};y.assign=function(a,b){a=this.evaluate(a);if("string"!=typeof a)throw"Invalid key name given for assignment.";var c=this.F();if(!c.has(a))throw"Attempting to assign to undefined value "+b;var d=this.evaluate(b);c.set(a,d);return d};y["break"]=function(){return Wa};y["case"]=function(a){for(var b=this.evaluate(a),c=0;c<b.length;c++){var d=this.evaluate(b[c]);if(d instanceof f)return d}};y["continue"]=function(){return Xa};
y.xd=function(a,b,c){var d=new v;b=this.evaluate(b);for(var e=0;e<b.length;e++)d.push(b[e]);var g=[y.oc.FN,a,d].concat(Array.prototype.splice.call(arguments,2,arguments.length-2));this.F().set(a,this.evaluate(g))};y.Ad=function(a,b){return this.evaluate(a)/this.evaluate(b)};y.Dd=function(a,b){return this.evaluate(a)==this.evaluate(b)};y.Ed=function(a){for(var b,c=0;c<arguments.length;c++)b=this.evaluate(arguments[c]);return b};
y.Hd=function(a,b,c){a=this.evaluate(a);b=this.evaluate(b);c=this.evaluate(c);var d=this.F();if("string"==typeof b)for(var e=0;e<b.length;e++){d.set(a,e);var g=this.xa(c);if(g instanceof f){if("break"==g.C)break;if("return"==g.C)return g}}else if(b instanceof Ka||b instanceof v||b instanceof w){var h=b.T(),k=h.length();for(e=0;e<k;e++)if(d.set(a,h.get(e)),g=this.xa(c),g instanceof f){if("break"==g.C)break;if("return"==g.C)return g}}};y.get=function(a){return this.F().get(this.evaluate(a))};
y.hc=function(a,b){var c;a=this.evaluate(a);b=this.evaluate(b);if(void 0===a||null===a)throw"TypeError: cannot access property of "+a+".";a instanceof Ka||a instanceof v||a instanceof w?c=a.get(b):"string"==typeof a&&("length"==b?c=a.length:ka(b)&&(c=a[b]));return c};y.Ld=function(a,b){return this.evaluate(a)>this.evaluate(b)};y.Md=function(a,b){return this.evaluate(a)>=this.evaluate(b)};y.Td=function(a,b){return this.evaluate(a)===this.evaluate(b)};y.Ud=function(a,b){return this.evaluate(a)!==this.evaluate(b)};
y["if"]=function(a,b,c){var d=[];this.evaluate(a)?d=this.evaluate(b):c&&(d=this.evaluate(c));var e=this.xa(d);if(e instanceof f)return e};y.be=function(a,b){return this.evaluate(a)<this.evaluate(b)};y.ce=function(a,b){return this.evaluate(a)<=this.evaluate(b)};y.ee=function(a,b){return this.evaluate(a)%this.evaluate(b)};y.multiply=function(a,b){return this.evaluate(a)*this.evaluate(b)};y.fe=function(a){return-this.evaluate(a)};y.he=function(a){return!this.evaluate(a)};
y.ie=function(a,b){return this.evaluate(a)!=this.evaluate(b)};y["null"]=function(){return null};y.or=function(a,b){return this.evaluate(a)||this.evaluate(b)};y.Dc=function(a,b){var c=this.evaluate(a);this.evaluate(b);return c};y.Ec=function(a){return this.evaluate(a)};y.quote=function(a){return Array.prototype.slice.apply(arguments)};y["return"]=function(a){return new f("return",this.evaluate(a))};
y.setProperty=function(a,b,c){a=this.evaluate(a);b=this.evaluate(b);c=this.evaluate(c);if(null===a||void 0===a)throw"TypeError: Can't set property "+b+" of "+a+".";(a instanceof w||a instanceof v||a instanceof Ka)&&a.set(b,c);return c};y.Ke=function(a,b){return this.evaluate(a)-this.evaluate(b)};
y["switch"]=function(a,b,c){a=this.evaluate(a);b=this.evaluate(b);c=this.evaluate(c);if(!ua(b)||!ua(c))throw"Error: Malformed switch instruction.";for(var d,e=!1,g=0;g<b.length;g++)if(e||a===this.evaluate(b[g]))if(d=this.evaluate(c[g]),d instanceof f){var h=d.C;if("break"==h)return;if("return"==h||"continue"==h)return d}else e=!0;if(c.length==b.length+1&&(d=this.evaluate(c[c.length-1]),d instanceof f&&("return"==d.C||"continue"==d.C)))return d};
y.Me=function(a,b,c){return this.evaluate(a)?this.evaluate(b):this.evaluate(c)};y["typeof"]=function(a){a=this.evaluate(a);return a instanceof w?"function":typeof a};y.undefined=function(){};y["var"]=function(a){for(var b=this.F(),c=0;c<arguments.length;c++){var d=arguments[c];"string"!=typeof d||b.add(d,void 0)}};
y["while"]=function(a,b,c,d){var e,g=this.evaluate(d);if(this.evaluate(c)&&(e=this.xa(g),e instanceof f)){if("break"==e.C)return;if("return"==e.C)return e}for(;this.evaluate(a);){e=this.xa(g);if(e instanceof f){if("break"==e.C)break;if("return"==e.C)return e}this.evaluate(b)}};var ab=function(){this.nc=!1;this.H=new x;Ya(this);this.nc=!0};ab.prototype.Zd=function(){return this.nc};ab.prototype.isInitialized=ab.prototype.Zd;ab.prototype.L=function(a){this.H.I.xc(String(a[0]))||(this.H.I.reset(),this.H.I.Hc(!0));return this.H.yb(a)};ab.prototype.execute=ab.prototype.L;ab.prototype.M=function(){this.H.M()};ab.prototype.makeImmutable=ab.prototype.M;
var Ya=function(a){function b(a,b){e.H.Vb(a,String(b))}function c(a,b){e.H.V(String(d[a]),b)}var d=y.oc,e=a;b("control",d.CONTROL);b("fn",d.FN);b("list",d.CREATE_ARRAY);b("map",d.CREATE_OBJECT);b("undefined",d.UNDEFINED);c("ADD",y.add);c("AND",y.and);c("APPLY",y.apply);c("ASSIGN",y.assign);c("BREAK",y["break"]);c("CASE",y["case"]);c("CONTINUE",y["continue"]);c("DEFAULT",y["case"]);c("DEFN",y.xd);c("DIVIDE",y.Ad);c("EQUALS",y.Dd);c("EXPRESSION_LIST",y.Ed);c("FOR_IN",y.Hd);c("GET",y.get);c("GET_INDEX",
y.hc);c("GET_PROPERTY",y.hc);c("GREATER_THAN",y.Ld);c("GREATER_THAN_EQUALS",y.Md);c("IDENTITY_EQUALS",y.Td);c("IDENTITY_NOT_EQUALS",y.Ud);c("IF",y["if"]);c("LESS_THAN",y.be);c("LESS_THAN_EQUALS",y.ce);c("MODULUS",y.ee);c("MULTIPLY",y.multiply);c("NEGATE",y.fe);c("NOT",y.he);c("NOT_EQUALS",y.ie);c("NULL",y["null"]);c("OR",y.or);c("POST_DECREMENT",y.Dc);c("POST_INCREMENT",y.Dc);c("PRE_DECREMENT",y.Ec);c("PRE_INCREMENT",y.Ec);c("QUOTE",y.quote);c("RETURN",y["return"]);c("SET_PROPERTY",y.setProperty);
c("SUBTRACT",y.Ke);c("SWITCH",y["switch"]);c("TERNARY",y.Me);c("TYPEOF",y["typeof"]);c("VAR",y["var"]);c("WHILE",y["while"])};ab.prototype.V=function(a,b){this.H.V(a,b)};ab.prototype.addInstruction=ab.prototype.V;ab.prototype.K=function(){return this.H.K()};ab.prototype.getQuota=ab.prototype.K;ab.prototype.Wa=function(){this.H.Wa()};ab.prototype.resetQuota=ab.prototype.Wa;ab.prototype.oa=function(a){this.H.I.oa(a)};ab.prototype.Na=function(a,b,c){this.H.I.Wb(a,b,c)};var bb=function(){this.Sa={}};bb.prototype.get=function(a){return this.Sa.hasOwnProperty(a)?this.Sa[a]:void 0};bb.prototype.add=function(a,b){if(this.Sa.hasOwnProperty(a))throw"Attempting to add a function which already exists: "+a+".";var c=new w(a,function(){for(var a=Array.prototype.slice.call(arguments,0),c=0;c<a.length;c++)a[c]=this.evaluate(a[c]);return b.apply(this,a)});c.M();this.Sa[a]=c};bb.prototype.addAll=function(a){for(var b in a)a.hasOwnProperty(b)&&this.add(b,a[b])};var z=window,B=document,cb=navigator,db=function(a,b){var c=z[a];z[a]=void 0===c?b:c;return z[a]},eb=function(a,b){b&&(a.addEventListener?a.onload=b:a.onreadystatechange=function(){a.readyState in{loaded:1,complete:1}&&(a.onreadystatechange=null,b())})},fb=function(a,b,c){var d=B.createElement("script");d.type="text/javascript";d.async=!0;d.src=a;eb(d,b);c&&(d.onerror=c);ha()&&d.setAttribute("nonce",ha());var e=B.getElementsByTagName("script")[0]||B.body||B.head;e.parentNode.insertBefore(d,e);return d},
gb=function(a,b){var c=B.createElement("iframe");c.height="0";c.width="0";c.style.display="none";c.style.visibility="hidden";var d=B.body&&B.body.lastChild||B.body||B.head;d.parentNode.insertBefore(c,d);eb(c,b);void 0!==a&&(c.src=a);return c},F=function(a,b,c){var d=new Image(1,1);d.onload=function(){d.onload=null;b&&b()};d.onerror=function(){d.onerror=null;c&&c()};d.src=a},hb=function(a,b,c,d){a.addEventListener?a.addEventListener(b,c,!!d):a.attachEvent&&a.attachEvent("on"+b,c)},ib=function(a,b,
c,d){a.removeEventListener?a.removeEventListener(b,c,!!d):a.detachEvent&&a.detachEvent("on"+b,c)},H=function(a){z.setTimeout(a,0)},lb=function(a){var b=B.getElementById(a);if(b&&kb(b,"id")!=a)for(var c=1;c<document.all[a].length;c++)if(kb(document.all[a][c],"id")==a)return document.all[a][c];return b},kb=function(a,b){return a&&b&&a.attributes&&a.attributes[b]?a.attributes[b].value:null},mb=function(a){var b=a.innerText||a.textContent||"";b&&" "!=b&&(b=b.replace(/^[\s\xa0]+|[\s\xa0]+$/g,""));b&&(b=
b.replace(/(\xa0+|\s{2,}|\n|\r\t)/g," "));return b},nb=function(a){var b=B.createElement("div");b.innerHTML="A<div>"+a+"</div>";b=b.lastChild;for(var c=[];b.firstChild;)c.push(b.removeChild(b.firstChild));return c},ob=function(a){cb.sendBeacon&&cb.sendBeacon(a)||F(a)};var pb=/^(?:(?:https?|mailto|ftp):|[^:/?#]*(?:[/?#]|$))/i;var qb=/:[0-9]+$/,rb=function(a,b,c){for(var d=a.split("&"),e=0;e<d.length;e++){var g=d[e].split("=");if(decodeURIComponent(g[0]).replace(/\+/g," ")==b){var h=g.slice(1).join("=");return c?h:decodeURIComponent(h).replace(/\+/g," ")}}},sb=function(a,b,c,d,e){var g,h=function(a){return a?a.replace(":","").toLowerCase():""},k=h(a.protocol)||h(z.location.protocol);b&&(b=String(b).toLowerCase());switch(b){case "protocol":g=k;break;case "host":g=(a.hostname||z.location.hostname).replace(qb,"").toLowerCase();
if(c){var l=/^www\d*\./.exec(g);l&&l[0]&&(g=g.substr(l[0].length))}break;case "port":g=String(Number(a.hostname?a.port:z.location.port)||("http"==k?80:"https"==k?443:""));break;case "path":g="/"==a.pathname.substr(0,1)?a.pathname:"/"+a.pathname;var m=g.split("/");0<=va(d||[],m[m.length-1])&&(m[m.length-1]="");g=m.join("/");break;case "query":g=a.search.replace("?","");e&&(g=rb(g,e));break;case "extension":var n=a.pathname.split(".");g=1<n.length?n[n.length-1]:"";g=g.split("/")[0];break;case "fragment":g=
a.hash.replace("#","");break;default:g=a&&a.href}return g},tb=function(a){var b="";a&&a.href&&(b=a.hash?a.href.replace(a.hash,""):a.href);return b},N=function(a){var b=document.createElement("a");a&&(pb.test(a),b.href=a);var c=b.pathname;"/"!==c[0]&&(c="/"+c);var d=b.hostname.replace(qb,"");return{href:b.href,protocol:b.protocol,host:b.host,hostname:d,pathname:c,search:b.search,hash:b.hash,port:b.port}};var wb=function(){this.Va=new ab;var a=new bb;a.addAll(ub());vb(this,function(b){return a.get(b)})},ub=function(){return{callInWindow:xb,callLater:yb,copyFromWindow:zb,encodeURI:encodeURI,encodeURIComponent:encodeURIComponent,getReferrer:Ab,getUrl:Bb,getUrlComponent:Cb,getUrlFragment:Db,isPlainObject:Eb,loadIframe:Fb,loadJavaScript:Gb,logToConsole:Hb,queryPermission:Ib,removeUrlFragment:Jb,replaceAll:Kb,sendPixel:Lb,setInWindow:Mb}};wb.prototype.L=function(a){return this.Va.L(a)};
wb.prototype.execute=wb.prototype.L;var vb=function(a,b){a.Va.V("require",b)};wb.prototype.oa=function(a){this.Va.oa(a)};wb.prototype.Na=function(a,b,c){this.Va.Na(a,b,c)};function xb(a,b){for(var c=a.split("."),d=z,e=d[c[0]],g=1;e&&g<c.length;g++)d=e,e=e[c[g]];if("function"==Ma(e)){var h=[];for(g=1;g<arguments.length;g++)h.push(Qa(arguments[g]));e.apply(d,h)}}function yb(a){var b=this.F();H(function(){a instanceof w&&a.o(b)})}function Bb(){return z.location.href}
function zb(a,b,c){for(var d=a.split("."),e=z,g=0;g<d.length-1;g++)if(e=e[d[g]],void 0===e||null===e)return;b&&(void 0===e[d[g]]||c&&!e[d[g]])&&(e[d[g]]=Qa(b));return Ra(e[d[g]])}function Ab(){return B.referrer}function Cb(a,b,c,d,e){var g;if(d&&d instanceof v){g=[];for(var h=0;h<d.length();h++){var k=d.get(h);"string"==typeof k&&g.push(k)}}return sb(N(a),b,c,g,e)}function Db(a){return sb(N(a),"fragment")}function Eb(a){return a instanceof Ka}
function Fb(a,b){var c=this.F();gb(a,function(){b instanceof w&&b.o(c)})}var Nb={};
function Gb(a,b,c,d){this.kb().assert("networkAccess",a);var e=this.F(),g=function(){b instanceof w&&b.o(e)},h=function(){c instanceof w&&c.o(e)};d?Nb[d]?(Nb[d].onSuccess.push(g),Nb[d].onFailure.push(h)):(Nb[d]={onSuccess:[g],onFailure:[h]},g=function(){for(var a=Nb[d].onSuccess,b=0;b<a.length;b++)H(a[b]);a.push=function(a){H(a);return 0}},h=function(){for(var a=Nb[d].onFailure,b=0;b<a.length;b++)H(a[b]);Nb[d]=null},fb(a,g,h)):fb(a,g,h)}
function Hb(){for(var a=Array.prototype.slice.call(arguments,0),b=0;b<a.length;b++)a[b]=Qa(a[b]);console.log.apply(console,a)}function Jb(a){return tb(N(a))}function Kb(a,b,c){return a.replace(new RegExp(b,"g"),c)}function Lb(a,b,c){this.kb().assert("sendPixel",a);var d=this.F();F(a,function(){b instanceof w&&b.o(d)},function(){c instanceof w&&c.o(d)})}
function Mb(a,b,c){for(var d=a.split("."),e=z,g=0;g<d.length-1;g++)if(e=e[d[g]],void 0===e)return!1;return void 0===e[d[g]]||c?(e[d[g]]=Qa(b),!0):!1}function Ib(a,b){try{return this.kb().assert.apply(null,Array.prototype.slice.call(arguments,0)),!0}catch(c){return!1}};var kc,lc=[],mc=[],nc=[],oc=[],pc=[],qc={},rc,sc,tc,uc=function(a){var b=a["function"];if(!b)throw"Error: No function name given for function call.";var c=!!qc[b],d={},e;for(e in a)a.hasOwnProperty(e)&&0===e.indexOf("vtp_")&&(d[c?e:e.substr(4)]=a[e]);return c?qc[b](d):kc(b,d)},wc=function(a,b,c,d){c=c||[];d=d||qa;var e={},g;for(g in a)a.hasOwnProperty(g)&&(e[g]=vc(a[g],b,c,d));return e},xc=function(a){var b=a["function"];if(!b)throw"Error: No function name given for function call.";var c=qc[b];return c?
c.b||0:0},vc=function(a,b,c,d){if(ua(a)){var e;switch(a[0]){case "function_id":return a[1];case "list":e=[];for(var g=1;g<a.length;g++)e.push(vc(a[g],b,c,d));return e;case "macro":var h=a[1];if(c[h])return;var k=lc[h];if(!k||b(k))return;c[h]=!0;try{var l=wc(k,b,c,d);e=uc(l);tc&&(e=tc.sd(e,l))}catch(A){d(h,A),e=!1}c[h]=!1;return e;case "map":e={};for(var m=1;m<a.length;m+=2)e[vc(a[m],b,c,d)]=vc(a[m+1],b,c,d);return e;case "template":e=[];for(var n=!1,p=1;p<a.length;p++){var q=vc(a[p],b,c,d);sc&&(n=
n||q===sc.Ia);e.push(q)}return sc&&n?sc.td(e):e.join("");case "escape":e=vc(a[1],b,c,d);if(sc&&ua(a[1])&&"macro"===a[1][0]&&sc.$d(a))return sc.se(e);e=String(e);for(var r=2;r<a.length;r++)Ob[a[r]]&&(e=Ob[a[r]](e));return e;case "tag":var u=a[1];if(!oc[u])throw Error("Unable to resolve tag reference "+u+".");return e={bc:a[2],index:u};case "zb":var t=yc({"function":a[1],arg0:a[2],arg1:a[3],ignore_case:a[5]},b,c,d);a[4]&&(t=!t);return t;default:throw Error("Attempting to expand unknown Value type: "+
a[0]+".");}}return a},yc=function(a,b,c,d){try{return rc(wc(a,b,c,d))}catch(e){JSON.stringify(a)}return null};var Bc=null,Fc=function(a){function b(a){for(var b=0;b<a.length;b++)d[a[b]]=!0}var c=[],d=[];Bc=Cc(a,Dc()||function(){});for(var e=0;e<mc.length;e++){var g=mc[e],h=Ec(g);if(h){for(var k=g.add||[],l=0;l<k.length;l++)c[k[l]]=!0;b(g.block||[])}else null===h&&b(g.block||[])}var m=[];for(e=0;e<oc.length;e++)c[e]&&!d[e]&&(m[e]=!0);return m},Ec=function(a){for(var b=a["if"]||[],c=0;c<b.length;c++){var d=Bc(b[c]);if(!d)return null===d?null:!1}var e=a.unless||[];for(c=0;c<e.length;c++){d=Bc(e[c]);if(null===
d)return null;if(d)return!1}return!0};var Cc=function(a,b){var c=[];return function(d){void 0===c[d]&&(c[d]=yc(nc[d],a,void 0,b));return c[d]}};/*
 Copyright (c) 2014 Derek Brans, MIT license https://github.com/krux/postscribe/blob/master/LICENSE. Portions derived from simplehtmlparser, which is licensed under the Apache License, Version 2.0 */
var Ic={},Jc=null;Ic.s="**********";var Kc=null,Lc=null,Mc="//www.googletagmanager.com/a?id="+Ic.s+"&cv=1",Nc={},Oc={},Pc=B.currentScript?B.currentScript.src:void 0,Qc=function(){var a=Jc.sequence||0;Jc.sequence=a+1;return a};var P=function(){var a=function(a){return{toString:function(){return a}}};return{Kb:a("convert_case_to"),Lb:a("convert_false_to"),Mb:a("convert_null_to"),Nb:a("convert_true_to"),Ob:a("convert_undefined_to"),P:a("function"),Lc:a("instance_name"),Mc:a("live_only"),Nc:a("malware_disabled"),Oc:a("once_per_event"),Qb:a("once_per_load"),Rb:a("setup_tags"),Pc:a("tag_id"),Sb:a("teardown_tags")}}();var Rc=new Da,Sc={},Vc={set:function(a,b){Pa(Tc(a,b),Sc)},get:function(a){return Uc(a,2)},reset:function(){Rc=new Da;Sc={}}},Uc=function(a,b){return 2!=b?Rc.get(a):Wc(a)},Wc=function(a,b,c){var d=a.split(".");var e=function(a,b){for(var c=0;void 0!==a&&c<d.length;c++){if(null===a)return!1;a=a[d[c]]}return void 0!==a||1<c?a:b.length?e(Xc(b.pop()),b):Yc(d)};return e(Sc.eventModel,[b,c]);return Yc(d)},Yc=function(a){for(var b=Sc,c=0;c<a.length;c++){if(null===
b)return!1;if(void 0===b)break;b=b[a[c]]}return b};var Xc=function(a){if(a){var b=Yc(["gtag","targets",a]);return Oa(b)?b:void 0}},Zc=function(a,b){function c(a){if(a)for(var b in a)a.hasOwnProperty(b)&&(d[b]=null)}var d={};c(Sc);delete d.eventModel;c(Xc(a));c(Xc(b));c(Sc.eventModel);var e=[],g;for(g in d)d.hasOwnProperty(g)&&e.push(g);return e};
var $c=function(a,b){Rc.set(a,b);Pa(Tc(a,b),Sc)},Tc=function(a,b){for(var c={},d=c,e=a.split("."),g=0;g<e.length-1;g++)d=d[e[g]]={};d[e[e.length-1]]=b;return c};var ad=new RegExp(/^(.*\.)?(google|youtube|blogger|withgoogle)(\.com?)?(\.[a-z]{2})?\.?$/),bd={customPixels:["nonGooglePixels"],html:["customScripts","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],customScripts:["html","customPixels","nonGooglePixels","nonGoogleScripts","nonGoogleIframes"],nonGooglePixels:[],nonGoogleScripts:["nonGooglePixels"],nonGoogleIframes:["nonGooglePixels"]},cd={customPixels:["customScripts","html"],html:["customScripts"],customScripts:["html"],nonGooglePixels:["customPixels",
"customScripts","html","nonGoogleScripts","nonGoogleIframes"],nonGoogleScripts:["customScripts","html"],nonGoogleIframes:["customScripts","html","nonGoogleScripts"]},dd=function(a,b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]),c.push.apply(c,b[a[d]]||[]);return c};
var ed=function(a){var b=Uc("gtm.whitelist");b=["google","gtagfl","oid","op"];var c=b&&dd(Ba(b),bd),d=Uc("gtm.blacklist")||Uc("tagTypeBlacklist")||[];
ad.test(z.location&&z.location.hostname)&&(d=Ba(d),d.push("nonGooglePixels","nonGoogleScripts"));var e=d&&dd(Ba(d),cd),g={};return function(h){var k=h&&h[P.P];if(!k||"string"!=typeof k)return!0;k=k.replace(/^_*/,"");if(void 0!==g[k])return g[k];var l=Oc[k]||[],m=a(k);if(b){var n;if(n=m)a:{if(0>va(c,k))if(l&&0<l.length)for(var p=0;p<l.length;p++){if(0>va(c,l[p])){n=!1;break a}}else{n=!1;break a}n=!0}m=n}var q=!1;if(d){var r;if(!(r=0<=
va(e,k)))a:{for(var u=l||[],t=new Da,A=0;A<e.length;A++)t.set(e[A],!0);for(var C=0;C<u.length;C++)if(t.get(u[C])){r=!0;break a}r=!1}q=r}return g[k]=!m||q}};var fd={sd:function(a,b){b[P.Kb]&&"string"===typeof a&&(a=1==b[P.Kb]?a.toLowerCase():a.toUpperCase());b.hasOwnProperty(P.Mb)&&null===a&&(a=b[P.Mb]);b.hasOwnProperty(P.Ob)&&void 0===a&&(a=b[P.Ob]);b.hasOwnProperty(P.Nb)&&!0===a&&(a=b[P.Nb]);b.hasOwnProperty(P.Lb)&&!1===a&&(a=b[P.Lb]);return a}};var gd=function(a,b){this.oe=b};ia(gd,Error);gd.prototype.getParameters=function(){return this.oe};var hd=function(a){var b=Jc.zones;!b&&a&&(b=Jc.zones=a());return b},id={active:!0,isWhitelisted:function(){return!0}};var jd=!1,kd=0,ld=[];function md(a){if(!jd){var b=B.createEventObject,c="complete"==B.readyState,d="interactive"==B.readyState;if(!a||"readystatechange"!=a.type||c||!b&&d){jd=!0;for(var e=0;e<ld.length;e++)H(ld[e])}ld.push=function(){for(var a=0;a<arguments.length;a++)H(arguments[a]);return 0}}}function nd(){if(!jd&&140>kd){kd++;try{B.documentElement.doScroll("left"),md()}catch(a){z.setTimeout(nd,50)}}}var od=function(a){jd?a():ld.push(a)};var pd=!1,rd=function(){return z.GoogleAnalyticsObject&&z[z.GoogleAnalyticsObject]};
var sd=function(){function a(a){return!ta(a)||0>a?0:a}if(z.performance&&z.performance.timing){var b=z.performance.timing.navigationStart,c=ta(Vc.get("gtm.start"))?Vc.get("gtm.start"):0;Jc._li={cst:a(c-b),cbt:a(Kc-b)}}},td=function(a){z.GoogleAnalyticsObject||(z.GoogleAnalyticsObject=a||"ga");var b=z.GoogleAnalyticsObject;if(!z[b]){var c=function(){c.q=c.q||[];c.q.push(arguments)};c.l=Number(Ca());z[b]=c}sd();return z[b]},ud=function(a,b,c,d){b=String(b).replace(/\s+/g,"").split(",");var e=rd();e(a+
"require","linker");e(a+"linker:autoLink",b,c,d)};
var yd=function(){return"&tc="+oc.filter(function(a){return a}).length},zd="0.005000">Math.random(),Ad=function(){var a=0,b=0;return{ae:function(){if(2>a)return!1;1E3<=Ca().getTime()-b&&(a=0);return 2<=a},ze:function(){1E3<=Ca().getTime()-b&&(a=0);a++;b=Ca().getTime()}}},Bd="",Cd=function(){Bd=[Mc,"&v=3&t=t","&pid="+xa(),"&rv=a1"].join("")},Dd={},Ed="",Fd=void 0,Gd={},Hd={},Id=void 0,Jd=null,Kd=1E3,Ld=function(){var a=Fd;return void 0===a?"":[Bd,Dd[a]?"":"&es=1",
Gd[a],yd(),Ed,"&z=0"].join("")},Md=function(){Id&&(z.clearTimeout(Id),Id=void 0);void 0===Fd||Dd[Fd]&&!Ed||(Hd[Fd]||Jd.ae()||0>=Kd--?Hd[Fd]=!0:(Jd.ze(),F(Ld()),Dd[Fd]=!0,Ed=""))},Nd=function(a,b,c){if(zd&&!Hd[a]&&b){a!==Fd&&(Md(),Fd=a);var d=c+String(b[P.P]||"").replace(/_/g,"");Ed=Ed?Ed+"."+d:"&tr="+d;Id||(Id=z.setTimeout(Md,500));2022<=Ld().length&&Md()}};function Od(a,b,c,d,e,g){var h=oc[a],k=Pd(a,b,c,d,e,g);if(!k)return null;var l=vc(h[P.Rb],g.Y,[],Qd());if(l&&l.length){var m=l[0];k=Od(m.index,b,k,1===m.bc?e:k,e,g)}return k}
function Pd(a,b,c,d,e,g){function h(){var b=wc(k,g.Y,[],l);b.vtp_gtmOnSuccess=function(){Nd(g.id,oc[a],"5");c()};b.vtp_gtmOnFailure=function(){Nd(g.id,oc[a],"6");d()};b.vtp_gtmTagId=k.tag_id;if(k[P.Nc])d();else{Nd(g.id,k,"1");try{uc(b)}catch(C){Nd(g.id,
k,"7");e()}}}var k=oc[a];if(g.Y(k))return null;var l=Qd(),m=vc(k[P.Sb],g.Y,[],l);if(m&&m.length){var n=m[0],p=Od(n.index,b,c,d,e,g);if(!p)return null;c=p;d=2===n.bc?e:p}if(k[P.Qb]||k[P.Oc]){var q=k[P.Qb]?pc:b,r=c,u=d;if(!q[a]){h=Fa(h);var t=Rd(a,q,h);c=t.U;d=t.la}return function(){q[a](r,u)}}return h}
function Rd(a,b,c){var d=[],e=[];b[a]=Sd(d,e,c);return{U:function(){b[a]=Td;for(var c=0;c<d.length;c++)d[c]()},la:function(){b[a]=Ud;for(var c=0;c<e.length;c++)e[c]()}}}function Sd(a,b,c){return function(d,e){a.push(d);b.push(e);c()}}function Td(a){a()}function Ud(a,b){b()}function Qd(){return function(){}};function Vd(a){var b=0,c=0,d=!1;return{add:function(){c++;return Fa(function(){b++;d&&b>=c&&a()})},$c:function(){d=!0;b>=c&&a()}}}function Wd(a,b){var c,d=b.b,e=a.b;c=d>e?1:d<e?-1:0;var g;if(0!==c)g=c;else{var h=a.Jc,k=b.Jc;g=h>k?1:h<k?-1:0}return g}
function Xd(a,b){if(!zd)return;var c=function(a){var d=b.Y(oc[a])?"3":"4",g=vc(oc[a][P.Rb],b.Y,[],qa);g&&g.length&&c(g[0].index);Nd(b.id,oc[a],d);var h=vc(oc[a][P.Sb],b.Y,[],qa);h&&h.length&&c(h[0].index)};c(a);}var Yd=!1;function Dc(){return function(){}};var Zd=function(a,b){var c={};c[P.P]="__"+a;for(var d in b)b.hasOwnProperty(d)&&(c["vtp_"+d]=b[d]);for(d in void 0)(void 0).hasOwnProperty(d)&&(c[d]=(void 0)[d]);oc.push(c);return oc.length-1};var $d="allow_ad_personalization_signals cookie_domain cookie_expires cookie_name cookie_path custom_params event_callback event_timeout groups send_to send_page_view session_duration user_properties".split(" ");var ae=/[A-Z]+/,be=/\s/,ce=function(a){if(sa(a)&&(a=a.trim(),!be.test(a))){var b=a.indexOf("-");if(!(0>b)){var c=a.substring(0,b);if(ae.test(c)){for(var d=a.substring(b+1).split("/"),e=0;e<d.length;e++)if(!d[e])return;return{id:a,prefix:c,containerId:c+"-"+d[0],X:d}}}}};var de=null,ee={},fe={},ge;function he(){de=de||!Jc.gtagRegistered;Jc.gtagRegistered=!0;return de}var ie=function(a,b){var c={event:a};b&&(c.eventModel=Pa(b),b.event_callback&&(c.eventCallback=b.event_callback),b.event_timeout&&(c.eventTimeout=b.event_timeout));return c};
function je(a){if(void 0===fe[a.id]){var b;if("UA"==a.prefix)b=Zd("gtagua",{trackingId:a.id});else if("AW"==a.prefix)b=Zd("gtagaw",{conversionId:a});else if("DC"==a.prefix)b=Zd("gtagfl",{targetId:a.id});else if("GF"==a.prefix)b=Zd("gtaggf",{conversionId:a});else if("G"==a.prefix)b=Zd("get",{trackingId:a.id,isAutoTag:!0});else if("HA"==a.prefix)b=Zd("gtagha",{conversionId:a});else return;if(!ge){var c={name:"send_to",dataLayerVersion:2},d={};d[P.P]="__v";for(var e in c)c.hasOwnProperty(e)&&(d["vtp_"+
e]=c[e]);lc.push(d);ge=["macro",lc.length-1]}var g={arg0:ge,arg1:a.id,ignore_case:!1};g[P.P]="_lc";nc.push(g);var h={"if":[nc.length-1],add:[b]};h["if"]&&(h.add||h.block)&&mc.push(h);fe[a.id]=b}}
var le={event:function(a){var b=a[1];if(sa(b)&&!(3<a.length)){var c;if(2<a.length){if(!Oa(a[2]))return;c=a[2]}var d=ie(b,c);var e;var g=c,h=Uc("gtag.fields.send_to",2);sa(h)||(h="send_to");var k=g&&g[h];void 0===k&&(k=Uc(h,2),void 0===k&&(k="default"));if(sa(k)||ua(k)){for(var l,m=k.toString().replace(/\s+/g,"").split(","),n=[],p=0;p<m.length;p++)0<=m[p].indexOf("-")?n.push(m[p]):n=n.concat(ee[m[p]]||[]);l=n;for(var q={},r=0;r<l.length;++r){var u=ce(l[r]);u&&(q[u.id]=
u)}var t=[],A;for(A in q)if(q.hasOwnProperty(A)){var C=q[A];"AW"===C.prefix&&C.X[1]&&t.push(C.containerId)}for(var D=0;D<t.length;++D)delete q[t[D]];var L=[],E;for(E in q)q.hasOwnProperty(E)&&L.push(q[E]);e=L}else e=void 0;if(!e)return;var G=he();G||ke();for(var J=[],I=0;G&&I<e.length;I++){var K=e[I];J.push(K.id);je(K)}d.eventModel=d.eventModel||{};0<e.length?d.eventModel.send_to=J.join():delete d.eventModel.send_to;return d}},set:function(a){var b;2==a.length&&Oa(a[1])?
b=Pa(a[1]):3==a.length&&sa(a[1])&&(b={},b[a[1]]=a[2]);if(b)return b.eventModel=Pa(b),b.event="gtag.set",b._clear=!0,b},js:function(a){if(2==a.length&&a[1].getTime)return{event:"gtm.js","gtm.start":a[1].getTime()}},config:function(a){var b=a[2]||{};if(2>a.length||!sa(a[1])||!Oa(b))return;var c=ce(a[1]);if(!c)return;he()?je(c):ke();var d=c.id,e;for(e in ee)if(ee.hasOwnProperty(e)){var g=va(ee[e],d);0<=g&&ee[e].splice(g,1)}var h=c.id,k=b.groups||"default";k=k.toString().split(",");
for(var l=0;l<k.length;l++)ee[k[l]]=ee[k[l]]||[],ee[k[l]].push(h);delete b.groups;$c("gtag.targets."+c.id,void 0);$c("gtag.targets."+c.id,Pa(b));var m={};m.send_to=c.id;return ie("gtag.config",m);}},ke=Fa(function(){});var me=!1,ne=[];function oe(){if(!me){me=!0;for(var a=0;a<ne.length;a++)H(ne[a])}};var pe=[],qe=!1,re=function(a){var b=a.eventCallback,c=Fa(function(){ra(b)&&H(function(){b(Ic.s)})}),d=a.eventTimeout;d&&z.setTimeout(c,Number(d));return c},se=function(){for(var a=!1;!qe&&0<pe.length;){qe=!0;delete Sc.eventModel;var b=pe.shift();if(ra(b))try{b.call(Vc)}catch(De){}else if(ua(b)){var c=b;if(sa(c[0])){var d=c[0].split("."),e=d.pop(),g=c.slice(1),h=Uc(d.join("."),2);if(void 0!==h&&null!==h)try{h[e].apply(h,g)}catch(De){}}}else{var k=b;if(k&&("[object Arguments]"==Object.prototype.toString.call(k)||
Object.prototype.hasOwnProperty.call(k,"callee"))){a:{if(b.length&&sa(b[0])){var l=le[b[0]];if(l){b=l(b);break a}}b=void 0}if(!b){qe=!1;continue}}var m;var n=void 0,p=b,q=p._clear;for(n in p)p.hasOwnProperty(n)&&"_clear"!==n&&(q&&$c(n,void 0),$c(n,p[n]));var r=p.event;if(r){var u=p["gtm.uniqueEventId"];u||(u=Qc(),p["gtm.uniqueEventId"]=u,$c("gtm.uniqueEventId",u));Lc=r;var t;var A,C,D=p,L=D.event,E=D["gtm.uniqueEventId"],G=Jc.zones;C=G?G.checkState(Ic.s,E):id;if(C.active){var J=re(D);c:{var I=C.isWhitelisted;
if("gtm.js"==L){if(Yd){A=!1;break c}Yd=!0}var K=E,R=L;if(zd&&!Hd[K]&&Fd!==K){Md();Fd=K;Ed="";var ja=Gd,W=K,aa,M=R;aa=0===M.indexOf("gtm.")?encodeURIComponent(M):"*";ja[W]="&e="+aa+"&eid="+K;Id||(Id=z.setTimeout(Md,500))}var S=ed(I),O={id:E,name:L,callback:J||qa,Y:S,Da:[]};O.Da=Fc(S);for(var za,Za=O,Ub=Vd(Za.callback),zc=[],jb=[],$a=0;$a<oc.length;$a++)if(Za.Da[$a]){var Ee=oc[$a];var Vb=Ub.add();try{var Fe=Od($a,zc,Vb,Vb,Vb,Za);Fe?jb.push({Jc:$a,b:xc(Ee),L:Fe}):(Xd($a,Za),Vb())}catch(De){Vb()}}Ub.$c();jb.sort(Wd);for(var qd=0;qd<jb.length;qd++)jb[qd].L();za=0<jb.length;if("gtm.js"===L||"gtm.sync"===L)d:{}if(za){for(var Lg={__cl:!0,__evl:!0,__fsl:!0,__hl:!0,__jel:!0,__lcl:!0,__sdl:!0,__tl:!0,__ytl:!0},Ac=0;Ac<O.Da.length;Ac++)if(O.Da[Ac]){var He=oc[Ac];if(He&&!Lg[He[P.P]]){A=!0;break c}}A=!1}else A=za}t=A?!0:!1}else t=!1;Lc=null;m=t}else m=!1;a=m||a}qe=!1}return!a},te=function(){var a=se();try{var b=z["dataLayer"].hide;if(b&&void 0!==b[Ic.s]&&b.end){b[Ic.s]=!1;var c=!0,d;for(d in b)if(b.hasOwnProperty(d)&&
!0===b[d]){c=!1;break}c&&(b.end(),b.end=null)}}catch(e){}return a},ue=function(){var a=db("dataLayer",[]),b=db("google_tag_manager",{});b=b["dataLayer"]=b["dataLayer"]||{};ld.push(function(){b.gtmDom||(b.gtmDom=!0,a.push({event:"gtm.dom"}))});ne.push(function(){b.gtmLoad||(b.gtmLoad=!0,a.push({event:"gtm.load"}))});var c=a.push;a.push=function(){var b=[].slice.call(arguments,0);c.apply(a,b);for(pe.push.apply(pe,b);300<this.length;)this.shift();return se()};pe.push.apply(pe,a.slice(0));
H(te)};var ve={};ve.Ia=new String("undefined");ve.ab={};var we=function(a){this.resolve=function(b){for(var c=[],d=0;d<a.length;d++)c.push(a[d]===ve.Ia?b:a[d]);return c.join("")}};we.prototype.toString=function(){return this.resolve("undefined")};we.prototype.valueOf=we.prototype.toString;ve.td=function(a){return new we(a)};var xe={};ve.Ae=function(a,b){var c=Qc();xe[c]=[a,b];return c};ve.Yb=function(a){var b=a?0:1;return function(a){var c=xe[a];if(c&&"function"===typeof c[b])c[b]();xe[a]=void 0}};
ve.$d=function(a){for(var b=!1,c=!1,d=2;d<a.length;d++)b=b||8===a[d],c=c||16===a[d];return b&&c};ve.se=function(a){if(a===ve.Ia)return a;var b=Qc();ve.ab[b]=a;return'google_tag_manager["'+Ic.s+'"].macro('+b+")"};ve.Qc=we;var ye=new Da,ze=function(a,b){function c(a){var b=N(a),c=sb(b,"protocol"),d=sb(b,"host",!0),e=sb(b,"port"),g=sb(b,"path").toLowerCase().replace(/\/$/,"");if(void 0===c||"http"==c&&"80"==e||"https"==c&&"443"==e)c="web",e="default";return[c,d,e,g]}for(var d=c(String(a)),e=c(String(b)),g=0;g<d.length;g++)if(d[g]!==e[g])return!1;return!0};
function Ae(a){var b=a.arg0,c=a.arg1;switch(a["function"]){case "_cn":return 0<=String(b).indexOf(String(c));case "_css":var d;a:{if(b){var e=["matches","webkitMatchesSelector","mozMatchesSelector","msMatchesSelector","oMatchesSelector"];try{for(var g=0;g<e.length;g++)if(b[e[g]]){d=b[e[g]](c);break a}}catch(u){}}d=!1}return d;case "_ew":var h,k;h=String(b);k=String(c);var l=h.length-k.length;return 0<=l&&h.indexOf(k,l)==l;case "_eq":return String(b)==String(c);case "_ge":return Number(b)>=Number(c);
case "_gt":return Number(b)>Number(c);case "_lc":var m;m=String(b).split(",");return 0<=va(m,String(c));case "_le":return Number(b)<=Number(c);case "_lt":return Number(b)<Number(c);case "_re":var n;var p=a.ignore_case?"i":void 0;try{var q=String(c)+p,r=ye.get(q);r||(r=new RegExp(c,p),ye.set(q,r));n=r.test(b)}catch(u){n=!1}return n;case "_sw":return 0==String(b).indexOf(String(c));case "_um":return ze(b,c)}return!1};var Be=function(){return!1};function Ce(a,b,c,d){return(d||"https:"==z.location.protocol?a:b)+c}function Ie(a,b){for(var c=b||(a instanceof v?new v:new Ka),d=a.T(),e=0;e<d.length();e++){var g=d.get(e);if(a.has(g)){var h=a.get(g);h instanceof v?(c.get(g)instanceof v||c.set(g,new v),Ie(h,c.get(g))):h instanceof Ka?(c.get(g)instanceof Ka||c.set(g,new Ka),Ie(h,c.get(g))):c.set(g,h)}}return c}function Je(){return Ic.s}function Ke(){return(new Date).getTime()}function Le(a,b){return Ra(Uc(a,b||2))}function Me(){return Lc}
function Ne(a){return nb('<a href="'+a+'"></a>')[0].href}function Oe(a){return ya(Qa(a))}function Pe(a){return null===a?"null":void 0===a?"undefined":a.toString()}function Qe(a,b){return xa(a,b)}function Re(a,b,c){if(!(a instanceof v))return null;for(var d=new Ka,e=!1,g=0;g<a.length();g++){var h=a.get(g);h instanceof Ka&&h.has(b)&&h.has(c)&&(d.set(h.get(b),h.get(c)),e=!0)}return e?d:null}
var Se=function(){var a=new bb,b=ub();Be()&&(b.loadJavaScript=qa,b.loadIframe=qa);a.addAll(b);a.addAll({buildSafeUrl:Ce,copy:Ie,copyFromDataLayer:Le,decodeHtmlUrl:Ne,generateRandom:Qe,generateUniqueNumber:Qc,getContainerId:Je,getCurrentTime:Ke,getEventName:Me,makeInteger:Oe,makeString:Pe,tableToMap:Re});return function(b){return a.get(b)}},Ue=function(){var a={networkAccess:Te};return function(b,c,d){return a[b]?a[b](c,d):qa}};
function Te(a,b){var c=a.url_list||[];return function(a,e){if(c.length){for(var d=0;d<c.length;d++)if(c[d]===e)return;throw b(a,{URL:e});}}};var Ve,Xe=function(){var a=data.runtime||[],b=data.permissions||{};Ve=new wb;kc=function(a,b){var c=new Ka,d;for(d in b)b.hasOwnProperty(d)&&c.set(d,Ra(b[d]));var e=Ve.L([a,c]);e instanceof f&&"return"===e.C&&(e=e.getData());return Qa(e)};rc=Ae;vb(Ve,Se());for(var c=0;c<a.length;c++){var d=a[c];if(!ua(d)||3>d.length){if(0==d.length)continue;return}Ve.L(d)}var e=function(a){throw We(a,{},"The requested permission is not configured.");};Ve.oa(e);var g=Ue(),h;for(h in b)if(b.hasOwnProperty(h)){var k=
b[h],l=!1,m;for(m in k)if(k.hasOwnProperty(m)){l=!0;var n=g(m,k[m],We);Ve.Na(h,m,n)}l||Ve.Na(h,"default",e)}};function We(a,b,c){return new gd(a,b,c)};var Ye=function(a,b){var c=function(){};c.prototype=a.prototype;var d=new c;a.apply(d,Array.prototype.slice.call(arguments,1));return d};var Ze=function(a){return encodeURIComponent(a)},$e=function(a,b){if(!a)return!1;var c=sb(N(a),"host");if(!c)return!1;for(var d=0;b&&d<b.length;d++){var e=b[d]&&b[d].toLowerCase();if(e){var g=c.length-e.length;0<g&&"."!=e.charAt(0)&&(g--,e="."+e);if(0<=g&&c.indexOf(e,g)==g)return!0}}return!1};
var Q=function(a,b,c){for(var d={},e=!1,g=0;a&&g<a.length;g++)a[g]&&a[g].hasOwnProperty(b)&&a[g].hasOwnProperty(c)&&(d[a[g][b]]=a[g][c],e=!0);return e?d:null},af=function(a,b){Pa(a,b)},bf=function(a){return ya(a)},cf=function(a,b){return va(a,b)};var df=function(a){var b={"gtm.element":a,"gtm.elementClasses":a.className,"gtm.elementId":a["for"]||kb(a,"id")||"","gtm.elementTarget":a.formTarget||a.target||""};b["gtm.elementUrl"]=(a.attributes&&a.attributes.formaction?a.formAction:"")||a.action||a.href||a.src||a.code||a.codebase||"";return b},ef=function(a){Jc.hasOwnProperty("autoEventsSettings")||(Jc.autoEventsSettings={});var b=Jc.autoEventsSettings;b.hasOwnProperty(a)||(b[a]={});return b[a]},ff=function(a,b,c,d){var e=ef(a),g=Ea(e,b,d);e[b]=
c(g)},gf=function(a,b,c){var d=ef(a);return Ea(d,b,c)};var hf=!1;if(B.querySelectorAll)try{var jf=B.querySelectorAll(":root");jf&&1==jf.length&&jf[0]==B.documentElement&&(hf=!0)}catch(a){}var kf=hf;var lf=function(a,b,c){for(var d=[],e=String(b||document.cookie).split(";"),g=0;g<e.length;g++){var h=e[g].split("="),k=h[0].replace(/^\s*|\s*$/g,"");if(k&&k==a){var l=h.slice(1).join("=").replace(/^\s*|\s*$/g,"");l&&!0===c&&(l=decodeURIComponent(l));d.push(l)}}return d},of=function(a,b,c,d){var e=mf(a,d);if(1===e.length)return e[0].id;if(0!==e.length){e=nf(e,function(a){return a.Bd},b);if(1===e.length)return e[0].id;e=nf(e,function(a){return a.pe},c);return e[0]?e[0].id:void 0}},rf=function(a,b,
c,d,e,g){c=c||"/";var h=d=d||"auto",k=c;if(pf.test(document.location.hostname)||"/"===k&&qf.test(h))return!1;g&&(b=encodeURIComponent(b));var l=b;l&&1200<l.length&&(l=l.substring(0,1200));b=l;var m=a+"="+b+"; path="+c+"; ";void 0!==e&&(m+="expires="+e.toGMTString()+"; ");if("auto"===d){var n=!1,p;a:{var q=[],r=document.location.hostname.split(".");if(4===r.length){var u=r[r.length-1];if(parseInt(u,10).toString()===u){p=["none"];break a}}for(var t=r.length-2;0<=t;t--)q.push(r.slice(t).join("."));q.push("none");
p=q}for(var A=p,C=0;C<A.length&&!n;C++)n=rf(a,b,c,A[C],e);return n}d&&"none"!==d&&(m+="domain="+d+";");var D=document.cookie;document.cookie=m;return D!=document.cookie||0<=lf(a).indexOf(b)};function nf(a,b,c){for(var d=[],e=[],g,h=0;h<a.length;h++){var k=a[h],l=b(k);l===c?d.push(k):void 0===g||l<g?(e=[k],g=l):l===g&&e.push(k)}return 0<d.length?d:e}
function mf(a,b){for(var c=[],d=lf(a),e=0;e<d.length;e++){var g=d[e].split("."),h=g.shift();if(!b||-1!==b.indexOf(h)){var k=g.shift();k&&(k=k.split("-"),c.push({id:g.join("."),Bd:1*k[0]||1,pe:1*k[1]||1}))}}return c}var qf=/^(www\.)?google(\.com?)?(\.[a-z]{2})?$/,pf=/(^|\.)doubleclick\.net$/i;var sf=window,tf=document;var uf=function(){for(var a=sf.navigator.userAgent+(tf.cookie||"")+(tf.referrer||""),b=a.length,c=sf.history.length;0<c;)a+=c--^b++;var d=1,e,g,h;if(a)for(d=0,g=a.length-1;0<=g;g--)h=a.charCodeAt(g),d=(d<<6&268435455)+h+(h<<14),e=d&266338304,d=0!=e?d^e>>21:d;return[Math.round(2147483647*Math.random())^d&2147483647,Math.round(Ca().getTime()/1E3)].join(".")},xf=function(a,b,c,d){var e=vf(b);return of(a,e,wf(c),d)};
function vf(a){if(!a)return 1;a=0===a.indexOf(".")?a.substr(1):a;return a.split(".").length}function wf(a){if(!a||"/"===a)return 1;"/"!==a[0]&&(a="/"+a);"/"!==a[a.length-1]&&(a+="/");return a.split("/").length-1}function yf(a,b){var c=""+vf(a),d=wf(b);1<d&&(c+="-"+d);return c};var zf=["1"],Af={},Ef=function(a,b,c){var d=Bf(a);Af[d]||Cf(d,b,c)||(Df(d,uf(),b,c),Cf(d,b,c))};function Df(a,b,c,d){var e;e=["1",yf(c,d),b].join(".");rf(a,e,d,c,new Date(Ca().getTime()+7776E6))}function Cf(a,b,c){var d=xf(a,b,c,zf);d&&(Af[a]=d);return d}function Bf(a){return(a||"_gcl")+"_au"};function Ff(){for(var a=Gf,b={},c=0;c<a.length;++c)b[a[c]]=c;return b}function Hf(){var a="ABCDEFGHIJKLMNOPQRSTUVWXYZ";a+=a.toLowerCase()+"0123456789-_";return a+"."}
var Gf,If,Jf=function(a){Gf=Gf||Hf();If=If||Ff();for(var b=[],c=0;c<a.length;c+=3){var d=c+1<a.length,e=c+2<a.length,g=a.charCodeAt(c),h=d?a.charCodeAt(c+1):0,k=e?a.charCodeAt(c+2):0,l=g>>2,m=(g&3)<<4|h>>4,n=(h&15)<<2|k>>6,p=k&63;e||(p=64,d||(n=64));b.push(Gf[l],Gf[m],Gf[n],Gf[p])}return b.join("")},Kf=function(a){function b(b){for(;d<a.length;){var c=a.charAt(d++),e=If[c];if(null!=e)return e;if(!/^[\s\xa0]*$/.test(c))throw Error("Unknown base64 encoding at char: "+c);}return b}Gf=Gf||Hf();If=If||
Ff();for(var c="",d=0;;){var e=b(-1),g=b(0),h=b(64),k=b(64);if(64===k&&-1===e)return c;c+=String.fromCharCode(e<<2|g>>4);64!=h&&(c+=String.fromCharCode(g<<4&240|h>>2),64!=k&&(c+=String.fromCharCode(h<<6&192|k)))}};var Lf;function Mf(a,b){if(!a||b===B.location.hostname)return!1;for(var c=0;c<a.length;c++)if(a[c]instanceof RegExp){if(a[c].test(b))return!0}else if(0<=b.indexOf(a[c]))return!0;return!1}var Nf=function(){var a=db("google_tag_data",{}),b=a.gl;b&&b.decorators||(b={decorators:[]},a.gl=b);return b};var Of=/(.*?)\*(.*?)\*(.*)/,Pf=/([^?#]+)(\?[^#]*)?(#.*)?/,Qf=/(.*?)(^|&)_gl=([^&]*)&?(.*)/,Sf=function(a){var b=[],c;for(c in a)if(a.hasOwnProperty(c)){var d=a[c];void 0!==d&&d===d&&null!==d&&"[object Object]"!==d.toString()&&(b.push(c),b.push(Jf(String(d))))}var e=b.join("*");return["1",Rf(e),e].join("*")},Rf=function(a,b){var c=[window.navigator.userAgent,(new Date).getTimezoneOffset(),window.navigator.userLanguage||window.navigator.language,Math.floor((new Date).getTime()/60/1E3)-(void 0===b?0:
b),a].join("*"),d;if(!(d=Lf)){for(var e=Array(256),g=0;256>g;g++){for(var h=g,k=0;8>k;k++)h=h&1?h>>>1^3988292384:h>>>1;e[g]=h}d=e}Lf=d;for(var l=4294967295,m=0;m<c.length;m++)l=l>>>8^Lf[(l^c.charCodeAt(m))&255];return((l^-1)>>>0).toString(36)},Uf=function(){return function(a){var b=N(z.location.href),c=b.search.replace("?",""),d=rb(c,"_gl",!0)||"";a.query=Tf(d)||{};var e=sb(b,"fragment").match(Qf);a.fragment=Tf(e&&e[3]||"")||{}}},Tf=function(a){var b;b=void 0===b?3:b;try{if(a){var c=Of.exec(a);if(c&&
"1"===c[1]){var d=c[3],e;a:{for(var g=c[2],h=0;h<b;++h)if(g===Rf(d,h)){e=!0;break a}e=!1}if(e){for(var k={},l=d?d.split("*"):[],m=0;m<l.length;m+=2)k[l[m]]=Kf(l[m+1]);return k}}}}catch(n){}};
function Vf(a,b,c){function d(a){var b=a,c=Qf.exec(b),d=b;if(c){var e=c[2],g=c[4];d=c[1];g&&(d=d+e+g)}a=d;var h=a.charAt(a.length-1);a&&"&"!==h&&(a+="&");return a+l}c=void 0===c?!1:c;var e=Pf.exec(b);if(!e)return"";var g=e[1],h=e[2]||"",k=e[3]||"",l="_gl="+a;c?k="#"+d(k.substring(1)):h="?"+d(h.substring(1));return""+g+h+k}
function Wf(a,b,c){for(var d={},e={},g=Nf().decorators,h=0;h<g.length;++h){var k=g[h];(!c||k.forms)&&Mf(k.domains,b)&&(k.fragment?Ga(e,k.callback()):Ga(d,k.callback()))}if(Ha(d)){var l=Sf(d);if(c){if(a&&a.action){var m=(a.method||"").toLowerCase();if("get"===m){for(var n=a.childNodes||[],p=!1,q=0;q<n.length;q++){var r=n[q];if("_gl"===r.name){r.setAttribute("value",l);p=!0;break}}if(!p){var u=B.createElement("input");u.setAttribute("type","hidden");u.setAttribute("name","_gl");u.setAttribute("value",
l);a.appendChild(u)}}else if("post"===m){var t=Vf(l,a.action);pb.test(t)&&(a.action=t)}}}else Xf(l,a,!1)}if(!c&&Ha(e)){var A=Sf(e);Xf(A,a,!0)}}function Xf(a,b,c){if(b.href){var d=Vf(a,b.href,void 0===c?!1:c);pb.test(d)&&(b.href=d)}}
var Yf=function(a){try{var b;a:{for(var c=a.target||a.srcElement||{},d=100;c&&0<d;){if(c.href&&c.nodeName.match(/^a(?:rea)?$/i)){b=c;break a}c=c.parentNode;d--}b=null}var e=b;if(e){var g=e.protocol;"http:"!==g&&"https:"!==g||Wf(e,e.hostname,!1)}}catch(h){}},Zf=function(a){try{var b=a.target||a.srcElement||{};if(b.action){var c=sb(N(b.action),"host");Wf(b,c,!0)}}catch(d){}},$f=function(a,b,c,d){var e=Nf();e.init||(hb(B,"mousedown",Yf),hb(B,"keyup",Yf),hb(B,"submit",Zf),e.init=!0);var g={callback:a,
domains:b,fragment:"fragment"===c,forms:!!d};Nf().decorators.push(g)};var ag=/^\w+$/,bg=/^[\w-]+$/,cg=/^~?[\w-]+$/,dg={aw:"_aw",dc:"_dc",gf:"_gf",ha:"_ha"},fg=function(a){var b=lf(a,B.cookie),c=[];if(!b||0==b.length)return c;for(var d=0;d<b.length;d++){var e=b[d].split(".");3==e.length&&"GCL"==e[0]&&e[1]&&c.push(e[2])}return eg(c)};function gg(a){return a&&"string"==typeof a&&a.match(ag)?a:"_gcl"}
var hg=function(a){if(a){if("string"==typeof a){var b=gg(a);return{dc:b,aw:b,gf:b,ha:b}}if(a&&"object"==typeof a)return{dc:gg(a.dc),aw:gg(a.aw),gf:gg(a.gf),ha:gg(a.ha)}}return{dc:"_gcl",aw:"_gcl",gf:"_gcl",ha:"_gcl"}},ig=function(){var a=N(z.location.href),b={},c=function(a,c){b[c]||(b[c]=[]);b[c].push(a)},d=sb(a,"query",!1,void 0,"gclid"),e=sb(a,"query",!1,void 0,"gclsrc");if(!d||!e){var g=a.hash.replace("#","");d=d||rb(g,"gclid");e=e||rb(g,"gclsrc")}if(void 0!==d&&d.match(bg))switch(e){case void 0:c(d,
"aw");break;case "aw.ds":c(d,"aw");c(d,"dc");break;case "ds":c(d,"dc");break;case "gf":c(d,"gf");break;case "ha":c(d,"ha")}var h=sb(a,"query",!1,void 0,"dclid");h&&c(h,"dc");return b},kg=function(a){function b(a,b){var g=jg(a,c);g&&rf(g,b,e,d,h,!0)}a=a||{};var c=hg(a.prefix),d=a.domain||"auto",e=a.path||"/",g=Ca().getTime(),h=new Date(g+7776E6),k=Math.round(g/1E3),l=ig(),m=function(a){return["GCL",k,a].join(".")};l.aw&&(!0===a.vf?b("aw",m("~"+l.aw[0])):b("aw",m(l.aw[0])));l.dc&&b("dc",m(l.dc[0]));
l.gf&&b("gf",m(l.gf[0]));l.ha&&b("ha",m(l.ha[0]))},jg=function(a,b){var c=dg[a];if(void 0!==c){var d=b[a];if(void 0!==d)return d+c}},lg=function(a){var b=a.split(".");return 3!==b.length||"GCL"!==b[0]?0:1E3*(Number(b[1])||0)},mg=function(a,b,c,d,e){if(ua(b)){var g=hg(e);$f(function(){for(var b={},c=0;c<a.length;++c){var d=jg(a[c],g);if(d){var e=lf(d,B.cookie);e.length&&(b[d]=e.sort()[e.length-1])}}return b},b,c,d)}},eg=function(a){return a.filter(function(a){return cg.test(a)})};var ng=/^\d+\.fls\.doubleclick\.net$/;function og(a){var b=N(z.location.href),c=sb(b,"host",!1);if(c&&c.match(ng)){var d=sb(b,"path").split(a+"=");if(1<d.length)return d[1].split(";")[0].split("?")[0]}}
var pg=function(a){var b=og("gclaw");if(b)return b.split(".");var c=hg(a);if("_gcl"==c.aw){var d=ig().aw||[];if(0<d.length)return d}var e=jg("aw",c);return e?fg(e):[]},qg=function(a){var b=og("gcldc");if(b)return b.split(".");var c=hg(a);if("_gcl"==c.dc){var d=ig().dc||[];if(0<d.length)return d}var e=jg("dc",c);return e?fg(e):[]},rg=function(a){var b=hg(a);if("_gcl"==b.ha){var c=ig().ha||[];if(0<c.length)return c}return fg(b.ha+"_ha")},sg=function(){var a=og("gac");if(a)return decodeURIComponent(a);
for(var b=[],c=B.cookie.split(";"),d=/^\s*_gac_(UA-\d+-\d+)=\s*(.+?)\s*$/,e=0;e<c.length;e++){var g=c[e].match(d);g&&b.push({Cb:g[1],value:g[2]})}var h={};if(b&&b.length)for(var k=0;k<b.length;k++){var l=b[k].value.split(".");"1"==l[0]&&3==l.length&&l[1]&&(h[b[k].Cb]||(h[b[k].Cb]=[]),h[b[k].Cb].push({timestamp:l[1],Id:l[2]}))}var m=[],n;for(n in h)if(h.hasOwnProperty(n)){for(var p=[],q=h[n],r=0;r<q.length;r++)p.push(q[r].Id);p=eg(p);p.length&&m.push(n+":"+p.join(","))}return m.join(";")},tg=function(a,
b,c){Ef(a,b,c);var d=Af[Bf(a)],e=ig().dc||[];if(d&&0<e.length){var g=Jc.joined_au=Jc.joined_au||{},h=a||"_gcl";if(!g[h]){for(var k=!1,l=0;l<e.length;l++){var m="https://adservice.google.com/ddm/regclk";m+="?gclid="+e[l]+"&auiddc="+d;ob(m);k=!0}if(k){var n=Bf(a);Af[n]&&Df(n,Af[n],b,c);g[h]=!0}}}};var ug;a:{ug="g";break a;ug="G"}var vg={"":"n",UA:"u",AW:"a",DC:"d",G:"e",GTM:ug},wg=function(a){var b=Ic.s.split("-"),c=b[0].toUpperCase();return(vg[c]||"i")+"a1"+(a&&"GTM"===c?b[1]:"")};
var xg=function(a){return!(void 0===a||null===a||0===(a+"").length)},yg=function(a,b){var c;if(2===b.J)return a("ord",xa(1E11,1E13)),!0;if(3===b.J)return a("ord","1"),a("num",xa(1E11,1E13)),!0;if(4===b.J)return xg(b.sessionId)&&a("ord",b.sessionId),!0;if(5===b.J)c="1";else if(6===b.J)c=b.Fc;else return!1;xg(c)&&a("qty",c);xg(b.gb)&&a("cost",b.gb);xg(b.Db)&&a("ord",b.Db);return!0},zg=encodeURIComponent,Ag=function(a,b){function c(a,b,c){g.hasOwnProperty(a)||(b+="",e+=";"+a+"="+(c?b:zg(b)))}var d=a.jb,
e=a.protocol;e+=a.Xa?"//"+d+".fls.doubleclick.net/activityi":"//ad.doubleclick.net/activity";e+=";src="+zg(d)+(";type="+zg(a.lb))+(";cat="+zg(a.va));var g=a.vd||{},h;for(h in g)g.hasOwnProperty(h)&&(e+=";"+zg(h)+"="+zg(g[h]+""));if(yg(c,a)){xg(a.Fb)&&c("u",a.Fb);xg(a.tran)&&c("tran",a.tran);c("gtm",wg());!1===a.Yc&&c("npa","1");if(a.fb){var k=qg(a.ia);k&&k.length&&c("gcldc",k.join("."));var l=pg(a.ia);l&&l.length&&c("gclaw",l.join("."));var m=sg();m&&c("gac",m);
Ef(a.ia);var n=Af[Bf(a.ia)];n&&c("auiddc",n);}xg(a.ub)&&c("prd",a.ub,!0);for(var p in a.Fa)a.Fa.hasOwnProperty(p)&&c(p,a.Fa[p]);e+=b||"";xg(a.Ta)&&c("~oref",a.Ta);a.Xa?gb(e+"?",a.U):F(e+"?",a.U,a.la)}else H(a.la)};
var Cg=function(a){if(a)try{if(a.conversion_id&&a.conversion_data){var b="/pagead/conversion/"+Bg(a.conversion_id)+"/?",c=Bg(JSON.stringify(a.conversion_data)),d="https://www.googletraveladservices.com/travel/flights/clk"+b+"conversion_data="+c;if(a.conversionLinkerEnabled){var e;a:{var g=hg(a.conversionPrefix);if("_gcl"==g.gf){var h=ig().gf||[];if(0<h.length){e=h;break a}}var k=jg("gf",g);e=k?fg(k):[]}var l=e;if(l&&l.length)for(var m=0;m<l.length;m++)d+="&gclgf="+Bg(l[m])}F(d,a.onSuccess,a.onFailure)}}catch(n){}},
Bg=function(a){return null===a||void 0===a||0===String(a).length?"":encodeURIComponent(String(a))};var Dg=!!z.MutationObserver,Eg=void 0,Fg=function(a){if(!Eg){var b=function(){var a=B.body;if(a)if(Dg)(new MutationObserver(function(){for(var a=0;a<Eg.length;a++)H(Eg[a])})).observe(a,{childList:!0,subtree:!0});else{var b=!1;hb(a,"DOMNodeInserted",function(){b||(b=!0,H(function(){b=!1;for(var a=0;a<Eg.length;a++)H(Eg[a])}))})}};Eg=[];B.body?b():H(b)}Eg.push(a)};var Qg="www.googletagmanager.com/gtm.js";Qg="www.googletagmanager.com/gtag/js";
var Rg=Qg,Sg=function(a,b,c,d){hb(a,b,c,d)},Tg=function(a,b){return z.setTimeout(a,b)},T=function(a,b,c){if(Be()){b&&H(b)}else return fb(a,b,c)},Ug=function(){return z.location.href},Vg=function(a){return sb(N(a),"fragment")},Wg=function(a,b,c,d,e){return sb(a,b,c,d,e)},U=function(a,b){return Uc(a,b||2)},Xg=function(a,b,c){b&&(a.eventCallback=b,c&&(a.eventTimeout=c));return z["dataLayer"].push(a)},Yg=function(a,
b){z[a]=b},V=function(a,b,c){b&&(void 0===z[a]||c&&!z[a])&&(z[a]=b);return z[a]},Zg=function(a,b,c){return lf(a,b,void 0===c?!0:!!c)},$g=function(a,b,c){kg({prefix:a,path:b,domain:c})},ah=function(a,b,c,d){var e=Uf(),g=Nf();g.data||(g.data={query:{},fragment:{}},e(g.data));var h={},k=g.data;k&&(Ga(h,k.query),Ga(h,k.fragment));for(var l=hg(b),m=0;m<a.length;++m){var n=a[m];if(void 0!==dg[n]){var p=jg(n,l),q=h[p];if(q){var r=Math.min(lg(q),Ca().getTime()),
u;b:{for(var t=r,A=lf(p,B.cookie),C=0;C<A.length;++C)if(lg(A[C])>t){u=!0;break b}u=!1}u||rf(p,q,c,d,new Date(r+7776E6),!0)}}}},bh=function(a,b,c,d,e){mg(a,b,c,d,e);},ch=function(a,b){var c;a:{var d;d=100;for(var e={},g=0;g<b.length;g++)e[b[g]]=!0;for(var h=a,k=0;h&&k<=d;k++){if(e[String(h.tagName).toLowerCase()]){c=h;break a}h=h.parentElement}c=null}return c},X=function(a,
b,c,d){var e=!d&&"http:"==z.location.protocol;e&&(e=2!==dh());return(e?b:a)+c},eh=function(a,b){if(Be()){b&&H(b)}else gb(a,b)};
var fh=function(a){var b=0;return b},gh=function(a){},hh=function(a){var b=!1;return b},ih=function(a,b){var c;a:{if(a&&
ua(a))for(var d=0;d<a.length;d++)if(a[d]&&b(a[d])){c=a[d];break a}c=void 0}return c},jh=function(a,b,c,d){ff(a,b,c,d)},kh=function(a,b,c){return gf(a,b,c)},lh=function(a){return!!gf(a,"init",!1)},mh=function(a){ef(a).init=!0};
var dh=function(){var a=Rg;if(Pc){if(0===Pc.toLowerCase().indexOf("https://"))return 2;if(0===Pc.toLowerCase().indexOf("http://"))return 3}a=a.toLowerCase();for(var b="https://"+a,c="http://"+a,d=1,e=B.getElementsByTagName("script"),g=0;g<e.length&&100>g;g++){var h=e[g].src;if(h){h=h.toLowerCase();if(0===h.indexOf(c))return 3;1===d&&0===h.indexOf(b)&&(d=2)}}return d};
var oh=function(a,b){return Wc(a,b,void 0)},ph=function(a,b,c,d){var e={config:a,gtm:wg(void 0)};c&&(Ef(d),e.auiddc=Af[Bf(d)]);b&&(e.loadInsecure=b);V("__dc_ns_processor",[]).push(e);T((b?"http":"https")+"://www.googletagmanager.com/dclk/ns/v1.js")};
var qh=function(a,b,c){var d=(void 0===c?0:c)?"www.googletagmanager.com/gtag/js":Rg;d+="?id="+encodeURIComponent(a)+"&l=dataLayer";if(b)for(var e in b)b[e]&&b.hasOwnProperty(e)&&(d+="&"+e+"="+encodeURIComponent(b[e]));T(X("https://","http://",d))};
var sh=function(a,b,c){a instanceof ve.Qc&&(a=a.resolve(ve.Ae(b,c)),b=qa);return{mb:a,U:b}};var Fh=function(a,b,c){this.n=a;this.t=b;this.p=c},Gh=function(){this.c=1;this.e=[];this.p=null};function Hh(a){var b=Jc,c=b.gss=b.gss||{};return c[a]=c[a]||new Gh}var Ih=function(a,b){Hh(a).p=b},Jh=function(a,b,c){var d=Math.floor(Ca().getTime()/1E3);Hh(a).e.push(new Fh(b,d,c))},Kh=function(a){};var Th=window,Uh=document,Vh=function(a){var b=Th._gaUserPrefs;if(b&&b.ioo&&b.ioo()||a&&!0===Th["ga-disable-"+a])return!0;try{var c=Th.external;if(c&&c._gaUserPrefs&&"oo"==c._gaUserPrefs)return!0}catch(g){}for(var d=lf("AMP_TOKEN",Uh.cookie,!0),e=0;e<d.length;e++)if("$OPT_OUT"==d[e])return!0;return!1};var $h=function(a){if(1===Hh(a).c){Hh(a).c=2;var b=encodeURIComponent(a);fb(("http:"!=z.location.protocol?"https:":"http:")+("//www.googletagmanager.com/gtag/js?id="+b+"&l=dataLayer&cx=c"))}},ai=function(a,b){};var Z={a:{}};
Z.a.gtagha=["google"],function(){function a(a){function b(a,b){void 0!==b&&c.push(a+"="+b)}if(void 0===a)return"";var c=[];b("hct_base_price",a.ic);b("hct_booking_xref",a.jc);b("hct_checkin_date",a.Pd);b("hct_checkout_date",a.Qd);b("hct_currency_code",a.Rd);b("hct_partner_hotel_id",a.kc);b("hct_total_price",a.mc);return c.join(";")}function b(b,c,d,k){var e=encodeURIComponent(b),g=encodeURIComponent(a(c)),h="https://www.googletraveladservices.com/travel/clk/pagead/conversion/"+e+"/?data="+g;d&&(h+=
rg(k).map(function(a){return"&gclha="+encodeURIComponent(a)}).join(""));return h}function c(a,b,c,d){var e={};sa(a)?e.jc=a:"number"===typeof a&&(e.jc=String(a));sa(c)&&(e.Rd=c);sa(b)?e.mc=e.ic=b:"number"===typeof b&&(e.mc=e.ic=String(b));if(!ua(d)||0==d.length)return e;var g=d[0];if(!Oa(g))return e;sa(g.id)?e.kc=g.id:"number"===typeof g.id&&(e.kc=String(g.id));sa(g.start_date)&&(e.Pd=g.start_date);sa(g.end_date)&&(e.Qd=g.end_date);return e}function d(a){var b=Lc,e=a.vtp_gtmOnSuccess,k=a.vtp_gtmOnFailure,
l=a.vtp_conversionId,m=l.containerId,n=function(a){return Wc(a,m,l.id)},p=!1!==n("conversion_linker"),q=n("conversion_cookie_prefix");if("gtag.config"===b)p&&$g(q),H(e);else if("purchase"===b){var r=c(n("transaction_id"),n("value"),n("currency"),n("items"));d.Ne(l.X[0],r,p,q,e,k)}else H(k)}d.Ne=function(a,c,d,k,l,m){if(/^\d+$/.test(a)){var e=b(a,c,d,k);F(e,l,m)}else H(m)};Z.__gtagha=d;Z.__gtagha.g="gtagha";Z.__gtagha.h=!0;Z.__gtagha.b=0}();
Z.a.e=["google"],function(){(function(a){Z.__e=a;Z.__e.g="e";Z.__e.h=!0;Z.__e.b=0})(function(){return Lc})}();


Z.a.v=["google"],function(){(function(a){Z.__v=a;Z.__v.g="v";Z.__v.h=!0;Z.__v.b=0})(function(a){var b=a.vtp_name;if(!b||!b.replace)return!1;var c=U(b.replace(/\\\./g,"."),a.vtp_dataLayerVersion||1);return void 0!==c?c:a.vtp_defaultValue})}();




Z.a.gtagaw=["google"],function(){var a=!1,b=!1,c=[],d=["aw","dc"],e="send_to aw_remarketing aw_remarketing_only custom_params send_page_view language value currency transaction_id user_id conversion_linker conversion_cookie_prefix page_location page_referrer phone_conversion_number phone_conversion_callback phone_conversion_css_class items aw_merchant_id aw_feed_country aw_feed_language discount disable_merchant_reported_purchases allow_ad_personalization_signals".split(" "),g=function(a){var b=V("google_trackConversion"),
c=a.gtm_onFailure;"function"==typeof b?b(a)||c():c()},h=function(){for(;0<c.length;)g(c.shift())},k=function(){a||(a=!0,T(X("https://","http://","www.googleadservices.com/pagead/conversion_async.js"),function(){h();c={push:g}},function(){h();a=!1}))},l=function(a,c,d,e){if(Be()){}else if(c){var g=a.X[0],h=a.X[1],k=V("_googWcmImpl",function(){k.q=k.q||[];k.q.push(arguments)});V("_googWcmAk",g);b||(b=!0,T(X("https://",
"http://","www.gstatic.com/wcm/loader.js")));var l={ak:g,cl:h};void 0===d&&(l.autoreplace=c);k(2,d,l,c,e,new Date,e)}},m=function(a){if(a){for(var b=[],c=0;c<a.length;++c){var d=a[c];d&&b.push({item_id:d.id,quantity:d.quantity,value:d.price,start_date:d.start_date,end_date:d.end_date})}return b}},n=function(a){var b=a.vtp_conversionId,g=Lc,h="gtag.config"==g,n=b.X[0],p=b.X[1],C=void 0!==p,D=b.containerId,L=C?b.id:void 0,E=function(a){return Wc(a,D,L)},G=!1!==E("conversion_linker"),J=E("conversion_cookie_prefix");
if(h){var I=E("linker")||{};G&&((I.accept_incoming||!1!==I.accept_incoming&&I.domains)&&ah(d,J),$g(J));I.domains&&bh(d,I.domains,I.url_position,!!I.decorate_forms,J);if(C){var K=E("phone_conversion_number"),R=E("phone_conversion_callback"),ja=E("phone_conversion_css_class"),W=E("phone_conversion_options");l(b,K,R||ja,W)}}var aa=!1===E("aw_remarketing")||!1===E("send_page_view");if(!h||!C&&!aa)if(!0===E("aw_remarketing_only")&&(C=!1),!1!==E("allow_ad_personalization_signals")||C){var M={google_conversion_id:n,
google_remarketing_only:!C,onload_callback:a.vtp_gtmOnSuccess,gtm_onFailure:a.vtp_gtmOnFailure,google_conversion_format:"3",google_conversion_color:"ffffff",google_conversion_domain:"",google_conversion_label:p,google_conversion_language:E("language"),google_conversion_value:E("value"),google_conversion_currency:E("currency"),google_conversion_order_id:E("transaction_id"),google_user_id:E("user_id"),google_conversion_page_url:E("page_location"),google_conversion_referrer_url:E("page_referrer"),google_gtm:wg(void 0)};
!1===E("allow_ad_personalization_signals")&&(M.google_allow_ad_personalization_signals=!1);M.google_read_gcl_cookie_opt_out=!G;G&&J&&(Oa(J)?M.google_gcl_cookie_prefix=J.aw:M.google_gcl_cookie_prefix=J);var S=function(){var a=E("custom_params"),b={event:g};if(ua(a)){for(var c=0;c<a.length;++c){var d=a[c],h=E(d);void 0!==h&&(b[d]=h)}return b}var k=E("eventModel");if(!k)return null;Pa(k,b);for(var l=0;l<e.length;++l)delete b[e[l]];return b}();S&&(M.google_custom_params=S);!C&&E("items")&&(M.google_gtag_event_data=
{items:E("items"),value:E("value")});if(C&&"purchase"==g){E("aw_merchant_id")&&(M.google_conversion_merchant_id=E("aw_merchant_id"),M.google_basket_feed_country=E("aw_feed_country"),M.google_basket_feed_language=E("aw_feed_language"),M.google_basket_discount=E("discount"),M.google_basket_transaction_type=g,M.google_disable_merchant_reported_conversions=!0===E("disable_merchant_reported_purchases"),Be()&&(M.google_disable_merchant_reported_conversions=!0));var O=m(E("items"));O&&(M.google_conversion_items=
O)}c.push(M)}k()};Z.__gtagaw=n;Z.__gtagaw.g="gtagaw";Z.__gtagaw.h=!0;Z.__gtagaw.b=0}();

Z.a.get=["google"],function(){(function(a){Z.__get=a;Z.__get.g="get";Z.__get.h=!0;Z.__get.b=0})(function(a){if(a.vtp_isAutoTag){for(var b=String(a.vtp_trackingId),c=Lc||"",d={},e=0;e<$d.length;e++){var g=oh($d[e],b);void 0!==g&&(d[$d[e]]=g)}var h=oh("custom_params",b);if(ua(h))for(var k=0;k<h.length;k++){var l=h[k],m=oh(l,b);void 0!==m&&(d[l]=m)}else{var n=U("eventModel");Pa(n,d)}var p=Pa(d,void 0);$h(b);Jh(b,c,p);Kh(b)}else{var q=a.vtp_settings,r=q.eventParameters,u=q.userProperties,t=Q(a.vtp_eventParameters,
"name","value");Pa(t,r);var A=Q(a.vtp_userProperties,"name","value");Pa(A,u);r.user_properties=u;var C=String(q.streamId),D=String(a.vtp_eventName);$h(C);Jh(C,D,r);Kh(C)}a.vtp_gtmOnSuccess()})}();



Z.a.gtagfl=[],function(){function a(a){var b=/^DC-(\d+)(\/([\w-]+)\/([\w-]+)\+(\w+))?$/.exec(a);if(b){var c={standard:2,unique:3,per_session:4,transactions:5,items_sold:6,"":1}[(b[5]||"").toLowerCase()];if(c)return{containerId:"DC-"+b[1],Kc:b[3]?a:"",Tc:b[1],Sc:b[3]||"",va:b[4]||"",J:c}}}function b(a,b){function c(b,c,e){void 0!==e&&0!==(e+"").length&&d.push(b+c+":"+a(e+""))}var d=[],e=b("items")||[];if(ua(e))for(var g=0;g<e.length;g++){var n=e[g],p=g+1;c("i",p,n.id);c("p",p,n.price);c("q",p,n.quantity);
c("c",p,b("country"));c("l",p,b("language"))}return d.join("|")}function c(a,b,c){var d=/^u([1-9]\d?|100)$/,e=a("custom_map")||{},g=Zc(b,c),h={},p={};if(Oa(e))for(var q in e)if(e.hasOwnProperty(q)&&d.test(q)){var r=e[q];sa(r)&&(h[q]=r)}for(var u=0;u<g.length;u++){var t=g[u];d.test(t)&&(h[t]=t)}for(var A in h)h.hasOwnProperty(A)&&(p[A]=a(h[A]));return p}var d=["aw","dc"];(function(a){Z.__gtagfl=a;Z.__gtagfl.g="gtagfl";Z.__gtagfl.h=!0;Z.__gtagfl.b=0})(function(e){var g=e.vtp_gtmOnSuccess,h=e.vtp_gtmOnFailure,
k=a(e.vtp_targetId);if(k){var l=function(a){return Wc(a,k.containerId,k.Kc||void 0)},m=!1!==l("conversion_linker"),n=l("conversion_cookie_prefix"),p=l("dc_natural_search"),q=3===dh();if("gtag.config"===Lc){var r=l("linker")||{};m&&((r.accept_incoming||!1!==r.accept_incoming&&r.domains)&&ah(d,n),$g(n),tg(n,void 0,void 0));r.domains&&bh(d,r.domains,r.url_position,!!r.decorate_forms,n);if(p&&p.exclusion_parameters&&p.engines){}H(g)}else{var u=
{},t=l("dc_custom_params");if(Oa(t))for(var A in t)if(t.hasOwnProperty(A)){var C=t[A];void 0!==C&&null!==C&&(u[A]=C)}var D="";if(5===k.J||6===k.J)D=b(Ze,l);var L=c(l,k.containerId,k.Kc),E=!0===l("allow_custom_scripts");if(Be()&&E){E=!1}var G={va:k.va,fb:m,ia:n,gb:l("value"),J:k.J,vd:u,jb:k.Tc,lb:k.Sc,la:h,U:g,Ta:tb(N(Ug())),ub:D,protocol:q?"http:":"https:",Fc:l("quantity"),Xa:E,sessionId:l("session_id"),Db:l("transaction_id"),
Fa:L,Yc:!1!==l("allow_ad_personalization_signals")};Ag(G,void 0)}}else H(h)})}();
Z.a.gtaggf=["google"],function(){var a=/.*\.google\.com(:\d+)?\/booking\/flights.*/,b=function(a){if(a){for(var b=[],c=0,g=0;g<a.length;++g){var h=a[g];!h||void 0!==h.category&&""!==h.category&&"FlightSegment"!==h.category||(b[c]={cabin:h.travel_class,fare_product:h.fare_product,booking_code:h.booking_code,flight_number:h.flight_number,origin:h.origin,destination:h.destination,departure_date:h.start_date},c++)}return b}};(function(a){Z.__gtaggf=a;Z.__gtaggf.g="gtaggf";Z.__gtaggf.h=!0;Z.__gtaggf.b=
0})(function(c){var d=Lc,e=c.vtp_gtmOnSuccess,g=c.vtp_gtmOnFailure,h=c.vtp_conversionId,k=h.X[0],l=h.containerId,m=function(a){return Wc(a,l,h.id)},n=!1!==m("conversion_linker"),p=m("conversion_cookie_prefix");if("gtag.config"===d)n&&$g(p),H(e);else{var q={conversion_id:k,onFailure:g,onSuccess:e,conversionLinkerEnabled:n,conversionPrefix:p};if("purchase"===d){var r=a.test(Ug()),u={partner_id:k,trip_type:m("trip_type"),total_price:m("value"),currency:m("currency"),is_direct_booking:r,flight_segment:b(m("items"))},
t=m("passengers");t&&"object"===typeof t&&(u.passengers_total=t.total,u.passengers_adult=t.adult,u.passengers_child=t.child,u.passengers_infant_in_seat=t.infant_in_seat,u.passengers_infant_in_lap=t.infant_in_lap);q.conversion_data=u}Cg(q)}})}();


Z.a.gtagua=["google"],function(){var a,b={client_id:1,client_storage:"storage",cookie_name:1,cookie_domain:1,cookie_expires:1,cookie_path:1,cookie_update:1,sample_rate:1,site_speed_sample_rate:1,use_amp_client_id:1,store_gac:1,conversion_linker:"storeGac"},c={anonymize_ip:1,app_id:1,app_installer_id:1,app_name:1,app_version:1,campaign:{name:"campaignName",source:"campaignSource",medium:"campaignMedium",term:"campaignTerm",content:"campaignContent",id:"campaignId"},currency:"currencyCode",description:"exDescription",
fatal:"exFatal",language:1,non_interaction:1,page_hostname:"hostname",page_referrer:"referrer",page_path:"page",page_location:"location",page_title:"title",screen_name:1,transport_type:"transport",user_id:1},d={content_id:1,event_category:1,event_action:1,event_label:1,link_attribution:1,linker:1,method:1,name:1,send_page_view:1,value:1},e={cookie_name:1,cookie_expires:"duration",levels:1},g={anonymize_ip:1,fatal:1,non_interaction:1,use_amp_client_id:1,send_page_view:1,store_gac:1,conversion_linker:1},
h=function(a,b,c,d){if(void 0!==c)if(g[b]&&(c=Aa(c)),"anonymize_ip"!=b||c||(c=void 0),1===a)d[k(b)]=c;else if(sa(a))d[a]=c;else for(var e in a)a.hasOwnProperty(e)&&void 0!==c[e]&&(d[a[e]]=c[e])},k=function(a){return a&&sa(a)?a.replace(/(_[a-z])/g,function(a){return a[1].toUpperCase()}):a},l=function(a,b,c){a.hasOwnProperty(b)||(a[b]=c)},m=function(a,e,g){var k={},m={},n={},p;var q=oh("experiments",a);if(ua(q)){for(var t=[],r=0;r<q.length;r++){var u=q[r];if(void 0!=u){var A=u.id,ja=u.variant;void 0!=
A&&void 0!=ja&&t.push(String(A)+"."+String(ja))}}p=0<t.length?t.join("!"):void 0}else p=void 0;p&&l(m,"exp",p);var W=oh("custom_map",a);if(Oa(W))for(var aa in W)if(W.hasOwnProperty(aa)&&/^(dimension|metric)\d+$/.test(aa)){var M=oh(W[aa],a);void 0!==M&&l(m,aa,M)}for(var S=Zc(a,void 0),O=0;O<S.length;++O){var Y=S[O],ba=oh(Y,a);d.hasOwnProperty(Y)?h(d[Y],Y,ba,k):c.hasOwnProperty(Y)?h(c[Y],Y,ba,m):b.hasOwnProperty(Y)?h(b[Y],Y,ba,n):/^(dimension|metric|content_group)\d+$/.test(Y)&&h(1,Y,ba,m)}var ca=String(Lc);
l(n,"cookieDomain","auto");l(m,"forceSSL",!0);var wa="general";0<=cf("add_payment_info add_to_cart add_to_wishlist begin_checkout checkout_progress purchase refund remove_from_cart set_checkout_option".split(" "),ca)?wa="ecommerce":0<=cf("generate_lead login search select_content share sign_up view_item view_item_list view_promotion view_search_results".split(" "),ca)?wa="engagement":"exception"==ca&&(wa="error");l(k,"eventCategory",wa);0<=cf(["view_item","view_item_list","view_promotion","view_search_results"],
ca)&&l(m,"nonInteraction",!0);"login"==ca||"sign_up"==ca||"share"==ca?l(k,"eventLabel",oh("method",a)):"search"==ca||"view_search_results"==ca?l(k,"eventLabel",oh("search_term",a)):"select_content"==ca&&l(k,"eventLabel",oh("content_type",a));var za=k.linker||{};if(za.accept_incoming||0!=za.accept_incoming&&za.domains)n.allowLinker=!0;if(!1===oh("allow_display_features",a)||!1===oh("allow_ad_personalization_signals",a))m.allowAdFeatures=!1;n.name=e;m["&gtm"]=wg(!0);m.hitCallback=g;k.S=m;k.$b=n;return k},
n=function(a){function b(a){var b=Pa(a,void 0);b.list=a.list_name;b.listPosition=a.list_position;b.position=a.list_position||a.creative_slot;b.creative=a.creative_name;return b}function c(a){for(var c=[],d=0;a&&d<a.length;d++)a[d]&&c.push(b(a[d]));return c.length?c:void 0}function d(a){return{id:e("transaction_id"),affiliation:e("affiliation"),revenue:e("value"),tax:e("tax"),shipping:e("shipping"),coupon:e("coupon"),list:e("list_name")||a}}for(var e=function(b){return Wc(b,a,void 0)},g=e("items"),
h,k=0;g&&k<g.length&&!(h=g[k].list_name);k++);var m=e("custom_map");if(Oa(m))for(k=0;g&&k<g.length;++k){var n=g[k],p;for(p in m)m.hasOwnProperty(p)&&/^(dimension|metric)\d+$/.test(p)&&l(n,p,n[m[p]])}var q=null,r=Lc,u=e("promotions");"purchase"==r||"refund"==r?q={action:r,sa:d(),ma:c(g)}:"add_to_cart"==r?q={action:"add",ma:c(g)}:"remove_from_cart"==r?q={action:"remove",ma:c(g)}:"view_item"==r?q={action:"detail",sa:d(h),ma:c(g)}:"view_item_list"==r?q={action:"impressions",Vd:c(g)}:"view_promotion"==
r?q={action:"promo_view",vb:c(u)}:"select_content"==r&&u&&0<u.length?q={action:"promo_click",vb:c(u)}:"select_content"==r?q={action:"click",sa:{list:e("list_name")||h},ma:c(g)}:"begin_checkout"==r||"checkout_progress"==r?q={action:"checkout",ma:c(g),sa:{step:"begin_checkout"==r?1:e("checkout_step"),option:e("checkout_option")}}:"set_checkout_option"==r&&(q={action:"checkout_option",sa:{step:e("checkout_step"),option:e("checkout_option")}});q&&(q.af=e("currency"));return q},p={},q=function(a,b){var c=
p[a];p[a]=Pa(b,void 0);if(!c)return!1;for(var d in b)if(b.hasOwnProperty(d)&&b[d]!==c[d])return!0;for(d in c)if(c.hasOwnProperty(d)&&c[d]!==b[d])return!0;return!1},r=function(b){var c=b.vtp_trackingId,d=td(void 0),g="gtag_"+c.split("-").join("_"),p=function(a){var b=[].slice.call(arguments,0);b[0]=g+"."+b[0];d.apply(window,b)},r=function(){var a=function(a,b){for(var c=0;b&&c<b.length;c++)p(a,b[c])},b=n(c);if(b){var d=b.action;if("impressions"==d)a("ec:addImpression",b.Vd);else if("promo_click"==
d||"promo_view"==d){var e=b.vb;a("ec:addPromo",b.vb);e&&0<e.length&&"promo_click"==d&&p("ec:setAction",d)}else a("ec:addProduct",b.ma),p("ec:setAction",d,b.sa)}},u=function(){if(Be()){}else{var a=oh("optimize_id",c);a&&(p("require",a,{dataLayer:"dataLayer"}),p("require","render"))}},G=m(c,g,b.vtp_gtmOnSuccess);q(g,G.$b)&&d(function(){rd()&&rd().remove(g)});d("create",c,G.$b);(function(){var a=oh("custom_map",c);
d(function(){if(Oa(a)){var b=G.S,c=rd().getByName(g),d;for(d in a)if(a.hasOwnProperty(d)&&/^(dimension|metric)\d+$/.test(d)){var e=c.get(k(a[d]));l(b,d,e)}}})})();(function(a){if(a){var b={};if(Oa(a))for(var c in e)e.hasOwnProperty(c)&&h(e[c],c,a[c],b);p("require","linkid",b)}})(G.linkAttribution);var J=G.linker;J&&J.domains&&ud(g+".",J.domains,!!J.use_anchor,!!J.decorate_forms);var I=function(a,b,c){c&&(b=""+b);G.S[a]=b},K=Lc;"page_view"==K?(u(),p("send","pageview",G.S)):"gtag.config"==K?(u(),0!=
G.sendPageView&&p("send","pageview",G.S)):"screen_view"==K?p("send","screenview",G.S):"timing_complete"==K?(I("timingCategory",G.eventCategory,!0),I("timingVar",G.name,!0),I("timingValue",ya(G.value)),void 0!==G.eventLabel&&I("timingLabel",G.eventLabel,!0),p("send","timing",G.S)):"exception"==K?p("send","exception",G.S):(0<=cf("view_item_list select_content view_item add_to_cart remove_from_cart begin_checkout set_checkout_option purchase refund view_promotion checkout_progress".split(" "),K)&&(p("require",
"ec","ec.js"),r()),I("eventCategory",G.eventCategory,!0),I("eventAction",G.eventAction||K,!0),void 0!==G.eventLabel&&I("eventLabel",G.eventLabel,!0),void 0!==G.value&&I("eventValue",ya(G.value)),p("send","event",G.S));a||(a=!0,T("https://www.google-analytics.com/analytics.js",function(){rd().loaded||b.vtp_gtmOnFailure()},b.vtp_gtmOnFailure))};Z.__gtagua=r;Z.__gtagua.g="gtagua";Z.__gtagua.h=!0;Z.__gtagua.b=
0}();

var bi={macro:function(a){if(ve.ab.hasOwnProperty(a))return ve.ab[a]}};bi.dataLayer=Vc;bi.onHtmlSuccess=ve.Yb(!0);bi.onHtmlFailure=ve.Yb(!1);bi.callback=function(a){Nc.hasOwnProperty(a)&&ra(Nc[a])&&Nc[a]();delete Nc[a]};bi.ed=function(){Jc[Ic.s]=bi;Oc=Z.a;sc=sc||ve;tc=fd};
bi.Wd=function(){Jc=z.google_tag_manager=z.google_tag_manager||{};if(Jc[Ic.s]){var a=Jc.zones;a&&a.unregisterChild(Ic.s)}else{for(var b=data.resource||{},c=b.macros||[],d=0;d<c.length;d++)lc.push(c[d]);for(var e=b.tags||[],g=0;g<e.length;g++)oc.push(e[g]);for(var h=b.predicates||[],k=0;k<h.length;k++)nc.push(h[k]);for(var l=b.rules||[],m=0;m<l.length;m++){for(var n=l[m],p={},q=0;q<n.length;q++)p[n[q][0]]=Array.prototype.slice.call(n[q],1);mc.push(p)}qc=Z;Xe();bi.ed();ue();jd=!1;kd=0;if("interactive"==
B.readyState&&!B.createEventObject||"complete"==B.readyState)md();else{hb(B,"DOMContentLoaded",md);hb(B,"readystatechange",md);if(B.createEventObject&&B.documentElement.doScroll){var r=!0;try{r=!z.frameElement}catch(t){}r&&nd()}hb(z,"load",md)}me=!1;"complete"===B.readyState?oe():hb(z,"load",oe);a:{
if(!zd)break a;Cd();Fd=void 0;Gd={};Dd={};Id=void 0;Hd={};Ed="";Jd=Ad();z.setInterval(Cd,864E5);}Kc=(new Date).getTime()}};bi.Wd();

})()
