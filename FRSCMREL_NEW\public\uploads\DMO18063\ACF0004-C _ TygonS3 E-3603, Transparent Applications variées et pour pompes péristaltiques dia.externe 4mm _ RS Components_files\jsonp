lpTag.callback({"serviceMap":[{"service":"leBackofficeInt","account":"********","baseURI":"z2.houston.int.liveperson.net"},{"service":"liveEngage","account":"********","baseURI":"z2.le.liveperson.net"},{"service":"accountCreation","account":"********","baseURI":"z2.acc-create.liveperson.net"},{"service":"appKeyManagement","account":"********","baseURI":"z2.appkey-mgmnt.liveperson.net"},{"service":"connectionPanel","account":"********","baseURI":"z2.connection-panel.liveperson.net"},{"service":"liveEngageUI","account":"********","baseURI":"lo.le1.liveperson.net"},{"service":"rtbf","account":"********","baseURI":"lo.data-mng.liveperson.net"},{"service":"leBilling","account":"********","baseURI":"lo.le-billing.liveperson.net"},{"service":"interactionPlatform","account":"********","baseURI":"lo.i.liveperson.net"},{"service":"rtDashboard","account":"********","baseURI":"lo.realtime.liveperson.net"},{"service":"facadeMsg","account":"********","baseURI":"lo.facade-msg.liveperson.net"},{"service":"etool","account":"********","baseURI":"z2.etool.liveperson.net"},{"service":"swift","account":"********","baseURI":"z2.objectstorage.liveperson.net"},{"service":"staticContent","account":"********","baseURI":"lo.content.lpsnmedia.net"},{"service":"coApp","account":"********","baseURI":"z2.coapp.liveperson.net"},{"service":"keyService","account":"********","baseURI":"lo.keyservice.liveperson.net"},{"service":"cqmMsg","account":"********","baseURI":"lo.cqm-msg.liveperson.net"},{"service":"loggos","account":"********","baseURI":"z2.loggos.liveperson.net"},{"service":"tokenizer","account":"********","baseURI":"lo.tokenizer.liveperson.net"},{"service":"leRBMstr","account":"********","baseURI":"z2-3.birb.liveperson.net"},{"service":"pusherInt","account":"********","baseURI":"lo.pushsrv.int.liveperson.net"},{"service":"acCdnDomain","account":"********","baseURI":"accdn.lpsnmedia.net"},{"service":"leDataReporting","account":"********","baseURI":"lo.data.liveperson.net"},{"service":"predictiveDialer","account":"********","baseURI":"lo.pd.liveperson.net"},{"service":"act","account":"********","baseURI":"lo.act.liveperson.net"},{"service":"liveEngageVep","account":"********","baseURI":"lo.batchelor.liveperson.net"},{"service":"ALL","account":"********","baseURI":"server.lon.liveperson.net"},{"service":"faasUI","account":"********","baseURI":"lo.faasui.liveperson.net"},{"service":"redirect","account":"********","baseURI":"z2.liveper.sn"},{"service":"mobileVisit","account":"********","baseURI":"dispatch.look.io"},{"service":"agentVep","account":"********","baseURI":"lo.agentvep.liveperson.net"},{"service":"visitorFeed","account":"********","baseURI":"lo.v-feed.liveperson.net"},{"service":"mTag","account":"********","baseURI":"server.lon.liveperson.net"},{"service":"accountConfigReadWrite","account":"********","baseURI":"lo.ac.liveperson.net"},{"service":"lpEng","account":"********","baseURI":"z2.lpEng.liveperson.net"},{"service":"msgHist","account":"********","baseURI":"lo.msghist.liveperson.net"},{"service":"mobileChat","account":"********","baseURI":"dispatch.look.io"},{"service":"accountConfigReadOnly","account":"********","baseURI":"z2.acr.liveperson.net"},{"service":"msgEwtAPI","account":"********","baseURI":"lo.lp-msgewt.liveperson.net"},{"service":"openPlatform","account":"********","baseURI":"server.lon.liveperson.net"},{"service":"asyncMessaging","account":"********","baseURI":"lo.ams.liveperson.net"},{"service":"conversationVep","account":"********","baseURI":"lo.convep.liveperson.net"},{"service":"provisionGW","account":"********","baseURI":"lo.register.liveperson.net"},{"service":"faasGW","account":"********","baseURI":"lo.faasgw.liveperson.net"},{"service":"engHistDomain","account":"********","baseURI":"lo.enghist.liveperson.net"},{"service":"batchelor","account":"********","baseURI":"z2.batchelor.liveperson.net"},{"service":"routingBot","account":"********","baseURI":"lo.routingbot.ext.liveperson.net"},{"service":"leIntegration","account":"********","baseURI":"lo.octopus.liveperson.net"},{"service":"leBiMstr","account":"********","baseURI":"z2.bi.liveperson.net"},{"service":"mcs","account":"********","baseURI":"mcs.liveperson.net"},{"service":"idp","account":"********","baseURI":"lo.idp.liveperson.net"},{"service":"nlp","account":"********","baseURI":"lo.nlp.liveperson.net"},{"service":"adminArea","account":"********","baseURI":"server.lon.liveperson.net"},{"service":"visitManager","account":"********","baseURI":"lo.vm.liveperson.net"},{"service":"smt","account":"********","baseURI":"lo.v.liveperson.net"},{"service":"coBrowse","account":"********","baseURI":"lo.cobrowse.liveperson.net"},{"service":"intentAnalyzer","account":"********","baseURI":"lo.intent.liveperson.net"},{"service":"agent","account":"********","baseURI":"server.lon.liveperson.net"},{"service":"msdkgw","account":"********","baseURI":"lo.v.liveperson.net"},{"service":"sentinel","account":"********","baseURI":"lo.sentinel.liveperson.net"},{"service":"leCdnDomain","account":"********","baseURI":"lpcdn.lpsnmedia.net"},{"service":"asyncMessagingEnt","account":"********","baseURI":"lo.msg.liveperson.net"},{"service":"pusher","account":"********","baseURI":"lo.push.liveperson.net"}],"taglets":[{"name":"lpMobileLandscape","type":0},{"name":"lpSecureStorage","type":0},{"name":"lp_sdes","type":0},{"name":"scraper","type":0},{"name":"lpActivityMonitor","type":0},{"name":"rendererStub","type":0},{"name":"lp_version_detector","type":0},{"name":"lp_monitoringSDK","type":0},{"name":"lpTransporter","type":0},{"name":"lpUnifiedWindow","type":0},{"name":"SMT","type":0},{"name":"hooks","type":0},{"name":"lp_SMT","type":0},{"name":"authenticator","type":0},{"name":"jsLoader","type":0}],"site":"********","features":{"Common.LiveEngage_2_CrossDomainStorage":false,"Messaging.Auto_Messages":false}});try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lpMobileLandscape=lpTag.taglets.lpMobileLandscape||function(){function l(){lpTag.events.bind("lpUnifiedWindow","conversationInfo",function(){if(!e){var l=document.styleSheets[document.styleSheets.length-1],t=l.cssRules.length;t=l.insertRule(".lp_mobile.lp_landscape #lpChat>.lp_maximized .lp_header, .lp_tablet.lp_landscape #lpChat>.lp_maximized .lp_header {display:block !important;}",t);t=l.insertRule(".lp_mobile.lp_landscape #lpChat>.lp_maximized>.lp_main {top:38px !important; height:calc(100% - 38px)}",t);t=l.insertRule(".lp_tablet.lp_landscape #lpChat>.lp_maximized>.lp_main {top: 8% !important; height: 93% !important; }",t);t=l.insertRule(".lp_landscape .lp_location_bottom {height: 50px !important;}",t);l.insertRule("#lpChat .lp_main .lp_main_area .lp_location_center {bottom: 38px !important;}",t);e=!0}})}var t="lpMobileLandscape",a="1.2",e=!1;return{name:t,_v:a,init:l}}();}catch(e){lpTag.handleGeneralError("lpMobileLandscape",e);}try{window.lpTag=window.lpTag||{};window.lpTag.taglets=window.lpTag.taglets||{};lpTag.taglets.lpSecureStorage=lpTag.taglets.lpSecureStorage||function(e){function t(){}function n(e){e=ae(e);return e&&Be[e]?Be[e]:Be}function r(e){c(e);e.type=Pe.GET;l(e)}function o(e){c(e);e.type=Pe.MULTIGET;l(e)}function a(e){if(!e||-1!==e.expires&&-1!==e.ttl){e.type=Pe.SET;l(e)}else u(e)}function i(e){c(e);e.type=Pe.TOUCH;l(e)}function u(e){if(e){e.ttl=-1;e.expires=-1}e.type=Pe.REMOVE;l(e)}function p(e){var t,n;e&&e.debug===!0&&(xe=!0);for(var r in e)if("object"==typeof e[r]&&e.hasOwnProperty(r)&&e[r].site){e[r].app||(e[r].app=We);if(e[r].url&&0===e[r].url.indexOf("http")){Te("Configuring url "+e[r].url);t=ae(e[r].url);if(Ae[t])if(Be[t]&&Be[t][e[r].app])j(e[r].chosenStorageHandler,Be[t][e[r].app]);else{z(t,e[r].app,e[r].chosenStorageHandler);f(e[r],t)}else{z(t,e[r].app,e[r].chosenStorageHandler);n=s(e[r],t);W(e[r],n,t)}}else e[r].url===ke&&K(e[r])}}function s(e,t){return{type:Pe.SELECT_STORAGE,app:e.app,domain:t,site:e.site,initialStorageType:e.initialStorageType,force:e.force}}function f(e,t){l(s(e,t))}function c(e){if(e&&e.value){e.value=null;delete e.value}return e}function l(e){var t=ae(e.domain);e.app||(e.app=We);if(d(e,t))e.alreadyConfigured&&e.error?j(e.error,"Could not configure storage",e.keys||e.key):p(S(e));else{y(e,t)&&(e.alreadyConfigured=!0);m(e,t)}}function y(e,t){var n=we[t]&&we[t].filter(function(e){return e.type===Pe.SELECT_STORAGE}),r=g(e,t),o=e.type===Pe.SELECT_STORAGE||r||n&&n.length>0;return o}function d(e,t){var n=y(e,t),r=!(!Be[t]||!Be[t][e.app]);return!n&&!r}function g(e,t){if(Ge)for(var n in Ge)if(Ge.hasOwnProperty(n)&&Ge[n].type===Pe.SELECT_STORAGE&&Ge[n].domain===t&&Ge[n].app===e.app)return!0;return!1}function S(e){var t={};t[e.app]={site:e.site,app:e.app,url:e.domain,initialStorageType:e.initialStorageType,chosenStorageHandler:l.bind(this,e)};return t}function m(e,t){var n=e.domain===ke;n?E(e):ze.get(t)?C(e,t):R(e,t)}function T(){lpTag.storageMethods&&!ve&&(ve=lpTag.storageMethods);return ve?ve.isSessionStorageEnabled():!1}function E(e){var t,n;if(T())switch(e.type){case Pe.GET:case Pe.MULTIGET:n=O(e);j(e.success,n,e.keys||e.key);break;case Pe.SET:t=""+e.site+e.key;k(t,e);j(e.success,e.value,e.key);break;case Pe.TOUCH:n=h(""+e.site+e.key,e);j(n?e.success:e.error,n?n:{error:"No data to touch"},e.key);break;case Pe.REMOVE:t=""+e.site+e.key;n={error:"Object not found."};N(t,e)&&(n=void 0);j(n&&n.error&&e.error?e.error:e.success,n&&n.error||n,e.key)}else j(e.error||e.success,{error:"SessionStorage is not active",code:500},e.key)}function v(e){var t=ve.getSessionData(e);try{t=""===t?"":JSON.parse(t)}catch(n){}return""!==t?t:null}function h(e,t){var n=v(e);t.appName&&null!==n&&"object"==typeof n&&(n=n[t.appName]);return""!==n?n:null}function O(e){var t,n,r=!1,o=!1,a={};if(e.keys&&Array.isArray(e.keys)){r=!0;t=e.keys}else t=[e.key];t.forEach(function(t){n=h(""+e.site+t,e);if(n){a[t]=n;o=!0}});a=o?r?a:a[t[0]]:null;return a}function k(e,t){var n=v(e);null===n&&(n={});if("object"==typeof n){n[t.appName]=t.value;return ve.setSessionData(e,B(n))}return!1}function N(e,t){var n=!1,r=!1,o=v(e);if(null!==o&&"object"==typeof o&&o.hasOwnProperty(t.appName)){o[t.appName]=null;delete o[t.appName];for(var a in o)if(o.hasOwnProperty(a)){r=!0;break}n=!0}return n&&r?ve.setSessionData(e,B(o)):n?ve.removeSessionData(e):!1}function R(e,t){var n;if(e&&((e.success||e.error)&&(e.key||e.keys)&&e.appName||e.type===Pe.SELECT_STORAGE)&&e.domain&&e.site)if(Ne)if(Ae[t]){n=e.app&&Be[t]&&Be[t][e.app];if(n&&e.type===Pe.SELECT_STORAGE){M(e.domain,e.app,n);J(e.domain)}else if(!n||n&&!n.error){ye(e);pe(e);Te("Made request for key: "+e.key+" appName: "+e.appName)}else{j(e.error||e.success,null,e.key);Te("No Storage Selected, Blocked request for key: "+e.key+" appName: "+e.appName)}}else W(e,e);else Re.push(function(){l(e)})}function C(e,t){Le[t]="undefined"==typeof Le[t]?[]:Le[t];Le[t].push(e)}function A(){var e,t=(new Date).getTime(),n=!1;if(Ee){clearTimeout(Ee);Ee=null}for(var r in Ge)if(Ge.hasOwnProperty(r)){e=0;e=t-Ge[r].startTime;if(e>=Ue){me("iFrame not responding in time to requests, domain: "+Ge[r].domain);F(r,Ge[r].key,null,!0)}else n=!0}w(n)}function w(e){e&&!Ee&&(Ee=setTimeout(A,Me))}function G(e,t){Ie[e]=t?"undefined"==typeof Ie[e]?1:Ie[e]+1:0;Te("Domain "+e+" error count: "+Ie[e],"_manageErrorState");Ie[e]>Fe&&Z(Le[e],e,"iFrame not responding in time to requests")}function I(e){if(Ae[e]&&Ae[e].parentNode){Ae[e].parentNode.removeChild(Ae[e]);Ae[e]=null;delete Ae[e];Ie[e]=0;me("Removed iFrame for domain","_removeIFrame")}}function b(){document.body?ce():setTimeout(b,0)}function L(e){e&&e.data&&be[e.origin]&&x(e.data,e.origin)}function D(e){Ge[e]=null;delete Ge[e]}function x(e,t){var n,r,o,a,i=V(e);if(i){n=i.id;r=i.key||i.keys;o="undefined"!=typeof i.value?i.value:i.error;if(qe===r){_(i,t);a=ae(t);F(n,r,o,!!i.error,a)}else{F(n,r,o,!!i.error);Te("Got result for key: "+r+" appName: "+i.appName)}}}function _(e,t){var n,r;if(!e.app){if(Ge){for(var o in Ge)if(Ge.hasOwnProperty(o)&&U(t,Ge[o])){n=Ge[o].app;r=o;break}r&&D(r)}if(we&&!r)for(var a in we){we.hasOwnProperty(a)&&a===t&&Array.isArray(we[a])&&we[a].every(function(e,o){if(U(t,e)){n=e.app;r=o;return!1}return!0});if("undefined"!=typeof r){we[a].splice(r,1);break}}e.app=n||We}Be[t]=Be[t]||{};Be[t][e.app]=e.value;Te("Got storage type: "+e.value+" for origin: "+t+" appName: "+e.appName);M(t,e.app,e.value)}function U(e,t){return t.type!==Pe.SELECT_STORAGE||t.domain!==e||t.app!==We&&t.app!==Xe?!1:!0}function M(e,t,n){if(je[e]&&je[e][t]&&je[e][t].length>0){je[e][t].forEach(function(e){j(e,n)});je[e][t]=[]}}function F(e,t,n,r,o){var a=P(e),i=o||a&&ae(a.domain);a&&H(a,n,t,r);e&&D(e);ze.set(!1,i);G(i,r);J(i)}function J(e){Le[e]&&Le[e].length>0&&l(Le[e].shift())}function P(e){return e&&Ge[e]?Ge[e]:void 0}function H(e,t,n,r){r?j(e.error||e.success,t,n):j(e.success,t,n)}function V(e){try{e=decodeURIComponent(e);e=JSON.parse(e)}catch(t){}return e}function B(e){var t,n;if("function"==typeof Array.prototype.toJSON){n=Array.prototype.toJSON;delete Array.prototype.toJSON;try{t=JSON.stringify(e)}catch(r){Array.prototype.toJSON=n;throw r}Array.prototype.toJSON=n}else t=JSON.stringify(e);return t}function j(e,t,n){if("function"==typeof e)try{e(t,n)}catch(r){}}function q(t,n,r){e.addEventListener?t.addEventListener(n,r,!1):t.attachEvent("on"+n,r)}function K(e){var t={app:e.app,value:"NONE"};z(ke,e.app,e.chosenStorageHandler);if(T()){Ae[ke]=!0;t.value=He.STATICSESSIONSTORAGE}_(t,ke)}function W(e,t,n){var r,o=e.url||e.domain,a=e.site;n=n||ae(o);if(!Ae[n])if(we[n])X(n,t);else{Te("Attaching iFrame: "+o+" domain: "+n);r=Q();be[n]=!0;r.setAttribute("src",ne(o,a,e));we[n]=[];X(n,t);Ne?Y(n,r)():Ce.push(Y(n,r))}return r}function X(e,t){t&&we[e].push(t)}function z(e,t,n){if(n){je[e]=je[e]||{};je[e][t]=je[e][t]||[];je[e][t].push(n)}}function Q(){var e=document.createElement("iframe"),t=e.style,n="lpSS_"+Math.floor(9e10*Math.random()),r={tabIndex:"-1","aria-hidden":"true",role:"presentation",title:"Intentionally blank",name:n,id:n};t.width="0px";t.height="0px";t.position="absolute";t.top="-1000px";t.left="-1000px";for(var o in r)r.hasOwnProperty(o)&&e.setAttribute(o,r[o]);return e}function Y(e,t){return function(){var n=setTimeout(function(){Z(we[e],e,"unable to load iFrame for key")},_e);q(t,"load",function(){ee(t,e,n)});document.body.appendChild(t)}}function Z(e,t,n){for(;e.length>0;){var r=e.shift();r.error&&setTimeout($(r.error,n||{error:n,key:r.key},r.key),0)}I(t)}function $(e,t,n){return function(){j(e,t,n)}}function ee(e,t,n){n&&clearTimeout(n);Ae[t]=e;Te("Loaded frame "+t);te(t)}function te(e){Te("Running pending request for frame "+e);if(we[e]){for(;we[e].length>0;)l(we[e].shift());we[e]=null;delete we[e]}}function ne(e,t,n){var r,o,a,i;if(e&&"string"==typeof e){o=ge();a=oe(n);e=re(e);r=0<location.port.length?":"+location.port:"";i=e+"/"+a+"?loc="+encodeURIComponent(location.protocol+"//"+location.hostname+r)+"&site="+encodeURIComponent(t)+(n.initialStorageType?"&ist="+encodeURIComponent(n.initialStorageType):"")+(n.force?"&force=1":"")+(o?"&env="+encodeURIComponent(o):"")+(de()?"&isCrossDomain=true":"")}return i}function re(e){var t,n,r=e.lastIndexOf("/");if(r>8)n=e.substr(0,r);else{t=e.indexOf("?");t>0&&(n=e.substr(0,t))}return n}function oe(e){var t="storage.min.html";xe?t="storage.html":e.env&&(t="storage.secure.min.html");return t}function ae(e){var t,n,r=null;if(e===He.STATICSESSIONSTORAGE)r=e;else{t=new RegExp(/(http{1}s{0,1}?:\/\/){0,1}([^\/\?]+)(\/?)/gi);n=t.exec(e);n&&n.length>=3&&""!==n[2]&&(r=n[1]+n[2].toLowerCase())}return r}function ie(e){var t={};t.id=e.id;e.key?t.key=e.key:e.keys&&(t.keys=e.keys);t.site=e.site;t.appName=e.appName||"*";t.type=e.type;if(e.type===Pe.SELECT_STORAGE){t.initialStorageType=e.initialStorageType;t.force=e.force}isNaN(e.ttl)||(t.ttl=e.ttl);isNaN(e.expires)||(t.expires=e.expires);e.app&&(t.app=e.app);if("undefined"!=typeof e.value){t.value=e.value;t.locations=ue(e.locations)}return t}function ue(e){e=e?e.constructor===Array?e:[e]:[];e.push(document.location.hostname);return e}function pe(e){var t,n=ae(e.domain);if(Ae[n]){e.startTime=(new Date).getTime();t=ie(e);ze.set(!0,n);se(t,n);w(!0)}}function se(e,t){Ae[t]&&Ae[t].contentWindow.postMessage(fe(e),t)}function fe(e){try{e=B(e);e=encodeURIComponent(e)}catch(t){}return e}function ce(){Ne=!0;for(;Ce.length>0;)try{Ce.shift()()}catch(e){}for(;Re.length>0;)try{Re.shift()()}catch(e){}}function le(){De++;De>2e4&&(De=0);return"k"+De+(new Date).getTime()+parseInt(100*Math.random(),10)}function ye(e,t){e.id=t?t:le();"undefined"!=typeof e.value&&(e.type=Pe.SET);Ge[e.id]=e}function de(){return lpTag.features&&"function"==typeof lpTag.features.getFeature&&lpTag.features.getFeature(Ke)}function ge(){return"function"==typeof lpTag.getEnv&&lpTag.getEnv()}function Se(t,n){e.lpTag&&lpTag.log&&lpTag.log(t,n,he)}function me(e){Se(e,Je.ERROR)}function Te(e){Se(e,Je.DEBUG)}var Ee,ve,he="lpSecureStorage",Oe="*******-release_435",ke="STATICSESSIONSTORAGE",Ne=!1,Re=[],Ce=[],Ae={},we={},Ge={},Ie={},be={},Le={},De=0,xe=!1,_e=7e3,Ue=5e3,Me=1e3,Fe=10,Je={ERROR:"ERROR",DEBUG:"DEBUG",INFO:"INFO"},Pe={SET:"set",GET:"get",MULTIGET:"multiget",REMOVE:"remove",TOUCH:"touch",SELECT_STORAGE:"selectStorage"},He={LOCALSTORAGE:"localStorage",SESSIONSTORAGE:"sessionStorage",INDEXEDDB:"indexedDB",COOKIE:"secureSessionCookie",STATICSESSIONSTORAGE:ke},Ve={STORAGE:"storage_error"},Be={},je={},qe="SecureStorageClient/storageType",Ke="Common.LiveEngage_2_CrossDomainStorage",We="default",Xe="cobrowse",ze={set:function(e,t){t&&(this[t]=e)},get:function(e){return e?this[e]:void 0}};q(e,"message",L);b();return{storageTypes:He,getStorageType:n,getValue:r,getValues:o,setValue:a,touchValue:i,removeValue:u,configure:p,sessionStorageStaticDomain:ke,errorTypes:Ve,v:Oe,name:he,init:t,inspect:function(){var e={};for(var t in Ae)Ae.hasOwnProperty(t)&&(e[t]={url:t,storageType:Be[t]});return{name:he,version:Oe,configuredFrames:e}}}}(window);}catch(e){lpTag.handleGeneralError("lpSecureStorage",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lp_sdes=lpTag.taglets.lp_sdes||function(){function e(){var e=lpTag.sdes;lpTag.sdes=lpTag.taglets.lp_sdes;if("undefined"!=typeof e&&e.length)for(var n=0;n<e.length;n++)t(e[n])}function t(e){if("undefined"!=typeof e){"undefined"==typeof e.length&&(e=[e]);for(var t=0;t<e.length;t++)if("object"==typeof e[t]&&e[t].type){var n=s(e[t].type);if("undefined"!=typeof n){n.push(e[t]);f[e[t].type]=n}else f[e[t].type]=[e[t]];d("Added sde type: "+e[t].type);lpTag.events.trigger(o,"VAR_ADDED",e[t])}else g("sde: "+JSON.stringify(e[t])+" is not an Object");lpTag.taglets.lp_monitoringSDK.sdes.push(e)}else g("Called push with no sde")}function n(e,n,s){"undefined"!=typeof e&&t(e);p("called send with sdes: "+JSON.stringify(e),"DEBUG");return lpTag.taglets.lp_monitoringSDK.send(n,s)}function s(e){return e?f[e]:f}function l(){f={};d("sdes store was reset")}function i(){l()}function p(e,t){window.lpTag&&lpTag.log&&lpTag.log(e,t,o)}function g(e){p(e,"ERROR")}function d(e){p(e,"INFO")}var o="lp_sdes",a="2.21.0",f={};return{v:a,name:o,init:e,get:s,push:t,send:n,reset:l,reinit:i,inspect:function(){return{store:s()}}}}();}catch(e){lpTag.handleGeneralError("lp_sdes",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.scraper=lpTag.taglets.scraper||function(){function e(e){if(e)for(var n=0;n<e.length;n++)"object"==typeof e[n]&&(d[e[n].id]=e[n].value)}function n(){lpTag.taglets.jsonp.issueCall({url:lpTag.protocol+"//"+lpTag.csds.getDomain(u.ACCDN)+"/api/account/"+lpTag.site+"/configuration/le-campaigns/zones?fields=id&fields=zoneValue",callbackName:"lpZonesStaticCB",success:function(e){f=e;o()},error:function(){lpTag.log("Loading elements list for scraper failed","ERROR","SCRAPER")}})}function a(){i()}function t(){o()}function l(e){for(var n=0;n<f.length;n++)if(f[n].id===e)return f[n].zoneValue;return null}function r(){return{config:d,zones:f}}function i(){for(var e=0;e<f.length;e++)f[e].scraped=!1}function o(){var e=s();c(e);lpTag.isDom||setTimeout(o,d.scrapeInterval)}function s(){for(var e=[],n=0;n<f.length;n++)if(!f[n].scraped){var a=document.getElementById(f[n].zoneValue);if(null!=a){f[n].scraped=!0;e.push({type:"pagediv",divId:f[n].zoneValue})}}return e}function c(e){if(e.length>0){lpTag.log("zones: "+JSON.stringify(e),"DEBUG","SCRAPER");lpTag.sdes.send(e,function(){lpTag.log("scraper::_send::lpTag.sdes.send success for: "+e.toString(),"DEBUG","SCRAPER")})}}var g="0.3.1",p="scraper",u={ACCDN:"acCdnDomain"},f=[],d={scrapeInterval:50};return{v:g,name:p,init:e,start:n,reinit:a,restart:t,getElmId:l,inspect:r}}();}catch(e){lpTag.handleGeneralError("scraper",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lpActivityMonitor=lpTag.taglets.lpActivityMonitor||function(){function t(t){lpTag.sdes=lpTag.sdes||[];f(t);s(v)}function e(){c("Starting activity tracking");S=!0;O=setTimeout(E,A.timeout)}function n(){c("Called Stop");if(O){clearTimeout(O);S=!1;p("Stopped activity tracking")}T()}function i(t){c("Called push with type="+t);t&&"number"==typeof t?lpTag.sdes.push({type:A.sdeType,input:t}):r("Did not push sde since type is not a number type="+t,"ERROR")}function o(t){c("Called Reinit");u();f(t);v()}function u(){D=0;U=N.UNDEFINED}function a(){return S}function r(t,e){window.lpTag&&lpTag.log&&lpTag.log(t,e,h)}function c(t){r(t,"DEBUG")}function p(t){r(t,"INFO")}function l(t){try{return JSON.parse(JSON.stringify(t))}catch(e){return t}}function s(t){return setTimeout(t,0)}function f(t){if(t)for(var e=0;e<t.length;e++){var n=t[e].value;"string"==typeof n&&""!==n&&("["===n.charAt(0)||"{"===n.charAt(0))&&("undefined"!=typeof JSON&&JSON.parse?n=JSON.parse(n):r("unable to parse JSON, no JSON object on page ","ERROR"));A[t[e].id]=n}else p("No cfg were given on init")}function m(t,e,n){t.addEventListener?t.addEventListener(e,n,!1):t.attachEvent&&t.attachEvent("on"+e,n)}function d(t,e,n){t.removeEventListener?t.removeEventListener(e,n,!1):t.detachEvent&&t.detachEvent("on"+e,n)}function g(t){y&&clearTimeout(y);var e=t.type;y=s(function(){if(I[e]){U=I[e].input;D=(new Date).getTime()}})}function v(){if(!R)for(var t in I)I.hasOwnProperty(t)&&m(I[t].elem,t,g);R=!0}function T(){if(R)for(var t in I)I.hasOwnProperty(t)&&d(I[t].elem,t,g);R=!1}function E(){var t=(new Date).getTime()-D;if(0!==D&&t<=A.timeout){lpTag.events.trigger(h,"ACTIVITY_MONITORED",{inputType:U,time:D});i(U)}O&&clearTimeout(O);O=setTimeout(E,A.timeout)}var O,y,w="2.21.0",h="lpActivityMonitor",S=!1,N={UNDEFINED:0,MOUSE:1,KEYBOARD:2,TOUCH:3,FOCUS:4},D=0,U=N.UNDEFINED,A={timeout:6e4,sdeType:"tabActive"},R=!1,I={click:{elem:document,input:N.MOUSE},mousemove:{elem:document,input:N.MOUSE},wheel:{elem:document,input:N.MOUSE},scroll:{elem:window,input:N.MOUSE},keydown:{elem:document,input:N.KEYBOARD},touchmove:{elem:document,input:N.TOUCH},focus:{elem:window,input:N.FOCUS}};return{v:w,name:h,init:t,start:e,reinit:o,restart:e,stop:n,push:i,reset:u,isActive:a,inputType:N,inspect:function(){return{conf:l(A),lastActivityTime:D,lastInputType:U,intervalId:O}}}}();}catch(e){lpTag.handleGeneralError("lpActivityMonitor",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.rendererStub=lpTag.taglets.rendererStub||function(){function e(){lpTag.log("rendererStub init called","DEBUG","LP_OFFER");D()&&n()}function n(){lpTag.events.bind("LE_ENGAGER","SHOW",c);lpTag.events.bind("lpUnifiedWindow","state",m);lpTag.events.bind("LE_ENGAGER","CHAT",p);lpTag.events.bind("LE_ENGAGER","SURVEY",u);lpTag.events.bind("LE_ENGAGER","OPEN",d);lpTag.events.bind("LP_OFFERS","OFFER_REMOVE",f);lpTag.events.bind("lpUnifiedWindow","STORAGE_SELECTED",J)}function t(e){e=e||{};s(e)}function a(e){var n={};if(z.ids[e]&&z.ids[e].engData){n=JSON.parse(U(z.ids[e].engData));n.engagementId=e}return n}function i(e){var n={engagementId:e,state:0,desc:"NA"};if(z.ids[e]&&z.ids[e].engData&&"undefined"!=typeof z.ids[e].engData.state&&"undefined"!=typeof j[z.ids[e].engData.state]){n.state=z.ids[e].engData.state;n.desc=j[n.state]}return n}function r(e,n,t){var a={},i=e.engagementWindowId||e.windowId||n.windowId;a.site=lpTag.site;a.scid=e.contextId;a.cid=e.campaignId;a.eid=e.engagementId;a.ename=e.engagementName;a.target=t&&t.target||(ee+lpTag.site).replace(/[^a-z0-9]/gi,"_");a.params=t&&t.params||ne;a.svid=lpTag.taglets.lp_monitoringSDK.getVid();a.ssid=lpTag.taglets.lp_monitoringSDK.getSid();a.ssuid=lpTag.taglets.lp_monitoringSDK.getSidPrefix();a.isPopOut="boolean"==typeof n.isPopOut||"true"===n.isPopOut?!!n.isPopOut:!1;a.env=t&&t.env||"function"==typeof lpTag.getEnv&&lpTag.getEnv();a.offlineSurveyId=e.offlineSurveyId;a.async=e.conversationType===ie.MESSAGING;a.allowUnauthMsg=n.allowUnauthMsg;(e.connector||n.connector)&&(a.connector=lpTag.taglets.lpUtil.cloneExtend(e.connector||{},n.connector||{}));n.skillName&&(a.skill=n.skillName);n.skillId&&(a.skillId=n.skillId);i&&(a.lewid=i);n.language&&(a.lang=n.language);(e.forceOffline===!0||e.state===ae.OFFLINE||e.state===ae.BUSY&&n.availabilityPolicy===re.OFFLINE)&&(a.isOffline=!0);if(e.redirect){a.redirect=e.redirect;a.isPopOut=!0}e.ssoKey&&(a.ssoKey=e.ssoKey);e.minimiseOnStart&&(a.minimiseOnStart=!0);e.preChatLines&&(a.preChatLines=e.preChatLines);e.queuePriority&&(a.queuePriority=e.queuePriority);(n.availabilityPolicy===re.OFFLINE||n.availabilityPolicy===re.CHAT)&&(a.availabilityPolicy=n.availabilityPolicy);e[Z]&&(a[Z]=e[Z]);return a}function g(e,n){var t=!1;if(z.ids[e]&&"function"==typeof z.ids[e].offerClick){t=!0;n=n||{};o(n)&&(z.ids[e].engData.preChatLines=n.preChatLines);z.ids[e].offerClick()}return t}function o(e){function n(e){return"string"==typeof e}return e&&e.preChatLines&&Array.isArray(e.preChatLines)&&e.preChatLines.every(n)}function l(e,n){var t;if(e&&n&&n.length)for(var a=0;a<n.length;a++){t=n[a];e[t]=null;delete e[t]}}function s(e){var n,t=[],a=e.idsToKeep||{},i=e.divIdsToKeep||{};for(var r in i)if(i.hasOwnProperty(r)&&z.divIds.hasOwnProperty(r)){n=z.divIds[r];a[n]=!0}for(var g in z.ids)if(z.ids.hasOwnProperty(g)&&!a[g]){try{z.ids[g].cleanupOffer({silent:!0})}catch(o){lpTag.log("Failed to remove engagement, might have been removed already. ex="+o.message,"DEBUG","LP_OFFER")}t.push(g)}l(z.ids,t);Y={}}function f(e){var n=e.engagementId;if(n&&z.ids[n]){z.ids[n]=null;delete z.ids[n]}}function c(e){if(x(e)){lpTag.log("rendererStub received new smt msg. {id:"+e.engagementId+" ,type:"+e.engagementType+",revision:"+e.engagementRevision+",campaignId "+e.campaignId+"}","DEBUG","LP_OFFER");e[Z]=X.SHOW;if("undefined"!=typeof e.controlGroup&&e.controlGroup)T(e);else{P(e);e.customTaglet&&v(e);y(e,F(e).files);I(e)}}}function d(e){if("object"==typeof e){e[Z]=X.OPEN;e.conversationType=q.MESSAGING;e.minimiseOnStart=!0;x(e)?I(e,function(n){e.confKey=A(e);Y[e.confKey]=n;h(n)?C(e):p(e)}):p(e)}}function p(e){e.tglName="conversationInitiator";y(e,e.tglName)}function u(e){e.forceOffline=!0;p(e)}function m(e){lpTag.log("rendererStub received hide engagements event.","DEBUG","LP_OFFER");e&&"chatting"===e.state&&lpTag.events.trigger("LP_OFFERS","HIDE")}function T(e){lpTag.log("rendererStub::controlGroup is true for campaignID: "+e.campaignId+" - engagementId: "+e.engagementId,"DEBUG","LP_OFFER");var n=[{type:"impDisplay",campaign:e.campaignId,engId:e.engagementId,revision:e.engagementRevision,eContext:[{type:"engagementContext",id:e.contextId}]}];lpTag.sdes.send(n,function(){lpTag.log("rendererStub:::impression event send for "+e.campaignId+" - engagementId: "+e.engagementId,"DEBUG","LP_OFFER")});lpTag.events.trigger("LP_OFFERS","CONTROL_GROUP",e)}function y(e,n){lpTag.log("_loadTaglet called with url: "+JSON.stringify(e),"DEBUG","LP_OFFER");var t=!1,a={},i=lpTag.protocol+"//"+lpTag.csds.getDomain(W.LPCDN)+"/le_re/";k&&(i+=k+"/");i+="jsv2/";if(n){n=Array.isArray(n)?n:[n];for(var r=0;r<n.length;r++)if("string"==typeof n[r]&&!lpTag.taglets[n[r]]){a[n[r]]=i+n[r]+".js?_v="+k;t=!0}}t?lpTag.taglets.jsLoader.loadJS({loadObj:a,success:O(e),error:E(e)}):R(e)}function v(e){lpTag.log("_loadCustomTaglet called with url: "+JSON.stringify(e),"DEBUG","LP_OFFER");lpTag.taglets[e.tglName]?R(e):"function"==typeof lpTag.loadTaglet&&lpTag.loadTaglet({name:e.tglName,success:O(e),error:E(e)})}function O(e){return function(){lpTag.log("_loadTagletSuccess taglet not exist, isReady: "+S(e),"DEBUG","LP_OFFER");R(e)}}function E(e){return function(n){lpTag.log("Loading taglet: "+e.tglName+" failed. Error: "+JSON.stringify(n),"ERROR","LP_OFFER")}}function I(e,n){var t=lpTag.protocol+"//"+lpTag.csds.getDomain(W.ACCDN)+"/api/account/"+lpTag.site+"/configuration/le-campaigns/campaigns/"+e.campaignId+"/engagements/"+e.engagementId+"/revision/"+e.engagementRevision+"?v="+ge;lpTag.log("_loadEngagementConfig called with url: "+t+" msg: "+JSON.stringify(e),"DEBUG","LP_OFFER");Y[e.confKey]?R(e):lpTag.taglets.jsonp.issueCall({url:t,data:{flavor:"dependency"},callbackName:"lp"+e.engagementId,success:function(t){if(K(t)&&B(t)){lpTag.log("Engagement "+e.engagementId+" loaded","DEBUG","LP_OFFER");Y[e.confKey]=t;if("function"==typeof n)n(t);else{lpTag.log("_loadEngagementConfig conf not exist, isReady: "+S(e),"DEBUG","LP_OFFER");R(e)}}},error:function(){lpTag.log("Loading engagement: "+e.engagementId+"   failed.  id : ","ERROR","LP_OFFER")}})}function S(e){return!(!lpTag.taglets[e.tglName]||!N(e)||e.instantiated||!Y[e.confKey]&&"conversationInitiator"!==e.tglName)}function N(e){for(var n=F(e).taglets||[],t=!0,a=0;a<n.length;a++)if(!lpTag.taglets[n[a]]){t=!1;break}return t}function F(e){var n=e.customTaglet?"customTaglet":e.tglName;return Q[n]&&Q[n]||{}}function R(e){if(!lpTag.taglets.lpUnifiedWindow||oe){lpTag.log("_execTaglet, is ready:"+S(e)+" msg: "+JSON.stringify(e),"DEBUG","LP_OFFER");var n;if(S(e)){lpTag.log("taglet "+e.tglName+" executed by rendererStub at: "+(new Date).getTime()+"(number of milliseconds since 1970/01/01)","DEBUG","LP_OFFER");e.instantiated=!0;n=L(e);n&&_(e)}}else le.push(e)}function L(e){var n=!0;e.engagementId&&z.ids[e.engagementId]&&(n=b(e,e.engagementId,z.ids));return n}function b(e,n,t){var a,i=!0;if(e.confKey!==t[n].engData.confKey||e.state!==t[n].activeState.type){a=t[n]||{};e.prevState=a.engData&&a.engData.state;a.cleanupOffer({silent:!0});l(t,[n])}else{t[n].setContextId(e.contextId);i=!1}return i}function C(e){var n=G(Y[e.confKey]),t=G(e),a={configuration:n.windowConf.json.externalConfiguration,args:r(t,n),errorCallback:function(){lpTag.log("Error while opening an external channel")}};try{lpTag.taglets.lpUtil.runCallbackByObject(a.configuration,a.args,a.errorCallback)}catch(i){lpTag.log("Exception while opening an external channel: "+i.message)}}function _(e,n){var t,a,i=G(Y[e.confKey]);if(e.customTaglet){a=new lpTag.taglets.baseOffer;a.init(G(e),i)}try{if(n&&n.ssoKey){e.ssoKey=n.ssoKey;e.redirect_uri=n.redirect_uri||te}t=lpTag.taglets[e.tglName].createInstance(G(e),i,a)}catch(r){lpTag.log("Failed to create engagement instance. ex="+r.message,"ERROR","LP_OFFER")}if(t&&"function"==typeof t.cleanupOffer){z.ids[e.engagementId]=t;t.parentContainer&&(z.divIds[t.parentContainer]=e.engagementId)}}function h(e){return e.windowConf&&e.windowConf.type===$&&e.windowConf.json.externalConfiguration?!0:!1}function P(e){e.confKey=A(e);e.customTaglet=w(e);var n="";M[e.engagementType]&&(n=M[e.engagementType]);e.tglName=e.customTaglet?e.tglName:n}function w(e){return e.renderingType===V.TAGLET}function D(){var e=navigator.userAgent;e=e.toLowerCase();var n=/(msie) ([\w.]+)/,t=n.exec(e);if(null!=t){var a=parseInt(t[2],10);if(10>=a)return!1}return!0}function G(e){return e&&JSON.parse(U(e))}function U(e){var n;if("function"==typeof Array.prototype.toJSON){var t=Array.prototype.toJSON;delete Array.prototype.toJSON;try{n=JSON.stringify(e)}catch(a){Array.prototype.toJSON=t;throw a}Array.prototype.toJSON=t}else n=JSON.stringify(e);return n}function A(e){return e&&e.campaignId+"_"+e.engagementId+"_"+e.engagementRevision}function x(e){if(e&&e.campaignId&&e.engagementId&&e.engagementRevision)return!0;lpTag.log("Missing engagement parameters: campaignId, engagementId, engagementRevision; "+JSON.stringify(e),"ERROR",H);return void 0}function K(e){if("object"==typeof e&&!e.error)return!0;lpTag.log("Error in engagement config: "+JSON.stringify(e),"ERROR",H);return void 0}function B(e){return!e.allowUnauthMsg||e.allowUnauthMsg&&lpTag&&lpTag.taglets&&lpTag.taglets.unAuthMessaging}function J(){oe=!0;le.forEach(R);le=[]}var k="3.27.0.0-release_2916",H="rendererStub",M={0:"peeling",1:"overlay",2:"toaster",3:"slider",5:"overlay",6:"overlay"},j={0:"na",1:"online",2:"offline"},W={CHAT:"CHAT",ACCDN:"acCdnDomain",LPCDN:"leCdnDomain"},V={INTERNAL:0,HTML:1,TAGLET:2},q={LIVE_CHAT:0,MESSAGING:1},Y={},z={ids:{},divIds:{}},Q={peeling:{files:["peeling","UISuite"],taglets:["peeling","baseOffer","baseUIOffer"]},overlay:{files:["overlay","UISuite"],taglets:["overlay","baseOffer","baseUIOffer"]},toaster:{files:["toaster","UISuite"],taglets:["toaster","baseOffer","baseUIOffer"]},slider:{files:["slider","UISuite"],taglets:["slider","baseOffer","baseUIOffer"]},customTaglet:{files:["baseOffer"],taglets:["baseOffer"]},conversationInitiator:{taglets:["baseOffer"]}},X={OPEN:"OPEN",SHOW:"SHOW"},Z="eventName",$="external app",ee="LiveEngageChat_",ne="height=650,width=330,menubar=no,resizable=no",te="https://liveperson.net",ae={BUSY:4,CONTENT:3,OFFLINE:2,ONLINE:1},ie={CHAT:0,MESSAGING:1},re={OFFLINE:0,CHAT:1},ge="3.0",oe=!1,le=[];return{_v:k,_name:H,init:e,onBeforeNavigation:t,getEngagementState:i,getEngagementInfo:a,getClickObject:r,click:g}}();}catch(e){lpTag.handleGeneralError("rendererStub",e);}try{window.lpTag=lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lp_version_detector=function(){function e(e,n){window.lpTag&&lpTag.log&&lpTag.log(e,n,p)}function n(n){e(n,"ERROR")}function a(n){e(n,"INFO")}function o(e,n){if("undefined"!=typeof n){if(e)for(var a=0;a<e.length;a++){var o=e[a].value;if("string"==typeof o&&""!==o)if("true"===o)o=!0;else if("false"===o)o=!1;else if("["==o.charAt(0)||"{"==o.charAt(0))try{"undefined"!=typeof JSON&&JSON.parse?o=JSON.parse(o):lpTag.log("unable to parse JSON, no JSON object on page ","ERROR",p)}catch(t){lpTag.log("unable to parse JSON:"+t,"ERROR",p)}n[e[a].id]=o}}else lpTag.log("_config is passed as undefined","ERROR",p)}function t(e){o(e,s)}function i(){s.sendUDE&&g({name:"lpTagVer",value:lpTag._v});s.sendSDE&&l({type:"lpTagVer",version:lpTag._v})}function l(e){window.lpTag.sdes=window.lpTag.sdes||[];lpTag.sdes.push(e)}function g(e){window.lpMTagConfig=window.lpMTagConfig||{};window.lpMTagConfig.pageVar=window.lpMTagConfig.pageVar||[];try{e.value=""+e.value;var o=encodeURIComponent(e.name)+"="+encodeURIComponent(e.value);lpMTagConfig.pageVar.push(o);a("Added variable scope:page name:"+e.name+" value:"+e.value)}catch(t){n("Exception in adding variable e="+t)}}var p="lp_version_detector",r="1.0.1",s={sendSDE:!1,sendUDE:!0};return{getVersion:function(){return r},getName:function(){return p},init:t,start:i,inspect:function(){return{conf:s}}}}();}catch(e){lpTag.handleGeneralError("lp_version_detector",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lp_monitoringSDK=lpTag.taglets.lp_monitoringSDK||function(){function e(e){S();Ce=!0;if(e)for(var n=0;n<e.length;n++){var r=e[n].value;"string"==typeof r&&""!==r&&("["===r.charAt(0)||"{"===r.charAt(0))&&("undefined"!=typeof JSON&&JSON.parse?r=JSON.parse(r):T("unable to parse JSON, no JSON object on page "));Ve[e[n].id]=r}else C("No cfg were given on init");Ve.useStorage=Ve.useStorage||!Fe.isCookieEnabled();var i={frames:{url:Ve.protocol+Ve.baseUrl+Ve.frameName}};lpTag.taglets.postmessage.configure(i);Ve.useSecureStorage=lpTag.features&&lpTag.features.getFeature(Ae);!Oe&&Ve.useSecureStorage&&t()}function t(){var e=lpTag.csds.getDomain("leCdnDomain"),t="function"==typeof lpTag.getEnv&&lpTag.getEnv(),r={debug:Ve.debug};Pe=lpTag.taglets.lpSecureStorage;Ve.secureStorageLocation="https://"+e+"/le_secure_storage/"+(Pe.v?Pe.v+"/":"");r[je]={env:t,site:Ve.accountId,app:je,asyncStorageMaxRetry:15,url:Ve.secureStorageLocation,chosenStorageHandler:n};Pe.configure(r);Oe=!0}function n(e){e&&e.error&&(Ve.secureStorageUnSupported=!0)}function r(e,t){C("Called startPage onSuccess: "+e);var n={type:we.startPage,onSuccess:e,onError:t,isSync:!0};Ve.useSecureStorage?F(i.bind(this,n)):i(n)}function i(e){var t=le(e);Me.identities(function(n){n&&n.length>0&&(t.data.identities=n);if(fe){e.rid=t.rid;e.result={error:500,message:"startPage request was already sent once"};e.status=xe.err;N(e)}else de(t);fe=!0;lpTag.events.trigger(Ee,"SP_SENT")})}function a(e,t){var n;I("Called pageLoaded onSucess is: "+e);var r={type:we.pageLoaded,onSuccess:e,onError:t,isSync:!0};n=le(r);if(fe&&!Se)ne(n);else{r.rid=n.rid;r.result={error:500,message:"pageLoaded request was already sent once"};r.status=xe.err;N(r)}Se=!0}function o(e,t){I("Called inPage onSucess is: "+e);var n={type:we.inPage,onSuccess:e,onError:t,isSync:!0},r=le(n);if(fe&&Se)ne(r);else{n.rid=r.rid;n.result={error:500,message:"Cannot issue inPage request when startPage and pageLoaded were not sent"};n.status=xe.err;N(n)}}function s(e,t){I("Called send, onSuccess: "+e);var n={type:we.updateContext,onSuccess:e,onError:t,isSync:!0};if(Re.length>0){var r=le(n);ne(r);return!0}return!1}function c(e){I("Called push with sdes: "+JSON.stringify(e));if("undefined"!=typeof e&&e instanceof Array)for(var t=0;t<e.length;t++)"object"==typeof e[t]&&e[t].type?Re.push(e[t]):T("push: SDE when calling is not an Object")}function u(){var e=d();return e?e.substring(0,e.indexOf(".")):void 0}function d(){return Ie||(Ve.useStorage?_e.getSessionData("LPSID-"+Ve.accountId)||null:x("LPSID-"+Ve.accountId))}function l(){return Te||(Ve.useStorage?_e.getSessionData("LPVID")||null:x("LPVID"))}function g(){return Ve.thirdPartyEnabled}function p(e,t,n){-1===e.indexOf("?")?e+="?":"&"!==e[e.length-1]&&(e+="&");t||(t="SVID");n||(n="SSID");d()&&(e+=encodeURIComponent(n)+"="+encodeURIComponent(d()));l()&&(e+="&"+encodeURIComponent(t)+"="+encodeURIComponent(l()));return e}function f(e){var t;if("function"==typeof Array.prototype.toJSON){var n=Array.prototype.toJSON;delete Array.prototype.toJSON;try{t=JSON.stringify(e)}catch(r){Array.prototype.toJSON=n;throw r}Array.prototype.toJSON=n}else t=JSON.stringify(e);return t}function S(){I("Called reset");Ve={thirdPartyEnabled:void 0,protocol:lpTag.protocol?lpTag.protocol+"//":0===document.location.toString().indexOf("https:")?"https://":"http://",baseUrl:lpTag.getDomain("smt"),apiPath:"/api/js",rdrPath:"/rdr",frameName:"/postmessage/postmessage.min.html",accountId:lpTag.site||{},rmSession:!1,timeout:5e4,debug:!1,shadow:!1};Re=Re||[];ge=[];pe=void 0;Je={};fe=!1;Se=!1;me=!1;ye=!1;ve=!1;Oe=!1;he=0;Te=void 0;Ie=void 0;clearTimeout(De);De=void 0;be={count:0,max:6,timeout:10}}function m(){clearTimeout(De);De=void 0;Ce=!1}function y(t){I("reinit called");e(t);ke=D()}function v(){return{conf:Ve,retry:be}}function h(e,t){window.lpTag&&lpTag.log&&lpTag.log(e,t,"lp_monitoringSDK")}function T(e){h(e,"ERROR")}function I(e){h(e,"DEBUG")}function C(e){h(e,"INFO")}function O(){he++;return he}function D(){return Math.abs(Math.round(***********Math.random()))}function b(e){Ce?lpTag.taglets.lpAjax.issueCall(e):T("Monitoring session not active, skipping request: "+JSON.stringify(e))}function P(){var e=_e.getSessionData("lpTabId");if(!e){e=D();_e.setSessionData("lpTabId",e)}return e}function N(e){var t,n=Je[e.rid],r=e.result;r&&r.body&&(r=r.body);if(n){I("Called _monitoringCb with type= "+e.type+" ,result="+JSON.stringify(r));G(r);V();$();lpTag.events.trigger({appName:Ee,eventName:"DEBUG_STATUS",data:{debug:Ve.debug},aSync:!0});lpTag.events.trigger({appName:Ee,eventName:"REQUEST_COMPLETE",data:{type:e.type,status:e.status},aSync:!0});var i=_(r,n.req);if(!i){delete r.sdkConf;be.count=0;Je[e.rid]=null;delete Je[e.rid];t=z(r,e.status);E({success:t,result:r,cbs:n,response:e})}}else T("Failed on _monitoringCb, request id does not exist in map, rid="+e.rid)}function E(e){if(e.success){w(e.result);if(e.cbs.cOnSuccess)try{e.cbs.cOnSuccess(e.result)}catch(t){T("Failed calling client onSuccess, _monitoringCb e="+JSON.stringify(t.message))}}else if(e.cbs.cOnError)try{e.cbs.cOnError(e.result)}catch(t){T("Failed calling client onError, _monitoringCb e="+JSON.stringify(t.message))}e.response.type===we.startPage?se(e.success):e.response.isSync?oe():ue()}function _(e,t){var n=e.sdkConf&&e.sdkConf.retry;if(n){De&&clearTimeout(De);isNaN(n.max)||(be.max=parseInt(n.max,10));isNaN(n.timeout)||(be.timeout=parseInt(n.timeout,10));if(be.count<be.max){C("retrying count="+be.count+" max="+be.max);be.count++;De=setTimeout(function(){t.data.rc=be.count;H(t);b(t)},1e3*be.timeout);return!0}T("exceeded max retries: "+be.max+", stopping")}}function w(e){var t={};if(e.messagesToVisitor&&!Ve.shadow)for(var n=0;n<e.messagesToVisitor.length;n++){t=e.messagesToVisitor[n];try{lpTag.events.trigger(t.destination,t.subject,t.content)}catch(r){T("Failed on _handleMsgs when triggering dest="+t.destination+" ,subject="+t.subject+" ,content= "+t.content+" ,e="+JSON.stringify(r.message))}}}function x(e){return Fe.readCookie(e)||null}function L(e,t,n,r,i){n&&(n=60*n*60*24);Fe.writeSessionCookie(e,t,n,r,i)}function R(e,t,n){x(e)&&Fe.clearCookie(e,t,n)}function V(){Ve.rmVisitor&&U();Ve.rmSession&&q();Ve.lpVisitorId&&k(Ve.lpVisitorId);Ve.lpSessionId&&J(Ve.lpSessionId);Ve.useSecureStorage&&Oe&&!Ve.secureStorageUnSupported&&A()}function J(e){Ie=e;Ve.useStorage?_e.setSessionData("LPSID-"+Ve.accountId,e):L("LPSID-"+Ve.accountId,e,void 0,"/",Ve.domain);Ve.lpSessionId=void 0}function k(e){Te=e;Ve.useStorage?_e.setSessionData("LPVID",e):L("LPVID",e,730,"/",Ve.domain);Ve.lpVisitorId=void 0}function q(){delete Ve.lpSessionId;Ie=null;_e.removeSessionData("LPSID-"+Ve.accountId);R("LPSID-"+Ve.accountId,"/",Ve.domain)}function U(){delete Ve.lpVisitorId;Te=null;_e.removeSessionData("LPVID");R("LPVID","/",Ve.domain)}function A(){(Te||Ie)&&Pe.setValue({key:Ue,site:Ve.accountId,app:je,value:{vid:Te,sid:Ie},success:K,error:B,appName:Ee,domain:Ve.secureStorageLocation})}function F(e){Pe.getValue({key:Ue,site:Ve.accountId,app:je,success:j.bind(this,e),error:M.bind(this,e),appName:Ee,domain:Ve.secureStorageLocation})}function j(e,t){Te=t&&t.vid;Ie=t&&t.sid;e()}function M(e,t){B(t);e()}function K(e){C("Setting data on secureStorage.setValue in _setDataInSecureStorage. Data = "+JSON.stringify(e))}function B(e){T("Failed on secureStorage get/setValue. Error = "+JSON.stringify(e))}function $(){var e=Ve.lpLastVisit;if(e){_e.setPersistentData("lpLastVisit-"+Ve.accountId,e);Ve.lpLastVisit=void 0}}function G(e){if(e.sdkConf)for(var t in e.sdkConf)Ve[t]=e.sdkConf[t]}function z(e,t){return t===xe.ok&&!e.error}function H(e){var t=l();t&&(e.data.vid=t);var n=d();n&&(e.url=W(e.url,"sid",n));return e}function Q(e){var t;if(e.data.t===we.startPage){t=_e.getPersistentData("lpLastVisit-"+Ve.accountId);t&&(e.data.rvt=t)}}function W(e,t,n){if(e.indexOf(t)>-1){var r=new RegExp("([?&])"+t+"=.*?(&|$)","i");return e.replace(r,"$1"+encodeURIComponent(t)+"="+encodeURIComponent(n)+"$2")}var i=encodeURIComponent(t)+"="+encodeURIComponent(n);e+=e.indexOf("?")===e.length-1?i:e.indexOf("?")>-1&&e.indexOf("?")<e.length?"&"+i:"?"+i;return e}function X(e,t){function n(e){i-=1;a.push(e);0>=i&&t(a)}var r=e.filter(function(e){return"function"==typeof e}),i=r.length,a=[];i>0?r.forEach(function(e){try{e(n)}catch(t){n()}}):n()}function Y(e){I("Called _prepareRequest with params: "+JSON.stringify(e));var t,n;if(e.data)t=e.data.sdes;else{t=Re;Re=[]}n=ee(e);H(n);Q(n);e.type===we.startPage&&te(n);t=Z(t);t&&t.length>0&&(n.data.sdes=t);var r=Me.debug();r&&(n.data.dbg=r);return n}function Z(e){var t,n=Array.isArray(e)&&e.length>0;if(n&&lpTag.hooks&&lpTag.hooks.exec){t=lpTag.hooks.exec({name:Le.BEFORE_SEND_SDE,data:{sdes:e}});e=t&&t.data&&Array.isArray(t.data.sdes)&&t.data.sdes}return e}function ee(e){return{rid:e.rid,data:{t:e.type,ts:(new Date).getTime(),pid:ke,tid:qe},method:"POST",encoding:lpTag.charset||"UTF-8",transportOrder:["jsonp","postmessage"],success:function(t){e.result=t;e.status=xe.ok;N(e)},error:function(t){e.result=t;e.status=xe.err;N(e)},url:Ve.protocol+Ve.baseUrl+Ve.apiPath+"/"+Ve.accountId+"?"}}function te(e){e.timeout=Ve.timeout;var t=Me.title();t&&(e.data.pt=t);var n=Me.url();n&&(e.data.u=n);var r=Me.referrer();r&&(e.data.r=r);var i=Me.section();i&&(e.data.sec=i);lpTag.device&&lpTag.device.family&&(e.data.df=lpTag.device.family());lpTag.device&&lpTag.device.os&&(e.data.os=lpTag.device.os());var a=Me.emtVisitorId();a&&(e.data.evid=a)}function ne(e){ge.push(e);ie()}function re(e,t){if(e.error)try{e.error(t)}catch(n){T("Failed calling client onError _callClientCbOnError e="+n)}}function ie(){if(!pe&&ge.length>0&&me){I("Called _processPendingSyncRequests, initiating a new sync request: "+ge.length+" inProgress: "+JSON.stringify(pe));var e=ge.shift();e=H(e);pe=e;try{b(e)}catch(t){T("Failed on lpAjax.issueCall in _processPendingSyncRequests. e: "+JSON.stringify(t.message));re(e,{error:{code:"500",message:"error while issue request: "+JSON.stringify(t.message)}})}}else C("Called _processPendingSyncRequests no sync requests to initiate, pending: "+ge.length+" inProgress: "+JSON.stringify(pe))}function ae(){I("Called _syncRequestFinished");pe=void 0}function oe(){ce()}function se(e){I("Called _initPageCb isSuccess: "+e);e&&(me=!0);ce()}function ce(){ae();ie()}function ue(){I("Called _asyncRequestCb");ve=!1}function de(e){pe=e;try{lpTag.taglets.lpAjax.issueCall(e)}catch(t){T("Failed on lpAjax.issueCall in _sendInitializingRequest. requestId: "+e.rid+", e="+JSON.stringify(t.message))}}function le(e){e.rid=O();var t=Y(e);Je[e.rid]={req:t,cOnSuccess:e.onSuccess,cOnError:e.onError};return t}var ge,pe,fe,Se,me,ye,ve,he,Te,Ie,Ce,Oe,De,be,Pe,Ne="2.21.0",Ee="lp_monitoringSDK",_e=lpTag.storageMethods,we={startPage:"sp",pageLoaded:"pl",inPage:"ip",updateContext:"uc"},xe={err:"ERROR",ok:"OK"},Le={BEFORE_SEND_SDE:"BEFORE_SEND_SDE"},Re=[],Ve={},Je={},ke=D(),qe=P(),Ue="lpsmt",Ae="Common.LiveEngage_2_CrossDomainStorage",Fe=lpTag.cookieMethods,je="monitoringSDK";lpTag.identities=lpTag.identities||[];lpTag.identitiesSupport=!0;var Me={referrer:function(){var e;document.referrer&&document.referrer.length>0&&(e=document.referrer);return e},title:function(){var e;document.title&&document.title.length>0&&(e=document.title);return e},url:function(){var e;lpTag.url?e=lpTag.url:window.location.href&&window.location.href.length>0&&(e=window.location.href);return e},debug:function(){var e=_e.getPersistentData("lpDebug-"+Ve.accountId);if(e&&!ye){ye=!0;return!0}return!1},section:function(){var e=lpTag.section;"string"==typeof e&&e.length&&(e=[e]);return e},emtVisitorId:function(){return x(Ve.accountId+"-VID")},identities:function(e){X(lpTag.identities,function(t){e(t.filter(function(e){return!!e&&"undefined"!=typeof e.acr}).map(function(e){var t={iss:e.iss,acr:e.acr};e.sub&&(t.sub=e.sub);return t}))})}};return{v:Ne,name:Ee,inspect:v,init:e,sdes:{push:c},hooks:Le,appendCtx:p,isTPCEnabled:g,getVid:l,getSid:d,getSidPrefix:u,send:s,inPage:o,pageLoaded:a,startPage:r,stop:m,reset:S,reinit:y,stringify:f}}();}catch(e){lpTag.handleGeneralError("lp_monitoringSDK",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lpajax_utils={_name:"lpajax_utils",_v:"0.1",each:function(e,t,r){if(null!=e){var a=Array.prototype.forEach;if(a&&e.forEach===a)e.forEach(t,r);else if(e.length===+e.length){for(var n=0,o=e.length;o>n;n++)if(n in e&&t.call(r,e[n],n,e)==={})return}else for(var l in e)if(Object.prototype.hasOwnProperty.call(e,l)&&t.call(r,e[l],l,e)==={})return}},extend:function(e){this.each(Array.prototype.slice.call(arguments,1),function(t){for(var r in t)e[r]=t[r]});return e},isEmptyObj:function(e){for(var t in e)return!1;return!0},init:function(){}};window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lpAjax=lpTag.taglets.lpAjax||function(e){function t(){m=!0}function r(t,r){e.lpTag&&lpTag.log&&lpTag.log(t,r,f)}function a(e,t){if(g[e])r("Existing transport: "+e+" tried to register",h.DEBUG);else{g[e]=t;r("Added transport: "+e,h.DEBUG)}}function n(e){m||t();var a="unknown";try{var n=i(e);if(n){n.issueCall(e);return!0}r("No Transport found to issueCall",h.ERROR);c(h.ERROR,e.error,{responseCode:601,error:"No Transport found to issueCall, request: "+e.url,body:"ERROR"},e.context)}catch(o){n&&n.getName&&(a=n.getName());r("Transport - "+a+" - unknown exception while issueCall",h.ERROR);c(h.ERROR,e.error,{responseCode:600,error:"Transport - "+a+" - unknown exception while issueCall: "+e.url+" e="+o,body:"ERROR"},e.context)}}function o(e){m||t();for(var r in e){var a=g[r];a&&a.configure(e[r])}}function l(e){if(e&&"object"==typeof e){e.appName=p;e.ts=(new Date).getTime();e.tags&&e.tags.constructor===Array&&e.tags.push({pageId:y});r(e,h.METRICS)}}function i(e){for(var t,r=!1,a=-1,n=0;n<e.transportOrder.length;n++)if(!r){t=u({},e);var o=g[t.transportOrder[n]];if(o&&o.isValidRequest&&o.isValidRequest(t)){r=!0;a=n}}return r?g[t.transportOrder[a]]:null}function s(e,t,r){if(null!=e){var a=Array.prototype.forEach;if(a&&e.forEach===a)e.forEach(t,r);else if(e.length===+e.length){for(var n=0,o=e.length;o>n;n++)if(n in e&&t.call(r,e[n],n,e)==={})return}else for(var l in e)if(Object.prototype.hasOwnProperty.call(e,l)&&t.call(r,e[l],l,e)==={})return}}function u(e){s(Array.prototype.slice.call(arguments,1),function(t){for(var r in t)e[r]=t[r]});return e}function c(e,t,a,n){if("function"==typeof t)try{t.call(n||null,a);t=null}catch(o){r("runCallback: Exception in execution of callback, type :"+e+" e=["+o.message+"]",h.ERROR)}else r("runCallBack: No callback, of type :"+e,h.INFO)}var d="1.1.3",f="lpAjax",p="lpTransporter",g={},m=!1,h={ERROR:"ERROR",DEBUG:"DEBUG",INFO:"INFO",METRICS:"METRICS"},y="lpT"+Math.floor(1e5*Math.random())+"_"+Math.floor(1e6*Math.random());return{getVersion:function(){return d},getName:function(){return f},init:t,publishMetrics:l,issueCall:n,configureTransports:o,addTransport:a}}(window);window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.jsonp=lpTag.taglets.jsonp||function(e){function t(){if(lpTag&&lpTag.taglets&&lpTag.taglets.lpAjax)try{lpTag.taglets.lpAjax.addTransport(W,z)}catch(e){}E()}function r(e){if(e)for(var r in e)D.hasOwnProperty(r)&&e.hasOwnProperty(r)&&(D[r]=e[r]);t()}function a(t){var r=!1;if(M&&t&&t.url){var a=!1;t.callbackName&&"string"==typeof t.callbackName&&(Q[t.callbackName]||e[t.callbackName])&&(a=!0);var n;try{n=c(t)}catch(o){k("Could not evaluate the length  of the request, e="+o,L.ERROR,"isValidRequest");r=!1}"undefined"!=typeof n&&U>n&&!a&&(r=!0)}return r}function n(e){var t;if(!a(e)){k("URL request was too long or static callback name already exists, url: "+t,L.ERROR,"issueCall");T();e&&e.error&&F(L.ERROR,e.error,s(600,"Transport - JSONP - unable to run request: "+e.url),e.context);return!1}e=l(e);e.callbackName&&"string"==typeof e.callbackName?e.retries=0:e.callbackName=q+i();t=e.url+(e.url.indexOf("?")>-1?"&":"?")+e.callback+"="+e.callbackName;e.data&&(t+="&"+d(e.data));e.query&&(t+="&"+d(e.query));e.callUrl=t;if(p(e)){I(e);g()}else k("URL request was too long and was not sent, url: "+t,L.ERROR,"issueCall");return!0}function o(){var e={};for(var t in D)D.hasOwnProperty(t)&&(e[t]=D[t]);return e}function l(e){if("string"==typeof e){var t=e;e={url:t}}if(!e.url)return!1;e.encoding=e.encoding||D.encoding;e.callback=e.callback||D.callback;e.retries="number"==typeof e.retries?e.retries:D.retries;e.timeout=e.timeout?e.timeout:D.timeout;return e}function i(e){var t,r=99999,a="x";t=e?r+a+r:Math.round(Math.random()*r)+a+Math.round(Math.random()*r);return t}function s(e,t){return{statusCode:e,responseCode:e,error:t,body:"ERROR"}}function u(){return"scr"+Math.round(999999999*Math.random())+"_"+Math.round(999999999*Math.random())}function c(e){var t=H;e.callbackName&&"string"==typeof e.callbackName&&(t=e.callbackName.length);return 4+(e.callback||D.callback).length+e.url.length+t+d(e.data).length+d(e.query).length}function d(e){var t="";if("string"==typeof e)t+=e;else{var r=!0;for(var a in e){var n;"object"==typeof e[a]?n=f(e[a]):"function"!=typeof e[a]&&(n=e[a]);if("undefined"!=typeof n){r||(t+="&");t+=encodeURIComponent(a)+"="+encodeURIComponent(n);r=!1}}}return t}function f(e){var t;if("function"==typeof Array.prototype.toJSON){var r=Array.prototype.toJSON;delete Array.prototype.toJSON;try{t=JSON.stringify(e)}catch(a){Array.prototype.toJSON=r;throw a}Array.prototype.toJSON=r}else t=JSON.stringify(e);return t}function p(t){var r,a=!1,n=new RegExp(/(http{1}s{0,1}?:\/\/)([^\/\?]+)(\/?)/gi);r=n.exec(0===t.callUrl.indexOf("http")?t.callUrl:e.location.href);if(r&&r.length>=3&&""!==r[2]){var o=r[2].toLowerCase();t.domainMatch=o;_[o]=_[o]||[];_[o].inFlight=_[o].inFlight||0;_[o].push(t);a=!0;G+=1;k("buffered URL: "+t.callUrl,L.DEBUG,"lpTag.taglets.jsonp.bufferRequest")}else k("NO MATCH for URL: "+t.callUrl,L.ERROR,"lpTag.taglets.jsonp.bufferRequest");return a}function g(){var e;for(var t in _)if(_.hasOwnProperty(t)){e=_[t];for(var r=!1;!r&&e.inFlight<6&&e.length>0;){var a=e.shift();if(a){k("Sent URL: "+a.callUrl,L.DEBUG,"lpTag.taglets.jsonp.sendRequests");a.scriptId=h(a.callUrl,a.encoding,a.callbackName);a.startTime=(new Date).getTime();y(t,a.callbackName,a.timeout);G-=1}else r=!0}}e=null}function m(){clearTimeout(A);A=null;var t=new Date;for(var r in Q)if(Q.hasOwnProperty(r)&&Q[r].launchTime){var a=t-Q[r].launchTime;(Q[r].loadTime||a>Q[r].timeout)&&e[r].apply(null,[s(408,{message:"Request timed out",name:"timeout"}),!0])}P>0&&(A=setTimeout(m,1e3))}function h(t,r,a){var n=u(),o=document.createElement("script");o.setAttribute("type","text/javascript");o.setAttribute("charset",r);o.onload=function(){Q[a]&&(Q[a].loadTime=new Date);this.onload=this.onerror=this.onreadystatechange=null};e.addEventListener?o.onerror=function(){Q[a]&&(Q[a].loadTime=new Date);this.onload=this.onerror=this.onreadystatechange=null}:o.onreadystatechange=function(){if(this.readyState&&("loaded"===this.readyState||"complete"===this.readyState)){Q[a]&&(Q[a].loadTime=new Date);this.onload=this.onerror=this.onreadystatechange=null}};o.setAttribute("src",t);o.setAttribute("id",n);document.getElementsByTagName("head")[0].appendChild(o);A||(A=setTimeout(m,1e3));o=null;return n}function y(e,t,r){_[e].inFlight=_[e].inFlight+1;Q[t]={launchTime:new Date,timeout:r};P+=1;S+=1}function T(){B+=1}function R(e){var t=document.getElementById(e);if(t)try{t.parentNode.removeChild(t)}catch(r){k("error when removing script",L.ERROR,"removeScript")}}function v(e){_[e].inFlight=_[e].inFlight-1;P-=1}function O(e,t,r){N(t.startTime,t.url,r);R(t.scriptId);v(t.domainMatch);x(t.callbackName,r);if(r){if(t.callbackName){t.callbackName=null;delete t.callbackName}w(e,t)}else{C(t);F("callback",t.success,e,t.context);t=null;g()}}function b(){var e;if(lpTag.taglets.lpAjax&&lpTag.taglets.lpAjax.publishMetrics&&V.length>0){e={tags:[{transport:W}],metrics:V};lpTag.taglets.lpAjax.publishMetrics(e);V.length=0}E()}function E(){j&&clearTimeout(j);j=setTimeout(b,D.metricsTimeout)}function N(e,t,r){var a,n;if(e){n=(new Date).getTime();a=n-e;V.push({rd:a,ts:e,url:t,method:"GET",statusCode:r?400:200});V.length>=D.metricsCount&&b()}}function w(e,t){J+=1;if(t.retries>0){t.retries=t.retries-1;n(t)}else{C(t);F(L.ERROR,t.error,e||s(408,{id:408,name:"TIMEOUT",message:"Request has timed out on all retries"}),t.context);t=null;g()}}function C(e){for(var t=["callUrl","retries","id","requestTimeout","type","encoding","launchTime","callbackName","domainMatch","startTime"],r=0;r<t.length;r++)if(e.hasOwnProperty(t[r])){e[t[r]]=null;delete e[t[r]]}}function F(e,t,r,a){if("function"==typeof t)try{t.call(a||null,r);t=null}catch(n){k("Exception in execution of callback, type :"+e+" e=["+n.message+"]",L.ERROR,"runCallback")}else k("No callback, of type :"+e,L.INFO,"runCallback")}function x(t,r){Q[t]=null;delete Q[t];if(r===!0)e[t]=function(){e[t]=null;try{delete e[t]}catch(r){}};else{e[t]=null;try{delete e[t]}catch(a){}}}function I(t){if(Q[t.callbackName]){T();w(s(409,{message:"This callbackName is already in a pending request and can't be serviced",id:409,name:"CONFLICT"}),t)}else e[t.callbackName]=function(e,r){O(e,t,r)}}function k(t,r,a){if(e.lpTag&&lpTag.log){"string"==typeof t&&a&&(t=a+": "+t);lpTag.log(t,r,W)}}var A,j,D={callback:"cb",encoding:"UTF-8",timeout:1e4,retries:2,metricsCount:100,metricsTimeout:6e4},L={ERROR:"ERROR",DEBUG:"DEBUG",INFO:"INFO"},M=!0,U=2083,q="lpCb",_={},S=0,G=0,P=0,B=0,J=0,V=[],Q={},H=i(!0).length,K="1.1.7",W="jsonp",z={init:t,configure:r,issueCall:n,isValidRequest:a,getVersion:function(){return K},getName:function(){return W},getDefaults:o,inspect:function(){return{name:W,version:K,callsMade:S,errorsFound:J,pending:P,buffered:G,refused:B,defaults:o()}}};t();return z}(window);window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.postmessage=lpTag.taglets.postmessage||function(e){function t(e){var t=0===location.protocol.indexOf("https");if(e){if(e.frames){e.frames=e.frames.constructor===Array?e.frames:[e.frames];for(var r=0;r<e.frames.length;r++)g(e.frames[r],t)}if(e.defaults)for(var a in e.defaults)Te.hasOwnProperty(a)&&e.defaults.hasOwnProperty(a)&&(Te[a]=e.defaults[a])}re=!0}function r(){if(lpTag&&lpTag.taglets&&lpTag.taglets.lpAjax)try{lpTag.taglets.lpAjax.addTransport(te,Oe)}catch(e){}}function a(t){var r=!1;if(e.postMessage&&e.JSON&&t&&t.success&&(t.domain&&t.validation||t.url)){t.domain=t.domain||p(t.url);(ae[t.domain]||ue[t.domain])&&(r=!0)}return r}function n(e){var t=!1;if(re&&a(e))if(ae[e.domain])if(ae[e.domain].validated!==ve.PENDING||e.validation){t=L(e);t?v(e.domain):ne[e.callId].timeout=0}else t=O(e.domain,e);else{Y("Adding iFrame to DOM - first request: "+e.domain,fe.INFO,"issueCall");t=O(e.domain,e);m(ue[e.domain]);delete ue[e.domain]}else t=D(e.domain,e.error,e.context);return t}function o(e){return e&&ae[e]?{url:ae[e].url,validated:ae[e].validated,requestCount:ae[e].requestCount,defaults:S(ae[e].defaults),started:ae[e].validated===ve.VALIDATED}:{}}function l(){var e={};for(var t in ae)ae.hasOwnProperty(t)&&(e[t]=o(t));return e}function i(e,t,r){e.addEventListener?e.addEventListener(t,r,!1):e.attachEvent("on"+t,r)}function s(e,t){return{callId:e,responseType:t.responseType,responseCode:t.responseCode,error:{message:t.message,id:t.responseCode,name:t.name}}}function u(e,t,r){e.removeEventListener?e.removeEventListener(t,r,!1):e.detachEvent&&e.detachEvent("on"+t,r)}function c(){if(document.body){ce=!0;d()}else setTimeout(c,5)}function d(){for(;de.length>0;)try{de.shift().call(null)}catch(e){Y("Unable to execute queued callbacks for window interactive state: "+e,fe.ERROR,"_attachPendingIFrames")}}function f(e){return e+"_"+Math.floor(1e5*Math.random())+"_"+Math.floor(1e5*Math.random())}function p(e){var t,r=new RegExp(/(http{1}s{0,1}?:\/\/)([^\/\?]+)(\/?)/gi),a=null;if(0!==e.indexOf("http"))return location.protocol+"//"+location.host;t=r.exec(e);t&&t.length>=3&&""!==t[2]&&(a=t[1].toLowerCase()+t[2].toLowerCase());return a}function g(e,t){var r,a,n=!1;if(!e||!e.url||"string"!=typeof e.url){Y("iFrame configuration empty or missing url parameter",fe.ERROR,"_queueFrame");return n}r=p(e.url);a=0===e.url.toLowerCase().indexOf("https");if(!(ae[r]||ue[r]||t&&a!==t)){ue[r]=e;n=!0}return n}function m(e){var t=p(e.url);if(ae[t])return b(t,e.callback||e.success,e.context);var r=f("fr");ae[t]={elem:E(r),url:e.url,validated:ve.PENDING,defaults:e.defaults||{},delayLoad:isNaN(e.delayLoad)?0:e.delayLoad,requestCount:0,success:e.callback||e.success,error:e.error,maxReloadRetries:e.maxReloadRetries||3,reloadInterval:1e3*e.reloadInterval||3e4};setTimeout(function(){y(e.url,t)},ae[t].delayLoad);Y("iFrame Queued to load "+t,fe.INFO,"_addFrame");return ve.PENDING}function h(e){var t=p(e.url);ue[t]={url:e.url,defaults:e.defaults||{},delayLoad:e.delayLoad,success:e.success,error:e.error,maxReloadRetries:e.maxReloadRetries,reloadInterval:e.reloadInterval/1e3}}function y(e,t){ce?T(e,t):de.push(function(){T(e,t)})}function T(e,t){ae[t].loadCallback=ae[t].loadCallback||R(t);V(ae[t].elem,e);i(ae[t].elem,"load",ae[t].loadCallback);ae[t].iFrameOnloadTimeout=setTimeout(ae[t].loadCallback,Re);ae[t].attachTime=(new Date).getTime();document.body.appendChild(ae[t].elem)}function R(e){return function(t){if(ae[e].iFrameOnloadTimeout){clearTimeout(ae[e].iFrameOnloadTimeout);delete ae[e].iFrameOnloadTimeout}ae[e].loadTime=(new Date).getTime()-ae[e].attachTime;C(e,t)}}function v(e){le+=1;se+=1;ae[e].requestCount=ae[e].requestCount+1}function O(e,t){oe[e]=oe[e]||[];oe[e].push(t);return!0}function b(e,t,r){var a=o(e);W(t,r,a);return ae[e].validated}function E(e){var t=document.createElement("IFRAME");t.setAttribute("id",e);t.setAttribute("name",e);t.setAttribute("tabindex","-1");t.setAttribute("aria-hidden","true");t.setAttribute("title","");t.setAttribute("role","presentation");t.style.width="0px";t.style.height="0px";t.style.position="absolute";t.style.top="-1000px";t.style.left="-1000px";return t}function N(e,t,r,a,n,o){var l=!1;if(e&&t&&"function"==typeof t){ne[e]={success:t,error:r,progress:a,ctx:n,launchTime:new Date,timeout:isNaN(o)?Te.timeout:o+1e3};l=!0}return l}function w(e){if(ne[e]){ne[e]=null;delete ne[e];return!0}return!1}function C(e,t){Y("onLoad validation called "+e,fe.INFO,"_validateFrame");var r=function(t){F(t,e)};t&&t.error?F(t,e):setTimeout(function(){n({domain:e,success:r,error:r,validation:!0,timeout:100,retries:-1,defaults:ae[e].defaults})},10);return!0}function F(e,t){var r,a=ae[t];Y("running validation of domain "+t,fe.INFO,"_validateFrameCallback");if(a){ae[t].validated=e&&e.error?ve.FAILED:ve.VALIDATED;r=ae[t].validated===ve.VALIDATED;r?x(t,e):ae[t].reloadObj&&ae[t].reloadObj.retriesLeft>0?k(t):I(t)}a=null;return r}function x(e,t){var r;Y("FrameLoaded "+e,fe.INFO,"_runFrameValidated");r=S(he);for(var a in t)t.hasOwnProperty(a)&&(r[a]=t[a]);W(ae[e].success,ae[e].context,r);H(e);A(e,!0)}function I(e){Y("iFrame is a teapot "+e,fe.ERROR,"_runFrameFailedToLoad");if(ae[e].error){var t=s(0,ye);t.domain=e;W(ae[e].error,ae[e].context,t)}j(e);A(e,!1)}function k(e){Y("Retry loading domain: "+e,"info","_runReloadAttempt");A(e,!1);P(e)}function A(e,t){Y("Running buffer queue : "+e+" loaded: "+t,fe.INFO,"_runQueuedRequests");if(oe[e]&&oe[e].length>0){do{var r=oe[e].shift();t?n(r):W(r.error,r.context,{responseCode:600,error:"Transport - postmessage - unable to run request: "+e,body:"ERROR"})}while(oe[e].length>0);oe[e]=null;delete oe[e]}}function j(e){Y("Cleaning up failed iFrame: "+e,fe.INFO,"_cleanupIFrame");if(ae[e]){u(ae[e].elem,"load",ae[e].loadCallback);ae[e].elem.parentNode.removeChild(ae[e].elem);var t=S(ye);t.domain=e;t.url=ae[e].url;W(ae[e].error,ae[e].context,t);h(ae[e]);ae[e]=null;delete ae[e]}}function D(e,t,r){Y("Frame not found for domain: "+e,fe.ERROR,"_noFrameFound");W(t,{responseCode:600,error:"Transport - postmessage - unable to run request: "+e,body:"ERROR"},r);return!1}function L(e){var t,r=!1;e=U(e);t=S(e);try{t=M(t)}catch(a){Y("Error trying to _stringify message",fe.ERROR,"sendMessageToFrame");return!1}Y("sending msg to domain "+e.domain,fe.DEBUG,"sendMessageToFrame");var n;isNaN(e.timeout)||isNaN(e.retries)||(n=e.timeout*(e.retries+1)+2e3);N(e.callId,e.success,e.error,e.progress,e.context,n);try{r=q(e.domain,t);$=setTimeout(_,1e3)}catch(a){Y("Error trying to send message: "+a,fe.ERROR,"sendMessageToFrame");r=!1}return r}function M(e){var t;if("function"==typeof Array.prototype.toJSON){var r=Array.prototype.toJSON;delete Array.prototype.toJSON;try{t=JSON.stringify(e)}catch(a){Array.prototype.toJSON=r;throw a}Array.prototype.toJSON=r}else t=JSON.stringify(e);return t}function U(t){var r=ae[t.domain]&&ae[t.domain].defaults;t.callId=f("call");t.returnDomain=pe;"undefined"==typeof t.timeout&&(t.timeout=r&&r.timeout||Te.timeout);"undefined"==typeof t.retries&&(t.retries=r&&"undefined"!=typeof r.retries?r.retries:Te.retries);t.progress&&(t.fireProgress=!0);t.headers=t.headers||{};t.headers["LP-URL"]=e.location.href;return t}function q(e,t){var r=!1;try{ae[e].elem.contentWindow.postMessage(t,e);r=!0}catch(a){Y("Error trying to send message: "+a,fe.ERROR,"_postTheMessage")}return r}function _(){$&&clearTimeout($);$=null;var e=new Date,t=0,r=[];for(var a in ne)if(ne.hasOwnProperty(a)&&ne[a].launchTime){var n=e-ne[a].launchTime;n>ne[a].timeout?r.push(a):t+=1}if(r.length){Y("Checking errors found "+r.length+" timeout callbacks to call",fe.DEBUG,"_checkForErrors");for(var o=0;o<r.length;o++)G(s(r[o],me))}t>0&&($=setTimeout(_,1e3));return!0}function S(e){var t=e;try{t=JSON.parse(M(e))}catch(r){}return t}function G(e,t){var r,a=ne[e.callId],n=e.responseType,o=!1;if(e.callId&&ne[e.callId]||e.responseType===ge.reloading||e.responseType===ge.stats)try{switch(n){case ge.completed:r=a.success;o=!0;break;case ge.error:r=a.error;o=!0;ie+=1;break;case ge.progress:r=a.progress;break;case ge.reloading:e=t;r=P;break;case ge.stats:r=Z;e=e.rawData}if(o){w(e.callId);K(e);se=se>=0?0:se-1}r&&"function"==typeof r&&W(r,a&&a.ctx||null,e);r=null;a=null}catch(l){Y("Error in executing callback: "+l,fe.ERROR,"_executeMessageCallback");return!1}return!0}function P(e){Y("Got reload request from "+e,fe.INFO,"_handleReload");ae[e].validated=ve.PENDING;if(!ae[e].reloadObj){Y("Creating reloadObj"+e,fe.DEBUG,"_handleReload");ae[e].reloadObj=Q(e)}B(e)}function B(e){Y("Reload try for domain "+e+" ,retries left "+ae[e].reloadObj.retriesLeft,fe.INFO,"_reloadIFrame");ae[e].reloadObj.retriesLeft=ae[e].reloadObj.retriesLeft-1;ae[e].reloadObj.setLocationTimeout&&clearTimeout(ae[e].reloadObj.setLocationTimeout);if(ae[e].reloadObj.retry)ae[e].reloadObj.setLocationTimeout=setTimeout(J(e),ae[e].reloadInterval);else{ae[e].reloadObj.retry=!0;J(e)()}}function J(e){return function(){ae[e].iFrameOnloadTimeout=setTimeout(function(){C(e,{error:{code:404,message:"Frame did not trigger load"}})},Re);V(ae[e].elem,ae[e].url)}}function V(e,t){t+=t.indexOf("?")>0?"&bust=":"?bust=";t+=(new Date).getTime();t+="&loc="+encodeURIComponent(location.protocol+"//"+location.host);Y("Setting iFrame to URL: "+t,fe.INFO,"_setIFrameLocation");e.setAttribute("src",t)}function Q(e){Y("Creating reload object "+e,fe.INFO,"_createReloadObject");var t=ae[e].maxReloadRetries;return{retriesLeft:t}}function H(e){Y("Cleaning up reload object for this instance"+e,fe.INFO,"_cleanUpReloadObject");if(ae[e].reloadObj){ae[e].reloadObj.setLocationTimeout&&clearTimeout(ae[e].reloadObj.setLocationTimeout);ae[e].reloadObj=null;delete ae[e].reloadObj}}function K(e){for(var t=["callId","responseType"],r=0;r<t.length;r++){e[t[r]]=null;delete e[t[r]]}}function W(e,t,r){if(e&&"function"==typeof e)try{e.call(t||null,r)}catch(a){Y("Error in executing callback: "+a,fe.ERROR,"runCallback")}}function z(e){var t,r;try{r=e.origin;if(!ae[r])return;t=X(e.data);t.body=X(t.body)}catch(a){t=null;Y("Error in handling message from frame:"+a+" origin: "+r,fe.ERROR,"_handleMessage")}t&&"object"==typeof t&&G(t,r)}function X(e){var t=e;if("string"==typeof e)try{t=JSON.parse(e)}catch(r){Y("Error in parsing string: "+e,fe.DEBUG,"_parseJSONString")}return t}function Y(t,r,a){if(e.lpTag&&lpTag.log){"string"==typeof t&&a&&(t=a+": "+t);lpTag.log(t,r,te)}}function Z(e){if(lpTag.taglets.lpAjax&&lpTag.taglets.lpAjax.publishMetrics){e.tags&&e.tags.constructor===Array&&e.tags.push({transport:te});lpTag.taglets.lpAjax.publishMetrics(e)}}var $,ee="1.1.8",te="postmessage",re=!0,ae={},ne={},oe={},le=0,ie=0,se=0,ue={},ce=!1,de=[],fe={DEBUG:"DEBUG",INFO:"INFO",ERROR:"ERROR"},pe=p(document.location.href),ge={progress:"progressLoad",completed:"completeLoad",success:"success",error:"errorLoad",reloading:"reloading",stats:"statData"},me={responseType:ge.error,responseCode:404,message:"Request timed out on parent postMessage layer",name:"TIMEOUT"},he={responseType:ge.success,responseCode:200,message:"iFrame has successfully loaded",name:"OK"},ye={responseType:ge.error,responseCode:418,message:"This iFrame is a teapot, not very useful for communication but lovely for earl grey",name:"TEAPOT"},Te={timeout:6e4,metricsCount:1e3},Re=1e4,ve={VALIDATED:"valid",PENDING:"pending",FAILED:"failed"};c();i(e,"message",z);var Oe={v:ee,name:te,init:r,issueCall:n,isValidRequest:a,getVersion:function(){return ee},getName:function(){return te},configure:t,getFrameData:o,inspect:function(){return{name:te,version:ee,callsMade:le,errorsFound:ie,pending:se,defaults:Te,iFrameList:S(ue),activeFrames:l()}}};r();return Oe}(window);window.lpTag=lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lpTransporter=lpTag.taglets.lpTransporter||function(){function e(e,t){window.lpTag&&lpTag.log&&lpTag.log(e,t,r)}function t(){for(var t=0;t<n.taglets.length;t++){var r=lpTag.taglets[n.taglets[t]];try{r.init();e("Called init on taglet: "+n.taglets[t],"DEBUG")}catch(a){e("Error init taglet:"+n.taglets[t]+"  e="+a,"ERROR")}}for(var o=0;t<n.taglets.length;o++){var l=lpTag.taglets[n.taglets[o]];try{"function"==typeof l.start&&l.start();e("Called start on taglet: "+n.taglets[o],"DEBUG")}catch(a){e("Error start taglet: "+n.taglets[o]+"e= "+a,"ERROR")}}}var r="lpTransporter",a="1.1.0",n={taglets:["lpAjax","lpajax_utils","jsonp","postmessage"]};return{v:a,name:r,init:t}}();}catch(e){lpTag.handleGeneralError("lpTransporter",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.taglets.lpUtil=lpTag.taglets.lpUtil||function(){function e(e,t){if("function"==typeof[].indexOf&&"function"==typeof e.indexOf)return e.indexOf(t);if(e.constructor===Array)for(var n=0;n<e.length;n++)if(e[n]===t)return n;return-1}function t(e){return"string"!=typeof e?e:e.trim?e.trim():e.replace(/^\s+|\s+$/gm,"")}function n(e){if("string"!=typeof e)return e;var n=t(e);return n.toLowerCase()}function i(e,t){var n=new RegExp(/((?:http|ftp|ws){1}s{0,1}?:\/\/){0,1}([^\/\?\/:]+)(\/?)/gi),i=n.exec(e),o=null;if(i&&i.length>=3&&""!==i[2]){o=i[2].toLowerCase();t&&(o=i[1]+o)}return o}function o(e){var t,n,i={};if("string"==typeof e){t=e.substr(1).split("&");for(var o=0;o<t.length;o++)if(t[o].indexOf("=")>-0){n=t[o].split("=");2==n.length&&(i[decodeURIComponent(n[0])]=decodeURIComponent(n[1]))}}return i}function a(e){e=""+e;var t,n,o=null,a={top:null,country:null};e=i(e);t=e.split(".");if(t.length<3)return e;for(var s=t.length-1,c=s;c>-1;c--){l(t[c],a,c);if(null!==a.country&&null!==a.top)break}if(null!==a.top||null!==a.country){o=a.top;(null===o||null!==a.country&&a.country<o&&o-1===a.country)&&(o=a.country);n=o>0?o-1:o;return r(t.slice(n))}return e}function r(e){return e.join(".")}function l(e,t,n){e=""+e;null===t.top&&(W.topLevelDomain[e]||W.customTopLevelDomain[e])?t.top=n:null===t.country&&W.countryTopLevelDomain[e]&&(t.country=n)}function s(e,t){var n=/(\?|&|\/|\\)$/;if("string"==typeof e&&e.length>0){n.test(e)&&(e=e.substr(0,e.length-1));if(Array.isArray(t))return c(e,t);if(f(t))return p(e,t);if("object"==typeof t)return d(e,t)}return e}function c(e,t){for(var n,i=e,o=0;o<t.length;o++){n=t[o];if(!i||"object"!=typeof n)break;i=p(i,n)}return i}function p(e,t){return g(e,t)?u(e,t.key,t.value):e}function d(e,t){for(var n in t)t.hasOwnProperty(n)&&(e=u(e,n,t[n]));return e}function u(e,t,n){var i=new RegExp("([?&])"+t+"=.*?(&|$)","ig"),o=-1===e.indexOf("?")?"?":"&";if("object"==typeof n)try{n=JSON.stringify(n)}catch(a){}return e.match(i)?e.replace(i,"$1"+t+"="+n+"$2"):e+o+t+"="+n}function g(e,t){return"string"==typeof e&&e.length>0&&f(t)}function f(e){var t=e&&e.key,n=e&&e.value;return e&&"string"==typeof t&&t.length>0&&("number"==typeof n||"string"==typeof n&&n.length>0)}function m(e,t,n){var i;if(!e||"object"!=typeof e)return e;if(!t)return h(e);i=t||e.constructor()||{};for(var o in e)i[o]!==e[o]&&e.hasOwnProperty(o)&&(i[o]=n?m(e[o],void 0,n):e[o]);return i}function h(e){try{return JSON.parse(b(e))}catch(t){lpTag.log("unable to clone object:"+JSON.stringify(t),"ERROR",L);return}}function C(e,t){if("undefined"!=typeof t){if(e)for(var n=0;n<e.length;n++){var i=e[n].value;if("string"==typeof i&&""!==i)if("true"===i)i=!0;else if("false"===i)i=!1;else if("["==i.charAt(0)||"{"==i.charAt(0))try{i=JSON.parse(i)}catch(o){lpTag.log("unable to parse JSON:"+JSON.stringify(o),"ERROR",L)}t[e[n].id]=i}}else lpTag.log("_config is passed as undefined","ERROR",L)}function _(e,t){if(e&&"string"==typeof e){t=t||{};var n,i=t.document||window.document,o=t.id;if("undefined"!=typeof o){n=i.getElementById(o)||i.createElement("style");n.id=o}else n=i.createElement("style");n.type="text/css";i.getElementsByTagName("head")[0].appendChild(n);n.styleSheet?n.styleSheet.cssText=e:n.appendChild(document.createTextNode(e));return n}}function v(e){var t={};for(var n in e)e.hasOwnProperty(n)&&(t[n]=n);return t}function S(e,t){function n(){o.body?y(e,i):setTimeout(n,50)}t=t||{};var i=t.context,o=t.document||window.document;n()}function b(e){var t,n;if("function"==typeof Array.prototype.toJSON){n=Array.prototype.toJSON;delete Array.prototype.toJSON;try{t=JSON.stringify(e)}catch(i){Array.prototype.toJSON=n;throw i}Array.prototype.toJSON=n}else t=JSON.stringify(e);return t}function w(){var e="tttttttt-tttt-4ttt-fttt-t7ttttttttttt".replace(/[tf]/g,function(e){var t=16*Math.random()|0,n="t"==e?t:3&t|8;return n.toString(16)});return e+"-"+Math.floor(1e5*Math.random())}function y(e,t){t=t||window;if("function"==typeof e){var n=Array.prototype.slice.call(arguments,2);try{return e.apply(t,n)}catch(i){lpTag.log("Failed to execute callback exc= "+i.message,"ERROR",L)}}}function T(e,t,n){if("function"==typeof x){e=e||{};var i=x(window,e.jsContext),o=x(window,e.jsMethodName);if("function"!=typeof o){lpTag.log("runCallbackByObject err=function "+e.jsMethodName+" was not found","ERROR","utils");n()}else{lpTag.log("runCallbackByObject calling function "+e.jsMethodName,"DEBUG","utils");o.call(i,t)}}else lpTag.log("lpTag.taglets.lpUtil.getPropertyFromObject is not a function","ERROR","utils")}function E(e,t,n){e&&t&&n&&(e.addEventListener?e.addEventListener(t,n,!1):e.attachEvent("on"+t,n))}function I(e,t,n){e&&t&&n&&(e.removeEventListener?e.removeEventListener(t,n,!1):e.detachEvent("on"+t,n))}function N(t,n,i){var o,a;t="string"==typeof t?t:"";o=t.split(" ");a=n&&n.constructor===Array?n:"string"==typeof n?[n]:[];i=i&&i.constructor===Array?i:"string"==typeof i?[i]:[];for(var r=0;r<o.length;r++)e(i,o[r])<0&&e(a,o[r])<0&&a.push(o[r]);return a.join(" ").replace(/$\s/gi,"")}function A(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}function x(e,t,n){var i,o=e;if("string"==typeof t){i=t.split(".");for(var a=0;a<i.length;a++){if("undefined"==typeof o||null===o||"undefined"==typeof o[i[a]]||null===o[i[a]]){o="undefined"!=typeof n?n:void 0;break}o=o[i[a]]}}else lpTag.log("Empty path sent to lookup getPropertyFromObject","DEBUG","utils");return o}function k(e,t){if(e.className.indexOf(t)<0){e.className=[e.className,t].join(" ");e.className=O(e.className)}}function R(e,t){e.className=e.className.replace(new RegExp(t,"g"),"");e.className=O(e.className)}function O(e){return t(e.replace(/\s\s*/g," "))}function D(e,t,n){var i={};if(e&&"string"==typeof e){n&&(e=decodeURIComponent(e));for(var o,a=e.split(t),r=0;r<a.length;r++){o=a[r].split("=");i[o[0]]=o[1]}}return i}var L="Utils",W={customTopLevelDomain:{aero:"aero",asia:"asia",bike:"bike",biz:"biz",camera:"camera",cat:"cat",clothing:"clothing",coop:"coop",equipment:"equipment",estate:"estate",eus:"eus",gallery:"gallery",graphics:"graphics",guru:"guru",info:"info","int":"int",holdings:"holdings",jobs:"jobs",lighting:"lighting",mobi:"mobi",museum:"museum",name:"name",photography:"photography",plumbing:"plumbing",post:"post",pro:"pro",singles:"singles",tel:"tel",travel:"travel",ventures:"ventures",xxx:"xxx"},topLevelDomain:{ac:"ac",co:"co",com:"com",edu:"edu",gov:"gov",mil:"mil",net:"net",org:"org"},countryTopLevelDomain:{ac:"ac",ad:"ad",ae:"ae",af:"af",ag:"ag",ai:"ai",al:"al",am:"am",an:"an",ao:"ao",aq:"aq",ar:"ar",as:"as",at:"at",au:"au",aw:"aw",ax:"ax",az:"az",ba:"ba",bb:"bb",bd:"bd",be:"be",bf:"bf",bg:"bg",bh:"bh",bi:"bi",bj:"bj",bm:"bm",bn:"bn",bo:"bo",bq:"bq",br:"br",bs:"bs",bt:"bt",bv:"bv",bw:"bw",by:"by",bz:"bz",bzh:"bzh",ca:"ca",cc:"cc",cd:"cd",cf:"cf",cg:"cg",ch:"ch",ci:"ci",ck:"ck",cl:"cl",cm:"cm",cn:"cn",co:"co",cr:"cr",cs:"cs",cu:"cu",cv:"cv",cw:"cw",cx:"cx",cy:"cy",cz:"cz",dd:"dd",de:"de",dj:"dj",dk:"dk",dm:"dm","do":"do",dz:"dz",ec:"ec",ee:"ee",eg:"eg",eh:"eh",er:"er",es:"es",et:"et",eu:"eu",fi:"fi",fj:"fj",fk:"fk",fm:"fm",fo:"fo",fr:"fr",ga:"ga",gb:"gb",gd:"gd",ge:"ge",gf:"gf",gg:"gg",gh:"gh",gi:"gi",gl:"gl",gm:"gm",gn:"gn",gp:"gp",gq:"gq",gr:"gr",gs:"gs",gt:"gt",gu:"gu",gw:"gw",gy:"gy",hk:"hk",hm:"hm",hn:"hn",hr:"hr",ht:"ht",hu:"hu",id:"id",ie:"ie",il:"il",im:"im","in":"in",io:"io",iq:"iq",ir:"ir",is:"is",it:"it",je:"je",jm:"jm",jo:"jo",jp:"jp",ke:"ke",kg:"kg",kh:"kh",ki:"ki",km:"km",kn:"kn",kp:"kp",kr:"kr","krd:":"krd",kw:"kw",ky:"ky",kz:"kz",la:"la",lb:"lb",lc:"lc",li:"li",lk:"lk",lr:"lr",ls:"ls",lt:"lt",lu:"lu",lv:"lv",ly:"ly",ma:"ma",mc:"mc",md:"md",me:"me",mg:"mg",mh:"mh",mk:"mk",ml:"ml",mm:"mm",mn:"mn",mo:"mo",mp:"mp",mq:"mq",mr:"mr",ms:"ms",mt:"mt",mu:"mu",mv:"mv",mw:"mw",mx:"mx",my:"my",mz:"mz",na:"na",nc:"nc",ne:"ne",nf:"nf",ng:"ng",ni:"ni",nl:"nl",no:"no",np:"np",nr:"nr",nu:"nu",nz:"nz",om:"om",pa:"pa",pe:"pe",pf:"pf",pg:"pg",ph:"ph",pk:"pk",pl:"pl",pm:"pm",pn:"pn",pr:"pr",ps:"ps",pt:"pt",pw:"pw",py:"py",qa:"qa",re:"re",ro:"ro",rs:"rs",ru:"ru",rw:"rw",sa:"sa",sb:"sb",sc:"sc",sd:"sd",se:"se",sg:"sg",sh:"sh",si:"si",sj:"sj",sk:"sk",sl:"sl",sm:"sm",sn:"sn",so:"so",sr:"sr",ss:"ss",st:"st",su:"su",sv:"sv",sx:"sx",sy:"sy",sz:"sz",tc:"tc",td:"td",tf:"tf",tg:"tg",th:"th",tj:"tj",tk:"tk",tl:"tl",tm:"tm",tn:"tn",to:"to",tp:"tp",tr:"tr",tt:"tt",tv:"tv",tw:"tw",tz:"tz",ua:"ua",ug:"ug",uk:"uk",us:"us",uy:"uy",uz:"uz",va:"va",vc:"vc",ve:"ve",vg:"vg",vi:"vi",vn:"vn",vu:"vu",wf:"wf",ws:"ws",ye:"ye",yt:"yt",yu:"yu",za:"za",zm:"zm",zr:"zr"}},M={delimiter:"|",set:function(e,t,n){if("string"==typeof e&&t.join&&"function"==typeof t.join){var i=encodeURIComponent(e)+"="+encodeURIComponent(t.join(this.delimiter))+";path=/";"undefined"!=typeof n&&(i+=";domain="+n);document.cookie=i;return!0}},get:function(e){var t="\\$&";if("string"==typeof e){var n=document.cookie.replace(new RegExp("(?:(?:^|.*;)\\s*"+encodeURIComponent(e).replace(/[\-\.\+\*]/g,t)+"\\s*\\=\\s*([^;]*).*$)|^.*$"),"$1")||null;if("string"==typeof n){var i=decodeURIComponent(n);if(i)return i.split(this.delimiter)}}},remove:function(e,t){if("string"==typeof e){this.set(e,["null"],t);return!0}}};return{indexOf:e,trim:t,trimAndLower:n,getDomain:i,getURLParams:o,getParentDomain:a,addQueryParams:s,cloneExtend:m,clone:h,convertConfig:C,addStyleTag:_,objectKeys:v,waitForBody:S,stringify:b,cookieActions:M,getUID:w,runCallback:y,runCallbackByObject:T,registerEvent:E,unregisterEvent:I,buildClassString:N,isEmpty:A,getPropertyFromObject:x,addClass:k,removeClass:R,mapString:D}}();window.lpTag=window.lpTag||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.AppConfigurationManager=lpTag.unifiedWindow.AppConfigurationManager||function(e){function t(e,t,i,o){var a="",r=n(e,t,i);o&&e.connector?e.connector.type===D?a=y(r,e,t):(e.connector.type===L||e.connector.type===W)&&(a=w(r,e,t)):a=T(r,t);return a}function n(e,t,n){var i={accountId:t.accountId,env:t.env,clickedChannel:t.clickedChannel};n?S(i,e,t):b(i,e,t);return i}function i(e){return e.connector&&0===e.connector.type}function o(){return"undefined"!=typeof k.taglets.lpUnifiedWindowRecaptcha}function a(e,t){var n=O&&O.lpVersion;return n&&e.debug&&e.env!==M?"/"+n:t?"/"+t:""}function r(e){x.isEmpty(G)&&u(e);return G}function l(e){return e.channelPriority||F}function s(e,t){return e.async?e.allowUnauthMsg?V:B:t?H:j}function c(e){switch(e){case j:case H:return k.unifiedWindow.consts.engagementTypes.CHAT_ENGAGEMENT;case B:case V:return k.unifiedWindow.consts.engagementTypes.MESSAGING_ENGAGEMENT}}function p(e){var t;k.features&&"function"==typeof k.features.getFeature&&(t=k.features.getFeature(e));return t}function d(e,n,i,a){var r=t(e,n,i,a),l=o()?z:U;return window.open(r,e.target+"POP",l)}function u(e){var t=l(e);t.forEach(function(t){G[t]={sessionKey:t,sessionTimeout:g(t,e)}})}function g(e,t){var n;switch(e){case j:case H:n=f(t,"chatSessionTimeout");break;case V:n=f(t,"unauthenticatedMessagingSessionTimeout");break;case B:n=f(t,"messagingSessionTimeout")}return n}function f(e,t){var n,i=R.getDeviceFamilyName().toLowerCase();n=e&&e[t]&&e[t][i]?e[t][i]:m();return n}function m(){var e;e=R.isMobile()?P.MOBILE:R.isTablet()?P.TABLET:P.DESKTOP;return e}function h(e){O&&O.lpDebug&&(e.lpDebug=O.lpDebug);O&&O.lpVersion&&(e.lpVersion=O.lpVersion);return e}function C(){x=e.utils;k=e.lpTag;R=e.deviceDetector;O=e.queryParams}function _(e){var t=document.createElement("a");t.href=e;return{search:t.search,port:t.port,protocol:t.protocol,hostname:t.hostname,pathname:v(t.pathname)}}function v(e){return 0===e.indexOf("/")?e:"/"+e}function S(e,t,n){e.poppedOut=!0;e.sessionId=n.sessionId;e.supportBlockCCPattern=n.supportBlockCCPattern;e.scp=n.scp;e.engConf={lewid:t.lewid}}function b(e,t,n){var a=t.connector,r=x.getPropertyFromObject(a,"configuration.acrValues");"undefined"!=typeof r&&delete a.configuration.acrValues;if(t.connector&&t.connector.configuration&&!t.async){a=x.clone(t.connector);a.configuration.authorizationEndpoint&&(a.configuration.authorizationEndpoint=encodeURIComponent(a.configuration.authorizationEndpoint));a.configuration.tokenEndpoint&&(a.configuration.tokenEndpoint=encodeURIComponent(a.configuration.tokenEndpoint))}e.external=!0;e.chatSessionTimeout=n.chatSessionTimeout;e.supportBlockCCPattern=n.supportBlockCCPattern;e.scp=n.scp;e.secureStorageType=n.secureStorageType;e.engConf={async:t.async,scid:t.scid,cid:t.cid,eid:t.eid,lang:t.lang,svid:t.svid,ssid:t.ssid,lewid:t.lewid,connector:a,allowUnauthMsg:t.allowUnauthMsg};t.isOffline&&(e.engConf.isOffline=!0);o()&&(e.useRecaptcha=!0);n.sessionTimeout&&(e.sessionTimeout=n.sessionTimeout);n.chatReconnectTimeout&&(e.chatReconnectTimeout=n.chatReconnectTimeout);i(t)&&(e.engConf.authConnId=t.connector.id);t.skill&&(e.engConf.skill=t.skill);t.preChatLines&&(e.engConf.preChatLines=A(t.preChatLines));e.engConf.connector&&e.engConf.connector.configuration&&delete e.engConf.connector.configuration.jwtPublicKey}function w(e,t,n){var i,o,a;t.connector=t.connector||{};o=n.useOAuth2Standard||t.connector.configuration&&t.connector.configuration.rfcCompliance;if(N(t.connector)){i=t.connector.type===L?o?"id_token":"token":"code";var r=E(n,!0),l=I(e,!0,o),s={response_type:i};"undefined"!=typeof t.connector.configuration.clientId&&(s.client_id=t.connector.configuration.clientId);if(o){s.redirect_uri=r;s.state=l}else s.redirect_uri=r+l;a=x.addQueryParams(t.connector.configuration.authorizationEndpoint,s)}else{e.invalidAuthConnector=!0;a=T(e,n)}return a}function y(e,t,n){var i=t.connector.configuration.genKeyUrl,o=_(i),a={rt:"redir",redirect:encodeURIComponent(n.codeRepository+"/index.html"),lpUnifiedWindowConfig:encodeURIComponent(x.stringify(e))};a=h(a);o.search=x.addQueryParams(o.search,a);return o.protocol+"//"+o.hostname+(o.port?":"+o.port:"")+o.pathname+o.search}function T(e,t,n){return E(t,n)+I(e,n)}function E(e,t){var n=_(e.staticCodeRepository+"/index.html"),i=n.protocol+"//"+n.hostname+(n.port?":"+n.port:"")+n.pathname;return t?encodeURIComponent(i):i}function I(e,t,n){var i,o;if(n){i={lpUnifiedWindowConfig:e};i=h(i);o=x.stringify(i)}else{i={lpUnifiedWindowConfig:encodeURIComponent(x.stringify(e))};i=h(i);o=x.addQueryParams("?",i)}return t?encodeURIComponent(o):o}function N(e){e.configuration=e.configuration||{};return e.configuration.authorizationEndpoint&&e.type}function A(e){var t=1500,n={},i=0;for(var o in e){n[o]=e[o];if(i>=t)break;i+=o.length+e[o].length}return n}var x,k,R,O,D=0,L=1,W=2,M="dev",U="menubar=no, location=no, resizable=1, scrollbars=no, status=yes, width=280px, height=400px, modal=true",z="menubar=no, location=no, resizable=1, scrollbars=no, status=yes, width=320px, height=450px, modal=true",P={DESKTOP:40,MOBILE:120,TABLET:120},j="-lpuw-chat",V="-lpuw-unauthMessaging",B="-lpuw-authMessaging",H="-lpuw",F=[j,V,B,H],G={};C(e);return{createExternalConfiguration:n,isRecaptchaEnabled:o,isAuthenticatedEnabled:i,getExternalResourceURL:t,getFeatureById:p,getUWDir:a,openExternal:d,getChannels:r,getChannelsPriority:l,getClickedChannel:s,getEngagementTypeByChannel:c}};window.lpTag=window.lpTag||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.BrowserStateManager=lpTag.unifiedWindow.BrowserStateManager||function(e){function t(e){w="boolean"==typeof navigator.onLine;y=w?navigator.onLine:!0;k=e;E=s();T=window.innerWidth;R=h();var t=m();I=t.hiddenAttr;N=t.visibilityChange;d()}function n(e,t,n){D[e]=D[e]||[];D[e].push({callback:t,context:n})}function i(e,t){b(e,t)}function o(){w&&(y=navigator.onLine);return y}function a(){return!!I}function r(){return L}function l(){return document[I]}function s(){var e;if("undefined"!=typeof window.orientation){var t=window.orientation;e=90===t||-90===t}else e=window.innerWidth>window.screen.availHeight;return e}function c(){for(var e in D){D[e]&&D[e].constructor===Array&&(D[e].length=0);D[e]=null;delete D[e]}}function p(){c();R&&W.unregisterEvent(window,R,C);W.unregisterEvent(window,"resize",v);W.unregisterEvent(window,"focus",u);W.unregisterEvent(window,"blur",f)}function d(){R&&W.registerEvent(window,R,C);W.registerEvent(window,"resize",v);W.registerEvent(window,"focus",u);W.registerEvent(window,"blur",f);W.registerEvent(document,N,g)}function u(){L=!0;if("undefined"!=typeof D[O.FOCUS_CHANGE])for(var e=0;e<D[O.FOCUS_CHANGE].length;e++)W.runCallback(D[O.FOCUS_CHANGE][e].callback,D[O.FOCUS_CHANGE][e].context,{focus:L})}function g(){if("undefined"!=typeof D[O.VISIBILITY_CHANGE])for(var e=0;e<D[O.VISIBILITY_CHANGE].length;e++)W.runCallback(D[O.VISIBILITY_CHANGE][e].callback,D[O.VISIBILITY_CHANGE][e].context,{visible:!document[I]})}function f(){L=!1;if("undefined"!=typeof D[O.FOCUS_CHANGE])for(var e=0;e<D[O.FOCUS_CHANGE].length;e++)W.runCallback(D[O.FOCUS_CHANGE][e].callback,D[O.FOCUS_CHANGE][e].context,{focus:L})}function m(){var e,t;if("undefined"!=typeof document.webkitHidden){e="webkitHidden";t="webkitvisibilitychange"}else if("undefined"!=typeof document.mozHidden){e="mozHidden";t="mozvisibilitychange"}else if("undefined"!=typeof document.msHidden){e="msHidden";t="msvisibilitychange"}else if("undefined"!=typeof document.hidden){e="hidden";t="visibilitychange"}return{hiddenAttr:e,visibilityChange:t}}function h(){return k.isAndroid()||k.isIOS()?"orientationchange":"resize"}function C(){x&&clearTimeout(x);var e=300;k.isAndroid()&&(e=T===window.innerWidth?900:400);x=setTimeout(_,e)}function _(){var e,t,n;e=s();t=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth||0;n=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight||0;if(e!==E&&"undefined"!=typeof D[O.ORIENTATION_CHANGE])for(var i=0;i<D[O.ORIENTATION_CHANGE].length;i++){W.runCallback(D[O.ORIENTATION_CHANGE][i].callback,D[O.ORIENTATION_CHANGE][i].context,{landscape:e,height:n,width:t});E=e}T=window.innerWidth}function v(){A?clearTimeout(A):S();A=setTimeout(function(){var e=window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth||0,t=window.innerHeight||document.documentElement.clientHeight||document.body.clientHeight||0,n={height:t,width:e};if("undefined"!=typeof D[O.RESIZE])for(var i=0;i<D[O.RESIZE].length;i++)W.runCallback(D[O.RESIZE][i].callback,D[O.RESIZE][i].context,n);A=null},300)}function S(){if("undefined"!=typeof D[O.RESIZE_START])for(var e=0;e<D[O.RESIZE_START].length;e++)W.runCallback(D[O.RESIZE_START][e].callback,D[O.RESIZE_START][e].context)}function b(e,t){var n=D[e];if(n){for(var i=[],o=0;o<n.length;o++)n[o].callback!==t&&i.push(n[o]);n.length=0;D[e]=i}}var w,y,T,E,I,N,A,x,k,R,O=(lpTag.unifiedWindow.log,{RESIZE_START:"resizeStart",ORIENTATION_CHANGE:"orientationChange",RESIZE:"resize",FOCUS_CHANGE:"focusChange",VISIBILITY_CHANGE:"visibilityChange"}),D={},L=!0,W=lpTag.taglets.lpUtil;t(e);return{EVENT_NAME:O,on:n,off:i,visibilitySupported:a,isConnected:o,isHidden:l,isFocus:r,isLandscape:s,unregisterAllEvents:c,dispose:p}};window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.DeviceDetector=lpTag.unifiedWindow.DeviceDetector||function(){function e(){return V===U.familyEnum.desktop}function t(){return V===U.familyEnum.mobile}function n(){return V===U.familyEnum.tablet}function i(){return j===U.osEnum.iOS}function o(){return j===U.osEnum.android}function a(){return o()&&!z.match(/Chrome/)}function r(){return F}function l(){return R}function s(){return O}function c(){return L}function p(){if("boolean"==typeof W)return W;var e=!0;try{new Audio}catch(t){e=!1}return e}function d(){return D}function u(){return B}function g(){return H}function f(){return i()?k()<=7||!1:void 0}function m(){return i()?k()<=6||!1:void 0}function h(){return i()?k()>=8||!1:void 0}function C(){return j===U.osEnum.windows}function _(){return j===U.osEnum.mac}function v(){return"Microsoft Internet Explorer"===P||S()}function S(){return z.match(/Trident.*rv[ :]*11\./)}function b(){return U.familyName()}function w(){var e=document.createElement("DIV");I(e);N(e);e=null}function y(){var e;o()?e=z.match(/Android (\d+)\.(\d+)(?:\.(\d+))?;+/i):i()&&(e=z.match(/OS (\d+)_(\d+)(?:_(\d+))?\s+/i));return e}function T(){var e;if(v())if(S())e=11;else{var t=new RegExp("MSIE ([0-9]{1,}[.0-9]{0,})");try{null!=t.exec(z)&&(e=parseFloat(RegExp.$1))}catch(n){}}return e}function E(){var e=document.getElementsByName("viewport");if(e.length>0)for(var t=0;t<e.length;t++){var n=x(e[t].content);if("device-width"===n.width)return!0}return!1}function I(e){O=A(e,"AnimationName");O.supported&&(R=!0)}function N(e){D=A(e,"Transition");D.supported&&(L=!0)}function A(e,t){var n,i={Webkit:"-webkit-",Moz:"-moz-",O:"-o-",ms:"-ms-",Khtml:"-khtml-"},o=!1,a=t?t.substring(0,1).toLowerCase()+t.substr(1):"",r=a,l=t;if(a&&"undefined"!=typeof e.style[a]){o=!0;n=""}if(!o)for(var s in i)if(t&&i.hasOwnProperty(s)&&"undefined"!=typeof e.style[s+t]){n=i[s];r=n+a;l=s+t;o=!0;break}return{supported:o,preFix:o?n:"",propertyName:o?l:"",cssPropertyName:o?r:""}}function x(e){for(var t,n=e.split(","),i={},o=0;o<n.length;o++){t=n[o].split("=");i[M.trimAndLower(t[0])]=M.trimAndLower(t[1])}return i}function k(){return Number(B[1])}var R,O,D,L,W,M=(lpTag.unifiedWindow.log,lpTag.taglets.lpUtil),U=lpTag.device,z=navigator.userAgent,P=navigator.appName,j=U.os(),V=U.family(),B=y(),H=T(),F=E();w();return{isDesktop:e,isMobile:t,isTablet:n,isMobileOptimized:r,isAnimationSupported:l,getAnimationData:s,isTransitionSupported:c,isAudioSupported:p,getTransitionData:d,isAndroid:o,isNativeAndroid:a,isIOS:i,isWindows:C,isMacOS:_,getDeviceFamilyName:b,osVersion:u,browserVersion:g,isIOS6:m,isIOS7:f,isIOS8OrAbove:h,isIE:v}};window.lpTag=window.lpTag||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.Events=lpTag.unifiedWindow.Events||function(e){function t(e){r=new lpTag.Events(e);l=r.trigger;r.trigger=n;s=lpTag.taglets.lpUtil}function n(e,t,n){l(e,t,n);i(e)}function i(e){var t,n,i;c||(c=s.getPropertyFromObject(lpTag,"unifiedWindow.publicEvents"));e="object"==typeof e?e:null;n=e&&c&&c[e.appName]&&c[e.appName][e.eventName];if(lpTag.events&&e&&(e.global||n)){if(e.global){e.passDataByRef=!1;e.aSync=!0;lpTag.events.trigger(e)}if(n){t=Array.isArray(n)?n:[n];for(var a=0;a<t.length;a++){i=o(e,t[a]);lpTag.events.trigger(i)}}}}function o(e,t){var n=s.clone(t);n.passDataByRef=!1;n.aSync=!0;n.data=a(e,n.data);return n}function a(e,t){var n={};if("object"==typeof t)for(var i in t)n[i]=s.getPropertyFromObject(e,t[i]);else n=s.getPropertyFromObject(e,t);return n}var r,l,s,c;t(e);return r};!function(){function e(){function e(e){var t=r[a]&&r[a][e];return"string"==typeof t?t:o(e)||""}function t(e){return s[e]===!0}function n(e){a=e||l}function i(e,t){t=t||{};r[t.locale]=r[t.locale]||{};lpTag.taglets.lpUtil.cloneExtend(e,r[t.locale]);t.locale===l&&lpTag.taglets.lpUtil.cloneExtend(e,lpTag.unifiedWindow.defaultDictionary)}function o(e){var t=lpTag.unifiedWindow.defaultDictionary;return t[a]&&t[a][e]?t[a][e]:t[e]}var a,r={},l="en-US",s={"he-IL":!0,"ar-AE":!0};return{getString:e,isRTL:t,add:i,setLocale:n}}window.lpTag=window.lpTag||{};lpTag.unifiedWindow.language=lpTag.unifiedWindow.language||new e;lpTag.unifiedWindow.addLanguage=function(e,t){lpTag.unifiedWindow.language.add(e,t)}}();window.lpTag=window.lpTag||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.SessionManager=lpTag.unifiedWindow.SessionManager||function(e){function t(e){if("object"==typeof e&&e.accountId&&e.secureStorageLocation&&"string"==typeof e.accountId){E=e.accountId;I=e.sessionId;T=e.events;N=e.external;A=e.secureStorageLocation;y=e.sessionRefreshTimeout||M;O=e.echoedSessionTimeout;S=e.sessions;b=e.sessionsPriority;c(e.clickedSession)}}function n(e,t){W.getValues({keys:b,site:E,app:z,success:p.bind(this,e,t),error:function(){T.trigger({appName:lpTag.taglets.lpUnifiedWindow.name,eventName:P,global:!0})},appName:D,domain:A})}function i(e){var t;W.getValue({key:k.sessionKey,site:E,app:z,success:function(n){n&&(t=n.timestamp);L.runCallback(e,null,t)},error:function(){},appName:D,domain:A})}function o(){return R}function a(e){W.getValue({key:k.sessionKey,app:z,site:E,success:function(t){t&&L.runCallback(e,null,t)},error:function(){},appName:D,domain:A})}function r(e){if(N)L.runCallback(e);else{U=!1;n(_,e)}}function l(e){clearTimeout(w);h();I=null;U=!0;e===!0||N||W.removeValue({key:k.sessionKey,site:E,app:z,appName:D,success:v,error:v,domain:A})}function s(){return k.sessionKey}function c(e){x=S[e];k=x&&S[x.sessionKey]}function p(e,t,n){var i,o;o=d(n);if(o.session){k=S[o.sessionKey];i=o.session.timestamp||"null";I=I?I:o.session.sid}if(o.session||u())f(o.session,i,e,t);else{L.runCallback(e,null);L.runCallback(t)}}function d(e){var t,n;if(e&&"object"==typeof e)for(var i=0;i<b.length;i++){n=b[i];if(e[n]){t=e[n];break}}else t=e;return{session:t,sessionKey:n}}function u(){return!!x}function g(e){return O||e&&e.sessionTimeout&&parseInt(e.sessionTimeout,10)||k.sessionTimeout}function f(e,t,n,i){R||(R=g(e));I&&"null"!==I||(I=L.getUID());N?L.runCallback(n,null,I):W.setValue({key:k.sessionKey,site:E,app:z,value:{sid:I,timestamp:t||"null",sessionTimeout:R||"null"},ttl:1e3*k.sessionTimeout,success:function(){L.runCallback(n,null,I);L.runCallback(i)},error:function(){},appName:D,domain:A})}function m(){T.bind({appName:"ChatStateManager",eventName:"startChatInfo",func:C})}function h(){T.unbind({appName:"ChatStateManager",eventName:"startChatInfo",func:C})}function C(e){e.chatTimeout&&(R=e.chatTimeout)}function _(){w&&clearTimeout(w);if(!U&&!N){{(new Date).getTime()}W.touchValue({key:k.sessionKey,site:E,ttl:1e3*k.sessionTimeout,success:function(){w=setTimeout(_,y)},error:function(){U=!0},appName:D,domain:A})}}function v(){}var S,b,w,y,T,E,I,N,A,x,k,R,O,D="SessionState",L=lpTag.taglets.lpUtil,W=(lpTag.unifiedWindow.log,lpTag.taglets.lpSecureStorage),M=5e3,U=!1,z=lpTag.unifiedWindow.apps.UNIFIED_WINDOW,P="STORAGE_SELECTED";t(e);m();return{dispose:l,refresh:r,getCurrentSessionKey:s,setClickedSession:c,getSessionId:n,getSessionTime:i,getSession:a,getSessionTimeout:o}};window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.StateAnalyzer=lpTag.unifiedWindow.StateAnalyzer||function(e,t){function n(e){if("object"==typeof e&&t&&e.storageKey&&e.accountId&&e.secureStorageLocation){c=t;p=e.storageKey;d=e.accountId;g=!!e.disableSessionTimeout||!1;u=e.secureStorageLocation}}function i(e,t){h.getValue({key:p,site:d,app:_,success:r(e),error:s(t),appName:f,domain:u})}function o(e){var t=(new Date).getTime(),n=1e3*c.getSessionTimeout(),i=null;c.getSessionTime(a.bind(this,e,t,n,i))}function a(e,t,n,i,o){i="undefined"==typeof o||"null"===o?C.NOT_STARTED:g||n>=t-o?C.IN_SESSION:C.EXPIRED;e(i)}function r(e){return function(t){o(l.bind(this,e,t,C.NOT_STARTED))}}function l(e,t,n,i){t&&i===C.EXPIRED?n=C.EXPIRED:t?n=C.IN_SESSION:i===C.IN_SESSION&&(n=C.INVALID);m.runCallback(e,null,t,n)}function s(e){return function(t){m.runCallback(e,null,t,C.NOT_STARTED)}}var c,p,d,u,g,f="UIState",m=(lpTag.unifiedWindow.log,lpTag.taglets.lpUtil),h=lpTag.taglets.lpSecureStorage,C={NOT_STARTED:0,IN_SESSION:1,INVALID:2,EXPIRED:3},_=lpTag.unifiedWindow.apps.UNIFIED_WINDOW;n(e);return{getState:i,state:m.clone(C)}};window.lpTag=window.lpTag||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.WindowConfigurationManager=lpTag.unifiedWindow.WindowConfigurationManager||function(e){function t(e){if("object"==typeof e&&e.storageKey&&e.accountId&&e.secureStorageLocation&&e.domain){c=e.accountId;p=e.secureStorageLocation;d=e.domain;g=e.windowId;u=e.storageKey+m}}function n(e){f?h.runCallback(e,null,f):C.getValue({key:u,site:c,app:_,success:r(e),error:r(e),appName:m,domain:p})}function i(){C.removeValue({key:u,site:c,app:_,appName:m,success:s,error:s,domain:p})}function o(e){C.setValue({key:u,site:c,app:_,value:{conf:e,windowId:g},success:a,error:a,appName:m,domain:p,expires:108e5})}function a(){}function r(e){return function(t){if(e)if(t){f=t.conf;g=t.windowId;h.runCallback(e,null,f)}else l(e)}}function l(e){var t;if(g){t="https://"+d+"/api/account/"+c+"/configuration/engagement-window/window-confs/"+g;lpTag.taglets.jsonp.issueCall({url:t,timeout:5e3,retries:0,success:function(t){if(t&&t.json&&t.json&&!t.error){f=t.json;h.runCallback(e,null,f);o(f)}else h.runCallback(e)},error:function(){h.runCallback(e)}})}else h.runCallback(e,null,{})}function s(){}var c,p,d,u,g,f,m="UIConf",h=(lpTag.unifiedWindow.log,lpTag.taglets.lpUtil),C=lpTag.taglets.lpSecureStorage,_=lpTag.unifiedWindow.apps.UNIFIED_WINDOW;t(e);return{getConf:n,clear:i}};window.lpTag=window.lpTag||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.WrapperWindow=lpTag.unifiedWindow.WrapperWindow||function(e,t,n,i,o){function a(e){var t;if(e&&fe.isDesktop()&&e.right>=0){t=e.right>document.body.clientWidth?document.body.clientWidth-280:e.right;Ee.style.right=t+"px";Ie.style.right=t+"px"}}function r(e){if(e){te()||a(e.position);l(e.maximized);s(e.embedded,Xe.external,Xe.poppedOut)}else l(Xe.isMaximized)}function l(e){if(e){Ee.style.display="none";Ie.style.display="block"}else{Ee.style.display="block";Ie.style.display="none"}}function s(e,t,n){if(!e&&!t&&!n){Ee.style.display="none";Ie.style.display="none"}}function c(e,t){re();t&&R();var n=_e[e];if(n&&n!==ve){e!==be.CONFIRMATION&&(Ce=e);he=n.error;T(he);fe.isDesktop()&&Oe.focus();n.title?p(n.title):n.text&&p(n.text);U(n,e);ve=n}}function p(e,t){if(Se!==e){var n=we.querySelectorAll('[data-lp-point="headerText"]');t=t||e;for(var i=0;i<n.length;i++)n[i].innerHTML=t;Se=e}}function d(){k()&&(we.style.display="block")}function u(){k()&&(we.style.display="none")}function g(e,t){Ke=!1;k()?Ge.runCallback(t):Ge.waitForBody(function(){ee(e);L();Ge.runCallback(t)})}function f(){return we}function m(){return Ee}function h(){return Ne}function C(){return Ie}function _(){return ye}function v(){return De}function S(){return Le}function b(){return We}function w(){return Me}function y(){return Ue}function T(e){e?Ge.removeClass(Oe,"lp_hidden"):Ge.addClass(Oe,"lp_hidden")}function E(e){e?Ge.removeClass(ze,"lp_hidden"):Ge.addClass(ze,"lp_hidden")}function I(e){e&&ue(e)}function N(){if(!Ke){O();ve=null;Se=null;x(!1);ye.remove?ye.remove():document.body.removeChild(ye);Ke=!0;Ge.runCallback(Ve)}}function A(){return He.WRAPPER_DIV_ID}function x(e){qe=e}function k(){return qe===!0}function R(){l(!0)}function O(){D();me.off(me.EVENT_NAME.ORIENTATION_CHANGE,le)}function D(){var e=Ie.querySelector(['[data-lp-point="confirm_button"]']),t=Ie.querySelector(['[data-lp-point="cancel_button"]']);Ge.unregisterEvent(Ee,"click",R);Ge.unregisterEvent(Oe,"click",N);t&&Ge.unregisterEvent(t,"click",V);e&&Ge.unregisterEvent(e,"click",N)}function L(){Ge.registerEvent(Oe,"click",N)}function W(e){re();var t=Re.querySelector('[data-lp-point="message"]');if(t){t.remove?t.remove():Re.removeChild(t);e||p("");H()}}function M(){return Xe.iconsRepository}function U(e,t){var n=e.text||"",i=e.imgUrl||"",o=e.imgAltText||"";d();W();var a;a=t===be.WAIT?'<div class="lp_message" data-lp-point="message"><table class="lp_centralizer"><tbody><td><div class="lp_rotator-container"><div class="lp_rotator-container-bg"></div><div class="lp_rotator"><img src="{{imagesRepository}}/{{imageUrl}}" alt="{{imageAltText}}" role="alert"></div></div></td></tbody></table></div>':'<div class="lp_message" data-lp-point="message" aria-labelledby="lp_alert_message"><table class="lp_centralizer"><tbody><tr><td><img src="{{imagesRepository}}/{{imageUrl}}"><div id="lp_alert_message" class="lp_text">{{text}}</div><div class="lp_buttons_area lp_hidden"><button class="lp_cancel_button" data-lp-point="cancel_button" data-lp-cust-id="cancel_button">{{cancel}}</button> <button class="lp_confirm_button" data-lp-point="confirm_button" data-lp-cust-id="button">{{confirm}}</button></div></td></tr></tbody></table></div>';a=B(a);a=a.replace("{{text}}",n);a=a.replace("{{imageUrl}}",i);a=a.replace("{{imageAltText}}",o||"");a=a.replace("{{cancel}}",Fe.getString("closeWindowCancelBtn"));

a=a.replace("{{confirm}}",Fe.getString("closeWindowOkBtn"));e.showConfirm&&(a=a.replace("lp_hidden",""));if(e.needConfirmation){Ge.unregisterEvent(Oe,"click",N);Ge.registerEvent(Oe,"click",P)}if(e.deleteSession){Ge.unregisterEvent(Oe,"click",N);Ge.registerEvent(Oe,"click",j)}Re.innerHTML+=a;z(e.showConfirm);setTimeout(function(){Re.firstChild.setAttribute("role","alert")},0)}function z(e){if(e){var t=Ie.querySelector(['[data-lp-point="cancel_button"]']),n=Ie.querySelector(['[data-lp-point="confirm_button"]']);Ge.registerEvent(t,"click",V);Ge.registerEvent(n,"click",j)}}function P(){c(be.CONFIRMATION);Ge.unregisterEvent(Oe,"click",P);Ge.registerEvent(Oe,"click",j)}function j(){i.trigger({appName:"*",eventName:"knockout"})}function V(){Ge.unregisterEvent(Oe,"click",P);Ge.registerEvent(Oe,"click",N);c(Ce)}function B(e){e=e.replace(/\{\{iconsRepository\}\}/g,M());e=e.replace(/\{\{imagesRepository\}\}/g,Xe.imagesRepository);return e}function H(){Ge.removeClass(Re,"lp_centered")}function F(e,t){if("object"==typeof e)for(var n in e)Xe[n]=e[n];else Xe[e]=t}function G(e,t,n,i){fe=t;me=n;Ve=i;F(e);q()}function q(e){e&&Fe.setLocale(e);_e={CONNECTION_UNAVAILABLE:{title:Fe.getString("couldNotConnect"),imgUrl:"connect-error-dark.png",text:Fe.getString("connectionUnavailable"),error:!0},RIP:{title:Fe.getString("error"),imgUrl:"connect-error-dark.png",text:Fe.getString("chatEndedOnExternalWindow"),error:!0},EXTERNAL_OPEN:{title:Fe.getString("error"),imgUrl:"connect-error-dark.png",text:Fe.getString("externalWindowOpen"),error:!0},UNSUPPORTED:{title:Fe.getString("error"),imgUrl:"connect-error-dark.png",text:Fe.getString("unsupportedBrowserMode"),error:!0},IN_SESSION:{title:Fe.getString("error"),imgUrl:"connect-error-dark.png",text:Fe.getString("cannotResumeChat"),error:!0},AUTH_ERROR:{title:Fe.getString("error"),imgUrl:"thank-you-dark.png",text:Fe.getString("chatAuthError"),error:!0,needConfirmation:!1,deleteSession:!0},CONFIRMATION:{title:Fe.getString("error"),imgUrl:"thank-you-dark.png",text:Fe.getString("confirmMessage"),error:!0,showConfirm:!0},FAILED_START_CONVERSATION_ERROR:{title:Fe.getString("error"),imgUrl:"connect-error-dark.png",text:Fe.getString("startConversationError"),error:!0},SESSION_EXPIRED:{title:Fe.getString("sessionExpired"),imgUrl:"embedded-error.png",text:Fe.getString("sessionError"),error:!0},WAIT:{title:Fe.getString("loading"),imgUrl:"loader_on_warmGray5_75.gif",imgAltText:Fe.getString("loading")}};be=Ge.objectKeys(_e)}function K(){var e=He.WRAPPER_DIV_CSS_CLASS_PREFIX;if(fe.isMobile()){e+=He.MOBILE_CSS_CLASS;Ge.addClass(ye,He.DEVICE_CLASS)}else if(fe.isTablet()){e+=He.TABLET_CSS_CLASS;Ge.addClass(ye,He.DEVICE_CLASS)}else e+=He.DESKTOP_CSS_CLASS;Ge.addClass(ye,e);return e}function X(){fe.isNativeAndroid()&&Ge.addClass(ye,He.WRAPPER_DIV_CSS_CLASS_PREFIX+He.NATIVE_CSS_CLASS);var e=He.WRAPPER_DIV_CSS_CLASS_PREFIX;if(fe.isIE())e+="ie"+fe.browserVersion();else if(fe.isIOS()){e+=He.IOS_CSS_CLASS;fe.isIOS6()?e+=" "+He.IOS6_CSS_CLASS:fe.isIOS8OrAbove()&&(e+=" "+He.WRAPPER_DIV_CSS_CLASS_PREFIX+He.IOS8_OR_ABOVE_CSS_CLASS)}else if(fe.isAndroid())e+=He.ANDROID_CSS_CLASS;else{if(!fe.isMacOS())return;e+=He.MAC_OSX_CSS_CLASS}Ge.addClass(ye,e);return e}function Y(){var e='<div class="lp_minimized" data-lp-point="minimized" data-lp-cust-id="minimized" role="region"><div class="lp_main" data-lp-point="main" role="navigation"><div class="lp_header" data-lp-point="header" data-lp-cust-id="top"><div class="lp_notification_number lpHide" data-lp-point="notification_counter"><span class="lp_notification_text" data-lp-point="notification_text">{{notification_number}}</span></div><div class="lp_header-content-wrapper"><div class="lp_title" role="heading"><div class="lp_chatting-with-icon lpHide" data-lp-type="icon" data-lp-point="chattingWithIcon"><img src="{{iconsRepository}}/sprites_v1.png" alt="" data-lp-cust-id="topBarIcon"></div><span data-lp-point="headerText" data-lp-cust-id="top_text" class="lp_top-text" data-studio-click="true">{{windowTitle}}</span></div><div class="lp_header-buttons-container"><button title="{{tooltip_Maximize}}" class="lp_maximize" data-lp-point="maximize" role="button"><div class="lp_maximize-icon" data-lp-type="icon"><img src="{{iconsRepository}}/sprites_v1.png" alt="{{tooltip_Maximize}}" aria-hidden="true"></div></button> <button title="{{tooltip_Close}}" class="lp_close" data-lp-point="close" role="button"><div class="lp_close-icon" data-lp-type="icon"><img src="{{iconsRepository}}/sprites_v1.png" alt="{{tooltip_Close}}" aria-hidden="true"></div></button></div></div></div></div></div>';e=B(e);e=e.replace(/\{\{tooltip_Close\}\}/g,Fe.getString("tooltip_Close"));we.innerHTML+=e}function J(){Y();Z();$()}function Z(){var e='<div class="lp_maximized lpmx" data-lp-point="maximized" role="region"><div class="lp_header" data-lp-point="header" data-lp-cust-id="top" data-studio-click="true" role="navigation"><div class="lp_header-content-wrapper"><button title="{{tooltip_open_widgets}}" class="lp_hidden lp_slider" data-lp-point="widget_sdk"><div class="lp-slider-icon" data-lp-type="icon"><img src="{{iconsRepository}}/sprites_v1.png" alt="" aria-hidden="true"></div></button><div class="lp_notification_number wsdkNotification lpHide" data-lp-point="widgetNotificationContainer"><span class="lp_notification_text" data-lp-point="widgetNotificationText">0</span></div><div class="lp_title" data-lp-point="maximizedTitleContainer"><div class="lp_chatting-with-icon lpHide" data-lp-type="icon" data-lp-point="chattingWithIcon"><img src="{{iconsRepository}}/sprites_v1.png" alt="" aria-hidden="true" data-lp-cust-id="topBarIcon"></div><span data-lp-point="headerText" data-lp-cust-id="top_text" class="lp_top-text" data-studio-click="true" aria-live="assertive" role="presentation">{{windowTitle}}</span></div><div class="lp_header-buttons-container"><button title="{{tooltip_Minimize}}" class="lp_minimize" data-lp-point="minimize" role="button"><div class="lp_minimize-icon" data-lp-type="icon"><img src="{{iconsRepository}}/sprites_v1.png" alt="{{tooltip_Minimize}}" aria-hidden="true"></div></button> <button title="{{tooltip_Close}}" class="lp_close" data-lp-point="close" role="button"><div class="lp_close-icon" data-lp-type="icon"><img src="{{iconsRepository}}/sprites_v1.png" alt="{{tooltip_Close}}" aria-hidden="true"></div></button></div></div></div><div class="lp_main" data-lp-point="main" data-lp-cust-id="mainArea" role="main"></div></div><div data-lp-point="buffer-strip" class="lp_buffer-strip"><div class="lp_buffer-strip-container" data-lp-cust-id="mainArea"></div></div>';e=B(e);e=e.replace(/\{\{tooltip_Close\}\}/g,Fe.getString("tooltip_Close"));we.innerHTML+=e}function $(){Ee=we.querySelector('[data-lp-point="minimized"]');Ie=we.querySelector('[data-lp-point="maximized"]');Ne=we.querySelector('[data-lp-point="buffer-strip"]')}function Q(){we=document.createElement("div");He.WRAPPER_DIV_ID&&(we.id=He.WRAPPER_DIV_ID);ye=document.createElement("div");ye.appendChild(we);document.body.appendChild(ye)}function ee(e){var t=e&&e.window,n=e&&e.engConf&&e.engConf.lang;n&&q(n);Ke=!1;ge();Q();J();var i=Ee.querySelector('[data-lp-point="main"]');Ge.addClass(Ee,"lpHide");if(He.MINIMIZED_VIEW_IFRAME_RENDERING){Ae=ae(i);xe=Ae.body;Ge.addClass(xe,"lp_main")}else xe=i;var o=Ie.querySelector('[data-lp-point="main"]');if(He.MAXIMIZED_VIEW_IFRAME_RENDERING){ke=ae(o);Re=ke.body;Ge.addClass(Re,"lp_main")}else Re=o;De=Ie.querySelector(['[data-lp-point="widget_sdk"]']);Le=Ie.querySelector(['[data-lp-point="widgetNotificationContainer"]']);We=Ie.querySelector(['[data-lp-point="widgetNotificationText"]']);Me=Ie.querySelector(['[data-lp-point="maximizedTitleContainer"]']);Ue=Ie.querySelector(['[data-lp-point="chattingWithIcon"]']);Oe=Ie.querySelector(['[data-lp-point="close"]']);ze=Ie.querySelector(['[data-lp-point="minimize"]']);Pe=Ie.querySelector(['[data-lp-point="popicon"]']);Pe&&Ge.addClass(Pe,Xe.poppedOut?"lp_pop-in-icon":"lp_pop-out-icon");if(te()){se();ce();de(Xe.engConf.lang);pe()}E(!1);T(!1);r(t);oe();ne();x(!0);ie()}function te(){return Xe.poppedOut||Xe.external}function ne(){je=setTimeout(function(){c(be.WAIT)},He.WAIT_INDICATION_DELAY)}function ie(){K();X();if(fe.isMobile()||fe.isTablet()){le({landscape:me.isLandscape()});me.on(me.EVENT_NAME.ORIENTATION_CHANGE,le)}}function oe(){Te=Ge.addStyleTag('#lpChat .lp_close-icon{position:relative;display:inline-block;vertical-align:middle;overflow:hidden}#lpChat .lp_close-icon img{top:0;left:-480px;position:absolute}#lpChat .lp_minimize-icon{display:none}.lp_desktop #lpChat .lp_close-icon,.lp_desktop #lpChat .lp_close{width:22px;height:22px}.lpdv #lpChat .lp_close-icon,.lpdv #lpChat .lp_close{width:24px;height:24px}#lpChat *{box-sizing:border-box;font-weight:normal;letter-spacing:0;font-family:inherit;opacity:1;filter:alpha(opacity=100);max-width:none;direction:inherit;text-align:inherit;outline:none}#lpChat *::-moz-focus-inner{border:0}#lpChat table,#lpChat tbody,#lpChat td,#lpChat span,#lpChat tr,#lpChat table,#lpChat div,#lpChat button,#lpChat img{margin:0;padding:0;border:0;background:inherit;background:initial;position:static;position:initial;width:auto;height:auto;text-shadow:none;box-shadow:none;-webkit-box-shadow:initial;line-height:normal}#lpChat img{vertical-align:bottom;background:inherit;color:black}#lpChat table,#lpChat tbody,#lpChat td,#lpChat span,#lpChat tr,#lpChat table,#lpChat div,#lpChat button{vertical-align:baseline;background:inherit;color:inherit;font-size:inherit}#lpChat button{font-size:1em;border-radius:0}#lpChat{font-family:"Arial"}#lpChat .lp_header{background-color:#f0f0f0;border:1px solid #d6d6d6;border-radius:5px 5px 0 0;font-size:1.1em;z-index:2;width:100%;position:absolute}#lpChat .lp_main{padding:0;z-index:1;height:100%;width:100%;position:absolute;right:0;bottom:0}#lpChat .lp_main .lp_wait{position:absolute;top:0;bottom:0;left:0;right:0}#lpChat .lp_main .lp_wait table{height:100%;width:100%}#lpChat .lp_main .lp_wait td{text-align:center;vertical-align:middle}#lpChat .lp_main>.lp_message{text-align:center;width:100%;height:100%}#lpChat .lp_main>.lp_message .lp_text{padding:10px 4px 0 4px;text-align:center}#lpChat .lp_main>.lp_message .lp_buttons_area{position:relative;text-align:center;padding:0 5%;height:40px;margin-top:5px}#lpChat .lp_main>.lp_message .lp_buttons_area button{padding:9px 6px;width:45%;float:left;font-size:.9em;border:none;text-align:center;cursor:pointer;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}#lpChat .lp_main>.lp_message .lp_buttons_area .lp_confirm_button{color:#fff;background-color:#0363ad;margin-left:10%;-webkit-border-radius:2px;-moz-border-radius:2px;-ms-border-radius:2px;-o-border-radius:2px;border-radius:2px}#lpChat .lp_main>.lp_message .lp_buttons_area .lp_confirm_button:disabled{opacity:0.5;cursor:default}#lpChat .lp_main>.lp_message .lp_buttons_area .lp_confirm_button:hover,#lpChat .lp_main>.lp_message .lp_buttons_area .lp_confirm_button:focus{background-color:#025487}.lp_lpIos #lpChat .lp_main>.lp_message .lp_buttons_area .lp_cancel_button:hover,.lp_lpIos #lpChat .lp_main>.lp_message .lp_buttons_area .lp_cancel_button:focus{background:#F2F2F2}#lpChat .lp_main>.lp_message .lp_buttons_area .lp_cancel_button{background:transparent;color:#6d6e70}#lpChat .lp_main>.lp_message .lp_buttons_area .lp_single_button{float:none;margin-left:0}#lpChat .lp_header-content-wrapper{width:100%;height:100%}#lpChat .lp_header-content-wrapper .lp_title,#lpChat .lp_header-content-wrapper .lp_subtitle{direction:ltr}#lpChat .lp_header-content-wrapper .lp_title>*,#lpChat .lp_header-content-wrapper .lp_subtitle>*{vertical-align:middle}#lpChat .lp_header-content-wrapper .lp_top-text{overflow:hidden;text-overflow:ellipsis;display:inline-block;margin-left:8px;max-width:150px;white-space:nowrap}#lpChat .lp_header-content-wrapper button{background:none;position:absolute;cursor:pointer}#lpChat>.lpmx{border:1px solid #d6d6d6;border-radius:5px 5px 0 0;box-shadow:0 0 16px 3px rgba(0,0,0,0.2);color:#000}#lpChat>.lpmx>.lp_main{background-color:#fff}#lpChat>.lpmx.lp_external-window{border-radius:0;position:fixed;left:0;right:0;bottom:0;top:0;width:100%;height:100%;overflow:hidden}#lpChat>.lpmx.lp_external-window.lp_no-top>.lp_header{display:none}#lpChat>.lpmx.lp_external-window.lp_no-top>.lp_main{top:0}#lpChat button:focus{outline:#6A9FB1 solid 2px}#lpChat .lp_text:empty,#lpChat img[src=""]{display:none}#lpChat .lpHide,#lpChat .lp_hidden{display:none !important}#lpChat table.lp_centralizer{height:100%;width:100%}#lpChat table.lp_centralizer td{text-align:center;vertical-align:middle}#lpChat .lp_rotator-container{position:relative;display:inline-block;width:100px;height:100px}#lpChat .lp_rotator-container-bg{position:absolute;top:0;right:0;bottom:0;left:0;background-color:rgba(0,0,0,0);opacity:0.92;border-radius:10px}#lpChat .lp_rotator{padding-top:12px;text-align:center;position:absolute;top:0;right:0;bottom:0;left:0}.lp_desktop #lpChat{font-size:13px}.lp_desktop #lpChat>*{width:280px;z-index:99999999;position:fixed;right:20px;bottom:0}.lp_desktop #lpChat>* .lp_header{height:36px;padding:0 5px}.lp_desktop #lpChat>* .lp_header button{z-index:99999999;top:6px}.lp_desktop #lpChat>.lpmx{height:400px;max-height:100%}.lp_desktop #lpChat>.lpmx>.lp_header .lp_title{float:left;padding-top:9px;padding-left:3px}.lp_desktop #lpChat>.lpmx>.lp_main{height:auto;top:36px}.lp_desktop #lpChat>.lpmx .lp_header-content-wrapper .lp_header-buttons-container{float:right;direction:ltr;font-size:0}.lp_desktop #lpChat>.lpmx .lp_header-content-wrapper button{position:relative;display:inline-block}.lp_desktop #lpChat>.lpmx .lp_header-content-wrapper .lp_cancel_button:hover,.lp_desktop #lpChat>.lpmx .lp_header-content-wrapper .lp_cancel_button:focus{background:#F2F2F2}.lpdv #lpChat .lp_header-content-wrapper{text-align:center;display:table;border-collapse:collapse}.lpdv #lpChat .lp_header-content-wrapper>*{vertical-align:middle;display:table-cell}.lpdv #lpChat .lp_header-content-wrapper .lp_header-buttons-container{display:table}.lpdv #lpChat>.lpmx{z-index:99999999;opacity:0.95;position:fixed;top:0;right:0;bottom:0;left:0;border-radius:0}.lpdv #lpChat>.lpmx>.lp_header{direction:ltr;height:8%;min-height:40px}.lpdv #lpChat>.lpmx>.lp_header .lp_title{width:100%;text-align:center}.lpdv #lpChat>.lpmx>.lp_header .lp_top-text{text-align:center}.lpdv #lpChat>.lpmx>.lp_header button{top:0;right:0;height:100%;width:40px;text-align:center;position:absolute}.lpdv #lpChat>.lpmx>.lp_header .lp_close{right:0}.lpdv #lpChat>.lpmx>.lp_main{height:92%}.lpdv #lpChat>.lpmx>.lp_main .lp_buttons_area{margin-top:3%}.lpdv #lpChat>.lpmx>.lp_main .lp_buttons_area button{font-size:1.2em}.lpdv #lpChat.lp_landscape #lpChat>.lpmx .lp_header{display:none}.lpdv #lpChat.lp_landscape #lpChat>.lpmx>.lp_main{padding-top:0;top:0;height:100%}.lp_mobile #lpChat{font-size:14px}.lp_tablet #lpChat{font-size:18px}.lp_tablet #lpChat>.lpmx .lp_header-content-wrapper .lp_title .lp_top-text{max-width:250px}',{id:He.STYLE_TAG_ID})}function ae(e){var t=document.createElement("iframe");e.appendChild(t);return t.contentWindow.document}function re(){clearTimeout(je)}function le(e){var t=He.WRAPPER_DIV_CSS_CLASS_PREFIX;if(e.landscape){Ge.removeClass(ye,t+He.PORTRAIT_CSS_CLASS);Ge.addClass(ye,t+He.LANDSCAPE_CSS_CLASS)}else{Ge.removeClass(ye,t+He.LANDSCAPE_CSS_CLASS);Ge.addClass(ye,t+He.PORTRAIT_CSS_CLASS)}}function se(){Ge.addClass(Ie,He.EXTERNAL_CSS_CLASS)}function ce(){Xe.hideTopBar&&Ge.addClass(Ie,He.HIDE_TOP_BAR_CSS_CLASS)}function pe(){Xe.NativeSDK&&Ge.addClass(Ie,He.WEBVIEW_CSS_CLASS)}function de(e){document.documentElement.setAttribute("lang",e)}function ue(e){var t=e.getStyle("top","border-radius");if(t){Ee.style.borderRadius=t;Ie.style.borderRadius=t}}function ge(){window.outerWidth<window.innerWidth&&window.outerWidth<He.MINIMUM_INNER_WIDTH&&window.resizeTo(He.MINIMUM_INNER_WIDTH,window.outerHeight)}var fe,me,he,Ce,_e,ve,Se,be,we,ye,Te,Ee,Ie,Ne,Ae,xe,ke,Re,Oe,De,Le,We,Me,Ue,ze,Pe,je,Ve,Be=lpTag.unifiedWindow,He={STYLE_TAG_ID:"lpChatStyle",WRAPPER_DIV_ID:"lpChat",WRAPPER_DIV_CSS_CLASS_PREFIX:"lp_",MOBILE_CSS_CLASS:"mobile",TABLET_CSS_CLASS:"tablet",DESKTOP_CSS_CLASS:"desktop",DEVICE_CLASS:"lpdv",ANDROID_CSS_CLASS:"android",IOS_CSS_CLASS:"ios",IOS6_CSS_CLASS:"ios6",IOS8_OR_ABOVE_CSS_CLASS:"ios8_or_above",MAC_OSX_CSS_CLASS:"mac_osx",NATIVE_CSS_CLASS:"native_android",EXTERNAL_CSS_CLASS:"lp_external-window",HIDE_TOP_BAR_CSS_CLASS:"lp_no-top",WEBVIEW_CSS_CLASS:"lp_webview-window",PORTRAIT_CSS_CLASS:"portrait",LANDSCAPE_CSS_CLASS:"landscape",MINIMIZED_VIEW_IFRAME_RENDERING:!1,MAXIMIZED_VIEW_IFRAME_RENDERING:!1,MINIMUM_INNER_WIDTH:372,WAIT_INDICATION_DELAY:250},Fe=Be.language,Ge=lpTag.taglets.lpUtil,qe=(Be.log,!1),Ke=!1,Xe={isMaximized:!0};G(e,t,n,o);return{render:g,show:d,hide:u,message:c,removeMessage:W,unbindDomEvents:D,setTitle:p,Messages:be,getMinimizedElement:m,getBufferStripElement:h,getMaximizedElement:C,getMainWindowElement:f,getChatWrapperElement:_,getSliderButton:v,getWidgetNotificationContainer:S,getWidgetNotificationText:b,getTitleDiv:w,getChattingWIthIcon:y,dispose:N,showClose:T,showMinimized:E,getIconRepository:M,adjustConfigStyle:I,getWrapperElementId:A}};window.lpTag=window.lpTag||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.defaultDictionary=lpTag.unifiedWindow.defaultDictionary||{error:"Error",couldNotConnect:"Oops - unable to connect.",tryAgain:"Try again",loadingOnMinimize:"Loading",loading:"Loading",unableToConnect:"Unable to connect",reconnect:"Trying to reconnect",chatEndedOnExternalWindow:"Close window in order to start new chat",externalWindowOpen:"You have an ongoing chat. Return to that window to continue the chat.",unsupportedBrowserMode:"Your browser may be in incognito mode, or it may be blocking third party cookies. Please open a regular browser session to chat, or check your browser privacy settings",cannotResumeChat:"Resume chat from the tab where the interaction began",crossDomainError:"Cannot resume conversation, please return to previous page to continue.",chatAuthError:"Go back to the previous page or log in to continue your conversation.",confirmMessage:"Closing the conversation means that you won’t be able to continue even if you go back or log in. Are you sure you want to proceed?",startConversationError:"Please refresh the page and start a new conversation.",connectionUnavailable:"Connection unavailable",sessionExpired:"Session expired",sessionError:"Your session has expired. Please close this window and start a new conversation in order to resume.",sharkSessionExpired:"Please refresh the page and start a new conversation.",tooltip_Close:"Close",tooltip_Minimize:"Minimize",tooltip_open_widgets:"Open widgets",tooltip_close_widgets:"Close widgets",tooltip_refresh_slider:"Refresh",closeWindowOkBtn:"OK",closeWindowCancelBtn:"Cancel","de-DE":{loading:"Loading"}};window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.unifiedWindow=lpTag.unifiedWindow||{};lpTag.unifiedWindow.apps=lpTag.unifiedWindow.apps||{UNIFIED_WINDOW:"lpUnifiedWindow"};lpTag.taglets.lpUnifiedWindow=lpTag.taglets.lpUnifiedWindow||function(){function e(e){var t;a(e);M(Re);pe=window.lpTag.taglets.lpSecureStorage;he=new Ie.DeviceDetector;ge=new lpTag.unifiedWindow.AppConfigurationManager({lpTag:lpTag,utils:lpTag.taglets.lpUtil,deviceDetector:he,queryParams:Pe});j(Re,xe);Re.engConf&&Re.engConf.offsiteEngContext&&(Re.clickedChannel=ge.getClickedChannel(Re.engConf,!0));me=new Ie.BrowserStateManager(he);ce=new lpTag.unifiedWindow.Events({eventBufferLimit:0,cloneEventData:!1});f();B();t=o(Re);t?V(t,!0):i()}function t(e){e=e||{};e.dispose&&ee()}function n(){Re.clickedChannel&&ge&&ge.getClickedChannel&&Re.clickedChannel===ge.getClickedChannel({async:!0})&&!Re.poppedOut&&!Re.external&&ce.trigger({appName:"*",eventName:lpTag.unifiedWindow.events.chat.AUTHENTICATION_ERROR,data:{error:lpTag.unifiedWindow.consts.authErrorsTypes.LOGOUT}})}function i(){var e={debug:Re.debug};e[Ue]={site:Re.accountId,env:Re.env,app:Ue,url:Re.secureStorageLocation,chosenStorageHandler:s};pe.configure(e)}function o(e){var t;e.invalidAuthConnector?t=fe.Messages.SESSION_EXPIRED:e.JsLoadingFailure?t=fe.Messages.CONNECTION_UNAVAILABLE:"#RIP"===window.location.hash&&(t=fe.Messages.RIP);return t}function a(e){lpTag.getExternalConfig&&(Re=lpTag.getExternalConfig());r(e)}function r(e){if(e&&e.constructor===Array){try{Ne.convertConfig(e,Re)}catch(t){}Re.displayShortlyMessage=l()}else e&&(Re=Ne.cloneExtend(Re,e,!0))}function l(){var e=Re.textCustomization&&Re.textCustomization[0]&&Re.textCustomization[0].displayShortlyMessage;return e===!0||"true"===e}function s(e){m(e,g.bind(this,e))}function c(e){e=se(e);if(e.redirect||"undefined"!=typeof Re.embeddedSupported)p(e);else{Re.startedEngConf=e;Re.clickedChannel=ge.getClickedChannel(e,!0)}}function p(e){var t,n;e=se(e);e&&e.preChatLines&&(e.preChatLines=le(e.preChatLines));Re.clickedChannel=ge.getClickedChannel(e);if(je){Ie.Application&&re()&&ce.trigger({appName:Te,eventName:"maximize"});n=Ce.getCurrentSessionKey();if(Re.clickedChannel!==n){t=ge.getEngagementTypeByChannel(n);ce.trigger({appName:Te,eventName:lpTag.unifiedWindow.events.chat.MULTIPLE_CHANNELS_REQUESTED,data:{channelType:t}})}}else if(me.isConnected()){ae()&&ie();if(v(e))_(e);else{if(Re.browserModeUnSupported){V(fe.Messages.UNSUPPORTED,!0);return}f();d(N.bind(this,e))}}else V(fe.Messages.CONNECTION_UNAVAILABLE)}function d(e,t){t&&f();m(null,function(){ue.getState(e,e)})}function u(){return{conf:Ne.clone(Re)}}function g(e){"object"!=typeof e?Re.secureStorageType=e:e&&e.error&&(Re.browserModeUnSupported=!0);de=e;Re.popoutSupported=Re.secureStorageType&&Re.secureStorageType!==pe.storageTypes.SESSIONSTORAGE&&me.visibilitySupported();Re.embeddedSupported=!!Re.secureStorageType;ce.trigger({appName:Te,eventName:Be,global:!0});y()}function f(){if(!fe){fe=new Ie.WrapperWindow(Re,he,me,ce,Q);Re.wrapperElementId=fe.getWrapperElementId()}}function m(e,t){Ce||(Ce=h());Re.clickedChannel&&Ce.setClickedSession(Re.clickedChannel);Ce.getSessionId(C.bind(this,e,t))}function h(){return new Ie.SessionManager({accountId:Re.accountId,sessionId:Re.sessionId,external:Re.external,events:ce,sessionsPriority:ge.getChannelsPriority(Re),sessions:ge.getChannels(Re),clickedSession:Re.clickedChannel,secureStorageLocation:Re.secureStorageLocation,echoedSessionTimeout:Re.sessionTimeout})}function C(e,t,n){if(n){Re.sessionId=n;ue||(ue=new Ie.StateAnalyzer({storageKey:Re.sessionId,accountId:Re.accountId,disableSessionTimeout:Re.disableSessionTimeout,secureStorageLocation:Re.secureStorageLocation},Ce))}Ne.runCallback(t,null,e)}function _(e){e.redirect||w()&&b()?window.location.href=ge.getExternalResourceURL(e,Re,!1,!0):Re.openedExternal=ge.openExternal(e,Re,!1,!0)}function v(e){return!Re.embeddedSupported||e.isPopOut||b()||ge.isRecaptchaEnabled()}function S(){return Re.poppedOut||Re.external}function b(){return!he.isMobileOptimized()&&!he.isDesktop()}function w(){return Re.startedEngConf}function y(){ue&&ue.getState(I,I)}function T(e,t,n){if(E())V(fe.Messages.EXTERNAL_OPEN,!0);else{Re.poppedOut&&(t=ue.state.IN_SESSION);we=t;switch(t){case ue.state.INVALID:te();return;case ue.state.IN_SESSION:return{uiState:e};case ue.state.NOT_STARTED:return n?{engConf:n}:void 0;case ue.state.EXPIRED:te();return null}}}function E(){if(Re.openedExternal){if(!Re.openedExternal.closed)return!0;Re.openedExternal=void 0;delete Re.openedExternal}return!1}function I(e,t){var n=T(e,t);if(Re.external){n&&n.uiState&&n.uiState.chat&&n.uiState.chat.state===We&&(n.uiState={engConf:n.uiState.engConf});O(n||Re)}else n?me.isConnected()?O(n):V(fe.Messages.CONNECTION_UNAVAILABLE):w()&&p(Re.startedEngConf)}function N(e,t,n){var i=T(t,n,e);i&&O(i)}function A(e){if(ve&&!0===De&&Oe&&!Se){var t={window:ve,unified:Re,uiState:e};Ce.refresh(x.bind(this,t))}}function x(e){Se=Ie.Application(e,{appConfigurationManager:ge,browserStateManager:me,sessionManager:Ce,deviceDetector:he,wrapperWindow:fe,events:ce})}function k(e,t){if(t&&!t.error){ve=t;A(e)}else V(fe.Messages.CONNECTION_UNAVAILABLE)}function R(e,t){if(t){V(fe.Messages.CONNECTION_UNAVAILABLE);te()}else{De=!0;A(e)}}function O(e){var t;if(!je){je=!0;if(e.uiState)t=e.uiState;else{t=Ne.cloneExtend(xe);t.engConf=e.engConf;e.engConf&&e.engConf.minimiseOnStart&&(t.window.maximized=!1)}t.engConf.sessionKey=Ce.getCurrentSessionKey();fe.render(t,function(){Oe=!0;fe.message(fe.Messages.WAIT);A(t)},!0);var n={storageKey:Re.sessionId,accountId:Re.accountId,secureStorageLocation:Re.secureStorageLocation,domain:Re.acCdnDomain};t.engConf&&"undefined"!=typeof t.engConf.lewid&&(n.windowId=t.engConf.lewid);_e=new Ie.WindowConfigurationManager(n);_e.getConf(k.bind(this,t));W(R.bind(this,t),t)}}function D(e){Re.isMessaging=!!e.async;var t={},n=Re.isMessaging?"UMSClientAPI":"lpChatV3",i=he.getDeviceFamilyName().toLowerCase();if(Ie.Application)Se||ze[n]||(t=Re.loadObj||{api:Re.codeRepository+"/"+n+".min.js"+Ae});else{t=Re.loadObj||{framework:Re.codeRepository+"/ui-framework.js"+Ae,api:Re.codeRepository+"/"+n+".min.js"+Ae,surveyLogic:Re.codeRepository+"/surveylogicinstance.min.js"+Ae,unified:{dependency:["framework","api","surveyLogic"],url:Re.codeRepository+"/"+i+"Embedded.js"+Ae}};ze[n]=!0}if(S()){ye=z();lpTag.ovr=lpTag.ovr||{domain:ye};lpTag.debug=Re.debug?"2":"1"}e&&e.lang&&"en-us"!==e.lang.toLowerCase()&&(t.lang=Re.langRepository+"/"+e.lang+".js"+Ae);!v(e)&&ge.isAuthenticatedEnabled(e)&&(t.xhr=Re.codeRepository+"/xhr.js"+Ae);Re.widgetSDK&&(t.widgetSDK="https://"+Re.leCdnDomain+"/unifiedwindow/widgetSDK.min.js"+Ae);return t}function L(e,t){Ne.isEmpty(e)?Ne.runCallback(t):lpTag.taglets.jsLoader.loadJS({loadObj:e,success:function(){Ne.runCallback(t)},error:function(e){Ne.runCallback(t,null,e||{})}})}function W(e,t){var n=D(t.engConf);lpTag.loadTaglets&&!S()?lpTag.loadTaglets({scp:Re.scp,excludeWhitelist:!0,success:function(){L(n,e)},error:function(t){Ne.runCallback(e,null,t||{})}}):L(n,e)}function M(e){e.accountId=""+(e.accountId||lpTag.site);e.domain=lpTag.csds.getDomain(Me.ADMIN_AREA)||lpTag.csds.getDomain(Me.ALL);e.domain="hc1"===e.domain?"hc1.dev.lprnd.net":e.domain;e.env=e.env||U();e.asyncMessagingDomain=lpTag.csds.getDomain(Me.ASYNC_MESSAGING);e.idpDomain=lpTag.csds.getDomain(Me.IDP);e.messagingHistoryDomain=lpTag.csds.getDomain(Me.MESSAGING_HISTORY);e.debug=P()}function U(){return"function"==typeof lpTag.getEnv&&lpTag.getEnv()}function z(){return"function"==typeof lpTag.getTagDomain&&lpTag.getTagDomain(Re.env)}function P(){return Re.debug||Re.accountId&&(0===Re.accountId.indexOf("qa")||0===Re.accountId.indexOf("le"))||Re.domain&&0===Re.domain.indexOf("hc1")}function j(e,t){var n,i,o,a;e.leCdnDomain=e.leCdnDomain||lpTag.csds.getDomain(Me.LECDN);e.acCdnDomain=e.acCdnDomain||lpTag.csds.getDomain(Me.ACCDN);n="https://"+e.leCdnDomain+"/";o="le_unified_window";i=o+ge.getUWDir(e,Ee);a=n+"le_secure_storage/"+(pe.v?pe.v+"/":"");e.staticCodeRepository=n+(e.codeRepository?e.codeRepository:o);e.codeRepository=n+(e.codeRepository?e.codeRepository:i);e.imagesRepository=n+(e.imagesRepository?e.imagesRepository:i+"/resources");e.iconsRepository=n+(e.iconsRepository?e.iconsRepository:i+"/resources/icons")+"/"+he.getDeviceFamilyName().toLowerCase();e.audioRepository=n+(e.audioRepository?e.audioRepository:i+"/resources/audio");e.langRepository=n+(e.langRepository?e.langRepository:i+"/resources/i18n");e.agentImageRepository=n+(e.agentImageRepository?e.agentImageRepository:i+"/resources/agentImages");e.secureStorageLocation=e.external?pe.sessionStorageStaticDomain:e.secureStorageLocation||a;(he.isMobile()||he.isTablet())&&(t.position={right:0});t.window.embedded=!e.external;if(!t.window.embedded){e.NativeSDK="1"==Pe.sdk;e.IOS="1"==Pe.ios}e.supportBlockCCPattern=!!lpTag.taglets.cleanCCPatterns;e.scp=e.scp||"uw"}function V(e,t){be&&clearTimeout(be);fe.render(null,function(){fe.message(e,!0)});t||S()||(be=setTimeout(function(){fe&&fe.dispose()},Le))}function B(){me.on(me.EVENT_NAME.FOCUS_CHANGE,q);ce.bind({appName:"ChatStateManager",eventName:"ended",func:te});ce.bind({appName:"ConversationManager",eventName:"ended",func:ie});ce.bind({appName:"Application",eventName:"appEnded",func:ie});ce.bind({appName:"*",eventName:"doHaraKiri(Seppuku)",func:ne});ce.bind({appName:"SessionManager",eventName:"sessionChanged",func:Y});ce.bind({appName:"ChatAPIV3",eventName:"error",func:F});ce.bind({appName:"API",eventName:"error",func:F});ce.bind({appName:"API",eventName:"error",func:G});ce.bind({appName:"ChatStateManager",eventName:"startChatInfo",func:H});ce.bind({appName:"*",eventName:"forgetMe",func:te});ce.bind({appName:"*",eventName:"knockout",func:oe})}function H(e){e.chatTimeout&&(Re.sessionTimeout=e.chatTimeout)}function F(e){var t=fe.Messages[e&&e.errorType]||fe.Messages.CONNECTION_UNAVAILABLE;V(t,e&&e.keepError)}function G(e){Ve=e&&e.errorType===lpTag.unifiedWindow.consts.errorTypes.AUTH_ERROR?ie:Ve}function q(e){e.focus&&we===ke&&K()}function K(){Ce=Ce||h();Ce.setClickedSession(Re.clickedChannel);Ce.getSession(X)}function X(e){if(e&&e.sid&&"null"!==e.sid){Ce=null;d(Z,!0)}}function Y(){Ce&&Ce.getSession(J)}function J(e){if(e&&e.sid&&"null"!==e.sid){te(!0);f();d(Z)}else te()}function Z(e,t){we=t;switch(t){case ue.state.INVALID:V(fe.Messages.IN_SESSION);break;case ue.state.IN_SESSION:$(e.engConf);break;case ue.state.NOT_STARTED:ie()}}function $(e){ie();p(e)}function Q(){Re.clickedChannel=null;Ne.runCallback(Ve)}function ee(){te();ie()}function te(e){je=!1;be&&clearTimeout(be);we=ke;if(Ce){Ce.dispose(e);Ce=null}ve=null;if(_e){_e.clear();_e=null}Re.sessionId=null;delete Re.sessionId;ue=null}function ne(){fe&&fe.dispose()}function ie(){De=!1;Oe=!1;if(Se){Se.methods.end();Se=null;je=!1}fe=null;ce.trigger({appName:Te,eventName:"windowClosed",global:!0});lpTag.newPage(document.URL)}function oe(){ie();te()}function ae(){return Ne.getPropertyFromObject(Se,"methods.isDisposed")&&Se.methods.isDisposed()}function re(){return Ne.getPropertyFromObject(Se,"methods.isDisposed")&&!Se.methods.isDisposed()}function le(e){var t="msg",n=0,i={};e.forEach(function(e){i[t+ ++n]=e});return i}function se(e){var t=Ne.getPropertyFromObject(e,"connector.configuration.acrValues");"undefined"!=typeof t&&delete e.connector.configuration.acrValues;return e}var ce,pe,de,ue,ge,fe,me,he,Ce,_e,ve,Se,be,we,ye,Te="lpUnifiedWindow",Ee="9.6.0.1-release_4127",Ie=lpTag.unifiedWindow,Ne=lpTag.taglets.lpUtil,Ae="?version="+(Ee||(new Date).getTime().toString()),xe={window:{maximized:!0,position:{right:"20px",bottom:0},sound:!0,notificationCount:0,actionsVisible:!1,embedded:!0}},ke=3,Re={},Oe=!1,De=!1,Le=5e3,We="applicationEnded",Me={ADMIN_AREA:"adminArea",ALL:"ALL",LECDN:"leCdnDomain",ACCDN:"acCdnDomain",ASYNC_MESSAGING:"asyncMessagingEnt",MESSAGING_HISTORY:"msgHist",IDP:"idp"},Ue=Ie.apps.UNIFIED_WINDOW,ze={UMSClientAPI:!1,lpChatV3:!1},Pe=Ne.getURLParams(window.location.search),je=!1,Ve=ee,Be="STORAGE_SELECTED";return{v:Ee,name:Te,init:e,reinit:n,onBeforeNavigation:t,startFlow:c,clicked:p,inspect:u}}();}catch(e){lpTag.handleGeneralError("lpUnifiedWindow",e);}try{window.lpTag=lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.SMT=lpTag.taglets.SMT||function(){function a(){}var t="SMT",g="2.21.0";return{v:g,name:t,init:a}}();}catch(e){lpTag.handleGeneralError("SMT",e);}try{"use strict";var _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(o){return typeof o}:function(o){return o&&"function"==typeof Symbol&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o};window.lpTag=window.lpTag||{};window.lpTag.taglets=window.lpTag.taglets||{};window.lpTag.taglets.hooks=window.lpTag.taglets.hooks||function(o){function n(){c={};e(o.hooks);t()}function t(){o.hooks=o.taglets.hooks}function e(o){o=Array.isArray(o)?o:[o];o.length>0&&o.forEach(function(o){if(a(o)){c[o.name]=c[o.name]||[];c[o.name].push(o.callback)}})}function a(n){if(!n){o.log("Hook is not defined","ERROR","hooks");return!1}if("string"!=typeof n.name){o.log("Hook name '"+n.name+"' is not a string","ERROR","hooks");return!1}if("function"!=typeof n.callback){o.log("Hook callback for '"+n.name+"' is not a function","ERROR","hooks");return!1}o.log("Hook callback for '"+n.name+"' was added successfully","INFO","hooks");return!0}function i(n){if(n&&n.name){var t=c[n.name]||[];t.forEach(function(t){if("function"==typeof t)try{var e=t(n);if("object"===("undefined"==typeof e?"undefined":_typeof(e))){n=e;o.log("Hook '"+n.name+"' returned data successfully","INFO","hooks")}else o.log("Hook '"+n.name+"' callback didn't return an object","ERROR","hooks")}catch(a){o.log("Hook callback threw an exception: "+a.message,"ERROR","hooks")}})}return n}var l="hooks",r="0.0.2",c={};return{version:r,name:l,init:n,push:e,exec:i}}(window.lpTag);}catch(e){lpTag.handleGeneralError("hooks",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.lp_SMT=lpTag.taglets.lp_SMT||function(){function e(e){if(e)for(var t=0;t<e.length;t++){var n=e[t].value;"string"==typeof n&&""!==n&&("["===n.charAt(0)||"{"===n.charAt(0))&&("undefined"!=typeof JSON&&JSON.parse?n=JSON.parse(n):s("unable to parse JSON, no JSON object on page"));_[e[t].id]=n}else c("No cfg were given on init")}function t(){_.active=!0;if(lpTag.taglets.lp_monitoringSDK.inspect().conf.baseUrl===lpTag.getDomain("ALL")){_.active=!1;s("Monitoring set to inactive! smt domain was not found")}lpTag.events.trigger(b,"MONITORING_STATE",{active:_.active});O=lpTag.events.bind("lp_monitoringSDK","SP_SENT",a);S(q.sp)}function n(){clearTimeout(m);lpTag.events.unbind(O);N&&lpTag.events.unbind(N);_.active=!1}function i(t){clearTimeout(m);lpTag.events.unbind(O);N&&lpTag.events.unbind(N);e(t)}function o(){return _.active}function a(){lpTag.isDom?S(q.pl):N=lpTag.events.bind("LPT","DOM_READY",function(){S(q.pl)})}function r(e,t){window.lpTag&&lpTag.log&&lpTag.log(e,t,b)}function s(e){r(e,"ERROR")}function l(e){r(e,"DEBUG")}function c(e){r(e,"INFO")}function g(e){c("success call made by smt with the following result: "+("string"!=typeof e?JSON.stringify(e):e))}function p(e){s("error returned to smt callback onError: "+("string"!=typeof e?JSON.stringify(e):e))}function u(e){return e.status===A.ok&&!e.error}function f(e){d(e);v();if(_.keepAliveFreq&&_.active){if(e.reqType===q.pl||e.reqType===q.ip){m=setTimeout(function(){S(q.ip)},1e3*_.keepAliveFreq);l("Next request to be issued in "+_.keepAliveFreq+"sec")}}else lpTag.taglets.lp_monitoringSDK.stop();T(u(e)?g:p,e,"Client onSuccess callback returned an error: ")}function T(e,t,n){try{e(t)}catch(i){s(n+JSON.stringify(i.message))}}function v(){_.active||lpTag.events.trigger(b,"MONITORING_STATE",{active:_.active})}function d(e){if(e.smtConf){for(var t in e.smtConf)_[t]=e.smtConf[t];delete e.smtConf}}function S(e){if(_.active){lpTag.taglets.lp_monitoringSDK[e](function(t){t.status=A.ok;t.reqType=e;f(t)},function(t){t.status=A.err;t.reqType=e;f(t)});l("Sending"+e+"request")}else c("Monitoring is set to inactive, not sending any requests")}var m,O,N,y="2.21.0",b="lp_SMT",q={sp:"startPage",pl:"pageLoaded",ip:"inPage"},A={err:"ERROR",ok:"OK"},_={keepAliveFreq:10,active:!0};return{v:y,name:b,inspect:function(){return _},init:e,start:t,reinit:i,restart:t,stop:n,isActive:o}}();}catch(e){lpTag.handleGeneralError("lp_SMT",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.authenticator=lpTag.taglets.authenticator||function(){function t(){lpTag.log("authenticator init called","DEBUG","authenticator")}function e(t,e,i){return t.type===p.OAUTH_IMPLICIT||t.type===p.OAUTH_CODE?n(t,e,i):r(t,e,i)}function n(t,e,n){var r=o(n),u=i(e,r);t=t||{};lpTag.log("_getOAuthSSOKey: "+g,"DEBUG","authenticator");lpTag.taglets.lpUtil.runCallbackByObject(t.configuration,u,r)}function r(t,e,n){lpTag.log("_getServer2ServerSSOKey"+g,"DEBUG","authenticator");lpTag.taglets.xhr.issueCall({url:t.configuration.genKeyUrl,method:"GET",query:{rt:"json"},retries:f.retries,timeout:f.timeout,success:u(e),error:o(n)})}function i(t,e){return function(n,r){var i=null===r||"undefined"==typeof r;if(i&&n){var o,u;if("object"==typeof n){o=n.ssoKey;u=n.redirect_uri}else o=n;t({ssoKey:o,redirect_uri:u})}else e()}}function o(t){return function(e){l=!0;"function"==typeof t&&t(e)}}function u(t){return function(e){t(e.body||{})}}function a(){return l?!0:!1}function c(){var t={name:g,ssoKeyGenTransport:{timeout:f.timeout,retries:f.retries}};return t}var l,s="0.1.0",g="authenticator",f={timeout:3e4,retries:3,successResponseCode:201},p={S2S:0,OAUTH_IMPLICIT:1,OAUTH_CODE:2};return{v:s,init:t,getSSOKey:e,isErroneous:a,inspect:c}}();}catch(e){lpTag.handleGeneralError("authenticator",e);}try{window.lpTag=window.lpTag||{};lpTag.taglets=lpTag.taglets||{};lpTag.taglets.jsLoader=lpTag.taglets.jsLoader||function(e){function t(e){var t=[],n=[],a=!1;for(var r in e)if("string"==typeof e[r]||e[r].url)if(e[r].dependency){var l=o(e,e[r].dependency);l.length>0&&(t=t.concat(l))}else"string"==typeof e[r]||"string"==typeof e[r].url?a=!0:n.push(e[r]);else"string"!=typeof e[r]&&n.push(r);return{missingKeys:t,invalidUrls:n,requestValid:0===t.length&&0===n.length&&a}}function n(){lpTag.log("JSLoader was initialised","DEBUG",v)}function a(e){T+=1;var n="";if(e&&e.loadObj){var a=t(e.loadObj);if(a.requestValid){e.startTime=new Date;for(var r in e.loadObj)if(e.loadObj[r]!==!0&&!e.loadObj[r].dependency){n=e.loadObj[r].url||e.loadObj[r];f(n,e.context,i(r,e))}}else j(e.error,e.context||null,a)}}function r(){return{version:S,name:v,errors:m,downloads:E,configsCount:T}}function o(e,t){var n=[];t=t.constructor===Array?t:[t];for(var a=0;a<t.length;a++)e[t[a]]||n.push(t[a]);return n}function l(e,t,n){m+=1;n.loadError=n.loadError||[];n.loadError.push({key:e,url:t});return!0}function d(e,t,n){E+=1;if("object"==typeof t.loadObj[e]){var a=t.loadObj[e];j(n&&a.error?a.error:a.success,a.context||null,n?{error:!0,data:a}:{data:a})}t.loadObj[e]=!0}function c(e){var t=!0;for(var n in e.loadObj)if(e.loadObj.hasOwnProperty(n)&&e.loadObj[n]!==!0){t=!1;break}return t}function i(e,t){return function(n,a){d(e,t,a);a&&l(e,n,t);u(e,t);c(t)&&s(t,!!t.loadError)}}function s(e,t){e.endTime=new Date;setTimeout(function(){j(t?e.error:e.success,e.context,t?e.loadError:e);e=null},10)}function u(e,t){var n;for(var a in t.loadObj)if("object"==typeof t.loadObj[a]){t.loadObj[a].dependency=t.loadObj[a].dependency&&t.loadObj[a].dependency.constructor===Array?t.loadObj[a].dependency:[t.loadObj[a].dependency];n=[];for(var r=0;r<t.loadObj[a].dependency.length;r++)t.loadObj[a].dependency[r]!==e&&n.push(t.loadObj[a].dependency[r]);if(n.length!==t.loadObj[a].dependency.length){t.loadObj[a].dependency=n;0===n.length&&f(t.loadObj[a].url,t.loadObj[a].context,i(a,t))}}}function f(e,t,n){if(w[e])w[e].state===R.LOADING?g(n,w[e].callbackQueue):w[e].state===R.SUCCESS&&j(n,t,[e]);else{w[e]=p(R.LOADING);g(n,w[e].callbackQueue);b(e,{success:function(){w[e].state=R.SUCCESS;O.call(this,e,t,w[e].callbackQueue)},error:function(){w[e].state=R.ERROR;y.call(this,e,t,w[e].callbackQueue)}})}}function p(e,t){return{state:e,callbackQueue:t||[]}}function b(e,t){var n=document.createElement("script");n.setAttribute("type","text/javascript");n.setAttribute("charset","UTF-8");n.onreadystatechange=n.onload=t.success;n.onerror=t.error;n.setAttribute("src",e);(document.head||document.getElementsByTagName("head")[0]).appendChild(n)}function O(t,n,a){var r=!1;this.readyState?e.opera||"loaded"!==this.readyState&&"complete"!==this.readyState||(r=!0):r=!0;if(r){h(t,n,a,!1);this.onerror=this.onload=this.onreadystatechange=null}}function y(e,t,n){h(e,t,n,!0);this.onerror=this.onload=this.onreadystatechange=null}function g(e,t){t&&-1===t.indexOf(e)&&t.push(e)}function h(e,t,n,a){for(var r;n.length;){r=n.pop();j(r,t,e,a)}}function j(t,n){n=n||e;if("function"==typeof t){var a=Array.prototype.slice.call(arguments,2);try{return t.apply(n,a)}catch(r){lpTag.log("Failed to execute callback exc= "+r.message,"ERROR",v)}}}var v="jsLoader",S="1.0.1",E=0,m=0,T=0,R={LOADING:"LOADING",SUCCESS:"SUCCESS",ERROR:"ERROR"},w={};return{v:S,init:n,loadJS:a,validateLoadObj:t,inspect:r}}(window);}catch(e){lpTag.handleGeneralError("jsLoader",e);}