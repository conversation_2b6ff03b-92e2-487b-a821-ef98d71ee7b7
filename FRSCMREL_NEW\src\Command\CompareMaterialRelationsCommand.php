<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;

#[AsCommand(
    name: 'app:compare-material-relations',
    description: 'Comparer les relations Document ↔ Material entre ancienne et nouvelle base'
)]
class CompareMaterialRelationsCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('package', null, InputOption::VALUE_OPTIONAL, 'Numéro de package à analyser (optionnel)')
            ->addOption('detailed', null, InputOption::VALUE_NONE, 'Affichage détaillé des différences')
            ->addOption('missing-only', null, InputOption::VALUE_NONE, 'Afficher seulement les relations manquantes');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $packageNum = $input->getOption('package');
        $detailed = $input->getOption('detailed');
        $missingOnly = $input->getOption('missing-only');

        $output->writeln("<info>Comparaison des relations Document ↔ Material</info>");
        if ($packageNum) {
            $output->writeln("<comment>Package analysé: {$packageNum}</comment>");
        } else {
            $output->writeln("<comment>Analyse de tous les packages</comment>");
        }

        try {
            // Connexion directe à db_release
            $legacyPdo = new \PDO(
                'mysql:host=localhost;port=3306;dbname=db_release;charset=utf8mb4',
                'root',
                'Lemans72!'
            );
            $legacyPdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

            // 1. Récupérer toutes les relations attendues depuis l'ancienne base
            $whereClause = $packageNum ? "AND Rel_Pack_Num = ?" : "";
            $params = $packageNum ? [$packageNum] : [];
            
            $legacyDocsSql = "SELECT Reference, FXXX, Rel_Pack_Num FROM tbl_released_drawing WHERE FXXX IS NOT NULL AND TRIM(FXXX) != '' $whereClause ORDER BY Rel_Pack_Num, Reference";
            $stmt = $legacyPdo->prepare($legacyDocsSql);
            $stmt->execute($params);
            $legacyDocs = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            $output->writeln("<info>Trouvé " . count($legacyDocs) . " documents avec matériaux dans l'ancienne base</info>");

            // 2. Analyser chaque relation attendue
            $stats = [
                'total_expected_relations' => 0,
                'documents_found' => 0,
                'documents_not_found' => 0,
                'materials_found' => 0,
                'materials_not_found' => 0,
                'relations_exist' => 0,
                'relations_missing' => 0,
                'multiple_materials_docs' => 0
            ];

            $missingRelations = [];
            $missingDocuments = [];
            $missingMaterials = [];

            foreach ($legacyDocs as $legacyDoc) {
                $reference = $legacyDoc['Reference'];
                $materialRefs = $legacyDoc['FXXX'];
                $packageNum = $legacyDoc['Rel_Pack_Num'];

                // Séparer les matériaux multiples
                $materialRefArray = array_map('trim', explode(',', $materialRefs));
                
                if (count($materialRefArray) > 1) {
                    $stats['multiple_materials_docs']++;
                }

                // Vérifier si le document existe dans la nouvelle base
                $documentSql = 'SELECT id FROM document WHERE reference = ? AND rel_pack_id = ?';
                $documentResult = $this->em->getConnection()->fetchAssociative($documentSql, [$reference, $packageNum]);

                if (!$documentResult) {
                    $stats['documents_not_found']++;
                    $missingDocuments[] = [
                        'reference' => $reference,
                        'package' => $packageNum,
                        'materials' => $materialRefs
                    ];
                    
                    // Compter les relations qui auraient dû exister
                    $stats['total_expected_relations'] += count($materialRefArray);
                    $stats['relations_missing'] += count($materialRefArray);
                    continue;
                }

                $stats['documents_found']++;
                $documentId = $documentResult['id'];

                // Vérifier chaque matériau
                foreach ($materialRefArray as $materialRef) {
                    if (empty($materialRef)) continue;

                    $stats['total_expected_relations']++;

                    // Vérifier si le matériau existe
                    $materialSql = 'SELECT id FROM material WHERE reference = ?';
                    $materialResult = $this->em->getConnection()->fetchAssociative($materialSql, [$materialRef]);

                    if (!$materialResult) {
                        $stats['materials_not_found']++;
                        $stats['relations_missing']++;
                        $missingMaterials[] = [
                            'material' => $materialRef,
                            'document' => $reference,
                            'package' => $packageNum
                        ];
                        continue;
                    }

                    $stats['materials_found']++;
                    $materialId = $materialResult['id'];

                    // Vérifier si la relation existe
                    $relationSql = "SELECT COUNT(*) as count FROM document_materials WHERE document_id = ? AND material_id = ?";
                    $relationResult = $this->em->getConnection()->fetchAssociative($relationSql, [$documentId, $materialId]);

                    if ($relationResult['count'] > 0) {
                        $stats['relations_exist']++;
                    } else {
                        $stats['relations_missing']++;
                        $missingRelations[] = [
                            'document' => $reference,
                            'material' => $materialRef,
                            'package' => $packageNum,
                            'document_id' => $documentId,
                            'material_id' => $materialId
                        ];
                    }
                }
            }

            // 3. Afficher les statistiques
            if (!$missingOnly) {
                $this->displayStatistics($output, $stats);
            }

            // 4. Afficher les détails si demandé
            if ($detailed || $missingOnly) {
                $this->displayDetails($output, $missingDocuments, $missingMaterials, $missingRelations, $missingOnly);
            }

            // 5. Résumé final
            $completionRate = $stats['total_expected_relations'] > 0 
                ? round(($stats['relations_exist'] / $stats['total_expected_relations']) * 100, 2)
                : 100;

            $output->writeln("\n<comment>Résumé:</comment>");
            $output->writeln("  - Taux de complétude: <info>{$completionRate}%</info>");
            
            if ($stats['relations_missing'] > 0) {
                $output->writeln("  - <error>{$stats['relations_missing']} relations manquantes sur {$stats['total_expected_relations']} attendues</error>");
                return Command::FAILURE;
            } else {
                $output->writeln("  - <info>Toutes les relations sont présentes !</info>");
                return Command::SUCCESS;
            }

        } catch (\Exception $e) {
            $output->writeln("<error>Erreur: " . $e->getMessage() . "</error>");
            return Command::FAILURE;
        }
    }

    private function displayStatistics(OutputInterface $output, array $stats): void
    {
        $output->writeln("\n<comment>Statistiques globales:</comment>");
        $output->writeln("  - Relations attendues: " . $stats['total_expected_relations']);
        $output->writeln("  - Documents avec matériaux multiples: " . $stats['multiple_materials_docs']);
        $output->writeln("  - Documents trouvés: " . $stats['documents_found']);
        $output->writeln("  - Documents non trouvés: " . $stats['documents_not_found']);
        $output->writeln("  - Matériaux trouvés: " . $stats['materials_found']);
        $output->writeln("  - Matériaux non trouvés: " . $stats['materials_not_found']);
        $output->writeln("  - Relations existantes: <info>" . $stats['relations_exist'] . "</info>");
        $output->writeln("  - Relations manquantes: <error>" . $stats['relations_missing'] . "</error>");
    }

    private function displayDetails(OutputInterface $output, array $missingDocuments, array $missingMaterials, array $missingRelations, bool $missingOnly): void
    {
        if (!empty($missingDocuments) && !$missingOnly) {
            $output->writeln("\n<error>Documents non trouvés (" . count($missingDocuments) . "):</error>");
            foreach (array_slice($missingDocuments, 0, 10) as $doc) {
                $output->writeln("  - {$doc['reference']} (Package: {$doc['package']}) → Matériaux: {$doc['materials']}");
            }
            if (count($missingDocuments) > 10) {
                $output->writeln("  ... et " . (count($missingDocuments) - 10) . " autres");
            }
        }

        if (!empty($missingMaterials) && !$missingOnly) {
            $output->writeln("\n<error>Matériaux non trouvés (" . count($missingMaterials) . "):</error>");
            $uniqueMaterials = array_unique(array_column($missingMaterials, 'material'));
            foreach (array_slice($uniqueMaterials, 0, 10) as $material) {
                $count = count(array_filter($missingMaterials, fn($m) => $m['material'] === $material));
                $output->writeln("  - {$material} (utilisé dans {$count} documents)");
            }
            if (count($uniqueMaterials) > 10) {
                $output->writeln("  ... et " . (count($uniqueMaterials) - 10) . " autres matériaux");
            }
        }

        if (!empty($missingRelations)) {
            $output->writeln("\n<error>Relations manquantes (" . count($missingRelations) . "):</error>");
            foreach (array_slice($missingRelations, 0, 15) as $relation) {
                $output->writeln("  - Document {$relation['document']} ↔ Matériau {$relation['material']} (Package: {$relation['package']})");
            }
            if (count($missingRelations) > 15) {
                $output->writeln("  ... et " . (count($missingRelations) - 15) . " autres relations");
            }
        }
    }
}
