<?php

namespace App\Command;

use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;

#[AsCommand(
    name: 'app:fix-material-relations-pdo',
    description: 'Corriger les relations Document ↔ Material avec PDO direct'
)]
class FixMaterialRelationsPDOCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this
            ->addOption('package', null, InputOption::VALUE_OPTIONAL, 'Numéro de package à corriger (optionnel)')
            ->addOption('dry-run', null, InputOption::VALUE_NONE, 'Mode simulation sans modification')
            ->addOption('create-missing-materials', null, InputOption::VALUE_NONE, 'Créer automatiquement les matériaux manquants avec statut inactif');
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $packageNum = $input->getOption('package');
        $dryRun = $input->getOption('dry-run');
        $createMissingMaterials = $input->getOption('create-missing-materials');

        $output->writeln("<info>Correction des relations Document ↔ Material avec PDO direct</info>");
        if ($dryRun) {
            $output->writeln("<comment>Mode simulation activé - aucune modification ne sera effectuée</comment>");
        }
        if ($createMissingMaterials) {
            $output->writeln("<comment>Création automatique des matériaux manquants activée</comment>");
        }

        try {
            // Connexion directe à db_release
            $legacyPdo = new \PDO(
                'mysql:host=localhost;port=3306;dbname=db_release;charset=utf8mb4',
                'root',
                'Lemans72!'
            );
            $legacyPdo->setAttribute(\PDO::ATTR_ERRMODE, \PDO::ERRMODE_EXCEPTION);

            // Récupérer les documents avec leurs matériaux depuis l'ancienne base
            $whereClause = $packageNum ? "AND Rel_Pack_Num = ?" : "";
            $params = $packageNum ? [$packageNum] : [];

            $legacyDocsSql = "SELECT Reference, FXXX, Rel_Pack_Num FROM tbl_released_drawing WHERE FXXX IS NOT NULL AND TRIM(FXXX) != '' $whereClause ORDER BY Rel_Pack_Num, Reference";
            $stmt = $legacyPdo->prepare($legacyDocsSql);
            $stmt->execute($params);
            $legacyDocs = $stmt->fetchAll(\PDO::FETCH_ASSOC);

            $output->writeln("<info>Trouvé " . count($legacyDocs) . " documents avec matériaux dans l'ancienne base</info>");

            $stats = [
                'documents_not_found' => 0,
                'materials_not_found' => 0,
                'materials_created' => 0,
                'relations_exist' => 0,
                'relations_created' => 0,
                'multiple_materials' => 0
            ];

            $relationsToCreate = [];

            foreach ($legacyDocs as $legacyDoc) {
                $reference = $legacyDoc['Reference'];
                $materialRefs = $legacyDoc['FXXX'];
                $packageNum = $legacyDoc['Rel_Pack_Num'];



                // Séparer les matériaux multiples (séparés par des virgules)
                $materialRefArray = array_map('trim', explode(',', $materialRefs));

                if (count($materialRefArray) > 1) {
                    $stats['multiple_materials']++;
                    $output->writeln("<comment>Document {$reference} a plusieurs matériaux: " . implode(', ', $materialRefArray) . "</comment>");
                }

                // Trouver l'ID du document dans la nouvelle base en utilisant référence ET package
                $documentSql = 'SELECT id FROM document WHERE reference = ? AND rel_pack_id = ?';
                $documentResult = $this->em->getConnection()->fetchAssociative($documentSql, [$reference, $packageNum]);

                if (!$documentResult) {
                    $stats['documents_not_found']++;
                    $output->writeln("<comment>Document non trouvé: {$reference} (Package: {$packageNum})</comment>");
                    continue;
                }

                $documentId = $documentResult['id'];


                // Traiter chaque matériau
                foreach ($materialRefArray as $materialRef) {
                    if (empty($materialRef)) continue;

                    // Trouver l'ID du matériau
                    $materialSql = 'SELECT id FROM material WHERE reference = ?';
                    $materialResult = $this->em->getConnection()->fetchAssociative($materialSql, [$materialRef]);

                    if (!$materialResult) {
                        if ($createMissingMaterials && !$dryRun) {
                            // Créer le matériau manquant avec statut inactif
                            $materialId = $this->createMissingMaterial($materialRef, $output);
                            if ($materialId) {
                                $stats['materials_created']++;
                                $output->writeln("<info>Matériau créé: {$materialRef} (ID: {$materialId}) - Statut: INACTIF</info>");
                            } else {
                                $stats['materials_not_found']++;
                                $output->writeln("<error>Erreur lors de la création du matériau: {$materialRef}</error>");
                                continue;
                            }
                        } else {
                            $stats['materials_not_found']++;
                            if ($createMissingMaterials && $dryRun) {
                                $output->writeln("<comment>Matériau à créer: {$materialRef} pour document {$reference} (Package: {$packageNum})</comment>");
                            } else {
                                $output->writeln("<comment>Matériau non trouvé: {$materialRef} pour document {$reference} (Package: {$packageNum})</comment>");
                            }
                            continue;
                        }
                    } else {
                        $materialId = $materialResult['id'];
                    }


                    // Vérifier si la relation existe déjà
                    $relationSql = "SELECT COUNT(*) as count FROM document_materials WHERE document_id = ? AND material_id = ?";
                    $relationResult = $this->em->getConnection()->fetchAssociative($relationSql, [$documentId, $materialId]);

                    if ($relationResult['count'] > 0) {
                        $stats['relations_exist']++;

                        continue;
                    }


                    // Ajouter à la liste des relations à créer
                    $relationsToCreate[] = [
                        'document_id' => $documentId,
                        'material_id' => $materialId,
                        'document_ref' => $reference,
                        'material_ref' => $materialRef,
                        'package_num' => $packageNum
                    ];
                }
            }

            // Créer les relations en batch
            if (!empty($relationsToCreate) && !$dryRun) {
                $this->createRelationsBatch($relationsToCreate, $output);
            }

            $stats['relations_created'] = count($relationsToCreate);

            // Afficher les statistiques
            $output->writeln("\n<comment>Statistiques:</comment>");
            $output->writeln("  - Documents traités: " . count($legacyDocs));
            $output->writeln("  - Documents avec matériaux multiples: " . $stats['multiple_materials']);
            $output->writeln("  - Documents non trouvés: " . $stats['documents_not_found']);
            $output->writeln("  - Matériaux non trouvés: " . $stats['materials_not_found']);
            if ($createMissingMaterials) {
                $output->writeln("  - Matériaux créés: " . $stats['materials_created']);
            }
            $output->writeln("  - Relations existantes: " . $stats['relations_exist']);
            $output->writeln("  - Relations à créer: " . $stats['relations_created']);

            if ($dryRun) {
                $output->writeln("\n<comment>Mode simulation - aucune modification effectuée</comment>");
                $output->writeln("<comment>Relations qui seraient créées:</comment>");
                foreach (array_slice($relationsToCreate, 0, 10) as $relation) {
                    $output->writeln("  - Document {$relation['document_ref']} ↔ Matériau {$relation['material_ref']} (Package: {$relation['package_num']})");
                }
                if (count($relationsToCreate) > 10) {
                    $output->writeln("  ... et " . (count($relationsToCreate) - 10) . " autres");
                }
            } else {
                $output->writeln("\n<info>Relations créées avec succès !</info>");
            }

        } catch (\Exception $e) {
            $output->writeln("<error>Erreur: " . $e->getMessage() . "</error>");
            return Command::FAILURE;
        }

        return Command::SUCCESS;
    }

    private function createRelationsBatch(array $relations, OutputInterface $output): void
    {
        $batchSize = 500;
        $batches = array_chunk($relations, $batchSize);

        foreach ($batches as $batchIndex => $batch) {
            $values = [];
            $params = [];

            foreach ($batch as $relation) {
                $values[] = '(?, ?)';
                $params[] = $relation['document_id'];
                $params[] = $relation['material_id'];
            }

            $sql = 'INSERT IGNORE INTO document_materials (document_id, material_id) VALUES ' . implode(', ', $values);
            $this->em->getConnection()->executeStatement($sql, $params);

            $output->writeln("<comment>Batch " . ($batchIndex + 1) . "/" . count($batches) . " créé (" . count($batch) . " relations)</comment>");
        }
    }

    /**
     * Créer un matériau manquant avec statut inactif
     */
    private function createMissingMaterial(string $materialRef, OutputInterface $output): ?int
    {
        try {
            // Insérer le nouveau matériau avec statut inactif
            $insertSql = "INSERT INTO material (reference, description, status) VALUES (?, ?, ?)";
            $this->em->getConnection()->executeStatement($insertSql, [
                $materialRef,
                "Matériau créé automatiquement - " . $materialRef,
                'INACTIVE'  // status = 'INACTIVE' pour inactif
            ]);

            // Récupérer l'ID du matériau créé
            $materialId = $this->em->getConnection()->lastInsertId();

            return (int)$materialId;

        } catch (\Exception $e) {
            $output->writeln("<error>Erreur lors de la création du matériau {$materialRef}: " . $e->getMessage() . "</error>");
            return null;
        }
    }
}
