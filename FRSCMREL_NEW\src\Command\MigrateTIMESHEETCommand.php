<?php

namespace App\Command;

use Doctrine\DBAL\Connection;
use App\Entity\User;
use App\Entity\Project;
use App\Entity\Code;
use App\Entity\Impute;
use App\Entity\Phase;

use App\Repository\UserRepository;
use App\Repository\ProjectRepository;
use App\Repository\CodeRepository;
use App\Repository\PhaseRepository;
use App\Repository\ImputeRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\Bundle\DoctrineBundle\Attribute\Connection as ConnectionName;

#[AsCommand(
    name: 'app:migrate-timesheet',
    description: 'Migre les données timesheet de l\'ancienne BD vers le nouveau schéma'
)]
class MigrateTIMESHEETCommand extends Command
{
    protected static $defaultName = 'app:migrate-timesheet';

    // caches for performance
    private array $codes_array = [];
    private array $users_array = [];
    private array $phases_array = [];

    public function __construct(
        #[ConnectionName('legacy_timesheet')]
        private Connection $oldDb,
        private EntityManagerInterface $em,
        private ImputeRepository $imputes,
        private UserRepository $users,
        private CodeRepository $codes,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $this->cleanExistingData($output);

        $this->initializeAllCaches($output);

        $this->migrateImputes($output);

        $this->commentPhases($output);

        $output->writeln('<info>Migration IMPUTE terminée avec succès !</info>');
        return Command::SUCCESS;
    }

    private function cleanExistingData(OutputInterface $output): void
    {
        $output->writeln('<info>Nettoyage des tables IMPUTE existantes...</info>');

        $this->em->getConnection()->executeStatement('DELETE FROM impute');

        $output->writeln('<info>Tables nettoyées.</info>');
    }

    private function initializeAllCaches(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation de tous les caches...</info>');

        // 1. Cache des utilisateurs
        $this->initializeUserCache($output);

        // 2. Cache des codes
        $this->initializeCodeCache($output);

        // 3. Cache des phases (si nécessaire, sinon on peut l'ignorer)
        $this->initializePhaseCache($output);

        $output->writeln('<info>Tous les caches initialisés.</info>');
    }

    private function initializeUserCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des utilisateurs...</info>');

        $users = $this->users->findAll();
        foreach ($users as $user) {
            $prenom = substr($user->getPrenom(), 0, 1) . '.';
            $nom = str_replace(['é', 'è', 'ê', 'ë'], ['e', 'e', 'e', 'e'], $user->getNom());

            $username = strtoupper($nom) . ' ' . $prenom;
            $this->users_array[$username] = $user->getId();
        }

        $output->writeln('<info>Cache des utilisateurs initialisé. Nombre d\'utilisateurs : ' . count($this->users_array) . '</info>');
    }

    private function initializeCodeCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des codes...</info>');

        $codes = $this->codes->findAll();
        foreach ($codes as $code) {
            $this->codes_array[$code->getCode()] = $code->getId();
        }

        $output->writeln('<info>Cache des codes initialisé. Nombre de codes : ' . count($this->codes_array) . '</info>');
    }

    private function initializePhaseCache(OutputInterface $output): void
    {
        $output->writeln('<info>Initialisation du cache des phases...</info>');

        $phases = $this->em->getRepository(Phase::class)->findAll();
        foreach ($phases as $phase) {
            $this->phases_array[$phase->getCode()] = $phase->getId();
        }

        $output->writeln('<info>Cache des phases initialisé. Nombre de phases : ' . count($this->phases_array) . '</info>');
    }

    private function migrateImputes(OutputInterface $output): void
    {
        $output->writeln('<info>Démarrage de la migration des IMPUTES...</info>');

        $query = 'SELECT Username, Project_Code,Timestamp, Time FROM tbl_data where Project_Title!="-- IMPUTATIONS HORS PROJETS --" and Project_Code not LIKE "C%" and Project_Code!="";';
        $stmt = $this->oldDb->fetchAllAssociative($query);

        foreach ($stmt as $row) {
            $userId = $this->users_array[$row['Username']] ?? 1;
            $codeId = $this->codes_array[$row['Project_Code']] ?? null;
            if ($userId && $codeId) {
                $impute = new Impute();
                $impute->setUser($this->em->getReference(User::class, $userId));
                $impute->setCode($this->em->getReference(Code::class, $codeId));
                $impute->setNbHeures($row['Time']);
                $impute->setCreatedAt(new \DateTime($row['Timestamp']));

                $this->em->persist($impute);
            } else {
                if (!$userId) {
                    $output->writeln('<error>Utilisateur manquant pour : ' . $row['Username'] . '</error>');
                }
                if (!$codeId) {
                    $output->writeln('<error>Code manquant pour : ' . $row['Project_Code'] . '</error>');
                }
            }
        }

        $this->em->flush();

        $output->writeln('<info>Migration des IMPUTES terminée.</info>');
    }

    private function commentPhases(OutputInterface $output): void
    {
        $output->writeln('<info>Ajout des commentaires aux phases...</info>');

        $query = 'SELECT WBS code, Comment FROM tbl_phase_comments;';
        $stmt = $this->oldDb->fetchAllAssociative($query);
        

        foreach ($stmt as $row) {
            $phaseCode = $row['code'];
            $comment = $row['Comment'];

            if (isset($this->phases_array[$phaseCode])) {
                $phaseId = $this->phases_array[$phaseCode];
                $phase = $this->em->getReference(Phase::class, $phaseId);
                $phase->setCommentaire($comment);
                $this->em->persist($phase);
            } else {
                $output->writeln('<error>Phase manquante pour le code : ' . $phaseCode . '</error>');
            }
        }
        $this->em->flush();
    }
}