<?php

namespace App\Command;

use App\Repository\DocumentRepository;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-statistics',
    description: 'Test des méthodes statistiques avec les données réelles'
)]
class TestStatisticsCommand extends Command
{
    private DocumentRepository $documentRepository;

    public function __construct(DocumentRepository $documentRepository)
    {
        $this->documentRepository = $documentRepository;
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        
        $io->title('Test des Statistiques Documents - Phase 1');
        
        // Test 1: Temps moyen par étape du workflow
        $io->section('1. Temps moyen par étape du workflow');
        try {
            $workflowTime = $this->documentRepository->getAverageTimePerWorkflowStep();
            if (empty($workflowTime)) {
                $io->warning('Aucune donnée trouvée pour les temps de workflow');
            } else {
                $io->success(sprintf('✓ %d étapes analysées', count($workflowTime)));
                $table = [];
                foreach ($workflowTime as $step => $avgTime) {
                    $table[] = [$step, number_format($avgTime, 2) . ' jours'];
                }
                $io->table(['Étape', 'Temps moyen'], array_slice($table, 0, 10));
            }
        } catch (\Exception $e) {
            $io->error('✗ Erreur: ' . $e->getMessage());
        }

        // Test 2: Statistiques de temps de cycle
        $io->section('2. Statistiques de temps de cycle');
        try {
            $cycleTime = $this->documentRepository->getCycleTimeStatistics();
            $io->success('✓ Statistiques de cycle récupérées');
            $io->table(['Métrique', 'Valeur'], [
                ['Documents complétés', $cycleTime['completed_count']],
                ['Temps de cycle moyen', number_format($cycleTime['avg_cycle_time'], 2) . ' jours'],
                ['Temps de cycle minimum', $cycleTime['min_cycle_time'] . ' jours'],
                ['Temps de cycle maximum', $cycleTime['max_cycle_time'] . ' jours']
            ]);
        } catch (\Exception $e) {
            $io->error('✗ Erreur: ' . $e->getMessage());
        }

        // Test 3: Statistiques des visas
        $io->section('3. Statistiques des visas par étape');
        try {
            $visaStats = $this->documentRepository->getVisaStatisticsByStep();
            if (empty($visaStats)) {
                $io->warning('Aucune donnée trouvée pour les statistiques de visas');
            } else {
                $io->success(sprintf('✓ %d étapes avec visas analysées', count($visaStats)));
                $table = [];
                foreach (array_slice($visaStats, 0, 10) as $step => $stats) {
                    $table[] = [
                        $step,
                        $stats['total'],
                        $stats['approved'],
                        $stats['pending'],
                        number_format($stats['approval_rate'], 1) . '%'
                    ];
                }
                $io->table(['Étape', 'Total', 'Approuvés', 'En attente', 'Taux'], $table);
            }
        } catch (\Exception $e) {
            $io->error('✗ Erreur: ' . $e->getMessage());
        }

        // Test 4: Performance des validateurs
        $io->section('4. Performance des validateurs');
        try {
            $validatorPerf = $this->documentRepository->getValidatorPerformanceStatistics();
            if (empty($validatorPerf)) {
                $io->warning('Aucune donnée trouvée pour la performance des validateurs');
            } else {
                $io->success(sprintf('✓ %d validateurs analysés', count($validatorPerf)));
                $table = [];
                $count = 0;
                foreach ($validatorPerf as $userId => $stats) {
                    if ($count >= 10) break;
                    $table[] = [
                        $stats['name'],
                        $stats['department'] ?? 'N/A',
                        $stats['total_visas'],
                        number_format($stats['approval_rate'], 1) . '%',
                        $stats['avg_response_time_hours'] ? number_format($stats['avg_response_time_hours'], 1) . 'h' : 'N/A'
                    ];
                    $count++;
                }
                $io->table(['Nom', 'Département', 'Total Visas', 'Taux', 'Temps Réponse'], $table);
            }
        } catch (\Exception $e) {
            $io->error('✗ Erreur: ' . $e->getMessage());
        }

        // Test 5: Distribution des types de documents
        $io->section('5. Distribution des types de documents');
        try {
            $docTypes = $this->documentRepository->getDetailedDocumentTypeDistribution();
            if (empty($docTypes)) {
                $io->warning('Aucune donnée trouvée pour la distribution des types');
            } else {
                $io->success(sprintf('✓ %d types de documents analysés', count($docTypes)));
                $totals = [];
                foreach ($docTypes as $docType => $procTypes) {
                    $total = 0;
                    foreach ($procTypes as $procType => $matTypes) {
                        foreach ($matTypes as $count) {
                            $total += $count;
                        }
                    }
                    $totals[$docType] = $total;
                }
                arsort($totals);
                $table = [];
                foreach (array_slice($totals, 0, 10, true) as $type => $count) {
                    $table[] = [$type, $count];
                }
                $io->table(['Type de document', 'Nombre'], $table);
            }
        } catch (\Exception $e) {
            $io->error('✗ Erreur: ' . $e->getMessage());
        }

        // Test 6: Distribution par projet/DMO
        $io->section('6. Distribution par projet/DMO');
        try {
            $projectDmo = $this->documentRepository->getDocumentDistributionByProjectAndDMO();
            $io->success('✓ Distribution projet/DMO récupérée');
            $io->table(['Catégorie', 'Nombre'], [
                ['Projets', count($projectDmo['projects'])],
                ['DMOs', count($projectDmo['dmos'])],
                ['Documents autonomes', $projectDmo['standalone']]
            ]);
            
            if (!empty($projectDmo['projects'])) {
                $io->text('Top 5 projets:');
                $table = [];
                foreach (array_slice($projectDmo['projects'], 0, 5) as $project) {
                    $table[] = [$project['name'], $project['document_count']];
                }
                $io->table(['Projet', 'Documents'], $table);
            }
        } catch (\Exception $e) {
            $io->error('✗ Erreur: ' . $e->getMessage());
        }

        // Test 7: Tendances de création
        $io->section('7. Tendances de création (6 derniers mois)');
        try {
            $trends = $this->documentRepository->getDocumentCreationTrends(6);
            if (empty($trends)) {
                $io->warning('Aucune donnée trouvée pour les tendances de création');
            } else {
                $io->success(sprintf('✓ %d mois analysés', count($trends)));
                $table = [];
                foreach ($trends as $trend) {
                    $table[] = [$trend['month'], $trend['count'], $trend['cumulative']];
                }
                $io->table(['Mois', 'Créés', 'Cumulatif'], $table);
            }
        } catch (\Exception $e) {
            $io->error('✗ Erreur: ' . $e->getMessage());
        }

        $io->success('Tests terminés ! Vous pouvez maintenant accéder à l\'interface web sur /statistics');
        
        return Command::SUCCESS;
    }
}
