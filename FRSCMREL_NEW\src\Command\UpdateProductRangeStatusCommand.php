<?php

namespace App\Command;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:update-product-range-status',
    description: 'Met à jour le statut des ProductRange selon la liste fournie'
)]
class UpdateProductRangeStatusCommand extends Command
{
    public function __construct(
        private EntityManagerInterface $em
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        // Liste des ProductRange qui doivent avoir etat = 1 (actifs)
        $activeProductRanges = [
            ['9316', 'Energy'],
            ['OFS', 'Energy'],
            ['EDBC', 'Energy'],
            ['6kV1600A', 'Energy'],
            ['EFS', 'Energy'],
            ['DLS', 'Energy'],
            ['WG', 'Energy'],
            ['376', 'Energy'],
            ['Showet', 'Energy'],
            ['MOD', 'Energy'],
            ['MSD', 'Energy'],
            ['MPD', 'Energy'],
            ['6kV400A', 'Energy'],
            ['18kV400A', 'Energy'],
            ['18kV900A', 'Energy'],
            ['SEA-VI 6-100', 'Energy'],
            ['ODBC', 'Energy'],
            ['ILC', 'Energy'],
            ['AMB', 'Energy'],
            ['365 - Screw', 'Energy'],
            ['Specification', 'Energy'],
            ['363 - Seal', 'Energy'],
            ['375 - Diabolo', 'Energy'],
            ['MILITARY', 'Energy'],
            ['Comp. Standard', 'Energy'],
            ['6kV500A', 'Energy'],
            ['HDCAM', 'Aerospace'],
            ['CAM', 'Industry'],
            ['CMR', 'Industry'],
            ['CPM', 'Industry'],
            ['CER', 'Industry'],
            ['CEV', 'Industry'],
            ['CUP', 'Industry'],
            ['FXP', 'Industry'],
            ['CSR', 'Industry'],
            ['DTX', 'Industry'],
            ['EKO', 'Industry'],
            ['WYE', 'Industry'],
            ['Other Series (CEV1695/UTO/CHP/CSI/CUP/DUP/PE..)', 'Industry'],
            ['ONX', 'Industry'],
            ['Other Series (DMO/CSF)', 'Industry'],
            ['CPP', 'Industry'],
            ['UIC552', 'Industry'],
            ['UIC541', 'Industry'],
            ['AGR', 'Industry'],
            ['CCA', 'Industry'],
            ['CLN', 'Industry'],
            ['NGC', 'Industry'],
            ['CPE', 'Industry'],
            ['API/BEC/CVF/CSR1696/DTR/MCM', 'Industry'],
            ['SIG', 'Industry'],
            ['CSF', 'Industry'],
            ['CIR', 'Industry'],
            ['CMB', 'Industry'],
            ['CMC', 'Industry'],
            ['UIC558', 'Industry'],
            ['SRC', 'Industry'],
            ['CCI/CMI/CDI', 'Industry'],
            ['MU27P', 'Industry'],
            ['Tools OUT', 'Industry'],
            ['Accessories STD', 'Industry'],
            ['Misc (other component & semi finished)', 'Industry'],
            ['Misc (STD)', 'Industry'],
            ['UIC552 Solutions', 'Industry'],
            ['UIC558 Solutions', 'Industry'],
            ['UIC541 Solutions', 'Industry'],
            ['Other Solutions (CMC, CUP….)', 'Industry'],
            ['CAM Solutions', 'Industry'],
            ['LOPRO', 'Industry'],
            ['SEA-VI 66-1250', 'Energy'],
            ['364 - Spring', 'Energy'],
            ['AHVC', 'Aerospace'],
            ['BATT', 'Aerospace'],
            ['SPLICE', 'Energy'],
            ['STAR-END', 'Energy'],
            ['Specification', 'Industry'],
        ];

        $io->title('Mise à jour du statut des ProductRange');

        // 1. Mettre tous les ProductRange à etat = 0 (inactifs)
        $io->section('Étape 1: Mise à jour de tous les ProductRange à etat = 0');
        $updateAllSql = 'UPDATE product_range SET etat = 0';
        $this->em->getConnection()->executeStatement($updateAllSql);
        $io->success('Tous les ProductRange mis à etat = 0');

        // 2. Mettre les ProductRange de la liste à etat = 1 (actifs)
        $io->section('Étape 2: Activation des ProductRange de la liste');
        
        $updated = 0;
        $notFound = [];

        foreach ($activeProductRanges as [$productRange, $division]) {
            $sql = 'UPDATE product_range SET etat = 1 WHERE product_range = ? AND division = ?';
            $result = $this->em->getConnection()->executeStatement($sql, [$productRange, $division]);
            
            if ($result > 0) {
                $updated++;
                $io->writeln("✓ {$productRange} | {$division}");
            } else {
                $notFound[] = "{$productRange} | {$division}";
                $io->writeln("<comment>⚠ Non trouvé: {$productRange} | {$division}</comment>");
            }
        }

        $io->success("{$updated} ProductRange activés (etat = 1)");
        
        if (!empty($notFound)) {
            $io->warning(count($notFound) . ' ProductRange non trouvés:');
            foreach ($notFound as $item) {
                $io->writeln("  - {$item}");
            }
        }

        // 3. Statistiques finales
        $io->section('Statistiques finales');
        
        $totalActive = $this->em->getConnection()->fetchOne('SELECT COUNT(*) FROM product_range WHERE etat = 1');
        $totalInactive = $this->em->getConnection()->fetchOne('SELECT COUNT(*) FROM product_range WHERE etat = 0');
        $total = $this->em->getConnection()->fetchOne('SELECT COUNT(*) FROM product_range');

        $io->table(
            ['Statut', 'Nombre'],
            [
                ['Actifs (etat = 1)', $totalActive],
                ['Inactifs (etat = 0)', $totalInactive],
                ['Total', $total]
            ]
        );

        return Command::SUCCESS;
    }
}
