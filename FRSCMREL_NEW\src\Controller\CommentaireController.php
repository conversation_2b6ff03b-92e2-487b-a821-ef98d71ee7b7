<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Commentaire;
use App\Entity\Document;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use GuzzleHttp\Client;
use Symfony\Component\HttpFoundation\JsonResponse;


class CommentaireController extends AbstractController
{
    #[Route('/commentaire', name: 'app_commentaire')]
    public function index(): Response
    {
        return $this->render('commentaire/index.html.twig', [
            'controller_name' => 'CommentaireController',
        ]);
    }
// create a new comment from ajax request
#[Route('/commentaire/new', name: 'app_commentaire_new', methods: ['POST'])]
public function new(Request $request, EntityManagerInterface $em): Response
{
    // get the data from the request
    $data = json_decode($request->getContent(), true);

    // get current user
    $user = $this->getUser();

    // get document:
    $document = $em->getRepository(Document::class)->find($data['documentId']);
    // create a new comment
    $comment = new Commentaire();
    $comment->setUser($user);
    $comment->setCommentaire($data['content']);
    $comment->settype($data['type']);
    $comment->setDocuments($document);
    $comment->setState($data['state']);
    $comment->setCreatedAt(new \DateTimeImmutable());

    // save the comment (utiliser l'EntityManager injecté)
    $em->persist($comment);
    $em->flush();

    // return the response
    return new Response('Comment created!', 201);
}

// get all comments for one document from ajax request

    #[Route('/commentaire/get/{documentId}', name: 'app_commentaire_get', methods: ['GET'])]
    public function getComments($documentId, EntityManagerInterface $em): Response
    {
        // get the document
        $document = $em->getRepository(Document::class)->find($documentId);

        if (!$document) {
            return $this->json(['error' => 'Document non trouvé'], 404);
        }

        // get all comments for this document
        $comments = $document->getCommentaires();

        // Transformer les commentaires en tableau simple pour éviter les problèmes de sérialisation
        $commentsData = [];
        foreach ($comments as $comment) {
            $commentsData[] = [
                'id' => $comment->getId(),
                'state' => $comment->getState(),
                'commentaire' => $comment->getCommentaire(),
                'type' => $comment->getType(),
                'created_at' => $comment->getCreatedAt() ? $comment->getCreatedAt()->format('Y-m-d H:i:s') : null,
                'user' => $comment->getUser() ? [
                    'id' => $comment->getUser()->getId(),
                    'prenom' => $comment->getUser()->getPrenom(),
                    'nom' => $comment->getUser()->getNom()
                ] : null
            ];
        }

        return $this->json($commentsData, 200);
    }




}
