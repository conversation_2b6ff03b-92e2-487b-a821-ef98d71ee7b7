<?php

// src/Controller/ConfigController.php
namespace App\Controller;

use App\Entity\Config;
use App\Repository\ConfigRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\Routing\Annotation\Route;

class ConfigController extends AbstractController
{
    #[Route('/config', name: 'app_config')]
    public function getConfig(ConfigRepository $configRepository): Response
    {
        $config = $configRepository->findOneBy([]);
        if (!$config) {
            return $this->json(['error' => 'Config not found'], Response::HTTP_NOT_FOUND);
        }
    
        // Formatage de la période pour affichage (ex: "Mars 2025")
        setlocale(LC_TIME, 'fr_FR.UTF-8', 'fr_FR', 'fr', 'french');
        $periodeFormatted = strftime('%B %Y', $config->getPeriode()->getTimestamp());
        $periodeFormatted = ucfirst($periodeFormatted);
    
        // Calculer le premier et le dernier jour ouvrable du mois
        $periodeDate = $config->getPeriode();
        $year = $periodeDate->format('Y');
        $month = $periodeDate->format('m');
    
        $firstDay = new \DateTime("$year-$month-01");
        $firstWorkingDay = clone $firstDay;
        while ($firstWorkingDay->format('N') >= 6) {
            $firstWorkingDay->modify('+1 day');
        }
    
        $lastDay = new \DateTime("$year-$month-" . cal_days_in_month(CAL_GREGORIAN, $month, $year));
        $lastWorkingDay = clone $lastDay;
        while ($lastWorkingDay->format('N') >= 6) {
            $lastWorkingDay->modify('-1 day');
        }
    
        // Les dates en DB pour ouverture et clôture
        $dateOuverture = $config->getDateDeb();
        $dateCloture = $config->getDateFin();
    
        // Calcul du nombre de jours ouvrables entre le premier et le dernier jour ouvrable
        $workingDays = $this->countWorkingDays($firstWorkingDay, $lastWorkingDay);
    
        return $this->json([
             'periode' => $periodeFormatted,
             'debut' => $firstWorkingDay->format('Y-m-d'),
             'fin' => $lastWorkingDay->format('Y-m-d'),
             'jours_ouvrables' => $workingDays,
             'annee_fiscale' => $year,
             'date_ouverture' => $dateOuverture->format('Y-m-d'),
             'date_cloture' => $dateCloture->format('Y-m-d')
        ]);
    }
    
    #[Route('/config/edit', name: 'app_config_edit')]
    public function editConfig(Request $request, ConfigRepository $configRepository, EntityManagerInterface $em): Response
    {
        $config = $configRepository->findOneBy([]);
        if (!$config) {
            return new JsonResponse(['error' => 'Config not found'], Response::HTTP_NOT_FOUND);
        }
        if (!($this->getUser()->getTitre() === "Chef de Projets" || $this->getUser()->isManager())) {
            return new JsonResponse(['error' => 'Vous n\'avez pas les droits pour modifier la configuration'], Response::HTTP_FORBIDDEN);
        }
        if ($request->isMethod('POST')) {
            // Récupération des valeurs envoyées
            $periode = $request->request->get('periode');
            $dateDeb = $request->request->get('dateDeb');
            $dateFin = $request->request->get('dateFin');
            
            try {
                $config->setPeriode(new \DateTime($periode));
                $config->setDateDeb(new \DateTime($dateDeb));
                $config->setDateFin(new \DateTime($dateFin));
            } catch (\Exception $e) {
                return new JsonResponse(['error' => 'Format de date invalide'], Response::HTTP_BAD_REQUEST);
            }
    
            $em->flush();
            return new JsonResponse(['success' => true]);
        }
    
        // Pour les requêtes GET, on pourrait retourner un message ou une erreur
        return new JsonResponse(['error' => 'Méthode non autorisée'], Response::HTTP_METHOD_NOT_ALLOWED);
    }
    
    private function countWorkingDays(\DateTimeInterface $start, \DateTimeInterface $end): int
    {
        $workDays = 0;
        $current = clone $start;
        while ($current <= $end) {
            if ($current->format('N') < 6) {
                $workDays++;
            }
            $current->modify('+1 day');
        }
        return $workDays;
    }
}
