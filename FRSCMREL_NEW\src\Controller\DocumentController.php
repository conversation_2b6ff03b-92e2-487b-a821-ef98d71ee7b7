<?php
namespace App\Controller;

use App\Entity\Document;
use App\Entity\ReleasedPackage;
use App\Entity\Visa;
use App\Entity\Commentaire;
use App\Entity\Material;
use App\Entity\ProductCode;
use App\Form\DocumentType;
use App\Entity\User;
use App\Repository\DocumentRepository;
use App\Repository\ProductCodeRepository;
use App\Repository\CommodityCodeRepository;
use App\Service\WorkflowSimulator;
use App\Service\WorkflowTeleportation;
use App\Service\DocumentDuplicator;
use App\Service\DoctypeResetService;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Workflow\Registry;
use Symfony\Component\HttpFoundation\JsonResponse;
use Knp\Component\Pager\PaginatorInterface;

#[Route('/document')]
final class DocumentController extends AbstractController
{

    private WorkflowSimulator $workflowSimulator;
    private WorkflowTeleportation $workflowTeleportation;
    private DocumentDuplicator $documentDuplicator;
    private DoctypeResetService $doctypeResetService;

    public function __construct(
        private Registry $registry,
        DocumentDuplicator $documentDuplicator,
        WorkflowSimulator $workflowSimulator,
        WorkflowTeleportation $workflowTeleportation,
        DoctypeResetService $doctypeResetService
    ) {
        $this->workflowSimulator = $workflowSimulator;
        $this->workflowTeleportation = $workflowTeleportation;
        $this->documentDuplicator = $documentDuplicator;
        $this->doctypeResetService = $doctypeResetService;
    }

    #[Route(name: 'app_document_index', methods: ['GET'])]
    public function index(DocumentRepository $documentRepository, EntityManagerInterface $entityManager): Response
    {
        $document = $documentRepository->find(1);
        $workflow = $this->registry->get($document,'document_workflow');
        $marking = [];
        $marking_start = $workflow->getMarking($document)->getPlaces();
        foreach ($workflow->getEnabledTransitions($document) as $transition) {
            $marking[$transition->getTos()[0]] = 1;
            $from = $transition->getFroms()[0];
            if (isset($marking_start[$from])) {
                unset($marking_start[$from]);
            }
        }
        $marking = array_merge($marking, $marking_start);
        $document->setCurrentSteps($marking);
        $entityManager->flush();
        return $this->render('document/index.html.twig', [
            'documents' => $documentRepository->findAll(),
        ]);
    }

    #[Route('/suivie-ref', name: 'suivie_ref', methods: ['GET'])]
    public function suivie_ref(
        Request $request,
        DocumentRepository $documentRepository,
        EntityManagerInterface $entityManager,
        PaginatorInterface $paginator,
        WorkflowSimulator $workflowSimulator
    ): Response {
        $queryBuilder = $documentRepository->createQueryBuilder('d')
            ->leftJoin('d.relPack', 'p')
            ->leftJoin('d.materials', 'm');

        // Apply filters - clean empty values
        $filters = [
            'search' => trim($request->query->get('search', '')),
            'reference' => trim($request->query->get('reference', '')),
            'pack' => trim($request->query->get('pack', '')),
            'docType' => trim($request->query->get('docType', '')),
            'procType' => trim($request->query->get('procType', '')),
            'activity' => trim($request->query->get('activity', '')),
            'material' => trim($request->query->get('material', '')),
            'productCode' => trim($request->query->get('productCode', '')),
            'currentStep' => trim($request->query->get('currentStep', '')),
        ];

        // Keep original filters for template (including empty ones)
        $templateFilters = $filters;

        // Remove empty filters for query building
        $filters = array_filter($filters, function($value) {
            return $value !== '' && $value !== null;
        });

        // Global search across multiple fields
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('d.reference', ':search'),
                    $queryBuilder->expr()->like('d.refTitleFra', ':search'),
                    $queryBuilder->expr()->like('d.prodDraw', ':search'),
                    $queryBuilder->expr()->like('d.prodDrawRev', ':search'),
                    $queryBuilder->expr()->like('d.refRev', ':search'),
                    $queryBuilder->expr()->like('m.reference', ':search'),
                    $queryBuilder->expr()->like('d.productCode', ':search'),
                    $queryBuilder->expr()->like('d.docType', ':search'),
                    $queryBuilder->expr()->like('d.procType', ':search'),
                    $queryBuilder->expr()->like('p.Activity', ':search')
                )
            )->setParameter('search', $searchTerm);
        }

        // Specific field filters
        if (!empty($filters['reference'])) {
            $queryBuilder->andWhere('d.reference LIKE :reference')
                ->setParameter('reference', '%' . $filters['reference'] . '%');
        }

        if (!empty($filters['pack'])) {
            // Filtrer par ID de pack (exact ou partiel)
            $queryBuilder->andWhere('p.id LIKE :pack')
                ->setParameter('pack', '%' . $filters['pack'] . '%');
        }

        if (!empty($filters['docType'])) {
            $queryBuilder->andWhere('d.docType = :docType')
                ->setParameter('docType', $filters['docType']);
        }

        if (!empty($filters['procType'])) {
            $queryBuilder->andWhere('d.procType = :procType')
                ->setParameter('procType', $filters['procType']);
        }

        if (!empty($filters['activity'])) {
            $queryBuilder->andWhere('p.Activity LIKE :activity')
                ->setParameter('activity', '%' . $filters['activity'] . '%');
        }

        if (!empty($filters['material'])) {
            $queryBuilder->andWhere('m.reference LIKE :material')
                ->setParameter('material', '%' . $filters['material'] . '%');
        }

        if (!empty($filters['productCode'])) {
            $queryBuilder->andWhere('d.productCode LIKE :productCode')
                ->setParameter('productCode', '%' . $filters['productCode'] . '%');
        }
 
        if (!empty($filters['currentStep'])) {
            $queryBuilder->andWhere('d.currentSteps LIKE :currentStep')
                ->setParameter('currentStep', '%' . $filters['currentStep'] . '%');
        }

        // Order by most recent first
        $queryBuilder->orderBy('d.id', 'DESC');

        $pagination = $paginator->paginate(
            $queryBuilder,
            $request->query->getInt('page', 1),
            20, // Increased page size for better user experience
            [
                'wrap-queries' => true, // Enable query wrapping for better performance
                'defaultSortFieldName' => 'd.id',
                'defaultSortDirection' => 'desc'
            ]
        );

        // Pass current query parameters to pagination template
        $pagination->setCustomParameters([
            'route' => 'suivie_ref',
            'query' => $request->query->all(),
            'pageParameterName' => 'page'
        ]);

        $documents = $pagination->getItems();
        $simulated = $workflowSimulator->simulateWorkflowList($documents);

        // Suppression du refresh inutile qui ralentit les performances
        // foreach ($documents as $document) {
        //     $entityManager->refresh($document);
        // }

        // Get filter options for dropdowns
        $filterOptions = $this->getFilterOptions($documentRepository);

        // Count active filters
        $activeFilters = count(array_filter($templateFilters, function($value) {
            return $value !== '' && $value !== null;
        }));

        return $this->render('document/suivie_list.html.twig', [
            'results' => $simulated,
            'pagination' => $pagination,
            'filters' => $templateFilters,
            'filterOptions' => $filterOptions,
            'activeFilters' => $activeFilters,
        ]);
    }

    /**
     * Get filter options for dropdowns
     */
    private function getFilterOptions(DocumentRepository $documentRepository): array
    {
        $queryBuilder = $documentRepository->createQueryBuilder('d')
            ->leftJoin('d.relPack', 'p');

        // Get distinct document types
        $docTypes = $documentRepository->createQueryBuilder('d')
            ->select('DISTINCT d.docType')
            ->where('d.docType IS NOT NULL')
            ->orderBy('d.docType', 'ASC')
            ->getQuery()
            ->getScalarResult();

        // Get distinct process types
        $procTypes = $documentRepository->createQueryBuilder('d')
            ->select('DISTINCT d.procType')
            ->where('d.procType IS NOT NULL')
            ->orderBy('d.procType', 'ASC')
            ->getQuery()
            ->getScalarResult();

        // Get distinct activities
        $activities = $documentRepository->createQueryBuilder('d')
            ->select('DISTINCT p.Activity')
            ->leftJoin('d.relPack', 'p')
            ->where('p.Activity IS NOT NULL')
            ->orderBy('p.Activity', 'ASC')
            ->getQuery()
            ->getScalarResult();

        // Get distinct materials
        $materials = $documentRepository->createQueryBuilder('d')
            ->select('DISTINCT m.reference')
            ->leftJoin('d.materials', 'm')
            ->where('m.reference IS NOT NULL')
            ->orderBy('m.reference', 'ASC')
            ->setMaxResults(50) // Limit for performance
            ->getQuery()
            ->getScalarResult();

        // Get all workflow steps from workflow.yaml
        $commonSteps = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        return [
            'docTypes' => array_column($docTypes, 'docType'),
            'procTypes' => array_column($procTypes, 'procType'),
            'activities' => array_column($activities, 'Activity'),
            'materials' => array_column($materials, 'reference'),
            'currentSteps' => $commonSteps,
        ];
    }

    #[Route('/suivie-ref/export', name: 'suivie_ref_export', methods: ['GET'])]
    public function suivieRefExport(
        Request $request,
        DocumentRepository $documentRepository,
        EntityManagerInterface $entityManager,
        WorkflowSimulator $workflowSimulator
    ): Response {
        $queryBuilder = $documentRepository->createQueryBuilder('d')
            ->leftJoin('d.relPack', 'p')
            ->leftJoin('d.materials', 'm');

        // Apply the same filters as the main page
        $filters = [
            'search' => $request->query->get('search'),
            'reference' => $request->query->get('reference'),
            'pack' => $request->query->get('pack'),
            'docType' => $request->query->get('docType'),
            'procType' => $request->query->get('procType'),
            'activity' => $request->query->get('activity'),
            'material' => $request->query->get('material'),
            'productCode' => $request->query->get('productCode'),
            'currentStep' => $request->query->get('currentStep'),
        ];

        // Apply filters (same logic as main method)
        if (!empty($filters['search'])) {
            $searchTerm = '%' . $filters['search'] . '%';
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('d.reference', ':search'),
                    $queryBuilder->expr()->like('d.refTitleFra', ':search'),
                    $queryBuilder->expr()->like('d.prodDraw', ':search'),
                    $queryBuilder->expr()->like('d.prodDrawRev', ':search'),
                    $queryBuilder->expr()->like('d.refRev', ':search'),
                    $queryBuilder->expr()->like('m.reference', ':search'),
                    $queryBuilder->expr()->like('d.productCode', ':search'),
                    $queryBuilder->expr()->like('d.docType', ':search'),
                    $queryBuilder->expr()->like('d.procType', ':search'),
                    $queryBuilder->expr()->like('p.Activity', ':search')
                )
            )->setParameter('search', $searchTerm);
        }

        if (!empty($filters['reference'])) {
            $queryBuilder->andWhere('d.reference LIKE :reference')
                ->setParameter('reference', '%' . $filters['reference'] . '%');
        }

        if (!empty($filters['pack'])) {
            $queryBuilder->andWhere('p.id LIKE :pack')
                ->setParameter('pack', '%' . $filters['pack'] . '%');
        }

        if (!empty($filters['docType'])) {
            $queryBuilder->andWhere('d.docType = :docType')
                ->setParameter('docType', $filters['docType']);
        }

        if (!empty($filters['procType'])) {
            $queryBuilder->andWhere('d.procType = :procType')
                ->setParameter('procType', $filters['procType']);
        }

        if (!empty($filters['activity'])) {
            $queryBuilder->andWhere('p.Activity LIKE :activity')
                ->setParameter('activity', '%' . $filters['activity'] . '%');
        }

        if (!empty($filters['material'])) {
            $queryBuilder->andWhere('m.reference LIKE :material')
                ->setParameter('material', '%' . $filters['material'] . '%');
        }

        if (!empty($filters['productCode'])) {
            $queryBuilder->andWhere('d.productCode LIKE :productCode')
                ->setParameter('productCode', '%' . $filters['productCode'] . '%');
        }

        if (!empty($filters['currentStep'])) {
            $queryBuilder->andWhere(
                $queryBuilder->expr()->orX(
                    $queryBuilder->expr()->like('d.currentSteps', ':currentStep1'),
                    $queryBuilder->expr()->like('d.currentSteps', ':currentStep2')
                )
            )
            ->setParameter('currentStep1', '%"' . $filters['currentStep'] . '":1%')
            ->setParameter('currentStep2', '%"' . $filters['currentStep'] . '":true%');
        }

        $queryBuilder->orderBy('d.id', 'DESC');
        $documents = $queryBuilder->getQuery()->getResult();

        // Generate CSV
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv');
        $response->headers->set('Content-Disposition', 'attachment; filename="suivi_documents_' . date('Y-m-d_H-i-s') . '.csv"');

        $csvData = "Pack;Activity;Reference;RefRev;ProdDraw;ProdDrawRev;DocType;ProcType;Material;ProductCode;CurrentSteps\n";

        foreach ($documents as $document) {
            $currentSteps = implode('|', array_keys($document->getCurrentSteps()));
            $csvData .= sprintf(
                "%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
                $document->getRelPack() ? $document->getRelPack()->getId() : '',
                $document->getRelPack() ? $document->getRelPack()->getActivity() : '',
                $document->getReference() ?? '',
                $document->getRefRev() ?? '',
                $document->getProdDraw() ?? '',
                $document->getProdDrawRev() ?? '',
                $document->getDocType() ?? '',
                $document->getProcType() ?? '',
                $document->getMaterial() ?? '',
                $document->getProductCode() ?? '',
                $currentSteps
            );
        }

        $response->setContent($csvData);
        return $response;
    }

    #[Route('/backward/{id}', name: 'backward', methods: ['GET'])]
    public function backward(Document $document, EntityManagerInterface $entityManager): JsonResponse
    {
        $TP_List = $this->workflowTeleportation->getDocumentTeleportation($document);
        return new JsonResponse($TP_List);
    }

    #[Route('/retour', name: 'retour', methods: ['GET'])]
    public function retour(EntityManagerInterface $entityManager, DocumentRepository $Repository): Response
    {
        // Use optimized query with limit and ordering for better performance
        $packages = $entityManager->getRepository(ReleasedPackage::class)
            ->createQueryBuilder('p')
            ->orderBy('p.id', 'DESC')
            ->setMaxResults(1000) // Limit for performance
            ->getQuery()
            ->getResult();

        return $this->render('document/retour.html.twig', [
            'packages' => $packages,
        ]);
    }


    #[Route('/review/{place}', name: 'app_document_place', methods: ['GET'])]
    public function place(DocumentRepository $documentRepository, string $place, EntityManagerInterface $entityManager): Response
    {
        // Cache des documents par place pour éviter les requêtes répétées
        static $documentsCache = [];
        static $cacheTime = [];

        $now = time();
        $cacheKey = $place;

        if ($place === 'BE_0' || $place === 'BE_1' || $place === 'BE') {
            return $this->redirectToRoute('app_package');
        }

        // Vérifier si on a un cache valide (1 minute)
        if (!isset($documentsCache[$cacheKey]) || !isset($cacheTime[$cacheKey]) || ($now - $cacheTime[$cacheKey]) > 60) {
            if ($place === 'Qual_Logistique' || $place === 'Logistique') {
                // Utiliser la méthode optimisée pour les documents logistiques
                $documents = $documentRepository->findActiveDocumentsInLogisticsSteps();
                // On considère 'Qual_Logistique' comme nom d'affichage (ou de template)
                $place = 'Qual_Logistique';
            } else {
                // Utiliser la méthode optimisée pour les autres étapes
                $documents = $documentRepository->findActiveDocumentsInStep($place);
            }

            $documentsCache[$cacheKey] = $documents;
            $cacheTime[$cacheKey] = $now;
        } else {
            $documents = $documentsCache[$cacheKey];
        }

        // Tri en fonction du nombre de jours passés dans l'état $place
        usort($documents, function($a, $b) use ($place) {
            return $b->getDaysInState($place) <=> $a->getDaysInState($place);
        });

        return $this->render('document/places/document_' . $place . '.html.twig', [
            'documents' => $documents,
            'place'     => $place,
            'total_documents' => count($documents),
        ]);
    }

    #[Route('/document/{id}/visas', name: 'document_visas', methods: ['GET'])]
    public function getVisas(Document $document): Response
    {
        // On récupère tous les visas du document
        $visas = $document->getVisas();

        // On convertit en tableau
        $data = [];
        foreach ($visas as $visa) {
            $data[] = [
                'id'       => $visa->getId(),
                'name'     => $visa->getName(),
                'status'   => $visa->getStatus(),
                'dateVisa' => $visa->getDateVisa()?->format('d/m/Y H:i'),
                'signer'   => (string)$visa->getValidator(),
            ];
        }
        return $this->json($data);
    }


    #[Route('/update-document', name: 'update_document', methods: ['POST'])]
    public function updateDocument(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $places_docType = [
            'MACH' => ['ORIGINE'=> 'Machining', 'DESTINATION'=> 'Machining'],
            'MOLD' => ['ORIGINE'=> 'Molding', 'DESTINATION'=> 'Molding'],
            'ASSY' => ['ORIGINE'=> 'Assembly', 'DESTINATION'=> ['Assembly', 'Quality']],
            'PUR'  => ['ORIGINE'=> ['Achat_F30','Achat_Rfq','Quality'], 'DESTINATION'=> 'Quality'],
            'DOC'  => ['ORIGINE'=> 'Quality', 'DESTINATION'=> 'Quality'],
        ];

        // Récupérer les données de la requête AJAX
        $data = json_decode($request->getContent(), true);

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'], $data['field'], $data['value'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['id'];
        $field      = $data['field'];
        $value      = $data['value'];

        // Rechercher le document correspondant
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // On récupère l'ancien docType pour la gestion du workflow après mise à jour
        $oldDocType = $document->getDoctype();

        // Exemple : si on veut gérer spécifiquement un champ "superviseur"
        if ($field === 'superviseur') {
            // dd($documentId,$value);
            $user = $entityManager->getRepository(User::class)->find($value);
            if (!$user) {
                return new JsonResponse(['status' => 'error', 'message' => 'Utilisateur non trouvé'], 404);
            }
            $document->setSuperviseur($user);
            $entityManager->persist($document);
            $entityManager->flush();
        }
        // Gestion spécifique du champ productCode (relation vers ProductCode)
        elseif ($field === 'productCode') {
            $productCodeRepository = $entityManager->getRepository(ProductCode::class);

            if (empty($value)) {
                // Si la valeur est vide, on supprime la relation
                $document->setProductCode(null);
            } else {
                // Chercher ou créer le ProductCode
                $productCode = $productCodeRepository->findOrCreateByCode($value, false);
                $document->setProductCode($productCode);
            }

            $entityManager->persist($document);
            $entityManager->flush();

        } else if($field === 'qInspection') {
            if (!is_array($value)) {
                return new JsonResponse(['status' => 'error', 'message' => 'La valeur pour qInspection doit être une liste.'], 400);
            }
            $document->setQInspection($value);
        } else if($field === 'qDocRec'){
            if (!is_array($value)) {
                return new JsonResponse(['status' => 'error', 'message' => 'La valeur pour qDocRec doit être une liste.'], 400);
            }
            $document->setQDocRec($value);
        } else if ($field === 'matProdType') {
            // Mapper les anciens libellés vers les codes SAP si nécessaire
            $sapCode = $this->mapMaterialTypeToSAP($value);
            $document->setMatProdType($sapCode);
            $entityManager->persist($document);
            $entityManager->flush();
        } else if ($field === 'qualOwner') {
            $user = $entityManager->getRepository(User::class)->find($value);
            if (!$user) {
                return new JsonResponse(['status' => 'error', 'message' => 'Utilisateur non trouvé'], 404);
            }
            $document->setQualOwner($user);
            $entityManager->persist($document);
            $entityManager->flush();
        }

        else {
            // Sinon on continue avec la méthode "set + ucfirst($field)" générique
            $setterMethod = 'set'.ucfirst($field);
            if (method_exists($document, $setterMethod)) {
                $document->$setterMethod($value);
            } else {
                return new JsonResponse(['status' => 'error', 'message' => 'Méthode introuvable pour le champ '.$field], 500);
            }
        }

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('edit', $this->getUser(), 'Modification du champ ' . $field);

        // On persiste et flush
        $entityManager->persist($document);
        $entityManager->flush();

        // Logique de workflow et réinitialisation si on modifie le docType
        if ($field === 'doctype') {
            // Appeler le service de réinitialisation de doctype pour tous les changements
            $resetResult = $this->doctypeResetService->resetDocumentForDoctypeChange($document, $oldDocType, $value, $this->getUser());

            // Nettoyage spécifique des données selon les transitions
            $this->cleanDataForDoctypeTransition($document, $oldDocType, $value, $entityManager);

            // Logique de workflow seulement si le nouveau doctype n'est pas DOC
            if ($value !== "DOC") {
                $newDocType = $value;
                $ORIGINE    = $places_docType[$oldDocType]['ORIGINE'];
                $DESTINATION= $places_docType[$newDocType]['DESTINATION'];

                // Si tu utilises un WorkflowRegistry, il faut injecter $this->registry ou autre
                $workflow = $this->registry->get($document, 'document_workflow');
                $markingStart = $workflow->getMarking($document)->getPlaces();

                if (is_array($ORIGINE)) {
                    if (array_key_exists('Achat_F30', $markingStart)) {
                        $ORIGINE = 'Achat_F30';
                    } elseif (array_key_exists('Achat_Rfq', $markingStart)) {
                        $ORIGINE = 'Achat_Rfq';
                    } else {
                        $ORIGINE = 'Quality';
                    }
                }

                unset($markingStart[$ORIGINE]);

                // Gérer les destinations multiples (comme ASSY -> Assembly + Quality)
                if (is_array($DESTINATION)) {
                    foreach ($DESTINATION as $dest) {
                        $markingStart[$dest] = 1;
                    }
                } else {
                    $markingStart[$DESTINATION] = 1;
                }

                if (in_array($ORIGINE, ['Quality','Achat_F30','Achat_Rfq'])) {
                    $visas = $document->getVisas();
                    foreach ($visas as $visa) {
                        if (in_array($visa->getName(), ['visa_Quality', 'visa_Achat_F30', 'visa_Achat_Rfq'])) {
                            $entityManager->remove($visa);
                        }
                    }
                }
                $document->setCurrentSteps($markingStart);

                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Mise à jour du document');

                $entityManager->flush();
            }
        }

        // Récupérer les places actuelles du document pour les retourner dans la réponse
        $currentSteps = $document->getCurrentSteps();
        $currentPlaces = array_keys($currentSteps);

        return new JsonResponse([
            'status' => 'success',
            'message' => 'Document mis à jour avec succès',
            'currentPlaces' => $currentPlaces,
            'field' => $field
        ]);
    }

    // update metroControl field
    #[Route('/update-metro-control', name: 'update_metro_control', methods: ['POST'])]
    public function UpdateMetroControl(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        // Récupérer les données de la requête AJAX
        $data = json_decode($request->getContent(), true);

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'], $data['metroControl'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['id'];
        $metroControl = $data['metroControl'];
        // dd($metroControl);

        // Rechercher le document correspondant
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // Mettre à jour le champ metroControl
        $document->setMetroControl($metroControl);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('edit', $this->getUser(), 'Modification du contrôle métrologique');

        // Persister les modifications
        $entityManager->persist($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'MetroControl mis à jour avec succès']);
    }
    // public function workflowMove(Document $document, EntityManagerInterface $entityManager): JsonResponse
    // {
    //     $workflow = $this->registry->get($document, 'document_workflow');
    //     $markingStart = $workflow->getMarking($document)->getPlaces();
    //     $enabledTransitions = $workflow->getEnabledTransitions($document);
    //     $marking = [];
    //     $timestamps = $document->getStateTimestamps() ?? [];

    //     foreach ($enabledTransitions as $transition) {
    //         // On marque la place « to » comme atteignable
    //         $to = $transition->getTos()[0];
    //         $marking[$to] = 1;

    //         if (!isset($timestamps[$to])) {
    //             $timestamps[$to] = (new \DateTime())->format('Y-m-d H:i:s');
    //         }
    //         // On supprime la place « from » de la liste des places courantes
    //         // si elle y est encore présente
    //         $from = $transition->getFroms()[0];
    //         if (isset($markingStart[$from])) {
    //             unset($markingStart[$from]);
    //         }
    //     }

    //     // Fusion : on conserve aussi les places non « consommées »
    //     $finalMarking = array_merge($marking, $markingStart);

    //     // $timestamps = array_intersect_key($timestamps, $finalMarking);

    //     // Mise à jour de l’entité Document (ou autre entité ciblée)
    //     $document->setCurrentSteps($finalMarking);
    //     $document->setStateTimestamps($timestamps);

    //     // Persistance en base
    //     $entityManager->flush();

    //     return new JsonResponse(['status' => 'success', 'message' => $finalMarking]);
    // }
    public function workflowMove(Document $document, EntityManagerInterface $entityManager): JsonResponse
    {
        $workflow = $this->registry->get($document, 'document_workflow');
        $markingStart = $workflow->getMarking($document)->getPlaces();
        $enabledTransitions = $workflow->getEnabledTransitions($document);
        $marking = [];

        foreach ($enabledTransitions as $transition) {
            // On marque la place « to » comme atteignable
            $to = $transition->getTos()[0];
            $marking[$to] = 1;

            // Enregistrer l'entrée dans le nouvel état
            $document->addStateEnter($to);

            // On supprime la place « from » de la liste des places courantes
            // si elle y est encore présente
            $from = $transition->getFroms()[0];
            if (isset($markingStart[$from])) {
                // Enregistrer la sortie de l'état précédent
                $document->addStateExit($from);
                unset($markingStart[$from]);
            }

            // Si le document arrive dans la place Assembly, on modifie son procType à E et son Unit à PC
            if ($to === 'Assembly') {
                // Ne pas écraser les valeurs existantes si elles sont déjà définies
                $document->setProcType('E');
                $document->setUnit('PC');

                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Assembly: procType=E, Unit=PC');

                // Conserver les autres champs s'ils sont déjà définis
                if ($document->getCustDrawing() === null) $document->setCustDrawing($document->getCustDrawing());
                if ($document->getCustDrawingRev() === null) $document->setCustDrawingRev($document->getCustDrawingRev());
                if ($document->getAction() === null) $document->setAction($document->getAction());
                if ($document->getEx() === null) $document->setEx($document->getEx());
            }

            // Si le document arrive dans la place Machining, on modifie son procType à E, son Unit à PC, son prodAgent à USI et son matProdType à HALB
            if ($to === 'Machining') {
                $document->setProcType('E');
                $document->setUnit('PC');
                $document->setProdAgent('USI');
                $document->setMatProdType('HALB');

                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Machining: procType=E, Unit=PC, prodAgent=USI, matProdType=HALB');

                // Conserver les autres champs s'ils sont déjà définis
                if ($document->getCustDrawing() === null) $document->setCustDrawing($document->getCustDrawing());
                if ($document->getCustDrawingRev() === null) $document->setCustDrawingRev($document->getCustDrawingRev());
                if ($document->getAction() === null) $document->setAction($document->getAction());
                if ($document->getEx() === null) $document->setEx($document->getEx());
            }

            // Si le document arrive dans la place Molding, on modifie son procType à E et son matProdType à HALB
            if ($to === 'Molding') {
                $document->setProcType('E');
                $document->setMatProdType('HALB');

                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Molding: procType=E, matProdType=HALB');

                // Conserver les autres champs s'ils sont déjà définis
                if ($document->getCustDrawing() === null) $document->setCustDrawing($document->getCustDrawing());
                if ($document->getCustDrawingRev() === null) $document->setCustDrawingRev($document->getCustDrawingRev());
                if ($document->getAction() === null) $document->setAction($document->getAction());
                if ($document->getEx() === null) $document->setEx($document->getEx());
            }
        }

        // Fusion : on conserve aussi les places non « consommées »
        $finalMarking = array_merge($marking, $markingStart);

        // Mise à jour de l'entité Document (ou autre entité ciblée)
        $document->setCurrentSteps($finalMarking);

        // Persistance en base
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => $finalMarking]);
    }

    // create visa
    #[Route('/create-visa', name: 'create_visa', methods: ['POST'])]
    public function createVisa(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        // Récupérer les données de la requête AJAX
        $data = json_decode($request->getContent(), true);

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['documentId'], $data['name'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['documentId'];
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // Récupérer la page d'origine pour appliquer les validations spécifiques
        $currentPage = $data['currentPage'] ?? null;

        // Validation spécifique pour l'action "creation" SEULEMENT sur les pages Product Management
        $productManagementPages = ['Produit', 'Product']; // Pages où la validation CLS/MOQ est requise

        if (strtolower($document->getAction()) === 'creation' && in_array($currentPage, $productManagementPages)) {
            $validationErrors = [];

            // Vérifier CLS
            if ($document->getCls() === null || $document->getCls() === 0) {
                $validationErrors[] = 'CLS';
            }

            // Vérifier MOQ
            if ($document->getMoq() === null || $document->getMoq() === 0) {
                $validationErrors[] = 'MOQ';
            }

            // Vérifier Code Produit
            if (empty($document->getProductCode()) || trim($document->getProductCode()) === '' || strtoupper(trim($document->getProductCode())) === 'TBD') {
                $validationErrors[] = 'Code Produit';
            }

            // Vérifier ECCN
            if (empty($document->getEccn()) || trim($document->getEccn()) === '' || strtoupper(trim($document->getEccn())) === 'TBD') {
                $validationErrors[] = 'ECCN';
            }

            // Si des champs sont manquants, retourner une erreur
            if (!empty($validationErrors)) {
                $message = 'Pour l\'action "creation", les champs suivants doivent être renseignés : ' . implode(', ', $validationErrors);
                return new JsonResponse(['status' => 'error', 'message' => $message], 400);
            }
        }

        if(in_array($data['name'], ['Assembly','Machining','Molding'])){
            $originalName = $data['name']; // Sauvegarder le nom original
            $data['name'] = "prod";
            $document->setProcType('E');

            // Si le document arrive dans la place Assembly, on modifie également son Unit à PC
            if ($originalName === 'Assembly') {
                $document->setUnit('PC');
                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Assembly: Unit=PC');
            }

            // Si le document arrive dans la place Machining, on modifie son Unit à PC, son prodAgent à USI et son matProdType à HALB
            if ($originalName === 'Machining') {
                $document->setUnit('PC');
                $document->setProdAgent('USI');
                $document->setMatProdType('HALB');
                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Machining: Unit=PC, prodAgent=USI, matProdType=HALB');
            }

            // Si le document arrive dans la place Molding, on modifie son matProdType à HALB
            if ($originalName === 'Molding') {
                $document->setMatProdType('HALB');
                // Ajouter une entrée dans l'historique des mises à jour
                $document->addUpdate('edit', $this->getUser(), 'Modification automatique pour Molding: matProdType=HALB');
            }

            $entityManager->persist($document);
            $entityManager->flush();
        }

        $name = "visa_" . $data['name'];


        // Vérifier si le document a déjà un visa avec le même nom et en statut valid
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === $name && $existingVisa->getStatus() === 'valid') {
                return new JsonResponse(['status' => 'error', 'message' => 'visa valid existant'], 400);
            }
            if ($existingVisa->getName() === $name && $existingVisa->getStatus() !== 'valid') {
                $entityManager->remove($existingVisa);
                $entityManager->flush();
            }
        }

        $entityManager->refresh($document);
        // Créer l’entité Visa
        $this->creationVisa($document, $name, $entityManager);

        // refresh
        $entityManager->refresh($document);
        // Mettre à jour le workflow
        // $workflow = $this->registry->get($document, 'document_workflow');
        // dd($enabledTransitions = $workflow->getEnabledTransitions($document));
        $this->workflowMove($document, $entityManager);

        return new JsonResponse(['status' => 'success', 'message' => 'Visa créé avec succès']);
    }



    // create_visa_inventory

    #[Route('/create-visa-inventory', name: 'create_visa_inventory', methods: ['POST'])]
    public function createVisaInventory(
    Request $request, DocumentRepository $documentRepository, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        $documentId = $data['documentId'];
        $document = $documentRepository->find($documentId);

        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        $hasQualLogistiqueVisa = false;
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === 'visa_Qual_Logistique' && $existingVisa->getStatus() === 'valid') {
                $hasQualLogistiqueVisa = true;
                break;
            }
        }

        // Vérifier si le document a déjà un visa avec le même nom et en statut valid
        foreach ($document->getVisas() as $existingVisa) {
            if ($existingVisa->getName() === 'visa_Logistique' && $existingVisa->getStatus() === 'valid') {
                return new JsonResponse(['status' => 'error', 'message' => 'visa valid existant'], 400);
            }
            if ($existingVisa->getName() === 'visa_Logistique' && $existingVisa->getStatus() !== 'valid') {
                $entityManager->remove($existingVisa);
                $entityManager->flush();
            }
        }

        if (!$hasQualLogistiqueVisa) {
            $this->creationVisa($document, 'visa_Qual_Logistique', $entityManager);
            // refresh
            $entityManager->refresh($document);
            $this->workflowMove($document, $entityManager);

        } else {
            $this->creationVisa($document, 'visa_Logistique', $entityManager);
            // refresh
            $entityManager->refresh($document);
            $this->workflowMove($document, $entityManager);
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Visa Inventory créé avec succès']);
    }

    public function creationVisa(Document $document, String $nameVisa, EntityManagerInterface $entityManager): void
    {
        $visa = new Visa();
        $visa->setReleasedDrawing($document);
        $visa->setName($nameVisa);
        $visa->setStatus('valid');
        $visa->setDateVisa(new \DateTimeImmutable());
        // current user
        $currentUser = $this->getUser();
        $visa->setValidator($currentUser);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('visa', $currentUser, 'Ajout du visa ' . $nameVisa);

        $entityManager->persist($visa);
        $entityManager->flush();
    }



    #[Route('/new', name: 'app_document_new', methods: ['GET', 'POST'])]
    public function new(Request $request, EntityManagerInterface $entityManager): Response
    {
        $document = new Document();
        $form = $this->createForm(DocumentType::class, $document);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            // Ajouter une entrée dans l'historique des mises à jour
            $document->addUpdate('create', $this->getUser(), 'Création du document');

            $entityManager->persist($document);
            $entityManager->flush();

            return $this->redirectToRoute('app_document_index', [], Response::HTTP_SEE_OTHER);
        }

        return $this->render('document/new.html.twig', [
            'document' => $document,
            'form' => $form,
        ]);
    }

    // get all prisDans1 attribute of document in ajax - Optimized version
    #[Route('/prisDans1', name: 'get_prisDans1', methods: ['GET'])]
    public function prisDans1(DocumentRepository $documentRepository): JsonResponse
    {
        // Use optimized query to get distinct values directly from database
        $qb = $documentRepository->createQueryBuilder('d');
        $qb->select('DISTINCT d.prisDans1')
           ->where('d.prisDans1 IS NOT NULL')
           ->orderBy('d.prisDans1', 'ASC');

        $result = $qb->getQuery()->getScalarResult();
        $distinctPrisDans1Values = array_column($result, 'prisDans1');

        return new JsonResponse($distinctPrisDans1Values);
    }

        // get all prisDans1 attribute of document in ajax
        #[Route('/prisDans2', name: 'get_prisDans2', methods: ['GET'])]
        public function prisDans2(DocumentRepository $documentRepository): JsonResponse
        {
            // Use optimized query to get distinct values directly from database
            $qb = $documentRepository->createQueryBuilder('d');
            $qb->select('DISTINCT d.prisDans2')
               ->where('d.prisDans2 IS NOT NULL')
               ->orderBy('d.prisDans2', 'ASC');

            $result = $qb->getQuery()->getScalarResult();
            $distinctPrisDans2Values = array_column($result, 'prisDans2');

            return new JsonResponse($distinctPrisDans2Values);
        }

        #[Route('/purchasingGroup', name: 'get_purchasingGroup', methods: ['GET'])]
        public function purchasingGroup(DocumentRepository $documentRepository): JsonResponse
        {
            $purchasingGroupValues = $documentRepository->createQueryBuilder('d')
                ->select('DISTINCT d.purchasingGroup')
                ->where('d.purchasingGroup IS NOT NULL')
                ->orderBy('d.purchasingGroup', 'ASC')
                ->getQuery()
                ->getScalarResult();

            return new JsonResponse(array_column($purchasingGroupValues, 'purchasingGroup'));
        }

        #[Route('/commodityCode', name: 'get_commodityCode', methods: ['GET'])]
        public function commodityCode(CommodityCodeRepository $commodityCodeRepository): JsonResponse
        {
            // Récupérer tous les commodity codes depuis la table commodity_code
            $commodityCodes = $commodityCodeRepository->findAll();

            $result = [];
            foreach ($commodityCodes as $commodityCode) {
                $result[] = [
                    'value' => $commodityCode->getCode(),
                    'label' => $commodityCode->getCode() . ' - ' . $commodityCode->getDescription()
                ];
            }

            return new JsonResponse($result);
        }

        #[Route('/hts', name: 'get_hts', methods: ['GET'])]
        public function hts(DocumentRepository $documentRepository): JsonResponse
        {
            // Use optimized query to get distinct values directly from database
            $qb = $documentRepository->createQueryBuilder('d');
            $qb->select('DISTINCT d.hts')
               ->where('d.hts IS NOT NULL')
               ->orderBy('d.hts', 'ASC');

            $result = $qb->getQuery()->getScalarResult();
            $distinctHtsValues = array_column($result, 'hts');

            return new JsonResponse($distinctHtsValues);
        }

        #[Route('/productCode', name: 'get_productCode', methods: ['GET'])]
        public function productCode(ProductCodeRepository $productCodeRepository): JsonResponse
        {
            // Récupérer tous les ProductCode actifs avec code et description
            $productCodes = $productCodeRepository->findActive();

            $result = [];
            foreach ($productCodes as $productCode) {
                $displayValue = $productCode->getCode();
                if ($productCode->getDescription()) {
                    $displayValue .= ' - ' . $productCode->getDescription();
                }
                $result[] = [
                    'value' => $productCode->getCode(),
                    'label' => $displayValue
                ];
            }

            return new JsonResponse($result);
        }

        #[Route('/eccn', name: 'get_eccn', methods: ['GET'])]
        public function eccn(DocumentRepository $documentRepository): JsonResponse
        {
            // Use optimized query to get distinct values directly from database
            $qb = $documentRepository->createQueryBuilder('d');
            $qb->select('DISTINCT d.eccn')
               ->where('d.eccn IS NOT NULL')
               ->orderBy('d.eccn', 'ASC');

            $result = $qb->getQuery()->getScalarResult();
            $distinctEccnValues = array_column($result, 'eccn');

            return new JsonResponse($distinctEccnValues);
        }


    #[Route('/create-visa-be0/{id}', name: 'release_package', methods: ['POST'])]
    public function createVisaBE0(ReleasedPackage $package, EntityManagerInterface $entityManager, Request $request): JsonResponse
    {
        $documents = $package->getDocuments();
        $verif = $request->request->get('verif');

        $user = $entityManager->getRepository(User::class)->find($verif);
        $package->setVerif($user);
        foreach ($documents as $document) {
            $visa = new Visa();
            $visa->setReleasedDrawing($document);
            $visa->setName('visa_BE_0');
            $visa->setStatus('valid');
            $visa->setDateVisa(new \DateTimeImmutable());
            $visa->setValidator($this->getUser());
            $entityManager->persist($visa);
            $entityManager->flush();
            $this->workflowMove($document, $entityManager);
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Visa BE_0 créé avec succès']);
    }

    #[Route('/create-visa-be1/{id}', name: 'verification_package', methods: ['POST'])]
    public function createVisaBE1(ReleasedPackage $package, EntityManagerInterface $entityManager, Request $request): JsonResponse
    {
        $documents = $package->getDocuments();
        $valid = $request->request->get('valid');

        $user = $entityManager->getRepository(User::class)->find($valid);
        $package->setValid($user);
        foreach ($documents as $document) {
            $visa = new Visa();
            $visa->setReleasedDrawing($document);
            $visa->setName('visa_BE_1');
            $visa->setStatus('valid');
            $visa->setDateVisa(new \DateTimeImmutable());
            $visa->setValidator($this->getUser());
            $entityManager->persist($visa);
            $entityManager->flush();
            $this->workflowMove($document, $entityManager);
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Visa BE_1 créé avec succès']);
    }

    #[Route('/create-visa-be/{id}', name: 'validation_package', methods: ['POST'])]
    public function createVisaBE(ReleasedPackage $package, EntityManagerInterface $entityManager): JsonResponse
    {
        $documents = $package->getDocuments();
        foreach ($documents as $document) {
            $visa = new Visa();
            $visa->setReleasedDrawing($document);
            $visa->setName('visa_BE');
            $visa->setStatus('valid');
            $visa->setDateVisa(new \DateTimeImmutable());
            $visa->setValidator($this->getUser());
            $entityManager->persist($visa);
            $entityManager->flush();
            $this->workflowMove($document, $entityManager);
        }

        return new JsonResponse(['status' => 'success', 'message' => 'Visa BE créé avec succès']);
    }

    #[Route('/refuser-package/{id}', name: 'refuser_package', methods: ['POST'])]
    public function refuserPackage(ReleasedPackage $package, EntityManagerInterface $entityManager): JsonResponse
    {
        $documents = $package->getDocuments();
        foreach ($documents as $document) {
            // Récupérer l'état actuel
            $workflow = $this->registry->get($document, 'document_workflow');
            $currentMarking = $workflow->getMarking($document)->getPlaces();

            // Pour chaque état actuel, enregistrer la sortie
            foreach (array_keys($currentMarking) as $currentState) {
                $document->addStateExit($currentState);
            }

            // Définir le nouvel état BE_0
            $marking = ['BE_0' => 1];
            $document->setCurrentSteps($marking);

            // Enregistrer l'entrée dans BE_0
            $document->addStateEnter('BE_0', $this->getUser());

            $entityManager->flush();

            // Supprimer les visas
            $visas = $entityManager->getRepository(Visa::class)->findBy(['releasedDrawing' => $document]);
            foreach ($visas as $visa) {
                $entityManager->remove($visa);
            }
        }

        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Package refusé avec succès']);
    }



    #[Route('/add-document/{id}', name: 'add_document', methods: ['POST'])]
    public function addDocument(Request $request, EntityManagerInterface $em, $id): JsonResponse
    {
        try {
            $data = $request->request->all();
            unset($data['id'], $data['relPack']);

            $package = $em->getRepository(ReleasedPackage::class)->find($id);
            if (!$package) {
                return new JsonResponse(['status'=>'error','message'=>'Package non trouvé'], 404);
            }

            $documentRepo = $em->getRepository(Document::class);
            $document     = new Document();

            // 1) Si révision existante : recopie intégrale
            if (!empty($data['reference'])) {
                $source = $documentRepo->findLatestByReference($data['reference']);
                if ($source) {
                    $this->copyDocumentFields($source, $document);
                }
            }

            //
            // 2) Puis j’applique **uniquement** les champs du formulaire,
            //    qui prendront le pas sur la recopie :
            //

            // — textuels
            $document->setReference   ($data['reference']    ?? $document->getReference());
            $document->setRefRev      ($data['refRev']       ?? $document->getRefRev());
            $document->setRefTitleFra ($data['refTitleFra']  ?? $document->getRefTitleFra());
            $document->setProdDraw    ($data['prodDraw']     ?? $document->getProdDraw());
            $document->setProdDrawRev ($data['prodDrawRev']  ?? $document->getProdDrawRev());
            $document->setAlias       ($data['alias']        ?? $document->getAlias());
            $document->setAction      ($data['action']       ?? $document->getAction());
            $document->setDocType     ($data['docType']      ?? $document->getDocType());
            $document->setProcType    ($data['procType']     ?? $document->getProcType());
            $document->setMatProdType ($this->mapMaterialTypeToSAP($data['matProdType'] ?? $document->getMatProdType()));
            $document->setInventoryImpact($data['inventoryImpact'] ?? $document->getInventoryImpact());
            $document->setEx          ($data['ex']           ?? $document->getEx());

            // — ALETIQ / dessins / titre
            $document->setIdAletiq(
                isset($data['idAletiq']) && $data['idAletiq'] !== '' 
                    ? (int)$data['idAletiq'] 
                    : $document->getIdAletiq()
            );
            $document->setCustDrawing    ($data['custDrawing']    ?? $document->getCustDrawing());
            $document->setCustDrawingRev ($data['custDrawingRev'] ?? $document->getCustDrawingRev());

            // — numériques
            $toInt = fn($k) => isset($data[$k]) && $data[$k] !== '' ? (int)$data[$k] : null;
            $document->setWeight         ($toInt('weight'));
            $document->setPlatingSurface ($toInt('platingSurface'));
            // — unité de surface de placage
            $document->setPlatingSurfaceUnit(
                isset($data['platingSurfaceUnit']) && $data['platingSurfaceUnit'] !== ''
                    ? $data['platingSurfaceUnit']
                    : $document->getPlatingSurfaceUnit()
            );
            $document->setCls            ($toInt('cls'));
            $document->setMoq            ($toInt('moq'));
            $document->setLeadtime       ($toInt('leadtime'));
            $document->setMetroTime      ($toInt('metroTime'));
            $document->setCriticalComplete($toInt('criticalComplete'));

            // — booléens
            $document->setInternalMachRec(
                array_key_exists('internalMachRec', $data)
                    ? (bool)$data['internalMachRec']
                    : $document->isInternalMachRec()
            );
            $document->setSwitchAletiq  (isset($data['switchAletiq']) ? (bool)$data['switchAletiq'] : $document->isSwitchAletiq());
            $document->setDocImpact     (isset($data['docImpact'])    ? (bool)$data['docImpact']    : $document->isDocImpact());

            // — chaînes & relations
            if (!empty($data['productCode'])) {
                $pc = $em->getRepository(ProductCode::class)
                        ->findOrCreateByCode($data['productCode'], false);
                $document->setProductCode($pc);
            }
            $document->setCommodityCode   ($data['commodityCode'] ?? $document->getCommodityCode());
            $document->setPurchasingGroup ($data['purchasingGroup'] ?? $document->getPurchasingGroup());
            $document->setUnit            ($data['Unit'] ?? $document->getUnit());
            $document->setFia             ($data['fia'] ?? $document->getFia());
            $document->setEccn            ($data['eccn'] ?? $document->getEccn());
            $document->setRdo             ($data['rdo'] ?? $document->getRdo());
            $document->setHts             ($data['hts'] ?? $document->getHts());

            // — tableaux JSON (qInspection, qDocRec, metroControl), peuvent être string ou array
            if (\array_key_exists('qInspection', $data)) {
                $val = $data['qInspection'];
                $document->setQInspection(
                    \is_string($val) 
                        ? \json_decode($val, true) 
                        : (array) $val
                );
            }

            if (\array_key_exists('qDynamization', $data)) {
                $document->setQDynamization($data['qDynamization']);
            }

            if (\array_key_exists('qDocRec', $data)) {
                $val = $data['qDocRec'];
                $document->setQDocRec(
                    \is_string($val) 
                        ? \json_decode($val, true) 
                        : (array) $val
                );
            }

            if (\array_key_exists('qControlRouting', $data)) {
                $document->setQControlRouting($data['qControlRouting']);
            }

            if (isset($data['metroControl'])) {
                // 1) parse en JSON s’il s’agit d’une chaîne
                $raw = $data['metroControl'];
                if (is_string($raw)) {
                    $parsed = json_decode($raw, true);
                } else {
                    $parsed = $raw;
                }
                // 2) n’appelle le setter que si on a bien un array
                if (is_array($parsed)) {
                    $document->setMetroControl($parsed);
                }
                // sinon, on skip : le champ restera à sa valeur par défaut (null ou ce que vous voulez)
            }

            // — matériaux
            if (!empty($data['materials']) && is_array($data['materials'])) {
                $matRepo = $em->getRepository(Material::class);
                foreach ($data['materials'] as $mid) {
                    if ($m = $matRepo->find($mid)) {
                        $document->addMaterial($m);
                    }
                }
            }

            // — commentaire initial
            if (isset($data['comments'])) {
                $c = new Commentaire();
                $c->setUser($this->getUser())
                ->setCommentaire($data['comments'])
                ->setType('principal')
                ->setDocuments($document)
                ->setCreatedAt(new \DateTimeImmutable());
                $em->persist($c);
            }

            // — finalisation
            $document->setRelPack($package)
                    ->setCurrentSteps(['BE_0' => 1])
                    ->setStateTimestamps(null)
                    ->setUpdates(null)
                    ->addUpdate('create', $this->getUser(), 'Création du document')
            ;

            $em->persist($document);
            $em->flush();

            return new JsonResponse(['status'=>'success','message'=>'Document créé avec succès']);

        }catch (\Throwable $e) {
            // On renvoie maintenant le message et la trace complète pour débug
            return new JsonResponse([
                'status'  => 'error',
                'message' => $e->getMessage(),
                'trace'   => explode("\n", $e->getTraceAsString())
            ], 500);
        }
    }



    /**
     * Copie tous les champs d'un document source vers un nouveau document
     * Exclut les champs qui doivent être uniques ou réinitialisés
     */
    private function copyDocumentFields(Document $source, Document $target): void
    {
        try {
            // Champs de base
            $target->setRefTitleFra($source->getRefTitleFra());
            $target->setProdDraw($source->getProdDraw());
            $target->setProdDrawRev($source->getProdDrawRev());
            $target->setAlias($source->getAlias());
            $target->setDocType($source->getDocType());
            $target->setMatProdType($source->getMatProdType());
            $target->setProcType($source->getProcType());
            $target->setInventoryImpact($source->getInventoryImpact());
            $target->setAction($source->getAction());
            $target->setEx($source->getEx());

            // Champs techniques importants (ceux qui manquaient)
            $target->setCls($source->getCls());
            $target->setMoq($source->getMoq());
            $target->setCommodityCode($source->getCommodityCode());
            $target->setPurchasingGroup($source->getPurchasingGroup());
            $target->setFia($source->getFia());
            $target->setEccn($source->getEccn());
            $target->setRdo($source->getRdo());
            $target->setHts($source->getHts());

            // Autres champs techniques
            $target->setWeight($source->getWeight());
            $target->setWeightUnit($source->getWeightUnit());
            $target->setPlatingSurface($source->getPlatingSurface());
            $target->setPlatingSurfaceUnit($source->getPlatingSurfaceUnit());
            $target->setInternalMachRec($source->isInternalMachRec());
            $target->setProdAgent($source->getProdAgent());
            $target->setMof($source->getMof());
            $target->setUnit($source->getUnit());
            $target->setLeadtime($source->getLeadtime());
            $target->setPrisDans1($source->getPrisDans1());
            $target->setPrisDans2($source->getPrisDans2());

            $target->setMetroTime($source->getMetroTime());
            $target->setQInspection($source->getQInspection());
            $target->setQDynamization($source->getQDynamization());
            $target->setQDocRec($source->getQDocRec());
            $target->setQControlRouting($source->getQControlRouting());
            $target->setCriticalComplete($source->getCriticalComplete());
            $target->setSwitchAletiq($source->isSwitchAletiq());
            $target->setMetroControl($source->getMetroControl() ?? []);
            $target->setDocImpact($source->isDocImpact() ?? false);

            // Relations importantes
            if ($source->getSuperviseur()) {
                $target->setSuperviseur($source->getSuperviseur());
            }
            if ($source->getQualOwner()) {
                $target->setQualOwner($source->getQualOwner());
            }
            if ($source->getProductCode()) {
                $target->setProductCode($source->getProductCode());
            }

            // Matériaux
            foreach ($source->getMaterials() as $material) {
                $target->addMaterial($material);
            }
        } catch (\Exception $e) {
            throw $e;
        }

        // NE PAS copier ces champs (ils doivent être uniques ou réinitialisés) :
        // - id (auto-généré)
        // - reference (sera défini par le formulaire)
        // - refRev (sera défini par le formulaire)
        // - idAletiq (sera défini par le formulaire ou null)
        // - relPack (sera défini par le package courant)
        // - currentSteps (nouveau workflow)
        // - stateTimestamps (nouveaux timestamps)
        // - updates (nouvel historique)
        // - visas (nouveaux visas)
        // - commentaires (nouveaux commentaires)
    }


    #[Route('/maj-document/{id}', name: 'maj_document', methods: ['POST'])]
    public function majDocument(Request $request, EntityManagerInterface $entityManager, $id): JsonResponse
    {
        $data = $request->request->all();
        $document = $entityManager->getRepository(Document::class)->find($id);

        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        $document->setReference($data['reference'] ?? null);
        $document->setRefRev($data['refRev'] ?? null);
        $document->setIdAletiq($data['idAletiq'] ?? null);
        $document->setProdDraw($data['prodDraw'] ?? null);
        $document->setProdDrawRev($data['prodDrawRev'] ?? null);
        $document->setAlias($data['alias'] ?? null);
        $document->setRefTitleFra($data['refTitleFra'] ?? null);
        $document->setAction($data['action'] ?? null);
        $document->setDocType($data['docType'] ?? null);
        // Mapper les anciens libellés vers les codes SAP si nécessaire
        $matProdType = $this->mapMaterialTypeToSAP($data['matProdType'] ?? $data['materialType'] ?? null);
        $document->setMatProdType($matProdType);
        $document->setInventoryImpact($data['inventoryImpact'] ?? null);
        $document->setEx($data['ex'] ?? null);
        $document->setCustDrawing($data['custDrawing'] ?? null);
        $document->setCustDrawingRev($data['custDrawingRev'] ?? null);
        $document->setWeight($data['weight'] ?? null);
        $document->setWeightUnit($data['weightUnit'] ?? null);
        // Gérer les matériaux multiples
        if (isset($data['materials']) && is_array($data['materials'])) {
            // Vider les matériaux existants
            foreach ($document->getMaterials() as $material) {
                $document->removeMaterial($material);
            }

            // Ajouter les nouveaux matériaux
            $materialRepository = $entityManager->getRepository(Material::class);
            foreach ($data['materials'] as $materialId) {
                $material = $materialRepository->find($materialId);
                if ($material) {
                    $document->addMaterial($material);
                }
            }
        }
        $document->setPlatingSurface($data['platingSurface'] ?? null);
        $document->setPlatingSurfaceUnit($data['platingSurfaceUnit'] ?? null);
        $document->setInternalMachRec($data['internalMachRec'] ?? null);
        $document->setEccn($data['eccn'] ?? null);
        $document->setRdo($data['rdo'] ?? null);
        $document->setHts($data['hts'] ?? null);
        $document->setDocImpact(0);

        $existingPrincipalComment = null;
        foreach ($document->getCommentaires() as $comment) {
            if ($comment->gettype() === 'principal') {
                $existingPrincipalComment = $comment;
                break;
            }
        }

        if (isset($data['comments'])) {
            if ($existingPrincipalComment) {
                $existingPrincipalComment->setCommentaire($data['comments']);
            } else {
                $commentaire = new Commentaire();
                $commentaire->setUser($this->getUser());
                $commentaire->setCommentaire($data['comments']);
                $commentaire->settype('principal');
                $commentaire->setDocuments($document);
                $commentaire->setCreatedAt(new \DateTimeImmutable());
                $entityManager->persist($commentaire);
            }
        }

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('edit', $this->getUser(), 'Mise à jour du document');

        $entityManager->persist($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Document mis à jour avec succès']);
    }


    #[Route('/delete-document/{id}', name: 'delete_document', methods: ['POST'])]
    public function deleteDocument(Request $request, EntityManagerInterface $entityManager, $id): JsonResponse
    {
        $document = $entityManager->getRepository(Document::class)->find($id);

        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        foreach ($document->getCommentaires() as $comment) {
            $entityManager->remove($comment);
        }

        $entityManager->remove($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Document supprimé avec succès']);
    }

    // find document by id return json
    #[Route('/document/{id}', name: 'get_document', methods: ['POST'])]
    public function getDocument(Document $document): JsonResponse
    {
        // dd($document);
        return new JsonResponse($document->toJson());
    }

    #[Route('/document/count-document', name: 'count_document', methods: ['GET'])]
    public function countDocument(DocumentRepository $documentRepository): JsonResponse
    {
        // Utiliser la nouvelle méthode optimisée avec cache
        $count = $documentRepository->countDocumentsByWorkflowStepCached();
        return new JsonResponse($count);
    }

    #[Route('/document/export-csv/{place}', name: 'app_document_export_csv', methods: ['GET'])]
    public function exportDocumentsCsv(string $place, DocumentRepository $documentRepository): Response
    {
        // Redirection pour les places BE vers les packages
        if ($place === 'BE_0' || $place === 'BE_1' || $place === 'BE') {
            return new Response('Export non disponible pour cette place', 404);
        }

        // Utiliser la même logique que getAllDocumentIds pour récupérer les documents
        $conn = $documentRepository->getEntityManager()->getConnection();

        if ($place === 'Qual_Logistique') {
            $sql = "
                SELECT d.*
                FROM document d
                WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                AND NOT EXISTS (
                    SELECT 1 FROM visa v
                    WHERE v.released_drawing_id = d.id
                    AND v.name = 'visa_Qual_Logistique'
                    AND v.status = 'valid'
                )
                ORDER BY d.id DESC
            ";
            $result = $conn->executeQuery($sql, ['$."' . $place . '"']);
        } else {
            $sql = "
                SELECT d.*
                FROM document d
                WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                AND NOT EXISTS (
                    SELECT 1 FROM visa v
                    WHERE v.released_drawing_id = d.id
                    AND v.name = ?
                    AND v.status = 'valid'
                )
                ORDER BY d.id DESC
            ";
            $result = $conn->executeQuery($sql, ['$."' . $place . '"', 'visa_' . $place]);
        }

        $documentsData = $result->fetchAllAssociative();

        // Convertir en objets Document pour utiliser les getters
        $documents = [];
        foreach ($documentsData as $docData) {
            $document = $documentRepository->find($docData['id']);
            if ($document) {
                $documents[] = $document;
            }
        }

        // Générer le CSV
        $response = new Response();
        $response->headers->set('Content-Type', 'text/csv; charset=utf-8');
        $response->headers->set('Content-Disposition', 'attachment; filename="documents_' . $place . '_' . date('Y-m-d_H-i-s') . '.csv"');

        // Ajouter le BOM UTF-8 pour Excel
        $csvData = "\xEF\xBB\xBF";
        $csvData .= "Pack;Activity;Reference;RefRev;ProdDraw;ProdDrawRev;DocType;ProcType;Material;MaterialType;Action;CLS;MOQ;Valorisation;CodePrix;GrpFraisGen;CurrentSteps;Commentaires\n";

        foreach ($documents as $document) {
            // Calculs SAP Data comme dans le template
            $class_val = '-';
            if ($document->getMatProdType() == 'FERT') {
                $class_val = '7920';
            } elseif ($document->getMatProdType() == 'HALB' && ($document->getProcType() == 'F' || $document->getProcType() == 'E')) {
                $class_val = '7900';
            } elseif ($document->getMatProdType() == 'HALB' && $document->getProcType() == 'F30') {
                $class_val = '7910';
            } elseif ($document->getMatProdType() == 'VERP') {
                $class_val = '3050';
            } elseif ($document->getMatProdType() == 'ROH') {
                if ($document->getDocType() == 'DOC') {
                    $class_val = '3000';
                } else {
                    $class_val = '3030';
                }
            }

            $code_prix = '-';
            if ($document->getProcType() == 'F' || $document->getProcType() == 'F30') {
                $code_prix = 'V';
            } elseif ($document->getProcType() == 'E') {
                $code_prix = 'S';
            }

            $frai_gen = '-';
            if ($document->getMatProdType() != 'FERT' && ($document->getProcType() == 'F' || $document->getProcType() == 'F30')) {
                $frai_gen = 'ROH';
            } elseif ($document->getMatProdType() != 'VERP' || $document->getMatProdType() != 'ROH') {
                $frai_gen = 'ROH';
            }

            $currentSteps = implode('|', array_keys($document->getCurrentSteps()));

            // Récupérer et formater les commentaires
            $comments = [];
            foreach ($document->getCommentaires() as $commentaire) {
                $user = $commentaire->getUser() ?
                    $commentaire->getUser()->getPrenom() . ' ' . $commentaire->getUser()->getNom() :
                    'Utilisateur inconnu';
                $date = $commentaire->getCreatedAt() ?
                    $commentaire->getCreatedAt()->format('d/m/Y H:i') :
                    '';
                $state = $commentaire->getState() ?? 'Général';

                // Nettoyer et encoder correctement le texte du commentaire
                $commentText = $commentaire->getCommentaire();
                // Supprimer les caractères de contrôle et normaliser l'encodage
                $commentText = preg_replace('/[\x00-\x1F\x7F]/', '', $commentText);
                $commentText = mb_convert_encoding($commentText, 'UTF-8', 'UTF-8');

                $comments[] = sprintf(
                    "[%s] %s - %s (%s)",
                    $state,
                    $commentText,
                    $user,
                    $date
                );
            }
            $commentsText = implode(' | ', $comments);

            $csvData .= sprintf(
                "%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s;%s\n",
                $document->getRelPack() ? $document->getRelPack()->getId() : '',
                $document->getRelPack() ? $document->getRelPack()->getActivity() : '',
                $document->getReference() ?? '',
                $document->getRefRev() ?? '',
                $document->getProdDraw() ?? '',
                $document->getProdDrawRev() ?? '',
                $document->getDocType() ?? '',
                $document->getProcType() ?? '',
                $document->getMaterial() ?? '',
                $document->getMatProdType() ?? '',
                $document->getAction() ?? '',
                $document->getCls() ?? '',
                $document->getMoq() ?? '',
                $class_val,
                $code_prix,
                $frai_gen,
                $currentSteps,
                '"' . str_replace('"', '""', $commentsText) . '"'
            );
        }

        $response->setContent($csvData);
        return $response;
    }

    #[Route('/document/get-all-ids/{place}', name: 'app_document_get_all_ids', methods: ['GET'])]
    public function getAllDocumentIds(string $place, DocumentRepository $documentRepository): JsonResponse
    {
        // Redirection pour les places BE vers les packages
        if ($place === 'BE_0' || $place === 'BE_1' || $place === 'BE') {
            return new JsonResponse([]);
        }

        // Optimized: Get only IDs directly from database instead of loading full entities
        if ($place === 'Qual_Logistique' || $place === 'Logistique') {
            // Use native SQL to get only IDs for logistics steps
            $conn = $documentRepository->getEntityManager()->getConnection();
            $sql = "
                SELECT d.id
                FROM document d
                WHERE (
                    JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                    OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
                )
                AND NOT (
                    EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                    AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
                )
                ORDER BY d.id DESC
            ";
            $result = $conn->executeQuery($sql);
            $documentIds = array_map(function($row) { return ['id' => (int)$row['id']]; }, $result->fetchAllAssociative());
        } else {
            // Use optimized query to get only IDs for other steps
            $conn = $documentRepository->getEntityManager()->getConnection();
            $sql = "
                SELECT d.id
                FROM document d
                WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
                AND NOT EXISTS (
                    SELECT 1 FROM visa v
                    WHERE v.released_drawing_id = d.id
                    AND v.name = ?
                    AND v.status = 'valid'
                )
                ORDER BY d.id DESC
            ";
            $result = $conn->executeQuery($sql, ['$."' . $place . '"', 'visa_' . $place]);
            $documentIds = array_map(function($row) { return ['id' => (int)$row['id']]; }, $result->fetchAllAssociative());
        }

        return new JsonResponse($documentIds);
    }

    #[Route('/document/package/{id}', name: 'get_document_package', methods: ['POST'])]
    public function getDocumentPackage(ReleasedPackage $package): JsonResponse
    {
        $documents = $package->getDocuments();
        $data = [];
        foreach ($documents as $document) {
            $data[] = $document->toJson();
        }
        return new JsonResponse($data);
    }



    #[Route('/teleportation', name: 'teleportation', methods: ['POST'])]
    public function teleportation(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = $request->request->all();
        $documentId = $data['document'];
        $position = $data['position'];
        $retourner = $data['retourner'];

        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['success' => false, 'error' => 'Document non trouvé'], 404);
        }

        // Supprimer les visas correspondants
        $visas = $document->getVisas();
        foreach ($retourner as $place) {
            if(in_array($place, ['Assembly','Machining','Molding'])){
                $place = "prod";
            }
            foreach ($visas as $visa) {
                if ($visa->getName() === 'visa_'.$place) {
                    $entityManager->remove($visa);
                }
            }
        }

        // Enregistrer la sortie de l'état actuel
        if (isset($document->getCurrentSteps()[$position])) {
            $document->addStateExit($position);
        }

        // Mettre à jour les états courants
        $currentSteps = $document->getCurrentSteps();
        unset($currentSteps[$position]);

        // Ajouter les nouveaux états et enregistrer les entrées
        foreach ($retourner as $place) {
            $currentSteps[$place] = 1;
            // Enregistrer l'entrée dans le nouvel état
            $document->addStateEnter($place);
        }

        $document->setCurrentSteps($currentSteps);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('state_change', $this->getUser(), 'Téléportation du document');

        $entityManager->flush();

        return new JsonResponse(['success' => true]);
    }


    #[Route('/comment-gid', name: 'comment_gid', methods: ['POST'])]
    public function commentGID(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = $request->request->all();
        $commentId = $data['commentId'];
        $documentId = $data['documentId'];
        $comment = $data['comment'];
        $state = $data['state'];

        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['success' => false, 'error' => 'Document non trouvé'], 404);
        }

        $commentaire = $entityManager->getRepository(Commentaire::class)->find($commentId);

        // If comment is empty and commentaire exists, delete it
        if (empty($comment) && $commentaire) {
            $entityManager->remove($commentaire);
            $entityManager->flush();
            return new JsonResponse(['success' => true]);
        }

        if (!$commentaire) {
            $commentaire = new Commentaire();
            $commentaire->setUser($this->getUser());
            $commentaire->setState($state);
            $commentaire->setType('secondaire');
            $commentaire->setDocuments($document);
            $commentaire->setCreatedAt(new \DateTimeImmutable());
        }

        $commentaire->setCommentaire($comment);
        $entityManager->persist($commentaire);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('comment', $this->getUser(), 'Ajout/modification d\'un commentaire ' . $state);

        $entityManager->flush();

        return new JsonResponse(['success' => true]);
    }
    #[Route('/update-modif-docImpact', name: 'update_modifdocImpact', methods: ['POST'])]
    public function updateModifExigencesQual(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $data = json_decode($request->getContent(), true);

        if ($data === null) {
            return new JsonResponse(['status' => 'error', 'message' => 'Requête mal formée ou vide'], 400);
        }

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'], $data['docImpact'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['id'];
        $docImpact = $data['docImpact'];

        // Rechercher le document correspondant
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // Mettre à jour le champ modifExigencesQual
        $document->setDocImpact($docImpact);

        // Ajouter une entrée dans l'historique des mises à jour
        $document->addUpdate('edit', $this->getUser(), 'Modification de l\'impact documentaire');

        // Persister les modifications
        $entityManager->persist($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'docImpact mis à jour avec succès']);
    }

    //delete document ajax
    #[Route('/delete-document-ajax', name: 'delete_document_ajax', methods: ['POST'])]
    public function deleteDocumentAjax(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        if(!($this->getUser()->isManager())){
            return new JsonResponse(['status' => 'error', 'message' => 'Vous ne pouvez pas supprimer ce document'], 403);
        }
        $data = json_decode($request->getContent(), true);

        if ($data === null) {
            return new JsonResponse(['status' => 'error', 'message' => 'Requête mal formée ou vide'], 400);
        }

        // Vérifier que les données nécessaires sont présentes
        if (!isset($data['id'])) {
            return new JsonResponse(['status' => 'error', 'message' => 'Données invalides'], 400);
        }

        $documentId = $data['id'];

        // Rechercher le document correspondant
        $document = $entityManager->getRepository(Document::class)->find($documentId);
        if (!$document) {
            return new JsonResponse(['status' => 'error', 'message' => 'Document non trouvé'], 404);
        }

        // Supprimer les commentaires associés au document
        foreach ($document->getCommentaires() as $commentaire) {
            $entityManager->remove($commentaire);
        }

        // Supprimer le document
        $entityManager->remove($document);
        $entityManager->flush();

        return new JsonResponse(['status' => 'success', 'message' => 'Document et ses commentaires supprimés avec succès']);
    }

    #[Route('/find-document', name: 'find_document', methods: ['POST'])]
    public function findDocument(Request $request, DocumentRepository $documentRepository): JsonResponse
    {
        $reference = $request->request->get('reference');

        if (!$reference) {
            return new JsonResponse(['error' => 'Référence manquante'], 400);
        }

        // Rechercher le document avec la référence donnée et la révision la plus récente
        $documents = $documentRepository->findBy(['reference' => $reference], ['refRev' => 'DESC']);

        if (empty($documents)) {
            return new JsonResponse(['error' => 'Document non trouvé'], 404);
        }

        // Prendre le document avec la révision la plus récente (le premier dans la liste triée)
        $document = $documents[0];

        // Convertir le document en tableau pour le retourner en JSON
        // Mais exclure les commentaires pour optimiser la réponse
        $documentData = $document->toJson();
        
        // Supprimer les commentaires de la réponse
        unset($documentData['commentaires']);
        unset($documentData['commentaires_By_Owner']);
        // unset($documentData['GID']);
        // unset($documentData['GID2']);

        return new JsonResponse(['document' => $documentData]);
    }

    #[Route('/test-badges', name: 'test_badges', methods: ['GET'])]
    public function testBadges(DocumentRepository $documentRepository): JsonResponse
    {
        try {
            // Test direct du repository
            $badges = $documentRepository->countDocumentsByWorkflowStepCached();

            return new JsonResponse([
                'status' => 'success',
                'badges' => $badges,
                'count' => count($badges),
                'message' => 'Badges récupérés avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'status' => 'error',
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine()
            ], 500);
        }
    }

    /**
     * Mappe les anciens libellés de type de matériau vers les codes SAP
     */
    private function mapMaterialTypeToSAP(?string $value): ?string
    {
        if (!$value) {
            return null;
        }

        $mapping = [
            'FINISHED PRODUCT' => 'FERT',
            'SEMI-FINISHED PRODUCT' => 'HALB',
            'SEMI FINISHED PRODUCT' => 'HALB',
            'PACKAGING' => 'VERP',
            'LITTERATURE' => 'LITERATURE',
            'Material' => 'ROH'
        ];

        // Si la valeur est déjà un code SAP valide, la retourner telle quelle
        $validSapCodes = ['FERT', 'HALB', 'VERP', 'ROH', 'RAW MATERIAL', 'NON VALUATED MATERIAL', 'LITERATURE'];
        if (in_array($value, $validSapCodes)) {
            return $value;
        }

        // Sinon, essayer de mapper depuis les anciens libellés
        return $mapping[$value] ?? $value; // Fallback: garder la valeur originale si pas de mapping
    }

    /**
     * Nettoie les données spécifiques selon les transitions de doctype et la place d'origine
     */
    private function cleanDataForDoctypeTransition(Document $document, string $oldDocType, string $newDocType, EntityManagerInterface $entityManager): void
    {
        $currentSteps = $document->getCurrentSteps();
        $currentPlace = $this->getCurrentWorkflowPlace($currentSteps);
        $cleanedFields = [];

        // Déterminer la logique de nettoyage selon la transition et la place d'origine
        if ($newDocType === 'PUR') {
            // Transitions vers PUR
            $this->handleTransitionToPur($document, $oldDocType, $currentPlace, $cleanedFields);
        } elseif ($oldDocType === 'PUR') {
            // Transitions depuis PUR
            $this->handleTransitionFromPur($document, $newDocType, $currentPlace, $cleanedFields);
        }

        if (!empty($cleanedFields)) {
            // Ajouter une entrée dans l'historique des mises à jour
            $document->addUpdate('edit', $this->getUser(), 'Nettoyage automatique lors du changement de type (depuis ' . $currentPlace . '): ' . implode(', ', $cleanedFields));
        }
    }

    /**
     * Détermine la place actuelle principale du document dans le workflow
     */
    private function getCurrentWorkflowPlace(array $currentSteps): string
    {
        // Priorité aux places de production et d'achat
        $priorityPlaces = [
            'Achat_F30' => 'Achat_F30',
            'Achat_Rfq' => 'Achat_Rfq',
            'Assembly' => 'Assembly',
            'Machining' => 'Machining',
            'Molding' => 'Molding',
            'Quality' => 'Quality'
        ];

        foreach ($priorityPlaces as $place => $name) {
            if (isset($currentSteps[$place])) {
                return $name;
            }
        }

        // Si aucune place prioritaire, retourner la première place trouvée
        if (!empty($currentSteps)) {
            return array_keys($currentSteps)[0];
        }

        return 'Unknown';
    }

    /**
     * Gère les transitions vers PUR selon la place d'origine
     */
    private function handleTransitionToPur(Document $document, string $oldDocType, string $currentPlace, array &$cleanedFields): void
    {
        switch ($currentPlace) {
            case 'Assembly':
                // Depuis prod assemblage: ASSY -> PUR
                // Nettoyage des données remplies en étape Prod Assembly (sans MOF et pris dans 1 et 2)
                $this->cleanProductionFieldsAssembly($document, $cleanedFields);
                break;

            case 'Molding':
                // Depuis prod moulage: MOLD -> PUR
                // Nettoyage des données remplies en étape Prod Moulage (avec MOF et pris dans 1 et 2)
                $this->cleanProductionFieldsComplete($document, $cleanedFields);
                break;

            case 'Machining':
                // Depuis Prod Usinage: MACH -> PUR
                // Nettoyage des données remplies en étape Prod Usinage (avec MOF et pris dans 1 et 2)
                $this->cleanProductionFieldsComplete($document, $cleanedFields);
                break;
        }
    }

    /**
     * Gère les transitions depuis PUR selon la place d'origine
     */
    private function handleTransitionFromPur(Document $document, string $newDocType, string $currentPlace, array &$cleanedFields): void
    {
        if (in_array($currentPlace, ['Achat_F30', 'Achat_Rfq'])) {
            // Depuis Pris Dans (achat_f30) ou RFQ (achat_rfq)
            // Nettoyage des données remplies en étape RFQ
            $this->cleanRFQFields($document, $cleanedFields);
        }
    }

    /**
     * Nettoie les champs de production pour Assembly (sans MOF, sans pris dans 1 et 2)
     */
    private function cleanProductionFieldsAssembly(Document $document, array &$cleanedFields): void
    {
        // Mat type
        if ($document->getMatProdType() !== null) {
            $document->setMatProdType(null);
            $cleanedFields[] = 'matProdType';
        }

        // Unit
        if ($document->getUnit() !== null) {
            $document->setUnit(null);
            $cleanedFields[] = 'unit';
        }

        // Leadtime
        if ($document->getLeadtime() !== null) {
            $document->setLeadtime(null);
            $cleanedFields[] = 'leadtime';
        }

        // Proc_type
        if ($document->getProcType() !== null) {
            $document->setProcType(null);
            $cleanedFields[] = 'procType';
        }
    }

    /**
     * Nettoie les champs de production complets (avec MOF et pris dans 1 et 2)
     */
    private function cleanProductionFieldsComplete(Document $document, array &$cleanedFields): void
    {
        // Nettoyer les champs de base d'Assembly
        $this->cleanProductionFieldsAssembly($document, $cleanedFields);

        // Pris dans 1 et 2
        if ($document->getPrisDans1() !== null) {
            $document->setPrisDans1(null);
            $cleanedFields[] = 'prisDans1';
        }

        if ($document->getPrisDans2() !== null) {
            $document->setPrisDans2(null);
            $cleanedFields[] = 'prisDans2';
        }

        // MOF
        if ($document->getMof() !== null) {
            $document->setMof(null);
            $cleanedFields[] = 'mof';
        }
    }

    /**
     * Nettoie les champs RFQ (étape Achat)
     */
    private function cleanRFQFields(Document $document, array &$cleanedFields): void
    {
        // Mat type
        if ($document->getMatProdType() !== null) {
            $document->setMatProdType(null);
            $cleanedFields[] = 'matProdType';
        }

        // Unit
        if ($document->getUnit() !== null) {
            $document->setUnit(null);
            $cleanedFields[] = 'unit';
        }

        // Commodity
        if ($document->getCommodityCode() !== null) {
            $document->setCommodityCode(null);
            $cleanedFields[] = 'commodityCode';
        }

        // Buyer (purchasingGroup)
        if ($document->getPurchasingGroup() !== null) {
            $document->setPurchasingGroup(null);
            $cleanedFields[] = 'purchasingGroup';
        }

        // Proc_type
        if ($document->getProcType() !== null) {
            $document->setProcType(null);
            $cleanedFields[] = 'procType';
        }
    }
}

