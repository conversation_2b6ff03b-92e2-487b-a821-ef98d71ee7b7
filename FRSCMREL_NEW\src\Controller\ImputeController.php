<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\HttpFoundation\Request;
use App\Entity\Project;
use App\Entity\User;
use App\Entity\Phase;
use App\Entity\Impute;
use App\Entity\Code;
use App\Repository\ProjectRepository;
use App\Repository\UserRepository;
use App\Repository\CodeRepository;
use App\Repository\ImputeRepository;
use App\Repository\PhaseRepository;
use Doctrine\ORM\EntityManagerInterface;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Spreadsheet;

class ImputeController extends AbstractController
{

    #[Route('/impute/show/{id}', name: 'app_impute_show', methods: ['GET'])]
    public function show($id, EntityManagerInterface $entityManager): JsonResponse
    {
        $impute = $entityManager->getRepository(Impute::class)->find($id);
        return new JsonResponse($impute->toArray());
    }

    // #[Route('/impute/create', name: 'app_impute_create', methods: ['POST'])]
    // public function create(Request $request, EntityManagerInterface $entityManager): JsonResponse
    // {
    //     $data = json_decode($request->getContent(), true);
    //     $impute = new Impute();
    //     $impute->setUser($this->getUser());
    //     $impute->setNbHeures($request->get('nbHeures'));
    //     $impute->setCode($entityManager->getRepository(Code::class)->find($request->get('codeId')));
    //     $impute->setCreatedAt(new \DateTime());
    //     $entityManager->persist($impute);
    //     $entityManager->flush();
    //     return new JsonResponse($impute->toArray());
    // }

    #[Route('/impute/create', name: 'app_impute_create', methods: ['POST'])]
    public function create(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $impute = new Impute();

        // Si l'utilisateur courant est admin, on attend un paramètre "userId"
        if ($this->getUser()->getTitre() === "Chef de Projets" || $this->getUser()->isManager()) {
            $userId = $request->get('userId');
            if (!$userId) {
                return new JsonResponse(['error' => 'Le paramètre userId est requis pour un administrateur'], 400);
            }
            $user = $entityManager->getRepository(User::class)->find($userId);
            if (!$user) {
                return new JsonResponse(['error' => 'Utilisateur invalide'], 400);
            }
            $impute->setUser($user);
        } else {
            // Pour les utilisateurs normaux, l'imputation est liée à l'utilisateur courant
            $impute->setUser($this->getUser());
        }

        $nbHeures = (int) $request->get('nbHeures');
        $impute->setNbHeures($nbHeures);
        $code = $entityManager->getRepository(Code::class)->find($request->get('codeId'));
        if (!$code) {
            return new JsonResponse(['error' => 'Code invalide'], 400);
        }
        $impute->setCode($code);
        $impute->setCreatedAt(new \DateTime());

        $entityManager->persist($impute);
        $entityManager->flush();

        return new JsonResponse($impute->toArray());
}

    #[Route('/impute/{id}', name: 'app_impute_edit', methods: ['PUT'])]
    public function edit($id, Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $impute = $entityManager->getRepository(Impute::class)->find($id);
        if($impute->getUser() !== $this->getUser() && ($this->getUser()->getTitre() !== 'Chef de projets' && $this->getUser()->getTitre() !== 'Cheffe de projets')){
            return new JsonResponse(['error' => 'You can only edit your imputes'], 400);
        }
        $nbHeures = $request->get('nbHeures');
        $impute->setNbHeures($nbHeures);
        $entityManager->flush();
        return new JsonResponse($impute->toArray());
    }

    #[Route('/impute/{id}', name: 'app_impute_delete', methods: ['DELETE'])]
    public function delete($id, EntityManagerInterface $entityManager): JsonResponse
    {
        $impute = $entityManager->getRepository(Impute::class)->find($id);
        if($impute->getUser() !== $this->getUser() && ($this->getUser()->getTitre() !== 'Chef de projets' && $this->getUser()->getTitre() !== 'Cheffe de projets')){
            return new JsonResponse(['error' => 'You can only delete your imputes'], 400);
        }
        $entityManager->remove($impute);
        $entityManager->flush();
        return new JsonResponse(['status' => 'Impute deleted']);
    }

    #[Route('/impute/filtered', name: 'app_impute_filtered', methods: ['GET'])]
    public function filtered(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $project = $request->get('projet');
        $user = $request->get('username');
        $code = $request->get('code');
        $phase = $request->get('phase');
        $yearMonth = $request->get('periode');
        $year = $request->get('periode');
        $workCenter = $request->get('workCenter');
        $ci = $request->get('ci');
        $sap = $request->get('sap');

        $imputes = $entityManager->getRepository(Impute::class)->findByFilters($project, $user, $code, $phase, $yearMonth, $year, $workCenter, $ci, $sap);

        $imputes = array_map(function($imputation){
            return $imputation->toArray();
        }, $imputes);
        
        return new JsonResponse([
            "imputations" => $imputes
        ]);
    }
    #[Route('/impute/export', name: 'app_impute_export', methods: ['GET'])]
    public function export(Request $request, EntityManagerInterface $entityManager): Response
    {
        $project = $request->get('projet');
        $user = $request->get('username');
        $code = $request->get('code');
        $phase = $request->get('phase');
        $yearMonth = $request->get('periode');
        $year = $request->get('periode');
        $workCenter = $request->get('workCenter');
        $ci = $request->get('ci');
        $sap = $request->get('sap');

        $imputes = $entityManager->getRepository(Impute::class)->findByFilters($project, $user, $code, $phase, $yearMonth, $year, $workCenter, $ci, $sap);

        $imputes = array_map(function($imputation){
            return $imputation->toArray();
        }, $imputes);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Period');
        $sheet->setCellValue('B1', 'Workcenter');
        $sheet->setCellValue('C1', 'Project_Code');
        $sheet->setCellValue('D1', 'Nom');
        $sheet->setCellValue('E1', 'Prénom');
        $sheet->setCellValue('F1', 'Department');
        $sheet->setCellValue('G1', 'Fiscal_Year');
        $sheet->setCellValue('H1', 'Project_Title');
        $sheet->setCellValue('I1', 'Project_Phase');
        $sheet->setCellValue('J1', 'Time');
        $sheet->setCellValue('K1', 'CIR');
        $sheet->setCellValue('L1', 'SAP');
        $sheet->setCellValue('M1', 'Timestamp');

        $row = 2;

        foreach($imputes as $impute){
            $sheet->setCellValue('A'.$row, $impute['periode']);
            $sheet->setCellValue('B'.$row, $impute['code']['workCenter']);
            $sheet->setCellValue('C'.$row, $impute['code']['code']);
            $sheet->setCellValue('D'.$row, $impute['nom']);
            $sheet->setCellValue('E'.$row, $impute['prenom']);
            $sheet->setCellValue('F'.$row, $impute['department']);
            $sheet->setCellValue('G'.$row, $impute['year']);
            $sheet->setCellValue('H'.$row, $impute['code']['projet']);
            $sheet->setCellValue('I'.$row, $impute['code']['phase']);
            $sheet->setCellValue('J'.$row, $impute['nbHeures']);
            $sheet->setCellValue('K'.$row, $impute['userObject']['ci'] ? 1 : 0);
            $sheet->setCellValue('L'.$row, $impute['userObject']['sap'] ? 1 : 0);
            $sheet->setCellValue('M'.$row, $impute['createdAt']);
            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'imputations.xlsx';
        $writer->save($filename);

        return $this->file($filename);
    }

    #[Route('/impute/export_sap', name: 'app_impute_export_sap', methods: ['GET'])]
    public function exportSap(Request $request, EntityManagerInterface $entityManager): Response
    {
        $project = $request->get('projet');
        $user = $request->get('username');
        $code = $request->get('code');
        $phase = $request->get('phase');
        $yearMonth = $request->get('periode');
        $year = $request->get('periode');
        $workCenter = $request->get('workCenter');
        $ci = $request->get('ci');
        $sap = $request->get('sap');

        $imputes = $entityManager->getRepository(Impute::class)->findByFilters($project, $user, $code, $phase, $yearMonth, $year, $workCenter, $ci, $sap);

        $imputes = array_map(function($imputation){
            return $imputation->toArray();
        }, $imputes);

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('A1', 'Network');
        $sheet->setCellValue('B1', 'Act.');
        $sheet->setCellValue('C1', 'Element');
        $sheet->setCellValue('D1', 'Cat');
        $sheet->setCellValue('E1', 'Spl');
        $sheet->setCellValue('F1', 'Pers. No.');
        $sheet->setCellValue('G1', 'WT');
        $sheet->setCellValue('H1', 'Workcenter');
        $sheet->setCellValue('I1', 'Plant');
        $sheet->setCellValue('J1', 'ActTyp');
        $sheet->setCellValue('K1', 'CM');
        $sheet->setCellValue('L1', 'Postg date');
        $sheet->setCellValue('M1', 'Bus. Process');
        $sheet->setCellValue('N1', 'Process Quantity');
        $sheet->setCellValue('O1', 'Unit');
        $sheet->setCellValue('P1', 'BusProc');
        $sheet->setCellValue('Q1', 'Unit');
        $sheet->setCellValue('R1', 'R');
        $sheet->setCellValue('S1', 'Caus');
        $sheet->setCellValue('T1', 'Prc');
        $sheet->setCellValue('U1', 'Act.Work');

        $row = 2;

        foreach($imputes as $impute){
            $codeParts = explode(' ', $impute['code']['code']);
            $network = $codeParts[0] ?? '';
            $act = $codeParts[1] ?? '';
            $element = $codeParts[2] ?? '';

            $sheet->setCellValue('A' . $row, $network);
            $sheet->setCellValue('B' . $row, $act);
            $sheet->setCellValue('C' . $row, $element);
            
            // The columns Cat, Spl, Pers. No., WT, Workcenter, Plant, ActTyp, CM, Postg date, Bus. Process,
            // Process Quantity, Unit, BusProc, Unit, R, Caus, Prc are left empty
            foreach (range('D', 'T') as $col) {
                $sheet->setCellValue($col . $row, '');
            }

            $sheet->setCellValue('U' . $row, $impute['nbHeures']);
            $row++;
        }

        $writer = new Xlsx($spreadsheet);
        $filename = 'imputations_sap.xlsx';
        $writer->save($filename);

        return $this->file($filename);
    }


    #[Route('/impute', name: 'app_impute_index', methods: ['GET'])]
    public function index(EntityManagerInterface $entityManager): Response
    {
       return $this->render('imputation/extract.html.twig');
    }

    //ajax get all users with imputation true
    #[Route('/impute/users', name: 'app_impute_users', methods: ['GET'])]
    public function users(EntityManagerInterface $entityManager): JsonResponse
    {
        $users = $entityManager->getRepository(User::class)->findBy(['imputation' => true], ['nom' => 'ASC']);
        $users = array_map(function($user){
            return $user->toArray();
        }, $users);
        return new JsonResponse($users);
    }

    //ajax get all users with imputation true for an manager
    #[Route('/impute/users/manager', name: 'app_impute_users_manager', methods: ['GET'])]
    public function usersForManager(EntityManagerInterface $entityManager): JsonResponse
    {
        $users = $entityManager->getRepository(User::class)->findBy([
            'imputation' => true, 
            'manager' => $this->getUser()->getNom() . ' ' . $this->getUser()->getPrenom()
        ], ['nom' => 'ASC']); // Tri par ordre alphabétique sur le champ 'nom'

        $users = array_map(function($user){
            return $user->toArray();
        }, $users);

        return new JsonResponse($users);
    }

    //ajax get all code
    #[Route('/impute/codes', name: 'app_impute_codes', methods: ['GET'])]
    public function codes(EntityManagerInterface $entityManager): JsonResponse
    {
        $codes = $entityManager->getRepository(Code::class)->findAll();
        $uniqueWorkCenters = [];

        $codes = array_filter($codes, function($code) use (&$uniqueWorkCenters) {
            $workCenter = $code->getWorkCenter();
            if (!in_array($workCenter, $uniqueWorkCenters)) {
            $uniqueWorkCenters[] = $workCenter;
            return true;
            }
            return false;
        });

        $codes = array_map(function($code){
            return $code->toArray();
        }, $codes);
        
        // Réindexation du tableau
        $codes = array_values($codes);
        
        return new JsonResponse($codes);
            }

    #[Route('/impute/analyse', name: 'app_impute_analyse', methods: ['GET'])]
    public function analyse(EntityManagerInterface $entityManager): Response
    {
        $projects = $entityManager->getRepository(Project::class)->findAll();

        $users = $entityManager->getRepository(User::class)->findAll();
        $users = array_filter($users, function($user) {
            return $user->hasImpute();
        });
        
        $codes = $entityManager->getRepository(Code::class)->findAll();
        $phases = $entityManager->getRepository(Phase::class)->findAll();
        return $this->render('imputation/analyse.html.twig', [
            'projects' => $projects,
            'users' => $users,
            'codes' => $codes,
            'phases' => $phases
        ]);
    }

    #[Route('/impute/data/code', name: 'app_impute_data_code', methods: ['GET'])]
    public function getDataByCode(Request $request, ImputeRepository $imputeRepository): JsonResponse
    {
        $startDate = $request->query->get('start_date', '2019-01-01');
        $endDate   = $request->query->get('end_date', '2026-01-01');
        $projectId = $request->query->get('project_id', null);
        $projectId = $projectId !== null ? (int) $projectId : null;
    
        $data = $imputeRepository->getTotalHeuresByCode($startDate, $endDate, $projectId);
    
        return $this->json($data);
    }
    
    #[Route('/impute/data/month', name: 'app_impute_data_month', methods: ['GET'])]
    public function getDataByMonth(Request $request, ImputeRepository $imputeRepository): JsonResponse
    {
        $startDate = $request->query->get('start_date', '2019-01-01');
        $endDate   = $request->query->get('end_date', '2026-01-01');
        $projectId = $request->query->get('project_id', null);
        $projectId = $projectId !== null ? (int) $projectId : null;
    
        $data = $imputeRepository->getTotalHeuresByMonth($startDate, $endDate, $projectId);
    
        return $this->json($data);
    }
    
    #[Route('/impute/data/month/user', name: 'app_impute_data_month_user', methods: ['GET'])]
    public function getDataByMonthForUser(Request $request, ImputeRepository $imputeRepository): JsonResponse
    {
        $startDate = $request->query->get('start_date', '2019-01-01');
        $endDate   = $request->query->get('end_date', '2026-01-01');
        $userId    = $request->query->get('user_id');
    
        if (!$userId) {
            return $this->json(['error' => 'Le paramètre user_id est requis.'], 400);
        }
    
        $projectId = $request->query->get('project_id', null);
        $projectId = $projectId !== null ? (int)$projectId : null;
    
        $data = $imputeRepository->getTotalHeuresByMonthForUser($startDate, $endDate, (int)$userId, $projectId);
    
        return $this->json($data);
    }


    #[Route('/impute/read', name: 'app_impute_read', methods: ['GET'])]
    public function read(ProjectRepository $projectRepository, EntityManagerInterface $entityManager): Response
    {
        $user = $this->getUser();
        // Pour afficher le champ "Projet" pour tous, on récupère tous les projets
        $projects = $projectRepository->findAll();
        
        // Pour le filtrage :
        $managerFilter = null;
        $userFilter = null;
        if ($user->isManager()) {
            $managerFilter = $user->getNom() . ' ' . $user->getPrenom();
        }
        // Pour un utilisateur normal (ni manager ni chef de projets), forcer le filtre par son identifiant
        elseif (!in_array($user->getTitre(), ['Chef de Projets', 'Cheffe de Projets'])) {
            $userFilter = $user->getId();
        }
        
        $imputes = $entityManager->getRepository(Impute::class)
            ->findByFilters2(null, $userFilter, null, null, null, null, null, null, null, $managerFilter);
        
        $imputations = array_map(fn($imputation) => $imputation->toArrayLight(), $imputes);

        return $this->render('imputation/read.html.twig', [
            'projects' => $projects,
            'imputations' => $imputations,
        ]);
    }
    
    // Route AJAX de filtrage dynamique
    #[Route('/impute/filtre', name: 'app_impute_filtered2', methods: ['GET'])]
    public function filtre(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        // Récupération des filtres de la requête
        $project    = $request->get('projet');
        // Pour le champ "username", pour un utilisateur normal, nous ignorons la saisie et forçons son identifiant
        $username   = $request->get('username');
        $code       = $request->get('code');
        $phase      = $request->get('phase');
        $periode    = $request->get('periode');
        $workCenter = $request->get('workCenter');
        $ci         = $request->get('ci');
        $sap        = $request->get('sap');
    
        $currentUser = $this->getUser();
        $managerFilter = null;
        $userFilter = null;
        if ($currentUser->isManager()) {
            $managerFilter = $currentUser->getNom() . ' ' . $currentUser->getPrenom();
        }
        // Pour un utilisateur normal, forcer le filtre sur son identifiant
        elseif (!in_array($currentUser->getTitre(), ['Chef de Projets', 'Cheffe de Projets'])) {
            $userFilter = $currentUser->getId();
        }
    
        // Utilisation du filtre sur "user" : si userFilter est défini, on l'utilise, sinon on garde la saisie (pour managers)
        $imputes = $entityManager->getRepository(Impute::class)
            ->findByFilters2($project, $userFilter ?: $username, $code, $phase, $periode, null, $workCenter, $ci, $sap, $managerFilter);
    
        $imputations = array_map(fn($imputation) => $imputation->toArrayLight(), $imputes);
        
        return new JsonResponse(["imputations" => $imputations]);
    }


    #[Route('/impute/admin/{id}', name: 'app_impute_admin_delete', methods: ['DELETE'])]
    public function deleteAdmin($id, EntityManagerInterface $entityManager): JsonResponse
    {
        $currentUser = $this->getUser();
        $impute = $entityManager->getRepository(Impute::class)->find($id);
        if(in_array($currentUser->getTitre(), ['Chef de Projets', 'Cheffe de Projets'])){
            $entityManager->remove($impute);
            $entityManager->flush();
            return new JsonResponse(['status' => 'Impute deleted']);
        }
        return new JsonResponse(['error' => 'You can only delete your imputes'], 400);
    }



}
