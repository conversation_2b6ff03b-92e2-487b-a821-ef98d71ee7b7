<?php

namespace App\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use App\Entity\Project;
use App\Repository\ProjectRepository;
use App\Entity\User;
use App\Repository\UserRepository;
use App\Entity\Phase;
use App\Entity\Impute;
use App\Repository\ImputeRepository;
use App\Repository\PhaseRepository;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;

class PhaseController extends AbstractController
{

    #[Route('/phases/codes', name: 'app_projet_codes', methods: ['POST'])]
    public function getCodes(Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $phase_id = $request->get('phase_id');
        $phase = $entityManager->getRepository(Phase::class)->find($phase_id);
        $codes = $phase->getCodes()->toArray();
        $codes = array_map(function($code){
            return $code->toArray();
        }, $codes);
        return new JsonResponse(['codes' => $codes]);
    }

    #[Route('/phases/edit/commentaire/{id}', name: 'app_phase_edit_commentaire', methods: ['PUT'])]
    public function editCommentaire($id, Request $request, EntityManagerInterface $entityManager): JsonResponse
    {
        $phase = $entityManager->getRepository(Phase::class)->find($id);
        $phase->setCommentaire($request->get('commentaire'));
        $entityManager->persist($phase);
        $entityManager->flush();
        return new JsonResponse(['status' => 'success']);
    }

    #[Route('/phases/edit/manualclose/{id}', name: 'app_phase_manual_close', methods: ['PUT'])]
    public function manualClose($id, EntityManagerInterface $entityManager): JsonResponse
    {
        $phase = $entityManager->getRepository(Phase::class)->find($id);
        $phase->setStatusManuel($phase->isStatusManuel() === null ? false : !$phase->isStatusManuel());
        $entityManager->persist($phase);
        $entityManager->flush();
        return new JsonResponse(['status' => 'success']);
    }
}
