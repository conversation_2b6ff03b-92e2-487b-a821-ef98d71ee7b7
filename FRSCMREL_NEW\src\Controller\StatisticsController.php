<?php

namespace App\Controller;

use App\Repository\DocumentRepository;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

#[Route('/statistics')]
class StatisticsController extends AbstractController
{
    private DocumentRepository $documentRepository;

    public function __construct(DocumentRepository $documentRepository)
    {
        $this->documentRepository = $documentRepository;
    }

    #[Route('/', name: 'statistics_dashboard')]
    public function dashboard(): Response
    {
        return $this->render('statistics/dashboard.html.twig');
    }

    #[Route('/workflow-time', name: 'statistics_workflow_time')]
    public function workflowTime(): JsonResponse
    {
        try {
            $data = $this->documentRepository->getAverageTimePerWorkflowStep();
            
            return new JsonResponse([
                'success' => true,
                'data' => $data,
                'message' => 'Temps moyen par étape du workflow récupéré avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération des temps de workflow'
            ], 500);
        }
    }

    #[Route('/cycle-time', name: 'statistics_cycle_time')]
    public function cycleTime(): JsonResponse
    {
        try {
            $data = $this->documentRepository->getCycleTimeStatistics();
            
            return new JsonResponse([
                'success' => true,
                'data' => $data,
                'message' => 'Statistiques de temps de cycle récupérées avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération des temps de cycle'
            ], 500);
        }
    }

    #[Route('/visa-statistics', name: 'statistics_visa')]
    public function visaStatistics(): JsonResponse
    {
        try {
            $data = $this->documentRepository->getVisaStatisticsByStep();
            
            return new JsonResponse([
                'success' => true,
                'data' => $data,
                'message' => 'Statistiques des visas récupérées avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération des statistiques de visas'
            ], 500);
        }
    }

    #[Route('/pending-visas', name: 'statistics_pending_visas')]
    public function pendingVisas(Request $request): JsonResponse
    {
        try {
            $userId = $request->query->get('user_id');
            $data = $this->documentRepository->getDocumentsPendingVisaByUser($userId ? (int)$userId : null);
            
            return new JsonResponse([
                'success' => true,
                'data' => $data,
                'message' => 'Documents en attente de visa récupérés avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération des documents en attente'
            ], 500);
        }
    }

    #[Route('/validator-performance', name: 'statistics_validator_performance')]
    public function validatorPerformance(): JsonResponse
    {
        try {
            $data = $this->documentRepository->getValidatorPerformanceStatistics();
            
            return new JsonResponse([
                'success' => true,
                'data' => $data,
                'message' => 'Performance des validateurs récupérée avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération de la performance des validateurs'
            ], 500);
        }
    }

    #[Route('/document-types', name: 'statistics_document_types')]
    public function documentTypes(): JsonResponse
    {
        try {
            $data = $this->documentRepository->getDetailedDocumentTypeDistribution();
            
            return new JsonResponse([
                'success' => true,
                'data' => $data,
                'message' => 'Distribution des types de documents récupérée avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération de la distribution des types'
            ], 500);
        }
    }

    #[Route('/project-dmo-distribution', name: 'statistics_project_dmo')]
    public function projectDmoDistribution(): JsonResponse
    {
        try {
            $data = $this->documentRepository->getDocumentDistributionByProjectAndDMO();
            
            return new JsonResponse([
                'success' => true,
                'data' => $data,
                'message' => 'Distribution par projet/DMO récupérée avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération de la distribution projet/DMO'
            ], 500);
        }
    }

    #[Route('/creation-trends', name: 'statistics_creation_trends')]
    public function creationTrends(Request $request): JsonResponse
    {
        try {
            $months = $request->query->get('months', 12);
            $data = $this->documentRepository->getDocumentCreationTrends((int)$months);
            
            return new JsonResponse([
                'success' => true,
                'data' => $data,
                'message' => 'Tendances de création récupérées avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération des tendances de création'
            ], 500);
        }
    }

    #[Route('/summary', name: 'statistics_summary')]
    public function summary(): JsonResponse
    {
        try {
            $summary = [
                'workflow_time' => $this->documentRepository->getAverageTimePerWorkflowStep(),
                'cycle_time' => $this->documentRepository->getCycleTimeStatistics(),
                'visa_stats' => $this->documentRepository->getVisaStatisticsByStep(),
                'document_types' => $this->documentRepository->getDetailedDocumentTypeDistribution(),
                'project_dmo' => $this->documentRepository->getDocumentDistributionByProjectAndDMO(),
                'creation_trends' => $this->documentRepository->getDocumentCreationTrends(6), // 6 derniers mois
                'validator_performance' => $this->documentRepository->getValidatorPerformanceStatistics()
            ];
            
            return new JsonResponse([
                'success' => true,
                'data' => $summary,
                'message' => 'Résumé des statistiques récupéré avec succès'
            ]);
        } catch (\Exception $e) {
            return new JsonResponse([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Erreur lors de la récupération du résumé des statistiques'
            ], 500);
        }
    }
}
