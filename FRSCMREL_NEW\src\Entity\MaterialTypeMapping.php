<?php

namespace App\Entity;

use App\Repository\MaterialTypeMappingRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: MaterialTypeMappingRepository::class)]
class MaterialTypeMapping
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255, unique: true)]
    private ?string $displayLabel = null;

    #[ORM\Column(length: 10)]
    private ?string $sapCode = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $description = null;

    #[ORM\Column]
    private ?bool $isActive = true;

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getDisplayLabel(): ?string
    {
        return $this->displayLabel;
    }

    public function setDisplayLabel(string $displayLabel): static
    {
        $this->displayLabel = $displayLabel;
        return $this;
    }

    public function getSapCode(): ?string
    {
        return $this->sapCode;
    }

    public function setSapCode(string $sapCode): static
    {
        $this->sapCode = $sapCode;
        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): static
    {
        $this->description = $description;
        return $this;
    }

    public function getIsActive(): ?bool
    {
        return $this->isActive;
    }

    public function setIsActive(bool $isActive): static
    {
        $this->isActive = $isActive;
        return $this;
    }

    public function __toString(): string
    {
        return $this->displayLabel . ' (' . $this->sapCode . ')';
    }
}
