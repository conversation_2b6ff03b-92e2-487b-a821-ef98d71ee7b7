<?php

namespace App\Entity;

use App\Repository\PhaseRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: PhaseRepository::class)]
class Phase
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 255)]
    private ?string $title = null;

    #[ORM\Column(length: 255)]
    private ?string $code = null;

    #[ORM\Column(nullable: true)]
    private ?int $level = null;

    #[ORM\Column]
    private ?bool $status = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $statusTxt = null;

    #[ORM\Column(nullable: true)]
    private ?bool $statusManuel = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $commentaire = null;

    #[ORM\ManyToOne(inversedBy: 'phases')]
    #[ORM\JoinColumn(nullable: false)]
    private ?Project $projet = null;

    /**
     * @var Collection<int, Code>
     */
    #[ORM\OneToMany(targetEntity: Code::class, mappedBy: 'phase', orphanRemoval: true)]
    private Collection $codes;

    public function __construct()
    {
        $this->codes = new ArrayCollection();
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function getTitle(): ?string
    {
        return $this->title;
    }

    public function setTitle(string $title): static
    {
        $this->title = $title;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(string $code): static
    {
        $this->code = $code;

        return $this;
    }

    public function getLevel(): ?int
    {
        return $this->level;
    }

    public function setLevel(?int $level): static
    {
        $this->level = $level;

        return $this;
    }

    public function isStatus(): ?bool
    {
        return $this->status;
    }

    public function setStatus(bool $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getStatusTxt(): ?string
    {
        return $this->statusTxt;
    }

    public function setStatusTxt(?string $statusTxt): static
    {
        $this->statusTxt = $statusTxt;

        return $this;
    }

    public function isStatusManuel(): ?bool
    {
        return $this->statusManuel;
    }

    public function setStatusManuel(?bool $statusManuel): static
    {
        $this->statusManuel = $statusManuel;

        return $this;
    }

    public function getCommentaire(): ?string
    {
        return $this->commentaire;
    }

    public function setCommentaire(?string $commentaire): static
    {
        $this->commentaire = $commentaire;

        return $this;
    }

    public function getProjet(): ?Project
    {
        return $this->projet;
    }

    public function setProjet(?Project $projet): static
    {
        $this->projet = $projet;

        return $this;
    }

    /**
     * @return Collection<int, Code>
     */
    public function getCodes(): Collection
    {
        return $this->codes;
    }

    public function addCode(Code $code): static
    {
        if (!$this->codes->contains($code)) {
            $this->codes->add($code);
            $code->setPhase($this);
        }

        return $this;
    }

    public function removeCode(Code $code): static
    {
        if ($this->codes->removeElement($code)) {
            // set the owning side to null (unless already changed)
            if ($code->getPhase() === $this) {
                $code->setPhase(null);
            }
        }

        return $this;
    }

    public function isOpen(): bool
    {
        return $this->isStatus() && ($this->isStatusManuel() === null || $this->isStatusManuel());
    }

    //toArray
    public function toArray(): array
    {
        return [
            'id' => $this->getId(),
            'title' => $this->getTitle(),
            'code' => $this->getCode(),
            'level' => $this->getLevel(),
            'status' => $this->isStatus(),
            'statusTxt' => $this->getStatusTxt(),
            'statusManuel' => $this->isStatusManuel(),
            'commentaire' => $this->getCommentaire(),
            'codes' => $this->codes->map(fn (Code $code) => $code->toArray())->toArray(),
        ];
    }
}
