<?php

namespace App\Entity;

use App\Repository\UserRepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

#[ORM\Entity(repositoryClass: UserRepository::class)]
#[ORM\UniqueConstraint(name: 'UNIQ_IDENTIFIER_EMAIL', fields: ['email'])]
class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column(length: 180)]
    private ?string $email = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $username = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $nom = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $prenom = null;

    /**
     * @var list<string> The user roles
     */
    #[ORM\Column]
    private array $roles = [];

    /**
     * @var string The hashed password
     */
    #[ORM\Column]
    private ?string $password = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $manager = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $titre = null;

    #[ORM\Column(nullable: true)]
    private ?bool $isManager = null;

    #[ORM\Column(length: 255)]
    private ?string $departement = null;

    /**
     * @var Collection<int, DocumentStatusHistory>
     */
    #[ORM\OneToMany(targetEntity: DocumentStatusHistory::class, mappedBy: 'performedBy')]
    private Collection $documentStatusHistories;

    /**
     * @var Collection<int, Visa>
     */
    #[ORM\OneToMany(targetEntity: Visa::class, mappedBy: 'validator')]
    private Collection $visas;

    /**
     * @var Collection<int, ReleasedPackage>
     */
    #[ORM\OneToMany(targetEntity: ReleasedPackage::class, mappedBy: 'owner')]
    private Collection $releasedPackages;

    /**
     * @var Collection<int, Commentaire>
     */
    #[ORM\OneToMany(targetEntity: Commentaire::class, mappedBy: 'user')]
    private Collection $commentaires;

    /**
     * @var Collection<int, ReleasedPackage>
     */
    #[ORM\OneToMany(targetEntity: ReleasedPackage::class, mappedBy: 'verif')]
    private Collection $verifPackages;

    /**
     * @var Collection<int, ReleasedPackage>
     */
    #[ORM\OneToMany(targetEntity: ReleasedPackage::class, mappedBy: 'valid')]
    private Collection $validPackages;

    /**
     * @var Collection<int, Document>
     */
    #[ORM\OneToMany(targetEntity: Document::class, mappedBy: 'superviseur')]
    private Collection $documents;

    /**
     * @var Collection<int, DMO>
     */
    #[ORM\OneToMany(targetEntity: DMO::class, mappedBy: 'requestor')]
    private Collection $dMOs;

    /**
     * @var Collection<int, DMO>
     */
    #[ORM\OneToMany(targetEntity: DMO::class, mappedBy: 'Eng_Owner')]
    private Collection $dMos_Eng_Owner;

    /**
     * @var Collection<int, DMO>
     */
    #[ORM\OneToMany(targetEntity: DMO::class, mappedBy: 'last_Modificator')]
    private Collection $Last_Modificator_Dmos;

    /**
     * @var Collection<int, Project>
     */
    #[ORM\OneToMany(targetEntity: Project::class, mappedBy: 'ProjectManager')]
    private Collection $projects;

    /**
     * @var Collection<int, Impute>
     */
    #[ORM\OneToMany(targetEntity: Impute::class, mappedBy: 'user', orphanRemoval: true)]
    private Collection $imputes;

    #[ORM\Column(nullable: true)]
    private ?bool $imputation = null;

    #[ORM\Column(nullable: true)]
    private ?bool $ci = null;

    #[ORM\Column(nullable: true)]
    private ?bool $sap = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $workCenter = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $codeGestionnaire = null;

    /**
     * @var array<string> Les places gérées par l'utilisateur
     */
    #[ORM\Column(nullable: true)]
    private ?array $managedPlaces = [];

    /**
     * @var Collection<int, UserPreference>
     */
    #[ORM\OneToMany(targetEntity: UserPreference::class, mappedBy: 'user', orphanRemoval: true)]
    private Collection $preferences;



    #[ORM\Column(type: 'datetime', nullable: true)]
    private ?\DateTime $lastLdapSync = null;

    /**
     * @var Collection<int, Document>
     */
    #[ORM\OneToMany(targetEntity: Document::class, mappedBy: 'qualOwner')]
    private Collection $qualDocuments;

    public function __construct()
    {
        $this->documentStatusHistories = new ArrayCollection();
        $this->visas = new ArrayCollection();
        $this->releasedPackages = new ArrayCollection();
        $this->commentaires = new ArrayCollection();
        $this->verifPackages = new ArrayCollection();
        $this->validPackages = new ArrayCollection();
        $this->documents = new ArrayCollection();
        $this->dMOs = new ArrayCollection();
        $this->dMos_Eng_Owner = new ArrayCollection();
        $this->Last_Modificator_Dmos = new ArrayCollection();
        $this->projects = new ArrayCollection();
        $this->imputes = new ArrayCollection();
        $this->preferences = new ArrayCollection();
        $this->qualDocuments = new ArrayCollection();

    }

    /**
     * @return Collection<int, UserPreference>
     */
    public function getPreferences(): Collection
    {
        return $this->preferences;
    }

    public function addPreference(UserPreference $preference): static
    {
        if (!$this->preferences->contains($preference)) {
            $this->preferences->add($preference);
            $preference->setUser($this);
        }

        return $this;
    }

    public function removePreference(UserPreference $preference): static
    {
        if ($this->preferences->removeElement($preference)) {
            // set the owning side to null (unless already changed)
            if ($preference->getUser() === $this) {
                $preference->setUser(null);
            }
        }

        return $this;
    }



    public function getId(): ?int
    {
        return $this->id;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): static
    {
        $this->email = $email;

        return $this;
    }

    public function getUsername(): ?string
    {
        return $this->username;
    }

    public function setUsername(string $username): static
    {
        $this->username = $username;

        return $this;
    }

    public function getNom(): ?string
    {
        return $this->nom;
    }

    public function setNom(string $nom): static
    {
        $this->nom = $nom;

        return $this;
    }

    public function getPrenom(): ?string
    {
        return $this->prenom;
    }

    public function setPrenom(string $prenom): static
    {
        $this->prenom = $prenom;

        return $this;
    }

    /**
     * A visual identifier that represents this user.
     *
     * @see UserInterface
     */
    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    /**
     * @see UserInterface
     *
     * @return list<string>
     */
    public function getRoles(): array
    {
        $roles = $this->roles;
        // guarantee every user at least has ROLE_USER
        $roles[] = 'ROLE_USER';

        return array_unique($roles);
    }

    /**
     * @param list<string> $roles
     */
    public function setRoles(array $roles): static
    {
        $this->roles = $roles;

        return $this;
    }

    /**
     * @see PasswordAuthenticatedUserInterface
     */
    public function getPassword(): ?string
    {
        return $this->password;
    }

    public function setPassword(string $password): static
    {
        $this->password = $password;

        return $this;
    }

    /**
     * @see UserInterface
     */
    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
        // $this->plainPassword = null;
    }

    /**
     * @return Collection<int, DocumentStatusHistory>
     */
    public function getDocumentStatusHistories(): Collection
    {
        return $this->documentStatusHistories;
    }

    public function addDocumentStatusHistory(DocumentStatusHistory $documentStatusHistory): static
    {
        if (!$this->documentStatusHistories->contains($documentStatusHistory)) {
            $this->documentStatusHistories->add($documentStatusHistory);
            $documentStatusHistory->setPerformedBy($this);
        }

        return $this;
    }

    public function removeDocumentStatusHistory(DocumentStatusHistory $documentStatusHistory): static
    {
        if ($this->documentStatusHistories->removeElement($documentStatusHistory)) {
            // set the owning side to null (unless already changed)
            if ($documentStatusHistory->getPerformedBy() === $this) {
                $documentStatusHistory->setPerformedBy(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Visa>
     */
    public function getVisas(): Collection
    {
        return $this->visas;
    }

    public function addVisa(Visa $visa): static
    {
        if (!$this->visas->contains($visa)) {
            $this->visas->add($visa);
            $visa->setValidator($this);
        }

        return $this;
    }

    public function removeVisa(Visa $visa): static
    {
        if ($this->visas->removeElement($visa)) {
            // set the owning side to null (unless already changed)
            if ($visa->getValidator() === $this) {
                $visa->setValidator(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ReleasedPackage>
     */
    public function getReleasedPackages(): Collection
    {
        return $this->releasedPackages;
    }

    public function addReleasedPackage(ReleasedPackage $releasedPackage): static
    {
        if (!$this->releasedPackages->contains($releasedPackage)) {
            $this->releasedPackages->add($releasedPackage);
            $releasedPackage->setOwner($this);
        }

        return $this;
    }

    public function removeReleasedPackage(ReleasedPackage $releasedPackage): static
    {
        if ($this->releasedPackages->removeElement($releasedPackage)) {
            // set the owning side to null (unless already changed)
            if ($releasedPackage->getOwner() === $this) {
                $releasedPackage->setOwner(null);
            }
        }

        return $this;
    }

    public function __toString(): string
    {
        $prenom0 = $this->prenom[0] ?? '';
        return strtoupper($prenom0 . '.' . $this->nom);
    }

    /**
     * @return Collection<int, Commentaire>
     */
    public function getCommentaires(): Collection
    {
        return $this->commentaires;
    }

    public function addCommentaire(Commentaire $commentaire): static
    {
        if (!$this->commentaires->contains($commentaire)) {
            $this->commentaires->add($commentaire);
            $commentaire->setUser($this);
        }

        return $this;
    }

    public function removeCommentaire(Commentaire $commentaire): static
    {
        if ($this->commentaires->removeElement($commentaire)) {
            // set the owning side to null (unless already changed)
            if ($commentaire->getUser() === $this) {
                $commentaire->setUser(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ReleasedPackage>
     */
    public function getVerifPackages(): Collection
    {
        return $this->verifPackages;
    }

    public function addVerifPackage(ReleasedPackage $verifPackage): static
    {
        if (!$this->verifPackages->contains($verifPackage)) {
            $this->verifPackages->add($verifPackage);
            $verifPackage->setVerif($this);
        }

        return $this;
    }

    public function removeVerifPackage(ReleasedPackage $verifPackage): static
    {
        if ($this->verifPackages->removeElement($verifPackage)) {
            // set the owning side to null (unless already changed)
            if ($verifPackage->getVerif() === $this) {
                $verifPackage->setVerif(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, ReleasedPackage>
     */
    public function getValidPackages(): Collection
    {
        return $this->validPackages;
    }

    public function addValidPackage(ReleasedPackage $validPackage): static
    {
        if (!$this->validPackages->contains($validPackage)) {
            $this->validPackages->add($validPackage);
            $validPackage->setValid($this);
        }

        return $this;
    }

    public function removeValidPackage(ReleasedPackage $validPackage): static
    {
        if ($this->validPackages->removeElement($validPackage)) {
            // set the owning side to null (unless already changed)
            if ($validPackage->getValid() === $this) {
                $validPackage->setValid(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Document>
     */
    public function getDocuments(): Collection
    {
        return $this->documents;
    }

    public function addDocument(Document $document): static
    {
        if (!$this->documents->contains($document)) {
            $this->documents->add($document);
            $document->setSuperviseur($this);
        }

        return $this;
    }

    public function removeDocument(Document $document): static
    {
        if ($this->documents->removeElement($document)) {
            // set the owning side to null (unless already changed)
            if ($document->getSuperviseur() === $this) {
                $document->setSuperviseur(null);
            }
        }

        return $this;
    }

    public function getManager(): ?string
    {
        return $this->manager;
    }

    public function setManager(?string $manager): static
    {
        $this->manager = $manager;
        return $this;
    }

    public function getTitre(): ?string
    {
        return $this->titre;
    }

    public function setTitre(?string $titre): static
    {
        $this->titre = $titre;
        return $this;
    }

    public function isManager(): ?bool
    {
        return $this->isManager;
    }

    public function setIsManager(?bool $isManager): static
    {
        $this->isManager = $isManager;
        return $this;
    }

    public function getDepartement(): ?string
    {
        return $this->departement;
    }

    public function setDepartement(?string $departement): static
    {
        $this->departement = $departement;
        return $this;
    }

    /**
     * @return Collection<int, DMO>
     */
    public function getDMOs(): Collection
    {
        return $this->dMOs;
    }

    public function addDMO(DMO $dMO): static
    {
        if (!$this->dMOs->contains($dMO)) {
            $this->dMOs->add($dMO);
            $dMO->setRequestor($this);
        }

        return $this;
    }

    public function removeDMO(DMO $dMO): static
    {
        if ($this->dMOs->removeElement($dMO)) {
            // set the owning side to null (unless already changed)
            if ($dMO->getRequestor() === $this) {
                $dMO->setRequestor(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, DMO>
     */
    public function getDMosEngOwner(): Collection
    {
        return $this->dMos_Eng_Owner;
    }

    public function addDMosEngOwner(DMO $dMosEngOwner): static
    {
        if (!$this->dMos_Eng_Owner->contains($dMosEngOwner)) {
            $this->dMos_Eng_Owner->add($dMosEngOwner);
            $dMosEngOwner->setEngOwner($this);
        }

        return $this;
    }

    public function removeDMosEngOwner(DMO $dMosEngOwner): static
    {
        if ($this->dMos_Eng_Owner->removeElement($dMosEngOwner)) {
            // set the owning side to null (unless already changed)
            if ($dMosEngOwner->getEngOwner() === $this) {
                $dMosEngOwner->setEngOwner(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, DMO>
     */
    public function getLastModificatorDmos(): Collection
    {
        return $this->Last_Modificator_Dmos;
    }

    public function addLastModificatorDmo(DMO $lastModificatorDmo): static
    {
        if (!$this->Last_Modificator_Dmos->contains($lastModificatorDmo)) {
            $this->Last_Modificator_Dmos->add($lastModificatorDmo);
            $lastModificatorDmo->setLastModificator($this);
        }

        return $this;
    }

    public function removeLastModificatorDmo(DMO $lastModificatorDmo): static
    {
        if ($this->Last_Modificator_Dmos->removeElement($lastModificatorDmo)) {
            // set the owning side to null (unless already changed)
            if ($lastModificatorDmo->getLastModificator() === $this) {
                $lastModificatorDmo->setLastModificator(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Project>
     */
    public function getProjects(): Collection
    {
        return $this->projects;
    }

    public function addProject(Project $project): static
    {
        if (!$this->projects->contains($project)) {
            $this->projects->add($project);
            $project->setProjectManager($this);
        }

        return $this;
    }

    public function removeProject(Project $project): static
    {
        if ($this->projects->removeElement($project)) {
            // set the owning side to null (unless already changed)
            if ($project->getProjectManager() === $this) {
                $project->setProjectManager(null);
            }
        }

        return $this;
    }

    /**
     * @return Collection<int, Impute>
     */
    public function getImputes(): Collection
    {
        return $this->imputes;
    }

    public function addImpute(Impute $impute): static
    {
        if (!$this->imputes->contains($impute)) {
            $this->imputes->add($impute);
            $impute->setUser($this);
        }

        return $this;
    }

    public function removeImpute(Impute $impute): static
    {
        if ($this->imputes->removeElement($impute)) {
            // set the owning side to null (unless already changed)
            if ($impute->getUser() === $this) {
                $impute->setUser(null);
            }
        }

        return $this;
    }

    public function hasImpute(): bool
    {
        return !$this->imputes->isEmpty();
    }

    public function isImputation(): ?bool
    {
        return $this->imputation;
    }

    public function setImputation(?bool $imputation): static
    {
        $this->imputation = $imputation;

        return $this;
    }

    public function isCi(): ?bool
    {
        return $this->ci;
    }

    public function setCi(?bool $ci): static
    {
        $this->ci = $ci;

        return $this;
    }

    public function isSap(): ?bool
    {
        return $this->sap;
    }

    public function setSap(?bool $sap): static
    {
        $this->sap = $sap;

        return $this;
    }

    public function getWorkCenter(): ?string
    {
        return $this->workCenter;
    }

    public function setWorkCenter(?string $workCenter): static
    {
        $this->workCenter = $workCenter;

        return $this;
    }

    public function getCodeGestionnaire(): ?string
    {
        return $this->codeGestionnaire;
    }

    public function setCodeGestionnaire(?string $codeGestionnaire): static
    {
        $this->codeGestionnaire = $codeGestionnaire;

        return $this;
    }

    /**
     * @return array<string>|null
     */
    public function getManagedPlaces(): ?array
    {
        return $this->managedPlaces;
    }

    /**
     * @param array<string>|null $managedPlaces
     */
    public function setManagedPlaces(?array $managedPlaces): static
    {
        $this->managedPlaces = $managedPlaces;

        return $this;
    }

    /**
     * Vérifie si l'utilisateur gère une place spécifique
     */
    public function managesPlace(string $place): bool
    {
        if ($this->managedPlaces === null) {
            return false;
        }

        return in_array($place, $this->managedPlaces);
    }

    public function toArray(): array
    {
        return [
            'id' => $this->id,
            'email' => $this->email ?? '',
            'username' => $this->username ?? '',
            'nom' => $this->nom ?? '',
            'prenom' => $this->prenom ?? '',
            'roles' => $this->roles,
            'manager' => $this->manager ?? '',
            'titre' => $this->titre ?? '',
            'isManager' => $this->isManager ?? '',
            'departement' => $this->departement ?? '',
            'imputation' => $this->imputation,
            'ci' => $this->ci,
            'sap' => $this->sap,
            'workCenter' => $this->workCenter ?? '',
            'codeGestionnaire' => $this->codeGestionnaire ?? '',
        ];
    }

    public function getLastLdapSync(): ?\DateTime
    {
        return $this->lastLdapSync;
    }

    public function setLastLdapSync(?\DateTime $lastLdapSync): static
    {
        $this->lastLdapSync = $lastLdapSync;

        return $this;
    }

    /**
     * @return Collection<int, Document>
     */
    public function getQualDocuments(): Collection
    {
        return $this->qualDocuments;
    }

    public function addQualDocument(Document $qualDocument): static
    {
        if (!$this->qualDocuments->contains($qualDocument)) {
            $this->qualDocuments->add($qualDocument);
            $qualDocument->setQualOwner($this);
        }

        return $this;
    }

    public function removeQualDocument(Document $qualDocument): static
    {
        if ($this->qualDocuments->removeElement($qualDocument)) {
            // set the owning side to null (unless already changed)
            if ($qualDocument->getQualOwner() === $this) {
                $qualDocument->setQualOwner(null);
            }
        }

        return $this;
    }

}
