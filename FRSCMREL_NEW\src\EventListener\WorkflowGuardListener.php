<?php

namespace App\EventListener;

use App\Service\VisaChecker;
use Symfony\Component\Workflow\Event\GuardEvent;
use Symfony\Component\EventDispatcher\EventSubscriberInterface;

class WorkflowGuardListener implements EventSubscriberInterface
{
    private VisaChecker $visaChecker;

    public function __construct(VisaChecker $visaChecker)
    {
        $this->visaChecker = $visaChecker;
    }

    public function onWorkflowGuard(GuardEvent $event): void
    {
        $document = $event->getSubject();
        $transition = $event->getTransition();
        $targetPlaces = $transition->getTos();
        $originPlaces = $transition->getFroms()[0];
        // dd($document,$transition);
        if (in_array($originPlaces,['Assembly','Machining','Molding'])){
            $originPlaces='prod';
        }
        $visas=$document->getVisasArray();

        if(!in_array('visa_'.$originPlaces,$visas)){
            // dd($originPlaces);
            $event->setBlocked(true);
            return;
        }
        // dd($originPlaces);
        foreach ($targetPlaces as $place) {
            if (!$this->validatePlace($place, $document)) {
                $event->setBlocked(true);
                return;
            }
        }
    }

    private function validatePlace(string $place, $document): bool
    {
        switch ($place) {
            case 'Project':
                return $this->validateProject($document);
            case 'Quality':
                return $this->validateQuality($document);
            case 'Qual_Logistique':
                return $this->validateQualLogistique($document);
            case 'Logistique':
                return $this->validateLogistique($document);
            case 'Assembly':
                return $this->validateAssembly($document);
            case 'Machining':
                return $this->validateMachining($document);
            case 'Molding':
                return $this->validateMolding($document);
            case 'Methode_assemblage':
                return $this->validateMethodeAssemblage($document);
            case 'Achat_F30':
                return $this->validateAchatF30($document);
            case 'Achat_FIA':
                return $this->validateAchatFIA($document);
            case 'Achat_Rfq':
                return $this->validateAchatRfq($document);
            case 'methode_Labo':
                return $this->validateMethodeLabo($document);
            case 'QProd':
                return $this->validateQProd($document);
            case 'Indus':
                return $this->validateIndus($document);
            case 'GID':
                return $this->validateGID($document);
            case 'Costing':
                return $this->validateCosting($document);
            case 'Core_Data':
                return $this->validateCoreData($document);
            case 'Planning':
                return $this->validatePlanning($document);
            case 'Metro':
                return $this->validateMetro($document);
            case 'Tirage_Plans':
                return $this->validateTirage_Plans($document);

            default:
                // Conditions qui ne requièrent pas de validation spécifique, seulement les visas
                // Core_Data, Achat_RoHs_REACH, Produit, Achat_Hts
                return $this->visaChecker->areVisasValidForStep($document, $place);
        }
    }


    private function validateProject($document): bool
    {
        $prodDraw = $document->getProdDraw();
        $relPackProject = $document->getRelPack() && $document->getRelPack()->getProjectRelation() ? $document->getRelPack()->getProjectRelation()->getOTP() : 'STAND';

        return (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT'))
            && $relPackProject != 'STAND' && $this->visaChecker->areVisasValidForStep($document, 'Project');
    }

    private function validateQuality($document): bool
    {
        $docType = $document->getDocType();

        return in_array($docType, ['PUR', 'ASSY', 'DOC'], true) && $this->visaChecker->areVisasValidForStep($document, 'Quality');
    }

    private function validateQualLogistique($document): bool
    {
        $docType = $document->getDocType();
        $inventoryImpact = $document->getInventoryImpact();
        return $docType !== 'DOC' && $inventoryImpact !== 'NO IMPACT' && $this->visaChecker->areVisasValidForStep($document, 'Qual_Logistique');
    }

    private function validateLogistique($document): bool
    {
        $docType = $document->getDocType();
        $inventoryImpact = $document->getInventoryImpact();
        return $docType !== 'DOC' && $inventoryImpact !== 'NO IMPACT' && $this->visaChecker->areVisasValidForStep($document, 'Logistique');
    }

    private function validateAssembly($document): bool
    {
        $docType = $document->getDocType();
        $procType = $document->getProcType();
        return in_array($docType, ['ASSY', 'DOC'], true) && !str_starts_with($procType, 'F') && $this->visaChecker->areVisasValidForStep($document, 'Assembly');
    }

    private function validateMachining($document): bool
    {
        $docType = $document->getDocType();

        return in_array($docType, ['MACH'], true) && $this->visaChecker->areVisasValidForStep($document, 'Machining');
    }


    private function validateMolding($document): bool
    {
        $docType = $document->getDocType();

        return in_array($docType, ['MOLD'], true) && $this->visaChecker->areVisasValidForStep($document, 'Molding');
    }

    private function validateMethodeAssemblage($document): bool
    {
        $docType = $document->getDocType();
        $matProdType = $document->getMatProdType();
        $activity = $document->getRelPack()?->getActivity();

        return ((in_array($docType, ['ASSY', 'DOC'], true) || $matProdType === 'VERP') && $this->visaChecker->areVisasValidForStep($document, 'Methode_assemblage') && $activity !== 'MOB_INDUS');
    }

    private function validateCoreData($document): bool
    {
        $docType = $document->getDocType();
        $procType = $document->getProcType();
        $prodDraw = $document->getProdDraw();
        $relPackProject = $document->getRelPack() && $document->getRelPack()->getProjectRelation() ? $document->getRelPack()->getProjectRelation()->getOTP() : 'STAND';
        $inventoryImpact = $document->getInventoryImpact();

        // Vérification des visas requis pour Core_Data
        if (!$this->visaChecker->areVisasValidForStep($document, 'Core_Data')) {
            return false;
        }

        // Récupération des visas validés optionnels
        $validatedOptionalVisas = $this->visaChecker->getValidatedOptionalVisas();

        // Conditions principales
        $condition1Valid = $docType === 'PUR' && $procType === 'F' && in_array('visa_Achat_Rfq', $validatedOptionalVisas, true);

        $condition2Valid = $docType === 'PUR' && $procType === 'F30' && in_array('visa_Achat_F30', $validatedOptionalVisas, true);

        $condition3Valid = in_array($docType, ['MACH', 'MOLD'], true)
            && in_array('visa_Metro', $validatedOptionalVisas, true)
            && in_array('visa_Planning', $validatedOptionalVisas, true);

        $condition4Valid = $docType === 'ASSY'
            && in_array('visa_Quality', $validatedOptionalVisas, true)
            && in_array('visa_Metro', $validatedOptionalVisas, true)
            && in_array('visa_Planning', $validatedOptionalVisas, true)
            && (
                ($relPackProject !== 'STAND'
                    && (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT')))
                ||
                ($relPackProject !== 'STAND'
                    && !(str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT')))
                ||
                ($relPackProject === 'STAND')
            );

        $condition5Valid = $docType === 'DOC'
            && in_array('visa_Quality', $validatedOptionalVisas, true)
            && in_array('visa_Metro', $validatedOptionalVisas, true)
            && (
                ($relPackProject !== 'STAND'
                    && in_array('visa_Project', $validatedOptionalVisas, true)
                    && (str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT')))
                ||
                ($relPackProject !== 'STAND'
                    && !in_array('visa_Project', $validatedOptionalVisas, true)
                    && !(str_starts_with($prodDraw, 'GA') || str_starts_with($prodDraw, 'FT')))
                ||
                ($relPackProject === 'STAND'
                    && !in_array('visa_Project', $validatedOptionalVisas, true))
            );

        $mainConditionValid = $condition1Valid || $condition2Valid || $condition3Valid || $condition4Valid || $condition5Valid;

        // Conditions d'inventaire
        $inventoryConditionValid = (
            ($inventoryImpact === 'TO BE SCRAPPED' || $inventoryImpact === 'TO BE UPDATED')
                && in_array('visa_Logistique', $validatedOptionalVisas, true)
        ) || (
            ($inventoryImpact === 'NO IMPACT' || $docType === 'DOC')
                && !in_array('visa_Logistique', $validatedOptionalVisas, true)
        );

        // Validation finale : les deux doivent être vraies
        return $mainConditionValid && $inventoryConditionValid;
    }

    private function validateAchatF30($document): bool
    {
        $procType = $document->getProcType();
        return $procType === 'F30' && $this->visaChecker->areVisasValidForStep($document, 'Achat_F30');
    }

    private function validateAchatFIA($document): bool
    {
        $procType = $document->getProcType();
        return in_array($procType, ['F', 'F30'], true) && $this->visaChecker->areVisasValidForStep($document, 'Achat_FIA');
    }

    private function validateMethodeLabo($document): bool
    {
        $docType = $document->getDocType();
        return $this->visaChecker->areVisasValidForStep($document, 'methode_Labo') && $docType === 'ASSY';
    }

    private function validateQProd($document): bool
    {
        $docType = $document->getDocType();
        return $this->visaChecker->areVisasValidForStep($document, 'QProd') && in_array($docType, ['MACH', 'MOLD'], true);
    }

    private function validateIndus($document): bool
    {
            $docType = $document->getDocType();
            // visa && (condition1 || condition2)
            $visas = $this->visaChecker->areVisasValidForStep($document, 'Indus');
            $validatedOptionalVisas = $this->visaChecker->getValidatedOptionalVisas();
            $condition1Valid = in_array('visa_Metro', $validatedOptionalVisas, true) && $docType === 'ASSY';
            $condition2Valid = $docType === 'DOC';
            return $visas && ($condition1Valid || $condition2Valid);
    }

    private function validateAchatRfq($document): bool
    {
        $docType = $document->getDocType();
        $procType = $document->getProcType();
        return $docType === 'PUR' && !in_array($procType, ['E'], true) && $this->visaChecker->areVisasValidForStep($document, 'Achat_Rfq');
    }

    private function validateGID($document): bool
    {
        $docType = $document->getDocType();
        $visas = $this->visaChecker->areVisasValidForStep($document, 'GID');
        $validatedOptionalVisas = $this->visaChecker->getValidatedOptionalVisas();

        $condition1Valid = in_array('visa_Indus', $validatedOptionalVisas, true) && $docType === 'ASSY';
        $condition2Valid = in_array('visa_prod', $validatedOptionalVisas, true) && ($docType === 'MACH' or $docType === 'MOLD');

        return $visas && ($condition1Valid || $condition2Valid);
    }

    private function validateCosting($document): bool
    {
        $docType = $document->getDocType();
        $visas = $this->visaChecker->areVisasValidForStep($document, 'Costing');
        $validatedOptionalVisas = $this->visaChecker->getValidatedOptionalVisas();

        $condition1Valid = in_array('visa_Achat_FIA', $validatedOptionalVisas, true) && $docType === 'PUR';
        $condition2Valid = in_array('visa_GID', $validatedOptionalVisas, true) && $docType !== 'PUR';
        return $visas && ($condition1Valid || $condition2Valid);
    }


    /**
     * Validation pour la place "Planning"
     */
    private function validatePlanning($document): bool
    {
        $docType = $document->getDocType();

        return in_array($docType, ['ASSY', 'MACH', 'MOLD','DOC'], true)
            && $this->visaChecker->areVisasValidForStep($document, 'Planning');
    }

    private function validateMetro($document): bool
    {
        $docType = $document->getDocType();
        $procType = $document->getProcType();
        $visas = $this->visaChecker->areVisasValidForStep($document, 'Metro');
        $validatedOptionalVisas = $this->visaChecker->getValidatedOptionalVisas();

        $condition1Valid = in_array('visa_prod', $validatedOptionalVisas, true) && $procType === 'E';
        $condition2Valid = in_array('visa_Quality', $validatedOptionalVisas, true) && $docType === 'PUR';

        return  $visas && ($condition1Valid || $condition2Valid);
    }

    private function validateTirage_Plans($document): bool
    {
        $docType = $document->getDocType();
        $validatedOptionalVisas = $this->visaChecker->getValidatedOptionalVisas();
        //switch aletiq isSwitchAletiq
        $isSwitchAletiq = $document->isSwitchAletiq();

        // dd( $this->visaChecker->areVisasValidForStep($document, 'Tirage_Plans'));
        return in_array($docType, [ 'MACH', 'MOLD'], true) && $isSwitchAletiq
            && $this->visaChecker->areVisasValidForStep($document, 'Tirage_Plans');
    }


    public static function getSubscribedEvents(): array
    {
        return [
            'workflow.document_workflow.guard' => 'onWorkflowGuard',
        ];
    }
}
