<?php

namespace App\Form;

use App\Entity\Document;
use App\Entity\ReleasedPackage;
use Symfony\Bridge\Doctrine\Form\Type\EntityType;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

class DocumentType extends AbstractType
{
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('reference')
            ->add('refRev')
            ->add('refTitleFra')
            ->add('prodDraw')
            ->add('prodDrawRev')
            ->add('drawingPath')
            ->add('alias')
            ->add('docType')
            ->add('relPack', EntityType::class, [
                'class' => ReleasedPackage::class,
                'choice_label' => 'id',
            ])
        ;
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => Document::class,
        ]);
    }
}
