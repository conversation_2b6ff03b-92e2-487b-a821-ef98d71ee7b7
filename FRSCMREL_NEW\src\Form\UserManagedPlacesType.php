<?php

namespace App\Form;

use App\Entity\User;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Workflow\Registry;

class UserManagedPlacesType extends AbstractType
{
    private Registry $workflowRegistry;

    public function __construct(Registry $workflowRegistry)
    {
        $this->workflowRegistry = $workflowRegistry;
    }

    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        // Récupérer toutes les places du workflow
        $workflow = $this->workflowRegistry->get(new \App\Entity\Document());
        $places = $workflow->getDefinition()->getPlaces();

        $placesChoices = [];
        foreach ($places as $place) {
            $placesChoices[$place] = $place;
        }

        $builder
            ->add('managedPlaces', ChoiceType::class, [
                'choices' => $placesChoices,
                'multiple' => true,
                'expanded' => true,
                'label' => 'Places gérées',
                'help' => 'Sélectionnez les places que vous gérez. Vous recevrez des notifications pour les documents dans ces places.',
                'required' => false,
            ]);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => User::class,
        ]);
    }
}
