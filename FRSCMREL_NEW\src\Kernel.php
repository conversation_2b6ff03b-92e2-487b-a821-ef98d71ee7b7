<?php

namespace App;

use Symfony\Bundle\FrameworkBundle\Kernel\MicroKernelTrait;
use Symfony\Component\HttpKernel\Kernel as BaseKernel;

class Kernel extends BaseKernel
{
    use MicroKernelTrait;

    public function __construct(string $environment, bool $debug)
    {
        parent::__construct($environment, $debug); // Utiliser la valeur $debug passée en paramètre

        setlocale(LC_ALL, 'fr_FR.UTF-8');
        date_default_timezone_set('Europe/Paris');
    }


}
