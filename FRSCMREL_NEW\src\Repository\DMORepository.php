<?php

namespace App\Repository;

use App\Entity\DMO;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @extends ServiceEntityRepository<DMO>
 */
class DMORepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, DMO::class);
    }

    //    /**
    //     * @return DMO[] Returns an array of DMO objects
    //     */
    //    public function findByExampleField($value): array
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->orderBy('d.id', 'ASC')
    //            ->setMaxResults(10)
    //            ->getQuery()
    //            ->getResult()
    //        ;
    //    }

    //    public function findOneBySomeField($value): ?DMO
    //    {
    //        return $this->createQueryBuilder('d')
    //            ->andWhere('d.exampleField = :val')
    //            ->setParameter('val', $value)
    //            ->getQuery()
    //            ->getOneOrNullResult()
    //        ;
    //    }
    public function findBySearchCriteria(array $criteria)
    {
        $qb = $this->createQueryBuilder('d');

        // Jointures pour les entités associées
        if (!empty($criteria['requestor'])) {
            $qb->join('d.requestor', 'r');
        }
        if (!empty($criteria['engOwner'])) {
            $qb->join('d.Eng_Owner', 'e');
        }
        if (!empty($criteria['lastModificator'])) {
            $qb->join('d.last_Modificator', 'lm');
        }
        if (!empty($criteria['productRange'])) {
            $qb->join('d.productRange', 'pr');
        }

        if (!empty($criteria['dmo'])) {
            $qb->andWhere('d.dmo LIKE :dmo')
               ->setParameter('dmo', '%' . $criteria['dmo'] . '%');
        }

        if (!empty($criteria['dateInit'])) {
            try {
                $dateInit = new \DateTimeImmutable($criteria['dateInit']);
                $start = $dateInit->setTime(0, 0, 0);
                $end   = $dateInit->setTime(23, 59, 59);
                $qb->andWhere('d.date_init BETWEEN :startDateInit AND :endDateInit')
                   ->setParameter('startDateInit', $start)
                   ->setParameter('endDateInit', $end);
            } catch (\Exception $e) {
                // Gérer une date invalide
            }
        }

        if (!empty($criteria['description'])) {
            $qb->andWhere('d.Description LIKE :description')
               ->setParameter('description', '%' . $criteria['description'] . '%');
        }
        if (!empty($criteria['project'])) {
            // On rejoint l'entité Project
            $qb->join('d.project_relation', 'p');
            // On filtre sur p.id
            $qb->andWhere('p.id = :projectId')
               ->setParameter('projectId', $criteria['project']);
        }

        if (!empty($criteria['requestor'])) {
            $qb->andWhere('r.username LIKE :requestor')
               ->setParameter('requestor', '%' . $criteria['requestor'] . '%');
        }

        if (!empty($criteria['decision'])) {
            $qb->andWhere('d.Decision LIKE :decision')
               ->setParameter('decision', '%' . $criteria['decision'] . '%');
        }

        if (isset($criteria['status']) && $criteria['status'] !== '') {
            $qb->andWhere('d.status = :status')
               ->setParameter('status', (bool)$criteria['status']);
        }

        if (!empty($criteria['ex'])) {
            $qb->andWhere('d.Ex LIKE :ex')
               ->setParameter('ex', '%' . $criteria['ex'] . '%');
        }

        if (isset($criteria['indusRelated']) && $criteria['indusRelated'] !== '') {
            $qb->andWhere('d.Indus_Related = :indusRelated')
               ->setParameter('indusRelated', (bool)$criteria['indusRelated']);
        }

        if (!empty($criteria['engOwner'])) {
            $qb->andWhere('e.username LIKE :engOwner')
               ->setParameter('engOwner', '%' . $criteria['engOwner'] . '%');
        }

        if (!empty($criteria['dateEnd'])) {
            try {
                $dateEnd = new \DateTimeImmutable($criteria['dateEnd']);
                $start = $dateEnd->setTime(0, 0, 0);
                $end   = $dateEnd->setTime(23, 59, 59);
                $qb->andWhere('d.date_end BETWEEN :startDateEnd AND :endDateEnd')
                   ->setParameter('startDateEnd', $start)
                   ->setParameter('endDateEnd', $end);
            } catch (\Exception $e) {
                // Gérer une date invalide
            }
        }

        if (!empty($criteria['prNumber'])) {
            $qb->andWhere('d.Pr_Number = :prNumber')
               ->setParameter('prNumber', $criteria['prNumber']);
        }

        if (!empty($criteria['lastUpdateDate'])) {
            try {
                $lastUpdateDate = new \DateTimeImmutable($criteria['lastUpdateDate']);
                $start = $lastUpdateDate->setTime(0, 0, 0);
                $end   = $lastUpdateDate->setTime(23, 59, 59);
                $qb->andWhere('d.last_Update_Date BETWEEN :startLastUpdateDate AND :endLastUpdateDate')
                   ->setParameter('startLastUpdateDate', $start)
                   ->setParameter('endLastUpdateDate', $end);
            } catch (\Exception $e) {
                // Gérer une date invalide
            }
        }

        if (!empty($criteria['exAssessment'])) {
            $qb->andWhere('d.Ex_Assessment LIKE :exAssessment')
               ->setParameter('exAssessment', '%' . $criteria['exAssessment'] . '%');
        }

        if (!empty($criteria['spentTime'])) {
            $qb->andWhere('d.Spent_Time = :spentTime')
               ->setParameter('spentTime', $criteria['spentTime']);
        }

        if (!empty($criteria['type'])) {
            $qb->andWhere('d.Type LIKE :type')
               ->setParameter('type', '%' . $criteria['type'] . '%');
        }

        if (!empty($criteria['document'])) {
            $qb->andWhere('d.Document LIKE :document')
               ->setParameter('document', '%' . $criteria['document'] . '%');
        }

        if (!empty($criteria['productRange'])) {
            $qb->andWhere('pr.ProductRange LIKE :productRange')
               ->setParameter('productRange', '%' . $criteria['productRange'] . '%');
        }

        if (!empty($criteria['lastModificator'])) {
            $qb->andWhere('lm.username LIKE :lastModificator')
               ->setParameter('lastModificator', '%' . $criteria['lastModificator'] . '%');
        }

        // Optionnel : tri par date d'initiation décroissante et attribut dmo
        $qb->orderBy('d.date_init', 'DESC')
           ->addOrderBy('d.dmo', 'DESC');

        return $qb; // Retourner le QueryBuilder pour la pagination
    }


    // /**
    //  * Récupère le nombre de DMO ouvertes (status=true) par mois pour le type "Engineering",
    //  * regroupées par division (via l'entité ProductRange) pour les dernières années.
    //  */
    // public function getOpenDmoByMonthForEngineering(int $visibleHistory = 3): array
    // {
    //     $qb = $this->createQueryBuilder('d')
    //         ->join('d.productRange', 'pr')
    //         ->select(
    //             'YEAR(d.date_init) as fy',
    //             'MONTH(d.date_init) as m',
    //             "DATE_FORMAT(d.date_init, '%b') as monthName",
    //             "SUM(CASE WHEN pr.Division = 'Energy' THEN 1 ELSE 0 END) as Energy",
    //             "SUM(CASE WHEN pr.Division = 'Industry' THEN 1 ELSE 0 END) as Industry",
    //             "SUM(CASE WHEN pr.Division = 'Aerospace' THEN 1 ELSE 0 END) as Aerospace"
    //         )
    //         ->where('d.Type = :type')
    //         ->andWhere('d.status = true')
    //         ->andWhere('YEAR(CURRENT_DATE()) - YEAR(d.date_init) <= :visibleHistory')
    //         ->setParameter('type', 'Engineering')
    //         ->setParameter('visibleHistory', $visibleHistory)
    //         ->groupBy('YEAR(d.date_init)')
    //         ->addGroupBy('MONTH(d.date_init)')
    //         ->addGroupBy("DATE_FORMAT(d.date_init, '%b')")
    //         ->orderBy('fy', 'ASC')
    //         ->addOrderBy('m', 'ASC');

    //     return $qb->getQuery()->getResult();
    // }

    // /**
    //  * Récupère le nombre de DMO clôturées (status=false et date_end renseignée) par mois pour le type "Engineering",
    //  * regroupées par division (via l'entité ProductRange) pour les dernières années.
    //  */
    // public function getClosedDmoByMonthForEngineering(int $visibleHistory = 3): array
    // {
    //     $qb = $this->createQueryBuilder('d')
    //         ->join('d.productRange', 'pr')
    //         ->select(
    //             'YEAR(d.date_end) as fy',
    //             'MONTH(d.date_end) as m',
    //             "DATE_FORMAT(d.date_end, '%b') as monthName",
    //             "SUM(CASE WHEN pr.Division = 'Energy' THEN 1 ELSE 0 END) as Energy",
    //             "SUM(CASE WHEN pr.Division = 'Industry' THEN 1 ELSE 0 END) as Industry",
    //             "SUM(CASE WHEN pr.Division = 'Aerospace' THEN 1 ELSE 0 END) as Aerospace"
    //         )
    //         ->where('d.Type = :type')
    //         ->andWhere('d.status = false')
    //         ->andWhere('d.date_end IS NOT NULL')
    //         ->andWhere('YEAR(CURRENT_DATE()) - YEAR(d.date_init) <= :visibleHistory')
    //         ->setParameter('type', 'Engineering')
    //         ->setParameter('visibleHistory', $visibleHistory)
    //         ->groupBy('YEAR(d.date_end)')
    //         ->addGroupBy('MONTH(d.date_end)')
    //         ->addGroupBy("DATE_FORMAT(d.date_end, '%b')")
    //         ->orderBy('fy', 'ASC')
    //         ->addOrderBy('m', 'ASC');

    //     return $qb->getQuery()->getResult();
    // }


}
