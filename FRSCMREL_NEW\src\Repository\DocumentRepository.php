<?php

namespace App\Repository;

use App\Entity\Document;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;
use Doctrine\ORM\EntityManagerInterface;

/**
 * @extends ServiceEntityRepository<Document>
 */
class DocumentRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, Document::class);
    }

//    /**
//     * @return Document[] Returns an array of Document objects
//     */
//    public function findByExampleField($value): array
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->orderBy('d.id', 'ASC')
//            ->setMaxResults(10)
//            ->getQuery()
//            ->getResult()
//        ;
//    }

//    public function findOneBySomeField($value): ?Document
//    {
//        return $this->createQueryBuilder('d')
//            ->andWhere('d.exampleField = :val')
//            ->setParameter('val', $value)
//            ->getQuery()
//            ->getOneOrNullResult()
//        ;
//    }

// findByCurrentStep(string $step)

// public function findByCurrentStepNative(string $key): array
// {
//     // dd($key);
//     $conn = $this->getEntityManager()->getConnection();

//     // Construct the JSON path dynamically
//     $path = '$."' . $key . '"';

//     $sql = "SELECT *
//             FROM document d
//             WHERE JSON_EXTRACT(d.current_steps, :jsonPath) IS NOT NULL";

//     $stmt = $conn->prepare($sql);
//     $stmt->bindValue('jsonPath', $path);
//     $resultSet = $stmt->executeQuery();

//     return $resultSet->fetchAllAssociative();
// }

public function findByCurrentStepNative(string $position)
{
    // Use optimized SQL query instead of loading all documents
    $conn = $this->getEntityManager()->getConnection();

    $sql = "
        SELECT d.*
        FROM document d
        WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
        ORDER BY d.id DESC
    ";

    $result = $conn->executeQuery($sql, ['$."' . $position . '"']);
    $documentsData = $result->fetchAllAssociative();

    // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
    if (empty($documentsData)) {
        return [];
    }

    $documentIds = array_column($documentsData, 'id');

    // Use DQL to load all documents with their relationships in one query
    $qb = $this->createQueryBuilder('d');
    $qb->select('d')
       ->where('d.id IN (:ids)')
       ->setParameter('ids', $documentIds)
       ->orderBy('d.id', 'DESC');

    return $qb->getQuery()->getResult();
}

    public function findLatestByReference(string $reference): ?Document
    {
        // Utiliser une requête DQL plus explicite pour récupérer toutes les propriétés
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
            ->andWhere('d.reference = :reference')
            ->setParameter('reference', $reference)
            ->orderBy('d.refRev', 'DESC')
            ->setMaxResults(1);

        $query = $qb->getQuery();
        $result = $query->getOneOrNullResult();



        return $result;
    }

    /**
     * Trouve des documents similaires en fonction du type de document, du type de processus et du type de matériau
     */
    public function findSimilarDocuments(?string $docType, ?string $procType, ?string $materialType, int $limit = 20): array
    {
        $qb = $this->createQueryBuilder('d');

        if ($docType) {
            $qb->andWhere('d.docType = :docType')
               ->setParameter('docType', $docType);
        }

        if ($procType) {
            $qb->andWhere('d.procType = :procType')
               ->setParameter('procType', $procType);
        }

        if ($materialType) {
            $qb->andWhere('d.Material_Type = :materialType')
               ->setParameter('materialType', $materialType);
        }

        return $qb->setMaxResults($limit)
                 ->getQuery()
                 ->getResult();
    }

    /**
     * Trouve des documents par période - Version optimisée
     */
    public function findByPeriod(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND (
                -- Check for documents created in the period (old format)
                EXISTS (
                    SELECT 1 FROM JSON_TABLE(
                        d.state_timestamps,
                        '$.*' COLUMNS (
                            state_date VARCHAR(255) PATH '$'
                        )
                    ) jt
                    WHERE STR_TO_DATE(jt.state_date, '%Y-%m-%d %H:%i:%s') BETWEEN ? AND ?
                )
                OR
                -- Check for documents created in the period (new format)
                EXISTS (
                    SELECT 1 FROM JSON_TABLE(
                        d.state_timestamps,
                        '$.*[*]' COLUMNS (
                            enter_date VARCHAR(255) PATH '$.enter'
                        )
                    ) jt2
                    WHERE STR_TO_DATE(jt2.enter_date, '%Y-%m-%d %H:%i:%s') BETWEEN ? AND ?
                )
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s'),
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s')
        ]);

        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents à valider par un utilisateur - Version optimisée
     */
    public function findDocumentsToValidateByUser($user): array
    {
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->innerJoin('d.visas', 'v')
           ->innerJoin('v.validator', 'u')
           ->where('u.id = :userId')
           ->andWhere('v.dateVisa IS NULL')
           ->setParameter('userId', $user->getId())
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents à réviser par un utilisateur - Version optimisée
     */
    public function findDocumentsToReviewByUser($user): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.superviseur_id = ?
            AND d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND (
                -- Check for rejection in new format (array entries with from_state containing 'reject')
                JSON_SEARCH(d.state_timestamps, 'one', '%reject%', NULL, '$.*[*].from_state') IS NOT NULL
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [$user->getId()]);
        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents traités entre deux dates - Version optimisée
     */
    public function findDocumentsProcessedBetween(\DateTime $startDate, \DateTime $endDate): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE d.state_timestamps IS NOT NULL
            AND d.state_timestamps != '{}'
            AND d.state_timestamps != ''
            AND EXISTS (
                SELECT 1 FROM JSON_TABLE(
                    d.state_timestamps,
                    '$.*[*]' COLUMNS (
                        exit_date VARCHAR(255) PATH '$.exit'
                    )
                ) jt
                WHERE jt.exit_date IS NOT NULL
                AND jt.exit_date != ''
                AND STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s') BETWEEN ? AND ?
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            $startDate->format('Y-m-d H:i:s'),
            $endDate->format('Y-m-d H:i:s')
        ]);

        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Compte les documents par étape de workflow de manière optimisée
     * Utilise des requêtes SQL natives pour contourner les limitations de Doctrine avec JSON
     */
    public function countDocumentsByWorkflowStep(): array
    {
        try {
            return $this->countDocumentsByWorkflowStepNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode en cas d'erreur
            return $this->countDocumentsByWorkflowStepFallback();
        }
    }

    private function countDocumentsByWorkflowStepNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Requête unique qui regroupe Qual_Logistique et Logistique
        $sql = "
            SELECT
            CASE
                WHEN jt.step IN ('Qual_Logistique', 'Logistique')
                THEN 'Qual_Logistique'
                ELSE jt.step
            END AS step_group,
            COUNT(DISTINCT d.id) AS nb_documents
            FROM document AS d
            JOIN JSON_TABLE(
            JSON_KEYS(d.current_steps),
            '$[*]' COLUMNS (
                step VARCHAR(100) PATH '$'
            )
            ) AS jt
            LEFT JOIN visa AS v
            ON v.released_drawing_id = d.id
            AND v.name COLLATE utf8mb4_unicode_ci
                = CONCAT('visa_', jt.step) COLLATE utf8mb4_unicode_ci
            AND v.status = 'valid'
            WHERE v.id IS NULL
            GROUP BY step_group
        ";

        $stmt = $conn->executeQuery($sql);
        $rows = $stmt->fetchAllAssociative();

        $counts = [];
        foreach ($rows as $row) {
            $counts[$row['step_group']] = (int) $row['nb_documents'];
        }

        return $counts;
    }


    /**
     * Version de fallback utilisant l'ancienne logique mais optimisée
     */
    private function countDocumentsByWorkflowStepFallback(): array
    {
        // Charger tous les documents une seule fois
        $documents = $this->findAll();
        $count = [];

        foreach ($documents as $document) {
            foreach ($document->getCurrentSteps() as $step => $value) {
                if ($step === 'Qual_Logistique' || $step === 'Logistique') {
                    if ($document->hasVisa('visa_Qual_Logistique') && $document->hasVisa('visa_Logistique')) {
                        continue;
                    }
                } else {
                    if ($document->hasVisa('visa_'.$step)) {
                        continue;
                    }
                }

                if (!isset($count[$step])) {
                    $count[$step] = 0;
                }
                $count[$step]++;
            }
        }

        return $count;
    }

    /**
     * Version avec cache du comptage des documents par étape
     * Cache pendant 30 secondes pour éviter les requêtes répétées
     */
    public function countDocumentsByWorkflowStepCached(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Si le cache est vide ou expiré (2 minutes)
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 120) {
            $cache = $this->countDocumentsByWorkflowStep();
            $cacheTime = $now;
        }

        return $cache;
    }

    /**
     * Charge les documents avec leurs relations de manière optimisée pour éviter les requêtes N+1
     */
    public function findDocumentsWithRelations(array $documentIds): array
    {
        if (empty($documentIds)) {
            return [];
        }

        $qb = $this->createQueryBuilder('d');
        $qb->select('d', 'v', 'validator', 'rp', 'superviseur', 'c')
           ->leftJoin('d.visas', 'v')
           ->leftJoin('v.validator', 'validator')
           ->leftJoin('d.relPack', 'rp')
           ->leftJoin('d.superviseur', 'superviseur')
           ->leftJoin('d.commentaires', 'c')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Version optimisée pour charger les documents actifs avec toutes leurs relations
     */
    public function findActiveDocumentsInStepWithRelations(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.id
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM visa v
                WHERE v.released_drawing_id = d.id
                AND v.name = ?
                AND v.status = 'valid'
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            '$."' . $step . '"',
            'visa_' . $step
        ]);

        $documentIds = array_column($result->fetchAllAssociative(), 'id');

        return $this->findDocumentsWithRelations($documentIds);
    }

    /**
     * Trouve les documents actifs dans une étape spécifique (sans visa correspondant)
     * Utilise une requête SQL native pour contourner les limitations de Doctrine avec JSON
     */
    public function findActiveDocumentsInStep(string $step): array
    {
        try {
            return $this->findActiveDocumentsInStepNative($step);
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->findActiveDocumentsInStepFallback($step);
        }
    }

    private function findActiveDocumentsInStepNative(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            AND NOT EXISTS (
                SELECT 1 FROM visa v
                WHERE v.released_drawing_id = d.id
                AND v.name = ?
                AND v.status = 'valid'
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, [
            '$."' . $step . '"',
            'visa_' . $step
        ]);

        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    private function findActiveDocumentsInStepFallback(string $step): array
    {
        $documents = $this->findByCurrentStepNative($step);
        return array_filter($documents, function ($document) use ($step) {
            return !$document->hasVisa('visa_'.$step);
        });
    }

    /**
     * Trouve les documents actifs dans les étapes logistiques
     * (Qual_Logistique ou Logistique sans avoir les deux visas)
     */
    public function findActiveDocumentsInLogisticsSteps(): array
    {
        try {
            return $this->findActiveDocumentsInLogisticsStepsNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->findActiveDocumentsInLogisticsStepsFallback();
        }
    }

    private function findActiveDocumentsInLogisticsStepsNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE (
                JSON_EXTRACT(d.current_steps, '$.\"Qual_Logistique\"') IS NOT NULL
                OR JSON_EXTRACT(d.current_steps, '$.\"Logistique\"') IS NOT NULL
            )
            AND NOT (
                EXISTS (SELECT 1 FROM visa v1 WHERE v1.released_drawing_id = d.id AND v1.name = 'visa_Qual_Logistique' AND v1.status = 'valid')
                AND EXISTS (SELECT 1 FROM visa v2 WHERE v2.released_drawing_id = d.id AND v2.name = 'visa_Logistique' AND v2.status = 'valid')
            )
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql);
        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    private function findActiveDocumentsInLogisticsStepsFallback(): array
    {
        $document0 = $this->findByCurrentStepNative('Qual_Logistique');
        $document1 = $this->findByCurrentStepNative('Logistique');
        $documents = array_unique(array_merge($document0, $document1), SORT_REGULAR);

        return array_filter($documents, function ($document) {
            return !$document->hasVisa('visa_Qual_Logistique') || !$document->hasVisa('visa_Logistique');
        });
    }

    /**
     * Version optimisée de findByCurrentStepNative utilisant une requête SQL native
     */
    public function findByCurrentStepOptimized(string $step): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT d.*
            FROM document d
            WHERE JSON_EXTRACT(d.current_steps, ?) IS NOT NULL
            ORDER BY d.id DESC
        ";

        $result = $conn->executeQuery($sql, ['$."' . $step . '"']);
        $documentsData = $result->fetchAllAssociative();

        // Optimize: Get all document IDs and load them in batch to avoid N+1 queries
        if (empty($documentsData)) {
            return [];
        }

        $documentIds = array_column($documentsData, 'id');

        // Use DQL to load all documents with their relationships in one query
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.id IN (:ids)')
           ->setParameter('ids', $documentIds)
           ->orderBy('d.id', 'DESC');

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents actifs pour l'analyse de risque - Version optimisée
     */
    public function findActiveDocumentsForRiskAnalysis(int $limit = 50): array
    {
        // Utiliser QueryBuilder au lieu de SQL brut pour éviter les problèmes de paramètres
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->where('d.currentSteps IS NOT NULL')
           ->andWhere('d.currentSteps != :empty_json')
           ->andWhere('d.currentSteps != :empty_string')
           ->andWhere('d.stateTimestamps IS NOT NULL')
           ->andWhere('d.stateTimestamps != :empty_json2')
           ->andWhere('d.stateTimestamps != :empty_string2')
           ->setParameter('empty_json', '{}')
           ->setParameter('empty_string', '')
           ->setParameter('empty_json2', '{}')
           ->setParameter('empty_string2', '')
           ->orderBy('d.id', 'DESC')
           ->setMaxResults($limit);

        return $qb->getQuery()->getResult();
    }

    /**
     * Trouve les documents terminés dans une période donnée - Version optimisée
     */
    public function findCompletedDocumentsInPeriod(\DateTime $startDate, \DateTime $endDate, ?string $docType = null): array
    {
        // Utiliser QueryBuilder avec jointures pour éviter les problèmes SQL
        $qb = $this->createQueryBuilder('d');
        $qb->select('d')
           ->innerJoin('d.visas', 'v_be0', 'WITH', 'v_be0.name = :visa_be0 AND v_be0.status = :valid_status')
           ->innerJoin('d.visas', 'v_costing', 'WITH', 'v_costing.name = :visa_costing AND v_costing.status = :valid_status2')
           ->where('v_be0.dateVisa >= :start_date')
           ->andWhere('v_be0.dateVisa <= :end_date')
           ->setParameter('visa_be0', 'visa_BE_0')
           ->setParameter('visa_costing', 'visa_Costing')
           ->setParameter('valid_status', 'valid')
           ->setParameter('valid_status2', 'valid')
           ->setParameter('start_date', $startDate)
           ->setParameter('end_date', $endDate)
           ->orderBy('d.id', 'DESC');

        if ($docType) {
            $qb->andWhere('d.docType = :doc_type')
               ->setParameter('doc_type', $docType);
        }

        return $qb->getQuery()->getResult();
    }

    /**
     * Statistiques optimisées pour la navbar avec cache
     */
    public function getNavbarStatsOptimized(): array
    {
        static $cache = null;
        static $cacheTime = null;

        $now = time();

        // Cache pendant 5 minutes pour les stats de navbar
        if ($cache === null || $cacheTime === null || ($now - $cacheTime) > 300) {
            $cache = $this->calculateNavbarStats();
            $cacheTime = $now;
        }

        return $cache;
    }




    /**
     * Compte le nombre total de documents
     */
    public function countAllDocuments(): int
    {
        return $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->getQuery()
            ->getSingleScalarResult();
    }

    /**
     * Récupère le nombre de documents par type
     */
    public function getDocumentCountByType(): array
    {
        $result = $this->createQueryBuilder('d')
            ->select('d.docType, COUNT(d.id) as count')
            ->groupBy('d.docType')
            ->getQuery()
            ->getResult();

        $counts = [];
        foreach ($result as $row) {
            $counts[$row['docType'] ?? 'Unknown'] = (int)$row['count'];
        }

        return $counts;
    }

    /**
     * Récupère la distribution des états de workflow
     */
    public function getStateDistribution(): array
    {
        $sql = "
            SELECT
                j.state_name,
                COUNT(*) as count
            FROM document d
            CROSS JOIN (
                SELECT 'BE_0' as state_name UNION ALL
                SELECT 'BE_1' UNION ALL
                SELECT 'BE' UNION ALL
                SELECT 'Core_Data' UNION ALL
                SELECT 'Prod_Data' UNION ALL
                SELECT 'Quality' UNION ALL
                SELECT 'Achat_F30' UNION ALL
                SELECT 'Achat_RFQ' UNION ALL
                SELECT 'Achat_RoHs_REACH' UNION ALL
                SELECT 'Qual_Logistique' UNION ALL
                SELECT 'Project' UNION ALL
                SELECT 'GID' UNION ALL
                SELECT 'Produit'
            ) j
            WHERE JSON_EXTRACT(d.current_steps, CONCAT('$.', j.state_name)) = '1'
            GROUP BY j.state_name
            ORDER BY count DESC
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery()->fetchAllAssociative();

        $distribution = [];
        foreach ($result as $row) {
            $distribution[$row['state_name']] = (int)$row['count'];
        }

        return $distribution;
    }


    /**
     * Récupère la distribution des documents par département
     */
    public function getDocumentDistributionByDepartment(): array
    {
        $sql = "
            SELECT
                COALESCE(u.departement, 'Unknown') as department,
                COUNT(d.id) as count
            FROM document d
            LEFT JOIN released_package rp ON d.rel_pack_id = rp.id
            LEFT JOIN user u ON rp.owner_id = u.id
            GROUP BY u.departement
            ORDER BY count DESC
            LIMIT 10
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery()->fetchAllAssociative();

        $distribution = [];
        foreach ($result as $row) {
            $distribution[$row['department']] = (int)$row['count'];
        }

        return $distribution;
    }

    /**
     * Récupère la distribution des documents par utilisateur
     */
    public function getDocumentDistributionByUser(): array
    {
        $sql = "
            SELECT
                CONCAT(COALESCE(u.prenom, ''), ' ', COALESCE(u.nom, '')) as user_name,
                COUNT(d.id) as count
            FROM document d
            LEFT JOIN released_package rp ON d.rel_pack_id = rp.id
            LEFT JOIN user u ON rp.owner_id = u.id
            WHERE u.id IS NOT NULL
            GROUP BY u.id, u.prenom, u.nom
            ORDER BY count DESC
            LIMIT 10
        ";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery()->fetchAllAssociative();

        $distribution = [];
        foreach ($result as $row) {
            $userName = trim($row['user_name']);
            if (!empty($userName)) {
                $distribution[$userName] = (int)$row['count'];
            }
        }

        return $distribution;
    }

    /**
     * Calcule les statistiques de navbar de manière optimisée
     */
    private function calculateNavbarStats(): array
    {
        try {
            return $this->calculateNavbarStatsNative();
        } catch (\Exception $e) {
            // Fallback vers l'ancienne méthode
            return $this->calculateNavbarStatsFallback();
        }
    }

    /**
     * Version optimisée avec requêtes SQL natives
     */
    private function calculateNavbarStatsNative(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        // Compter le total de documents
        $totalDocuments = $conn->executeQuery("SELECT COUNT(*) FROM document")->fetchOne();

        // Compter les documents sortis de BE (qui ont des state_timestamps)
        $documentsOutOfBE = $conn->executeQuery("
            SELECT COUNT(*) FROM document
            WHERE state_timestamps IS NOT NULL
            AND state_timestamps != '{}'
            AND state_timestamps != ''
        ")->fetchOne();

        // Calculer les statistiques de temps depuis BE
        $timeStats = $conn->executeQuery("
            SELECT
                AVG(DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter')))) as avg_days,
                MAX(DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter')))) as max_days
            FROM document
            WHERE JSON_EXTRACT(state_timestamps, '$.BE[0].enter') IS NOT NULL
        ")->fetchAssociative();

        $avgDaysSinceBE = $timeStats['avg_days'] ? round($timeStats['avg_days'], 1) : 0;
        $maxDaysSinceBE = $timeStats['max_days'] ? (int)$timeStats['max_days'] : 0;

        // Trouver le document avec le maximum de jours
        $documentWithMaxDays = null;
        if ($maxDaysSinceBE > 0) {
            $maxDocId = $conn->executeQuery("
                SELECT id FROM document
                WHERE JSON_EXTRACT(state_timestamps, '$.BE[0].enter') IS NOT NULL
                AND DATEDIFF(NOW(), JSON_UNQUOTE(JSON_EXTRACT(state_timestamps, '$.BE[0].enter'))) = ?
                LIMIT 1
            ", [$maxDaysSinceBE])->fetchOne();

            if ($maxDocId) {
                $documentWithMaxDays = $this->find($maxDocId);
            }
        }

        return [
            'totalDocuments' => (int)$totalDocuments,
            'documentsOutOfBE' => (int)$documentsOutOfBE,
            'avgDaysSinceBE' => $avgDaysSinceBE,
            'maxDaysSinceBE' => $maxDaysSinceBE,
            'documentWithMaxDays' => $documentWithMaxDays,
        ];
    }

    /**
     * Version de fallback utilisant l'ancienne logique
     */
    private function calculateNavbarStatsFallback(): array
    {
        // Utiliser une requête plus simple pour éviter de charger tous les documents
        $totalDocuments = $this->createQueryBuilder('d')
            ->select('COUNT(d.id)')
            ->getQuery()
            ->getSingleScalarResult();

        // Charger seulement les documents avec des timestamps pour les calculs
        $documentsWithTimestamps = $this->createQueryBuilder('d')
            ->where('d.stateTimestamps IS NOT NULL')
            ->andWhere('d.stateTimestamps != :empty1')
            ->andWhere('d.stateTimestamps != :empty2')
            ->setParameter('empty1', '{}')
            ->setParameter('empty2', '')
            ->getQuery()
            ->getResult();

        $documentsOutOfBE = 0;
        $totalDaysSinceBE = 0;
        $maxDaysSinceBE = 0;
        $documentWithMaxDays = null;

        foreach ($documentsWithTimestamps as $document) {
            $daysSinceBE = $document->getDaysSinceBE();
            if ($daysSinceBE !== null) {
                $documentsOutOfBE++;
                $totalDaysSinceBE += $daysSinceBE;

                if ($daysSinceBE > $maxDaysSinceBE) {
                    $maxDaysSinceBE = $daysSinceBE;
                    $documentWithMaxDays = $document;
                }
            }
        }

        $avgDaysSinceBE = $documentsOutOfBE > 0 ? round($totalDaysSinceBE / $documentsOutOfBE, 1) : 0;

        return [
            'totalDocuments' => (int)$totalDocuments,
            'documentsOutOfBE' => $documentsOutOfBE,
            'avgDaysSinceBE' => $avgDaysSinceBE,
            'maxDaysSinceBE' => $maxDaysSinceBE,
            'documentWithMaxDays' => $documentWithMaxDays,
        ];
    }

    // ========================================
    // STATISTIQUES TEMPORELLES - Phase 1.1
    // ========================================

    /**
     * Calcule le temps moyen passé dans chaque étape du workflow
     * @return array ['step_name' => average_days]
     */
    public function getAverageTimePerWorkflowStep(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                state_name,
                AVG(duration_days) as avg_duration
            FROM (
                SELECT
                    jt.state_name,
                    CASE
                        WHEN jt.exit_date IS NOT NULL AND jt.exit_date != ''
                        THEN DATEDIFF(
                            STR_TO_DATE(jt.exit_date, '%Y-%m-%d %H:%i:%s'),
                            STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                        )
                        ELSE DATEDIFF(
                            NOW(),
                            STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s')
                        )
                    END as duration_days
                FROM document d
                CROSS JOIN JSON_TABLE(
                    d.state_timestamps,
                    '$.*[*]' COLUMNS (
                        state_name VARCHAR(50) FOR ORDINALITY,
                        enter_date VARCHAR(255) PATH '$.enter',
                        exit_date VARCHAR(255) PATH '$.exit'
                    )
                ) jt
                WHERE d.state_timestamps IS NOT NULL
                AND d.state_timestamps != '{}'
                AND jt.enter_date IS NOT NULL
                AND jt.enter_date != ''
            ) duration_calc
            WHERE duration_days > 0
            GROUP BY state_name
            ORDER BY avg_duration DESC
        ";

        try {
            $result = $conn->executeQuery($sql)->fetchAllAssociative();
            $averages = [];
            foreach ($result as $row) {
                $averages[$row['state_name']] = round((float)$row['avg_duration'], 2);
            }
            return $averages;
        } catch (\Exception $e) {
            return $this->getAverageTimePerWorkflowStepFallback();
        }
    }

    /**
     * Version fallback pour le calcul du temps moyen par étape
     */
    private function getAverageTimePerWorkflowStepFallback(): array
    {
        $documents = $this->createQueryBuilder('d')
            ->where('d.stateTimestamps IS NOT NULL')
            ->andWhere('d.stateTimestamps != :empty1')
            ->andWhere('d.stateTimestamps != :empty2')
            ->setParameter('empty1', '{}')
            ->setParameter('empty2', '')
            ->getQuery()
            ->getResult();

        $stepTotals = [];
        $stepCounts = [];

        foreach ($documents as $document) {
            $timestamps = $document->getStateTimestamps();
            if (!is_array($timestamps)) continue;

            foreach ($timestamps as $step => $entries) {
                if (!is_array($entries)) continue;

                foreach ($entries as $entry) {
                    if (!isset($entry['enter'])) continue;

                    $enterDate = new \DateTime($entry['enter']);
                    $exitDate = isset($entry['exit']) && $entry['exit']
                        ? new \DateTime($entry['exit'])
                        : new \DateTime();

                    $duration = $exitDate->diff($enterDate)->days;

                    if (!isset($stepTotals[$step])) {
                        $stepTotals[$step] = 0;
                        $stepCounts[$step] = 0;
                    }

                    $stepTotals[$step] += $duration;
                    $stepCounts[$step]++;
                }
            }
        }

        $averages = [];
        foreach ($stepTotals as $step => $total) {
            if ($stepCounts[$step] > 0) {
                $averages[$step] = round($total / $stepCounts[$step], 2);
            }
        }

        arsort($averages);
        return $averages;
    }

    /**
     * Calcule le temps total de cycle pour les documents terminés
     * @return array ['avg_cycle_time' => days, 'min_cycle_time' => days, 'max_cycle_time' => days]
     */
    public function getCycleTimeStatistics(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                AVG(cycle_days) as avg_cycle,
                MIN(cycle_days) as min_cycle,
                MAX(cycle_days) as max_cycle,
                COUNT(*) as completed_count
            FROM (
                SELECT
                    d.id,
                    DATEDIFF(
                        COALESCE(
                            (SELECT v.date_visa FROM visa v
                             WHERE v.released_drawing_id = d.id
                             AND v.name = 'visa_Costing'
                             AND v.status = 'valid'
                             ORDER BY v.date_visa DESC LIMIT 1),
                            NOW()
                        ),
                        COALESCE(
                            (SELECT v2.date_visa FROM visa v2
                             WHERE v2.released_drawing_id = d.id
                             AND v2.name = 'visa_BE_0'
                             AND v2.status = 'valid'
                             ORDER BY v2.date_visa ASC LIMIT 1),
                            NOW()
                        )
                    ) as cycle_days
                FROM document d
                WHERE EXISTS (
                    SELECT 1 FROM visa v1
                    WHERE v1.released_drawing_id = d.id
                    AND v1.name = 'visa_BE_0'
                    AND v1.status = 'valid'
                )
            ) cycle_calc
            WHERE cycle_days > 0
        ";

        try {
            $result = $conn->executeQuery($sql)->fetchAssociative();
            return [
                'avg_cycle_time' => $result['avg_cycle'] ? round((float)$result['avg_cycle'], 2) : 0,
                'min_cycle_time' => $result['min_cycle'] ? (int)$result['min_cycle'] : 0,
                'max_cycle_time' => $result['max_cycle'] ? (int)$result['max_cycle'] : 0,
                'completed_count' => (int)$result['completed_count']
            ];
        } catch (\Exception $e) {
            return $this->getCycleTimeStatisticsFallback();
        }
    }

    /**
     * Version fallback pour les statistiques de temps de cycle
     */
    private function getCycleTimeStatisticsFallback(): array
    {
        $documents = $this->createQueryBuilder('d')
            ->innerJoin('d.visas', 'v_be0', 'WITH', 'v_be0.name = :visa_be0 AND v_be0.status = :valid')
            ->setParameter('visa_be0', 'visa_BE_0')
            ->setParameter('valid', 'valid')
            ->getQuery()
            ->getResult();

        $cycleTimes = [];

        foreach ($documents as $document) {
            $be0Date = $document->getVisaDate('visa_BE_0');
            $costingDate = $document->getVisaDate('visa_Costing');

            if ($be0Date) {
                $endDate = $costingDate ?: new \DateTime();
                $cycleTime = $endDate->diff($be0Date)->days;
                if ($cycleTime > 0) {
                    $cycleTimes[] = $cycleTime;
                }
            }
        }

        if (empty($cycleTimes)) {
            return [
                'avg_cycle_time' => 0,
                'min_cycle_time' => 0,
                'max_cycle_time' => 0,
                'completed_count' => 0
            ];
        }

        return [
            'avg_cycle_time' => round(array_sum($cycleTimes) / count($cycleTimes), 2),
            'min_cycle_time' => min($cycleTimes),
            'max_cycle_time' => max($cycleTimes),
            'completed_count' => count($cycleTimes)
        ];
    }

    // ========================================
    // STATISTIQUES DE VISAS - Phase 1.2
    // ========================================

    /**
     * Calcule les statistiques des visas par étape
     * @return array ['step' => ['total' => int, 'approved' => int, 'pending' => int, 'avg_approval_time' => float]]
     */
    public function getVisaStatisticsByStep(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                SUBSTRING(v.name, 6) as step_name,
                COUNT(*) as total_visas,
                SUM(CASE WHEN v.status = 'valid' THEN 1 ELSE 0 END) as approved_visas,
                SUM(CASE WHEN v.status != 'valid' THEN 1 ELSE 0 END) as pending_visas,
                AVG(
                    CASE WHEN v.status = 'valid'
                    THEN TIMESTAMPDIFF(HOUR,
                        COALESCE(
                            (SELECT MIN(STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s'))
                             FROM JSON_TABLE(d.state_timestamps, CONCAT('$.', SUBSTRING(v.name, 6), '[*]')
                                COLUMNS (enter_date VARCHAR(255) PATH '$.enter')) jt
                             WHERE jt.enter_date IS NOT NULL),
                            v.date_visa
                        ),
                        v.date_visa
                    )
                    ELSE NULL END
                ) as avg_approval_hours
            FROM visa v
            INNER JOIN document d ON v.released_drawing_id = d.id
            WHERE v.name LIKE 'visa_%'
            GROUP BY SUBSTRING(v.name, 6)
            ORDER BY total_visas DESC
        ";

        try {
            $result = $conn->executeQuery($sql)->fetchAllAssociative();
            $statistics = [];

            foreach ($result as $row) {
                $statistics[$row['step_name']] = [
                    'total' => (int)$row['total_visas'],
                    'approved' => (int)$row['approved_visas'],
                    'pending' => (int)$row['pending_visas'],
                    'approval_rate' => $row['total_visas'] > 0
                        ? round(($row['approved_visas'] / $row['total_visas']) * 100, 2)
                        : 0,
                    'avg_approval_time_hours' => $row['avg_approval_hours']
                        ? round((float)$row['avg_approval_hours'], 2)
                        : null
                ];
            }

            return $statistics;
        } catch (\Exception $e) {
            return $this->getVisaStatisticsByStepFallback();
        }
    }

    /**
     * Version fallback pour les statistiques de visas
     */
    private function getVisaStatisticsByStepFallback(): array
    {
        $visas = $this->getEntityManager()
            ->getRepository('App\Entity\Visa')
            ->createQueryBuilder('v')
            ->innerJoin('v.releasedDrawing', 'd')
            ->where('v.name LIKE :visa_prefix')
            ->setParameter('visa_prefix', 'visa_%')
            ->getQuery()
            ->getResult();

        $statistics = [];

        foreach ($visas as $visa) {
            $stepName = substr($visa->getName(), 5); // Remove 'visa_' prefix

            if (!isset($statistics[$stepName])) {
                $statistics[$stepName] = [
                    'total' => 0,
                    'approved' => 0,
                    'pending' => 0,
                    'approval_times' => []
                ];
            }

            $statistics[$stepName]['total']++;

            if ($visa->getStatus() === 'valid') {
                $statistics[$stepName]['approved']++;

                // Try to calculate approval time
                $document = $visa->getReleasedDrawing();
                $stateTimestamps = $document->getStateTimestamps();

                if (is_array($stateTimestamps) && isset($stateTimestamps[$stepName])) {
                    $stepEntries = $stateTimestamps[$stepName];
                    if (is_array($stepEntries) && !empty($stepEntries)) {
                        $firstEntry = reset($stepEntries);
                        if (isset($firstEntry['enter'])) {
                            $enterTime = new \DateTime($firstEntry['enter']);
                            $approvalTime = $visa->getDateVisa();
                            if ($approvalTime) {
                                $diffHours = ($approvalTime->getTimestamp() - $enterTime->getTimestamp()) / 3600;
                                if ($diffHours > 0) {
                                    $statistics[$stepName]['approval_times'][] = $diffHours;
                                }
                            }
                        }
                    }
                }
            } else {
                $statistics[$stepName]['pending']++;
            }
        }

        // Calculate final statistics
        foreach ($statistics as $step => &$stats) {
            $stats['approval_rate'] = $stats['total'] > 0
                ? round(($stats['approved'] / $stats['total']) * 100, 2)
                : 0;

            $stats['avg_approval_time_hours'] = !empty($stats['approval_times'])
                ? round(array_sum($stats['approval_times']) / count($stats['approval_times']), 2)
                : null;

            unset($stats['approval_times']); // Remove temporary array
        }

        return $statistics;
    }

    /**
     * Trouve les documents en attente de visa par utilisateur
     * @param int|null $userId Si null, retourne pour tous les utilisateurs
     * @return array
     */
    public function getDocumentsPendingVisaByUser(?int $userId = null): array
    {
        $qb = $this->createQueryBuilder('d')
            ->select('d.id, d.reference, d.refRev, u.id as user_id, u.prenom, u.nom, u.departement')
            ->innerJoin('d.visas', 'v')
            ->innerJoin('v.validator', 'u')
            ->where('v.status != :valid_status')
            ->setParameter('valid_status', 'valid')
            ->orderBy('u.departement', 'ASC')
            ->addOrderBy('u.nom', 'ASC');

        if ($userId) {
            $qb->andWhere('u.id = :user_id')
               ->setParameter('user_id', $userId);
        }

        return $qb->getQuery()->getArrayResult();
    }

    /**
     * Calcule les performances des validateurs
     * @return array ['user_id' => ['name' => string, 'total_visas' => int, 'avg_response_time' => float, 'pending_count' => int]]
     */
    public function getValidatorPerformanceStatistics(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                u.id as user_id,
                CONCAT(u.prenom, ' ', u.nom) as user_name,
                u.departement,
                COUNT(v.id) as total_visas,
                SUM(CASE WHEN v.status = 'valid' THEN 1 ELSE 0 END) as approved_visas,
                SUM(CASE WHEN v.status != 'valid' THEN 1 ELSE 0 END) as pending_visas,
                AVG(
                    CASE WHEN v.status = 'valid' AND v.date_visa IS NOT NULL
                    THEN TIMESTAMPDIFF(HOUR,
                        COALESCE(
                            (SELECT MIN(STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s'))
                             FROM JSON_TABLE(d.state_timestamps, CONCAT('$.', SUBSTRING(v.name, 6), '[*]')
                                COLUMNS (enter_date VARCHAR(255) PATH '$.enter')) jt
                             WHERE jt.enter_date IS NOT NULL),
                            DATE_SUB(v.date_visa, INTERVAL 24 HOUR)
                        ),
                        v.date_visa
                    )
                    ELSE NULL END
                ) as avg_response_hours
            FROM user u
            INNER JOIN visa v ON u.id = v.validator_id
            INNER JOIN document d ON v.released_drawing_id = d.id
            GROUP BY u.id, u.prenom, u.nom, u.departement
            HAVING total_visas > 0
            ORDER BY total_visas DESC
        ";

        try {
            $result = $conn->executeQuery($sql)->fetchAllAssociative();
            $performance = [];

            foreach ($result as $row) {
                $performance[$row['user_id']] = [
                    'name' => trim($row['user_name']),
                    'department' => $row['departement'],
                    'total_visas' => (int)$row['total_visas'],
                    'approved_visas' => (int)$row['approved_visas'],
                    'pending_visas' => (int)$row['pending_visas'],
                    'approval_rate' => $row['total_visas'] > 0
                        ? round(($row['approved_visas'] / $row['total_visas']) * 100, 2)
                        : 0,
                    'avg_response_time_hours' => $row['avg_response_hours']
                        ? round((float)$row['avg_response_hours'], 2)
                        : null
                ];
            }

            return $performance;
        } catch (\Exception $e) {
            return $this->getValidatorPerformanceStatisticsFallback();
        }
    }

    /**
     * Version fallback pour les performances des validateurs
     */
    private function getValidatorPerformanceStatisticsFallback(): array
    {
        $users = $this->getEntityManager()
            ->getRepository('App\Entity\User')
            ->createQueryBuilder('u')
            ->innerJoin('u.visas', 'v')
            ->groupBy('u.id')
            ->getQuery()
            ->getResult();

        $performance = [];

        foreach ($users as $user) {
            $visas = $user->getVisas();
            $totalVisas = $visas->count();
            $approvedVisas = 0;
            $responseTimes = [];

            foreach ($visas as $visa) {
                if ($visa->getStatus() === 'valid') {
                    $approvedVisas++;

                    // Simple response time calculation (24h default if no state info)
                    if ($visa->getDateVisa()) {
                        $responseTimes[] = 24; // Default 24 hours
                    }
                }
            }

            if ($totalVisas > 0) {
                $performance[$user->getId()] = [
                    'name' => trim($user->getPrenom() . ' ' . $user->getNom()),
                    'department' => $user->getDepartement(),
                    'total_visas' => $totalVisas,
                    'approved_visas' => $approvedVisas,
                    'pending_visas' => $totalVisas - $approvedVisas,
                    'approval_rate' => round(($approvedVisas / $totalVisas) * 100, 2),
                    'avg_response_time_hours' => !empty($responseTimes)
                        ? round(array_sum($responseTimes) / count($responseTimes), 2)
                        : null
                ];
            }
        }

        return $performance;
    }

    // ========================================
    // STATISTIQUES DE DISTRIBUTION - Phase 1.3
    // ========================================

    /**
     * Obtient la distribution détaillée des documents par type
     * @return array ['docType' => ['procType' => ['matProdType' => count]]]
     */
    public function getDetailedDocumentTypeDistribution(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                COALESCE(d.doc_type, 'Unknown') as doc_type,
                COALESCE(d.proc_type, 'Unknown') as proc_type,
                COALESCE(d.mat_prod_type, 'Unknown') as mat_prod_type,
                COUNT(*) as document_count
            FROM document d
            GROUP BY d.doc_type, d.proc_type, d.mat_prod_type
            ORDER BY document_count DESC
        ";

        try {
            $result = $conn->executeQuery($sql)->fetchAllAssociative();
            $distribution = [];

            foreach ($result as $row) {
                $docType = $row['doc_type'];
                $procType = $row['proc_type'];
                $matProdType = $row['mat_prod_type'];
                $count = (int)$row['document_count'];

                if (!isset($distribution[$docType])) {
                    $distribution[$docType] = [];
                }
                if (!isset($distribution[$docType][$procType])) {
                    $distribution[$docType][$procType] = [];
                }
                $distribution[$docType][$procType][$matProdType] = $count;
            }

            return $distribution;
        } catch (\Exception $e) {
            return $this->getDetailedDocumentTypeDistributionFallback();
        }
    }

    /**
     * Version fallback pour la distribution détaillée par type
     */
    private function getDetailedDocumentTypeDistributionFallback(): array
    {
        $documents = $this->findAll();
        $distribution = [];

        foreach ($documents as $document) {
            $docType = $document->getDocType() ?: 'Unknown';
            $procType = $document->getProcType() ?: 'Unknown';
            $matProdType = $document->getMatProdType() ?: 'Unknown';

            if (!isset($distribution[$docType])) {
                $distribution[$docType] = [];
            }
            if (!isset($distribution[$docType][$procType])) {
                $distribution[$docType][$procType] = [];
            }
            if (!isset($distribution[$docType][$procType][$matProdType])) {
                $distribution[$docType][$procType][$matProdType] = 0;
            }

            $distribution[$docType][$procType][$matProdType]++;
        }

        return $distribution;
    }

    /**
     * Obtient la distribution des documents par projet et DMO
     * @return array ['projects' => [...], 'dmos' => [...], 'standalone' => int]
     */
    public function getDocumentDistributionByProjectAndDMO(): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                'project' as type,
                p.id as entity_id,
                p.otp as entity_name,
                p.title as entity_description,
                COUNT(d.id) as document_count
            FROM document d
            INNER JOIN released_package rp ON d.rel_pack_id = rp.id
            INNER JOIN project p ON rp.project_relation_id = p.id
            GROUP BY p.id, p.otp, p.title

            UNION ALL

            SELECT
                'dmo' as type,
                dmo.id as entity_id,
                dmo.dmo as entity_name,
                dmo.description as entity_description,
                COUNT(DISTINCT d.id) as document_count
            FROM document d
            INNER JOIN released_package rp ON d.rel_pack_id = rp.id
            INNER JOIN released_package_dmo rpd ON rp.id = rpd.released_package_id
            INNER JOIN dmo ON rpd.dmo_id = dmo.id
            GROUP BY dmo.id, dmo.dmo, dmo.description

            ORDER BY document_count DESC
        ";

        try {
            $result = $conn->executeQuery($sql)->fetchAllAssociative();

            $distribution = [
                'projects' => [],
                'dmos' => [],
                'standalone' => 0
            ];

            foreach ($result as $row) {
                $item = [
                    'id' => (int)$row['entity_id'],
                    'name' => $row['entity_name'],
                    'description' => $row['entity_description'],
                    'document_count' => (int)$row['document_count']
                ];

                if ($row['type'] === 'project') {
                    $distribution['projects'][] = $item;
                } else {
                    $distribution['dmos'][] = $item;
                }
            }

            // Count standalone documents (no project or DMO)
            $standaloneCount = $conn->executeQuery("
                SELECT COUNT(*) as count
                FROM document d
                LEFT JOIN released_package rp ON d.rel_pack_id = rp.id
                WHERE rp.id IS NULL
                OR (rp.project_relation_id IS NULL
                    AND NOT EXISTS (
                        SELECT 1 FROM released_package_dmo rpd
                        WHERE rpd.released_package_id = rp.id
                    ))
            ")->fetchOne();

            $distribution['standalone'] = (int)$standaloneCount;

            return $distribution;
        } catch (\Exception $e) {
            return $this->getDocumentDistributionByProjectAndDMOFallback();
        }
    }

    /**
     * Version fallback pour la distribution par projet et DMO
     */
    private function getDocumentDistributionByProjectAndDMOFallback(): array
    {
        $documents = $this->createQueryBuilder('d')
            ->leftJoin('d.relPack', 'rp')
            ->leftJoin('rp.projectRelation', 'p')
            ->leftJoin('rp.dmos', 'dmo')
            ->getQuery()
            ->getResult();

        $distribution = [
            'projects' => [],
            'dmos' => [],
            'standalone' => 0
        ];

        $projectCounts = [];
        $dmoCounts = [];

        foreach ($documents as $document) {
            $relPack = $document->getRelPack();

            if (!$relPack) {
                $distribution['standalone']++;
                continue;
            }

            $project = $relPack->getProjectRelation();
            $dmos = $relPack->getDmos();

            if ($project) {
                $projectId = $project->getId();
                if (!isset($projectCounts[$projectId])) {
                    $projectCounts[$projectId] = [
                        'id' => $projectId,
                        'name' => $project->getOTP(),
                        'description' => $project->getTitle(),
                        'document_count' => 0
                    ];
                }
                $projectCounts[$projectId]['document_count']++;
            }

            foreach ($dmos as $dmo) {
                $dmoId = $dmo->getId();
                if (!isset($dmoCounts[$dmoId])) {
                    $dmoCounts[$dmoId] = [
                        'id' => $dmoId,
                        'name' => $dmo->getDmoId(),
                        'description' => $dmo->getDescription(),
                        'document_count' => 0
                    ];
                }
                $dmoCounts[$dmoId]['document_count']++;
            }

            if (!$project && $dmos->isEmpty()) {
                $distribution['standalone']++;
            }
        }

        $distribution['projects'] = array_values($projectCounts);
        $distribution['dmos'] = array_values($dmoCounts);

        // Sort by document count
        usort($distribution['projects'], fn($a, $b) => $b['document_count'] - $a['document_count']);
        usort($distribution['dmos'], fn($a, $b) => $b['document_count'] - $a['document_count']);

        return $distribution;
    }

    /**
     * Obtient les tendances temporelles de création de documents
     * @param int $months Nombre de mois à analyser (défaut: 12)
     * @return array ['month' => 'YYYY-MM', 'count' => int, 'cumulative' => int]
     */
    public function getDocumentCreationTrends(int $months = 12): array
    {
        $conn = $this->getEntityManager()->getConnection();

        $sql = "
            SELECT
                DATE_FORMAT(creation_date, '%Y-%m') as month,
                COUNT(*) as count
            FROM (
                SELECT
                    COALESCE(
                        rp.creation_date,
                        (SELECT MIN(STR_TO_DATE(jt.enter_date, '%Y-%m-%d %H:%i:%s'))
                         FROM JSON_TABLE(d.state_timestamps, '$.BE_0[*]'
                            COLUMNS (enter_date VARCHAR(255) PATH '$.enter')) jt
                         WHERE jt.enter_date IS NOT NULL),
                        NOW()
                    ) as creation_date
                FROM document d
                LEFT JOIN released_package rp ON d.rel_pack_id = rp.id
                WHERE COALESCE(
                    rp.creation_date,
                    (SELECT MIN(STR_TO_DATE(jt2.enter_date, '%Y-%m-%d %H:%i:%s'))
                     FROM JSON_TABLE(d.state_timestamps, '$.BE_0[*]'
                        COLUMNS (enter_date VARCHAR(255) PATH '$.enter')) jt2
                     WHERE jt2.enter_date IS NOT NULL),
                    NOW()
                ) >= DATE_SUB(NOW(), INTERVAL ? MONTH)
            ) doc_dates
            GROUP BY DATE_FORMAT(creation_date, '%Y-%m')
            ORDER BY month ASC
        ";

        try {
            $result = $conn->executeQuery($sql, [$months])->fetchAllAssociative();

            $trends = [];
            $cumulative = 0;

            foreach ($result as $row) {
                $count = (int)$row['count'];
                $cumulative += $count;

                $trends[] = [
                    'month' => $row['month'],
                    'count' => $count,
                    'cumulative' => $cumulative
                ];
            }

            return $trends;
        } catch (\Exception $e) {
            return $this->getDocumentCreationTrendsFallback($months);
        }
    }

    /**
     * Version fallback pour les tendances de création
     */
    private function getDocumentCreationTrendsFallback(int $months): array
    {
        $cutoffDate = new \DateTime();
        $cutoffDate->modify("-{$months} months");

        $documents = $this->createQueryBuilder('d')
            ->leftJoin('d.relPack', 'rp')
            ->getQuery()
            ->getResult();

        $monthCounts = [];

        foreach ($documents as $document) {
            $creationDate = null;

            // Try to get creation date from released package
            if ($document->getRelPack() && $document->getRelPack()->getCreationDate()) {
                $creationDate = $document->getRelPack()->getCreationDate();
            } else {
                // Try to get from BE_0 timestamp
                $timestamps = $document->getStateTimestamps();
                if (is_array($timestamps) && isset($timestamps['BE_0']) && is_array($timestamps['BE_0'])) {
                    $firstEntry = reset($timestamps['BE_0']);
                    if (isset($firstEntry['enter'])) {
                        $creationDate = new \DateTime($firstEntry['enter']);
                    }
                }
            }

            if ($creationDate && $creationDate >= $cutoffDate) {
                $monthKey = $creationDate->format('Y-m');
                if (!isset($monthCounts[$monthKey])) {
                    $monthCounts[$monthKey] = 0;
                }
                $monthCounts[$monthKey]++;
            }
        }

        ksort($monthCounts);

        $trends = [];
        $cumulative = 0;

        foreach ($monthCounts as $month => $count) {
            $cumulative += $count;
            $trends[] = [
                'month' => $month,
                'count' => $count,
                'cumulative' => $cumulative
            ];
        }

        return $trends;
    }

}
