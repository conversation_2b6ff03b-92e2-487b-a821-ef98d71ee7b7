<?php

namespace App\Service;

use App\Entity\Document;
use App\Entity\User;
use App\Entity\Visa;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;
use Symfony\Component\Workflow\Registry;

/**
 * Service pour gérer la réinitialisation des champs lors du changement de doctype
 * Reproduit la logique de l'ancien système avec les modules Quality, Production, Purchasing, Product
 */
class DoctypeResetService
{
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;
    private Registry $workflowRegistry;

    public function __construct(EntityManagerInterface $entityManager, LoggerInterface $logger, Registry $workflowRegistry)
    {
        $this->entityManager = $entityManager;
        $this->logger = $logger;
        $this->workflowRegistry = $workflowRegistry;
    }

    /**
     * Réinitialise les champs d'un document lors du changement de doctype
     *
     * @param Document $document Le document à réinitialiser
     * @param string $oldDocType L'ancien type de document
     * @param string $newDocType Le nouveau type de document
     * @param User $user L'utilisateur qui effectue le changement
     * @return array Résultat de la réinitialisation avec les champs modifiés
     */
    public function resetDocumentForDoctypeChange(Document $document, string $oldDocType, string $newDocType, User $user): array
    {
        $resetFields = [];
        $comments = [];
        $workflowChanges = [];

        // Logique spéciale pour le doctype DOC
        if ($newDocType === 'DOC') {
            $resetFields = array_merge($resetFields, $this->handleDocTypeChange($document));
            $comments[] = "[" . (new \DateTime())->format('Y-m-d H:i:s') . "] - Document type changed to DOC: Material Type set to LITERATURE";

            // Changer la place vers Quality pour les documents DOC
            $workflowChanges = $this->handleWorkflowChangeToDoc($document, $user);
        }

        // Logique spéciale pour sortir du doctype DOC
        if ($oldDocType === 'DOC' && $newDocType !== 'DOC') {
            $resetFields = array_merge($resetFields, $this->handleExitFromDocType($document));
            $comments[] = "[" . (new \DateTime())->format('Y-m-d H:i:s') . "] - Document type changed from DOC: Material Type reset";
        }

        // Déterminer les places affectées par le changement
        $affectedPlaces = $this->getAffectedPlaces($document, $oldDocType, $newDocType);

        // Réinitialiser les champs selon les places affectées
        foreach ($affectedPlaces as $place) {
            $placeResetResult = $this->resetFieldsForPlace($document, $place, $oldDocType, $newDocType, $user);
            $resetFields = array_merge($resetFields, $placeResetResult['fields']);
            $comments = array_merge($comments, $placeResetResult['comments']);
        }

        // Gérer les changements de workflow (changement de place + visas) pour tous les cas sauf DOC
        if ($newDocType !== 'DOC') {
            $workflowResult = $this->handleWorkflowChange($document, $oldDocType, $newDocType, $user);
            $workflowChanges = array_merge($workflowChanges, $workflowResult);
        }

        // Ajouter les commentaires de traçabilité
        foreach ($comments as $comment) {
            $document->addUpdate('doctype_change', $user, $comment);
        }

        // Log de l'opération
        $this->logger->info('Document doctype changed', [
            'document_id' => $document->getId(),
            'old_doctype' => $oldDocType,
            'new_doctype' => $newDocType,
            'reset_fields' => $resetFields,
            'workflow_changes' => $workflowChanges,
            'user_id' => $user->getId()
        ]);

        return [
            'success' => true,
            'reset_fields' => $resetFields,
            'comments' => $comments,
            'workflow_changes' => $workflowChanges
        ];
    }

    /**
     * Détermine quelles places du workflow sont affectées par le changement de doctype
     */
    private function getAffectedPlaces(Document $document, string $oldDocType, string $newDocType): array
    {
        $places = [];

        // Places toujours affectées lors d'un changement de doctype
        $places[] = 'Quality';
        $places[] = 'Produit';

        // Places selon l'ancien doctype
        $oldPlaces = $this->getPlacesForDocType($oldDocType);
        $places = array_merge($places, $oldPlaces);

        // Places selon le nouveau doctype (pour savoir où aller)
        $newPlaces = $this->getPlacesForDocType($newDocType);

        // Ajouter les places d'achat si concernées
        $currentSteps = $document->getCurrentSteps();
        if ($currentSteps) {
            $purchasingPlaces = ['Achat_Rfq', 'Achat_F30', 'Achat_FIA', 'Achat_Hts', 'Achat_RoHs_REACH'];
            foreach ($purchasingPlaces as $purchasingPlace) {
                if (array_key_exists($purchasingPlace, $currentSteps)) {
                    $places[] = $purchasingPlace;
                }
            }
        }

        return array_unique($places);
    }

    /**
     * Retourne les places du workflow associées à un doctype
     */
    private function getPlacesForDocType(string $docType): array
    {
        $doctypePlaces = [
            'MACH' => ['Machining'],
            'MOLD' => ['Molding'],
            'ASSY' => ['Assembly'],
            'PUR' => ['Achat_Rfq', 'Achat_F30', 'Achat_FIA', 'Achat_Hts', 'Achat_RoHs_REACH'],
            'DOC' => ['Quality']
        ];

        return $doctypePlaces[$docType] ?? [];
    }

    /**
     * Réinitialise les champs pour une place spécifique du workflow
     */
    private function resetFieldsForPlace(Document $document, string $place, string $oldDocType, string $newDocType, User $user): array
    {
        $resetFields = [];
        $comments = [];
        $currentDate = new \DateTime();
        $dateString = $currentDate->format('Y-m-d H:i:s');

        switch ($place) {
            case 'Quality':
                $resetFields = array_merge($resetFields, $this->resetQualityFields($document));
                $comments[] = "[$dateString] - Quality : change of supply to $newDocType";
                break;

            case 'Assembly':
            case 'Machining':
            case 'Molding':
                $resetFields = array_merge($resetFields, $this->resetProductionFields($document));
                $comments[] = "[$dateString] - Production ($place) : change of supply to $newDocType";
                break;

            case 'Achat_Rfq':
            case 'Achat_F30':
            case 'Achat_FIA':
            case 'Achat_Hts':
            case 'Achat_RoHs_REACH':
                $resetFields = array_merge($resetFields, $this->resetPurchasingFields($document));
                $comments[] = "[$dateString] - Purchasing ($place) : change of supply to $newDocType";
                break;

            case 'Produit':
                $resetFields = array_merge($resetFields, $this->resetProductFields($document));
                $comments[] = "[$dateString] - Product : change of supply to $newDocType";
                break;

            case 'Metro':
                $resetFields = array_merge($resetFields, $this->resetMetroFields($document));
                $comments[] = "[$dateString] - Metro : change of supply to $newDocType";
                break;
        }

        return [
            'fields' => $resetFields,
            'comments' => $comments
        ];
    }

    /**
     * Réinitialise les champs du module Quality
     */
    private function resetQualityFields(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser procType
        if ($document->getProcType() !== null) {
            $document->setProcType(null);
            $resetFields[] = 'procType';
        }

        // Réinitialiser metroTime
        if ($document->getMetroTime() !== null) {
            $document->setMetroTime(null);
            $resetFields[] = 'metroTime';
        }

        // Réinitialiser metroControl
        if ($document->getMetroControl() !== null) {
            $document->setMetroControl([]);
            $resetFields[] = 'metroControl';
        }

        // Supprimer les visas Quality, Prod, Metro
        $visasToRemove = ['visa_Quality', 'visa_prod', 'visa_Metro'];
        $resetFields = array_merge($resetFields, $this->removeVisas($document, $visasToRemove));

        return $resetFields;
    }

    /**
     * Réinitialise les champs du module Production
     */
    private function resetProductionFields(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser procType
        if ($document->getProcType() !== null) {
            $document->setProcType(null);
            $resetFields[] = 'procType';
        }

        // Réinitialiser leadtime
        if ($document->getLeadtime() !== null) {
            $document->setLeadtime(0);
            $resetFields[] = 'leadtime';
        }

        // Réinitialiser mof
        if ($document->getMof() !== null) {
            $document->setMof(null);
            $resetFields[] = 'mof';
        }

        // Réinitialiser prodAgent
        if ($document->getProdAgent() !== null) {
            $document->setProdAgent(null);
            $resetFields[] = 'prodAgent';
        }

        // Réinitialiser prisDans1 et prisDans2
        if ($document->getPrisDans1() !== null) {
            $document->setPrisDans1(null);
            $resetFields[] = 'prisDans1';
        }
        if ($document->getPrisDans2() !== null) {
            $document->setPrisDans2(null);
            $resetFields[] = 'prisDans2';
        }

        // Supprimer les visas Quality et Prod
        $visasToRemove = ['visa_Quality', 'visa_prod'];
        $resetFields = array_merge($resetFields, $this->removeVisas($document, $visasToRemove));

        return $resetFields;
    }

    /**
     * Réinitialise les champs du module Purchasing
     */
    private function resetPurchasingFields(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser commodityCode
        if ($document->getCommodityCode() !== null) {
            $document->setCommodityCode(null);
            $resetFields[] = 'commodityCode';
        }

        // Réinitialiser purchasingGroup
        if ($document->getPurchasingGroup() !== null) {
            $document->setPurchasingGroup(null);
            $resetFields[] = 'purchasingGroup';
        }

        // Réinitialiser procType
        if ($document->getProcType() !== null) {
            $document->setProcType(null);
            $resetFields[] = 'procType';
        }

        // Réinitialiser prisDans1 et prisDans2
        if ($document->getPrisDans1() !== null) {
            $document->setPrisDans1(null);
            $resetFields[] = 'prisDans1';
        }
        if ($document->getPrisDans2() !== null) {
            $document->setPrisDans2(null);
            $resetFields[] = 'prisDans2';
        }

        // Réinitialiser fia
        if ($document->getFia() !== null) {
            $document->setFia(null);
            $resetFields[] = 'fia';
        }

        // Réinitialiser metroTime
        if ($document->getMetroTime() !== null) {
            $document->setMetroTime(null);
            $resetFields[] = 'metroTime';
        }

        // Réinitialiser metroControl
        if ($document->getMetroControl() !== null) {
            $document->setMetroControl([]);
            $resetFields[] = 'metroControl';
        }

        // Supprimer tous les visas d'achat et qualité
        $visasToRemove = [
            'visa_Achat_Rfq', 'visa_Achat_F30', 'visa_Achat_FIA',
            'visa_Achat_RoHs_REACH', 'visa_Achat_Hts',
            'visa_Quality', 'visa_Metro'
        ];
        $resetFields = array_merge($resetFields, $this->removeVisas($document, $visasToRemove));

        return $resetFields;
    }

    /**
     * Réinitialise les champs du module Product
     */
    private function resetProductFields(Document $document): array
    {
        $resetFields = [];

        // Supprimer le visa Product
        $visasToRemove = ['visa_Produit'];
        $resetFields = array_merge($resetFields, $this->removeVisas($document, $visasToRemove));

        return $resetFields;
    }

    /**
     * Supprime les visas spécifiés du document
     */
    private function removeVisas(Document $document, array $visaNames): array
    {
        $removedVisas = [];

        foreach ($document->getVisas() as $visa) {
            if (in_array($visa->getName(), $visaNames)) {
                $document->removeVisa($visa);
                $this->entityManager->remove($visa);
                $removedVisas[] = 'visa_' . $visa->getName();
            }
        }

        return $removedVisas;
    }

    /**
     * Gère les changements de workflow lors du changement de doctype
     */
    private function handleWorkflowChange(Document $document, string $oldDocType, string $newDocType, User $user): array
    {
        $changes = [];

        // Obtenir le workflow
        $workflow = $this->workflowRegistry->get($document, 'document_workflow');
        $currentPlaces = $workflow->getMarking($document)->getPlaces();

        // Déterminer les nouvelles places selon le nouveau doctype
        $newPlaces = $this->getDestinationPlacesForDoctype($newDocType);
        $oldPlaces = $this->getDestinationPlacesForDoctype($oldDocType);

        // Supprimer les visas des anciennes places
        $this->removeVisasForPlaces($document, $oldPlaces);
        $changes[] = "Removed visas for old doctype places: " . implode(', ', $oldPlaces);

        // Changer les places dans le workflow
        $newCurrentSteps = [];
        foreach ($newPlaces as $place) {
            $newCurrentSteps[$place] = 1;
        }

        // Conserver les places qui ne sont pas liées au doctype
        $preservedPlaces = ['BE_0', 'BE_1', 'BE', 'Produit', 'Project', 'Core_Data', 'GID', 'Costing'];
        foreach ($currentPlaces as $place => $value) {
            if (in_array($place, $preservedPlaces)) {
                $newCurrentSteps[$place] = $value;
            }
        }

        $document->setCurrentSteps($newCurrentSteps);
        $changes[] = "Changed workflow places to: " . implode(', ', array_keys($newCurrentSteps));

        return $changes;
    }

    /**
     * Gère le changement de workflow vers DOC
     */
    private function handleWorkflowChangeToDoc(Document $document, User $user): array
    {
        $changes = [];

        // Pour les documents DOC, aller vers Quality
        $workflow = $this->workflowRegistry->get($document, 'document_workflow');
        $currentPlaces = $workflow->getMarking($document)->getPlaces();

        // Supprimer les visas des places de production/achat
        $productionPurchasingPlaces = ['Assembly', 'Machining', 'Molding', 'Achat_Rfq', 'Achat_F30', 'Achat_FIA', 'Achat_Hts', 'Achat_RoHs_REACH'];
        $this->removeVisasForPlaces($document, $productionPurchasingPlaces);

        // Définir Quality comme place active
        $newCurrentSteps = [];
        $preservedPlaces = ['BE_0', 'BE_1', 'BE', 'Produit', 'Project', 'Core_Data', 'GID', 'Costing'];
        foreach ($currentPlaces as $place => $value) {
            if (in_array($place, $preservedPlaces)) {
                $newCurrentSteps[$place] = $value;
            }
        }
        $newCurrentSteps['Quality'] = 1;

        $document->setCurrentSteps($newCurrentSteps);
        $changes[] = "Changed workflow to Quality for DOC type";

        return $changes;
    }

    /**
     * Retourne les places de destination selon le doctype
     */
    private function getDestinationPlacesForDoctype(string $docType): array
    {
        $destinations = [
            'MACH' => ['Machining'],
            'MOLD' => ['Molding'],
            'ASSY' => ['Assembly'],
            'PUR' => ['Quality'], // Les documents PUR vont vers Quality d'abord
            'DOC' => ['Quality']
        ];

        return $destinations[$docType] ?? ['Quality'];
    }

    /**
     * Supprime les visas pour des places spécifiques
     */
    private function removeVisasForPlaces(Document $document, array $places): array
    {
        $removedVisas = [];

        // Mapping des places vers les noms de visas
        $placeToVisaMapping = [
            'Assembly' => 'visa_prod',
            'Machining' => 'visa_prod',
            'Molding' => 'visa_prod',
            'Quality' => 'visa_Quality',
            'Achat_Rfq' => 'visa_Achat_Rfq',
            'Achat_F30' => 'visa_Achat_F30',
            'Achat_FIA' => 'visa_Achat_FIA',
            'Achat_Hts' => 'visa_Achat_Hts',
            'Achat_RoHs_REACH' => 'visa_Achat_RoHs_REACH',
            'Metro' => 'visa_Metro',
            'Produit' => 'visa_Produit'
        ];

        foreach ($places as $place) {
            if (isset($placeToVisaMapping[$place])) {
                $visaName = $placeToVisaMapping[$place];
                foreach ($document->getVisas() as $visa) {
                    if ($visa->getName() === $visaName) {
                        $document->removeVisa($visa);
                        $this->entityManager->remove($visa);
                        $removedVisas[] = $visaName;
                    }
                }
            }
        }

        return $removedVisas;
    }

    /**
     * Ajoute une méthode pour réinitialiser les champs Metro
     */
    private function resetMetroFields(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser metroTime
        if ($document->getMetroTime() !== null) {
            $document->setMetroTime(null);
            $resetFields[] = 'metroTime';
        }

        // Réinitialiser metroControl
        if ($document->getMetroControl() !== null) {
            $document->setMetroControl([]);
            $resetFields[] = 'metroControl';
        }

        return $resetFields;
    }

    /**
     * Gère le changement vers le doctype DOC
     */
    private function handleDocTypeChange(Document $document): array
    {
        $resetFields = [];

        // Définir automatiquement le Material Type sur "LITERATURE" pour les documents DOC
        if ($document->getMatProdType() !== 'ROH') {
            $document->setMatProdType('ROH'); // ROH correspond à LITERATURE dans le mapping
            $resetFields[] = 'matProdType';
        }

        return $resetFields;
    }

    /**
     * Gère la sortie du doctype DOC
     */
    private function handleExitFromDocType(Document $document): array
    {
        $resetFields = [];

        // Réinitialiser le Material Type s'il était sur LITERATURE
        if ($document->getMatProdType() === 'ROH') {
            $document->setMatProdType(null);
            $resetFields[] = 'matProdType';
        }

        return $resetFields;
    }

    /**
     * Applique les règles spéciales selon le nouveau doctype
     */
    private function applyDoctypeSpecificRules(Document $document, string $newDocType): array
    {
        $resetFields = [];

        switch ($newDocType) {
            case 'MACH':
                // Règles spécifiques pour MACH
                // Méthode de lotissement: 1M (pas de champ direct dans le nouveau système)
                // Clé d'horizon: Z07 (pas de champ direct dans le nouveau système)
                break;

            case 'MOLD':
                // Règles spécifiques pour MOLD
                // Méthode de lotissement: 6M (pas de champ direct dans le nouveau système)
                // Clé d'horizon: Z07 (pas de champ direct dans le nouveau système)
                break;

            case 'ASSY':
                // Règles spécifiques pour ASSY
                // Méthode de lotissement: 2Z (pas de champ direct dans le nouveau système)
                // Clé d'horizon: Z07 (pas de champ direct dans le nouveau système)
                break;

            case 'PUR':
                // Règles spécifiques pour PUR
                // Clé d'horizon: Z02 (pas de champ direct dans le nouveau système)
                break;

            case 'DOC':
                // Déjà géré dans handleDocTypeChange
                break;
        }

        return $resetFields;
    }

    /**
     * Gère la logique du module Finance pour le flag Critical_Complete
     */
    private function handleFinanceLogic(Document $document, string $newDocType): array
    {
        $resetFields = [];

        // Dans le module Finance, le flag Critical_Complete est mis à 1 pour les documents DOC avec VISA_MOF
        if ($newDocType === 'DOC') {
            // Vérifier si le document a un visa MOF (équivalent à Indus dans le nouveau système)
            if ($document->hasVisa('visa_Indus')) {
                $document->setCriticalComplete(1);
                $resetFields[] = 'criticalComplete';
            }
        }

        return $resetFields;
    }
}
