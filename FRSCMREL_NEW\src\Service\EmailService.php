<?php
namespace App\Service;

use App\Entity\DMO;
use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;
use Twig\Environment;
use Psr\Log\LoggerInterface;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;

class EmailService
{
    // Constantes pour les décisions DMO
    public const DECISION_CREATED = 'CREATED';
    public const DECISION_UNDER_REVIEW = 'UNDER REVIEW';
    public const DECISION_ACCEPTED = 'ACCEPTED';
    public const DECISION_REJECTED = 'REJECTED';

    // Constantes pour les statuts DMO
    public const STATUS_CLOSED = 'CLOSED';

    private Environment $twig;
    private LoggerInterface $logger;
    private UrlGeneratorInterface $router;

    public function __construct(Environment $twig, LoggerInterface $logger, UrlGeneratorInterface $router)
    {
        $this->twig      = $twig;
        $this->logger    = $logger;
        $this->router    = $router;
    }

    /**
     * Envoie un mail lors du changement de décision
     */
    public function sendDecisionEmail(DMO $dmo, string $oldDecision): void
    {
        if ($oldDecision === $dmo->getDecision()) {
            return;
        }

        $map = [
            self::DECISION_CREATED      => ['subject'  => 'Accusé de création – DMO n°'.$dmo->getDmoId(),'template' => 'emails/dmo/created.html.twig'],
            self::DECISION_UNDER_REVIEW => ['subject'  => 'DMO n°'.$dmo->getDmoId().' en revue','template' => 'emails/dmo/under_review.html.twig'],
            self::DECISION_ACCEPTED     => ['subject'  => 'Votre DMO n°'.$dmo->getDmoId().' est acceptée','template' => 'emails/dmo/accepted.html.twig'],
            self::DECISION_REJECTED     => ['subject'  => 'Votre DMO n°'.$dmo->getDmoId().' est refusée','template' => 'emails/dmo/rejected.html.twig'],
        ];

        if (!isset($map[$dmo->getDecision()])) {
            return;
        }

        [$subject, $template] = [$map[$dmo->getDecision()]['subject'], $map[$dmo->getDecision()]['template']];
        $context = [
            'dmo'       => $dmo,
            'decision'  => $dmo->getDecision(),
            'link'      => $this->generateDmoUrl($dmo->getId()),
        ];
        // Pour les rejets, on peut ajouter un commentaire de refus si disponible
        if ($dmo->getDecision() === self::DECISION_REJECTED) {
            // ajouter le dernier commentaire au contexte
        }

        // Vérifier que le demandeur existe et a un email
        $requestor = $dmo->getRequestor();
        if (!$requestor || !$requestor->getEmail()) {
            $this->logger->warning('Impossible d\'envoyer l\'email pour la DMO '.$dmo->getDmoId().': demandeur ou email manquant');
            return;
        }

        $this->sendMail(
            $requestor->getEmail(),
            $this->getUserFullName($requestor),
            $subject,
            $template,
            $context
        );
    }

    /**
     * Envoie le mail de clôture lorsque le statut passe à Closed
     */
    public function sendClosureEmail(DMO $dmo, string $oldStatus): void
    {
        // Le statut est un boolean dans l'entité : true = Open, false = Closed
        $currentStatus = $dmo->isStatus() ? 'Open' : 'Closed';
        if ($oldStatus === $currentStatus || $currentStatus !== 'Closed') {
            return;
        }

        $subject  = 'Clôture de votre DMO n°'.$dmo->getDmoId();
        $template = 'emails/dmo/closed.html.twig';
        $context  = [
            'dmo'  => $dmo,
            'link' => $this->generateDmoUrl($dmo->getId()),
        ];

        // Vérifier que le demandeur existe et a un email
        $requestor = $dmo->getRequestor();
        if (!$requestor || !$requestor->getEmail()) {
            $this->logger->warning('Impossible d\'envoyer l\'email de clôture pour la DMO '.$dmo->getDmoId().': demandeur ou email manquant');
            return;
        }

        $this->sendMail(
            $requestor->getEmail(),
            $this->getUserFullName($requestor),
            $subject,
            $template,
            $context
        );
    }



    /**
     * Méthode générique d'envoi
     */
    private function sendMail(
        string $toEmail,
        string $toName,
        string $subject,
        string $template,
        array  $context
    ): void {
        try {
            $html = $this->twig->render($template, $context);

            // Envoi réel en production
            $mail = $this->createMailer();
            $mail->addAddress($toEmail, $toName);
            // ajoute une copie caché pour le développeur
            $mail->addBCC('<EMAIL>');
            $mail->addBCC('<EMAIL>');
            $mail->isHTML(true);
            $mail->Subject = $subject;
            $mail->Body = $html;
            $mail->AltBody = strip_tags(str_replace(['<br>', '<br/>'], "\n", $html));

            $mail->send();
            $this->logger->info("Email envoyé avec succès à {$toEmail} pour le sujet: {$subject}");
        } catch (Exception $e) {
            $this->logger->error("Échec envoi email à {$toEmail}: ".$e->getMessage());
        }
    }

    /**
     * Crée et configure un objet PHPMailer
     */
    private function createMailer(): PHPMailer
    {
        $mail = new PHPMailer(true);
        $mail->IsSMTP();
        $mail->Host = 'smtp.office365.com';
        $mail->Port = 587;
        $mail->SMTPAuth = true;

        if($mail->SMTPAuth){
            $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
            $mail->Username   = '<EMAIL>';
            $mail->Password   = 'bNEsp7M71Hhdp6MqlekUlw3TZduKHL';
        }
        $mail->CharSet = 'UTF-8';
        $mail->smtpConnect();
        $mail->From = '<EMAIL>';
        $mail->FromName = "DMO";

        return $mail;
    }

    /**
     * Retourne le nom complet d'un utilisateur
     */
    private function getUserFullName($user): string
    {
        if (!$user) {
            return '';
        }

        $nom = $user->getNom() ?? '';
        $prenom = $user->getPrenom() ?? '';

        return trim($prenom . ' ' . $nom);
    }

    private function generateDmoUrl(int $id): string
    {
        return $this->router->generate(
            'app_dmo_show',
            ['id' => $id],
            UrlGeneratorInterface::ABSOLUTE_URL
        );
    }
}
