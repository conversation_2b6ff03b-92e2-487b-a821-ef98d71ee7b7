<?php

namespace App\Service;

use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

/**
 * Service pour surveiller et optimiser les performances de l'application
 */
class PerformanceMonitoringService
{
    private EntityManagerInterface $entityManager;
    private LoggerInterface $logger;
    private array $queryLog = [];
    private float $startTime;

    public function __construct(EntityManagerInterface $entityManager, LoggerInterface $logger)
    {
        $this->entityManager = $entityManager;
        $this->logger = $logger;
    }

    /**
     * Démarre le monitoring d'une opération
     */
    public function startMonitoring(string $operation): void
    {
        $this->startTime = microtime(true);
        $this->queryLog = [];

        // Note: SQL logging is handled differently in newer Doctrine versions
        // We'll track queries manually instead of using setSQLLogger

        $this->logger->info("Performance monitoring started for operation: {$operation}");
    }

    /**
     * Arrête le monitoring et génère un rapport
     */
    public function stopMonitoring(string $operation): array
    {
        $endTime = microtime(true);
        $executionTime = ($endTime - $this->startTime) * 1000; // en millisecondes

        $report = [
            'operation' => $operation,
            'execution_time_ms' => round($executionTime, 2),
            'query_count' => count($this->queryLog),
            'queries' => $this->queryLog,
            'memory_usage_mb' => round(memory_get_usage(true) / 1024 / 1024, 2),
            'peak_memory_mb' => round(memory_get_peak_usage(true) / 1024 / 1024, 2)
        ];

        // Analyser les problèmes de performance
        $issues = $this->analyzePerformanceIssues($report);
        $report['performance_issues'] = $issues;

        $this->logger->info("Performance monitoring completed for operation: {$operation}", $report);

        return $report;
    }

    /**
     * Log une requête SQL (appelé manuellement)
     */
    public function logQuery(string $sql, ?array $params = null): void
    {
        $this->queryLog[] = [
            'sql' => $sql,
            'params' => $params,
            'timestamp' => microtime(true)
        ];
    }

    /**
     * Simule le comptage des requêtes pour les tests
     */
    public function simulateQueryCount(int $count): void
    {
        for ($i = 0; $i < $count; $i++) {
            $this->queryLog[] = [
                'sql' => 'SELECT * FROM simulated_query_' . $i,
                'params' => [],
                'timestamp' => microtime(true)
            ];
        }
    }

    /**
     * Analyse les problèmes de performance potentiels
     */
    private function analyzePerformanceIssues(array $report): array
    {
        $issues = [];

        // Vérifier le nombre de requêtes (problème N+1)
        if ($report['query_count'] > 50) {
            $issues[] = [
                'type' => 'N+1_QUERIES',
                'severity' => 'HIGH',
                'message' => "Trop de requêtes détectées ({$report['query_count']}). Possible problème N+1.",
                'recommendation' => 'Utiliser eager loading ou batch loading'
            ];
        }

        // Vérifier le temps d'exécution
        if ($report['execution_time_ms'] > 5000) {
            $issues[] = [
                'type' => 'SLOW_EXECUTION',
                'severity' => 'HIGH',
                'message' => "Temps d'exécution élevé ({$report['execution_time_ms']}ms)",
                'recommendation' => 'Optimiser les requêtes et ajouter des index'
            ];
        } elseif ($report['execution_time_ms'] > 2000) {
            $issues[] = [
                'type' => 'SLOW_EXECUTION',
                'severity' => 'MEDIUM',
                'message' => "Temps d'exécution modéré ({$report['execution_time_ms']}ms)",
                'recommendation' => 'Considérer l\'optimisation des requêtes'
            ];
        }

        // Vérifier l'utilisation mémoire
        if ($report['memory_usage_mb'] > 256) {
            $issues[] = [
                'type' => 'HIGH_MEMORY_USAGE',
                'severity' => 'HIGH',
                'message' => "Utilisation mémoire élevée ({$report['memory_usage_mb']}MB)",
                'recommendation' => 'Réduire le nombre d\'entités chargées en mémoire'
            ];
        }

        // Analyser les requêtes pour détecter des patterns problématiques
        $jsonExtractCount = 0;
        $findAllQueries = 0;

        foreach ($report['queries'] as $query) {
            if (strpos($query['sql'], 'JSON_EXTRACT') !== false) {
                $jsonExtractCount++;
            }
            if (strpos($query['sql'], 'SELECT') === 0 && strpos($query['sql'], 'WHERE') === false) {
                $findAllQueries++;
            }
        }

        if ($jsonExtractCount > 10) {
            $issues[] = [
                'type' => 'EXCESSIVE_JSON_QUERIES',
                'severity' => 'MEDIUM',
                'message' => "Nombreuses requêtes JSON_EXTRACT détectées ({$jsonExtractCount})",
                'recommendation' => 'Ajouter des index JSON ou dénormaliser les données'
            ];
        }

        if ($findAllQueries > 0) {
            $issues[] = [
                'type' => 'UNFILTERED_QUERIES',
                'severity' => 'HIGH',
                'message' => "Requêtes sans filtres détectées ({$findAllQueries})",
                'recommendation' => 'Ajouter des clauses WHERE pour filtrer les données'
            ];
        }

        return $issues;
    }

    /**
     * Génère des statistiques de performance pour le dashboard
     */
    public function getPerformanceStats(): array
    {
        $conn = $this->entityManager->getConnection();

        // Statistiques de base de données
        $stats = [];

        // Nombre total de documents
        $stats['total_documents'] = $conn->executeQuery("SELECT COUNT(*) FROM document")->fetchOne();

        // Nombre de documents avec current_steps
        $stats['documents_with_steps'] = $conn->executeQuery(
            "SELECT COUNT(*) FROM document WHERE current_steps IS NOT NULL AND current_steps != '{}'"
        )->fetchOne();

        // Nombre de documents avec timestamps
        $stats['documents_with_timestamps'] = $conn->executeQuery(
            "SELECT COUNT(*) FROM document WHERE state_timestamps IS NOT NULL AND state_timestamps != '{}' AND state_timestamps != ''"
        )->fetchOne();

        // Nombre total de visas
        $stats['total_visas'] = $conn->executeQuery("SELECT COUNT(*) FROM visa")->fetchOne();

        // Taille moyenne des JSON current_steps
        $stats['avg_current_steps_size'] = $conn->executeQuery(
            "SELECT AVG(JSON_LENGTH(current_steps)) FROM document WHERE current_steps IS NOT NULL"
        )->fetchOne();

        // Taille moyenne des JSON state_timestamps
        $stats['avg_timestamps_size'] = $conn->executeQuery(
            "SELECT AVG(JSON_LENGTH(state_timestamps)) FROM document WHERE state_timestamps IS NOT NULL"
        )->fetchOne();

        return $stats;
    }

    /**
     * Recommandations d'optimisation basées sur l'analyse des données
     */
    public function getOptimizationRecommendations(): array
    {
        $stats = $this->getPerformanceStats();
        $recommendations = [];

        if ($stats['total_documents'] > 10000) {
            $recommendations[] = [
                'priority' => 'HIGH',
                'category' => 'DATABASE',
                'title' => 'Archivage des anciens documents',
                'description' => 'Considérer l\'archivage des documents terminés pour réduire la taille de la base de données',
                'impact' => 'Amélioration significative des performances de requête'
            ];
        }

        if ($stats['avg_current_steps_size'] > 10) {
            $recommendations[] = [
                'priority' => 'MEDIUM',
                'category' => 'JSON_OPTIMIZATION',
                'title' => 'Optimisation des champs JSON current_steps',
                'description' => 'Les champs current_steps sont volumineux. Considérer la dénormalisation.',
                'impact' => 'Réduction de l\'utilisation mémoire et amélioration des requêtes JSON'
            ];
        }

        if ($stats['avg_timestamps_size'] > 20) {
            $recommendations[] = [
                'priority' => 'MEDIUM',
                'category' => 'JSON_OPTIMIZATION',
                'title' => 'Optimisation des champs JSON state_timestamps',
                'description' => 'Les champs state_timestamps sont volumineux. Considérer une table séparée.',
                'impact' => 'Amélioration des performances de requête et de la maintenance'
            ];
        }

        return $recommendations;
    }
}
