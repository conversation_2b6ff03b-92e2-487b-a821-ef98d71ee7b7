<?php
declare(strict_types=1);

namespace App\Service;

use App\Entity\Document;
use App\Entity\Visa;
use App\Entity\User;
use Doctrine\Persistence\Proxy;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Workflow\Registry;

class WorkflowSimulator
{
    private Registry $registry;
    private object $userRepository;

    public function __construct(Registry $registry, ManagerRegistry $doctrine)
    {
        $this->registry = $registry;
        $this->userRepository = $doctrine->getRepository(User::class);
    }

    public function simulateWorkflowList(array $documents){
        $paniers = ['Achat_Hts', "QProd", "methode_Labo", "Methode_assemblage", "Achat_RoHs_REACH", "Tirage_Plans"];

        // Ordre du workflow pour trier les places futures
        $workflowOrder = [
            'BE_0', 'BE_1', 'BE', 'Produit', 'Qual_Logistique', 'Logistique',
            'Metro', 'Quality', 'Achat_Rfq', 'Achat_RoHs_REACH', 'Assembly',
            'Machining', 'Molding', 'Methode_assemblage', 'Planning', 'Core_Data',
            'Project', 'Achat_F30', 'Prod_Data', 'Achat_FIA', 'Achat_Hts',
            'Saisie_hts', 'Costing', 'GID', 'Indus', 'methode_Labo', 'QProd',
            'Tirage_Plans'
        ];

        $simulated = [];
        foreach ($documents as $document){
            $doc_paniers = ['oldPlaces' => [], 'currentSteps' => [], 'visitedPlaces' => []];
            $visas = $document->getVisas();

            // Créer un tableau des places passées avec leurs dates de visa pour le tri chronologique
            $past_place_with_dates = [];
            foreach ($visas as $visa){
                $placeName = str_replace('visa_', '', $visa->getName());
                $past_place_with_dates[] = [
                    'place' => $placeName,
                    'date' => $visa->getDateVisa()
                ];
            }

            // Trier les places passées par ordre chronologique (date de visa)
            usort($past_place_with_dates, function($a, $b) {
                if ($a['date'] === null && $b['date'] === null) return 0;
                if ($a['date'] === null) return 1;
                if ($b['date'] === null) return -1;
                return $a['date'] <=> $b['date'];
            });

            // Convertir en tableau simple pour compatibilité
            $past_place = [];
            foreach ($past_place_with_dates as $item) {
                $past_place[$item['place']] = 1;
            }

            $current_place = $document->getCurrentSteps();
            $futur_place = array_diff_key($this->simulateWorkflow($document), $current_place);
            $current_place = array_diff_key($current_place, $past_place);

            if (array_key_exists('BE_0', $past_place) && array_key_exists('BE_1', $past_place) && array_key_exists('BE', $past_place)){
                unset($past_place['BE_0']);
                unset($past_place['BE_1']);
            }
            if (array_key_exists('BE_0', $futur_place) && array_key_exists('BE_1', $futur_place) && array_key_exists('BE', $futur_place)){
                unset($futur_place['BE_0']);
                unset($futur_place['BE_1']);
            }
            // Traiter les paniers en préservant l'ordre chronologique pour oldPlaces
            $paniers_past_with_dates = [];
            foreach ($paniers as $panier){
                if (array_key_exists($panier, $past_place)){
                    // Trouver la date du visa pour ce panier
                    $visaDate = null;
                    foreach ($past_place_with_dates as $item) {
                        if ($item['place'] === $panier) {
                            $visaDate = $item['date'];
                            break;
                        }
                    }
                    $paniers_past_with_dates[] = [
                        'place' => $panier,
                        'date' => $visaDate
                    ];
                    unset($past_place[$panier]);
                }
                if (array_key_exists($panier, $current_place)){
                    $doc_paniers['currentSteps'][$panier][] = $document;
                    unset($current_place[$panier]);
                }
                if (array_key_exists($panier, $futur_place)){
                    $doc_paniers['visitedPlaces'][$panier][] = $document;
                    unset($futur_place[$panier]);
                }
            }

            // Trier les paniers passés par ordre chronologique
            usort($paniers_past_with_dates, function($a, $b) {
                if ($a['date'] === null && $b['date'] === null) return 0;
                if ($a['date'] === null) return 1;
                if ($b['date'] === null) return -1;
                return $a['date'] <=> $b['date'];
            });

            // Ajouter les paniers passés dans l'ordre chronologique
            foreach ($paniers_past_with_dates as $item) {
                $doc_paniers['oldPlaces'][$item['place']][] = $document;
            }

            $doc_paniers['visitedPlaces'] = array_diff_key($doc_paniers['visitedPlaces'], $doc_paniers['currentSteps']);
            $doc_paniers['currentSteps'] = array_diff_key($doc_paniers['currentSteps'], $doc_paniers['oldPlaces']);

            // Trier les places futures selon l'ordre du workflow
            $sorted_futur_place = [];
            foreach ($workflowOrder as $state) {
                if (array_key_exists($state, $futur_place)) {
                    $sorted_futur_place[$state] = $futur_place[$state];
                }
            }
            // Ajouter les places qui ne sont pas dans l'ordre prédéfini
            foreach ($futur_place as $state => $value) {
                if (!array_key_exists($state, $sorted_futur_place)) {
                    $sorted_futur_place[$state] = $value;
                }
            }

            // Trier les places futures des paniers selon l'ordre du workflow
            $sorted_paniers_futur = [];
            foreach ($workflowOrder as $state) {
                if (array_key_exists($state, $doc_paniers['visitedPlaces'])) {
                    $sorted_paniers_futur[$state] = $doc_paniers['visitedPlaces'][$state];
                }
            }
            // Ajouter les places qui ne sont pas dans l'ordre prédéfini
            foreach ($doc_paniers['visitedPlaces'] as $state => $value) {
                if (!array_key_exists($state, $sorted_paniers_futur)) {
                    $sorted_paniers_futur[$state] = $value;
                }
            }
            $doc_paniers['visitedPlaces'] = $sorted_paniers_futur;

            $simulated[] = [
                'document' => $document,
                'oldPlaces' => $past_place,
                'currentSteps' => $current_place,
                'visitedPlaces' => $sorted_futur_place,
                'paniers' => $doc_paniers
            ];
        }

        return $simulated;
    }

    public function simulateWorkflow(Document $document)
    {
        $workflow = $this->registry->get($document, 'document_workflow');
        $currentPlace = $workflow->getMarking($document)->getPlaces();

        // Sauvegarder l'état original pour le restaurer après simulation
        $originalCurrentSteps = $document->getCurrentSteps();
        $originalDocType = $document->getDocType();
        $originalProcType = $document->getProcType();
        $originalUnit = $document->getUnit();
        $originalProdAgent = $document->getProdAgent();
        $originalMatProdType = $document->getMatProdType();

        $addedVisas = [];
        $allPossiblePlaces = $currentPlace;

        $i = 0;
        $maxIterations = 100;
        $change = true;

        while ($change && $i < $maxIterations) {
            $i++;
            $change = false;

            // Pour chaque place actuelle, ajouter un visa temporaire si nécessaire
            foreach ($currentPlace as $place => $val) {
                $visaPlace = $place;
                if (in_array($place, ['Assembly', 'Machining', 'Molding'], true)) {
                    $visaPlace = "prod";
                }

                // Vérifier si le visa n'existe pas déjà (réel ou temporaire)
                $visaExists = false;
                foreach ($document->getVisas() as $existingVisa) {
                    if ($existingVisa->getName() === 'visa_' . $visaPlace) {
                        $visaExists = true;
                        break;
                    }
                }

                if (!$visaExists) {
                    $fakeVisa = $this->addFakeVisa($document, $visaPlace);
                    $addedVisas[] = $fakeVisa;
                }
            }

            // Obtenir les transitions possibles avec les nouveaux visas
            $transitions = $workflow->getEnabledTransitions($document);
            $newPlace = $currentPlace;

            foreach ($transitions as $transition) {
                $targetPlace = $transition->getTos()[0];
                if (!isset($newPlace[$targetPlace])) {
                    $newPlace[$targetPlace] = 1;
                    $change = true;

                    // Appliquer les règles automatiques pour cette nouvelle place
                    $this->applyAutomaticRules($document, $targetPlace);
                }
            }

            // Mettre à jour les places actuelles si des changements ont eu lieu
            if ($change) {
                $currentPlace = $newPlace;
                $document->setCurrentSteps($currentPlace);

                // Ajouter toutes les nouvelles places à la liste des places possibles
                foreach ($newPlace as $place => $val) {
                    if (!isset($allPossiblePlaces[$place])) {
                        $allPossiblePlaces[$place] = 1;
                    }
                }
            }
        }

        // Simulation récursive pour chaque nouvelle place trouvée
        $placesToSimulate = array_keys($allPossiblePlaces);
        $simulatedPlaces = [];

        foreach ($placesToSimulate as $placeToSimulate) {
            if (in_array($placeToSimulate, $simulatedPlaces)) {
                continue;
            }

            // Créer un état temporaire avec seulement cette place
            $tempCurrentSteps = [$placeToSimulate => 1];
            $document->setCurrentSteps($tempCurrentSteps);

            // Appliquer les règles automatiques pour cette place
            $this->applyAutomaticRules($document, $placeToSimulate);

            // Ajouter un visa temporaire pour cette place si nécessaire
            $visaPlace = $placeToSimulate;
            if (in_array($placeToSimulate, ['Assembly', 'Machining', 'Molding'], true)) {
                $visaPlace = "prod";
            }

            $tempVisa = null;
            $visaExists = false;
            foreach ($document->getVisas() as $existingVisa) {
                if ($existingVisa->getName() === 'visa_' . $visaPlace) {
                    $visaExists = true;
                    break;
                }
            }

            if (!$visaExists) {
                $tempVisa = $this->addFakeVisa($document, $visaPlace);
                $addedVisas[] = $tempVisa;
            }

            // Obtenir les transitions depuis cette place
            $tempTransitions = $workflow->getEnabledTransitions($document);
            foreach ($tempTransitions as $transition) {
                $targetPlace = $transition->getTos()[0];
                if (!isset($allPossiblePlaces[$targetPlace])) {
                    $allPossiblePlaces[$targetPlace] = 1;
                    $placesToSimulate[] = $targetPlace;
                }
            }

            $simulatedPlaces[] = $placeToSimulate;
        }

        // Nettoyer les visas temporaires ajoutés
        foreach ($addedVisas as $visa) {
            $document->removeVisa($visa);
        }

        // Restaurer l'état original
        $document->setCurrentSteps($originalCurrentSteps);
        $document->setDocType($originalDocType);
        $document->setProcType($originalProcType);
        $document->setUnit($originalUnit);
        $document->setProdAgent($originalProdAgent);
        $document->setMatProdType($originalMatProdType);

        return $allPossiblePlaces;
    }

    public function addFakeVisa(Document $document, string $place): Visa {
        $visa = new Visa();
        $visa->setReleasedDrawing($document);
        $visa->setName('visa_'.$place);
        $visa->setStatus('valid');
        $visa->setValidator($this->userRepository->find(1));
        $document->addVisa($visa);
        return $visa;
    }

    /**
     * Applique les règles automatiques lors des transitions de workflow
     */
    private function applyAutomaticRules(Document $document, string $targetPlace): void
    {
        switch ($targetPlace) {
            case 'Assembly':
                // Si le document arrive dans la place Assembly, on modifie son procType à E et son Unit à PC
                $document->setProcType('E');
                $document->setUnit('PC');
                // Définir le docType approprié si pas déjà défini
                if (!$document->getDocType() || $document->getDocType() === 'DOC') {
                    $document->setDocType('ASSY');
                }
                break;

            case 'Machining':
                // Si le document arrive dans la place Machining
                $document->setProcType('E');
                $document->setUnit('PC');
                $document->setProdAgent('USI');
                $document->setMatProdType('HALB');
                // Définir le docType approprié si pas déjà défini
                if (!$document->getDocType() || $document->getDocType() === 'DOC') {
                    $document->setDocType('MACH');
                }
                break;

            case 'Molding':
                // Si le document arrive dans la place Molding, on modifie son matProdType à HALB
                $document->setMatProdType('HALB');
                $document->setProcType('E');
                // Définir le docType approprié si pas déjà défini
                if (!$document->getDocType() || $document->getDocType() === 'DOC') {
                    $document->setDocType('MOLD');
                }
                break;

            case 'Achat_Rfq':
                // Pour les achats, définir le docType approprié
                if (!$document->getDocType() || $document->getDocType() === 'DOC') {
                    $document->setDocType('PUR');
                }
                break;

            case 'Quality':
                // Pas de changement automatique spécifique pour Quality
                break;

            default:
                // Pas de règles automatiques pour les autres places
                break;
        }
    }

}