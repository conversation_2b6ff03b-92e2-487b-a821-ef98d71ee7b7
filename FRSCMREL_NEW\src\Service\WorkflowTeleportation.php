<?php
declare(strict_types=1);

namespace App\Service;

use App\Entity\Document;
use App\Entity\Visa;
use App\Entity\User;
use Doctrine\Persistence\Proxy;
use Doctrine\Persistence\ManagerRegistry;
use Symfony\Component\Workflow\Registry;

class WorkflowTeleportation
{
    private Registry $registry;
    private object $userRepository;

    public function __construct(Registry $registry, ManagerRegistry $doctrine)
    {
        $this->registry = $registry;
        $this->userRepository = $doctrine->getRepository(User::class);
    }
    
    public function getTeleportationList(array $documents): array
    {
        $teleportationList = [];

        foreach ($documents as $document) {
            $teleportationList[$document->getId()] = $this->getDocumentTeleportation($document);
        }

        return $teleportationList;
    }

    public function getDocumentTeleportation(Document $document): array
    {
        $teleportation = [];
        foreach ($document->getCurrentSteps() as $currentPlace => $val) {
            $teleportation[$currentPlace] = $this->findFromPlace($document, $currentPlace);
        }
        return $teleportation;
    }

    public function findFromPlace(Document $document, string $place): array
    {
        $workflow = $this->registry->get($document, 'document_workflow');
        $transitions = $workflow->getDefinition()->getTransitions();
        $visas = $document->getVisasArray();
        $states = $document->getAllStates();
        $res = [];

        foreach ($transitions as $transition) {
            if ($transition->getTos()[0] === $place && (in_array("visa_" . $transition->getFroms()[0], $visas) || in_array($transition->getFroms()[0], $states))) {
                $res[] = $transition->getFroms()[0];
            }
        }

        return $res;
    }

}
