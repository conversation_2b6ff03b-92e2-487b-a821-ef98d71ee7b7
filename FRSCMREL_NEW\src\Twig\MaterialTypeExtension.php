<?php

namespace App\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFilter;
use Twig\TwigFunction;

class MaterialTypeExtension extends AbstractExtension
{
    private const MATERIAL_TYPE_MAPPING = [
        'FERT' => 'Finished Product',
        'HALB' => 'Semi Finished Product',
        'VERP' => 'Packaging',
        'ROH' => 'Material',
        'RAW MATERIAL' => 'RAW MATERIAL',
        'NON VALUATED MATERIAL' => 'NON VALUATED MATERIAL',
        'LITERATURE' => 'LITERATURE'
    ];

    // Mapping étendu pour les cas spéciaux de ROH
    private const EXTENDED_MATERIAL_TYPE_MAPPING = [
        'FERT' => 'Finished Product',
        'HALB' => 'Semi Finished Product',
        'VERP' => 'Packaging',
        'ROH' => 'Material',
        'RAW MATERIAL' => 'RAW MATERIAL',
        'NON VALUATED MATERIAL' => 'NON VALUATED MATERIAL',
        'LITERATURE' => 'LITERATURE'
    ];

    private const LEGACY_MAPPING = [
        'FINISHED PRODUCT' => 'FERT',
        'SEMI-FINISHED PRODUCT' => 'HALB',
        'SEMI FINISHED PRODUCT' => 'HALB',
        'PACKAGING' => 'VERP',
        'LITTERATURE' => 'LITERATURE',
        'Material' => 'ROH'
    ];

    public function getFilters(): array
    {
        return [
            new TwigFilter('material_type_label', [$this, 'getMaterialTypeLabel']),
            new TwigFilter('material_type_sap', [$this, 'getMaterialTypeSAP']),
        ];
    }

    public function getFunctions(): array
    {
        return [
            new TwigFunction('material_type_options', [$this, 'getMaterialTypeOptions']),
            new TwigFunction('material_type_options_sap', [$this, 'getMaterialTypeOptionsSAP']),
            new TwigFunction('all_material_types', [$this, 'getAllMaterialTypes']),
        ];
    }

    /**
     * Convertit un code SAP en libellé descriptif
     */
    public function getMaterialTypeLabel(?string $sapCode): string
    {
        if (!$sapCode) {
            return '';
        }

        return self::MATERIAL_TYPE_MAPPING[$sapCode] ?? $sapCode;
    }

    /**
     * Convertit un libellé en code SAP
     */
    public function getMaterialTypeSAP(?string $label): string
    {
        if (!$label) {
            return '';
        }

        // Si c'est déjà un code SAP valide, le retourner
        if (array_key_exists($label, self::MATERIAL_TYPE_MAPPING)) {
            return $label;
        }

        // Sinon, essayer de mapper depuis les anciens libellés
        return self::LEGACY_MAPPING[$label] ?? $label;
    }

    /**
     * Retourne les options HTML pour un select de type de matériau
     */
    public function getMaterialTypeOptions(?string $selectedValue = null): string
    {
        $options = [];

        // Options principales avec leurs descriptions
        $optionsData = [
            ['code' => 'FERT', 'label' => 'Finished Product'],
            ['code' => 'HALB', 'label' => 'Semi Finished Product'],
            ['code' => 'VERP', 'label' => 'Packaging'],
            ['code' => 'ROH', 'label' => 'Material'],
            ['code' => 'RAW MATERIAL', 'label' => 'RAW MATERIAL'],
            ['code' => 'NON VALUATED MATERIAL', 'label' => 'NON VALUATED MATERIAL'],
            ['code' => 'LITERATURE', 'label' => 'LITERATURE']
        ];

        foreach ($optionsData as $option) {
            $selected = ($selectedValue === $option['code']) ? ' selected' : '';
            $options[] = sprintf(
                '<option value="%s"%s>%s</option>',
                htmlspecialchars($option['code']),
                $selected,
                htmlspecialchars($option['label'])
            );
        }

        return implode("\n", $options);
    }

    /**
     * Retourne les options HTML pour un select de type de matériau avec codes SAP affichés
     */
    public function getMaterialTypeOptionsSAP(?string $selectedValue = null): string
    {
        $options = [];

        // Options principales avec leurs codes SAP affichés
        $optionsData = [
            ['code' => 'FERT', 'label' => 'FERT'],
            ['code' => 'HALB', 'label' => 'HALB'],
            ['code' => 'VERP', 'label' => 'VERP'],
            ['code' => 'ROH', 'label' => 'ROH'],
            ['code' => 'RAW MATERIAL', 'label' => 'RAW MATERIAL'],
            ['code' => 'NON VALUATED MATERIAL', 'label' => 'NON VALUATED MATERIAL'],
            ['code' => 'LITERATURE', 'label' => 'LITERATURE']
        ];

        foreach ($optionsData as $option) {
            $selected = ($selectedValue === $option['code']) ? ' selected' : '';
            $options[] = sprintf(
                '<option value="%s"%s>%s</option>',
                htmlspecialchars($option['code']),
                $selected,
                htmlspecialchars($option['label'])
            );
        }

        return implode("\n", $options);
    }

    /**
     * Retourne tous les types de matériaux avec leurs mappings
     */
    public function getAllMaterialTypes(): array
    {
        return [
            'mapping' => self::MATERIAL_TYPE_MAPPING,
            'legacy' => self::LEGACY_MAPPING,
            'options' => [
                ['code' => 'FERT', 'label' => 'Finished Product'],
                ['code' => 'HALB', 'label' => 'Semi Finished Product'],
                ['code' => 'VERP', 'label' => 'Packaging'],
                ['code' => 'ROH', 'label' => 'Material'],
                ['code' => 'RAW MATERIAL', 'label' => 'RAW MATERIAL'],
                ['code' => 'NON VALUATED MATERIAL', 'label' => 'NON VALUATED MATERIAL'],
                ['code' => 'LITERATURE', 'label' => 'LITERATURE']
            ]
        ];
    }
}
