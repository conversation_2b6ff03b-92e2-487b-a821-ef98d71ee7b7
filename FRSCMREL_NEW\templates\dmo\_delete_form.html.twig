<form method="post" action="{{ path('app_dmo_delete', {'id': dmo.id}) }}" class="delete_form d-inline">
    <input type="hidden" name="_token" value="{{ csrf_token('delete' ~ dmo.id) }}">
    <button type="submit" class="btn btn-link text-danger p-0 border-0 bg-transparent delete-text" title="Supprimer le DMO">
        <i class="me-2 fa fa-trash"></i> Supprimer
    </button>
</form>

<style>
    .fa-trash {
        color: #ff0000!important;
    }
    .delete-text {
        color: #ff0000!important;
        text-decoration: none;
    }
</style>

<script>
    document.querySelectorAll('.my-icon0').forEach(icon => {
        icon.addEventListener('mouseover', () => {
            icon.setAttribute('colors', 'primary:#ff0000,secondary:#ff0000');
        });
        icon.addEventListener('mouseout', () => {
            icon.setAttribute('colors', 'primary:#000000,secondary:#000000');
        });
    });

    $('.delete_form').on('submit', function(e) {
        e.preventDefault();
        const form = this;
        Swal.fire({
            title: 'Supprimer ce DMO ?',
            text: 'Cette action est irréversible. Tous les commentaires associés seront également supprimés.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: '<i class="fas fa-trash-alt"></i> Oui, supprimer',
            cancelButtonText: 'Annuler',
            customClass: {
                confirmButton: 'btn btn-sm me-2 btn-danger',
                cancelButton: 'btn btn-sm btn-secondary'
            },
            buttonsStyling: false
        }).then((result) => {
            if (result.isConfirmed) {
                form.submit();
            }
        });
    });
</script>
