{% extends 'base.html.twig' %}

{% block title %}Exporter les DMOs en CSV{% endblock %}

{% block navbar %}
    {% include '_partials/_nav2.html.twig' %}
{% endblock %}

{% block body %}
    <div class="mt-3 mx-5">
        <div class="card export-card border-0 shadow">
            <div class="card-header text-white border-0" style="background: linear-gradient(90deg, #009BFF, #00D4FF);">
                <h4 class="mb-0">Exporter les DMOs en CSV</h4>
            </div>
            <div class="card-body ps-4">
                <form id="exportForm" method="GET" action="{{ path('app_dmo_export_csv') }}">
                    <div class="row">
                        {% set columnsList = [
                            'dmo', 'Type', 'Description', 'Project', 'productRange', 'requestor','Eng_Owner', 'Indus_Related', 'date_init', 'date_end', 'status',
                            'Decision', 'Ex_Assessment', 'Ex', 'Spent_Time', 'Pr_Number','last_Modificator', 'last_Update_Date', 'Document'
                        ] %}
                        {% for col in columnsList %}
                            <div class="col-md-2 form-check">
                                <input class="form-check-input" type="checkbox" name="columns[]" value="{{ col }}" id="chk_{{ col }}" checked>
                                <label class="form-check-label" for="chk_{{ col }}">{{ col|capitalize|replace({'_': ' '}) }}</label>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="mt-3 d-flex justify-content-between align-items-center">
                        <div>
                            <button type="button" id="selectAll" class="btn btn-sm btn-outline-primary btn-select">Tout sélectionner</button>
                            <button type="button" id="deselectAll" class="btn btn-sm btn-outline-secondary btn-select">Tout désélectionner</button>
                        </div>
                        <button type="submit" class="btn btn-sm btn-primary btn-export">Exporter en CSV</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <h2 class="mt-3 mx-5">Prévisualisation</h2>
    <div class="table-container">
        <div class="table-responsive preview-table mx-5">
            <table id="previewTable" class="table table-striped table-hover">
                <thead >
                    <tr id="previewHeader"></tr>
                </thead>
                <tbody id="previewBody"></tbody>
            </table>
        </div>
    </div>
    {% set dmosData = [] %}
    {% for dmo in dmos|slice(0, 10) %}
        {% set dmoData = {
            'dmo': dmo.getDmoId(),
            'date_init': dmo.getDateInit() ? dmo.getDateInit()|date('Y-m-d') : '',
            'Description': dmo.getDescription(),
            'Project': dmo.getProjectRelation() ? dmo.getProjectRelation.otp() : '',
            'requestor': dmo.getRequestor.nom()~ ' ' ~ dmo.getRequestor.prenom(),
            'Decision': dmo.getDecision(),
            'status': dmo.isStatus() ? 'Open' : 'Closed',
            'Ex': dmo.getEx(),
            'Indus_Related': dmo.isIndusRelated() ? 'Yes' : 'No',
            'Eng_Owner': dmo.getEngOwner() ? dmo.getEngOwner().getNom() ~ ' ' ~ dmo.getEngOwner().getPrenom() : '',
            'date_end': dmo.getDateEnd() ? dmo.getDateEnd()|date('Y-m-d') : '',
            'Pr_Number': dmo.getPrNumber(),
            'last_Modificator': dmo.getLastModificator() ? dmo.getLastModificator().getNom() ~ ' ' ~ dmo.getLastModificator().getPrenom() : '',
            'last_Update_Date': dmo.getLastUpdateDate() ? dmo.getLastUpdateDate()|date('Y-m-d') : '',
            'Ex_Assessment': dmo.getExAssessment(),
            'Spent_Time': dmo.getSpentTime(),
            'Type': dmo.getType(),
            'Document': dmo.getDocument(),
            'productRange': dmo.getProductRange() ? (dmo.getNameDivisonProductRange() ~ ' / ' ~ dmo.getNameProductRange()) : ''
        } %}
        {% set dmosData = dmosData|merge([dmoData]) %}
    {% endfor %}

    <script>
        const dmosData = {{ dmosData|json_encode|raw }};
        console.log(dmosData);

        function formatColumnName(col) {
            return col.replace(/_/g, ' ').replace(/\b\w/g, char => char.toUpperCase());
        }

        function getSelectedColumns() {
            return Array.from(document.querySelectorAll('input[name="columns[]"]:checked')).map(cb => cb.value);
        }

        function updatePreviewTable() {
            const selectedColumns = getSelectedColumns();
            const headerRow = document.getElementById('previewHeader');
            const body = document.getElementById('previewBody');

            headerRow.innerHTML = '';
            body.innerHTML = '';

            selectedColumns.forEach(col => {
                const th = document.createElement('th');
                th.classList.add('text-center');
                th.classList.add('bg-primary');
                th.classList.add('text-white');

                th.style.whiteSpace = 'nowrap';
                th.textContent = formatColumnName(col);
                th.classList.add('text-center');
                th.style.fontSize = '13.5px';
                th.style.fontWeight = 'bold';
                headerRow.appendChild(th);
            });

            if (dmosData.length > 0) {
                dmosData.forEach(item => {
                    const tr = document.createElement('tr');
                    selectedColumns.forEach(col => {
                        const td = document.createElement('td');
                        td.classList.add('text-center');
                        td.style.whiteSpace = 'nowrap';
                        td.style.fontSize = '13px';
                        td.textContent = item[col] || '';
                        td.classList.add('text-center');
                        tr.appendChild(td);
                    });
                    body.appendChild(tr);
                });
            } else {
                const tr = document.createElement('tr');
                const td = document.createElement('td');
                td.setAttribute('colspan', selectedColumns.length);
                td.textContent = "Aucun enregistrement trouvé.";
                td.classList.add("text-center");
                tr.appendChild(td);
                body.appendChild(tr);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            updatePreviewTable();
            document.querySelectorAll('input[name="columns[]"]').forEach(cb => {
                cb.addEventListener('change', updatePreviewTable);
            });

            document.getElementById('selectAll').addEventListener('click', () => {
                document.querySelectorAll('input[name="columns[]"]').forEach(cb => {
                    cb.checked = true;
                });
                updatePreviewTable();
            });

            document.getElementById('deselectAll').addEventListener('click', () => {
                document.querySelectorAll('input[name="columns[]"]').forEach(cb => {
                    cb.checked = false;
                });
                updatePreviewTable();
            });
        });
    </script>

    <style>
    .card {
        border-radius: 12px;
    }

    .card-header {
        border-top-left-radius: 12px;
        border-top-right-radius: 12px;
    }

    body {
        background-image: url("{{ asset('images/wave.svg') }}");
        background-repeat: no-repeat;
        background-size: cover;
        background-attachment: fixed;
        background-position: center;
    }
</style>

{% endblock %}
