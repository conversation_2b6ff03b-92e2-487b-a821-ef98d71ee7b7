{{ form_start(form, {'attr': {'class': 'form-horizontal mt-4'}}) }}

{% for child in form %}
    {% if child.vars.name != '_token' %}
        <div class="mb-3">
            {{ form_label(child, null, {'label_attr': {'class': 'form-label'}}) }}
            {{ form_widget(child, {'attr': {'class': 'form-control'}}) }}
            <div class="text-danger">
                {{ form_errors(child) }}
            </div>
        </div>
    {% endif %}
{% endfor %}

<button type="submit" class="btn btn-primary">Ajouter</button>

{{ form_rest(form) }}
{{ form_end(form) }}
