{% extends 'base.html.twig' %}

{% block title %}Documents{% endblock %}

{% block body %}
<style>
    /* Vos styles existants */
    #table-container {
        padding: 1rem;
        background-color: #F8F9FA;
        border-radius: 0.5rem;
    }
    #table tr:last-child {
        border: none!important;
    }
    .table-filter-input {
        width: 100%;
        font-size: 0.85rem;
        height: calc(1.8rem + 2px);
    }
    span {
        cursor: pointer;
        transition: all 0.3s;
    }
     span:hover {
        padding: 0.375rem 0.75rem;
    }
    #table th, #table td {
        vertical-align: middle!important;
        white-space: nowrap;
        text-align: center!important;
        padding: 0.15rem!important;
        border: none!important;
    }
    #table thead th {
        user-select: none;
    }
    #table thead tr {
        border: none;
    }
    #table thead tr#entetes th {
        background-color: #004080;
        color: #fff;
        font-size: 0.85rem;
    }
    #table thead tr#filtres th {
        background-color: #F8F9FA; /* gris clair */
        border: none;
        cursor: pointer;
    }
    /* Icônes de tri */
    th.sort-asc i, th.sort-desc i {
        margin-left: 5px;
    }
    /* Badges */
    .badge.bg-primary {
        background: #0059B3!important;
    }
    /* Champs invalides */
    .is-invalid {
        border-color: #dc3545;
    }
    /* Boutons */
    .btn-refresh {
        margin-bottom: 1rem;
    }
    /* --- Modale personnalisée --- */
    .modal-dialog.modal-lg {
        max-width: 900px;
    }
    .modal-content.custom-modal-content {
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }
    .modal-header.custom-modal-header {
        background-color: #004080;
        color: #fff;
        border-bottom: none;
    }
    .modal-header.custom-modal-header .btn-close {
        filter: invert(100%); /* Rendre la croix blanche sur fond bleu */
    }
    .modal-footer.custom-modal-footer {
        border-top: none;
    }
    .tooltip .tooltip-inner {
        max-width: 400px;
        overflow-y: auto; /* Barre de défilement si dépasse */
        white-space: normal; /* Permet de passer à la ligne */
    }

    /* Style du conteneur de l'iframe et du spinner */
    #iframe-container {
        position: relative;
        width: 100%;
        height: 55vh;
        margin-top: 1rem;
    }
    #spinner {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        display: none;
    }

/* Styles pour le modal des commentaires */
.comment-item {
    background-color: #f8f9fa;
    border-left: 4px solid #009BFF;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.comment-item:last-child {
    margin-bottom: 0;
}

.comment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.comment-state {
    background-color: #009BFF;
    color: white;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
}

.comment-meta {
    color: #6c757d;
    font-size: 0.9rem;
}

.comment-content {
    color: #333;
    line-height: 1.5;
    margin-top: 8px;
    padding: 10px;
    background-color: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.no-comments {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 20px;
}

.comments-count {
    background-color: #e9ecef;
    color: #495057;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 0.9rem;
    margin-bottom: 20px;
    display: inline-block;
}

/* Styles pour le formulaire d'ajout de commentaire */
.modal-footer.custom-modal-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #dee2e6;
    padding: 15px 20px;
}

#newCommentText {
    resize: vertical;
    min-height: 38px;
}

#addCommentBtn {
    background-color: #009BFF;
    border-color: #009BFF;
    height: 38px;
}

#addCommentBtn:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.comment-form-loading {
    opacity: 0.6;
    pointer-events: none;
}

/* Retirer la transparence des tooltips pour une meilleure lisibilité */
.tooltip .tooltip-inner {
    background-color: rgba(0, 0, 0, 0.95) !important;
    opacity: 1 !important;
}

.tooltip.bs-tooltip-top .tooltip-arrow::before,
.tooltip.bs-tooltip-bottom .tooltip-arrow::before,
.tooltip.bs-tooltip-start .tooltip-arrow::before,
.tooltip.bs-tooltip-end .tooltip-arrow::before {
    border-color: rgba(0, 0, 0, 0.95) transparent !important;
}
</style>

<div class="mt-3" style="margin: 0 2%">
    <div class="row">
        <!-- Partie gauche : le tableau -->
        <div class="col">
            <h3 class="mb-2">Revue Qualité</h3>
            {% if documents is empty %}
                <div class="alert alert-warning" role="alert">
                    Aucun document trouvé.
                </div>
            {% else %}
                <div class="card shadow border-0">
                    <div class="card-body p-0">
                        <div id="table-container">
                            <table class="table table-hover table-bordered mb-0" id="table">
                                <thead>
                                    <!-- Ligne de filtres -->
                                    <tr id="filtres">
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Pack" data-col="1"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="#" data-col="0"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Activité" data-col="2"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Référence" data-col="3"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="R" data-col="4"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="prod plan" data-col="5"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="R" data-col="6"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Action" data-col="7"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" style="width: 3rem" placeholder="Type" data-col="9"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Propriétaire" data-col="10"></th>
                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Inventaire" data-col="8"></th>
                                        <th ><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Commentaires" data-col="12"></th>

                                        <th><input type="text" class="table-filter-input text-center form-control form-control-sm" placeholder="Étapes" data-col="18"></th>
                                        <th>
                                            <span id="signer-mass" style="display: none" class="badge bg-success" onclick="signSelectedDocuments()">Signer sélectionnés</span>
                                        </th>
                                        <th>
                                            <span class="badge bg-secondary">
                                                {{ total_documents }}{{ total_documents == 1 ? ' Document' : ' Documents' }}
                                            </span>
                                        </th>
                                    </tr>
                                    <!-- Ligne d'entêtes -->
                                    <tr id="entetes">
                                        <th>#</th>
                                        <th>Pack</th>
                                        <th>Activité</th>
                                        <th>Référence</th>
                                        <th>R</th>
                                        <th>prod plan</th>
                                        <th>R</th>
                                        <th>Action</th>
                                        <th>Type</th>
                                        <th>Propriétaire</th>
                                        <th>Inventaire</th>
                                        <th >Com</th>
                                        <th>Etat</th>
                                        <th>Signer</th>
                                        <th>Visas</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for document in documents %}
                                        <tr document-id="{{document.id}}" doc-id="{{ document.id }}" >
                                            <td>
                                            {% if document.stateTimestamps is not null %}
                                                {% if document.stateTimestamps[place] is defined %}
                                                    {% set arrivedDate = date(document.stateTimestamps[place]) %}
                                                    {% set diff = date().diff(arrivedDate) %}
                                                    <span class="badge bg-primary">{{ diff.days }} jour{{ diff.days > 1 ? 's' }}</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">N/A</span>
                                                {% endif %}

                                            {% else %}
                                                <span class="badge bg-secondary">N/A</span>
                                            {% endif %}
                                                {# {{document.getDaysInState(place)}} #}
                                            </td>
                                            <td><a href="{{ path('detail_package', {'id': document.relPack.id}) }}" class="badge bg-primary pack-link">{{document.relPack.id}}</a></td>
                                            <td>
                                                <table class="table table-striped table-bordered mb-0 text-center" style="font-size: 0.7rem;">
                                                    <tbody>
                                                        <tr>
                                                            <td class="p-1" style="font-size: 0.70rem;">{{ document.relPack.activity }}</td>
                                                        </tr>
                                                        <tr>
                                                            <td class="p-1" style="font-size: 0.70rem;">{{ document.relPack.getProjectRelation() ? document.relPack.getProjectRelation.otp() : '' }}</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </td>
                                            <td>{{ document.reference|trim }}</td>
                                            <td>
                                                {{ document.refrev }}
                                                {% if document.ex != 'NO' %}
                                                    <span style="color: red; font-weight: 500;"><sup>{{ document.ex }}</sup></span>
                                                {% else %}
                                                    {{ document.ex }}
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="https://app.aletiq.com/parts/preview/id/{{ document.prodDraw }}/revision/{{ document.prodDrawRev }}"
                                                    target="_blank"
                                                    class="badge bg-primary preview-tooltip">
                                                    {{ document.prodDraw }}
                                                </a>
                                            </td>
                                            <td>{{ document.prodDrawRev }}</td>
                                            <td>{{ document.action }}</td>
                                            <td class="p-1">
                                            <select class="form-control form-control-sm doc-type-select"
                                                    name="doctype"
                                                    doc-id="{{ document.id }}">
                                                <option value="ASSY" {% if document.doctype == 'ASSY' %}selected{% endif %}>ASSY</option>
                                                <option value="MACH" {% if document.doctype == 'MACH' %}selected{% endif %}>MACH</option>
                                                <option value="MOLD" {% if document.doctype == 'MOLD' %}selected{% endif %}>MOLD</option>
                                                <option value="DOC" {% if document.doctype == 'DOC' %}selected{% endif %}>DOC</option>
                                                <option value="PUR" {% if document.doctype == 'PUR' %}selected{% endif %}>PUR</option>
                                            </select></td>
                                            <td>{{ document.qualOwner }}</td>
                                            <td>{{ document.getInventoryImpact }}</td>
                                                                                    <td>
                                            {% if document.commentaires|length > 0 %}
                                                <div class="badge bg-primary" 
                                                onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')" 
                                                style="cursor: pointer; font-size: 0.75rem;"
                                                data-bs-toggle="tooltip"
                                                data-document-id="{{ document.id }}"
                                                onmouseenter="loadCommentsTooltip(this)"
                                                title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                                txt="{% for comment in document.commentaires %}
                                                <strong>{{ stepLabels[comment.state] ?? stepLabels[comment.state|upper] ?? (comment.state|replace({'_': ' '})|title) }}</strong> : {{ comment.commentaire|e }}
                                                par <em>{{ comment.user|e }}</em><br>
                                                {% endfor %}">
                                                <i class="fas fa-file-alt" style="color:rgb(255, 255, 255); cursor: pointer;"></i> {{document.commentaires|length}}
                                            </div>
                                            {% else %}
                                                <i class="fas fa-file-alt"
                                                style="color: #ccc; cursor: pointer;"
                                                title="Aucun commentaire"
                                                data-bs-toggle="tooltip"
                                                data-document-id="{{ document.id }}"
                                                onmouseenter="loadCommentsTooltip(this)"
                                                onclick="showCommentsModal({{ document.id }}, '{{ document.reference|escape('js') }}')"
                                                title="Cliquer pour voir les commentaires ({{ document.commentaires|length }}) | Survoler pour aperçu"
                                                txt="{% for comment in document.commentaires %}
                                                <strong>{{ stepLabels[comment.state] ?? stepLabels[comment.state|upper] ?? (comment.state|replace({'_': ' '})|title) }}</strong> : {{ comment.commentaire|e }}
                                                par <em>{{ comment.user|e }}</em><br>
                                                {% endfor %}"
                                                ></i>
                                            {% endif %}
                                        </td>
                                            <td class="text-center">
                                                {% if document.CurrentStepsVisa|length > 1 %}
                                                    <div class="dropdown">
                                                        <span id="dropdownMenuButton{{ document.id }}" data-bs-toggle="dropdown" aria-expanded="false" class="badge bg-primary">
                                                            {{ document.CurrentStepsVisa|length }}
                                                        </span>
                                                        <ul class="dropdown-menu" aria-labelledby="dropdownMenuButton{{ document.id }}">
                                                            {% for step, value in document.CurrentStepsVisa %}
                                                                <li>
                                                                    <a class="dropdown-item" href="{{ path('app_document_place', {'place': step}) }}">{{ step|replace({'_': ' '})|first|upper ~ step|replace({'_': ' '})|slice(1) }}</a>
                                                                </li>
                                                            {% endfor %}
                                                        </ul>
                                                    </div>
                                                {% else %}
                                                    {% for step, value in document.CurrentStepsVisa %}
                                                        <span class="badge bg-primary">{{ stepLabels[step] ?? (step|replace({'_': ' '})|title) }}</span>
                                                    {% endfor %}
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="d-flex justify-content-center align-items-center">
                                                    <div class="form-check">
                                                        <input class="form-check-input doc-select" type="checkbox" data-document-id="{{ document.id }}" data-current-steps='{{ document.CurrentStepsVisa|json_encode|e("html_attr") }}'>
                                                    </div>
                                                    <span class="badge bg-primary" onclick='createVisa("{{ document.id|escape("js") }}")'>Signe</span>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary" onclick="showVisas({{ document.id }})">
                                                    <i class="fa-solid fa-passport"></i>
                                                </span>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                </tbody>
                            </table>

                        </div><!-- ./table-responsive -->
                    </div><!-- ./card-body -->
                </div><!-- ./card -->
                <!-- Contrôles de pagination -->
                {# Pagination supprimée #}
            {% endif %}
        </div><!-- ./col -->

        <!-- Partie droite : formulaire et iframe -->
        <div class="col">
            <div class="row">
                <div class="card shadow border-0 h-100">
                    <div class="card-body">
                        <!-- Champ caché pour l'ID du document -->
                        <input type="hidden" id="documentId" value="">

                        <!-- Groupe "Inspect" -->
                        <div class="mb-3">
                            <label for="inspect" class="form-label fw-bold">Inspect</label>
                            <select id="inspect" class="selectpicker inspect-select" multiple disabled
                                    data-selected-text-format="count"
                                    data-style="btn-white border"
                                    data-width="100%"
                                    data-live-search="true"
                                    title="Sélectionner une inspect">
                                <option value="Z01">Z01</option>
                                <option value="Z04">Z04</option>
                                <option value="Z08">Z08</option>
                            </select>
                        </div>


                        <!-- Groupe "Gamme + Tps réception" -->
                        <div class="row g-3">
                            <div class="col-6">
                                <label for="gamme" class="form-label fw-bold">Gamme</label>
                                <select id="gamme" class="form-select gamme-select" disabled>
                                    <option value="">-- Sélectionner --</option>
                                    <option value="Z001">Z001 – SANS gamme de contrôle </option>
                                    <option value="Z002">Z002 – AVEC Gamme de contrôle </option>
                                </select>
                            </div>
                            <div class="col-6">
                                <label for="tpsRecept" class="form-label fw-bold">Tps Recept</label>
                                <p id="tpsRecept" class="mt-2 mb-0"></p>
                            </div>
                        </div>

                        <!-- Groupe "Dynamisation et Exigence Documentaire" -->
                        <div class="row g-3">
                            <div class="col-6">
                                <label for="dynamisation" class="form-label fw-bold">Dynamisation</label>
                                <select id="dynamisation" class="form-select dynamisation-select" disabled>
                                    <option value="">-- Sélectionner --</option>
                                    <option value="Z20" title="FAI / 100 CTRL">Z20 - FAI / 100 CTRL</option>
                                    <option value="Z21" title="FAI / 1 CTRL / 10 DOC / 1 CTRL">Z21 - FAI / 1 CTRL / 10 DOC / 1 CTRL</option>
                                    <option value="Z22" title="CTRL">Z22 - CTRL</option>
                                    <option value="Z23" title="1 CTRL / 10 DOC">Z23 - 1 CTRL / 10 DOC</option>
                                    <option value="Z24" title="CTRL / 10 SKIP">Z24 - CTRL / 10 SKIP</option>
                                    <option value="Z25" title="DELEGATION DE CONTRÔLE (SKIP)">Z25 - DELEGATION DE CONTRÔLE (SKIP)</option>
                                    <option value="Z26" title="FAI/CTRL AVEC GAMME (INTERNE SEULEMENT)">Z26 - FAI/CTRL AVEC GAMME (INTERNE SEULEMENT)</option>
                                </select>
                            </div>
                            <div class="col-6">
                                <label for="exigenceDocumentaire" class="form-label fw-bold">Exigence Documentaire</label>
                                <select id="exigenceDocumentaire" class="selectpicker qDocRec-multiple" multiple disabled data-selected-text-format="count" data-style="btn-white border" data-width="100%" data-live-search="true" title="Sélectionner une exigence documentaire">
                                    <option value="Z01">Z01 - Certificat 2.1_(EN10204)</option>
                                    <option value="Z02">Z02 - Certificat 2.2_(EN10204)</option>
                                    <option value="Z03">Z03 - Certificat 3.1_(EN10204)</option>
                                    <option value="Z04">Z04 - Certificat 3.1 NORSOK_(EN10204)</option>
                                    <option value="Z05">Z05 - Certificat 3.2_(EN10204)</option>
                                    <option value="Z06">Z06 - Certificat WPS PQR</option>
                                    <option value="Z07">Z07 - Certificat Choc Thermique</option>
                                    <option value="Z09">Z09 - Certificat Retention</option>
                                    <option value="Z10">Z10 - Certificat Test non destructif</option>
                                    <option value="Z11">Z11 - Date de Peremption</option>
                                    <option value="Z13">Z13 - Rapport de contrôle Niveau 0</option>
                                    <option value="Z14">Z14 - Rapport de contrôle Niveau 1</option>
                                    <option value="Z15">Z15 - Rapport de contrôle Niveau 2</option>
                                    <option value="Z16">Z16 - Rapport de contrôle Niveau 3</option>
                                    <option value="Z17">Z17 - Rapport de contrôle Niveau 4</option>
                                    <option value="Z18">Z18 - Rapport de contrôle Niveau 5</option>
                                    <option value="Z19">Z19 - ATEX</option>
                                    <option value="Z20">Z20 - Traitement de Surface</option>
                                    <option value="Z21">Z21 - Scellement</option>
                                    <option value="Z22">Z22 - Cahier des charges</option>
                                    <option value="Z23">Z23 - Tracabilité Niveau 1</option>
                                    <option value="Z24">Z24 - Tracabilité Niveau 2</option>
                                    <option value="Z25">Z25 - Certificat Passivation</option>
                                </select>
                            </div>
                        </div>
                        {# div qualOwner #}
                        <div class="mb-3">
                            <label for="qualOwner" class="form-label fw-bold">Propriétaire Qualité</label>
                            <select id="qualOwner" class="form-select qual-owner-select" disabled>
                                <option value="">-- Sélectionner --</option>
                            </select>
                        </div>

                        <!-- Groupe "Commentaire" -->
                        <div class="mt-3">
                            <div class="row">
                                <div class="col-6">
                                    <label for="commentaire" class="form-label fw-bold">Commentaire</label>
                                </div>
                                <div class="col-6 text-end mb-1">
                                    <span class="badge bg-primary" id="switchButton"  >Alerte ED</span>
                                </div>
                            </div>
                            <textarea id="commentaire" class="form-control comment-input" rows="2" disabled></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div id="iframe-container">
                <!-- Spinner (caché par défaut) -->
                <div id="spinner">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                </div>
                <!-- Iframe -->
                <iframe id="aletiq_preview" style="width: 100%; height: 100%;" src=""></iframe>
            </div>
        </div>
    </div><!-- ./row -->
</div><!-- ./container -->

<div class="modal fade" id="modalVisas" tabindex="-1" aria-labelledby="modalVisasLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalVisasLabel">Historique des visas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
            </div>
        </div>
    </div>
</div>


<!-- Modal pour les commentaires -->
<div class="modal fade" id="modalComments" tabindex="-1" aria-labelledby="modalCommentsLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable modal-lg">
        <div class="modal-content custom-modal-content">
            <div class="modal-header custom-modal-header">
                <h5 class="modal-title" id="modalCommentsLabel">
                    <i class="fas fa-comments me-2"></i>
                    Commentaires - <span id="documentReference"></span>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="commentsModalBody">
                <!-- Les commentaires seront chargés ici -->
            </div>
            <div class="modal-footer custom-modal-footer">
                <div class="w-100">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <span class="badge bg-primary" style="background-color: #009BFF !important; font-size: 0.9rem; padding: 8px 12px;">
                                {{ place|replace({'_': ' '})|title }}
                            </span>
                        </div>
                        <div class="col-md-8">
                            <textarea class="form-control form-control-sm"
                                      id="newCommentText"
                                      placeholder="Ajouter un commentaire pour {{ place|replace({'_': ' '})|title }}..."
                                      rows="2"></textarea>
                        </div>
                        <div class="col-md-2">
                            <button type="button"
                                    class="btn btn-primary btn-sm w-100"
                                    id="addCommentBtn"
                                    onclick="addNewComment()">
                                <i class="fas fa-plus me-1"></i>
                                Ajouter
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // Fonction pour afficher le modal des commentaires
    function showCommentsModal(documentId, documentReference) {
        // Stocker l'ID du document pour l'ajout de commentaires
        currentDocumentId = documentId;

        // Mettre à jour le titre du modal
        document.getElementById('documentReference').textContent = documentReference;

        // Afficher le modal
        const modal = new bootstrap.Modal(document.getElementById('modalComments'));
        modal.show();

        // Charger les commentaires
        loadCommentsForModal(documentId);
    }


    // Variable globale pour stocker l'ID du document actuel
    let currentDocumentId = null;

    // Fonction pour ajouter un nouveau commentaire (réutilise la logique de jhess)
    function addNewComment() {
        const commentTextarea = document.getElementById('newCommentText');
        const commentText = commentTextarea.value.trim();

        if (!commentText) {
            Toast.fire({
                icon: 'warning',
                title: 'Veuillez saisir un commentaire'
            });
            return;
        }

        if (!currentDocumentId) {
            Toast.fire({
                icon: 'error',
                title: 'Erreur: Document non identifié'
            });
            return;
        }

        // Désactiver le formulaire pendant l'envoi
        const form = document.querySelector('.modal-footer');
        form.classList.add('comment-form-loading');
        document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Ajout...';

        // Utiliser la fonction addComment existante de jhess
        // Créer un élément temporaire pour simuler l'input du tableau
        const tempInput = $('<input>').val(commentText).data('document-id', currentDocumentId);

        // Sauvegarder les fonctions originales de jhess
        const originalShowLoading = window.showLoading;
        const originalHideLoading = window.hideLoading;
        const originalRefreshTable = window.refreshTable;

        // Remplacer temporairement les fonctions pour le modal
        window.showLoading = function() { /* déjà géré par le formulaire */ };
        window.hideLoading = function() {
            form.classList.remove('comment-form-loading');
            document.getElementById('addCommentBtn').innerHTML = '<i class="fas fa-plus me-1"></i>Ajouter';

            // Vider le champ de commentaire
            commentTextarea.value = '';

            // Recharger les commentaires dans le modal après succès
            setTimeout(() => {
                loadCommentsForModal(currentDocumentId);
            }, 200);
        };
        window.refreshTable = function() {
            // Ne rien faire pour éviter de rafraîchir le tableau
        };

        // Appeler la fonction addComment de jhess
        addComment(currentDocumentId, tempInput[0]);

        // Restaurer les fonctions originales après un délai
        setTimeout(() => {
            window.showLoading = originalShowLoading;
            window.hideLoading = originalHideLoading;
            window.refreshTable = originalRefreshTable;
        }, 1000);
    }

    // Fonction séparée pour charger les commentaires (réutilisable)
    function loadCommentsForModal(documentId) {
        document.getElementById('commentsModalBody').innerHTML = `
            <div class="text-center py-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2 text-muted">Chargement des commentaires...</p>
            </div>
        `;

        $.ajax({
            url: "{{ path('app_commentaire_get', {'documentId': 'DOCUMENT_ID_PLACEHOLDER'}) }}".replace('DOCUMENT_ID_PLACEHOLDER', documentId),
            type: 'GET',
            success: function(comments) {
                displayComments(comments);
            },
            error: function(xhr, status, error) {
                document.getElementById('commentsModalBody').innerHTML = `
                    <div class="alert alert-danger" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Erreur lors du chargement des commentaires: ${error}
                    </div>
                `;
            }
        });
    }

    // Rendre les fonctions globales pour qu'elles soient accessibles depuis le HTML
    window.showCommentsModal = showCommentsModal;
    window.addNewComment = addNewComment;




function toggleSwitch() {
    var button = $('#switchButton');
    var newState = (button.text().trim() === 'Alerte ED off');

    if (newState) {
        button.text('Alerte ED on')
            .removeClass('bg-primary').addClass('bg-success');
    } else {
        button.text('Alerte ED off')
            .removeClass('bg-success').addClass('bg-primary');
    }

    var docId = $('#documentId').val();
    $.ajax({
        url: '{{ path("update_modifdocImpact") }}',
        method: 'POST',
        contentType: 'application/json',
        data: JSON.stringify({
            id: docId,
            docImpact: newState
        }),
        success: function(response) {
            if (response.status === 'success') {
                Toast.fire({
                    icon: 'success',
                    title: newState ? 'Alerte ED activée' : 'Alerte ED désactivée'
                });
            } else {
                alert('Erreur: ' + response.message);
            }
        },
        error: function(xhr, status, error) {
            console.error("Erreur lors de la mise à jour.", error);
            alert('Erreur lors de la mise à jour.');
        }
    });
}


// $('#table tbody tr').on('click', function() {
$(document).on('click', '#table tbody tr', fait);
function fait() {
    var documentId = $(this).attr('doc-id');
    var url = "{{ path('get_document', {'id': 0}) }}";
    url = url.replace('0', documentId);
    $('#table tbody tr').removeClass('table-active');
    $(this).addClass('table-active');

    $.ajax({
        url: url,
        type: 'POST',
        data: {
            documentId: documentId
        },
        async: true,
        success: function(response) {
            let doc = response;
            // Afficher le spinner et rafraîchir l'iframe
            $('#spinner').show();
            refreshIframe(doc.prodDraw, doc.prodDrawRev);

            // Mise à jour et activation des autres champs
            $('#gamme').val(doc.qControlRouting || "").prop('disabled', false);
            $('#gamme').attr('doc-id', doc.id);
            $('#dynamisation').val(doc.qDynamization || "").prop('disabled', false);
            $('#dynamisation').attr('doc-id', doc.id);

            var $selectEl = $('#exigenceDocumentaire');
            $selectEl.selectpicker('destroy');
            $selectEl.attr('doc-id', doc.id);
            $selectEl.find('option').prop('selected', false);

            // Logique pour ajouter automatiquement Z19 si document.ex != 'NO'
            if (doc.ex && doc.ex !== 'NO') {
                // Initialiser qDocRec comme un array s'il n'existe pas
                if (!doc.qDocRec || !Array.isArray(doc.qDocRec)) {
                    doc.qDocRec = [];
                }

                // Ajouter Z19 s'il n'est pas déjà présent
                if (!doc.qDocRec.includes('Z19')) {
                    doc.qDocRec.push('Z19');

                    // Mettre à jour le document côté serveur
                    updateDocumentField(doc.id, 'qDocRec', doc.qDocRec);

                    // Afficher une notification
                    Toast.fire({
                        icon: 'info',
                        title: 'Exigence Z19 (ATEX) ajoutée automatiquement car EX = ' + doc.ex
                    });
                }
            }

            if (doc.qDocRec && Array.isArray(doc.qDocRec)) {
                doc.qDocRec.forEach(function(val) {
                    $selectEl.find('option[value="' + val + '"]').prop('selected', true);
                });
            }
            $selectEl.prop('disabled', false);
            if (typeof $selectEl.selectpicker === 'function') {
                $selectEl.selectpicker();
            }

            // Mise à jour du select multiple "inspect"
            var $inspectEl = $('#inspect');
            $inspectEl.selectpicker('destroy');
            $inspectEl.attr('doc-id', doc.id);
            $inspectEl.find('option').prop('selected', false);

            // Logique pour ajouter automatiquement Z08 par défaut pour toute référence
            // Initialiser qInspection comme un array s'il n'existe pas
            if (!doc.qInspection || !Array.isArray(doc.qInspection)) {
                doc.qInspection = [];
            }

            // Ajouter Z08 s'il n'est pas déjà présent
            if (!doc.qInspection.includes('Z08')) {
                doc.qInspection.push('Z08');

                // Mettre à jour le document côté serveur
                updateDocumentField(doc.id, 'qInspection', doc.qInspection);

                // Afficher une notification
                Toast.fire({
                    icon: 'info',
                    title: 'Inspection Z08 ajoutée par défaut'
                });
            }

            if (doc.qInspection && Array.isArray(doc.qInspection)) {
                doc.qInspection.forEach(function(val) {
                    $inspectEl.find('option[value="' + val + '"]').prop('selected', true);
                });
            }
            $inspectEl.prop('disabled', false);
            if (typeof $inspectEl.selectpicker === 'function') {
                $inspectEl.selectpicker();
            }

            // Mise à jour du select "qualOwner"
            var $qualOwnerEl = $('#qualOwner');
            $qualOwnerEl.val(doc.qualOwner || "").prop('disabled', false);
            $qualOwnerEl.attr('doc-id', doc.id);

            // Mise à jour du textarea commentaire
            var commentPlaceholder = "";
            if (doc.commentaires && Array.isArray(doc.commentaires)) {
                commentPlaceholder = doc.commentaires.map(function(comment) {
                    return comment;
                }).join("\n");
            }
            $('#commentaire').attr('placeholder', commentPlaceholder).prop('disabled', false);
            $('#commentaire').attr('data-document-id', doc.id);

            updateTpsRecept($('#gamme').val());
            // Stocker l'ID du document dans le champ caché
            $('#documentId').val(doc.id || "");
            //console.log(doc.modifExigencesQual);
            if (doc.docImpact) {
                $('#switchButton')
                    .text('Alerte ED on')
                    .removeClass('bg-primary').addClass('bg-success')
                    .prop('disabled', false)
                    .attr('onclick', 'toggleSwitch()');
            } else {
                $('#switchButton')
                    .text('Alerte ED off')
                    .removeClass('bg-success').addClass('bg-primary')
                    .prop('disabled', false)
                    .attr('onclick', 'toggleSwitch()');

            }
        }
    });

};

// Lorsque l'iframe a fini de charger, masquer le spinner.
$('#aletiq_preview').on('load', function() {
    $('#spinner').hide();
});

function refreshIframe(prodDraw, prodDrawRev) {
    // check if the src is already set to avoid unnecessary reloads
    if ($('#aletiq_preview').attr('src') === "https://app.aletiq.com/parts/preview/id/" + prodDraw + "/revision/" + prodDrawRev) {
        $('#spinner').hide();
        return; // No need to refresh
    }
    if (prodDraw && prodDrawRev) {
        $('#aletiq_preview').attr('src', "https://app.aletiq.com/parts/preview/id/" + prodDraw + "/revision/" + prodDrawRev);
    } else {
        $('#aletiq_preview').attr('src', "");
        $('#spinner').hide();
    }
}

function updateTpsRecept(selectedGamme) {
    var tpsRecept = $('#tpsRecept');
    if (selectedGamme === 'Z002') {
        tpsRecept.text('20 jours');
    } else if (selectedGamme === 'Z001') {
        tpsRecept.text('5 jours');
    } else {
        tpsRecept.text('');
    }
}

$('#gamme').on('change', function() {
    var selectedGamme = $(this).val();
    updateTpsRecept(selectedGamme);
});

function fillQualOwnerSelect() {
    $.ajax({
        url: "{{ path('get_qual_user') }}",
        type: 'GET',
        success: function(data) {
            var $qualOwnerSelect = $('#qualOwner');
            $qualOwnerSelect.empty();
            $qualOwnerSelect.append('<option value="">-- Sélectionner --</option>');
            data.forEach(function(user) {
                $qualOwnerSelect.append('<option value="' + user.id + '">' + user.prenom + " " + user.nom + '</option>');
            });
        },
        error: function(xhr, status, error) {
            console.error("Erreur lors du chargement des utilisateurs qualité.", error);
        }
    });
}
fillQualOwnerSelect();
</script>



{% include 'js/jhess.html.twig' %}
{% endblock %}

