{% extends 'base.html.twig' %}

{% block title %}Suivi des documents{% endblock %}

{% block body %}

<div class="mt-3" style="margin: 0 2%;">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <h3 class="text-center mb-0">Suivi des documents</h3>

                {# Légende des couleurs #}
                <div class="d-flex justify-content-center gap-3">
                    <span class="badge rounded-pill panier">
                        Panier
                    </span>
                    <span class="badge rounded-pill old-place">
                        Étape Validée
                    </span>
                    <span class="badge rounded-pill current-step">
                        Étape Actuelle
                    </span>
                    <span class="badge rounded-pill visited-place">
                        Prochaine Étape
                    </span>
                </div>
            </div>

            {# Search and Filter Section #}
            <div class="card mb-4 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0 d-flex justify-content-between align-items-center">
                        <span><i class="fas fa-search me-2"></i>Recherche et Filtres</span>
                        {% if activeFilters > 0 %}
                            <span class="badge bg-warning text-dark">
                                {{ activeFilters }} filtre{{ activeFilters > 1 ? 's' : '' }} actif{{ activeFilters > 1 ? 's' : '' }}
                            </span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if activeFilters > 0 %}
                        <div class="alert alert-info d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>Filtres actifs :</strong>
                                {% for key, value in filters %}
                                    {% if value is not empty %}
                                        <span class="badge bg-primary me-1">
                                            {% if key == 'search' %}Recherche: "{{ value }}"
                                            {% elseif key == 'reference' %}Référence: "{{ value }}"
                                            {% elseif key == 'pack' %}Pack: "{{ value }}"
                                            {% elseif key == 'productCode' %}Code Produit: "{{ value }}"
                                            {% elseif key == 'docType' %}Type Doc: {{ value }}
                                            {% elseif key == 'procType' %}Type Proc: {{ value }}
                                            {% elseif key == 'activity' %}Activité: {{ value }}
                                            {% elseif key == 'material' %}Matériau: "{{ value }}"
                                            {% elseif key == 'currentStep' %}Étape: {{ stepLabels[value] ?? (value|replace({'_': ' '})|title) }}
                                            {% else %}{{ key|capitalize }}: {{ value }}
                                            {% endif %}
                                        </span>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="clearFiltersAlert">
                                <i class="fas fa-times"></i> Effacer
                            </button>
                        </div>
                    {% endif %}


                    <form method="GET" action="{{ path('suivie_ref') }}" id="filterForm">
                        <div class="row g-3">
                            {# Global Search #}


                            {# Reference Filter #}
                            <div class="col-md-4">
                                <label for="reference" class="form-label">Référence</label>
                                <input type="text" class="form-control" id="reference" name="reference"
                                       value="{{ filters.reference ?? '' }}"
                                       placeholder="Filtrer par référence...">
                            </div>

                            {# Pack Filter #}
                            <div class="col-md-4">
                                <label for="pack" class="form-label">Pack</label>
                                <input type="text" class="form-control" id="pack" name="pack"
                                       value="{{ filters.pack ?? '' }}"
                                       placeholder="Filtrer par ID de pack...">
                            </div>


                            {# Document Type Filter #}
                            <div class="col-md-4">
                                <label for="docType" class="form-label">Type Document</label>
                                <select class="form-select" id="docType" name="docType">
                                    <option value="">Tous les types</option>
                                    {% for docType in filterOptions.docTypes %}
                                        <option value="{{ docType }}" {{ filters.docType == docType ? 'selected' : '' }}>
                                            {{ docType }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>



                            {# Activity Filter #}
                            <div class="col-md-4">
                                <label for="activity" class="form-label">Activité</label>
                                <select class="form-select" id="activity" name="activity">
                                    <option value="">Toutes les activités</option>
                                    {% for activity in filterOptions.activities %}
                                        <option value="{{ activity }}" {{ filters.activity == activity ? 'selected' : '' }}>
                                            {{ activity }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>

                            {# Process Type Filter #}
                            <div class="col-md-4">
                                <label for="procType" class="form-label">Type Processus</label>
                                <select class="form-select" id="procType" name="procType">
                                    <option value="">Tous les processus</option>
                                    {% for procType in filterOptions.procTypes %}
                                        <option value="{{ procType }}" {{ filters.procType == procType ? 'selected' : '' }}>
                                            {{ procType }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                            {# Current Step Filter #}
                            
                           <div class="col-md-4">
                                <label for="currentStep" class="form-label">Étape Actuelle</label>
                                <select class="form-select" id="currentStep" name="currentStep">
                                    <option value="">Toutes les étapes</option>
                                    {% for step in filterOptions.currentSteps %}
                                        {# On récupère le libellé correspondant ou on formate par défaut #}
                                        {% set label = stepLabels[step] ?? (step|replace({'_':' '})|title) %}
                                        <option value="{{ step }}" {{ filters.currentStep == step ? 'selected' : '' }}>
                                            {{ label }}
                                        </option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex gap-2 flex-wrap">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i>Rechercher
                                    </button>
                                    <button type="button" class="btn btn-secondary" id="clearFilters">
                                        <i class="fas fa-times me-1"></i>Effacer les filtres
                                    </button>
                                    <button type="button" class="btn btn-success" id="exportCsv">
                                        <i class="fas fa-download me-1"></i>Exporter CSV
                                    </button>
                                    <div class="ms-auto">
                                        <span class="badge bg-info fs-6">
                                            Total: {{ pagination.getTotalItemCount }} documents
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            {# Table responsive avec amélioration de style #}
            <div class="table-responsive">
                <table class="table table-hover align-middle shadow-sm">
                    <thead id="table-head">
                        <tr>
                            <th scope="col" class="text-center">Pack</th>
                            <th scope="col" class="text-center">Activity</th>
                            <th scope="col" class="text-center" colspan="2">Reference</th>
                            <th scope="col" class="text-center" colspan="2">Prod Drawing</th>
                            <th scope="col" class="text-center">Type</th>
                            <th scope="col" class="text-center" colspan="2">Commentaires</th>
                            <th scope="col">Cheminement</th>
                        </tr>
                    </thead>
                    <tbody>
                    {% if results|length > 0 %}
                        {% for result in results %}
                            <tr>
                                <td class="text-center"><a href="{{ path('detail_package', {'id': result.document.relPack.id}) }}" class="badge bg-primary pack-link">{{result.document.relPack.id}}</a></td>
                                <td class="text-center">{{ result.document.relPack.activity }}</td>
                                <td class="text-center">{{ result.document.reference }}</td>
                                <td class="text-center">{{ result.document.refRev }}</td>
                                <td class="text-center">{{ result.document.prodDraw }}</td>
                                <td class="text-center">{{ result.document.prodDrawRev }}</td>
                                <td class="text-center type">
                                    <p class="mb-0">{{ result.document.docType }}</p>
                                    <p class="mb-0">{{ result.document.procType }}</p>
                                </td>
                            <td class="text-center px-0">
                                {% if result.document.PrincipalCommentaires|length > 0 %}
                                    <div class="tooltip-container"  data-bs-toggle="tooltip" data-bs-html="true" title="
                                        {% for comment in result.document.PrincipalCommentaires %}
                                            <p class='text-nowrap mb-0' >{{ comment }}</p>
                                        {% endfor %}
                                    ">
                                        <span class="tooltip-text"><i class="fa-solid fa-user"></i></span>
                                    </div>
                                {% endif %}
                            </td>
                            <td class="text-center px-0">
                                {% if result.document.GlobalCommentaires|length > 0 %}
                                    <div class="tooltip-container" data-bs-toggle="tooltip" data-bs-html="true" title="
                                        {% for comment in result.document.GlobalCommentaires %}
                                            <p class='text-nowrap mb-0' >{{ comment }}</p>
                                        {% endfor %}
                                    ">
                                        <span class="tooltip-text"><i class="fa-solid fa-users"></i></span>
                                    </div>
                                {% endif %}
                            </td>

                            <td class="gap-3 p-3" style="white-space: wrap;">
                                {% for place, val in result.oldPlaces %}
                                    <a
                                        class="badge rounded-pill old-place"
                                        style="text-decoration: none;">
                                            {{ stepLabels[place] ?? (place|replace({'_': ' '})|title) }}
                                    </a>
                                {% endfor %}
                                {% for place, val in result.paniers.oldPlaces %}
                                    <a
                                        class="badge rounded-pill old-place panier"
                                        style="text-decoration: none;">
                                        {{ stepLabels[place] ?? (place|replace({'_': ' '})|title) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.currentSteps %}
                                    <a href="{{ path('app_document_place', { 'place': place }) }}?document-id={{ result.document.id }}#document-id={{ result.document.id }}"
                                       class="badge rounded-pill current-step"
                                       style="text-decoration: none;">
                                        {{ stepLabels[place] ?? (place|replace({'_': ' '})|title) }}
                                    </a>
                                {% endfor %}
                                {% for place, val in result.paniers.currentSteps %}
                                    <a href="{{ path('app_document_place', { 'place': place }) }}#onlget={{ place }}&document-id={{ result.document.id }}"
                                       class="badge rounded-pill current-step panier"
                                       style="text-decoration: none;">
                                        {{ stepLabels[place] ?? (place|replace({'_': ' '})|title) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.visitedPlaces %}
                                    <a
                                        class="badge rounded-pill visited-place"
                                        style="text-decoration: none;">
                                        {{ stepLabels[place] ?? (place|replace({'_': ' '})|title) }}
                                    </a>
                                {% endfor %}

                                {% for place, val in result.paniers.visitedPlaces %}
                                    <a
                                        class="badge rounded-pill visited-place panier"
                                        style="text-decoration: none;">
                                        {{ stepLabels[place] ?? (place|replace({'_': ' '})|title) }}
                                    </a>
                                {% endfor %}
                            </td>
                        </tr>
                        {% endfor %}
                    {% else %}
                        <tr>
                            <td colspan="10" class="text-center py-5">
                                <div class="d-flex flex-column align-items-center">
                                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">Aucun document trouvé</h5>
                                    <p class="text-muted">Essayez de modifier vos critères de recherche ou de supprimer certains filtres.</p>
                                    <button type="button" class="btn btn-outline-primary" id="clearFiltersNoResults">
                                        <i class="fas fa-times me-1"></i>Effacer tous les filtres
                                    </button>
                                </div>
                            </td>
                        </tr>
                    {% endif %}
                    </tbody>
                </table>
            </div>

            {# Pagination améliorée #}
            <div>
                {{ knp_pagination_render(pagination) }}
            </div>

        </div>
    </div>
</div>

<style>
/* Styles pour les badges */
.old-place {
    background-color: #28a745;
    color: white;
}

.current-step {
    background-color:rgb(0, 17, 255);
    color: white;
}

.visited-place {
    background-color:rgb(73, 161, 255);
    color: white;
}

table thead th {
    font-weight: bold;
}

* {
    user-select: none;
}

td, span {
    font-size: 0.90rem;
    white-space: nowrap;
}

.type{
    font-size: 0.75rem;
}

th {
    font-size: 0.95rem;
    white-space: nowrap;
    background-color: #004080!important;
    color: #fff!important;

}
.old-place.panier {
    background: repeating-linear-gradient(
    -55deg,
    #28a745,
    #28a745 10px,
    rgba(40, 167, 69, 0.65) 10px,
    rgba(40, 167, 69, 0.65) 20px
    );
}

.current-step.panier {
    background: repeating-linear-gradient(
    -55deg,
    rgb(0, 17, 255),
    rgb(0, 17, 255) 10px,
    rgb(0, 17, 255, 0.65) 10px,
    rgb(0, 17, 255, 0.65) 20px
    );
}

.visited-place.panier {
    background: repeating-linear-gradient(
    -55deg,
        rgb(73, 161, 255),
        rgb(73, 161, 255) 10px,
        rgba(73, 161, 255, 0.65) 10px,
        rgb(73, 161, 255, 0.65) 20px
    );
}

.panier {
    background: repeating-linear-gradient(
    -55deg,
    rgb(95, 95, 95),
    rgb(95, 95, 95) 10px,
    rgba(44, 44, 44, 0.65) 10px,
    rgba(44, 44, 44, 0.65) 20px
);
}

/* Enhanced styling for search and filter section */
.card-header.bg-primary {
    background: linear-gradient(135deg, #0059B3 0%, #004080 100%) !important;
}

.form-label {
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.form-control:focus, .form-select:focus {
    border-color: #0059B3;
    box-shadow: 0 0 0 0.2rem rgba(0, 89, 179, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #0059B3 0%, #004080 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #004080 0%, #003366 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 89, 179, 0.3);
}

.btn-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%);
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    transition: all 0.3s ease;
}

.btn-success:hover {
    background: linear-gradient(135deg, #20c997 0%, #17a2b8 100%);
    transform: translateY(-1px);
}

.table {
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table thead th {
    background: linear-gradient(135deg, #004080 0%, #0059B3 100%) !important;
    border: none !important;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(0, 89, 179, 0.1) !important;
    transform: scale(1.01);
}

.badge {
    font-size: 0.75rem;
    padding: 0.5em 0.75em;
    margin: 0.1rem;
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.05);
}

.input-group-text {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-color: #ced4da;
}

/* Search highlighting */
mark {
    background-color: #fff3cd;
    padding: 0.1em 0.2em;
    border-radius: 0.2em;
    font-weight: bold;
}

/* Loading states */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .d-flex.gap-2.flex-wrap {
        flex-direction: column;
    }

    .d-flex.gap-2.flex-wrap .btn {
        margin-bottom: 0.5rem;
    }

    .ms-auto {
        margin-left: 0 !important;
        margin-top: 1rem;
    }
}

/* Animation for filter cards */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

/* Loading overlay */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    border-radius: 10px;
}

.table-responsive {
    position: relative;
}

/* Improved form feedback */
.form-control:invalid {
    border-color: #dc3545;
}

.form-control:valid {
    border-color: #28a745;
}

</style>
<script>
    const tooltipTriggerList = document.querySelectorAll('[data-bs-toggle="tooltip"]')
    const tooltipList = [...tooltipTriggerList].map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl, {
        customClass: 'custom-tooltip'
    }))

    // Attendre que jQuery et le DOM soient prêts
    $(document).ready(function() {
        // Removed auto-search functionality - users must click "Rechercher" button

        // Clear filters functionality
        $('#clearFilters, #clearFiltersNoResults, #clearFiltersAlert').on('click', function() {
            // Clear all form inputs
            $('#filterForm')[0].reset();

            // Remove URL parameters and reload
            window.location.href = '{{ path('suivie_ref') }}';
        });

        // Export CSV functionality
        $('#exportCsv').on('click', function() {
            // Get current filter values
            const formData = $('#filterForm').serialize();

            // Create export URL with current filters
            const exportUrl = '{{ path('suivie_ref_export') }}?' + formData;

            // Show loading indicator
            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-1"></i>Génération...');

            // Create temporary link and trigger download
            const link = document.createElement('a');
            link.href = exportUrl;
            link.download = 'suivi_documents_' + new Date().toISOString().slice(0, 19).replace(/:/g, '-') + '.csv';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // Reset button after a short delay
            setTimeout(() => {
                $(this).prop('disabled', false).html('<i class="fas fa-download me-1"></i>Exporter CSV');
            }, 2000);
        });

        // Show loading indicator during form submission
        $('#filterForm').on('submit', function(e) {
            // Optionnel : ajouter un indicateur de chargement ici si nécessaire
        });

        // Function to show global loading indicator
        function showGlobalLoading() {
            // Show loading text on search button (sans désactiver les champs)
            $('#filterForm button[type="submit"]').html('<i class="fas fa-spinner fa-spin me-1"></i>Recherche...');

            // Add loading overlay to table
            if (!$('.loading-overlay').length) {
                $('.table-responsive').append('<div class="loading-overlay"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Chargement...</span></div></div>');
            }
        }
    });
</script>

<style>
.custom-tooltip .tooltip-inner {
    white-space: normal;
    max-width: none;
}
</style>

{% endblock %}
