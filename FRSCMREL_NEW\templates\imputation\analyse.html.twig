{% extends 'baseImputation.html.twig' %}

{% block title %}
    Analyse des imputations
{% endblock %}

{% block body %}

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<div class="mt-4" style="margin: 0 7%;">

    <!-- Section de filtrage par plage de dates -->
    <div class="card mb-4">
        <div class="card-body">
            <h3 class="mb-2">Analyse des imputations</h3>
            <form id="dateForm" class="row">
                <div class="form-group col">
                    <label for="startDate" class="mr-2">Date de début :</label>
                    <input type="date" id="startDate" class="form-control" value="{{ 'now'|date_modify('-3 years')|date('Y-m-d') }}">
                </div>
                <div class="form-group col">
                    <label for="endDate" class="mr-2">Date de fin :</label>
                    <input type="date" id="endDate" class="form-control" value="{{ 'now'|date('Y-m-d') }}">
                </div>
                <div class="form-group col">
                    <label for="projectSelect" >Projet :</label>
                    <select id="projectSelect" class="selectpicker form-control" data-live-search="true" title="Sélectionnez un projet" data-style="btn border" data-size="10">
                        <option value="">Tous les projets</option>
                        {% for project in projects|reverse %}
                            <option value="{{ project.id }}">{{ project.otp }} - {{ project.title }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="form-group col-auto">
                    <label class="invisible">*</label>
                    <button id="filterDates" type="button" class="btn btn-primary w-100">Appliquer</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Grille de graphiques -->
    <div class="row">
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h4 class="card-title">Total des heures par Code</h4>
                    <canvas id="chartByCode" width="600" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-md-6 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h4 class="card-title">
                        Total des heures par Mois -
                        <span class="ms-2 badge fs-6" style="background-color: rgba(153, 102, 255, 1);">Total mensuel</span>
                        <span class="ms-2 badge fs-6" style="background-color: rgba(54, 162, 235, 1);">Total cumulatif</span>
                    </h4>
                    <canvas id="chartByMonth" width="600" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="card-title">
                    Total des heures par Mois par Utilisateur -
                    {# legend span rgba(255, 159, 64, 1) rgba(54, 162, 235, 1) #}
                    <span class="ms-2 fs-6 badge" style="background-color: rgba(255, 159, 64, 1);">Total mensuel</span>
                    <span class="ms-2 fs-6 badge" style="background-color: rgba(54, 162, 235, 1);">Total cumulatif</span>
                    <span class="ms-2 fs-6 badge bg-secondary" id="nbHeures" style="display: none;"></span></h4>
                <div class="d-flex">
                    <div class="form-group me-2">
                        <select id="userSelect" class="selectpicker form-control" data-live-search="true" title="Sélectionnez un utilisateur" data-style="btn border" data-size="10">
                            {% for user in users %}
                                {% if user.id == app.user.id %}
                                    <option value="{{ user.id }}" selected>{{ user.nom }} {{ user.prenom }}</option>
                                {% else %}
                                    <option value="{{ user.id }}">{{ user.nom }} {{ user.prenom }}</option>
                                {% endif %}
                            {% endfor %}
                        </select>
                    </div>
                </div>
            </div>
            <canvas id="chartByMonthUser" width="1200" height="400"></canvas>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Variables globales pour les graphiques
    window.chartByCode = null;
    window.chartByMonth = null;
    window.chartByMonthUser = null;

    // Fonction pour mettre à jour le graphique par Code
    function updateChartByCode() {
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        var projectId = $('#projectSelect').val(); // récupération du filtre Projet

        $.ajax({
            url: '{{ path("app_impute_data_code") }}',
            method: 'GET',
            data: { 
                start_date: startDate, 
                end_date: endDate,
                project_id: projectId
            },
            success: function(response) {
                var labels = [];
                var data = [];
                response.forEach(function(item) {
                    labels.push(item.code_id);
                    data.push(item.total_heures);
                });
                var ctx = document.getElementById('chartByCode').getContext('2d');
                if (window.chartByCode instanceof Chart) {
                    window.chartByCode.destroy();
                }
                window.chartByCode = new Chart(ctx, {
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [{
                            label: 'Total heures',
                            data: data,
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            borderColor: 'rgba(75, 192, 192, 1)',
                            borderWidth: 1
                        }]
                    },
                    options: {
                        plugins: {
                            legend: { display: false }
                        },
                        scales: {
                            y: { beginAtZero: true },
                            x: {
                                ticks: {
                                    font: { size: 10 }
                                }
                            }
                        }
                    }
                });
            }
        });
    }

    // Fonction pour mettre à jour le graphique par Mois
    function updateChartByMonth() {
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        var projectId = $('#projectSelect').val(); // récupération du filtre Projet

        $.ajax({
            url: '{{ path("app_impute_data_month") }}',
            method: 'GET',
            data: { 
                start_date: startDate, 
                end_date: endDate,
                project_id: projectId
            },
            success: function(response) {
                var labels = [];
                var data = [];
                response.forEach(function(item) {
                    labels.push(item.mois);
                    data.push(item.total_heures);
                });
                
                // Calcul du cumul des heures
                var cumulativeData = [];
                var sum = 0;
                data.forEach(function(hour) {
                    sum += parseFloat(hour);
                    cumulativeData.push(sum);
                });

                var ctx = document.getElementById('chartByMonth').getContext('2d');
                if (window.chartByMonth instanceof Chart) {
                    window.chartByMonth.destroy();
                }
                window.chartByMonth = new Chart(ctx, {
                    // Le type principal du graphique est la barre,
                    // mais nous ajoutons un dataset de type 'line'
                    type: 'bar',
                    data: {
                        labels: labels,
                        datasets: [
                            {
                                type: 'bar',
                                label: 'Total heures',
                                data: data,
                                backgroundColor: 'rgba(153, 102, 255, 0.2)',
                                borderColor: 'rgba(153, 102, 255, 1)',
                                borderWidth: 1
                            },
                            {
                                type: 'line',
                                label: 'Total heures cumulatif',
                                data: cumulativeData,
                                fill: false,
                                borderColor: 'rgba(54, 162, 235, 1)',
                                borderWidth: 2,
                                tension: 0.1
                            }
                        ]
                    },
                    options: {
                        plugins: {
                            legend: { display: false } // Affichage de la légende pour distinguer les deux datasets
                        },
                        scales: {
                            y: { beginAtZero: true }
                        }
                    }
                });
            }
        });
    }

    // Fonction pour mettre à jour le graphique par Mois pour un utilisateur avec deux courbes : normale et cumulative
    function updateChartByMonthUser(userId) {
        var startDate = $('#startDate').val();
        var endDate = $('#endDate').val();
        var projectId = $('#projectSelect').val(); // récupération du filtre Projet

        $.ajax({
            url: '{{ path("app_impute_data_month_user") }}',
            method: 'GET',
            data: { 
                start_date: startDate, 
                end_date: endDate, 
                user_id: userId,
                project_id: projectId
            },
            success: function(response) {
                var labels = [];
                var normalData = [];
                response.forEach(function(item) {
                    labels.push(item.mois);
                    normalData.push(item.total_heures);
                });
                // Calcul du cumul des heures
                var cumulativeData = [];
                var sum = 0;
                normalData.forEach(function(val) {
                    sum += parseFloat(val);
                    cumulativeData.push(sum);
                });
                $('#nbHeures').text(sum + ' heures').show();
                var ctx = document.getElementById('chartByMonthUser').getContext('2d');
                if (window.chartByMonthUser instanceof Chart) {
                    window.chartByMonthUser.destroy();
                }
                window.chartByMonthUser = new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: labels,
                        datasets: [
                            {
                                label: 'Total heures mensuel',
                                data: normalData,
                                fill: false,
                                borderColor: 'rgba(255, 159, 64, 1)',
                                tension: 0.1
                            },
                            {
                                label: 'Total heures cumulatif',
                                data: cumulativeData,
                                fill: false,
                                borderColor: 'rgba(54, 162, 235, 1)',
                                tension: 0.1
                            }
                        ]
                    },
                    options: {
                        plugins: {
                            legend: { display: false }
                        },
                        scales: { y: { beginAtZero: true } }
                    }
                });
            }
        });
    }

    // Fonction globale pour mettre à jour tous les graphiques
    function updateAllCharts() {
        Toast.fire({ icon: 'info', title: 'Mise à jour des graphiques...' });
        updateChartByCode();
        updateChartByMonth();
        var userId = $('#userSelect').val();
        if (userId) {
            updateChartByMonthUser(userId);
        } else if (window.chartByMonthUser instanceof Chart) {
            window.chartByMonthUser.destroy();
        }
    }

    // Mise à jour initiale des graphiques
    updateAllCharts();

    // Actualiser les graphiques lors du clic sur le bouton "Appliquer"
    $('#filterDates').on('click', function() {
        updateAllCharts();
    });

    // Actualiser le graphique utilisateur lors du changement de sélection d'utilisateur
    $('#userSelect').on('change', function() {
        var userId = $(this).val();
        if (userId) {
            updateChartByMonthUser(userId);
        } else if (window.chartByMonthUser instanceof Chart) {
            window.chartByMonthUser.destroy();
        }
    });

    // Actualiser les graphiques lors du changement de sélection de projet
    $('#projectSelect').on('change', function() {
        updateAllCharts();
    });
});

</script>
{% endblock %}
