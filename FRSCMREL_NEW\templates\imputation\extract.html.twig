{% extends 'baseImputation.html.twig' %}

{% block title %}
    Extraction & Correction
{% endblock %}

{% block body %}
<style>
    td{
        white-space: nowrap;
        vertical-align: middle;
    }

    th{
        white-space: nowrap;
        vertical-align: middle;
    }

    .imputation-item.active>td {
        background-color: #e9ecef!important;
    }
</style>

<div class="mx-3">
    <div class="d-flex justify-content-between align-items-center">
        <h3 class="mb-2 mt-2">Extraction & Correction</h3>

        <div id="checkboxes-div">
            <button link="{{ path('app_impute_export_sap') }}" class="btn btn-sm btn-primary export-file">Exporter SAP</button>
            <button link="{{ path('app_impute_export') }}" class="btn btn-sm btn-secondary export-file">Exporter CI</button>
            <div class="form-check form-check-inline fw-bold">
                <input class="form-check-input filter-check" type="checkbox" checked id="ci-checkbox">
                <label class="form-check-label" for="ci-checkbox">CI</label>
            </div>
            <div class="form-check form-check-inline fw-bold">
                <input class="form-check-input filter-check" type="checkbox" checked id="sap-checkbox">
                <label class="form-check-label" for="sap-checkbox">SAP</label>
            </div>
        </div>
    </div>
    <div id="imputations-container" >
        <div class="table-responsive" style="max-height: 80vh; overflow-y: auto;">
            <table class="table w-100 mb-0 table-hover">
                <thead style="position: sticky; top: 0px; background: white; z-index: 10;">
                    <tr class="border-0">
                        <th class="bg-light border-0"><input type="text" class="form-control form-control-sm filter-fields" placeholder="yyyy-mm-dd" id="periode-filter" value="{{ 'now'|date('Y-m') }}"></th>
                        <th class="bg-light border-0"><input type="text" class="form-control form-control-sm filter-fields text-center" placeholder="Utilisateur" id="username-filter"></th>
                        <th class="bg-light border-0"><input type="text" class="form-control form-control-sm filter-fields" placeholder="Projet" id="projet-filter"></th>
                        <th class="bg-light border-0"><input type="text" class="form-control form-control-sm filter-fields" placeholder="Phase" id="phase-filter"></th>
                        <th class="bg-light border-0"><input type="text" class="form-control form-control-sm filter-fields text-center" placeholder="Heures" id="nbHeures-filter"></th>
                        <th class="bg-light border-0"><input type="text" class="form-control form-control-sm filter-fields text-center" placeholder="Code" id="code-filter"></th>
                        <th class="bg-light border-0"><input type="text" class="form-control form-control-sm filter-fields text-center" placeholder="Poste" id="workCenter-filter"></th>
                        <th class="bg-light border-0 text-center"><span id="nbImputations" class="badge bg-primary fw-bold">0</span></th>
                    </tr>
                    <tr>
                        <th class="bg-light">Période</th>
                        <th class="text-center bg-light">Utilisateur</th>
                        <th class="bg-light">Projet</th>
                        <th class="bg-light">Phase</th>
                        <th class="text-center bg-light">Heures</th>
                        <th class="text-center bg-light">Code</th>
                        <th class="text-center bg-light">Poste</th>
                        <th class="text-center bg-light">Supprimer</th>
                    </tr>
                </thead>
                <tbody id="imputations-tbody">
                </tbody>
                <tfoot style="position: sticky; bottom: 0px; background: white; z-index: 10;"><tr class="fw-bold border-0">
                    <input type="hidden" id="imputation-id">
                    <td class="border-0 bg-light"><input type="text" class="form-control form-control-sm" disabled="" placeholder="Période" id="periode"></td>
                    <td class="border-0 bg-light"><input type="text" class="form-control form-control-sm text-center" disabled="" placeholder="Utilisateur" id="username"></td>
                    <td class="border-0 bg-light"><input type="text" class="form-control form-control-sm" disabled="" placeholder="Projet" id="projet"></td>
                    <td class="border-0 bg-light"><input type="text" class="form-control form-control-sm" disabled="" placeholder="Phase" id="phase"></td>
                    <td class="border-0 bg-light"><input type="text" class="form-control form-control-sm text-center" placeholder="Heures" id="nbHeures"></td>
                    <td class="border-0 bg-light" colspan="2"><input type="text" class="form-control form-control-sm text-center" disabled="" placeholder="Code" id="code"></td>
                    <td class="border-0 bg-light"><button class="btn btn-sm btn-primary w-100" id="saveNbHeures">Modifier</button></td></tr>
                </tfoot>
            </table>
        </div>    
    </div>
</div>

<script>
function getImputations(periode = '', username = '', projet = '', phase = '', nbHeures = '', code = '', workCenter = '', ci = 1, sap = 1) {
    Toast.fire({
        icon: 'info',
        title: 'Chargement des imputations...'
    });
    return new Promise((resolve, reject) => {
        $.ajax({
            url: "{{ path('app_impute_filtered') }}",
            type: 'GET',
            data: {
                periode: periode,
                username: username,
                projet: projet,
                phase: phase,
                nbHeures: nbHeures,
                code: code,
                workCenter: workCenter,
                ci: ci,
                sap: sap
            },
            success: function(response) {
                Toast.fire({
                    icon: 'success',
                    title: 'Imputations chargées !'
                });
                resolve(response.imputations); // On résout la Promise avec les imputations
            },
            error: function(error) {
                Toast.fire({
                    icon: 'error',
                    title: 'Erreur lors du chargement !'
                });
                reject(error); // On rejette la Promise en cas d'erreur
            }
        });
    });
}

function fillImputations(imputations) {
    $('#imputations-tbody').empty();
    if (imputations.length === 0) {
        $('#imputations-tbody').empty().append(
            $('<td>')
                .attr('colspan', 8)
                .addClass('alert alert-warning w-100 text-center mt-3')
                .text('Aucune imputation trouvée.')
        );
        return;
    }else{
        $('#nbImputations').text(imputations.length);
    }

    imputations.reverse();


    imputations.forEach(imputation => {
        $('<tr>').addClass('imputation-item').attr('data-imputation-id', imputation.id)
            .append($('<td>').text(imputation.periode.charAt(0).toUpperCase() + imputation.periode.slice(1)))
            .append($('<td>').text(imputation.username.toUpperCase()).addClass('text-center'))
            .append($('<td>').text(imputation.code.projet))
            .append(
                $('<td>').html((imputation.code.phaseStatus ? '<i class="ms-2 fa-regular fa-circle-dot" style="color: #198754;" title="Phase ouverte"></i>' + (imputation.code.phaseStatusManuel === null || imputation.code.phaseStatusManuel ? ' <i class="fa-regular fa-thumbs-up me-2" style="color: #198754" title="Phase ouverte"></i>' : ' <i class="fa-regular fa-hand me-2" style="color: #DC3545" title="Fermée par Chef de projet"></i>') : '<i class="ms-2 me-2 fa-regular fa-circle-dot" style="color: #DC3545;" title="Phase fermée"></i><i class="fa-regular fa-thumbs-down me-2 invisible" style="color: #DC3545" title="Phase fermée"></i>') + imputation.code.phase)
            )
            .append(
                $('<td>').addClass('text-center').append($('<span>').text(imputation.nbHeures).addClass('nbHeures'))
            )
            .append($('<td>').text(imputation.code.code).addClass('text-center'))
            .append($('<td>').text(imputation.code.workCenter).addClass('text-center'))
            .append(
                $('<td>').addClass('text-center')
                    .append($('<i class="fas fa-trash-alt delete-impute"></i>').css('cursor', 'pointer').attr('data-imputation-id', imputation.id))
            )
            .appendTo($('#imputations-tbody'));
    });
}

document.addEventListener('DOMContentLoaded', function() {
    $('#periode-filter').trigger('change'); // Trigger change event to load initial data
});

$(document).on('click', '.delete-impute', function() {
    var imputationId = $(this).data('imputation-id');
    Swal.fire({
        title: 'Êtes-vous sûr?',
        text: "Vous ne pourrez pas revenir en arrière!",
        showCancelButton: true,
        confirmButtonText: 'Oui supprimer!',
        cancelButtonText: 'Non',
        customClass: {
            confirmButton: 'btn btn-sm btn-primary',
            cancelButton: 'btn btn-sm btn-danger'
        },
    }).then((result) => {
        if (result.isConfirmed) {
            $.ajax({
                url: "{{ path('app_impute_delete', { 'id': 'edit_me' }) }}".replace('edit_me', imputationId),
                type: 'DELETE',
                success: function(response) {
                    updateImputation();
                    Toast.fire({
                        icon: 'success',
                        title: 'Imputation supprimée !'
                    });
                },
                error: function(error) {
                    Toast.fire({
                        icon: 'error',
                        title: 'Vous ne pouvez pas supprimer cette imputation !'
                    });
                }
            });
        }
    });
});

function updateImputation() {
    let periode = $('#periode-filter').val();
    let username = $('#username-filter').val();
    let projet = $('#projet-filter').val();
    let phase = $('#phase-filter').val();
    let nbHeures = $('#nbHeures-filter').val();
    let code = $('#code-filter').val();
    let workCenter = $('#workCenter-filter').val();
    let ci = $('#ci-checkbox').is(':checked') ? 1 : 0;
    let sap = $('#sap-checkbox').is(':checked') ? 1 : 0;

    getImputations(periode, username, projet, phase, nbHeures, code, workCenter, ci, sap).then(imputations => {
        fillImputations(imputations);
    });

    $('#imputation-id').val('');
    $('#periode').val('');
    $('#username').val('');
    $('#projet').val('');
    $('#phase').val('');
    $('#nbHeures').val('');
    $('#code').val('');
};

$(document).on('change', '.filter-fields', function() {
    updateImputation();
});

$(document).on('click', '.filter-check', function() {
    updateImputation();
});

$(document).on('click', '.imputation-item', function() {
    let imputationId = $(this).data('imputation-id');
    $('.imputation-item').removeClass('active');
    $(this).addClass('active');
    var container = $('#imputations-container');
    var scrollTo = $(this);
    container.animate({
        scrollTop: scrollTo.offset().top - container.offset().top + container.scrollTop()
    });
    $.ajax({
        url: "{{ path('app_impute_show', { 'id': 'edit_me' }) }}".replace('edit_me', imputationId),
        type: 'GET',
        success: function(imputation) {
            $('#imputation-id').val(imputation.id);
            $('#periode').val(imputation.periode);
            $('#username').val(imputation.username);
            $('#projet').val(imputation.code.projet);
            $('#phase').val(imputation.code.phase);
            $('#nbHeures').val(imputation.nbHeures);
            $('#code').val(imputation.code.code);
        }
    });
});

function editNbHeures(imputationId, nbHeures) {
    var url = "{{ path('app_impute_edit', { 'id': 'edit_me' }) }}";
    url = url.replace('edit_me', imputationId);
    Toast.fire({
        icon: 'info',
        title: 'Modification de l\'imputation...'
    });
    return new Promise((resolve, reject) => {
        $.ajax({
            url: url,
            type: 'PUT',
            data: { nbHeures: nbHeures },
            success: function(response) {
                Toast.fire({
                    icon: 'success',
                    title: 'Imputation modifiée !'
                });
                resolve(response);
            },
            error: function(error) {
                Toast.fire({
                    icon: 'error',
                    title: 'Vous ne pouvez pas modifier cette imputation !'
                });
                reject(error);
            }
        });
    });
}

$(document).on('click', '#saveNbHeures', function() {
    var imputationId = $('#imputation-id').val();
    var nbHeures = $('#nbHeures').val();
    editNbHeures(imputationId, nbHeures).then(response => {
        updateImputation();
        Toast.fire({
            icon: 'success',
            title: 'Imputation modifiée !'
        });
    });
});

$(document).on('click', '.export-file', function() {
    let periode = $('#periode-filter').val();
    let username = $('#username-filter').val();
    let projet = $('#projet-filter').val();
    let phase = $('#phase-filter').val();
    let nbHeures = $('#nbHeures-filter').val();
    let code = $('#code-filter').val();
    let workCenter = $('#workCenter-filter').val();
    let ci = $('#ci-checkbox').is(':checked') ? 1 : 0;
    let sap = $('#sap-checkbox').is(':checked') ? 1 : 0;

    let lien = $(this).attr('link');

    // Construire l'URL avec les paramètres GET
    let url = `${lien}?periode=${encodeURIComponent(periode)}&username=${encodeURIComponent(username)}&projet=${encodeURIComponent(projet)}&phase=${encodeURIComponent(phase)}&nbHeures=${encodeURIComponent(nbHeures)}&code=${encodeURIComponent(code)}&workCenter=${encodeURIComponent(workCenter)}&ci=${ci}&sap=${sap}`;

    // Rediriger l'utilisateur vers l'URL pour déclencher le téléchargement
    window.location.href = url;
});

</script>
{% endblock %}