{#
/**
 * Custom pagination template that preserves query parameters
 */
#}
{% if pageCount > 1 %}
    <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
            {% if previous is defined %}
                <li class="page-item">
                    <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): previous})) }}" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Previous">
                        <span aria-hidden="true">&laquo;</span>
                    </span>
                </li>
            {% endif %}

            {% if startPage > 1 %}
                <li class="page-item">
                    <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): 1})) }}">1</a>
                </li>
                {% if startPage == 3 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): 2})) }}">2</a>
                    </li>
                {% elseif startPage != 2 %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                {% endif %}
            {% endif %}

            {% for page in pagesInRange %}
                {% if page != current %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): page})) }}">{{ page }}</a>
                    </li>
                {% else %}
                    <li class="page-item active" aria-current="page">
                        <span class="page-link">{{ page }}</span>
                    </li>
                {% endif %}
            {% endfor %}

            {% if endPage < pageCount %}
                {% if endPage != pageCount - 1 %}
                    <li class="page-item disabled">
                        <span class="page-link">…</span>
                    </li>
                {% elseif endPage == pageCount - 2 %}
                    <li class="page-item">
                        <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): pageCount - 1})) }}">{{ pageCount - 1 }}</a>
                    </li>
                {% endif %}
                <li class="page-item">
                    <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): pageCount})) }}">{{ pageCount }}</a>
                </li>
            {% endif %}

            {% if next is defined %}
                <li class="page-item">
                    <a class="page-link" href="{{ path(route, query|merge({(pageParameterName): next})) }}" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link" aria-label="Next">
                        <span aria-hidden="true">&raquo;</span>
                    </span>
                </li>
            {% endif %}
        </ul>
    </nav>
{% endif %}
