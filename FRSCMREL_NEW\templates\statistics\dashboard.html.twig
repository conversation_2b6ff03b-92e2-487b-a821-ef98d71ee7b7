{% extends 'base.html.twig' %}

{% block title %}Statistiques Documents - Phase 1{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="{{ asset('css/statistics.css') }}" rel="stylesheet">
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --info-color: #3b82f6;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
            --border-color: #e5e7eb;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }

        * {
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
            color: var(--dark-color);
        }

        .stats-header {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            padding: 40px 0;
            margin-bottom: 40px;
            position: relative;
            overflow: hidden;
        }

        .stats-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/><circle cx="10" cy="60" r="0.5" fill="white" opacity="0.1"/><circle cx="90" cy="40" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .stats-header h1 {
            font-weight: 700;
            font-size: 2.5rem;
            margin-bottom: 0.5rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .stats-header .lead {
            font-weight: 400;
            opacity: 0.9;
            font-size: 1.1rem;
        }

        .stats-card {
            background: white;
            border-radius: 16px;
            box-shadow: var(--shadow-lg);
            padding: 24px;
            margin-bottom: 24px;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .stats-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-xl);
        }

        .stats-card:hover::before {
            opacity: 1;
        }

        .stats-card h5 {
            font-weight: 600;
            color: var(--dark-color);
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stats-card h5 i {
            color: var(--primary-color);
            font-size: 1.1em;
        }

        .metric-card {
            background: white;
            border-radius: 12px;
            box-shadow: var(--shadow-md);
            padding: 20px;
            text-align: center;
            border: 1px solid var(--border-color);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .metric-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: var(--primary-color);
            transform: scaleX(0);
            transition: transform 0.3s ease;
        }

        .metric-card:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
        }

        .metric-card:hover::before {
            transform: scaleX(1);
        }

        .metric-value {
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 8px;
            line-height: 1;
        }

        .metric-label {
            color: #6b7280;
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
            font-style: italic;
        }

        .loading::before {
            content: '';
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid var(--border-color);
            border-top: 2px solid var(--primary-color);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 8px;
            vertical-align: middle;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            color: var(--error-color);
            padding: 16px;
            border-radius: 8px;
            margin: 12px 0;
            border-left: 4px solid var(--error-color);
            font-weight: 500;
        }

        .success {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            color: var(--success-color);
            padding: 16px;
            border-radius: 8px;
            margin: 12px 0;
            border-left: 4px solid var(--success-color);
            font-weight: 500;
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
            background: #fafafa;
            border-radius: 8px;
            padding: 10px;
        }

        .table-responsive {
            max-height: 400px;
            overflow-y: auto;
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .table {
            margin-bottom: 0;
        }

        .table thead th {
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            color: var(--dark-color);
            font-weight: 600;
            border: none;
            padding: 16px 12px;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table tbody td {
            padding: 12px;
            border-color: var(--border-color);
            vertical-align: middle;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(102, 126, 234, 0.02);
        }

        .btn {
            font-weight: 500;
            border-radius: 8px;
            padding: 10px 20px;
            transition: all 0.3s ease;
            border: none;
            text-transform: none;
            letter-spacing: 0.025em;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: var(--shadow-md);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-lg);
            background: linear-gradient(135deg, #5a6fd8, #6b46a3);
        }

        .btn-secondary {
            background: #6b7280;
            box-shadow: var(--shadow-sm);
        }

        .btn-secondary:hover {
            background: #4b5563;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .btn-outline-secondary {
            border-color: var(--border-color);
            color: #6b7280;
        }

        .btn-outline-secondary:hover {
            background: #f8fafc;
            border-color: var(--primary-color);
            color: var(--primary-color);
        }

        .badge {
            font-weight: 500;
            padding: 6px 12px;
            border-radius: 6px;
        }

        .badge.bg-primary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
        }

        .badge.bg-info {
            background: linear-gradient(135deg, var(--info-color), #1e40af) !important;
        }

        #debugLogs {
            background: linear-gradient(135deg, #f8fafc, #f1f5f9);
            padding: 20px;
            border-radius: 8px;
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid var(--border-color);
            line-height: 1.5;
        }

        .form-check-input:checked {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        /* Animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .stats-card {
            animation: fadeInUp 0.6s ease forwards;
        }

        .stats-card:nth-child(1) { animation-delay: 0.1s; }
        .stats-card:nth-child(2) { animation-delay: 0.2s; }
        .stats-card:nth-child(3) { animation-delay: 0.3s; }
        .stats-card:nth-child(4) { animation-delay: 0.4s; }

        /* Responsive */
        @media (max-width: 768px) {
            .stats-header h1 {
                font-size: 2rem;
            }

            .metric-value {
                font-size: 2rem;
            }

            .stats-card {
                padding: 16px;
                margin-bottom: 16px;
            }

            .chart-container {
                height: 300px;
            }
        }

        /* Custom scrollbar */
        .table-responsive::-webkit-scrollbar,
        #debugLogs::-webkit-scrollbar {
            width: 6px;
        }

        .table-responsive::-webkit-scrollbar-track,
        #debugLogs::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .table-responsive::-webkit-scrollbar-thumb,
        #debugLogs::-webkit-scrollbar-thumb {
            background: var(--primary-color);
            border-radius: 3px;
        }

        .table-responsive::-webkit-scrollbar-thumb:hover,
        #debugLogs::-webkit-scrollbar-thumb:hover {
            background: var(--secondary-color);
        }
    </style>
{% endblock %}

{% block body %}
<!-- Conteneur pour les notifications toast -->
<div id="toastContainer" class="toast-container"></div>

<div class="stats-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1><i class="fas fa-chart-bar"></i> Statistiques Documents - Phase 1</h1>
                <p class="lead">Interface de test pour les méthodes statistiques de base</p>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Boutons de contrôle -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="stats-card">
                <h5><i class="fas fa-cog"></i> Contrôles</h5>
                <div class="d-flex flex-wrap align-items-center gap-3">
                    <button id="loadAllStats" class="btn btn-primary">
                        <i class="fas fa-sync me-2"></i> Charger toutes les statistiques
                    </button>
                    <button id="clearData" class="btn btn-secondary">
                        <i class="fas fa-trash me-2"></i> Effacer les données
                    </button>
                    <div class="form-check ms-auto">
                        <input class="form-check-input" type="checkbox" id="autoRefresh">
                        <label class="form-check-label" for="autoRefresh">
                            <i class="fas fa-clock me-1"></i> Actualisation automatique (30s)
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Métriques rapides -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="metric-card fade-in-up custom-tooltip" data-tooltip="Nombre total de documents traités dans le système">
                <div class="metric-value" id="totalDocuments">-</div>
                <div class="metric-label">Documents traités</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="metric-card fade-in-up custom-tooltip" data-tooltip="Temps moyen pour compléter un cycle de document">
                <div class="metric-value" id="avgCycleTime">-</div>
                <div class="metric-label">Temps de cycle moyen (jours)</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="metric-card fade-in-up custom-tooltip" data-tooltip="Nombre de validateurs actifs dans le système">
                <div class="metric-value" id="totalValidators">-</div>
                <div class="metric-label">Validateurs actifs</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="metric-card fade-in-up custom-tooltip" data-tooltip="Nombre de visas en attente de validation">
                <div class="metric-value" id="pendingVisas">-</div>
                <div class="metric-label">Visas en attente</div>
            </div>
        </div>
    </div>

    <!-- Graphiques principaux -->
    <div class="row mb-4">
        <!-- Temps par étape du workflow -->
        <div class="col-lg-6 mb-4">
            <div class="stats-card h-100">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-clock"></i> Temps moyen par étape du workflow</h5>
                    <div id="workflowTimeStatus" class="loading">Chargement...</div>
                </div>
                <div class="chart-container custom-scrollbar">
                    <canvas id="workflowTimeChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Statistiques des visas -->
        <div class="col-lg-6 mb-4">
            <div class="stats-card h-100">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-stamp"></i> Statistiques des visas par étape</h5>
                    <div id="visaStatsStatus" class="loading">Chargement...</div>
                </div>
                <div class="chart-container custom-scrollbar">
                    <canvas id="visaStatsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Distribution des types de documents -->
        <div class="col-lg-6 mb-4">
            <div class="stats-card h-100">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-file-alt"></i> Distribution des types de documents</h5>
                    <div id="docTypesStatus" class="loading">Chargement...</div>
                </div>
                <div class="chart-container custom-scrollbar">
                    <canvas id="docTypesChart"></canvas>
                </div>
            </div>
        </div>

        <!-- Tendances de création -->
        <div class="col-lg-6 mb-4">
            <div class="stats-card h-100">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-chart-line"></i> Tendances de création (6 derniers mois)</h5>
                    <div id="creationTrendsStatus" class="loading">Chargement...</div>
                </div>
                <div class="chart-container custom-scrollbar">
                    <canvas id="creationTrendsChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Performance des validateurs -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-users"></i> Performance des validateurs</h5>
                    <div id="validatorPerfStatus" class="loading">Chargement...</div>
                </div>
                <div class="table-responsive custom-scrollbar">
                    <table class="table table-striped table-hover" id="validatorPerfTable">
                        <thead>
                            <tr>
                                <th><i class="fas fa-user me-1"></i>Nom</th>
                                <th><i class="fas fa-building me-1"></i>Département</th>
                                <th><i class="fas fa-stamp me-1"></i>Total Visas</th>
                                <th><i class="fas fa-check me-1"></i>Approuvés</th>
                                <th><i class="fas fa-clock me-1"></i>En attente</th>
                                <th><i class="fas fa-percentage me-1"></i>Taux d'approbation</th>
                                <th><i class="fas fa-stopwatch me-1"></i>Temps de réponse moyen (h)</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Distribution Projet/DMO -->
    <div class="row mb-4">
        <div class="col-lg-6 mb-4">
            <div class="stats-card h-100">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-project-diagram"></i> Top 10 Projets</h5>
                    <div id="projectsStatus" class="loading">Chargement...</div>
                </div>
                <div class="table-responsive custom-scrollbar">
                    <table class="table table-sm table-hover" id="projectsTable">
                        <thead>
                            <tr>
                                <th><i class="fas fa-tag me-1"></i>Projet (OTP)</th>
                                <th><i class="fas fa-info-circle me-1"></i>Titre</th>
                                <th><i class="fas fa-file me-1"></i>Documents</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="col-lg-6 mb-4">
            <div class="stats-card h-100">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-cogs"></i> Top 10 DMOs</h5>
                    <div id="dmosStatus" class="loading">Chargement...</div>
                </div>
                <div class="table-responsive custom-scrollbar">
                    <table class="table table-sm table-hover" id="dmosTable">
                        <thead>
                            <tr>
                                <th><i class="fas fa-code me-1"></i>DMO</th>
                                <th><i class="fas fa-align-left me-1"></i>Description</th>
                                <th><i class="fas fa-file me-1"></i>Documents</th>
                            </tr>
                        </thead>
                        <tbody></tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs et debug -->
    <div class="row">
        <div class="col-12">
            <div class="stats-card">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h5><i class="fas fa-terminal"></i> Logs et Debug</h5>
                    <button id="clearLogs" class="btn btn-sm btn-outline-secondary">
                        <i class="fas fa-broom me-1"></i> Effacer les logs
                    </button>
                </div>
                <div id="debugLogs">
                    <span class="text-info">[Système]</span> Prêt pour les tests...<br>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Variables globales
        let charts = {};
        let autoRefreshInterval = null;
        
        // Fonction pour logger avec styles améliorés
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debugLogs');

            let icon = '';
            let colorClass = '';
            let prefix = '';

            switch(type) {
                case 'error':
                    icon = '<i class="fas fa-times-circle"></i>';
                    colorClass = 'text-danger';
                    prefix = 'ERREUR';
                    break;
                case 'success':
                    icon = '<i class="fas fa-check-circle"></i>';
                    colorClass = 'text-success';
                    prefix = 'SUCCÈS';
                    break;
                case 'warning':
                    icon = '<i class="fas fa-exclamation-triangle"></i>';
                    colorClass = 'text-warning';
                    prefix = 'ATTENTION';
                    break;
                default:
                    icon = '<i class="fas fa-info-circle"></i>';
                    colorClass = 'text-info';
                    prefix = 'INFO';
            }

            logElement.innerHTML += `<span class="${colorClass}">${icon} [${timestamp}] [${prefix}] ${message}</span><br>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        // Fonction pour afficher le statut avec icônes
        function showStatus(elementId, message, type = 'loading') {
            const element = document.getElementById(elementId);
            if (element) {
                element.className = type;
                let icon = '';
                switch(type) {
                    case 'success':
                        icon = '<i class="fas fa-check-circle me-1"></i>';
                        break;
                    case 'error':
                        icon = '<i class="fas fa-exclamation-triangle me-1"></i>';
                        break;
                    case 'loading':
                        icon = '<i class="fas fa-spinner fa-spin me-1"></i>';
                        break;
                    default:
                        icon = '<i class="fas fa-info-circle me-1"></i>';
                }
                element.innerHTML = icon + message;
            }
        }

        // Fonction pour faire une requête AJAX
        async function fetchData(url) {
            try {
                log(`Requête vers: ${url}`);
                const response = await fetch(url);
                const data = await response.json();
                
                if (data.success) {
                    log(`✓ ${data.message}`, 'success');
                    return data.data;
                } else {
                    log(`✗ ${data.message}: ${data.error}`, 'error');
                    throw new Error(data.error);
                }
            } catch (error) {
                log(`✗ Erreur réseau: ${error.message}`, 'error');
                throw error;
            }
        }

        // Fonction pour créer des notifications toast
        function showToast(message, type = 'info', duration = 3000) {
            const toastContainer = document.getElementById('toastContainer') || createToastContainer();

            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.innerHTML = `
                <div class="d-flex align-items-center">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-triangle' : 'info-circle'} me-2"></i>
                    <span>${message}</span>
                    <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
                </div>
            `;

            toastContainer.appendChild(toast);

            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, duration);
        }

        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toastContainer';
            container.className = 'toast-container';
            document.body.appendChild(container);
            return container;
        }

        // Initialisation au chargement de la page
        document.addEventListener('DOMContentLoaded', function() {
            log('Interface de statistiques initialisée');

            // Ajouter des effets visuels initiaux
            addVisualEffects();

            // Event listeners
            document.getElementById('loadAllStats').addEventListener('click', () => {
                loadAllStatistics();
                showToast('Chargement des statistiques en cours...', 'info');
            });

            document.getElementById('clearData').addEventListener('click', () => {
                clearAllData();
                showToast('Données effacées', 'success');
            });

            document.getElementById('clearLogs').addEventListener('click', () => {
                document.getElementById('debugLogs').innerHTML = '<span class="text-info">[Système]</span> Logs effacés...<br>';
                showToast('Logs effacés', 'success');
            });

            // Auto-refresh
            document.getElementById('autoRefresh').addEventListener('change', function() {
                if (this.checked) {
                    autoRefreshInterval = setInterval(loadAllStatistics, 30000);
                    log('Auto-refresh activé (30s)');
                    showToast('Actualisation automatique activée (30s)', 'success');
                } else {
                    if (autoRefreshInterval) {
                        clearInterval(autoRefreshInterval);
                        autoRefreshInterval = null;
                    }
                    log('Auto-refresh désactivé');
                    showToast('Actualisation automatique désactivée', 'info');
                }
            });
        });

        // Fonction pour ajouter des effets visuels
        function addVisualEffects() {
            // Ajouter la classe fade-in-up aux cartes
            document.querySelectorAll('.stats-card').forEach((card, index) => {
                card.style.animationDelay = `${index * 0.1}s`;
                card.classList.add('fade-in-up');
            });

            // Effet de pulsation sur les métriques importantes
            setTimeout(() => {
                document.querySelectorAll('.metric-card').forEach(card => {
                    card.classList.add('highlight');
                    setTimeout(() => card.classList.remove('highlight'), 2000);
                });
            }, 1000);
        }

        // Fonction principale pour charger toutes les statistiques
        async function loadAllStatistics() {
            log('=== Début du chargement des statistiques ===');

            // Ajouter des effets visuels
            addVisualEffects();

            try {
                // Charger les données en parallèle
                const [
                    workflowTime,
                    cycleTime,
                    visaStats,
                    docTypes,
                    creationTrends,
                    validatorPerf,
                    projectDmo
                ] = await Promise.all([
                    fetchData('{{ path('statistics_workflow_time') }}'),
                    fetchData('{{ path('statistics_cycle_time') }}'),
                    fetchData('{{ path('statistics_visa') }}'),
                    fetchData('{{ path('statistics_document_types') }}'),
                    fetchData('{{ path('statistics_creation_trends') }}?months=6'),
                    fetchData('{{ path('statistics_validator_performance') }}'),
                    fetchData('{{ path('statistics_project_dmo') }}')
                ]);

                // Mettre à jour les métriques rapides
                updateQuickMetrics(cycleTime, validatorPerf);
                
                // Créer/mettre à jour les graphiques
                updateWorkflowTimeChart(workflowTime);
                updateVisaStatsChart(visaStats);
                updateDocTypesChart(docTypes);
                updateCreationTrendsChart(creationTrends);
                
                // Mettre à jour les tableaux
                updateValidatorTable(validatorPerf);
                updateProjectDmoTables(projectDmo);
                
                log('=== Toutes les statistiques chargées avec succès ===', 'success');
                showToast('Toutes les statistiques ont été chargées avec succès !', 'success');

            } catch (error) {
                log(`=== Erreur lors du chargement: ${error.message} ===`, 'error');
                showToast(`Erreur lors du chargement: ${error.message}`, 'error');
            }
        }

        // Fonction pour effacer toutes les données
        function clearAllData() {
            // Détruire tous les graphiques
            Object.values(charts).forEach(chart => chart.destroy());
            charts = {};
            
            // Effacer les tableaux
            document.querySelector('#validatorPerfTable tbody').innerHTML = '';
            document.querySelector('#projectsTable tbody').innerHTML = '';
            document.querySelector('#dmosTable tbody').innerHTML = '';
            
            // Réinitialiser les métriques
            document.getElementById('totalDocuments').textContent = '-';
            document.getElementById('avgCycleTime').textContent = '-';
            document.getElementById('totalValidators').textContent = '-';
            document.getElementById('pendingVisas').textContent = '-';
            
            // Réinitialiser les statuts
            showStatus('workflowTimeStatus', 'Données effacées');
            showStatus('visaStatsStatus', 'Données effacées');
            showStatus('docTypesStatus', 'Données effacées');
            showStatus('creationTrendsStatus', 'Données effacées');
            showStatus('validatorPerfStatus', 'Données effacées');
            showStatus('projectsStatus', 'Données effacées');
            showStatus('dmosStatus', 'Données effacées');
            
            log('Toutes les données ont été effacées');
        }

        // Fonction pour mettre à jour les métriques rapides avec animation
        function updateQuickMetrics(cycleTime, validatorPerf) {
            animateCounter('totalDocuments', cycleTime.completed_count || 0);
            animateCounter('avgCycleTime', cycleTime.avg_cycle_time || 0, 1);
            animateCounter('totalValidators', Object.keys(validatorPerf).length || 0);

            // Calculer le total des visas en attente
            let totalPending = 0;
            Object.values(validatorPerf).forEach(validator => {
                totalPending += validator.pending_visas || 0;
            });
            animateCounter('pendingVisas', totalPending);
        }

        // Fonction pour animer les compteurs
        function animateCounter(elementId, targetValue, decimals = 0) {
            const element = document.getElementById(elementId);
            const startValue = parseFloat(element.textContent.replace(/[^\d.-]/g, '')) || 0;
            const increment = (targetValue - startValue) / 30;
            let currentValue = startValue;

            const timer = setInterval(() => {
                currentValue += increment;
                if ((increment > 0 && currentValue >= targetValue) || (increment < 0 && currentValue <= targetValue)) {
                    currentValue = targetValue;
                    clearInterval(timer);
                }
                element.textContent = decimals > 0 ? currentValue.toFixed(decimals) : Math.floor(currentValue);
            }, 50);
        }

        // Fonction pour mettre à jour le graphique des temps de workflow
        function updateWorkflowTimeChart(data) {
            const ctx = document.getElementById('workflowTimeChart').getContext('2d');

            if (charts.workflowTime) {
                charts.workflowTime.destroy();
            }

            const labels = Object.keys(data);
            const values = Object.values(data);

            if (labels.length === 0) {
                showStatus('workflowTimeStatus', 'Aucune donnée disponible', 'error');
                return;
            }

            charts.workflowTime = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Temps moyen (jours)',
                        data: values,
                        backgroundColor: 'rgba(102, 126, 234, 0.6)',
                        borderColor: 'rgba(102, 126, 234, 1)',
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Jours'
                            }
                        }
                    }
                }
            });

            showStatus('workflowTimeStatus', `${labels.length} étapes analysées`, 'success');
        }

        // Fonction pour mettre à jour le graphique des statistiques de visas
        function updateVisaStatsChart(data) {
            const ctx = document.getElementById('visaStatsChart').getContext('2d');

            if (charts.visaStats) {
                charts.visaStats.destroy();
            }

            const labels = Object.keys(data);
            const approvalRates = labels.map(step => data[step].approval_rate || 0);
            const totalVisas = labels.map(step => data[step].total || 0);

            if (labels.length === 0) {
                showStatus('visaStatsStatus', 'Aucune donnée disponible', 'error');
                return;
            }

            charts.visaStats = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Taux d\'approbation (%)',
                        data: approvalRates,
                        borderColor: 'rgba(75, 192, 192, 1)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        yAxisID: 'y'
                    }, {
                        label: 'Total visas',
                        data: totalVisas,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Taux d\'approbation (%)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Nombre de visas'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });

            showStatus('visaStatsStatus', `${labels.length} étapes analysées`, 'success');
        }

        // Fonction pour mettre à jour le graphique des types de documents
        function updateDocTypesChart(data) {
            const ctx = document.getElementById('docTypesChart').getContext('2d');

            if (charts.docTypes) {
                charts.docTypes.destroy();
            }

            // Calculer les totaux par docType
            const docTypeTotals = {};
            Object.keys(data).forEach(docType => {
                let total = 0;
                Object.keys(data[docType]).forEach(procType => {
                    Object.values(data[docType][procType]).forEach(count => {
                        total += count;
                    });
                });
                docTypeTotals[docType] = total;
            });

            const labels = Object.keys(docTypeTotals);
            const values = Object.values(docTypeTotals);

            if (labels.length === 0) {
                showStatus('docTypesStatus', 'Aucune donnée disponible', 'error');
                return;
            }

            charts.docTypes = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: labels,
                    datasets: [{
                        data: values,
                        backgroundColor: [
                            'rgba(255, 99, 132, 0.6)',
                            'rgba(54, 162, 235, 0.6)',
                            'rgba(255, 205, 86, 0.6)',
                            'rgba(75, 192, 192, 0.6)',
                            'rgba(153, 102, 255, 0.6)',
                            'rgba(255, 159, 64, 0.6)'
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            showStatus('docTypesStatus', `${labels.length} types de documents`, 'success');
        }

        // Fonction pour mettre à jour le graphique des tendances de création
        function updateCreationTrendsChart(data) {
            const ctx = document.getElementById('creationTrendsChart').getContext('2d');

            if (charts.creationTrends) {
                charts.creationTrends.destroy();
            }

            if (data.length === 0) {
                showStatus('creationTrendsStatus', 'Aucune donnée disponible', 'error');
                return;
            }

            const labels = data.map(item => item.month);
            const counts = data.map(item => item.count);
            const cumulative = data.map(item => item.cumulative);

            charts.creationTrends = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [{
                        label: 'Documents créés',
                        data: counts,
                        borderColor: 'rgba(102, 126, 234, 1)',
                        backgroundColor: 'rgba(102, 126, 234, 0.2)',
                        fill: true,
                        yAxisID: 'y'
                    }, {
                        label: 'Cumulatif',
                        data: cumulative,
                        borderColor: 'rgba(255, 99, 132, 1)',
                        backgroundColor: 'rgba(255, 99, 132, 0.1)',
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Documents créés'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Cumulatif'
                            },
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });

            showStatus('creationTrendsStatus', `${data.length} mois analysés`, 'success');
        }

        // Fonction pour mettre à jour le tableau des validateurs
        function updateValidatorTable(data) {
            const tbody = document.querySelector('#validatorPerfTable tbody');
            tbody.innerHTML = '';

            if (Object.keys(data).length === 0) {
                showStatus('validatorPerfStatus', 'Aucune donnée disponible', 'error');
                return;
            }

            Object.values(data).forEach(validator => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${validator.name}</td>
                    <td>${validator.department || 'N/A'}</td>
                    <td>${validator.total_visas}</td>
                    <td>${validator.approved_visas}</td>
                    <td>${validator.pending_visas}</td>
                    <td>${validator.approval_rate}%</td>
                    <td>${validator.avg_response_time_hours ? validator.avg_response_time_hours.toFixed(1) : 'N/A'}</td>
                `;
            });

            showStatus('validatorPerfStatus', `${Object.keys(data).length} validateurs`, 'success');
        }

        // Fonction pour mettre à jour les tableaux projet/DMO
        function updateProjectDmoTables(data) {
            // Tableau des projets
            const projectsTbody = document.querySelector('#projectsTable tbody');
            projectsTbody.innerHTML = '';

            if (data.projects && data.projects.length > 0) {
                data.projects.slice(0, 10).forEach(project => {
                    const row = projectsTbody.insertRow();
                    row.innerHTML = `
                        <td>${project.name || 'N/A'}</td>
                        <td>${project.description || 'N/A'}</td>
                        <td><span class="badge bg-primary">${project.document_count}</span></td>
                    `;
                });
                showStatus('projectsStatus', `${data.projects.length} projets`, 'success');
            } else {
                showStatus('projectsStatus', 'Aucun projet trouvé', 'error');
            }

            // Tableau des DMOs
            const dmosTbody = document.querySelector('#dmosTable tbody');
            dmosTbody.innerHTML = '';

            if (data.dmos && data.dmos.length > 0) {
                data.dmos.slice(0, 10).forEach(dmo => {
                    const row = dmosTbody.insertRow();
                    row.innerHTML = `
                        <td>${dmo.name || 'N/A'}</td>
                        <td>${dmo.description || 'N/A'}</td>
                        <td><span class="badge bg-info">${dmo.document_count}</span></td>
                    `;
                });
                showStatus('dmosStatus', `${data.dmos.length} DMOs`, 'success');
            } else {
                showStatus('dmosStatus', 'Aucun DMO trouvé', 'error');
            }
        }
    </script>
{% endblock %}
