{% extends 'base.html.twig' %}

{% block title %}Gérer mes places{% endblock %}

{% block body %}
<div class="container py-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white py-3">
                    <h4 class="card-title mb-0 fw-bold">Gérer mes places</h4>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Sélectionnez les places que vous gérez dans le workflow. Vous recevrez des notifications pour les documents dans ces places et ils apparaîtront dans votre tableau de bord.
                    </p>

                    {{ form_start(form) }}
                    
                    <div class="mb-4">
                        {{ form_label(form.managedPlaces, null, {'label_attr': {'class': 'fw-bold mb-2'}}) }}
                        <div class="row">
                            {% for child in form.managedPlaces %}
                                <div class="col-md-4 mb-2">
                                    <div class="form-check">
                                        {{ form_widget(child, {'attr': {'class': 'form-check-input'}}) }}
                                        {{ form_label(child, null, {'label_attr': {'class': 'form-check-label'}}) }}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                        <small class="form-text text-muted">{{ form_help(form.managedPlaces) }}</small>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i> Retour au tableau de bord
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i> Enregistrer
                        </button>
                    </div>
                    
                    {{ form_end(form) }}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
