<?php

namespace App\Tests\Unit;

use App\Entity\Document;
use PHPUnit\Framework\TestCase;

class DocumentValidationTest extends TestCase
{
    public function testValidateCreationActionWithMissingFields(): void
    {
        $document = new Document();
        $document->setAction('creation');
        
        // Test avec tous les champs manquants
        $document->setCls(null);
        $document->setMoq(null);
        $document->setProductCode('');
        $document->setEccn('');
        
        $validationErrors = $this->validateCreationFields($document);
        
        $this->assertCount(4, $validationErrors);
        $this->assertContains('CLS', $validationErrors);
        $this->assertContains('MOQ', $validationErrors);
        $this->assertContains('Code Produit', $validationErrors);
        $this->assertContains('ECCN', $validationErrors);
    }

    public function testValidateCreationActionWithZeroValues(): void
    {
        $document = new Document();
        $document->setAction('creation');
        
        // Test avec des valeurs zéro
        $document->setCls(0);
        $document->setMoq(0);
        $document->setProductCode('TBD');
        $document->setEccn('tbd');
        
        $validationErrors = $this->validateCreationFields($document);
        
        $this->assertCount(4, $validationErrors);
        $this->assertContains('CLS', $validationErrors);
        $this->assertContains('MOQ', $validationErrors);
        $this->assertContains('Code Produit', $validationErrors);
        $this->assertContains('ECCN', $validationErrors);
    }

    public function testValidateCreationActionWithValidFields(): void
    {
        $document = new Document();
        $document->setAction('creation');
        
        // Test avec tous les champs valides
        $document->setCls(100);
        $document->setMoq(50);
        $document->setProductCode('PROD-001');
        $document->setEccn('EAR99');
        
        $validationErrors = $this->validateCreationFields($document);
        
        $this->assertEmpty($validationErrors);
    }

    public function testValidateNonCreationActionSkipsValidation(): void
    {
        $document = new Document();
        $document->setAction('modification');
        
        // Même avec des champs manquants, la validation ne devrait pas s'appliquer
        $document->setCls(null);
        $document->setMoq(null);
        $document->setProductCode('');
        $document->setEccn('');
        
        $validationErrors = $this->validateCreationFields($document);
        
        $this->assertEmpty($validationErrors);
    }

    public function testValidateCreationActionWithPartiallyValidFields(): void
    {
        $document = new Document();
        $document->setAction('creation');
        
        // Test avec certains champs valides et d'autres non
        $document->setCls(100);
        $document->setMoq(null);
        $document->setProductCode('PROD-001');
        $document->setEccn('');
        
        $validationErrors = $this->validateCreationFields($document);
        
        $this->assertCount(2, $validationErrors);
        $this->assertContains('MOQ', $validationErrors);
        $this->assertContains('ECCN', $validationErrors);
        $this->assertNotContains('CLS', $validationErrors);
        $this->assertNotContains('Code Produit', $validationErrors);
    }

    /**
     * Reproduit la logique de validation du contrôleur
     */
    private function validateCreationFields(Document $document): array
    {
        // Ne valider que si l'action est "creation"
        if ($document->getAction() !== 'creation') {
            return [];
        }

        $validationErrors = [];
        
        // Vérifier CLS
        if ($document->getCls() === null || $document->getCls() === 0) {
            $validationErrors[] = 'CLS';
        }
        
        // Vérifier MOQ
        if ($document->getMoq() === null || $document->getMoq() === 0) {
            $validationErrors[] = 'MOQ';
        }
        
        // Vérifier Code Produit
        if (empty($document->getProductCode()) || trim($document->getProductCode()) === '' || strtoupper(trim($document->getProductCode())) === 'TBD') {
            $validationErrors[] = 'Code Produit';
        }
        
        // Vérifier ECCN
        if (empty($document->getEccn()) || trim($document->getEccn()) === '' || strtoupper(trim($document->getEccn())) === 'TBD') {
            $validationErrors[] = 'ECCN';
        }
        
        return $validationErrors;
    }
}
