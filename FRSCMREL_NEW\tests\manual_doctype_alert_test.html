<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test des alertes de changement de doctype</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container mt-5">
        <h1>Test des alertes de changement de doctype</h1>
        <p>Ce test simule les alertes qui apparaissent lors du changement de type de document.</p>
        
        <div class="row">
            <div class="col-md-6">
                <h3>Test des alertes de confirmation</h3>
                <p>Ces alertes apparaissent AVANT le changement :</p>
                <button class="btn btn-primary mb-2" onclick="testConfirmAlert('ASSY')">Test ASSY</button><br>
                <button class="btn btn-primary mb-2" onclick="testConfirmAlert('MACH')">Test MACH</button><br>
                <button class="btn btn-primary mb-2" onclick="testConfirmAlert('MOLD')">Test MOLD</button><br>
                <button class="btn btn-primary mb-2" onclick="testConfirmAlert('PUR')">Test PUR</button><br>
                <button class="btn btn-primary mb-2" onclick="testConfirmAlert('DOC')">Test DOC</button><br>
            </div>
            
            <div class="col-md-6">
                <h3>Test des alertes de succès</h3>
                <p>Ces alertes apparaissent APRÈS le changement avec les vraies destinations :</p>
                <button class="btn btn-success mb-2" onclick="testSuccessAlert(['Assembly', 'Quality'])">ASSY → Assembly + Quality</button><br>
                <button class="btn btn-success mb-2" onclick="testSuccessAlert(['Machining'])">MACH → Machining</button><br>
                <button class="btn btn-success mb-2" onclick="testSuccessAlert(['Molding'])">MOLD → Molding</button><br>
                <button class="btn btn-success mb-2" onclick="testSuccessAlert(['Quality'])">PUR → Quality</button><br>
                <button class="btn btn-success mb-2" onclick="testSuccessAlert(['Achat_Rfq'])">PUR → RFQ</button><br>
                <button class="btn btn-success mb-2" onclick="testSuccessAlert(['Achat_F30'])">PUR → F30</button><br>
            </div>
        </div>
    </div>

    <script>
        // Copie des mappings du fichier jhess.html.twig
        let docTypeDisplayName = {
            'ASSY': 'Assemblage',
            'MACH': 'Usinage',
            'MOLD': 'Moulage',
            'DOC':  'Qualité',
            'PUR':  'Achat'
        };

        let placeDisplayNames = {
            'Quality': 'Qualité',
            'Assembly': 'Assemblage',
            'Machining': 'Usinage',
            'Molding': 'Moulage',
            'Project': 'Projet',
            'Metro': 'Métro',
            'QProd': 'QProd',
            'Planning': 'Logistique',
            'Indus': 'Gamme Ass.',
            'Core_Data': 'Core Data',
            'Prod_Data': 'Prod Data',
            'methode_Labo': 'Laboratoire',
            'Qual_Logistique': 'Gestion des en-cours',
            'Logistique': 'Gestion des en-cours',
            'Achat_F30': 'F30',
            'Achat_FIA': 'FIA',
            'Achat_Rfq': 'RFQ',
            'Achat_RoHs_REACH': 'RoHS / REACH',
            'Achat_Hts': 'HTS',
            'Methode_assemblage': 'Méthode Assemblage',
            'Tirage_Plans': 'Tirage Plans',
            'GID': 'GID',
            'Costing': 'Costing',
            'Produit': 'Produit'
        };

        // Configuration Toast (copié de jhess.html.twig)
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
        });

        function testConfirmAlert(doctype) {
            // Simulation de l'alerte de confirmation
            Swal.fire({
                title: 'Êtes-vous sûr ?',
                text: "Vous êtes sur le point de changer le type de document vers " + (docTypeDisplayName[doctype] || doctype) + ". Le document sera déplacé vers les étapes appropriées.",
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Oui, changer !',
                cancelButtonText: 'Annuler'
            }).then((result) => {
                if (result.isConfirmed) {
                    Toast.fire({
                        icon: 'info',
                        title: 'Changement confirmé',
                        text: 'En réalité, ceci déclencherait la requête AJAX vers le serveur.'
                    });
                }
            });
        }

        function testSuccessAlert(currentPlaces) {
            // Simulation de l'alerte de succès avec les vraies destinations
            const displayPlaces = currentPlaces.map(place =>
                placeDisplayNames[place] || place
            );
            const placesText = displayPlaces.join(', ');
            
            Toast.fire({
                icon: 'success',
                title: 'Doctype modifié avec succès',
                text: 'Le document va maintenant vers : ' + placesText
            });
        }
    </script>
</body>
</html>
