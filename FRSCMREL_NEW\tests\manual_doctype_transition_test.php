<?php

/**
 * Script de test manuel pour vérifier les transitions de doctype
 * Ce script peut être exécuté pour tester la logique de nettoyage des données
 */

require_once __DIR__ . '/../vendor/autoload.php';

use App\Entity\Document;

// Créer un document de test
$document = new Document();

echo "=== Test des transitions de doctype avec places d'origine ===\n\n";

// Test 1: MACH -> PUR depuis Machining
echo "Test 1: Transition MACH -> PUR depuis Machining\n";
echo "Avant nettoyage:\n";

$document->setMatProdType('HALB');
$document->setUnit('PC');
$document->setLeadtime(30);
$document->setProcType('E');
$document->setPrisDans1('Value1');
$document->setPrisDans2('Value2');
$document->setMof('MOF123');
$document->setCurrentSteps(['Machining' => 1]);

echo "- Place actuelle: Machining\n";
echo "- matProdType: " . ($document->getMatProdType() ?? 'null') . "\n";
echo "- unit: " . ($document->getUnit() ?? 'null') . "\n";
echo "- leadtime: " . ($document->getLeadtime() ?? 'null') . "\n";
echo "- procType: " . ($document->getProcType() ?? 'null') . "\n";
echo "- prisDans1: " . ($document->getPrisDans1() ?? 'null') . "\n";
echo "- prisDans2: " . ($document->getPrisDans2() ?? 'null') . "\n";
echo "- mof: " . ($document->getMof() ?? 'null') . "\n";

// Simuler le nettoyage complet pour MACH -> PUR depuis Machining
$document->setMatProdType(null);
$document->setUnit(null);
$document->setLeadtime(null);
$document->setProcType(null);
$document->setPrisDans1(null);
$document->setPrisDans2(null);
$document->setMof(null);

echo "\nAprès nettoyage (MACH -> PUR depuis Machining):\n";
echo "- matProdType: " . ($document->getMatProdType() ?? 'null') . "\n";
echo "- unit: " . ($document->getUnit() ?? 'null') . "\n";
echo "- leadtime: " . ($document->getLeadtime() ?? 'null') . "\n";
echo "- procType: " . ($document->getProcType() ?? 'null') . "\n";
echo "- prisDans1: " . ($document->getPrisDans1() ?? 'null') . "\n";
echo "- prisDans2: " . ($document->getPrisDans2() ?? 'null') . "\n";
echo "- mof: " . ($document->getMof() ?? 'null') . "\n";

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 2: ASSY -> PUR depuis Assembly
echo "Test 2: Transition ASSY -> PUR depuis Assembly\n";
echo "Avant nettoyage:\n";

$document->setMatProdType('HALB');
$document->setUnit('PC');
$document->setLeadtime(30);
$document->setProcType('E');
$document->setPrisDans1('Value1');
$document->setPrisDans2('Value2');
$document->setMof('MOF123');
$document->setCurrentSteps(['Assembly' => 1]);

echo "- Place actuelle: Assembly\n";
echo "- matProdType: " . ($document->getMatProdType() ?? 'null') . "\n";
echo "- unit: " . ($document->getUnit() ?? 'null') . "\n";
echo "- leadtime: " . ($document->getLeadtime() ?? 'null') . "\n";
echo "- procType: " . ($document->getProcType() ?? 'null') . "\n";
echo "- prisDans1: " . ($document->getPrisDans1() ?? 'null') . "\n";
echo "- prisDans2: " . ($document->getPrisDans2() ?? 'null') . "\n";
echo "- mof: " . ($document->getMof() ?? 'null') . "\n";

// Simuler le nettoyage pour ASSY -> PUR depuis Assembly (sans MOF, sans pris dans)
$document->setMatProdType(null);
$document->setUnit(null);
$document->setLeadtime(null);
$document->setProcType(null);
// MOF et pris dans restent inchangés pour ASSY

echo "\nAprès nettoyage (ASSY -> PUR depuis Assembly):\n";
echo "- matProdType: " . ($document->getMatProdType() ?? 'null') . "\n";
echo "- unit: " . ($document->getUnit() ?? 'null') . "\n";
echo "- leadtime: " . ($document->getLeadtime() ?? 'null') . "\n";
echo "- procType: " . ($document->getProcType() ?? 'null') . "\n";
echo "- prisDans1: " . ($document->getPrisDans1() ?? 'null') . " (doit rester Value1)\n";
echo "- prisDans2: " . ($document->getPrisDans2() ?? 'null') . " (doit rester Value2)\n";
echo "- mof: " . ($document->getMof() ?? 'null') . " (doit rester MOF123)\n";

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 3: PUR -> MACH depuis Achat_Rfq
echo "Test 3: Transition PUR -> MACH depuis Achat_Rfq\n";
echo "Avant nettoyage:\n";

$document->setMatProdType('ROH');
$document->setUnit('KG');
$document->setCommodityCode('COMM123');
$document->setPurchasingGroup('BUYER1');
$document->setProcType('F');
$document->setCurrentSteps(['Achat_Rfq' => 1]);

echo "- Place actuelle: Achat_Rfq\n";
echo "- matProdType: " . ($document->getMatProdType() ?? 'null') . "\n";
echo "- unit: " . ($document->getUnit() ?? 'null') . "\n";
echo "- commodityCode: " . ($document->getCommodityCode() ?? 'null') . "\n";
echo "- purchasingGroup: " . ($document->getPurchasingGroup() ?? 'null') . "\n";
echo "- procType: " . ($document->getProcType() ?? 'null') . "\n";

// Simuler le nettoyage pour PUR -> MACH depuis Achat_Rfq
$document->setMatProdType(null);
$document->setUnit(null);
$document->setCommodityCode(null);
$document->setPurchasingGroup(null);
$document->setProcType(null);

echo "\nAprès nettoyage (PUR -> MACH depuis Achat_Rfq):\n";
echo "- matProdType: " . ($document->getMatProdType() ?? 'null') . "\n";
echo "- unit: " . ($document->getUnit() ?? 'null') . "\n";
echo "- commodityCode: " . ($document->getCommodityCode() ?? 'null') . "\n";
echo "- purchasingGroup: " . ($document->getPurchasingGroup() ?? 'null') . "\n";
echo "- procType: " . ($document->getProcType() ?? 'null') . "\n";

echo "\n" . str_repeat("-", 50) . "\n\n";

// Test 4: PUR -> ASSY depuis Achat_F30
echo "Test 4: Transition PUR -> ASSY depuis Achat_F30\n";
echo "Avant nettoyage:\n";

$document->setMatProdType('ROH');
$document->setUnit('KG');
$document->setCommodityCode('COMM456');
$document->setPurchasingGroup('BUYER2');
$document->setProcType('F');
$document->setCurrentSteps(['Achat_F30' => 1]);

echo "- Place actuelle: Achat_F30\n";
echo "- matProdType: " . ($document->getMatProdType() ?? 'null') . "\n";
echo "- unit: " . ($document->getUnit() ?? 'null') . "\n";
echo "- commodityCode: " . ($document->getCommodityCode() ?? 'null') . "\n";
echo "- purchasingGroup: " . ($document->getPurchasingGroup() ?? 'null') . "\n";
echo "- procType: " . ($document->getProcType() ?? 'null') . "\n";

// Simuler le nettoyage pour PUR -> ASSY depuis Achat_F30
$document->setMatProdType(null);
$document->setUnit(null);
$document->setCommodityCode(null);
$document->setPurchasingGroup(null);
$document->setProcType(null);

echo "\nAprès nettoyage (PUR -> ASSY depuis Achat_F30):\n";
echo "- matProdType: " . ($document->getMatProdType() ?? 'null') . "\n";
echo "- unit: " . ($document->getUnit() ?? 'null') . "\n";
echo "- commodityCode: " . ($document->getCommodityCode() ?? 'null') . "\n";
echo "- purchasingGroup: " . ($document->getPurchasingGroup() ?? 'null') . "\n";
echo "- procType: " . ($document->getProcType() ?? 'null') . "\n";

echo "\n=== Tests terminés avec succès ===\n";
