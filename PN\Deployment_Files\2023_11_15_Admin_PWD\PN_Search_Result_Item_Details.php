<html>

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta name="viewport" content="width=device-width, initial-scale=1">
	<meta charset="utf-8" />

	<meta http-equiv='cache-control' content='no-cache'>
	<meta http-equiv='expires' content='0'>
	<meta http-equiv='pragma' content='no-cache'>

	<link rel="stylesheet" type="text/css" href="PN_Styles.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

	<script>
		function open_drawing() {
			var frame_preview = document.getElementById("pdf_preview_frame_id");
			var check_exist = frame_preview.src.indexOf('NO_PREVIEW.pdf');
			if (check_exist == -1) {
				var draw_name = document.getElementById("drawing_id_").value;
				var full_link = "\\REL\\DRAWINGS\\OFFICIAL\\" + draw_name;
				window.open(full_link);
			}
		}

		function download_drawing(data, type = 'text/plain') {
			var frame_preview = document.getElementById("pdf_preview_frame_id");
			var check_exist = frame_preview.src.indexOf('NO_PREVIEW.pdf');
			if (check_exist == -1) {
				var name = document.getElementById("drawing_id_").value;
				if (name != "" && name != "-") {
					var full_link = "\\REL\\DRAWINGS\\OFFICIAL\\" + name;
					const anchor = document.createElement('a');
					anchor.href = full_link;
					anchor.download = name;
					anchor.click();
				}
			}
		}

		function print_drawing() {
			var frame_preview = document.getElementById("pdf_preview_frame_id");
			var check_exist = frame_preview.src.indexOf('NO_PREVIEW.pdf');
			if (check_exist == -1) {
				frame_preview.contentWindow.focus();
				frame_preview.contentWindow.print();
			}
		}


		function on_diff_alert(id_val) {
			// verifie si un plan est en cours de diffusion et alerte sur son caractere non officiel
			let tr_val_diff = document.getElementById("gid_date__" + id_val).innerHTML;
			var diff_check = tr_val_diff.indexOf("diffusion");
			if (diff_check > -1) {
				confirm("\u26a0 Cette revision est en cours de diffusion et n'est pas encore officielle. \n \u26a0 Merci de ne pas l'imprimer et de ne pas le communiquer.");
			}
		}

		// Bouton Modifier
		// Pour afficher tout les champs modifiables
		function Modif_Form() {
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() {
				var mdp = prompt("Password", "");
				if (mdp != "") {
					//var mdp=document.getElementById("pass_input").value;
					pwd = this.responseText.trim();
					if (mdp == pwd) {

						// MASQUAGE CHAMP/BOUTON ADMIN
						//document.getElementById("pass_input").value="";
						//document.getElementById("pass_zone").hidden=true;
						//////document.getElementById("admin_pass_button").hidden=false;
						// ---------------------------
						
						// Reference
						ref = document.getElementById("ref");
						ref.setAttribute("class", "Input_Btn");
						ref.disabled = false;

						// Ref_Rev
						ref_rev = document.getElementById("ref_rev");
						ref_rev.setAttribute("class", "Input_Btn");
						ref_rev.disabled = false;

						// On affiche le mot "rev"
						var rev_cust = document.getElementById('rev_cust');
						rev_cust.hidden = false

						// title_EN
						title_en = document.getElementById("title_en");
						if (title_en) {
							title_en.setAttribute("class", "Input_Btn");
							title_en.disabled = false;
						} else {
							div_title_en = document.getElementById('div_titles');
							div_title_en.innerHTML = '<th colspan=2 style="text-align:right;padding-right:7px"><img src="/Common_Resources/UK_Flag.png" style="height:8px;"></th><input maxlength="40" style="width:260px;" type="text" id="title_en" name="title_EN" class="Input_Btn" value="">';
							div_title_en.hidden = false;
						}

						// title_FR
						title_fr = document.getElementById("title_fr");
						if (title_fr) {
							title_fr.setAttribute("class", "Input_Btn");
							title_fr.disabled = false;
						} else {
							div_tilte_fr = document.getElementById('div_titles');
							div_tilte_fr.innerHTML = '<th colspan=2 style="text-align:right;padding-right:7px"><img src="/Common_Resources/FRA_Flag.png" style="height:8px;"></th><input maxlength="40" style="width:260px;" type="text" id="title_fr" name="title_FR" class="Input_Btn" value="">';
							div_tilte_fr.hidden = false;
						}

						// Prod_Draw
						Prod_Draw = document.getElementById('Prod_Draw');
						Prod_Draw.setAttribute("class", "Input_Btn");
						Prod_Draw.disabled = false;

						// Prod_Draw_Rev
						Prod_Draw_Rev = document.getElementById('Prod_Draw_Rev');
						Prod_Draw_Rev.setAttribute("class", "Input_Btn");
						Prod_Draw_Rev.disabled = false;

						// Alias
						Alias = document.getElementById('Alias');
						Alias.setAttribute("class", "Input_Btn");
						Alias.disabled = false;

						// Cust_Draw
						cust_draw = document.getElementById('plan_client');
						cust_draw.setAttribute('class', 'Non_Visible');

						cust_draw_hidden = document.getElementById('plan_client_');
						cust_draw_hidden.removeAttribute('class');

						// Cust_Draw_Rev
						cust_draw_rev = document.getElementById('cust_draw_rev');
						cust_draw_rev.setAttribute("class", "Input_Btn");
						cust_draw_rev.disabled = false;

						// Doc_Type et Division
						doc_hidden = document.getElementById('doc_div');
						doc_hidden.setAttribute('class', 'Non_Visible');

						doc_div_hidden = document.getElementById('doc_div_hidden');
						doc_div_hidden.removeAttribute('class');

						// Product_Code
						product_hidden = document.getElementById('product');
						product_hidden.setAttribute('class', 'Non_Visible');

						product_code_hidden = document.getElementById('product_hidden');
						product_code_hidden.removeAttribute('class');

						//Certification (Ex)
						certif_hidden = document.getElementById('certif_');
						certif_hidden.setAttribute('class', 'Non_Visible');

						certif_ex_hidden = document.getElementById('certif_hidden');
						certif_ex_hidden.removeAttribute('class');

						// Dates SAP et Costing
						dates = document.getElementById('dates');
						dates.setAttribute('class', 'Non_Visible');

						dates_hidden = document.getElementById('dates_hidden');
						dates_hidden.removeAttribute('class');

						// Rel_Pack_Num
						Rel_Pack_Num = document.getElementById('Pack_Num');
						Rel_Pack_Num.setAttribute('class', 'Non_Visible');

						Rel_Pack_Num_hidden = document.getElementById('Num_Diff');
						Rel_Pack_Num_hidden.removeAttribute('class');

						// DMO
						DMO = document.getElementById('DMO');
						DMO.setAttribute('class', 'Non_Visible');

						DMO_hidden = document.getElementById('DMO_');
						DMO_hidden.removeAttribute('class');

						// Bouton Supprimer
						supp = document.getElementById('supprimer_btn');
						supp.style.display = "block";

						// Bouton Update
						update = document.getElementById('update_btn');
						update.style.display = "block";

						// Bouton Modifier
						modif = document.getElementById('button_modifier');
						modif.style.display = "none";

						// étoile reference
						star1 = document.getElementById('star1');
						star1.removeAttribute('class');

						// étoile Titre(s)
						star2 = document.getElementById('star2');
						star2.removeAttribute('class');

						// étoile Plan
						star3 = document.getElementById('star3');
						star3.removeAttribute('class');

						// étoile Division / Type Document
						star4 = document.getElementById('star4');
						star4.removeAttribute('class');

						// étoile Code Produit
						star5 = document.getElementById('star5');
						star5.removeAttribute('class');

						// étoile Certification
						star6 = document.getElementById('star6');
						star6.removeAttribute('class');

						// Affichage des 2 champs pouur les plans

						// Drawing_Path						
						Prod_draw_path = document.getElementById('Prod_Draw_file_hidden');
						Prod_draw_path.removeAttribute('class');

						// Cust_Drawing_Path
						cust_draw_hidden = document.getElementById('cust_draw_hidden');
						cust_draw_hidden.removeAttribute('class');

					} else if (mdp != "" && mdp != null) {
						window.alert("Incorrect Password!");
						document.getElementById("pass_input").focus();
					} else {}
				}
			}
			xhttp.open("GET", "PN_Admin_PWD.php");
			xhttp.send();
		}
		
		function pass_display()
		{
			if (document.getElementById("pass_input")!="")
			{
				document.getElementById("pass_zone").hidden=false;
				document.getElementById("admin_pass_button").hidden=true;
				document.getElementById("pass_input").focus();
			}
		}

		function alive_check(check_type) {
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() {
				const raw_result = this.responseText.trim();
				const result = raw_result.split(":");
				var msg = "";
				if (check_type == 1) {
					if (result[0] != "0") {
						const resultat_pn = result[0].split("__");
						msg = "La reference" + resultat_pn[0] + " rev" + resultat_pn[1] + " existe deja dans la base. Vous ne pouvez pas l'ajouter.";
					}
					if (result[1] != "0") {
						const resultat_release = result[1].split("__");
						if (msg != "") {
							msg = msg + "\n";
							msg = msg + "Cette reference" + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Vous ne pouvez pas l'ajouter.";
						} else {
							msg = "Cette reference" + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Vous ne pouvez pas l'ajouter.";
						}
					}
				}
				if (check_type == 2) {
					if (result[0] != "0") {
						const resultat_pn = result[0].split("__");
						msg = "Le Prod_Draw " + resultat_pn[0] + " rev" + resultat_pn[1] + " existe deja dans la base. Vous ne pouvez pas l'ajouter.";
					}
					if (result[1] != "0") {
						const resultat_release = result[1].split("__");
						if (msg != "") {
							msg = msg + "\n";
							msg = msg + "Ce Prod_Draw " + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Vous ne pouvez pas l'ajouter.";
						} else {
							msg = "Ce Prod_Draw " + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Vous ne pouvez pas l'ajouter.";
						}
					}
				}
				if (msg != "") {
					if (check_type == 2) {
						alert(msg);
					} else {
						alert(msg);
						document.getElementById("update_btn").disabled = true;
						document.getElementById('ref').style.backgroundColor = "#F5B7B1";
						document.getElementById('ref_rev').style.backgroundColor = "#F5B7B1";
					}
				} else {
					document.getElementById("update_btn").disabled = false;
					document.getElementById('ref').style.backgroundColor = "white";
					document.getElementById('ref_rev').style.backgroundColor = "white";
				}
				return false;
			}

			if (check_type == 1) {
				if (document.getElementById("ref").value != "" && document.getElementById("ref").value != "ZPF000000000XXXXXX") {
					const ref_1 = document.getElementById("ref").value + "__" + document.getElementById("ref_rev").value;
					var id = document.getElementById("ID").innerHTML;
					id = id.trim();
					// alert('PN_REF_Alive_Check_AUTO.php?type=Reference__Ref_Rev&to_be_checked=' + ref_1 + '&ID=' + id);
					xhttp.open("GET", "PN_REF_Alive_Check_AUTO.php?type=Reference__Ref_Rev&to_be_checked=" + ref_1 + "&ID=" + id);
					xhttp.send();
				}
			}

			if (check_type == 2) {
				if (document.getElementById("Prod_Draw").value != "") {
					const ref_2 = document.getElementById("Prod_Draw").value + "__" + document.getElementById("Prod_Draw_Rev").value;
					var id = document.getElementById("ID").innerHTML;
					id = id.trim();
					xhttp.open("GET", "PN_REF_Alive_Check_AUTO.php?type=Prod_Draw__Prod_Draw_Rev&to_be_checked=" + ref_2 + "&ID=" + id);
					xhttp.send();
				}
			}
		}

		// On verifie si le nom du plan importé à le même nom que le Drawing_Path !
		function prod_draw_update() {
			let file_name = document.getElementById("prod_drawing_file").files[0]['name'];
			var draw_name = document.getElementById("drawing_id_").value;

			if (draw_name == "") {
				draw_name = document.getElementById("prod_drawing_file").files[0]['name'];
			}
			if (file_name != draw_name) {
				document.getElementById("prod_drawing_file").value = "";
				alert("Le fichier doit avoir le même nom ! " + draw_name);
			}
		}

		// Bouton Update
		function Update_Form() {
			var title_en = document.getElementById("title_en").value;
			var title_fr = document.getElementById("title_fr").value;
			var ref = document.getElementById('ref').value;
			var ref_rev = document.getElementById('ref_rev').value;
			var prod_draw = document.getElementById('Prod_Draw').value;
			var prod_draw_rev = document.getElementById('Prod_Draw_Rev').value;
			var doc_type = document.getElementById('Doc_Type').value;
			var certif = document.getElementById('Ex').value;
			var product_code = document.getElementById('Product_Code').value;
			var Division = document.getElementById('Division').value;

			if (ref == "" || ref_rev == "" || prod_draw == "" || prod_draw_rev == "" || doc_type == "" || certif == "" || product_code == "" || Division == "") {
				alert("Tous les champs signalés par une étoile (*) doivent être renseignés");
				return false;
			}

			if (title_en == "" && title_fr == "") {
				alert("Veuillez remplir l'un des deux champs titre");
				return false;
			}

			var r = confirm("Voulez-vous vraiment modifier cette ligne ?");
			if (r == true) {
				return true;
				// window.parent.document.location.reload(true);
			} else {
				return false;
			}
		}

		// Bouton supprimer
		function Supp_Form() {
			var r = confirm("Êtes-vous sûr de vouloir supprimer cette ligne ?");
			if (r == true) {
				window.parent.document.location.reload(true);
			} else {
				return false;
			}
		}
	</script>
</head>


<!---------------------------->
<!-- MISE EN FORME DONNEES  -->
<!---------------------------->
<?php

if (isset($_GET['ID']) && $_GET['ID'] != "") {
	$id_pn = str_replace("*", "%", $_GET['ID']);
} else {
	$id_pn = "%";
}

if (isset($_GET['ref']) && $_GET['ref'] != "") {
	$ref = str_replace("*", "%", $_GET['ref']);
} else {
	$ref = "%";
}

if (isset($_GET['ref_rev']) && $_GET['ref_rev'] != "") {
	$ref_rev = str_replace("*", "%", $_GET['ref_rev']);
} else {
	$ref_rev = "%";
}

if (isset($_GET['prod_draw']) && $_GET['prod_draw'] != "") {
	$prod_draw = str_replace("*", "%", $_GET['prod_draw']);
} else {
	$prod_draw = "%";
}

if (isset($_GET['prod_draw_rev']) && $_GET['prod_draw_rev'] != "") {

	$prod_draw_rev = str_replace("*", "%", $_GET['prod_draw_rev']);
} else {
	$prod_draw_rev = "%";
}

// PROD DRAW PATH FOR PREVIEW
// --------------------------
$prod_draw_preview = "";

// DEFINITION DIMENSION COLONNE POUR AMENAGER FORMAT A4 PORTRAIT EN COLONNE 2
// ---------------------------------------------------------------------------
$w_preview = '(29/21) * (100vh - 8px) - 15px';

// VARIABLE POUR MEMOIRE DU NOM DU PROD DRAW ET DE SA REVISION POUR CONSTRUIRE L'HISTORIQUE DU PLAN
// ------------------------------------------------------------------------------------------------
$prod_draw_history = "";
$prod_draw_rev_history = "";

?>

<?php

// Au clique sur le bouton supprimer on suprime toute la ligne 
if (isset($_POST['update_btn'])) {

	#FOR DEBUGGING ONLY
	#print_r($_POST);

	$reference = $_POST['Reference']; //
	$ref_rev = $_POST['ref_rev']; //
	$prod_draw = $_POST['Prod_Draw']; //
	$prod_draw_rev = $_POST['Prod_Draw_Rev']; //

	$title_EN = htmlspecialchars($_POST['title_EN'], ENT_QUOTES);
	if ($title_EN == "") {
		$title_EN = "";
	} //

	$title_FR = htmlspecialchars($_POST['title_FR'], ENT_QUOTES);
	if ($title_FR == "") {
		$title_FR = "";
	} //

	$alias = $_POST['Alias']; //
	if ($alias == "") {
		$alias = "";
	}

	$doc_type = $_POST['Doc_Type']; //
	$ex = $_POST['Ex']; //

	$date_sap = $_POST['DATE_SAP']; //
	if ($date_sap == "0000-00-00" || $date_sap == "") {
		$date_sap = date("Y-m-d");
	}

	$date_costing = $_POST['DATE_Costing']; //
	if ($date_costing == "0000-00-00" || $date_costing == "") {
		$date_costing = date("Y-m-d");
	}

	$product_code = $_POST['Product_Code']; //

	$rel_pack_num = $_POST['Rel_Pack_Num']; //
	if ($rel_pack_num == "") {
		$rel_pack_num = "";
	}

	$DMO = $_POST['DMO_name'];
	if ($DMO == "") {
		$DMO = "";
	}

	$division = $_POST['Division']; //

	$cust_drawing = $_POST['cust_draw']; //
	if ($cust_drawing == "") {
		$cust_drawing = "";
	}

	$cust_drawing_rev = $_POST['cust_draw_rev']; //
	if ($cust_drawing_rev == "") {
		$cust_drawing_rev = "";
	}

	$cust_drawing_path = basename($_FILES['cust_drawing_file']['name'][0]);
	if ($cust_drawing_path != "") {
		$cust_drawing_file_name = $cust_drawing_path;
		$cust_draw = 'Cust_Drawing_Path="' . $cust_drawing_file_name . '",';
	} else {
		if ($cust_drawing_path == "" && $_POST['cust_drawing_path'] != "") {
			$cust_drawing_file_name = $_POST['cust_drawing_path'];
			$cust_draw = 'Cust_Drawing_Path="' . $cust_drawing_file_name . '",';
		} else {
			$cust_drawing_file_name = "";
			$cust_draw = '';
		}
	}

	$drawing_path = basename($_FILES['prod_drawing_file']['name'][0]);
	if ($drawing_path != "") {
		$drawing_file_name = $drawing_path;
		$draw = 'Drawing_Path="' . $drawing_file_name . '",';
	} else {
		if ($drawing_path == "" && $_POST['drawing_name_'] != "") {
			$drawing_file_name = $_POST['drawing_name_'];
			$draw = 'Drawing_Path="' . $drawing_file_name . '",';
		} else {
			$drawing_file_name = "";
			$draw = '';
		}
	}

	include('../PN_Connexion_PN.php');

	$sql_1 = 'UPDATE tbl_pn SET
						Reference="' . $reference . '",
						Ref_Rev="' . $ref_rev . '",
	                    Ref_Title_EN="' . $title_EN . '",
	                    Ref_Title_FRA="' . $title_FR . '",
						Prod_Draw="' . $prod_draw . '",
						Prod_Draw_Rev="' . $prod_draw_rev . '",
						' . $draw . '
	                    Alias="' . $alias . '",
						Cust_Drawing="' . $cust_drawing . '",
						Cust_Drawing_Rev="' . $cust_drawing_rev . '",
	                    ' . $cust_draw . '
	                    Product_Code="' . $product_code . '",
						Doc_Type="' . $doc_type . '",
						Certif="' . $ex . '",
	                    Division="' . $division . '",
	                    Rel_Pack_Num="' . $rel_pack_num . '",
						DATE_SAP="' . $date_sap . '",
						DATE_Costing="' . $date_costing . '"
				WHERE ID like "' . $_GET['ID'] . '";';

	// print_r($sql_1);

	$resultat = $mysqli_pn->query($sql_1);

	// on ferme la connexion
	mysqli_close($mysqli_pn);

	include('../REL_Connexion_DB.php');

	$sql_2 = 'UPDATE tbl_released_package SET
						DMO="' . $DMO . '"
				WHERE Rel_Pack_Num like "' . $rel_pack_num . '";';

	$result = $mysqli->query($sql_2);

	// on ferme la connexion
	mysqli_close($mysqli);
}

// Au clique sur le bouton supprimer on suprime toute la ligne 
if (isset($_POST['supp_btn'])) {

	$drawing_files = $_POST['drawing_name_'];

	include('../REL_Connexion_DB.php');

	$sql_file_rel = 'SELECT Drawing_Path FROM tbl_released_drawing WHERE Drawing_Path like "' . $drawing_files . '"';
	$resultat_rel = $mysqli->query($sql_file_rel);
	$rowcount_rel = mysqli_num_rows($resultat_rel);

	mysqli_close($mysqli);

	//Connexion à BD
	include('../PN_Connexion_PN.php');

	$sql_file_pn = 'SELECT Drawing_Path FROM tbl_pn WHERE Drawing_Path like "' . $drawing_files . '"';
	$resultat_pn = $mysqli_pn->query($sql_file_pn);
	$rowcount_pn = mysqli_num_rows($resultat_pn);

	mysqli_close($mysqli_pn);

	if ($rowcount_rel == "0" && $rowcount_pn == "1") {
		include('../PN_Connexion_PN.PHP');
		$requete_delete = "DELETE FROM tbl_pn WHERE ID like " . $_GET['ID'];
		$resultat = $mysqli_pn->query($requete_delete);
		mysqli_close($mysqli_pn);

		$path_attachment = "..\REL\DRAWINGS\OFFICIAL\\";
		$file_to_delete = $path_attachment . $drawing_files;            // Chemin complet du fichier existant
		unlink($file_to_delete);  // Suppression

	} else {
		include('../PN_Connexion_PN.php');
		$requete_delete = "DELETE FROM tbl_pn WHERE ID like " . $_GET['ID'];
		$resultat = $mysqli_pn->query($requete_delete);
		mysqli_close($mysqli_pn);
	}
}

?>

<body>
	<form method="post" enctype="multipart/form-data">
		<table id="t02" border=0>
			<tr style="height:26px">
				<td>
					<div id="detail_page_title">
						Information pour la reference <span style="font-size:10pt;color:#0075A7"><?php if ($ref != "%" && isset($ref)) {
																										echo $ref;
																									} else {
																										echo "";
																									} ?></span>
					</div>
				</td>
				<td style="text-align:right">


					<input onclick="download_drawing()" type="image" src="\Common_Resources\download_icon.png" class="" style="width:21px;margin-bottom:-4px;cursor:pointer;margin-left:5px" title="Télécharger le plan" />

					<input onclick="open_drawing()" type="image" src="\Common_Resources\agrandir_icon.png" class="" style="width:21px;margin-bottom:-4px;cursor:pointer;margin-left:5px;" title="Ouvrir le plan dans un nouvel onglet" />

					<a onclick="print_drawing()"><img src="\Common_Resources\print_icon.png" class="img_print" style="width:21px;margin-bottom:-4px;cursor:pointer;margin-left:5px;" title="Imprimer le plan en A4" /></a>

				</td>
			</tr>
			<tr>
				<td>
					<table id="t03" style="min-width:500px;" border=0>
						<?php
						include('../PN_Connexion_PN.PHP');
						$requete = 'SELECT * FROM  tbl_pn
									WHERE ID like "' . $id_pn . '"';
						$resultat = $mysqli_pn->query($requete);
						$rowcount = mysqli_num_rows($resultat);

						if ($rowcount == 1) {

							// RECHERCHE DE LA REFERENCE OU DU PLAN DANS L'OUTIL DE DIFFUSION POUR SIGNIFER SI UNE DIFF EST EN COURS
							while ($row = $resultat->fetch_assoc()) {

								// ID
								echo '
							<tr hidden>
								<th>ID dans table tbl_pn dans db_pn</th>
								<td id="ID">' . $row['ID'] . '</td>
								<td><input type="text" name="cust_drawing_path" id="cust_path" value="' . $row['Cust_Drawing_Path'] . '"/></td>
							</tr>';

								// Reference et Ref_Rev
								echo '<tr id="ref_">
								<th colspan=2>Reference <FONT class="Non_Visible" id="star1" color="#EE0000">*</FONT></th>
								<td colspan=4 style="min-width:125px;">';
								$full_article = "";
								$ref_rev =  "";
								if ($row['Reference'] != "") {
									$full_article = '<input required maxlength="18" disabled class="Input_Not_Click" style="" onchange="alive_check(1)" name="Reference" type="text" id="ref" value="' . $row['Reference'] . '"/>';
									$ref_rev = ' rev <input required style="width:35px;" disabled class="Input_Not_Click" maxlength="3" onchange="alive_check(1)" type="text" name="ref_rev" id="ref_rev" value="' . $row['Ref_Rev'] . '"/>';
								} else {
									$full_article = '<input required maxlength="18" disabled class="Input_Not_Click" style="" onchange="alive_check(1)" name="Reference" type="text" id="ref" value=""/>';
									$ref_rev = ' rev <input required style="width:35px;" disabled class="Input_Not_Click" maxlength="3" onchange="alive_check(1)" type="text" name="ref_rev" id="ref_rev" value=""/>';
								}
								echo $full_article . '' . $ref_rev . '</td>
							</tr>';

								$en_in = "";
								echo '<tr id="titles">';
								echo '<th style="width:160px">Titre(s) <FONT class="Non_Visible" id="star2" color="#EE0000">*</FONT></th>';
								if ($row['Ref_Title_EN'] != "") {
									echo '<th style="text-align:right;padding-right:7px"><img src="\Common_Resources\UK_Flag.png" style="height:8px;"></th>
										<td>';
									echo '<input style="width:260px;" maxlength="40" id="title_en" name="title_EN" disabled class="Input_Not_Click" type="text" value="' . $row['Ref_Title_EN'] . '">';
									echo '</td>';
									echo '</tr>';
									$en_in = 'colspan=2';
								}
								if ($row['Ref_Title_FRA'] != "") {
									echo '<th ' . $en_in . '  style="text-align:right;padding-right:7px"><img src="\Common_Resources\FRA_Flag.png" style="height:8px;"></th>
										<td>';
									echo '<input style="width:260px;" maxlength="40" id="title_fr" name="title_FR" disabled class="Input_Not_Click" type="text" value="' . $row['Ref_Title_FRA'] . '">';
									echo '</td>';
									echo '</tr>';
								}

								echo '<tr hidden id="div_titles"></tr>';

								// Drawing_Path
								echo '<tr class="Non_Visible" id="Prod_Draw_file_hidden">
								<th style="width:160px" colspan=2>Prod. Drawing File</th>
								<td colspan=4 style="min-width:260px;">
									<div id="InpBox">
										<input id="prod_drawing_file" name="prod_drawing_file[]" type="file" accept=".pdf" onchange="prod_draw_update(this)">
									</div>
								</td>
								</tr>';

								// Prod_Draw et Prod_Draw_Rev
								echo '
								<tr id="Prod_">
									<th colspan=2>Plan <FONT class="Non_Visible" id="star3" color="#EE0000">*</FONT></th>
									<td colspan=4>';
								//if ($row['Prod_Draw'] != "") 
								//{
									echo '<input required disabled class="Input_Not_Click" id="Prod_Draw" name="Prod_Draw" onchange="alive_check(2)" type="text" value="' . $row['Prod_Draw'] . '"/> 
									rev <input required disabled class="Input_Not_Click" style="width:35px;" maxlength="3" id="Prod_Draw_Rev" name="Prod_Draw_Rev" onchange="alive_check(2)" type="text" value="' . $row['Prod_Draw_Rev'] . '">';
								//} else {
								//	echo '<input required disabled class="Input_Not_Click" id="Prod_Draw" name="Prod_Draw" onchange="alive_check(2)" type="text" value=""/> 
								//		rev <input required disabled class="Input_Not_Click" style="width:35px;" maxlength="3" id="Prod_Draw_Rev" name="Prod_Draw_Rev" onchange="alive_check(2)" type="text" value="">';
								//}
								echo '</td>
								</tr>';
								$prod_draw_history = $row['Prod_Draw'];
								$prod_draw_rev_history = $row['Prod_Draw_Rev'];


								// Alias
								echo '
							<tr id="Alias_">
								<th colspan=2>Référence Commerciale</th>
								<td colspan=4>';
								if ($row['Alias'] != "") {
									echo '<input id="Alias" style="width:260px;" maxlength="40" name="Alias" disabled class="Input_Not_Click" type="text" value="' . $row['Alias'] . '">';
								} else {
									echo '<input id="Alias" style="width:260px;" maxlength="40" name="Alias" disabled class="Input_Not_Click" type="text" value="">';
								}
								echo '
								</td>
							</tr>';

								// Cust_Drawing_Path
								echo '<tr class="Non_Visible" id="cust_draw_hidden">
								<th colspan=2>Cust Drawing File</th>
								<td colspan=4 style="min-width:260px;">
									<div id="InpBox">
										<input id="cust_drawing_file" name="cust_drawing_file[]" type="file" accept=".pdf"  onchange="cust_draw_update(this)">
									</div>
								</td>
							</tr>';

								// ---------------------------------------------------- hidden -----------------------------------------------------

								// Cust_Draw et Cust_Draw_Rev
								if ($row['Cust_Drawing_Rev'] != "") {
									$hidden_rev = "";
								} else {
									$hidden_rev = "hidden";
								}
								echo '
							<tr class="Non_Visible" id="plan_client_">
								<th colspan=2>Plan Client</th>';
								echo '<td colspan=4>';
								if ($row['Cust_Drawing'] != "") {
									echo '<input class="Input_Btn" name="cust_draw" id="cust_draw" type="text" value="' . $row['Cust_Drawing'] . '">';
									echo ' <span ' . $hidden_rev . ' id="rev_cust">rev</span> <input class="Input_Btn" style="width:35px;" maxlength="3" name="cust_draw_rev" id="cust_draw_rev" type="text" value="' . $row['Cust_Drawing_Rev'] . '">';
								} else {
									echo '<input class="Input_Btn" name="cust_draw" id="cust_draw" type="text" value="">';
									echo ' <span ' . $hidden_rev . ' id="rev_cust">rev</span> <input class="Input_Btn" style="width:35px;" maxlength="3" name="cust_draw_rev" id="cust_draw_rev" type="text" value="">';
								}
								echo '
								</td>
							</tr>';

								// ---------------------------------------------------- fin hidden -----------------------------------------------------

								// CUSTOMER DRAWING / PLAN CLIENT
								echo '
							<tr class="" id="plan_client">
								<th colspan=2>Plan Client</th>';
								echo '<td>';
								if ($row['Cust_Drawing'] != "") {
									if ($row['Cust_Drawing_Path'] != "") {
										echo '<a href="\\REL\\DRAWINGS\\OFFICIAL\\' . $row['Cust_Drawing_Path'] . '" target="_blank">';
									}
									echo $row['Cust_Drawing'];
									if ($row['Cust_Drawing_Rev'] != "") {
										echo ' rev' . $row['Cust_Drawing_Rev'];
									}
									if ($row['Cust_Drawing_Path'] != "") {
										echo '</a>';
									}
								} else {
									echo '';
								}
								echo '
								</td>
							</tr>';

								$doc_type_val = "";
								$icon_val = "";
								$title_val = "";
								switch ($row['Doc_Type']) {
									case "MACH";
									case "MOLD";
									case "ASSY";
									case "ARTICLE";
									case "PUR";
										$doc_type_val = "Article";
										$icon_val = '\Common_Resources\article_icon.png';
										$title_val = 'Article - ' . $row['Doc_Type'];
										break;
									case "DOC";
										$doc_type_val = "Document";
										$icon_val = '\Common_Resources\book_icon.png';
										$title_val = 'Document - ' . $row['Doc_Type'];
										break;
								}

								echo '<tr class="" id="doc_div">
								<th colspan=2>Division / Type Document</th>
								<td colspan=4>'
									. $row['Division'] . ' / ' . $doc_type_val . ' - <img src="' . $icon_val . '" title="' . $title_val . '" height="12px" style="vertical-align:bottom;padding-bottom:1px"></img>
								</td>
							</tr>';

								echo '							
							<tr class="" id="product">
								<th colspan=2>Code Produit</th>
								<td colspan=4>
									' . $row['Product_Code'];

								include('../SCM_Connexion_DB.php');
								$sql_pb = 'SELECT * FROM tbl_product_code where Code like "' . $row['Product_Code'] . '"';
								$resultat_pc = $mysqli_scm->query($sql_pb);
								$rowcount_pc = mysqli_num_rows($resultat_pc);
								if ($rowcount_pc == 1) {
									while ($row_pc = $resultat_pc->fetch_assoc()) {
										echo ' - ' . $row_pc['Description'];
									}
								} else {
									echo "&nbsp-";
								}
								$mysqli_scm->close();
								echo '
								</td>
							</tr>
	
							<tr class="" id="certif_">
								<th colspan=2>Certification</th>
								<td colspan=4 id="certif">';
								if ($row['Certif'] == "") {
									echo "&nbsp-";
								} else {
									echo $row['Certif'];
								}
								echo '
								</td>
							</tr>';

								// --------------------------------------Partie hidden-------------------------------------------- //

								echo '<tr class="Non_Visible" id="doc_div_hidden">
									<th colspan=2>Division / Type Document <FONT class="Non_Visible" id="star4" color="#EE0000">*</FONT></th>
							<td colspan=4>
								<div id="InpBox">
									<select required class="Input_Btn" id="Division" name="Division" type="submit" title="" style="">
										<option value=""></option>';
								// <!--LISTE DEROULANTE DYNAMIQUE-->
								// <!------------------------------>
								include('../REL_Connexion_DB.php');
								$requete_activity = "SELECT DISTINCT Activity FROM tbl_activity";
								$resultat_activity = $mysqli->query($requete_activity);
								$inc_activity = 0;
								while ($row_activity = $resultat_activity->fetch_assoc()) {
									$sel = "";
									if ($row['Division'] == $row_activity['Activity']) {
										$sel = "selected";
										$inc_activity = 1;
									}
									echo '<option ' . $sel . ' value ="' . $row_activity['Activity'] . '">' . $row_activity['Activity'] . '</option><br/>';
								}
								if ($inc_activity == 0 && $row['Division'] != "") {
									echo '<option selected value ="' . $row['Division'] . '">' . $row['Division'] . '</option><br/>';
								}
								echo '</select>
								/
									<select required class="Input_Btn" name="Doc_Type" id="Doc_Type" type="submit" title="" >
										<option value=""></option>';
								// <!------------------------------>
								$requete_doc_type = "SELECT DISTINCT Doc_Type FROM tbl_doc_type ORDER BY Doc_Type DESC;";
								$resultat_doc_type = $mysqli->query($requete_doc_type);
								$inc_doc_type = 0;
								while ($row_doc_type = $resultat_doc_type->fetch_assoc()) {
									$sel = "";
									if ($row['Doc_Type'] == $row_doc_type['Doc_Type']) {
										$sel = "selected";
										$inc_doc_type = 1;
									}
									echo '<option ' . $sel . ' value ="' . $row_doc_type['Doc_Type'] . '">' . $row_doc_type['Doc_Type'] . ' </option><br/>';
								}
								if ($inc_doc_type == 0 && $row['Doc_Type'] != "") {
									echo '<option selected value ="' . $row['Doc_Type'] . '">' . $row['Doc_Type'] . '</option><br/>';
								}

								mysqli_close($mysqli);
								// ---------------------------------
								echo '</select>
								</div>
							</td>
						</tr>

													
							<tr class="Non_Visible" id="product_hidden">
								<th colspan=2>Code Produit <FONT class="Non_Visible" id="star5" color="#EE0000">*</FONT></th>
								<td colspan=4 id="Product_Code_hidden">
								<select required class="Input_Btn" name="Product_Code" id="Product_Code" type="submit" title="">';
								echo '<option value=""></option>';

								include('../SCM_Connexion_DB.php');

								$requete_pc = "SELECT Code FROM tbl_product_code";
								$resultat_pc = $mysqli_scm->query($requete_pc);
								$in_pc = 0;
								while ($row_pc = $resultat_pc->fetch_assoc()) {
									$sel = "";
									if ($row['Product_Code'] == $row_pc['Code']) {
										$sel = "SELECTED";
										$in_pc = 1;
									}
									echo '<option ' . $sel . ' value="' . $row_pc['Code'] . '">' . $row_pc['Code'] . '</option><br/>';
								}
								if ($in_pc == 0 && $row['Product_Code'] != "") {
									echo '<option SELECTED value="' . $row['Product_Code'] . '">' . $row['Product_Code'] . '</option><br/>';
								}
								echo '</select>
								</td>
							</tr>

							<tr class="Non_Visible" id="certif_hidden">
								<th colspan=2>Certification <FONT class="Non_Visible" id="star6" color="#EE0000">*</FONT></th>
								<td colspan=4 id="certif_ex_hidden">';
								echo '<select required class="Input_Btn" name="Ex" id="Ex" type="submit" title="">';
								echo '<option value=""></option>';

								$requete_ex = "SELECT Ex FROM tbl_ex";
								$resultat_ex = $mysqli_scm->query($requete_ex);
								$in_ex = 0;
								while ($row_ex = $resultat_ex->fetch_assoc()) {
									$sel = "";
									if ($row['Certif'] == $row_ex['Ex']) {
										$sel = "SELECTED";
										$in_ex = 1;
									}
									echo '<option ' . $sel . ' value="' . $row_ex['Ex'] . '">' . $row_ex['Ex'] . '</option><br/>';
								}
								if ($in_ex == 0 && $row['Certif'] != "") {
									echo '<option SELECTED value="' . $row['Certif'] . '">' . $row['Certif'] . '</option><br/>';
								}
								mysqli_close($mysqli_scm);
								echo '</select>
								</td>
							</tr>

							<tr class="Non_Visible" id="dates_hidden" title="Date à laquelle la référence à sa nouvelle révision est créée dans SAP.">
								<th colspan=2 title="" >Date Création / Données SAP</th>
								<td colspan=4  id="dates_SAP_Costing">';
								echo '<input type="date" style="width:85px;" class="Input_Btn" id="DATE_SAP" name="DATE_SAP" value="' . $row['DATE_SAP'] . '">';
								echo '&nbsp/&nbsp <input type="date" style="width:85px;" class="Input_Btn" id="DATE_Costing" name="DATE_Costing" value="' . $row['DATE_Costing'] . '">';
								echo '
								</td>
							</tr>';

								// -------------------------------------------Fin partie hidden-------------------------------------------------------------------- //

								echo '<tr class="" id="dates" title="Date à laquelle la référence à sa nouvelle révision est créée dans SAP.">
								<th colspan=2 title="" >Date Création / Données SAP</th>
								<td colspan=4>';

								if ($row['DATE_SAP'] == "0000-00-00" || $row['DATE_SAP'] == "") {
									echo "&nbsp-";
								} else {
									echo $row['DATE_SAP'];
								}

								if ($row['DATE_Costing'] == "0000-00-00" || $row['DATE_Costing'] == "") {
									echo "&nbsp/&nbsp-";
								} else {
									echo '&nbsp/&nbsp' . $row['DATE_Costing'];
								}
								echo '
								</td>
							</tr>';

								// ---------------------------------------------- Hidden ---------------------------------------------------------------------------- //

								echo '<tr class="Non_Visible" id="Num_Diff">
								<th colspan=2>Num. Diffusion</th>
								<td colspan=4>
									<div id="InpBox">
											<input class="Input_Btn" list="Rel_Pack_Num" name="Rel_Pack_Num" id="Rel_Pack_Num_" title="" value="' . $row['Rel_Pack_Num'] . '">
										<datalist id="Rel_Pack_Num">';

								include('../REL_Connexion_DB.php');

								$requete_rel_pack_num = "SELECT DISTINCT Rel_Pack_Num FROM tbl_released_package";
								$resultat_rel_pack_num = $mysqli->query($requete_rel_pack_num);

								while ($row_rel_pack_num = $resultat_rel_pack_num->fetch_assoc()) {
									echo '<option value ="' . $row_rel_pack_num['Rel_Pack_Num'] . '">' . $row_rel_pack_num['Rel_Pack_Num'] . '</option><br/>';
								}
								mysqli_close($mysqli);

								echo '</datalist>
									</div>
								</td>
							</tr>

							<tr class="Non_Visible" id="DMO_" title="Demande de modification">
							<th colspan=2>DMO</th>
								<td colspan=4>
								<div id="InpBox">';

								include('../REL_Connexion_DB.php');

								if ($row['Rel_Pack_Num'] != "") {
									$requete_dmo = 'SELECT DMO FROM tbl_released_package WHERE Rel_Pack_Num like "' . $row['Rel_Pack_Num'] . '"';
									$res_dmo = $mysqli->query($requete_dmo);
									$rowcount_dmo = mysqli_num_rows($res_dmo);

									if ($rowcount_dmo > 0) {
										$row_dmo = $res_dmo->fetch_assoc();
										$dmo = $row_dmo['DMO'];
									} else {
										$dmo = "";
									}

									echo '<input class="Input_Btn" list="DMO_name" name="DMO_name" id="DMO_id" value="' . $dmo . '" />';
								} else {
									echo '<input class="Input_Btn" list="DMO_name" name="DMO_name" id="DMO_id" value=""/>';
								}
								echo '<datalist id="DMO_name">';

								$requete_dmo_ = 'SELECT DISTINCT DMO FROM tbl_released_package';
								$res_dmo_ = $mysqli->query($requete_dmo_);

								while ($row_dmo_ = $res_dmo_->fetch_assoc()) {
									echo '<option value ="' . $row_dmo_['DMO'] . '">' . $row_dmo_['DMO'] . '</option><br/>';
								}

								$mysqli->close();
								echo '</datalist>
									</div>
									</td>
							</tr>';

								// ---------------------------------------- fin hidden ----------------------------------------- //

								echo '<tr class="" id="Pack_Num">
								<th colspan=2>Num. Diffusion</th>
								<td>';
								if ($row['Rel_Pack_Num'] != "") {
									echo '<a class="verrou" href="\REL\REL_Pack_Overview.php?ID=' . $row['Rel_Pack_Num'] . '" target="_blank">' . $row['Rel_Pack_Num'] . '</a>';
								} else {
									echo "";
								}
								echo '
								</td>
							</tr>
							<tr class="" id="DMO" title="Demande de modification">
								<th colspan=2>DMO</th>
								<td>';
								include('../REL_Connexion_DB.php');
								$requete_dmo = 'SELECT DMO from tbl_released_package WHERE Rel_Pack_Num like "' . $row['Rel_Pack_Num'] . '"';
								$res_dmo = $mysqli->query($requete_dmo);
								$rowcount_dmo = mysqli_num_rows($res_dmo);
								$dmo_val = '';
								while ($row_dmo = $res_dmo->fetch_assoc()) {
									if ($row_dmo['DMO'] != "") {
										$dmo_val = '<a href="\DMO\DMO_Modification_form.php?ID=' . $row_dmo['DMO'] . '" target="_blank">' . $row_dmo['DMO'] . '</a>';
									}
								}

								$mysqli->close();
								echo $dmo_val;
								echo '	</td>
							</tr>

							<tr id="Drawing_Path">
								<th colspan=2>Nom Fichier</th>
								<td colspan=4>
								<input style="width:100%;" readonly type="text" class="Input_Not_Click" id="drawing_id_" name="drawing_name_" value="' . $row['Drawing_Path'] . '"/>
								</td>
							</tr>

							<tr id="diff_">
								<th colspan=2 style="vertical-align:top;padding-top:3px">Diffusion en cours</th>
								<td colspan=4 style="vertical-align:top;padding-top:3px">';

								$prod_draw_preview = $row['Drawing_Path'];

								// DETERMINATION DE DIFF DU PLAN OU DE LARTICLE EN COURS
								// -----------------------------------------------------
								include('../REL_Connexion_DB.php');

								$requete_draw_check = 'SELECT DISTINCT Prod_Draw, Prod_Draw_Rev, Creation_VISA, VISA_GID, tbl_released_drawing.Rel_Pack_Num
													 FROM tbl_released_drawing
													 LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
													 WHERE 
														 Prod_Draw like "' . $row['Prod_Draw'] . '"
													  AND (
															(
															ASCII(Prod_Draw_Rev) like ASCII("' . $row['Prod_Draw_Rev'] . '") AND BIT_LENGTH(Prod_Draw_Rev) > BIT_LENGTH("' . $row['Prod_Draw_Rev'] . '")
															) OR (
															ASCII(Prod_Draw_Rev) != ASCII("' . $row['Prod_Draw_Rev'] . '")
															)
														  )
													  AND Creation_VISA not like ""
													  AND VISA_GID like ""
													  ';

								$requete_ref_check = 'SELECT DISTINCT Reference, Ref_Rev, tbl_released_drawing.Rel_Pack_Num, Creation_VISA, VISA_GID, Inventory_Impact
													FROM tbl_released_drawing
													LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
													WHERE 
														 Reference like "' . $row['Reference'] . '"
													 AND (
															(
															ASCII(Ref_Rev) like ASCII("' . $row['Ref_Rev'] . '") AND BIT_LENGTH(Ref_Rev) > BIT_LENGTH("' . $row['Ref_Rev'] . '")
															) OR (
															ASCII(Ref_Rev) != ASCII("' . $row['Ref_Rev'] . '")
															)
														  )
													 AND Creation_VISA not like ""
													 AND VISA_GID like ""
													 
													 ';

								$res_draw_check = $mysqli->query($requete_draw_check);
								$rowcount_draw_check = mysqli_num_rows($res_draw_check);

								$res_ref_check = $mysqli->query($requete_ref_check);
								$rowcount_ref_check = mysqli_num_rows($res_ref_check);

								$result_check_rel = "";
								$inven_impact = "";

								if ($rowcount_draw_check > 0) {
									while ($row_draw_check = $res_draw_check->fetch_assoc()) {
										if ($result_check_rel != "") {
											$result_check_rel = $result_check_rel  . ' / Plan rev' . $row_draw_check['Prod_Draw_Rev'] . ' - Diffusion <a href="\\REL\REL_Pack_Overview.php?ID=' . $row_draw_check['Rel_Pack_Num'] . '" target="_blank">' . $row_draw_check['Rel_Pack_Num'] . '</a>';
										} else {
											$result_check_rel = 'Plan rev' . $row_draw_check['Prod_Draw_Rev'] . ' - Diffusion <a href="\\REL\REL_Pack_Overview.php?ID=' . $row_draw_check['Rel_Pack_Num'] . '" target="_blank">' . $row_draw_check['Rel_Pack_Num'] . '</a>';
										}
									}
								}

								if ($rowcount_ref_check > 0) {
									while ($row_ref_check = $res_ref_check->fetch_assoc()) {
										if ($result_check_rel != "") {
											$result_check_rel = $result_check_rel  . "<br>";
										} else {
											$result_check_rel = "";
										}
										$result_check_rel = $result_check_rel . 'Article rev' . $row_ref_check['Ref_Rev'] . ' - Diffusion <a href="\\REL\REL_Pack_Overview.php?ID=' . $row_ref_check['Rel_Pack_Num'] . '" target="_blank">' . $row_ref_check['Rel_Pack_Num'] . '</a>';
										$inven_impact = " - ";
										switch ($row_ref_check['Inventory_Impact']) {
											case "NO IMPACT":
												$inven_impact = $inven_impact . "Gestion En cours : Pas d'impact";
												break;
											case "TO BE SCRAPPED":
												$inven_impact = $inven_impact . "Gestion En cours : A rebuter";
												break;
											case "TO BE UPDATED":
												$inven_impact = $inven_impact . "Gestion En cours : A modifier";
												break;
											default:
												$inven_impact = "";
										}
										$result_check_rel = $result_check_rel . $inven_impact;
									}
								}

								$mysqli->close();
								// -----------------------------------------------------
								echo $result_check_rel . '</td>
							</tr>';

								// ---------------------------------------------------------------------
								// include('PN_Hidden_Admin.php');
								?>
								
								<tr>
									<td>
										<a id="button_modifier" onclick="return Modif_Form()"><img style="width:13px;height:13px;cursor:pointer" src="\Common_Resources\modifier_admin_pn.png" /></a>
										
										<!--
										<span HIDDEN id="pass_zone" style="font-size:9pt;font-weight:bold;font-style:italic">Password:<br> 
											<input type="password" value="" style="width:100px;height:21px;font-size:9pt;" id="pass_input">
											<input type="button" class="btn grey" onclick="return Modif_Form()" style="font-size:7pt;width:35px;height:21px;vertical-align:top;text-align:center" value="GO"/>
										</span>
										
										<input type="image" onclick="pass_display()" src="\Common_Resources\icon_admin.png" id="admin_pass_button" style="width:20px;height:20px;cursor:pointer" title="Administrateur" label="Administrateur">
										-->
								
								
										<input type="submit" id="update_btn" onclick="return Update_Form()" style="display:none;border-radius:10px;font-family:arial black;font-size:10px;min-width:70px; height:20px;box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.4), 0 6px 20px 0 rgba(0, 0, 0, 0.19);text-align: center;cursor:pointer" class="btn orange" name="update_btn" value="Update" title="Modifier la ligne sélectionnée" />
									</td>
									<td colspan=2>
										<input type="submit" onclick="return Supp_Form()" id="supprimer_btn" style="margin-left:-82px;display:none;border-radius:10px;font-family:arial black;font-size:10px;min-width:70px; height:20px;box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.4), 0 6px 20px 0 rgba(0, 0, 0, 0.19);text-align: center;cursor:pointer" class="btn red" name="supp_btn" value="Supprimer" title="Supprimer la ligne sélectionnée" />
									</td>
								</tr>
							
							<?php 
							}
						}

						$mysqli_pn->close();

						?>
					</table>
				</td>
				<td rowspan=2 style="width:calc(<?php echo $w_preview; ?>)">
					<iframe name="pdf_preview_frame" id="pdf_preview_frame_id" class="pdf_preview_frame" frameborder="1" scrolling="yes" width="100%" style="zoom:1;" <?php
																																										$src_val = "\Common_Resources\NO_PREVIEW.png";
																																										if ($prod_draw_preview != "") {
																																											$path_file = '\\REL\\DRAWINGS\\OFFICIAL\\' . $prod_draw_preview;
																																											$path_file = "/REL/DRAWINGS/OFFICIAL/" . $prod_draw_preview;
																																											if (file_exists($_SERVER['DOCUMENT_ROOT'] . $path_file))
																																											// if (file_exists($path_file)!=0)
																																											{
																																												$src_val = 'src="' . $path_file . '#toolbar=0&navpanes=0&scrollbar=0"';
																																											} else {
																																												$src_val = "src=\Common_Resources\NO_PREVIEW.pdf#toolbar=0&navpanes=0&scrollbar=0&view=Fitw";
																																											}
																																										}
																																										echo $src_val;
																																										?>></iframe>
				</td>
			</tr>
			<tr style="height:220px">
				<!-- TABLEAU LISTANT LES REFERENCES ET DONNANT ACCES EN PREVIEW OU EN OUVERTURE SUR NOUVEL ONGLET -->
				<!-------------------------------------------------------------------------------------------------->
				<td style="vertical-align:top">
					<div id="detail_page_title">
						Historique du Plan
						<span style="font-size:10pt;color:#0075A7"><?php if ($prod_draw != "%" && isset($prod_draw)) {
																		echo $prod_draw;
																	} else {
																		echo "";
																	} ?>
					</div>
					<table id="t05" border=0>
						<th>Rev</th>
						<th>Creation</th>
						<th>Données Complètes SAP</th>
						<th>#Diff</th>
						<?php

						include('../REL_Connexion_DB.php');
						$requete_release = 'SELECT  
										tbl_released_drawing.ID as "ID",
										tbl_released_drawing.Rel_Pack_Num,
										Prod_Draw, 
										Prod_Draw_Rev, 
										DATE_Finance, 
										DATE_GID, 
										VISA_GID,
										Drawing_Path, 
										Doc_Type
							FROM tbl_released_drawing
							LEFT JOIN tbl_released_package ON tbl_released_package.Rel_Pack_Num = tbl_released_drawing.Rel_Pack_Num
							WHERE
								  Prod_Draw like "' . $prod_draw_history . '"	
							  AND Prod_Draw_Rev not like "' . $prod_draw_rev_history . '"
							  AND Creation_VISA not like ""
							GROUP BY Prod_Draw, Prod_Draw_Rev
							ORDER BY Prod_Draw_Rev DESC';

						$res_release = $mysqli->query($requete_release);
						$rowcount = mysqli_num_rows($res_release);
						if ($rowcount > 0) {
							while ($row_rel = $res_release->fetch_assoc()) {
								if ($row_rel['VISA_GID'] == "") {
									$style_diff_encours = 'style="color:red"';
									$file_path = '\\IN_PROCESS\\';
									$date_GID = 'en cours de diffusion';
									$date_complete = '-';
								} else {
									$style_diff_encours = '';
									$file_path = '\\OFFICIAL\\';
									$date_GID = $row_rel['DATE_GID'];
									if ($row_rel['Doc_Type'] != "DOC") {
										$date_complete = $row_rel['DATE_Finance'];
									} else {
										$date_complete = $row_rel['DATE_GID'];
									}
								}
								echo '<tr ' . $style_diff_encours . '>';
								echo '<td>';
								if ($row_rel['Drawing_Path'] != "") {
									echo '<a href="\\REL\\DRAWINGS' . $file_path . $row_rel['Drawing_Path'] . '" target="_blank" onclick="on_diff_alert(' . $row_rel['ID'] . ')">' . $row_rel['Prod_Draw_Rev'] . '</a>';
								} else {
									echo $row_rel['Prod_Draw_Rev'];
								}
								echo '</td>';
								echo '<td id="gid_date__' . $row_rel['ID'] . '">';
								echo 		$date_GID;
								echo '</td>';
								echo '<td>';
								echo  		$date_complete;
								echo '</td>';
								echo '<td>';
								echo '	<a href="\REL\REL_Pack_Overview.php?ID=' . $row_rel['Rel_Pack_Num'] . '" target="_blank">';
								echo 		$row_rel['Rel_Pack_Num'];
								echo '	</a>';
								echo '</td>';
								echo '</tr>';
							}
						} else {
							echo '<tr>';
							echo '<td colspan=4>';
							echo '<i>-- Aucun historique de donnée disponible--</i>';
							echo '</td>';
							echo '</tr>';
						}
						$mysqli->close();
						?>
					</table>

				</td>
			</tr>
		</table>
	</form>
</body>

</html>