INSERT INTO db_pn.tbl_pn VALUES("1051","ZPF000000000812008","H","RECEPTACLE MALE 1 TWINAX 105 OHM KEY.N","","213229","H","213229indH.pdf","CMC1677-0002-B0001","C-213229-RATP","H","C-213229-RATPindH.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1052","ZPF000000000812023","E","","EMBASE F 4PTS T.04 CLAV.N + CTS","213265","E","213265indE.pdf","CMC32EA04-04SN","C-213265-<PERSON>NC<PERSON>","E","C-213265-SNCFindE.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1053","ZPF000000000812041","B","MALE RECEPTACLE 6+2P S.27 KEY.N W/O CT","","213267","B","213267indB.pdf","CMC32EA27-08PN090","C-213267","B","C-213267indB.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1054","ZPF000000000812506","B","LID + CHAIN","","213283","B","213283indB.pdf","DMCS1590-1000-A","C-213283","B","C-213283indB.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1055","ZPF000000000817118","D","","EMBASE M 13PTS T.07 CLAV.N + OPTION","213348","D","213348indD.pdf","CMC32EA07-13PN086","","","","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1056","ZPF000000000817153","C","","EMBASE M 49PTS T.27 CLAV.4 SS CT","213358","C","213358indC.pdf","CMC32EA27-49P4090","C-213358","C","C-213358indC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1057","ZPF000000000817241","B","","FICHE F 6+2P T.27 CLAV.N SS CT + OPTION","213409","B","213409indB.pdf","CMC36M27-08SNK280","C-213409","B","C-213409indB.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1058","ZPF000000000817246","D","","FICHE F 49PTS T.27 CLAV.N SS CT + M","213410","D","213410indD.pdf","CMC36M27-49SNK090","C-213410","D","C-213410indD.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1059","ZPF000000000817307","C","","FICHE F 2PTS T.27 CLAV.N SS CT + PG","213439","C","213439indC.pdf","CMC36PG27-02SNL090","C-213439","C","C-213439indC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1060","ZPF000000000817309","C","MALE PLUG 6+2P S.27 KEY.N W/O CT + PG","","213443","C","213443indC.pdf","CMC36PG27-08PNL090","C-213443","C","C-213443indC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1061","ZPF000000000819054","D","MALE PLUG CALIBER 20 - 185 MM2","","213465","D","213465indD.pdf","EKO1832-0185-A0001","C-213465","D","C-213465indD.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1062","ZPF000000000819057","D","","FICHE M UNIP  20 240MM  + CT + PE","213468","D","213468indD.pdf","EKO1832-0240-A0002","C-213468","D","C-213468indD.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1063","ZPF000000000819067","C","PLUG MALE 1CT DIA20 95MM + CT + CG","","213470","C","213470indC.pdf","EKO1832-208942","C-213470","C","C-213470indC.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1064","ZPF000000000819093","E","","FICHE M UNIP  12 10MM  + CT + PE","213482","E","213482indE.pdf","EKO1856-0010-A0002","C-213482","E","C-213482indE.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1065","ZPF000000000837781","C","","EMBASE F 3X22MM  + CTS + PE","213493","C","213493indC.pdf","EKO1864-0001-B0001","C-213493","C","C-213493indC.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1066","ZPF000000000838550","C","","EMBASE M 4PTS 38MM  SERT.COD.2 + PE + TO","213495","A","213495indA.pdf","EKO1792-0001-C0011","C-213495","C","C-213495indC.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1067","ZPF000000000838552","C","","EMBASE F4PTS 38MM  SERT. COD.1 + PE + TO","213496","C","213496indC.pdf","EKO1792-0001-B0010","C-213496","C","C-213496indC.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1068","ZPF000000000845940","H","","EMBASE F 90  BL 3PTS T.50 + LUGS 70MM","213513","H","213513indH.pdf","EKO1791-0009-B0001","C-213513","H","C-213513indH.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1069","ZPF000000000845941","E","PLUG M SHIELD 3P 70MM  CRIMP. S.50 - EKO","","213528","E","213528indE.pdf","EKO1791-0004-B0017","C-213528","E","C-213528indE.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1070","ZPF000000000817328","B","MALE PLUG 5POLES S.07 KEY.N W/O CT + RD","","213532","B","213532indB.pdf","CMC36RD07-05PNL090","C-213532","B","C-213532indB.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1071","ZPF000000000817524","D","MALE RECEPTACLE 1POLE S.19 KEY.1 W/O CT","","213545","D","213545indD.pdf","CPP32EA19-01P1090","C-213545","D","C-213545indD.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1072","ZPF000000000817529","B","","EMBASE F UNIP T.19 CLAV.1 SS CT","213548","B","213548indB.pdf","CPP32EA19-01S1090","C-213548","B","C-213548indB.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1073","ZPF000000000817535","B","","EMBASE M 4+2P T.19 CLAV.N SS CT + TROP.","213552","B","213552indB.pdf","CPP32EA19-6BPNA91","C-213552","B","C-213552indB.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1074","ZPF000000000817396","B","DUMMY RECEPTACLE S.27","","213557","B","213557indB.pdf","CMCBR27","C-213557","B","C-213557indB.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1075","ZPF000000000821437","B","FEMALE RECEPTACLE 37POLES W/O CT","","213585","B","213585indB.pdf","CMC1590-0016-A0004","","C","C-213610indC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1076","ZPF000000000821830","C","PLUG CAP S.19","","213610","C","213610indC.pdf","CMCBF19","C-213610","C","C-213610indC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1077","ZPF000000000821868","J","","FICHE F 4+2P T.61 70 ET 10MM  + CTS + SC","213615","J","213615indJ.pdf","CMC1733-0015-A0001","C-213615-SNCF","J","C-213615-SNCFindJ.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1078","ZPF000000000821870","G","RECEPTACLE FEMALE 4XCAL12 50MM2","","213625","G","213625indG.pdf","CMC1733-0009-C0001","C-213625","G","C-213625indG.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1079","ZPF000000000817687","C","RECEPTACLE FEMALE 18P + RAL 7016, UIC558","","213662","C","213662indC.pdf","CSF1680-0001-A0007","C-213662-DB","C","C-213662-DBindC.pdf","UIC__558","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1080","ZPF000000000817694","F","DUMMY RECEPTACLE F 18P + SURTEC","","213676","F","213676indF.pdf","CSF1680-0003-A0001","C-213676","F","C-213676indF.pdf","UIC__558","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1081","ZPF000000000817720","C","RECEPTACLE FEMALE 18P SILVER, UIC","","213678","C","213678indC.pdf","CSF1680-4000-00000","C-213678","C","C-213678indC.pdf","UIC__558","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1082","ZPF000000000817721","C","RECEPTACLE FEMALE 18P + RAL 5002, UIC558","","213679","C","213679indC.pdf","CSF1680-4000-A0002","C-213679","C","C-213679indC.pdf","UIC__558","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1083","ZPF000000000821229","C","RECEPTACLE FEMALE 18P, UIC558","","213680","C","213680indC.pdf","CSF1680-0001-A0004","C-213680","F","C-213680-SNCF.pdf","UIC__558","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1084","ZPF000000000817566","D","MALE PLUG 1POLE S.19 KEY.1 W/O CT","","213690","D","213690indD.pdf","CPP36PG19-01P1K090","C-213690","D","C-213690indD.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1085","ZPF000000000817569","C","FEMALE PLUG 1POLE S.19 KEY.1 W/O CT","","213691","C","213691indC.pdf","CPP36PG19-01S1K090","C-213691","C","C-213691indC.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1086","ZPF000000000817575","C","MALE PLUG 4+2P S.19 KEY.N W/O CT","","213695","C","213695indC.pdf","CPP36PG19-6BPNL090","C-213695","C","C-213695indC.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1087","ZPF000000000817578","C","FEMALE PLUG 4+2P S.19 KEY.N W/O CT","","213697","C","213697indC.pdf","CPP36PG19-6BSNL090","C-213697","C","C-213697indC.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1088","ZPF000000000816398","A","","FICHE F 25  9PTS UIC PAS.34","213705","A","213705indA.pdf","CAM1676-0002-B0004","C-213705","A","C-213705indA.pdf","UIC__541","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1089","ZPF000000000817582","D","MALE PLUG 1POLE S.27 KEY.N W/O CT","","213707","D","213707indD.pdf","CPP36PG27-01PNL090","C-213707","D","C-213707indD.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1090","ZPF000000000816401","E","MALE RECEPTACLE 9POLES UIC + RAL 9005","","213709","E","213709indE.pdf","CAM1676-0003-B0002","C-213709-DB","D","C-213709-DBindD.pdf","UIC__541","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1091","ZPF000000000817584","C","FEMALE PLUG 1POLE S.27 KEY.N W/O CT","","213710","C","213710indC.pdf","CPP36PG27-01SNL090","C-213710","C","C-213710indC.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1092","ZPF000000000816419","B","","BOITE DE REPOS M + ISOLANT + CT 10MM","213729","B","213729indB.pdf","CAM1676-0024-A0001","C-213729-SNCF","B","C-213729-SNCFindB.pdf","UIC__541","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1093","ZPF000000000837036","G","RECEPTACLE MALE, 9P, PG29, UIC541.5","","213739","G","213739indG.pdf","CAM1676-1005-B0001","C-213739-SNCF","G","C-213739-SNCFindG.pdf","UIC__541","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1094","ZPF000000000820408","B","RECEPT M 4P S.61 KEY.N W/O CT + OPTION","","213743","B","213743indB.pdf","CPP32EA61-09PN063","C-213743","B","C-213743indB.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1095","ZPF000000000820410","D","PLUG F 4P S.61 KEY.N WO CT + OPTION -CPP","","213744","D","213744indD.pdf","CPP36M61-09SNM062","C-213744","D","C-213744indD.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1096","ZPF000000000821148","C","","EMBASE M 4+2P T.19 CLAV.3 SS CT + OPTION","213751","C","213751indC.pdf","CPP32RD19-6BP3023","C-213751-SNCF","C","C-213751-SNCFindC.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1097","ZPF000000000839155","D","","EMBASE M 10PTS 1.34 A 2.61MM  SERT. T.27","213778","D","213778indD.pdf","CPP32M27-10PNL070","C-213778-SNCF","D","C-213778-SNCFindD.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1098","ZPF000000000847055","B","PLUG CAP CMC SIZE 27","","213810","B","213810indB.pdf","CMCBF27","C-213810","B","C-213810indB.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1099","ZPF000000000817545","A","MALE RECEPTACLE 4+E+6P S.61 KEY.N W/O CT","","213856","A","213856indA.pdf","CPP32EA61-11PN090","C-213856","A","C-213856indA.pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1100","ZPF000000000822134","H","RECEPT F 4+2P S.19 KEY.N + CT + OPTION","","213863","H","213863indH.pdf","CPP32SC19-6BSNN","C-213877-SNCF","H","C-213877-SNCFindH,pdf","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1101","ZPF000000000901016","B","FEMALE PLUG, 2 CTS DIA  7.9, OUTLET M50","","213867","B","213867indB.pdf","CMC36M27-02SNL","C-213867","B","C-213867indB.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1102","ZPF000000000816480","B","","EMBASE M 2PTS 16MM  SERT.","213878","B","213878indB.pdf","CCA0590-0019-C0002","C-213878-SNCF","B","C-213878-SNCFindB.pdf","CCA","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1103","ZPF000000000817787","J","JUNCTION BOX + LOCK + LID, UIC","","213897","J","213897indJ.pdf","CSF2544-0112-A0002","C-213897","J","C-213897indJ.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1104","ZPF000000000817799","F","JUNCTION BOX LEFT + RAL 1016, UIC","","213899","F","213899indF.pdf","CSF2544-0142-B0001","C-213899","F","C-213899indF.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1105","ZPF000000000816357","B","","EMBASE F 4+2P 4X150MM  + 2X6MM","213901","B","213901indB.pdf","CAM1238-0250-A0001","C-213901-CAF","B","C-213901-CAFindB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1106","ZPF000000000906731","B","","SERRURE GAUCHE","213904","B","213904indB.pdf","DSFS207636","C-213904","B","C-213904indB.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1107","ZPF000000000816379","J","RECEPTACLE MALE 61P BORE 2,2 CRIMP - CAM","","213914","J","213914indJ.pdf","CAM1608-0127-B0006","C-213914-SNCF","J","C-213914-SNCFindJ.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1108","ZPF000000000817814","J","JUNCTION BOX + RAL 9005, UIC","","213915","J","213915indJ.pdf","CSF2544-4000-00000","C-213915","J","C-213915indJ.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1109","ZPF000000000812128","E","","EMBASE F UNIP 70-240MM","213920","E","213920indE.pdf","CSF1610-0002-00000","C-213920","E","C-213920indE.pdf","POWER_SPEC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1110","ZPF000000000816984","H","JUMPER 1 PLUG M LG 3520MM","","213923","H","213923indH.pdf","CMC1592-0023-00000","C-213923","H","C-213923indH.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1111","ZPF000000000811581","B","MALE RECEPTACLE 36+E BORE 2,2","","213927","B","213927indB.pdf","CAM1556-0004-0M000","C-213927","B","C-213927indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1112","ZPF000000000817755","M","DERIVATION BOX, UIC","","213935","M","213935indM.pdf","CSF2544-0039-00000","C-213935-SNCF","M","C-213935-SNCFindM.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1113","ZPF000000000817660","C","PLUG F UNIP 50MM2 TO BE CRIMP.","","213943","C","213943indC.pdf","CSF1165-0110-B0003","C-213943","C","C-213943indC.pdf","POWER_SPEC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1114","ZPF000000000867516","C","CPP MALE RECEPT 19-01 W/O CTS + BCK RD","","213946","C","213946indC.pdf","CPP32RD19-01PNLA91","","","","CPP","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1115","ZPF000000000864224","C","KIT SING CL4 FEMALE PLUG CMC 61x1,5SQMM","","213947","C","213947indC.pdf","KIT-SING-CK100473","","","","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1116","ZPF000000000867130","B","KIT SING 110V FEMALE PLUG CMC 2X50SQ","","213949","B","213949indB.pdf","KIT-SING-PLUG110V","","","","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1117","ZPF000000000901037","B","PLUG F 07-13 + W/O CTS + BCK M25, CMC","","213951","B","213951indB.pdf","CMC36M07-13SNGA91","","","","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1118","ZPF000000000817768","G","JUNCTION BOX W/O LOCK, UIC","","213954","G","213954indG.pdf","CSF2544-0068-B0004","C-213954-SNCF","G","C-213954-SNCFindG.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1119","ZPF000000000817776","F","JUNCTION BOX + RAL 7024 + LOCK, UIC","","213965","F","213965indF.pdf","CSF2544-0091-B0002","C-213965","F","C-213965indF.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1120","ZPF000000000901091","N","JUNCTION BOX","","213976","N","213976indN.pdf","CSF2544-0112-00000","C-213976-SNCF","N","C-213976-SNCFindN.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1121","ZPF000000000817798","E","JUNCTION BOX + RAL 1016, UIC","","213977","E","213977indE.pdf","CSF2544-0142-A0002","C-213977","E","C-213977indE.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1122","ZPF000000000817805","E","JUNCTION BOX, RAL 7021, UIC","","213978","E","213978indE.pdf","CSF2544-0143-C0001","C-213978","E","C-213978indE.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1123","ZPF000000000817807","H","JUNCTION BOX + SWITCH + LOCK","","213979","H","213979indH.pdf","CSF2544-0144-A0001","","","","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1124","ZPF000000000817809","E","DERIVATION BOX, UIC","","213980","E","213980indE.pdf","CSF2544-0145-B0001","C-213980","E","C-213980indE.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1125","ZPF000000000822234","D","MALE RECEPTACLE 37POLES + CTS","","213993","D","213993indD.pdf","CMC1590-0016-A0001","C-213993-SNCF","D","C-213993-SNCFindD.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1126","ZPF000000000812916","A","","TOURELLE POUR PINCE DMC M317","214014","A","214014indA.pdf","OUTS24478-0003","C-214014","A","C-214014indA.pdf","OUTIL","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1127","ZPF000000000812917","A","","TOURELLE POUR PINCE DMC M317","214015","A","214015indA.pdf","OUTS24823-0003","C-214015","A","C-214015indA.pdf","OUTIL","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1128","ZPF000000000817756","F","DERIVATION BOX, UIC","","214022","F","214022indF.pdf","CSF2544-0039-A0001","","","","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1129","ZPF000000000836724","A","LOWER LID","","214036","A","214036indA.pdf","DSFS31770-00000","C-214036","A","C-214036indA.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1130","ZPF000000000900307","C","PLUG FEMALE 07-07 4 CTS + BCK RD, CMC","","214040","C","214040indC.pdf","CMC36RD07-07SNL181","C-214040","C","C-214040indC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1131","ZPF000000000901260","B","CMC, PLUG MALE 27-49 W/O CT + PG21","","214050","B","214050indB.pdf","CMC36PG27-49PNH090","","","","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1132","ZPF000000000904516","B","PLUG FEMALE ANGLED, 95MM2, TO BE CRIMPED","","214078","B","214078indB.pdf","CSF1165-0110-B0005","C-214078","B","C-214078indB.pdf","POWER_SPEC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1133","ZPF000000000816977","H","JUMPER 1 PLUG M LG 3520MM","","214079","H","214079indH.pdf","CMC1590-0023-00000","C-214079","H","C-214079indH.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1134","ZPF000000000812346","B","CABLE GLAND DIA36 + TROP.","","214081","B","214081indB.pdf","DAMS0679-0017-36-T","C-214081","B","C-214081indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1135","ZPF000000000816346","D","","FICHE M 10  4+2P SS CT","214082","D","214082indD.pdf","CAM1238-0046-C0003","C-214082-CAF","D","C-214082-CAFindD.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1136","ZPF000000000814520","B","","FICHE M 10  4+2P ALES.18 SOUD. PAS.60","214085","B","214085indB.pdf","CAM1238-0046-BM004","C-214085","B","C-214085indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1137","ZPF000000000816238","D","PLUG FEMALE 10 4+2P 70MM2 & 6MM2 CRIMP","","214087","D","214087indD.pdf","CAM0640-0046-B0013","C-214087","D","C-214087indD.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1138","ZPF000000000816240","B","","FICHE F 10  3+T+2P 70 +95 +6  SERT.","214092","B","214092indB.pdf","CAM0640-0046-C0005","C-214092-SNCF","B","C-214092-SNCFindB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1139","ZPF000000000816245","B","","EMBASE F 4+2P 70MM  SERT.","214096","B","214096indB.pdf","CAM0640-0092-B0013","C-214096","B","C-214096indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1140","ZPF000000000811987","E","PLUG FEMALE 49P BORE 1,95 DIA27 - CLN","","214107","E","214107indE.pdf","CLN1675-0002-A0001","C-214107-RATP","E","C-214107-RATPindE.pdf","CLN","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1141","ZPF000000000816248","B","MALE RECEPTACLE 2+E+2P BORE 16,5 + TROP.","","214112","B","214112indB.pdf","CAM0640-0239-00000","C-214112","B","C-214112indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1142","ZPF000000000816298","D","DUMMY RECEPTACLE + RAL 7012","","214115","D","214115indD.pdf","CAM0708-0410-A0005","C-214115-SNCF","D","C-214115-SNCFindD.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1143","ZPF000000000816354","D","","EMBASE F 3+T+2P + CLIQUET + MINIRUPEUR","214120","D","214120indD.pdf","CAM1238-0220-A0002","C-214120","D","C-214120indD.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1144","ZPF000000000812005","F","MALE RECEPTACLE 43+2TWIN REP103","","214121","F","214121indF.pdf","CMC1647-0001-B0001","C-214121-SNCF","F","C-214121-SNCFindF.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1145","ZPF000000000816378","B","MALE PLUG 61POLES W/O CT","","214126","B","214126indB.pdf","CAM1608-0005-B0003","C-214126-SNCF","B","C-214126-SNCFindB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1146","ZPF000000000816355","E","PLUG MALE Angled 10","","214129","E","214129indE.pdf","CAM1238-0221-B0001","C-214129","E","C-214129indE.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1147","ZPF000000000816429","B","","FICHE M 25  9PTS PAS.45 + SURTEC","214134","B","214134indB.pdf","CAM1682-0001-A0001","C-214134","B","C-214134indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1148","ZPF000000000816453","D","","EMBASE M 1+3P CLAV.1 SS CT + SURTEC","214138","D","214138indD.pdf","CAM1859-0002-B0001","C-214138-BOM","D","C-214138-BOMindD.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1149","ZPF000000000846655","B","","FICHE FEMELLE","214151","B","214151indB.pdf","CAM1845-0078-B0001","C-214151","B","C-214151indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1150","ZPF000000000846656","C","RECEP M 25CT+14OBT+2QDX CLAV.N+ RED PAIN","","214152","C","214152indC.pdf","CAM1845-0109-B0001","C-214152","C","C-214152indC.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1151","ZPF000000000816431","E","FEMALE RECEPTACLE 2+4+8 CRIMP. KEY.3H","","214030","E","214030indE.pdf","CAM1750-0396-00000","","","","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1152","ZPF000000000811590","B","","FICHE F 25  4+T+6P 5X35MM  + 6X1,5MM","214080","B","214080indB.pdf","CAM1604-0391-B0001","C-214080-SNCF","B","C-214080-SNCFindB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1153","ZPF000000000816281","D","DUMMY RECEPTACLE + SURTEC","","214114","D","214114indD.pdf","CAM0706-0041-A0002","C-214114-CAF","D","C-214114-CAFindD.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1154","ZPF000000000816311","B","","EMBASE F 5+6P + CTS SERT. + TROP.","214118","B","214118indB.pdf","CAM0775-0109-B0008","C-214118","B","C-214118indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1155","ZPF000000000816314","B","DUMMY RECEPTACLE","","214119","B","214119indB.pdf","CAM0775-0202-A0005","C-214119","B","C-214119indB.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1156","ZPF000000000907450","C","JUMPER CM 9","","214289","C","214289indC.pdf","CMC1136-0069-A0003","C-214289","C","C-214289indC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1157","ZPF000000000817745","J","","BOITE DE DERIVATION 2X185MM  + 1X70MM","214291","H","214291indH.pdf","CSF1846-0001-00000","C-214291","H","C-214291indH.pdf","UIC__552","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1158","ZPF000000000835770","D","3 MODULES CMR without Contact, Size 600","","214292","D","214292indD.pdf","CMR1755-0004-00000","C-214292","D","C-214292indD.pdf","CMR","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1159","ZPF000000000817448","C","SOCKET FEMALE STRAIGHT","","214293","C","214293indC.pdf","CMR1746-0033-A0002","C-214293","C","C-214293indC.pdf","CMR","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1160","ZPF000000000900505","B","PLUG MALE SHIELDED 3POLES W/O CT 250HSS","","214295","B","214295indB.pdf","EKO1864-0006-B0002","C-214295","B","C-214295indB.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1161","ZPF000000000812012","D","","EMBASE M TWINAX 120OHM T.02 CLAV.3H","214297","D","214297indD.pdf","CMC1794-0007-B0003","","","","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1162","ZPF000000000833767","C","LID RECEPTACLE","","214298","C","214298indC.pdf","DAMS0708-0242-B","C-214298","C","C-214298indC.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1163","ZPF000000000834124","B","WATERPROOF BACKSHELL S.19 DIA 31 TO 33MM","","214303","B","214303indB.pdf","CMC A RD T  A19","C-214303","B","C-214303indB.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1164","ZPF000000000839351","A","PLUG BODY SURTEC KEY.1","","214304","A","214304indA.pdf","DAMS0708-0243-B-CL1","C-214304","A","C-214304indA.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1165","ZPF000000000819109","E","","EMBASE F BL 3X35MM  SS PE","214305","E","214305indE.pdf","EKO1864-0003-A0001","C-214305","E","C-214305indE.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1166","ZPF000000000818437","C","SE PLUG POUR DIA44 A 48","","214306","C","214306indC.pdf","DMCS1592-206663","C-214306-SNCF","C","C-214306-SNCFindC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1167","ZPF000000000821835","B","MALE PLUG SHIELDED 3POLES W/O CT","","214307","B","214307indB.pdf","EKO1864-0006-B0001","C-214307","B","C-214307indB.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1168","ZPF000000000816416","B","JUMPER OF MALE RECEPTACLE LG 4200MM","","214308","B","214308indB.pdf","CAM1676-0020-00000","","","","UIC__541__SOL.","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1169","ZPF000000000903219","B","PLUG FEMALE 3P S.60 W/O CT M40 BACKSHELL","","214309","B","214309indB.pdf","CMR1746-0024-A0001","C-214309","B","C-214309indB.pdf","CMR","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1170","ZPF000000000821829","B","FEMALE IN LINE CONNECTOR TYPE CW DIA74","","214311","B","214311indB.pdf","CPM0649-0059-A0004","C-214311","B","C-214311indB.pdf","CPM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1171","ZPF000000000817368","C","MALE PLUG 61POLES S.61 KEY.N W/O CT + RD","","214317","C","214317indC.pdf","CMC36RD61-61PNX090","C-214317","C","C-214317indC.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1172","ZPF000000000817293","E","","FICHE FEMELLE T.07 13P avec PE","214318","E","214318indE.pdf","CMC36PG07-13SNG091","","","","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1173","ZPF000000000816951","G","BASE ANGLE F 4CTS BORE 12,5 CRIMP","","214322","G","214322indG.pdf","CMC1136-0002-00000","C-214322-SNCF","E","C-214322-SNCF_IND_E.pdf","CMC","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1174","ZPF000000000816544","C","","FICHE F UNIP 25MM  PAS.30","214323","C","214323indC.pdf","CCA1831-0002-A0001","C-214323","C","C-214323indC.pdf","CCA","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1175","ZPF000000000846914","C","PLUG FEMALE ANG.10 3+E+2P 150/ 70/ 6SQMM","","214325","C","214325indC.pdf","CAM1238-0067-B0002","C-214325","C","C-214325indC.pdf","CAM","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1176","ZPF000000000816748","G","","EMBASE M 2PTS 25MM  SERT. PAS.12","214326","G","214326indG.pdf","CEV1658-0409-A0001","C-214326-SNCF","G","C-214326-SNCFindG.pdf","CEV","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1177","ZPF000000000823267","C","RECEPTACLE, FEMALE, SHIELDIED, W/O CTS","","214327","C","214327indC.pdf","EKO 3-36S CA","C-214327","C","C-214327indC.pdf","EKO","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1178","ZPF000000000817439","C","","FICHE M 2X95MM  + CTS + PE","214333","C","214333indC.pdf","CMR1746-0031-A0005","C-214333","C","C-214333indC.pdf","ONX","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1179","ZPF000000000817441","C","MALE PLUG 2XAWG4/0 + CTS + CG","","214334","C","214334indC.pdf","CMR1746-0031-B0003","C-214334","C","C-214334indC.pdf","ONX","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1180","ZPF000000000817443","E","","FICHE M 2X150MM  + CTS + PE","214335","E","214335indE.pdf","CMR1746-0032-A0002","C-214335","E","C-214335indE.pdf","ONX","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1181","ZPF000000000817444","E","","FICHE M 2X120MM  + CTS + PE","214336","E","214336indE.pdf","CMR1746-0032-A0003","C-214336","E","C-214336indE.pdf","ONX","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1182","ZPF000000000817429","E","","FICHE M 3X150MM  + CTS + PE + SC","214337","E","214337indE.pdf","CMR1743-0002-00000","C-214337","E","C-214337indE.pdf","CMR","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1183","ZPF000000000817430","G","","FICHE M 3X150MM  + CTS + PE + SC","214339","G","214339indG.pdf","CMR1743-0002-A0001","C-214339","G","C-214339indG.pdf","CMR","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1184","ZPF000000000820982","C","","CMR 2 MODULES WITH CONTACTS 240 MM2","214342","C","214342indC.pdf","CMR1746-0032-A0005","C-214342","C","C-214342indC.pdf","ONX","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1185","ZPF000000000817472","B","","FICHE M UNIP 240MM  + CT SS PE","214343","B","214343indB.pdf","CMR1746-0051-00000","C-214343","B","C-214343indB.pdf","CMR","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");
INSERT INTO db_pn.tbl_pn VALUES("1186","ZPF000000000817473","B","","FICHE F UNIP 240MM  + CT SS PE","214344","B","214344indB.pdf","CMR1746-0051-A0001","C-214344","B","C-214344indB.pdf","CMR","ASSY","NO","MOB_INDUS","","2024-07-25","2024-07-25");