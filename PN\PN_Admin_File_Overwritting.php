<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <link rel="stylesheet" type="text/css" href="PN_Admin_styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">


    <script>
        setTimeout(function() {
            var obj = document.getElementById("message");
            obj.innerHTML = "";
        }, 3000);

        function Ajouter_prod() {
            $prod_draw_file = document.getElementById("prod_drawing_file").value;

            if ($prod_draw_file == "") {
                alert("Veuillez sélectionner un fichier à télécharger");
                return false;
            }
        }

        function Ajouter_cust() {
            $cust_draw_file = document.getElementById("cust_drawing_file").value;

            if ($cust_draw_file == "") {
                alert("Veuillez sélectionner un fichier à télécharger");
                return false;
            }
        }

        function check_cust_draw_() {
            let cust_draw_name = document.getElementById("cust_drawing_file").files[0]['name'];
            const cust_dr = document.getElementById("cust_draw");

            cust_draw_name = cust_draw_name.trim();
            cust_draw_name = cust_draw_name.toUpperCase(cust_draw_name);
            cust_draw_name = cust_draw_name.replace(".PDF", "");
            //--------------------------------------

            // Determination de la racine du plan choisi
            //-------------------------------------------
            if (cust_draw_name.includes("REV")) {
                cust_draw_name = cust_draw_name.substr(0, cust_draw_name.search("REV"));
            }
            let cust_draw_root = cust_draw_name.trim();

            cust_dr.value = cust_draw_root;
        }

        function check_cust_draw_select() {
            let file_name = document.getElementById("listed_cust_file_name").value;
            const cust_dr = document.getElementById("cust_draw");

            file_name = file_name.trim();
            file_name = file_name.toUpperCase(file_name);
            file_name = file_name.replace(".PDF", "");
            //--------------------------------------

            // Determination de la racine du plan choisi
            //-------------------------------------------
            if (file_name.includes("REV")) {
                file_name = file_name.substr(0, file_name.search("REV"));
            }
            let cust_dr_root = file_name.trim();

            cust_dr.value = cust_dr_root;
        }

        function Btn_Supp(){
            var res = confirm("Êtes-vous sur de vouloir supprimer ce fichier ?");
            if(res == false){
                return false;
            }
        }
    </script>

    <?php
    $msg_prod = "";
    $msg_cust = "";
	$Official_Folder = "..\REL\DRAWINGS\OFFICIAL";

    if (isset($_POST["Valid_cust"])) {

        $cust_drawing = basename($_FILES['cust_drawing_file']['name'][0]);

        $cust_draw = $_POST["cust_draw"];

        include('../PN_Connexion_PN.php');

        $sql_file_pn = 'SELECT Cust_Drawing_Path FROM tbl_pn WHERE Cust_Drawing_Path like "' . $cust_drawing . '"';
        $resultat_pn = $mysqli_pn->query($sql_file_pn);
        $rowcount_pn = mysqli_num_rows($resultat_pn);

        mysqli_close($mysqli_pn);

        include('../REL_Connexion_DB.php');

        $sql_file_rel = 'SELECT Cust_Drawing FROM tbl_released_drawing WHERE Cust_Drawing like "' . $cust_draw . '" AND VISA_GID not like ""';
        $resultat_rel = $mysqli->query($sql_file_rel);
        $rowcount_rel = mysqli_num_rows($resultat_rel);

        mysqli_close($mysqli);

        // print_r($sql_file_pn);

        $path =  $Official_Folder . "\\" . $cust_drawing;

        if ($rowcount_rel > 0) {
            $msg_cust = "<span id='message'>Le document est en cours de diffusion et ne peut donc pas être ajouté/écrasé.</span>";
        } else if ($cust_drawing != "") {   // Confirmer que l'utilisateur a désigné un fichier

            if ($rowcount_rel == 0 && $rowcount_pn == 0) {
                $msg_cust = "<span id='message'>Le document n'est pas en lien avec une référence existante sur le serveur et n'est pas en diffusion. Le document n'a pas besoin d'être ajouté. Procédure stoppée</span>";
            } else {

                if ($rowcount_pn > 0) {
                    $count_draw = 'Vous avez mis à jour ' . $rowcount_pn . ' ligne(s) avec l ajout de ce plan';
                }

                if ($_FILES['prod_drawing_file']['size'][0] <= 50000000) // S'assurer que la taille du fichier ne dépasse pas 50Mo
                {
                    $tmp_file = $_FILES['cust_drawing_file']['tmp_name'][0];
                    move_uploaded_file($_FILES['cust_drawing_file']['tmp_name'][0], $Official_Folder . "\\" . basename($_FILES['cust_drawing_file']['name'][0]));
                    $msg_cust = "<span id='message'>" . $count_draw . "</span>";
                } else {
                    $msg_cust = 'Le document ' . $_FILES['cust_drawing_file']['name'][0] . 'est trop gros';
                }
            }
        }
    }

    if (isset($_POST["valid_form"])) {

        $drawing = basename($_FILES['prod_drawing_file']['name'][0]);

        include('../REL_Connexion_DB.php');

        $requete_file_rel = 'SELECT Drawing_Path FROM tbl_released_drawing WHERE Drawing_Path like "' . $drawing . '" AND VISA_GID like ""';
        $resultat_rel = $mysqli->query($requete_file_rel);
        $rowcount_rel = mysqli_num_rows($resultat_rel);

        mysqli_close($mysqli);


        include('../PN_Connexion_PN.php');

        $requete_file_pn = 'SELECT Drawing_Path FROM tbl_pn WHERE Drawing_Path like "' . $drawing . '"';
        $resultat_pn = $mysqli_pn->query($requete_file_pn);
        $rowcount_pn = mysqli_num_rows($resultat_pn);

        mysqli_close($mysqli_pn);

        $path =  $Official_Folder . "\\" . $drawing;

        if ($rowcount_rel > 0) {
            $msg_prod = "<span id='message'>Le document est en cours de diffusion et ne peut donc pas être ajouté/écrasé.</span>";
        } else if ($drawing != "") {   // Confirmer que l'utilisateur a désigné un fichier

            if ($rowcount_rel == 0 && $rowcount_pn == 0) {
                $msg_prod = "<span id='message'>Le fichier n'est pas en lien avec une référence existante sur le serveur et n'est pas en diffusion. Le fichier n'a pas besoin d'être ajouté. Procédure stoppée</span>";
            } else {

                if ($rowcount_pn > 0) {
                    $count_draw = 'Vous avez mis à jour ' . $rowcount_pn . ' référence(s) avec l ajout de ce plan';
                }

                if ($_FILES['prod_drawing_file']['size'][0] <= 50000000) // S'assurer que la taille du fichier ne dépasse pas 50Mo
                {
                    $tmp_file = $_FILES['prod_drawing_file']['tmp_name'][0];
                    move_uploaded_file($_FILES['prod_drawing_file']['tmp_name'][0], $Official_Folder . "\\" . basename($_FILES['prod_drawing_file']['name'][0]));
                    $msg_prod = "<span id='message'>" . $count_draw . "</span>";
                } else {
                    $msg_prod = 'Le fichier ' . $_FILES['prod_drawing_file']['name'][0] . 'est trop gros';
                }
            }
        }
    }

    // <!------------------------------------------->
    // <!-- SUPPRESSION d'un plan DANS BASE release_drawing et dans le dossier-->
    // <!------------------------------------------->

    if (isset($_POST['Delete']) && ($_POST['listed_file_name'] != "")) {

        $prod_drawing = $_POST["listed_file_name"];

        //Connexion à BD
        include('../PN_Connexion_PN.php');

        $sql_file_pn = 'SELECT Drawing_Path FROM tbl_pn WHERE Drawing_Path like "' . $prod_drawing . '"';
        $resultat_pn = $mysqli_pn->query($sql_file_pn);
        $rowcount_pn = mysqli_num_rows($resultat_pn);

        mysqli_close($mysqli_pn);

        include('../REL_Connexion_DB.php');

        $sql_file_rel = 'SELECT Drawing_Path FROM tbl_released_drawing WHERE Drawing_Path like "' . $prod_drawing . '"';
        $resultat_rel = $mysqli->query($sql_file_rel);
        $rowcount_rel = mysqli_num_rows($resultat_rel);

        mysqli_close($mysqli);

        if ($rowcount_rel == "0") {
			if ($rowcount_pn != "0")
			{
				$pn_db_check='Certaines references disponibles dans V2D utilisant ce même plan ne pointeront plus sur le fichier supprimé.';
			} else {
				$pn_db_check='';
			}
            $file_to_delete = $Official_Folder ."\\". $prod_drawing;            // Chemin complet du fichier existant
            unlink($file_to_delete);                                        // Suppression

			include('../PN_Connexion_PN.php');
			$sql_pn_clean_up = 'UPDATE tbl_pn SET Drawing_Path="", Prod_Draw="", Prod_Draw_Rev="" WHERE Drawing_Path like "' . $prod_drawing . '"';
			$resultat_pn_clean_up = $mysqli_pn->query($sql_pn_clean_up);
			mysqli_close($mysqli_pn);
			
            $msg_prod = '<span id="message">Plan ' . $prod_drawing . ' supprimé !</br>'.$pn_db_check.'</span>';
        } else {
            $msg_prod = '<span id="message">Plan ' . $prod_drawing . ' utilisé par d\'autres references dans l\'outil de diffusion - La suppression n\'a pas été effectuée</br></span>';
        }
    }

    if (isset($_POST['Delete_cust']) && ($_POST["listed_cust_file_name"] != "")) {

        $cust_draw = $_POST["cust_draw"];
        $cust_drawing = $_POST["listed_cust_file_name"];

        //Connexion à BD
        include('../PN_Connexion_PN.php');

        $sql_file_pn = 'SELECT Cust_Drawing_Path FROM tbl_pn WHERE Cust_Drawing_Path like "' . $cust_drawing . '"';
        $resultat_pn = $mysqli_pn->query($sql_file_pn);
        $rowcount_pn = mysqli_num_rows($resultat_pn);

        mysqli_close($mysqli_pn);

        include('../REL_Connexion_DB.php');

        $sql_file_rel = 'SELECT Cust_Drawing FROM tbl_released_drawing WHERE Cust_Drawing like "' . $cust_draw . '"';
        $resultat_rel = $mysqli->query($sql_file_rel);
        $rowcount_rel = mysqli_num_rows($resultat_rel);

        mysqli_close($mysqli);

        if ($rowcount_rel == "0" ) 
		{
			if ($rowcount_pn != "0")
			{
				$pn_db_check='Certaines references disponibles dans V2D utilisant ce même plan ne pointeront plus sur le fichier supprimé.';
			} else {
				$pn_db_check='';
			}

            $file_to_delete = $Official_Folder ."\\". $cust_drawing;            // Chemin complet du fichier existant
            unlink($file_to_delete);                                        // Suppression
			
			include('../PN_Connexion_PN.php');
			$sql_pn_clean_up = 'UPDATE tbl_pn SET Cust_Drawing_Path="", Cust_Drawing="", Cust_Drawing_Rev="" WHERE Cust_Drawing_Path like "' . $cust_drawing . '"';
			$resultat_pn_clean_up = $mysqli_pn->query($sql_pn_clean_up);
			mysqli_close($mysqli_pn);
			
            $msg_cust = '<span id="message">Plan client ' . $cust_drawing . ' supprimé !</br>'.$pn_db_check.'</span>';
        } else {
            $msg_cust = '<span id="message">Plan client ' . $cust_drawing . ' utilisé par d\'autres references dans l\'outil de diffusion - La suppression n\'a pas été effectuée</br></span>';
        }
    }
    ?>

    <title>
        PN / File Overwritting Form
    </title>
</head>

<body style="margin-left:30px">
    <form enctype="multipart/form-data" action="" method="post">

        <table id="t03" style="width:80%" border=0>

            <tr>
                <p style="font-weight:bold;">Gestion Fichier</p>
                <div id="Explications">
                    <p>Cette page permet de supprimer un plan et/ou ajouter/écraser un plan sur le serveur</p>
                    <p><strong>Supprimer un plan</strong> sur le serveur : Choisir le plan à supprimer dans la liste déroulante puis cliquer sur le bouton "Supprimer"<br /></p>
                    <p>
                        <strong>Ajouter ou écraser un plan</strong> sur le serveur : Chercher le plan que vous voulez ajouter en cliquant sur "Choisir un fichier".<br /> 
                        Si un plan du même nom existe déjà sur le serveur alors il sera écrasé, ou dans le cas contraire si le plan n'existe pas sur le serveur il sera ajouté.
                    </p>
                </div>
            </tr>
            <br />

            <tr>
                <th id="Title" colspan=3 style="">Plan de Production</th>
				 <td rowspan=4 style="border-left:solid 1px black;padding-right:10px"></td>
                <th id="Title" colspan=3 style="">Plans Client</th>
            </tr>
            <tr>
                <!-- Prod drawing -->
                <td >
                    Fichier à supprimer :
                </td>
                <td>
                    <select tabindex="8" name="listed_file_name" id="listed_file_name" type="submit">
                        <option value="">Choisir un fichier</option>
                        <?php
                        $scandir = scandir($Official_Folder);

                        foreach ($scandir as $fichier) {
                            if (preg_match("#\.(jpg|jpeg|png|gif|bmp|tif|pdf)$#", strtolower($fichier))) {
                                echo '<option value="' . $fichier . '">' . $fichier . '</option><br/>';
                            }
                        }
                        ?>
                </td>
                <td style="padding-left:5px;padding-right:5px;">
                    <input onclick="return Btn_Supp()" style="width:65px;" type="submit" class="btn red" id="Supp_btn" name="Delete" value="Supprimer" title="Suppression du plan de production sélectionné" />
                </td>

               

                <!-- Cust drawing -->
                <td>Fichier à supprimer :</td>
                <td>
                    <select tabindex="9" name="listed_cust_file_name" onclick="check_cust_draw_select()" id="listed_cust_file_name" type="submit">
                        <option value="">Choisir un fichier</option>
                        <?php
                        $scandir = scandir($Official_Folder);

                        foreach ($scandir as $fichier) {
                            if (preg_match("#\.(jpg|jpeg|png|gif|bmp|tif|pdf)$#", strtolower($fichier))) {
                                echo '<option value="' . $fichier . '">' . $fichier . '</option><br/>';
                            }
                        }
                        ?>
                </td>
                <td style="padding-left:5px;padding-right:5px;">
					<input onclick="return Btn_Supp()" style="width:65px;" type="submit" id="Supp_btn" class="btn red" name="Delete_cust" value="Supprimer" title="Suppression du plan client sélectionné" />
				</td>
            </tr>
            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>
            <tr>
                <!-- Prod drawing -->
                <td>
                    Fichier à ajouter ou écraser :
                </td>
                <td>
                    <input style="height:18px;border:none;" tabindex="1" id="prod_drawing_file" name="prod_drawing_file[]" type="file" accept=".pdf">
                </td>
                <td style="padding-left:5px;padding-right:5px;">
                    <input style="width:65px;" onclick="return Ajouter_prod()" name="valid_form" type="submit" id="valid_form" class="btn orange" value="Ajouter" title="Add a new plan in the server" />
                </td>

                <!-- Cust drawing -->
                <td>
                    Fichier à ajouter ou écraser :
                </td>
                <td>
                    <input style="height:18px;border:none;" tabindex="1" id="cust_drawing_file" onchange="check_cust_draw_()" name="cust_drawing_file[]" type="file" accept=".pdf">
                </td>
                <td style="padding-left:5px;padding-right:5px;">
                    <input style="width:65px;" onclick="return Ajouter_cust()" name="Valid_cust" type="submit" id="Valid_cust" class="btn orange" value="Ajouter" title="Add a new cust draw in the server" />
                </td>
            </tr>
            <tr>
                <td colspan=3 style="color:red;text-align:left;margin-left:10px">
                    <?php
                    if ($msg_prod != "") {
                        echo $msg_prod;
                    }
                    ?>
                </td>
                <td colspan=3 style="color:red;text-align:left;margin-left:10px">
                    <?php
                    if ($msg_cust != "") {
                        echo $msg_cust;
                    }
                    ?>
                </td>
                <input hidden tabindex="4" type="text" id="cust_draw" size=22 name="cust_draw">
            </tr>
        </table>
    </form>
</body>

</html>