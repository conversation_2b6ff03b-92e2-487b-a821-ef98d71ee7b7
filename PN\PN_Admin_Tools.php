<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="PN_Admin_styles.css" type="text/css" />
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">


    <script>
        setTimeout(function() {
            var obj = document.getElementById("message");
            obj.innerHTML = "";
        }, 3000);
    </script>

	
    
</head>

<title>Page Principale</title>

<body style="margin-left:30px;">

    

    <!------------------------->
    <!-- UPDATE MOT DE PASSE -->
    <!------------------------->
    <?php
    $msg_conf = "";
    if (isset($_POST['pwd_update']) && isset($_POST['pwd_input']) && ($_POST['pwd_input']) != "") {

        // Get user input values and prepare them for SQL query
        $pwd = $_POST['pwd_input'];

        //Connexion à BD
        include('../PN_Connexion_PN.PHP');

        // Query preparation
        $sql_1 = 'UPDATE tbl_parameters
			  SET 
				Value="' . $pwd . '"
			  WHERE Parameter like "Admin_Password";';


        $resultat = $mysqli_pn->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli_pn);

        // Message confirmation
        $msg_conf = '<span id="message">Mot de passe modifiée !</span>';
    }
    ?>

    <!---------------------------------->
    <!--   BACKUP DES BASE DE DONNEES -->
    <!---------------------------------->
    <?php
    $msg_db = "";
    if (isset($_POST['Backup_start'])) {
        if (isset($_POST['db_pn_picked']) && $_POST['db_pn_picked'] == true) {
            //Extraction du dossier de stocage des sauvegarde
            include('../PN_Connexion_PN.PHP');
            $sql_1 = "SELECT DISTINCT Value from tbl_parameters where Parameter like 'Backup_Folder'";
            $resultat = $mysqli_pn->query($sql_1);
            while ($row = $resultat->fetch_assoc()) {
                $folder_name = $row['Value'];
            }
            mysqli_close($mysqli_pn);
            //-----

            // $user_myadmin = '"aTeVeSCo"';
            // $pass = '"7NY}8G7c{KPv;8peFAg@593vn{6w]."';
            $db = 'db_pn';

            $user_myadmin = '"root"';
			$pass = '"Lemans72!"';

			$param = array(
				$folder_name,
				$user_myadmin,
				$pass,
				$db,
			);

            $param_list = $param[0] . ' ' . $param[1] . ' ' . $param[2] . ' ' . $param[3];
            $bat_to_backup = '"backupdb.bat" ' . $param_list;
            echo exec($bat_to_backup);

            $msg_db = '<span id="message"> ' . $db . ' - backup OK !  </span>';
            // print_r($msg_db);
        }
    }
    ?>

    <form name="name_form" method="post" action="" enctype="multipart/form-data">

        <table border=0 style="width:90%">
            <tr>
                <td>
                        <b>Paramètres</b>
                </td>
            </tr>

            <tr>
                <td>
                    <div id="FilterTitle_User">
                        Choisir les bases de données à sauvegarder:
                    </div>
                </td>

                <td style="color:red;text-align:left;margin-left:10px">
                    <?php
                    if ($msg_db != "") {
                        echo $msg_db;
                    }
                    ?>
                </td>
            </tr>

            <tr>
                <td style="text-indent:55px;font-size:11px">
                    <div id="InpBox">
                        <input type="checkbox" id="db_pn" name="db_pn_picked" value="db_pn" checked>
                        <label for="db_pn">db_pn</label>
                    </div>
                </td>

                <td style="font-size:11px; text-align:left">
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue" name="Backup_start" value="Backup" title="sauvegarde des bases de données de l'outil" />
                    les sauvegardes sont disponibles dans le dossier <i><a href="VIEW_BACKUP.php" target="#">/PN/Backup</a></i>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <hr>
                </td>
            </tr>

            <tr>
                <td>
                       <b>Mot de passe Administration</b>
                </td>
            </tr>

            <tr>
                <td>
                    <div id="FilterTitle_User">
                        Changer le mot de passe pour accèder à la page Administration de l'outil:
                    </div>
                </td>
                <td style="color:red;text-align:left;margin-left:10px">
                    <?php if ($msg_conf != "") {
                        echo $msg_conf;
                    } ?>
                </td>
            </tr>

            <tr>
                <td style="width:30%;">
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px;text-align:left">
                        Mot de passe <font color=red> *</font>:
                    </div>
                </td>
                <?php
                include('../PN_Connexion_PN.PHP');
                $requete_mdp = 'SELECT Value
                          FROM tbl_parameters
                          WHERE Parameter like "Admin_Password"';
                $res = $mysqli_pn->query($requete_mdp);
                while ($row_mdp = $res->fetch_assoc()) {
                    $pwd = $row_mdp['Value'];
                }
                $mysqli_pn->close();
                ?>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input type="text" style="font-size:10pt;height:15px" size=15 name="pwd_input" id="pwd_input" title="Nouveau mot de passe" placeholder="<?php echo $pwd ?>">

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue" name="pwd_update" value="Mise à jour" title="validation du changement de mot de passe Administrateur" />
                    </div>
                </td>
            </tr>

        </table>
    </form>


</body>

</html>