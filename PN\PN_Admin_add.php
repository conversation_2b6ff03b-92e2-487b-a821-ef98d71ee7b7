<!DOCTYPE html>

<html>
    
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta http-equiv='cache-control' content='no-cache'>
<meta http-equiv='expires' content='0'>
<meta http-equiv='pragma' content='no-cache'>

<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="icon" type="image/png" href="\Common_Resources\Release_Icon.png" />
<link rel="stylesheet" type="text/css" href="PN_Admin_styles.css">




<script>
    const array = [0, 0, 0];

    function validation_form() {
        const ref = document.getElementById("Reference").value;
        const ref_rev = document.getElementById("ref_rev").value;
        const title_EN = document.getElementById("title_EN").value;
        const title_FR = document.getElementById("title_FR").value;
        const doctype = document.getElementById("Doc_Type").value;
        const prod_dr = document.getElementById("prod_draw").value;
        const prod_dr_rev = document.getElementById("prod_draw_rev").value;
        const ex_val = document.getElementById("Ex").value;
        const prod_draw_file = document.getElementById("prod_drawing_file").value;
        const file_exist = document.getElementById("listed_file_name").value;
        const product_code = document.getElementById("Product_Code").value;
        const Division = document.getElementById("Division").value;

        // On vérifie qu'une valeur n'est pas rempli dans les champ title
        // Si un des deux champs title est rempli alors on enlève le required de l'autre

        if (prod_draw_file != "" && file_exist != "") {
            alert("Veuillez choisir un seul plan");
            return false;
        }

        if (ref == "" || ref_rev == "" || prod_dr == "" || prod_dr_rev == "" || doctype == "" || ex_val == "" || product_code == "" || Division == "" || title_EN == "" && title_FR == "" || prod_draw_file == "" && file_exist == "") {
            alert("Tous les champs signalés par une étoile (*) doivent être renseignés");
            return false;
        }

        // Recuperation du champ de saisie de la reference pour stockage dans x
        const x = document.getElementById("Alias");
        // Remise en forme de la reference :
        //  - Suppression des espaces de debut et fin (trim)
        //  - Mise en majuscule du v de debut si erreur de saisie
        // -------------------------------------
        let y = x.value.trim();
        y = y.toUpperCase(y);

        if (y.length > 40) {
            alert("Il y a trop de caractères dans le champ 'Alias' (max 40). \nVeuillez corriger l'entrée.");
            return false;
        }

        if (title_FR.length > 40) {
            alert("Il y a trop de caractères dans le champ 'Title' (max 40). \nVeuillez corriger l'entrée.");
            return false;
        }

        if (array.includes(1)) {
            alert('Merci de corriger les erreurs dans le formulaire');
            return false;
        }
        // alert(array);

        var res = confirm("Êtes-vous sur de vouloir valider ?");
        if (res == false) {
            return false;
        }
    }

    // Visualiser ou non le plan selectionné
    function visual_chk() {
        //const visual_status=document.getElementById("pdf_visual");
        const pdf_area = document.getElementById("pdf_visual");
        const visual = document.getElementById("visu_drawing");
        let loaded_file = document.getElementById("prod_drawing_file").files[0]['name'];

        //alert(loaded_file + '   ' + pdf_area.checked);
        if (1 == 1) {
            if (loaded_file != "") {
                var tmppath = URL.createObjectURL(event.target.files[0]);
                tmppath = tmppath + '#toolbar=0&navpanes=0&scrollbar=0&view=Fit';
                visual.setAttribute("src", tmppath);
                alert(tmppath);
                //var tmppath = URL.createObjectURL(event.target.files[0]);
                //tmppath=tmppath + '#toolbar=0&navpanes=0&scrollbar=0';
                //pdf_area.setAttribute("src", tmppath);
            }
        } else {
            tmppath = "";
            //alert(tmppath);
            visual.setAttribute("src", tmppath);
        }

    }


    function ref_input_chk() {
        // Recuperation du champ de saisie de la reference pour stockage dans x
        const x = document.getElementById("Reference");
        const doctype = document.getElementById("Doc_Type").value;

        // Remise en forme de la reference :
        //  - Suppression des espaces de debut et fin (trim)
        //  - Mise en majuscule du v de debut si erreur de saisie
        //  - suppression de l'espace entre le code matiere et le code protection issue d'une copier coller du plan
        // -------------------------------------
        let y = x.value.trim();
        y = y.toUpperCase(y);
        if ((y.length == 19) && (y.substr(0, 1) == "V") && ((y.substr(15, 1) == " ") || (y.substr(15, 1) == "-"))) {
            y = y.substr(0, 15) + y.substr(16, 3);
        }
        let z = y.replace(/ /gi, "-");
        x.value = z;
        // -------------------------------------
        // -------------------------------------
        if (z.length > 18) {
            x.style.backgroundColor = "#F5B7B1";
            x.focus();
        } else if (z.length == 18) {
            x.style.backgroundColor = "white";
        }
        // -------------------------------------
    }

    function prod_drw_file_process() {
        let file_name = document.getElementById("prod_drawing_file").files[0]['name'];
        const prod_dr = document.getElementById("prod_draw");
        const prod_dr_rev = document.getElementById("prod_draw_rev");
        const ref_rev = document.getElementById("ref_rev");
        const ref = document.getElementById("Reference");
        const doctype = document.getElementById("Doc_Type");
        const visual = document.getElementById("visu_drawing");
        const ex_val = document.getElementById("Ex");
        const pdf_visualization = document.getElementById("pdf_visual");
        let prod_draw_name = document.getElementById("prod_drawing_file").files[0]['name'];


        // Affiche le plan choisi dans la fenetre
        //---------------------------------------
        if (1==1) {
            var tmppath = URL.createObjectURL(event.target.files[0]);
            tmppath = tmppath + '#toolbar=0&navpanes=0&scrollbar=0';
            visual.setAttribute("src", tmppath);
        }

        //---------------------------------------

        // Prepare le nom du fichier en supprimant les blancs (trim), le passant en majuscule (touppercase) et supprimant le suffixe .pdf
        //--------------------------------------
        file_name = file_name.trim();
        file_name = file_name.toUpperCase(file_name);
        file_name = file_name.replace(".PDF", "");
        //--------------------------------------

        //--------------------------------------
        // Determination de la racine du plan choisi
        //-------------------------------------------
        let prod_dr_root = file_name.trim();
        if (file_name.includes("REV")) {
            prod_dr_root = prod_dr_root.substr(0, file_name.search("REV"));
            let prod_d_rev = file_name.substr(file_name.indexOf("REV") + 3, file_name.length - file_name.indexOf("REV") + 3);
            file_name = file_name.substr(0, file_name.search("REV"));
            prod_dr_rev.value = prod_d_rev;
            prod_dr.value = prod_dr_root;
        } else {
            let prod_d_rev = file_name.substr(file_name.indexOf("REV") + 3, file_name.length - file_name.indexOf("REV") + 3);
            file_name = file_name.substr(0, file_name.search("REV"));
            prod_dr_rev.value = "";
            prod_dr.value = prod_dr_root;
        }

        // Lance la vérification de format du champ reference article
        //-----------------------------------------------------------
        ref_input_chk();
        //-----------------------------------------------------------

        // Lance la vérification de diffusion déjà potentiellement en cours
        //-----------------------------------------------------------------
        alive_check(2);

        const xhttp = new XMLHttpRequest();
        xhttp.onload = function() {
            const raw_result = this.responseText.trim();
            if (raw_result == true) {
                alert("Ce plan est déjà en cours de diffusion");
                array[0] = 1;
                document.getElementById("warning_plan").innerHTML = '<img opacity="0.5" title="Reference ou plan déjà en cours de diffusion" src="/Common_Resources/warning.png" height="15px">';
            } else {
                document.getElementById("warning_plan").innerHTML = '';
                array[0] = 0;
            }
        }
        if (prod_draw_name != "") {
            xhttp.open("GET", "PN_PLAN_AUTO.php?Draw=" + prod_draw_name);
            xhttp.send();
        }
        //-----------------------------------------------------------------
    }

    function cust_drw_file_process() {
        let cust_draw_name = document.getElementById("cust_drawing_file").files[0]['name'];
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function() {
            const raw_result = this.responseText.trim();
            if (raw_result == true) {
                alert("Ce plan est déjà en cours de diffusion");
                array[1] = 1;
                document.getElementById("warning_cust_plan").innerHTML = '<img opacity="0.5" title="Reference ou plan déjà en cours de diffusion" src="/Common_Resources/warning.png" height="15px">';
            } else {
                document.getElementById("warning_cust_plan").innerHTML = '';
                array[1] = 0;
            }
        }
        if (cust_draw_name != "") {
            xhttp.open("GET", "PN_PLAN_AUTO.php?Draw=" + cust_draw_name);
            xhttp.send();
        }
    }

    function alive_check(check_type) {
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function() {
            const raw_result = this.responseText.trim();
            const result = raw_result.split(":");

            var msg = "";
            if (check_type == 1) {
                if (result[0] != "0") {
                    const resultat_pn = result[0].split("__");
                    msg = "La reference" + resultat_pn[0] + " rev" + resultat_pn[1] + " existe deja dans la base. Vous ne pouvez pas l'ajouter.";
                }
                if (result[1] != "0") {
                    const resultat_release = result[1].split("__");
                    if (msg != "") {
                        msg = msg + "\n";
                        msg = msg + "Cette reference" + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Vous ne pouvez pas l'ajouter.";
                    } else {
                        msg = "Cette reference" + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Vous ne pouvez pas l'ajouter.";
                    }
                }
            }
            if (check_type == 2) {
                if (result[0] != "0") {
                    const resultat_pn = result[0].split("__");
                    msg = "Le Prod_Draw " + resultat_pn[0] + " rev" + resultat_pn[1] + " existe deja dans la base. Attention.";
                }
                if (result[1] != "0") {
                    const resultat_release = result[1].split("__");
                    if (msg != "") {
                        msg = msg + "\n";
                        msg = msg + "Ce Prod_Draw " + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Attention.";
                    } else {
                        msg = "Ce Prod_Draw " + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Attention.";
                    }
                }
            }
            if (msg != "") {
                if (check_type == 2) {
                    alert(msg);
                } else {
                    alert(msg);
                    array[2] = 1;
                    document.getElementById("warning_ref").innerHTML = '<img opacity="0.5" title="Reference ou plan déjà en cours de diffusion" src="/Common_Resources/warning.png" height="15px">';
                    document.getElementById('Reference').style.backgroundColor = "#F5B7B1";
                    document.getElementById('ref_rev').style.backgroundColor = "#F5B7B1";
                }
            } else {
                array[2] = 0;
                document.getElementById("warning_ref").innerHTML = '';
                document.getElementById('Reference').style.backgroundColor = "white";
                document.getElementById('ref_rev').style.backgroundColor = "white";
            }
            // else if (result[0] != "0" && result[1] != "0") {
            //     const resultat_pn = result[0].split("__");
            //     const resultat_release = result[1].split("__");
            //     alert("La reference " + resultat_pn[0] + " rev" + resultat_pn[1] + " existe deja dans la base. Vous ne pouvez pas l'ajouter. " + "\n" + "Cette reference " + resultat_release[0] + " rev" + resultat_release[1] + " est en cours de diffusion. Vous ne pouvez pas l'ajouter.");
            //     return false;
            // }
        }

        if (check_type == 1) {
            if (document.getElementById("Reference").value != "" && document.getElementById("Reference").value != "ZPF000000000XXXXXX") {
                const ref_1 = document.getElementById("Reference").value + "__" + document.getElementById("ref_rev").value;
                var id = "0";
                // alert('PN_REF_Alive_Check_AUTO.php?type=Reference__Ref_Rev&to_be_checked=' + ref_1);
                xhttp.open("GET", "PN_REF_Alive_Check_AUTO.php?type=Reference__Ref_Rev&to_be_checked=" + ref_1 + "&ID=" + id);
                xhttp.send();
            }
        }

        if (check_type == 2) {
            if (document.getElementById("prod_draw").value != "") {
                const ref_2 = document.getElementById("prod_draw").value + "__" + document.getElementById("prod_draw_rev").value;
                var id = "0";
                xhttp.open("GET", "PN_REF_Alive_Check_AUTO.php?type=Prod_Draw__Prod_Draw_Rev&to_be_checked=" + ref_2 + "&ID=" + id);
                xhttp.send();
            }
        }
    }

    // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

    function title_check() {
        // Recuperation du champ de saisie de la reference pour stockage dans x
        const title_EN = document.getElementById("title_EN");
        const title_FR = document.getElementById("title_FR");
        // Remise en forme de la reference :
        //  - Suppression des espaces de debut et fin (trim)
        //  - Mise en majuscule du v de debut si erreur de saisie
        // -------------------------------------
        let y = title_EN.value.trim();
        y = y.toUpperCase(y);

        let z = title_FR.value.trim();
        z = z.toUpperCase(y);
        // -------------------------------------
        // -------------------------------------
        if (y.length > 40) {
            alert("Le titre ne peut pas dépasser 40 chiffres");
            title_EN.style.backgroundColor = "#F5B7B1";
        } else if (y.length <= 40) {
            title_EN.style.backgroundColor = "white";
        }

        if (z.length > 40) {
            alert("Le titre ne peut pas dépasser 40 chiffres");
            title_FR.style.backgroundColor = "#F5B7B1";
        } else if (z.length <= 40) {
            title_FR.style.backgroundColor = "white";
        }
        // -------------------------------------
    }

    // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

    function alias_check() {
        // Recuperation du champ de saisie de l'alias pour stockage dans x
        const x = document.getElementById("Alias");
        // Remise en forme de la reference :
        //  - Suppression des espaces de debut et fin (trim)
        //  - Mise en majuscule du v de debut si erreur de saisie
        // -------------------------------------
        let y = x.value.trim();
        y = y.toUpperCase(y);
        // -------------------------------------


        // Verification de la longueur de l'alias : 40 caracteres max
        //   - Changement de la couleur du champ de saisie en rouge dans le cas contraire
        // -------------------------------------
        if (y.length > 40) {
            x.style.backgroundColor = "#F5B7B1";
            alert("L'alias ne peut pas comporter plus de 40 chiffres");
            x.focus();
        } else if (y.length <= 40) {
            x.style.backgroundColor = "white";
        }
        // -------------------------------------
    }

    function Select_Visu() {
        let select_prod_draw = document.getElementById("listed_file_name").value;
        const visual = document.getElementById("visu_drawing");
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function() {
            const raw_result = this.responseText.trim();
            if (select_prod_draw != "") {
                visual.setAttribute("src", raw_result);
            } else {
                raw_result = "";
                visual.setAttribute("src", raw_result);
            }
        }
        if (select_prod_draw != "") {
            xhttp.open("GET", "PN_Apercu_Plan.php?Draw=" + select_prod_draw);
            xhttp.send();
        }
    }

    setTimeout(function() {
        var obj = document.getElementById("message");
        obj.innerHTML = "";
    }, 3000);
</script>

<?php
if (isset($_POST['valid'])) {

    #FOR DEBUGGING ONLY
    #print_r($_POST);

    $msg = "";

    $reference = $_POST['Reference'];
    $ref_rev = $_POST['ref_rev'];
    $prod_draw = $_POST['prod_draw'];
    $prod_draw_rev = $_POST['prod_draw_rev'];

    $title_EN = htmlspecialchars($_POST['title_EN'], ENT_QUOTES);
    if ($title_EN == "") {
        $title_EN = "";
    }

    $title_FR = htmlspecialchars($_POST['title_FR'], ENT_QUOTES);
    if ($title_FR == "") {
        $title_FR = "";
    }

    $alias = $_POST['Alias_name'];
    if ($alias == "") {
        $alias = "";
    }

    $doc_type = $_POST['Doc_Type'];
    $ex = $_POST['Ex'];

    $date_sap = $_POST['DATE_SAP'];
    if ($date_sap == "") {
        $date_sap = date("y-m-d");
    }

    $date_costing = $_POST['DATE_Costing'];
    if ($date_costing == "") {
        $date_costing = date("y-m-d");
    }

    $product_code = $_POST['Product_Code'];

    $rel_pack_num = $_POST['Rel_Pack_Num'];
    if ($rel_pack_num == "") {
        $rel_pack_num = "";
    }

    $division = $_POST['Division'];

    $cust_drawing = $_POST['cust_draw'];
    if ($cust_drawing == "") {
        $cust_drawing = "";
    }

    $cust_drawing_rev = $_POST['cust_draw_rev'];
    if ($cust_drawing_rev == "") {
        $cust_drawing_rev = "";
    }

    //ISOLEMENT DE LA REF DU FICHIER/PLAN CHOISI
    if (isset($_FILES['prod_drawing_file'])) {
        //$drawing_file_name=$_FILES['prod_drawing_file']['tmp_name'][0];
        $drawing_file_name = basename($_FILES['prod_drawing_file']['name'][0]);
    }

    if ($_POST['listed_file_name'] != "") {
        $drawing_file_name = $_POST['listed_file_name'];
    }

    // if ($_POST['listed_file_name'] != "") {
    //     $drawing_file_name = $_POST['listed_file_name'];
    // } else {
    //     $drawing_file_name = "";
    // }

    if (isset($_FILES['cust_drawing_file'])) {
        //$drawing_file_name=$_FILES['prod_drawing_file']['tmp_name'][0];
        $cust_drawing_file_name = basename($_FILES['cust_drawing_file']['name'][0]);
    } else {
        $cust_drawing_file_name = "";
    }




    // Incremetation de l'ID pour la diff en cours
    include('../PN_Connexion_PN.php');

    $pn_numbering = 'SELECT MAX(ID) from tbl_pn';
    $pn_max_ID_tmp = $mysqli_pn->query($pn_numbering);
    $pn_max_ID = mysqli_fetch_row($pn_max_ID_tmp);
    $ID_max = intval($pn_max_ID[0]) + 1;

    // ####################################################################################
    // IF id_to_update=="" ou id_to_update==0 --> CREATION DANS TABLE TBL_RELREASE_DRAWING
    // ####################################################################################
    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_pn VALUES (
                    "' . $ID_max . '",
                    "' . $reference . '",
                    "' . $ref_rev . '",
                    "' . $title_EN . '",
                    "' . $title_FR . '",
                    "' . $prod_draw . '",
                    "' . $prod_draw_rev . '",
                    "' . $drawing_file_name . '",
                    "' . $alias . '",
                    "' . $cust_drawing . '",
                    "' . $cust_drawing_rev . '",
                    "' . $cust_drawing_file_name . '",
                    "' . $product_code . '",
                    "' . $doc_type . '",
                    "' . $ex . '",
                    "' . $division . '",
                    "' . $rel_pack_num . '",
                    "' . $date_sap . '",
                    "' . $date_costing . '"
                    );';


    $resultat = $mysqli_pn->query($sql_1);

    $msg = "La référence " . $reference . " a été ajoutée avec succès.";

    // on ferme la connexion
    mysqli_close($mysqli_pn);
}

// $drawing_file = basename($_FILES['prod_drawing_file']['name'][0]);

//     if ($drawing_file != "") {
//         $drawing_file_name = basename($_FILES['prod_drawing_file']['name'][0]);
//         $path_attachment = "C:\wamp\www\REL\DRAWINGS\OFFICIAL";

//         $path =  $path_attachment . "\\" . $drawing_file_name;

//         echo '<script>alert("' . $drawing_file_name . '")</script>';

//         if (file_exists($path)) {
//             echo 'File exists';
//         } else {
//             echo 'File does not exist';
//         }
//     }

?>

<head>

    <title>
        <?php echo 'PN / Creation '; ?>
    </title>

</head>

<body>


    <form enctype="multipart/form-data" action="" method="POST">

        <table id="t02" border=0>
            <h1>New Reference Creation</h1>

            <tr>
                <td colspan=2 style="padding-top:-10px;padding-bottom:-10px">
                    <hr>
                </td>
            </tr>

            <tr>
                <td>
                    <div id="Body">
                        Référence <FONT color="#EE0000">*</FONT>:
                    </div>
                </td>
                <td>
                    <div>
                        <input required tabindex="3" type="text" onblur="alive_check(1)" id="Reference" size=22 name="Reference" title="18 characters" onchange="ref_input_chk()">
                        <input required tabindex="4" maxlength="3" type="text" onblur="alive_check(1)" id="ref_rev" size=3 name="ref_rev" title="MAJOR = actual change in design or in BoM&#013MINOR = document update" placeholder="rev">
                        <span style="vertical-align:middle" id="warning_ref"></span>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Plan de Production <FONT color="#EE0000">*</FONT>:
                    </div>
                </td>
                <td>
                    <div>
                        <input required tabindex="5" type="text" id="prod_draw" size=22 name="prod_draw" onblur="alive_check(2)">
                        <input required tabindex="6" type="text" id="prod_draw_rev" size=3 name="prod_draw_rev" onblur="alive_check(2)" placeholder="rev">
                        <!-- <span style="vertical-align:middle" id="warning_draw"></span> -->
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Numéro de Diffusion:
                    </div>
                </td>
                <td>
                    <div>
                        <div id="Pack">
                            <input tabindex="7" list="Rel_Pack_Num" name="Rel_Pack_Num" multiple id="Rel_Pack_Num_" title="">
                        </div>
                        <datalist id="Rel_Pack_Num">
                            <!--LISTE DEROULANTE DYNAMIQUE-->
                            <!------------------------------>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = "SELECT DISTINCT Rel_Pack_Num FROM tbl_released_package";
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<option value ="' . $row['Rel_Pack_Num'] . '">' . $row['Rel_Pack_Num'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                            <!------------------------------>
                        </datalist>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Alias / Ref Commerciale:
                    </div>
                </td>
                <td>
                    <div>
                        <!-- Ajout d'une div pour faire un innerHTML -->
                        <div id="digit_alias">
                            <input tabindex="8" type="text" id="Alias" name="Alias_name" size=35 title="Catalogue number - Examples: &#013 9316-51H 61-21 PN 8 &#013 276-8203-64 GT &#013 P18-SW400-HARN-0025 &#013 etc..." onchange="alias_check()">
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Titre anglais <FONT color="#EE0000">*</FONT>:
                    </div>
                <td>
                    <div>
                        <!-- Ajout d'une div pour faire un innerHTML -->
                        <div id="digit_title_EN">
                            <input tabindex="9" type="text" id="title_EN" size=35 name="title_EN" title="XX characters max - Description in English" onchange="title_check()">
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Titre français <FONT color="#EE0000">*</FONT>:
                    </div>
                <td>
                    <div>
                        <!-- Ajout d'une div pour faire un innerHTML -->
                        <div id="digit_title_FR">
                            <input tabindex="10" type="text" id="title_FR" size=35 name="title_FR" title="XX characters max - Description in English" onchange="title_check()">
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Type <FONT color="#EE0000">*</FONT>:
                    </div>
                </td>
                <td>
                    <div>
                        <select required style="height:22px" tabindex="11" name="Doc_Type" id="Doc_Type" type="submit" title="">
                            <option value=""></option>
                            <!------------------------------>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = "SELECT DISTINCT Doc_Type, Doc_Type_Description FROM tbl_doc_type ORDER BY Doc_Type DESC;";
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<option value ="' . $row['Doc_Type'] . '">' . $row['Doc_Type'] . ' - ' . $row['Doc_Type_Description'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                            <!------------------------------>
                        </select>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Certification <FONT color="#EE0000">*</FONT>:
                    </div>
                </td>
                <td>
                    <div>
                        <select required style="height:22px" tabindex="12" name="Ex" id="Ex" type="submit" title="is the drawing/ref ex?">
                            <option value=""></option>

                            <!------------------------------>
                            <?php
                            include('../SCM_Connexion_DB.php');
                            $requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex DESC;";
                            $resultat = $mysqli_scm->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<option value ="' . $row['Ex'] . '">' . $row['Ex'] . '</option><br/>';
                            }
                            mysqli_close($mysqli_scm);
                            ?>
                            <!------------------------------>
                        </select>
                    </div>
                </td>
            </tr>

            <tr>
                <td>
                    <div id="Body">
                        Fichier Plan Client :
                    </div>
                </td>
                <td style="vertical-align:bottom">
                        <input style="height:22px;border:none;vertical-align:bottom" tabindex="13" id="cust_drawing_file" name="cust_drawing_file[]" type="file" accept=".pdf" onchange="cust_drw_file_process(this)">
                        <span style="vertical-align:middle" id="warning_cust_plan"></span>
                </td>
            </tr>

            <tr>
                <td>
                    <div id="Body">
                        Plan Client :
                    </div>
                </td>
                <td>
                    <div>
                        <input tabindex="14" type="text" id="cust_draw" size=22 name="cust_draw">
                        <input tabindex="15" type="text" id="cust_draw_rev" size=3 name="cust_draw_rev" placeholder="rev">
                    </div>
                </td>
            </tr>

            <tr>
                <td>
                    <div id="Body">
                        Code Produit <FONT color="#EE0000">*</FONT>:
                    </div>
                </td>
                <td>
                    <div>
                        <select required style="height:22px" tabindex="16" id="Product_Code" name="Product_Code" type="submit" title="">
                            <option value=""></option>
                            <!--LISTE DEROULANTE DYNAMIQUE-->
                            <!------------------------------>
                            <?php
                            include('../SCM_Connexion_DB.php');
                            $requete = "SELECT DISTINCT Code, Description FROM tbl_product_code";
                            $resultat = $mysqli_scm->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<option value ="' . $row['Code'] . '">' . $row['Code'] . ' / ' . $row['Description'] . ' </option><br/>';
                            }
                            mysqli_close($mysqli_scm);
                            ?>
                            <!------------------------------>
                        </select>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Division <FONT color="#EE0000">*</FONT>:
                    </div>
                </td>
                <td>
                    <div>
                        <select required style="height:22px" tabindex="17" id="Division" name="Division" type="submit" title="">
                            <option value=""></option>
                            <!--LISTE DEROULANTE DYNAMIQUE-->
                            <!------------------------------>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = "SELECT DISTINCT Activity FROM tbl_activity";
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<option value ="' . $row['Activity'] . '">' . $row['Activity'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                            <!------------------------------>
                        </select>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Date Création SAP :
                    </div>
                </td>
                <td>
                    <div>
                        <input tabindex="18" style="height:22px;font-size:12px;font-family: Tahoma, sans-serif;" type="date" class="input_date" id="DATE_SAP" name="DATE_SAP">
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="Body">
                        Date Costing :
                    </div>
                </td>
                <td>
                    <div>
                        <input tabindex="19" style="height:22px;font-size:12px;font-family: Tahoma, sans-serif;" type="date" class="input_date" id="DATE_Costing" name="DATE_Costing">
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <input tabindex="20" type="submit" style="width:37%;margin-left:9px;" value="Ajouter" onclick="return validation_form()" class="btn orange" name="valid" id="valid_form">
                </td>
            </tr>
            <tr>
                <td colspan=5>
                    <?php
                    error_reporting(0); // On déclare qu'on ne souhaite pas afficher les erreurs 
                    echo '<span style="color:red;" id="message">' . $msg . '</span>'
                    ?>
                </td>
            </tr>
        </table>
    </form>

</body>

</html>