body {
  background-color: transparent;
  color: black;
  font-size: 9pt;
  font-family: Tahoma, sans-serif;
  font-weight: normal;
}

/*  Page PN_Admin_from.php */

div#Title {
  margin-left: 5px;
  margin-top: 5px;
  text-align: left;
  margin-bottom: 6px;
  margin-left: 10px;
  vertical-align: middle;
  font-weight: Bold;
  font-size: 9pt;
}

div#Body {
  text-indent: 10px;
  margin-top: 5px;
}

#t01 {
  width: 100%;
  border-collapse: collapse;
  vertical-align: middle;
}

#t01 td {
  text-align: left;
  vertical-align: middle;
  font-size: 8.5pt;
}

#t01 tr {
  height: 20px;
}

/* Page PN_Admin_add.php */

h1 {
  font-size: 9pt;
  margin: 15px;
}

#t02 {
  width: 100%;
}

#t02 tr {
  height: 20px;
}

#t02 td,
#t03 td {
  text-align: left;
  vertical-align: middle;
  font-size: 8.5pt;
}

input,
select {
  font-size: 8.5pt;
  background-color: transparent;
  font-size: 11;
  height: 13pt;
  border: 1px solid black;
}

#valid_form,
#Valid_cust {
  border-radius: 10px;
  font-size: 10px;
  height: 20px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.4), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  text-align: center;
  cursor: pointer;
}

/* Page PN_Admin_File_Overwritting.php */

#t03 {
  width: calc(100% - 11%);
border-collapse:collapse;
}

#t03 th {
  font-size: 9pt;
  text-align: left;
padding-bottom:10px;
width:40vw;
}

#Supp_btn {
  border-radius: 10px;
  font-size: 10px;
  height: 20px;
  box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.4), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  text-align: center;
  cursor: pointer;
}


.title_skrinked {
  margin-left: 0px;
}

.main_frame_reduced {
  position: fixed;
  z-index: 1;
  top: 68px;
  left: 14%;
  width: 100%;
  height: calc(100% - 70px);
}

div#FilterTitle {
  text-indent: 20px;
  font-size: 8pt;
  font-weight: normal;
  font-family: Tahoma, sans-serif;
  margin-left: 5px;
  margin-bottom: 5px;
  text-align: justify;
}

div#FilterTitle_User {
  text-indent: 40px;
  font-size: 8pt;
  font-weight: normal;
  font-family: Tahoma, sans-serif;
  text-align: justify;
}

div#InpBox_User {
  font-size: 8pt;
  font-weight: normal;
  font-family: Tahoma, sans-serif;
  text-align: left;
  vertical-align: middle;
  margin-bottom: 0px;
  margin-top: 0px;
  margin-left: 2px;
}

#toast-container > .toast-success {
  margin-right: 110px;
}