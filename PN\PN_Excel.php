<?php	
	
	// PREPARATION DE VARIALBES DE RECHERCHE
	// -------------------------------------
	if (isset($_GET['ref']))
	{
		if ($_GET['ref']!="")
		{
			$ref=str_replace("*","%",$_GET['ref']);
		} 
		else
		{$ref="%";}
	}
	else
	{$ref="%";}


	if (isset($_GET['draw']))
	{
		if ($_GET['draw']!="")
		{
			$draw=str_replace("*","%",$_GET['draw']);
		} 
		else
		{$draw="%";}
	}
	else
	{$draw="%";}


	if (isset($_GET['certif']))
	{
		if ($_GET['certif']!="")
		{
			$certif=str_replace("*","%",$_GET['certif']);
			} 
		else
		{$certif="%";}
	}
	else
	{$certif="%";}


	if (isset($_GET['division']))
	{
		if ($_GET['division']!="")
		{
			$division=str_replace("*","%",$_GET['division']);
		} 
		else
		{$division="%";}
	}
	else
	{$division="%";}


	if (isset($_GET['product_code']))
	{
		if ($_GET['product_code']!="")
		{
			$product_code=str_replace("*","%",$_GET['product_code']);
		} 
		else
		{$product_code="%";}
	}
	else
	{$product_code="%";}


	if (isset($_GET['title']))
	{
		if ($_GET['title']!="")
		{
			$title=str_replace("*","%",$_GET['title']);
		} 
		else
		{$title="%";}
	}
	else
	{$title="%";}


	if (isset($_GET['alias']))
	{
		if ($_GET['alias']!="")
		{
			$alias=str_replace("*","%",$_GET['alias']);
		} 
		else
		{$alias="%";}
	}
	else
	{$alias="%";}

	if (isset($_GET['cust_drawing']))
	{
		if ($_GET['cust_drawing']!="")
		{
			$cust_drawing=str_replace("*","%",$_GET['cust_drawing']);
		} 
		else
		{$cust_drawing="%";}
	}
	else
	{$cust_drawing="%";}


	// -----------------
		

	$query_1='
			SELECT
			  *
			FROM  tbl_pn
			WHERE 
				 Reference like "'.$ref.'" 
			 AND Prod_Draw like "'.$draw.'"
			 AND Certif like "'.$certif.'"
			 AND Division like "'.$division.'"
			 AND Product_Code like "'.$product_code.'"
			 AND Alias like "'.$alias.'"
			 AND Cust_Drawing like "'.$cust_drawing.'"
			 AND (
				   Ref_Title_FRA like "'.$title.'" OR Ref_Title_EN like "'.$title.'" 
				  )
			 GROUP BY Reference, Prod_Draw
			 ORDER BY Reference DESC, Ref_Rev DESC, Prod_Draw DESC, Prod_Draw_Rev DESC
			 ';


	include('../PN_Connexion_PN.PHP');
	$resultat = $mysqli_pn->query($query_1);
	
	$table = [];
	$table[] = ['Reference', 'R', 'Ref_Title_EN', 'Ref_Title_FR', 'Prod Drawing', 'R', 'File', 'Alias', 'Cust Drawing', 'R', 'File', 'Product Code', 'Type', 'Certif', 'Division', 'Diff', 'Date SAP', 'Date Costing'];
	

	//Parcourons ces enregistrements et insérons les dans notre tableau
	while ($row = $resultat->fetch_assoc()) {
		$table[] = [$row['Reference'], $row['Ref_Rev'], $row['Ref_Title_EN'], $row['Ref_Title_FRA'], $row['Prod_Draw'], $row['Prod_Draw_Rev'],$row['Drawing_Path'], $row['Alias'], $row['Cust_Drawing'],$row['Cust_Drawing_Rev'], $row['Cust_Drawing_Path'], $row['Product_Code'], $row['Doc_Type'], $row['Certif'], $row['Division'], $row['Rel_Pack_Num'], $row['DATE_SAP'], $row['DATE_Costing']];
	}

	//File name defintion
	$csv_file_name = '.\Report\\' . date("Y_m_d_H_i_s") . '_PN_Export.csv';

	//CSV file opening
	$fichier_csv = fopen($csv_file_name, 'w');

	//afficher correctement par exemple les caractères accentués
	fprintf($fichier_csv, chr(0xEF) . chr(0xBB) . chr(0xBF));

	//Parcourer le tableau et écrivons dans le fichier CSV avec la fonction fputcsv
	foreach ($table as $ligne) {
		fputcsv($fichier_csv, $ligne, ";");
	}

	//Fermer maintenant le fichier
	fclose($fichier_csv);

	echo $csv_file_name;
		
	$mysqli_pn->close();

?>