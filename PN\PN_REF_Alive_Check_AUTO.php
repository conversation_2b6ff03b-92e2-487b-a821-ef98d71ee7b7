<?php

$type = explode("__", $_GET['type']);
$ref = explode("__", $_GET['to_be_checked']);

// Reference = $type[0]
// Ref_Rev = $type[1]

$ID_pn = 'AND ID not like "' . $_GET['ID'] . '"';
$ID_release = 'AND tbl_released_drawing.ID not like "' . $_GET['ID'] . '"';
if ($_GET['ID'] == '0') {
	$ID_pn = '';
	$ID_release = '';
}

include('../PN_Connexion_PN.PHP');
// -------------------------------- NOUVEAU --------------------------------

// On vérifie que la référence et la revision  existe dans la base
$query_pn = 'SELECT DISTINCT ' . $type[0] . ',' . $type[1] . '
				FROM tbl_pn
				WHERE ' . $type[0] . ' like "' . $ref[0] . '" 
				AND ' . $type[1] . ' like "' . $ref[1] . '"
				AND ' . $type[0] . ' not like "-"
				AND ' . $type[1] . ' not like "-"
				' . $ID_pn . '
				  ;';
$resultat_pn = $mysqli_pn->query($query_pn);
$rowcount_pn = mysqli_num_rows($resultat_pn);

// Si la référence existe dans la base
$output = "0";
if ($rowcount_pn > 0) {
	while ($row = $resultat_pn->fetch_assoc()) {
		if ($output == "0") {
			$output = $row[$type[0]];
		} else {
			$output = $output . '|' . $row[$type[0]];
		}
		$output = $output . "__" . $row[$type[1]];
	}
} else {
}
mysqli_close($mysqli_pn);

// -------------------------------- release ----------------------------------

include('../REL_Connexion_DB.PHP');

$query_3 = 'SELECT DISTINCT ' . $type[0] . ',' . $type[1] . '
				FROM tbl_released_package 
				LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE ' . $type[0] . ' like "' . $ref[0] . '"
				AND ' . $type[1] . ' like "' . $ref[1] . '"
				AND ' . $type[0] . ' not like "-"
				AND ' . $type[1] . ' not like "-"
				  AND VISA_GID like ""
				  AND Creation_VISA not like ""
					' . $ID_release . '
				  ;';
$resultat = $mysqli->query($query_3);
$rowcount = mysqli_num_rows($resultat);

$output = $output . ':';
if ($rowcount > 0) {
	while ($row = $resultat->fetch_assoc()) {
		if ($output == "0:") {
			$output = $output . $row[$type[0]];
		} else {
			$output = $output . '|' . $row[$type[0]];
		}
		$output = $output . "__" . $row[$type[1]];
	}
} else {
	$output = $output . '0';
}

echo $output;

mysqli_close($mysqli);
