<?php
    /*include('../PN_Connexion_PN.php');

	$sql_1 = 'SELECT Drawing_Path from tbl_pn where Reference="'.$_POST['ref'].'"';


	$resultat = $mysqli_pn->query($sql_1);
    while ($row = $resultat->fetch_assoc()) {
        $prod_draw_preview = $row['Drawing_Path'];
        print_r("https://frscmbe.scmlemans.com/REL/DRAWINGS/OFFICIAL/" . $prod_draw_preview);
    }   */     

    include('../PN_Connexion_PN.php');

    $sql_1 = 'SELECT Drawing_Path from tbl_pn where Prod_Draw_Rev=(select Prod_Draw_Rev from tbl_pn where Reference="'.$_POST['ref'].'" order by Prod_Draw_Rev desc limit 1) and Reference="'.$_POST['ref'].'" ';
    $resultat = $mysqli_pn->query($sql_1);

    $links = array(); 

    while ($row = $resultat->fetch_assoc()) {
        $prod_draw_preview = $row['Drawing_Path'];
        $links[] = "https://frscmbe.scmlemans.com/REL/DRAWINGS/OFFICIAL/" . $prod_draw_preview;
    }

    echo implode(',', $links);
?>
