<?php

    include('../PN_Connexion_PN.php');


    if(!isset($_POST['rev'])){
        $sql_1 = 'SELECT distinct Prod_Draw, Ref_Rev, Prod_Draw_Rev from tbl_pn where Prod_Draw_Rev=(select Prod_Draw_Rev from tbl_pn where Reference="'.$_POST['ref'].'" order by Prod_Draw_Rev desc limit 1) and Reference="'.$_POST['ref'].'" ';
    }else{
        $sql_1 = 'SELECT distinct Prod_Draw, Ref_Rev, Prod_Draw_Rev from tbl_pn where Reference="'.$_POST['ref'].'" and Ref_Rev="'.$_POST['rev'].'" order by Prod_Draw_Rev desc';
    }
    $resultat = $mysqli_pn->query($sql_1);

    $links = array(); 

    while ($row = $resultat->fetch_assoc()) {
        $rev = $row['Ref_Rev'];
        $prod_draw_preview = $row['Prod_Draw'];
        $prod_draw_rev = $row['Prod_Draw_Rev'];
        $links[] = "https://app.aletiq.com/parts/preview/id/" . $prod_draw_preview . "/revision/" . $prod_draw_rev;
    }

    $res = [
        'rev' => $rev,
        'plans' => $links
    ];

    header('Content-Type: application/json');
    echo json_encode($res);

?>
