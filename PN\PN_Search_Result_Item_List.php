<html>
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="PN_Styles.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<script>		
	
	
	// RECUPERATION DES VALEURS CHOISIES PAR L'UTILISATEUR POUR MODIFICATION/SUPPRESSION
	function frame_update(obj) 
	{
		// RECUPERATION DE LA PHASE (CODE) ET DU TITRE ASSOCIE CHOISI PAR L'UTILISATEUR
		
		// SELECTION DU BOUTON RADIO ASSOCIE A LA LIGNE DU TABLEAU CHOISI
		const indx="Radio_Picked_" + obj.cells[0].textContent.trim();
		const id=obj.cells[0].textContent.trim();
		const ref=obj.cells[2].textContent.trim();
		const ref_rev=obj.cells[3].textContent.trim();
		const title=obj.cells[4].textContent.trim();
		const prod_draw=obj.cells[5].textContent.trim();
		const prod_draw_rev=obj.cells[6].textContent.trim();
		const product_code=obj.cells[7].textContent.trim();
		const certif=obj.cells[8].textContent.trim();
		//const division=obj.cells[9].textContent.trim();
		
		// SELECTION DU SELECTOR ASSOCIE A LA LIGNE CLIQUEE
		document.querySelector('input[name="Picked_ID"]:checked');
		document.getElementById(indx).checked = true;

		
		// CHANGEMENT DE LA SOURCE  DE L'IFRAME CONTENANT LA PAGE PN_Search_Result_Item_Details.php
		window.parent.document.getElementById("Item_Details_Frame_id").src=	'PN_Search_Result_Item_Details.php?ID=' + id + 
																			'&ref=' + ref +
																			'&ref_rev=' + ref_rev +
																			'&title=' + title +
																			'&prod_draw=' + prod_draw + 
																			'&prod_draw_rev=' + prod_draw_rev +
																			'&product_code=' + product_code +
																			'&certif=' + certif;

		// MISE EN LUMIERE DE LA LIGNE SELECTIONNEE
			var tbl = obj.parentNode;
			var rows = document.getElementsByTagName('tr');

			for (var i=0;i<rows.length;i++)
			{
				if (rows[i]!=obj) 
				{
					rows[i].setAttribute('class',"unpicked_line");
				} else {
					obj.setAttribute('class',"picked_line");
				}
			}
	}
	
	
	
		// DETECTION DE L'EVENEMENT DE REDIMENSIONNEMENT DE LA FENETRE 
		// -----------------------------------------------------------
		//window.onresize = handle;
		// TRONCATURE DES DESCRIPTIONS TROP LONGUeS DANS LE TABLEAUX
		//function handle()
		// {
			// const data_table=document.getElementById("t04");
			// const th_list=data_table.getElementsByTagName("th");
			// var table_width=parent.window.getElementById("Item_List_Frame").style.width;
			// var curr_width_raw=0;
			// for (let i = 0; i<=12; i++)
			// {
				// if (i!=3)
				// {
					// var curr_width=th_list[i].style.width;
					// var px_pos=curr_width.indexOf("px");
					// var curr_width_raw=curr_width.substring(0,px_pos-1);
					// curr_width_raw=curr_width_raw+curr_width;
				// }
			// }
		// }
		// -----------------------------------------------------------
	
	

	
</script>
</head>



<!---------------------------->
<!-- MISE EN FORME DONNEES  -->
<!---------------------------->
 <?php
if (isset($_GET['draw']))
{
	if ($_GET['draw']!="")
	{
		$search_draw=str_replace("*","%",$_GET['draw']);
	} 
	else
	{
		$search_draw="%";
	}
}
else
{
	$search_draw="%";
}

if (isset($_GET['ref']))
{
	if ($_GET['ref']!="")
	{
		$search_ref=str_replace("*","%",$_GET['ref']);
	} 
	else
	{
		$search_ref="%";
	}
}
else
{
	$search_ref="%";
}


if (isset($_GET['certif']))
{
	if ($_GET['certif']!="")
	{
		$search_certif=str_replace("*","%",$_GET['certif']);
	} 
	else
	{
		$search_certif="%";
	}
}
else
{
	$search_certif="%";
}

if (isset($_GET['division']))
{
	if ($_GET['division']!="")
	{
		$search_division=str_replace("*","%",$_GET['division']);
	} 
	else
	{
		$search_division="%";
	}
}
else
{
	$search_division="%";
}

if (isset($_GET['product_code']))
{
	if ($_GET['product_code']!="")
	{
		$search_product_code=str_replace("*","%",$_GET['product_code']);
	} 
	else
	{
		$search_product_code="%";
	}
}
else
{
	$search_product_code="%";
}

if (isset($_GET['title']))
{
	if ($_GET['title']!="")
	{
		$search_title=str_replace("*","%",$_GET['title']);
	} 
	else
	{
		$search_title="%";
	}
}
else
{
	$search_title="%";
}

if (isset($_GET['alias']))
{
	if ($_GET['alias']!="")
	{
		$search_alias=str_replace("*","%",$_GET['alias']);
	} 
	else
	{
		$search_alias="%";
	}
}
else
{
	$search_alias="%";
}


if (isset($_GET['cust_drawing']))
{
	if ($_GET['cust_drawing']!="")
	{
		$search_cust_drawing=str_replace("*","%",$_GET['cust_drawing']);
	} 
	else
	{
		$search_cust_drawing="%";
	}
}
else
{
	$search_cust_drawing="%";
}


?>


<body>

<!-------------------------------------------->
<!-- AFFICHAGE TABLE IMPUTATION HORS PROJET -->
<!-------------------------------------------->

<table id="t04">

	<tr>
		<th style="width:8px;vertical-align:middle;">
			O
		</th>
		<th style="width:135px">
			Référence
		</th>
		<th style="width:30px">
			Rev
		</th>
		<th style="">
			Titre/Désignation
		</th>
		<th style="width:190px">
			Plan de Production
		</th>
		<th style="width:30px">
			Rev
		</th>
		<th style="width:220px">
			Ref. Commerciale
		</th>
		<th style="width:165px">
			Plan Client
		</th>
		<th style="width:35px">
			Rev
		</th>
		<th style="width:100px"><!--55px"-->
			Code
		</th>
		<th style="width:50px">
			Certif.
		</th>
		<th style="width:45px">
			Div.
		</th>
		<th style="width:30px">
			Type
		</th>
	</tr>

	<?php



	
		include('../PN_Connexion_PN.PHP');
		$requete = 'SELECT 
					  Max(ID) as "ID", 
					  Reference, 
					  MAX(Ref_Rev) as "Ref_Rev", 
					  Ref_Title_EN,
					  Ref_Title_FRA,
					  Prod_Draw,
					  MAX(Prod_Draw_Rev) as "Prod_Draw_Rev",
					  Drawing_Path,
					  Alias,
					  Cust_Drawing,
					  Cust_Drawing_Rev,
					  Product_Code, 
					  Doc_Type,
					  Certif,
					  Division,
					  Rel_Pack_Num,
					  DATE_SAP, 
					  DATE_Costing 
					FROM  tbl_pn
					WHERE 
						 Reference like "'.$search_ref.'" 
					 AND Prod_Draw like "'.$search_draw.'"
					 AND Certif like "'.$search_certif.'"
					 AND Division like "'.$search_division.'"
					 AND Product_Code like "'.$search_product_code.'"
					 AND Alias like "'.$search_alias.'"
					 AND Cust_Drawing like "'.$search_cust_drawing.'"
					 AND (
						   Ref_Title_FRA like "'.$search_title.'" OR Ref_Title_EN like "'.$search_title.'" 
						  )
				   GROUP BY Reference, Prod_Draw
				   ORDER BY Reference DESC, Ref_Rev DESC, Prod_Draw DESC, Prod_Draw_Rev DESC
				  
				   LIMIT '.$_GET['start'].','.$_GET['nb'];
				   
		$resultat = $mysqli_pn->query($requete);
		$rowcount=mysqli_num_rows($resultat);
		
		if ($rowcount>=1)
		{
			while ($row = $resultat->fetch_assoc())
			{
				
				echo '
				<tr onclick="frame_update(this)">
					<td hidden>
						'.$row['ID'].'
					</td>
					<td style="vertical-align:top;">
						<input type="radio"  id="Radio_Picked_'.$row['ID'].'" style="width:10px" name="Picked_ID" value="'.$row['ID'].'">
					</td>
					<td>
						'.$row['Reference'].'
					</td>
					<td>
						'.$row['Ref_Rev'].'
					</td>';
					
					$nb_limit=200 ; // TO BE FINALIZED
					if ($row['Ref_Title_EN']=="")
					{
						$nbcar=strlen($row['Ref_Title_FRA']);
						if($nbcar>$nb_limit)
						{
							$designation=substr($row['Ref_Title_FRA'],0,$nb_limit).'...';
						} else {
							$designation=$row['Ref_Title_FRA'];
						}
					} else {
						$nbcar=strlen($row['Ref_Title_EN']);
						if($nbcar>$nb_limit)
						{
							$designation=substr($row['Ref_Title_EN'],0,$nb_limit).'...';
						} else {
							$designation=$row['Ref_Title_EN'];
						}
					}
				echo '
					<td>
						'.$designation.'
					</td>
					<td>
						'.$row['Prod_Draw'].'
					</td>
					<td>
						'.$row['Prod_Draw_Rev'].'
					</td>
					<td>
						'.$row['Alias'].'
					</td>
					<td>
						'.$row['Cust_Drawing'].'
					</td>
					<td>
						'.$row['Cust_Drawing_Rev'].'
					</td>
					<td>
						'.$row['Product_Code'].'
					</td>
					<td>
						'.$row['Certif'].'
					</td>
					<td>
						<img src="\Common_Resources\Activity_'.$row['Division'].'.png" height="16px" title="'.$row['Division'].'">
					</td>
					<td hidden>
						'.$row['Drawing_Path'].'
					</td>';
						
						switch ($row['Doc_Type'])
							{
								case "MACH";
								case "MOLD";
								case "ASSY";
								case "ARTICLE";
								case "PUR";
									$icon_val='\Common_Resources\article_icon.png';
									$title_val='Article - '.$row['Doc_Type'];
									break;
								case "DOC";
									$icon_val='\Common_Resources\book_icon.png';
									$title_val='Document - '.$row['Doc_Type'];
									break;
							}
				echo '
					<td>
						<img src="'.$icon_val.'" title="'.$title_val.'" height="15px"></img>
					</td>
				</tr>';		
			}
		}
		$mysqli_pn->close();

?>
	



</table>



</body>
</html>

