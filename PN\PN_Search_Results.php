<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />
	
	<meta http-equiv='cache-control' content='no-cache'>
	<meta http-equiv='expires' content='0'>
	<meta http-equiv='pragma' content='no-cache'>

    <link rel="stylesheet" type="text/css" href="PN_Styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

   
<script>

	function selected_page(picked_page, i_val)
	{
		document.getElementsByClassName("picked_page")[0].setAttribute('class',"unpicked_page");
		picked_page.setAttribute('class',"picked_page"); 
	}
	
	function page_change(direction)
	{

		//var direction=parseInt(param_list.split("____")[0]);
		var current_page=parseInt(document.getElementsByName("current_page_td")[0].innerText);
		var previous_page=parseInt(document.getElementsByName("previous_page_td")[0].innerText);
		var total_record=parseInt(document.getElementsByName("total_record")[0].innerText.split(": ")[1]);
		//var nb_per_page=parseInt(document.getElementsByName("nb_per_page")[0].innerText.split("- ")[1]);
		var nb_per_page=parseInt(document.getElementById("limit_page_id").value);
		
		var total_page=Math.ceil(total_record/nb_per_page);
		var calculated_page=0;
		

		if (current_page>=1 && direction>0 && current_page<total_page || current_page>=1 && direction <1 && previous_page>0)
		{
			switch (direction)
			{
			case -1:
			case 1:
				calculated_page=current_page + direction;
				previous_page=calculated_page-1;
				next_page=calculated_page+1;
				break;
			case -2:
				calculated_page=current_page + 0;
				calculated_page=1;
				previous_page="";
				next_page=2;
				break;
			case 2:
				calculated_page=current_page + 0;
				calculated_page=total_page;
				next_page="";
				previous_page=total_page-1;
				break;
			}
			
			if (previous_page==0)
			{
				previous_page="";
			}
			if (next_page>total_page)
			{
				next_page="";
			}

			document.getElementsByName("previous_page_td")[0].innerText=previous_page;
			document.getElementsByName("next_page_td")[0].innerText=next_page;
			document.getElementsByName("current_page_td")[0].innerText=calculated_page;
			
			var start=(calculated_page-1)*nb_per_page;
			var ref=document.getElementById("ref").value;
			var draw=document.getElementById("draw").value;
			var certif=document.getElementById("certif").value;
			var division=document.getElementById("division").value;
			var product_code=document.getElementById("product_code").value;
			var title=document.getElementById("title").value;
			var alias=document.getElementById("alias").value;
			var cust_drawing=document.getElementById("cust_drawing").value;

			var url_list='PN_Search_Result_Item_List.php?start=' + start + '&nb=' + nb_per_page + '&ref=' + ref + '&draw=' + draw + '&alias=' + alias + '&cust_drawing=' + cust_drawing + '&certif=' + certif + '&division=' + division + '&product_code=' + product_code + '&title=' + title;
			
			document.getElementById("Item_List_Frame").src=url_list;
			
		}
		
	}
	
	function nb_record_per_page_update()
	{
		
		var current_limit_val=parent.document.getElementById("limit_val_id").value;
		var new_limit_val=document.getElementById("limit_page_id").value;
		
		var src_page_search=location.href;
		var new_src_page_search=src_page_search.replace("limit_val=" + current_limit_val, "limit_val=" + new_limit_val);
		
		location.href=new_src_page_search;
		parent.document.getElementById("limit_val_id").value=new_limit_val;
		
	}
	
	
	function excel_extraction()
	{
			var ref=document.getElementById("ref").value;
			var draw=document.getElementById("draw").value;
			var certif=document.getElementById("certif").value;
			var division=document.getElementById("division").value;
			var product_code=document.getElementById("product_code").value;
			var title=document.getElementById("title").value;
			var alias=document.getElementById("alias").value;
			var cust_drawing=document.getElementById("cust_drawing").value;
			
		const xhttp = new XMLHttpRequest();
			xhttp.onload = function() 
			{
				let raw_result=this.responseText.trim();
				var res_split=raw_result.split("\\");
				document.getElementById("excel_extract_feedback").innerHTML='<a href="' + raw_result + '">' + res_split[2] + '</a>';
			}
			
		const url_a = "PN_Excel.php?ref=" + ref +
				"&draw=" + draw +
				"&certif=" + certif +
				"&division=" + division +
				"&product_code=" + product_code +
				"&title=" + title +
				"&alias=" + alias +
				"&cust_drawing=" + cust_drawing;

		xhttp.open("GET", url_a);
		xhttp.send();
	}
	
</script>	




</head>

<title>
    Registre Ref & Plan SCM
</title>

<body>
	<?php
	
		// PREPARATION DE VARIALBES DE RECHERCHE
		// -------------------------------------
		if (isset($_GET['ref']))
		{
			if ($_GET['ref']!="")
			{
				$ref=str_replace("*","%",$_GET['ref']);
			} 
			else
			{$ref="%";}
		}
		else
		{$ref="%";}
	
	
		if (isset($_GET['draw']))
		{
			if ($_GET['draw']!="")
			{
				$draw=str_replace("*","%",$_GET['draw']);
			} 
			else
			{$draw="%";}
		}
		else
		{$draw="%";}
	
	
		if (isset($_GET['certif']))
		{
			if ($_GET['certif']!="")
			{
				$certif=str_replace("*","%",$_GET['certif']);
				} 
			else
			{$certif="%";}
		}
		else
		{$certif="%";}
	
	
		if (isset($_GET['division']))
		{
			if ($_GET['division']!="")
			{
				$division=str_replace("*","%",$_GET['division']);
			} 
			else
			{$division="%";}
		}
		else
		{$division="%";}
	
	
		if (isset($_GET['product_code']))
		{
			if ($_GET['product_code']!="")
			{
				$product_code=str_replace("*","%",$_GET['product_code']);
			} 
			else
			{$product_code="%";}
		}
		else
		{$product_code="%";}
	
	
		if (isset($_GET['title']))
		{
			if ($_GET['title']!="")
			{
				$title=str_replace("*","%",$_GET['title']);
			} 
			else
			{$title="%";}
		}
		else
		{$title="%";}
	
	
		if (isset($_GET['alias']))
		{
			if ($_GET['alias']!="")
			{
				$alias=str_replace("*","%",$_GET['alias']);
			} 
			else
			{$alias="%";}
		}
		else
		{$alias="%";}
	
		if (isset($_GET['cust_drawing']))
		{
			if ($_GET['cust_drawing']!="")
			{
				$cust_drawing=str_replace("*","%",$_GET['cust_drawing']);
			} 
			else
			{$cust_drawing="%";}
		}
		else
		{$cust_drawing="%";}
	
		// -----------------
		
		
		// NOMBRE DE RESULTATS AFFICHES PAR PAGE 
		// include('../PN_Connexion_PN.PHP');
		// $query_item_per_page = 'SELECT Value FROM tbl_parameters WHERE Parameter like "Nb_Item_Per_Page"';
		// $resultat_1 = $mysqli->query($query_item_per_page);
		// while ($row = $resultat_1->fetch_assoc())
		// {
			// $limit_val=$row['Value'];
		// }
		// $mysqli->close();
		// -----------------
		
		// HAUTEUR IFRAME DE RESULTAT
		// 20 : hauteur de ligne tableau
		// 4 : marge d'espacement
		$limit_val=$_GET['limit_val'];
		$h_item_list_iframe=(20 * ($limit_val + 1) + 4)."px";
		$h_item_detail_iframe= '100vh - '.$h_item_list_iframe.' - 20px - 45px';

	?>

    <!--<form enctype="multipart/form-data" action="" method="post">-->

        <table id="t01" border=0>
            <tr>
				<td>
					<div id="detail_page_title">
						Resultat de la recherche
					</div>
				</td>
				
				<input type="image" onclick="excel_extraction()" title="Extraction Excel du resultat complet de la recherche" src="\Common_Resources\Excel_icon.png" style="position:absolute;z-index:99;right:18px;top:14px;height:16px;cursor:pointer"></input>
				<span id="excel_extract_feedback" style="text-decoration: none; font-style:italic;font-size:11px;position:absolute;z-index:99;right:42px;top:16px;height:20px;cursor:pointer"></span>
			</tr>
			<tr>
				<td style="text-align:center;padding-top:3px;">
					<iframe 
						name="Item_List_Frame" 
						id="Item_List_Frame"
						class="Item_List_Frame" 
						alt="ok" 
						src="PN_Search_Result_Item_List.php?
							start=0
							&nb=<?php echo $limit_val ?>
							&ref=<?php echo $_GET['ref'] ?>
							&draw=<?php echo $_GET['draw'] ?>
							&certif=<?php echo $_GET['certif'] ?>
							&division=<?php echo $_GET['division'] ?>
							&product_code=<?php echo $_GET['product_code'] ?>
							&title=<?php echo $_GET['title'] ?>
							&cust_drawing=<?php echo $_GET['cust_drawing']?>
							&alias=<?php echo $_GET['alias']?>
							"
						style="height:<?php echo $h_item_list_iframe ?>;width:100%"
						frameborder="1"
						scrolling="no">
					</iframe>
				</td>
			</tr>
			<tr>
				<td style="padding-top:-4px;">
					<table width=100% id="page_table" border=0>
						<tr>
								<?php
								$query_1 = 'SELECT
											  *
											FROM  tbl_pn
											WHERE 
												 Reference like "'.$ref.'" 
											 AND Prod_Draw like "'.$draw.'"
											 AND Certif like "'.$certif.'"
											 AND Division like "'.$division.'"
											 AND Product_Code like "'.$product_code.'"
											 AND Alias like "'.$alias.'"
											 AND Cust_Drawing like "'.$cust_drawing.'"
											 AND (
												   Ref_Title_FRA like "'.$title.'" OR Ref_Title_EN like "'.$title.'" 
												  )
											 GROUP BY Reference, Prod_Draw
											 ORDER BY Reference DESC, Ref_Rev DESC, Prod_Draw DESC, Prod_Draw_Rev DESC
												';

								include('../PN_Connexion_PN.PHP');
								$resultat = $mysqli_pn->query($query_1);
								$rowcount = mysqli_num_rows($resultat);
								if (($rowcount/$limit_val)!=floor($rowcount/$limit_val))
								{
									$nb_page=ceil($rowcount/$limit_val); // CALCUL DU NOMBRE DE PAGES EN FAISANT L'ARRONDI SUPERIEUR
								} else {
									$nb_page=$rowcount/$limit_val;
								}
								// MISE EN PAGE POUR AFFFICHAGE DE CHAQUE PAGE - REMPLACER PAR PARCOURS PAR FLECHE NEXT/PREVIOUS
								// $i=1;
								// do {
									// if ($i==1)
									// {
										// $class_val="picked_page";
									// } else {
										// $class_val="unpicked_page";
									// }
									// echo '<td style="text-align:center" name="page_td" id="Page_'.$i.'" onclick="selected_page(this,'.$i.')" class="'.$class_val.'">
										  // <a href="PN_Search_Result_Item_List.php?start='.($i-1)*$limit_val.'
												// &nb='.$limit_val.'
												// &ref='.$_GET['ref'].'
												// &draw='.$_GET['draw'].'
												// &certif='.$_GET['certif'].'
												// &division='.$_GET['division'].'
												// &product_code='.$_GET['product_code'].'
												// &title='.$_GET['title'].'
												// "
											// target="Item_List_Frame"
											// style="text-align:center;text-decoration:none;color:black"
											// >'.$i.'</a></td>';
									// $i=$i+1;
								// } while ($i<=$nb_page);
								// $mysqli->close();
								
							?>
							</td>
							
							<td style="padding-left:5px;width:40px">Page: </td>
							<td style="width:20px;font-size:7pt;padding-top:3px;text-align:right" name="previous_page_td">
							
							</td>
							<td style="width:20px;text-align:center;font-weight:550" name="current_page_td">
							1
							</td>
							<td style="width:20px;font-size:7pt;padding-top:3px;text-align:left" name="next_page_td">
							<?php if($rowcount>$limit_val){echo "2";} else { echo "";} ?>
							</td>
							<td style="width:30px">
								<input type="image" src="\Common_Resources\full_arrow_icon.png" style="padding-top:3px; height:12px;cursor:pointer"  onclick="page_change(-2)"</input>
								<input type="image" src="\Common_Resources\arrow_icon.png" style="padding-bottom:3px; height:12px; transform: rotate(180deg);cursor:pointer" onclick="page_change(-1)"</input>
							</td>
							<td style="width:30px">
								<input type="image" src="\Common_Resources\arrow_icon.png" style="padding-top:3px; height:12px;cursor:pointer"  onclick="page_change(1)"></input>
								<input type="image" src="\Common_Resources\full_arrow_icon.png" style="padding-bottom:3px; height:12px; transform: rotate(180deg);cursor:pointer"  onclick="page_change(2)"></input>
							</td>
							
							<td style="width:285px" name="total_record" style=""> - Total : <?php  echo $rowcount ?> resultats | <?php  echo $nb_page ?> pages</td>
							<!--<td name="total_nb_page" style=""> - </td>
							    <td name="nb_per_page" style="">-  resultats par page</td>-->
							<td style="text-align:right">
								<select onchange="nb_record_per_page_update()" name="limi_page_name" id="limit_page_id" style="height:17px; font-size:8.5pt;text-align;center;vertical-align:middle" >
								<?php 
								
								$max_nb=20;
								if($rowcount<=$max_nb)
								{
									$max_nb=$rowcount;
								}
								$in=0;
								for ($i=1; $i<=$max_nb; $i++)
								{
									if ($i==$limit_val)
									{
										$sel="SELECTED";
										$in=1;
									} else {
										$sel="";
									}
									if($i>15)
									{
										$i=$i+4;
									}
									if ($in==0 && $i==$max_nb)
									{
										$sel="SELECTED";
									}
									
									echo '<option value="'.$i.'" '.$sel.'>'. $i .'</option>';
								}
								?>
								</select>
								&nbspresultats par page
							</td>
							<input HIDDEN id="ref" style="width:20px" type=text value="<?php  echo $_GET['ref'] ?>" enabled>
							<input HIDDEN id="draw" style="width:20px"  type=text value="<?php  echo $_GET['draw'] ?>" enabled>
							<input HIDDEN id="certif" style="width:20px"  type=text value="<?php  echo $_GET['certif'] ?>" enabled>
							<input HIDDEN id="division" style="width:20px"  type=text value="<?php  echo $_GET['division'] ?>" enabled>
							<input HIDDEN id="product_code" style="width:20px"  type=text value="<?php  echo $_GET['product_code'] ?>" enabled>
							<input HIDDEN id="title" style="width:20px"  type=text value="<?php  echo $_GET['title'] ?>" enabled>
							<input HIDDEN id="alias" style="width:20px"  type=text value="<?php  echo $_GET['alias'] ?>" enabled>
							<input HIDDEN id="cust_drawing" style="width:20px"  type=text value="<?php  echo $_GET['cust_drawing'] ?>" enabled>
							
							
								
							
						</tr>
					</table>		
				</td>
			</tr>
			<tr>
				<td>
				
					<iframe 
						name="Item_Details_Frame" 
						id="Item_Details_Frame_id"
						class="Item_Details_Frame" 
						alt="ok" 
						src="PN_Search_Result_Item_Details.php?
							ID=
							&ref
							&ref_rev=
							&prod_draw=
							&prod_draw_rev=
							&division=
							&product_code=
							&certif=
							&alias=
							"
							
						style="height:calc(<?php echo $h_item_detail_iframe ?> );width:100%"
						frameborder="0"
						scrolling="no">
					</iframe>
				
				</td>
			</tr>
		</table> 
    <!--</form>-->
</body>

</html>