body {
  color: black;
  font-size: 9pt;
  font-family: <PERSON>homa, sans-serif;
  font-weight: normal;
}

@font-face {
	font-family: "Conneqt";
	src: url("/Common_Resources/font_Conneqt.otf");
}


div#FilterTitle {
  text-indent: 5%;
  font-size: 9pt;
  font-weight: normal;
  font-family: Tahoma, sans-serif;
  margin-top: 10px;
  margin-bottom: 2px;
  text-align: justify;
}

div#News_Title {
  color: #1a5276;
  font-family: Trattatello, fantasy;
  margin-top: 10%;
  text-align: center;
  font-size: 16pt;
  font-weight: normal;
}

div#sidepanel_left{
   width:11%;
   transition: all 0.4s;
   display:block;
border-right:0.25px solid #E4E4E4
   /*box-shadow: 0 4px 4px 4px rgba(0, 0, 0, 0.4), 0 6px 20px 10px rgba(0, 0, 0, 0.19);*/
}

.input_welcome{
   font-size:12px;
   height:20px;
   margin-left:5%;
   width:90%;
   text-align:center;
   box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.2), 0 6px 6px 0 rgba(0, 0, 0, 0.19);
  border-radius:5px;
}

.select_welcome{
   margin-left:8px;
   width:55px;
   font-size:11px;
   height:20px;
   box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
  border-radius:5px;
}

.btn_welcome{
padding: 3px 3px;
border-radius:10px;
border: none;
color: white;
font-family:Arial black, sans-serif;
font-size:12px;
font-weight:bold;
vertical-align:middle;
text-align: center;
text-decoration:none;
width:75%;
height:30px;
box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.4), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
cursor:pointer;
}


.grey {
    background-color: #cccbcb; 
    color: black;
}

.grey:hover {
   background: rgb(151, 149, 149);
}

.blue {
   background-color: #2196F3;
} 
.blue:hover {
background: #0b7dda;
}



div#bottom_icon{
  bottom:0%;
  z-index:99;
  position:absolute;
  margin-bottom:70px;
  left:10px;
}

.title_expanded {
  margin-left: 28px;
}

.title_skrinked {
  margin-left: 0px;
}

span#version {
  position: absolute;
  bottom: 0;
  font-size: 80pt;
  color: grey;
  left: 0;
}

div#detail_page_title {
  font-weight: 550;
  font-variant: small-caps;
  font-size: 10pt;
  margin-top: 5px;
  margin-bottom: 2px;
}

/* FRAME PRINCIPALE DE LA PAGE - PN_WELCOME */
/* ---------------------------------------- */
.main_frame_reduced {
  position: absolute;
  z-index: 1;
  top: 58px;
  left: calc(11% + 20px);
  width: calc(100% - 11%);
  height: calc(100% - 58px - 4px);
  margin-left: -15px;
  background-color: transparent;
  transition: all 0.5s;
}

.main_frame_expanded {
  position: absolute;
  z-index: 1;
  top: 58px;
  left: 20px;
  width: calc(100% - 10px);
  height: calc(100% - 58px - 4px);
  margin-left: -15px;
  background-color: transparent;
  transition: all 0.5s;
}

/* FRAME INTERNE AFFICHANT LES RESULTATS DE LA RECHERCHE - PN_SEARCH_RESULT */
/* ------------------------------------------------------------------------ */
.Item_List_Frame {
  width: 100%;
}

/* FRAME INTERNE AFFICHANT LES DETAILS DE LA RECHERCHE - PN_SEARCH_RESULT */
/* ---------------------------------------------------------------------- */
.Item_Details_Frame {
  height: calc(100vh - 5px);
}

/* FRAME AFFICHANT LE PLAN EN PREVIEW - PN_SEARCH_RESULT_ITEM_DETAIL */
/* ----------------------------------------------------------------- */
.pdf_preview_frame {
  height: calc(100vh - 8px);
}

/* BOUTON AVEC IMAGE */
/* ----------------- */
input[type="image"]:hover {
  border-bottom: 1px solid #bbbbbb;
  border-right: 1px solid #bbbbbb;
  margin-top: -1px;
  transform: scale(1.15);

  /*box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);*/
}

.img_print:hover {
  border-bottom: 1px solid #bbbbbb;
  border-right: 1px solid #bbbbbb;
  margin-top: -1px;
  transform: scale(1.15);
}

input[type="image"]:active {
  border-bottom: 1px solid #bbbbbb;
  border-right: 1px solid #bbbbbb;
  border-left: 1px solid #bbbbbb;
  border-top: 1px solid #bbbbbb;
  margin-top: -1px;
  transform: scale(0.95);
}

/*MISE EN LUMIERE PAGE DE RESULTATS SELECTIONNEE - PN_SEARCH_RESULT */
/*----------------------------------------------------------------- */
.picked_page {
  background-color: #c3dbff;
  border-top: 1px #0066ff solid;
  border-bottom: 1px #0066ff solid;
  border-left: 1px #0066ff solid;
  border-right: 1px #0066ff solid;
  padding-right: 1px;
  padding-bottom: 3px;
  text-shadow: 1px 1px 2px grey;
  z-index: 99;
  border-radius: 4px;
  width: 10px;
  text-align: center;
  color: white;
  font-weight: 550;
}

.unpicked_page {
  background-color: white;
}

/*MISE EN LUMIERE LIGNE DU TABLEAU DE RESULTATS SELECTIONNEE - PN_SEARCH_RESULT_ITEM_LIST */
/*--------------------------------------------------------------------------------------- */
.picked_line {
  background-color: #bbcae9; /*#C8CAD5;*/
  border: 1.5px solid grey;
  font-weight: 550;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}

.unpicked_line {
  background-color: transparent;
  border-top: 0.5px solid black;
  border-bottom: 0.5px solid black;
}

/*TABLEAU PAGINATION RECHERCHE - PN_SEARCH_RESULT */
/*------------------------------------------------ */
#page_table {
  margin-top: -5px;
  height: 20px;
  vertical-align: top;
}

/*TABLEAU RESULTAT RECHERCHE - PN_SEARCH_RESULT */
/*--------------------------------------------- */
#t01 {
  width: calc(100% - 10px);
  border-collapse: collapse;
  vertical-align: middle;
}

#t01 th {
  border: 1px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
}

#t01 td {
  text-align: left;
  vertical-align: middle;
}

#t01 tr {
  height: 20px;
}

/* TABLEAU PRINCIPAL DE LA PAGE PN_SEARCH_RESULT_ITEM_DETAIL */
/* --------------------------------------------------------- */
#t02 {
  margin-left: -6px;
  margin-top: -7px;
  border-collapse: collapse;
  vertical-align: top;
  width: calc(100vw - 3px);
  font-size: 8.5pt;
  text-align: left;
}

#t02 td {
  vertical-align: top;
}

/* TABLEAU DETAIL - PN_SEARCH_RESULT_ITEM_DETAIL */
/* --------------------------------------------- */
#t03 {
  border-collapse: collapse;
  width: calc(100%);
  font-size: 8.5pt;
  text-align: left;
}

#t03 th {
  background-color: rgba(244, 244, 244, 0.45);
  font-weight: 520;
}

#t03 td {
  vertical-align: middle;
}

#t03 tr {
  height: 20px;
}

/*TABLEAU DE RESULTATS - PN_SEARCH_RESULT_ITEM_LIST */
/*------------------------------------------------- */
#t04 {
  border-collapse: collapse;
  width: calc(100vw - 2px);
  overflow-y: auto;
  margin-top: -7px;
  margin-left: -7px;
  font-size: 8.5pt;
}

#t04 th {
  border: 0.5px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
  font-family: Arial Helvetica, sans-serif;
  text-align: center;
}

#t04 td {
  text-align: center;
  vertical-align: middle;
  border-right: 0.5px solid black;
  border-left: 0.5px solid black;
  border-top: 0.5px solid black;
  border-bottom: 0.5px solid black;
}

#t04 tr {
  height: 20px;
}

#t04 tr:hover {
  background-color: rgb(76, 126, 160);
  color: white;
  cursor: pointer;
}

/*TABLEAU HISTORIQUE PLAN - PN_SEARCH_RESULT_ITEM_DETAIL */
/*------------------------------------------------------ */
#t05 {
  border-collapse: collapse;
  width: calc(100% - 10px);
  font-size: 8.5pt;
  margin-top: 8px;
}

#t05 th {
  border-bottom: 0.5px solid black;
  background-color: transparent;
  font-family: Arial Helvetica, sans-serif;
  font-size: 8pt;
  text-align: center;
}

#t05 td {
  text-align: center;
  vertical-align: middle;
}

#t05 tr {
  height: 15px;
}

/*TABLEAU DE RECHERCHE UTILISATEUR - PN_WELCOME */
/*--------------------------------------------- */
#search_hist_table {
  margin-left: 18px;
  margin-top: 20px;
  width: calc(100% - 35px);
  font-size: 7.5pt;
  border-collapse: separate;
  border-radius: 3px;
  background-color: #e2e2e2;
}

#search_hist_table th {
  box-shadow: 0;
  border-bottom: 0.5px solid white;
  background-color: #f4f4f4;
}

#search_hist_table th:hover {
  cursor: default;
  background-color: #f4f4f4;
}

#search_hist_table td {
  text-align: center;
}

#search_hist_table tr {
  height: 15px;
  box-shadow: 0 6px 20px 0 rgba(0, 0, 0, 0.09);
}

#search_hist_table tr:hover {
  background-color: #f4f4f4;
  cursor: pointer;
}

/* Champs modifiable dans PN_Search_Result_Item_Details */
/*------------------*/
.Input_Not_Click {
  font-family: Tahoma, sans-serif;
  font-size: 8.5pt;
  background-color: transparent;
  color: black;
  border: none;
  outline: none;
}

.Input_Btn {
  font-family: Tahoma, sans-serif;
  font-size: 8.5pt;
  background-color: transparent;
  border: 1px grey solid;
}

.Non_Visible {
  display: none;
}

/* ---------------------------------------------- */