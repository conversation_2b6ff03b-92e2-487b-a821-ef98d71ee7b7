<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="PN_Styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<script>
	
</script>	




</head>

<title>
    Registre Ref & Plan SCM
</title>

<body>
	<?php 
		// PARAMETRES A DEFINIR
		// --------------------
		$origin = date_create('2023-03-10');
		$duration = 14;
		// --------------------
		
		
		$target = date_create(date('Y-m-j'));
		$interval = date_diff($origin, $target);
		$array = array("Jan<PERSON>", "Fév<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Aout", "Septembre", "Octobre", "Novembre", "Décembre");
		if ($interval->format('%R%a')>=0 && $interval->format('%R%a')<=$duration ) // AFFICHE LE MESSAGE DURANT LA DUREE DEFINITI EN PARAMETRE $duration APRES LA DATE DE DEBUT DEFINIT EN PARAMETRE $origin
		{ 
			echo '<div id="News_Title">
				<font style="text-decoration:none">		
					Nouveauté depuis le '.date_format($origin, 'd').' '.$array[intval(date_format($origin, 'm'))-1].' '.date_format($origin, 'Y').'
					</font>
				<br>
				<br>
				<font style="color:#393939">
					L\'ensemble des articles présents dans SAP et leurs plans associés sont maintenant disponibles.
				</font>
				</div>';
				
		}
	?>
	
		
</body>

</html>