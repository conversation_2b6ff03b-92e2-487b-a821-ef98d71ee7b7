<!DOCTYPE html>
<html>


<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<meta http-equiv='cache-control' content='no-cache'>
<meta http-equiv='expires' content='0'>
<meta http-equiv='pragma' content='no-cache'>

<link rel="stylesheet" type="text/css" href="PN_Styles.css">
<link rel="stylesheet" type="text/css" href="PN_vertical_tab.css">
<!--<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">-->
<link rel="icon" type="image/png" href="\Common_Resources\engineering_icon.png" />


<head>
	<script>
	
		// VARIABLES GLOBALES SCRIPT
		// -------------------------
		var historic_content=[];
		var historic_content_head='<th colspan=2 style="border-bottom:0.5px solid white">Historique Recherche</th>';
		var criteria_available_list=['Search_Draw', 'Search_Ref', 'Search_certif' , 'Search_divison' , 'Search_product_code' , 'Search_Title' , 'limit_val_id' , 'Search_Cust_Drawing' , 'Search_Alias'];
		var criteria_available_clean_name=['Plan', 'Référence Article', 'Certification' , 'Division' , 'Code Produit' , 'Titre' , 'Nbre resultat' , 'Plan Client' , 'Référence Commerciale'];
		var maintenance=1;
		// -------------------------
		
		
		// DETECTION DE L'EVENEMENT DE REDIMENSIONNEMENT DE LA FENETRE 
		// -----------------------------------------------------------
		window.onresize = handle;
		function handle(){
			historique_table_update();
			return true;
		}
		// -----------------------------------------------------------
		
		
		// AFFICHAGE DE LA TABLE HISTOTIQUE ADAPTEE A LA TAILLE DE LA FENETRE
		// ------------------------------------------------------------------
		function historique_table_update()
		{

			var updated_table_val=historic_content_head;
			if (window.innerHeight < 690)
			{
				var histo=1;
				} else  if (window.innerHeight < 720)
					{
						var histo=3;
					} else  if (window.innerHeight < 780)
						{
							var histo=5;
							} else  if (window.innerHeight < 830)
								{
								var histo=8;
								} else  if (window.innerHeight < 860)
								{
								var histo=10;
									} else if (window.innerHeight < 910)
										{
											var histo=12;
											}  else {
													var histo=15;
												}

			if (historic_content.length>=1)
			{
				for (let i = 0 ; i <histo && i<=historic_content.length-1 ; i++)
				{
					updated_table_val=updated_table_val + historic_content[i];
				}
				// AFFICHAGE DU TABLEAU D'HISTORIQUE
				document.getElementById("search_hist_table").innerHTML=updated_table_val;
			}
			
		}
		// ------------------------------------------------------------------
		
		
		// MISE A JOUR DE LA DESCRIPTION DU CODE PRODUIT CHOISI
		// -----------------------------------------------------
		function Product_Code_Description_Auto() 
		{
		  
		  const xhttp = new XMLHttpRequest();
		  xhttp.onload = function() 
		  {
			const raw_result=this.responseText.trim();
			document.getElementById("product_code_descritpion").value=raw_result;
		  }
		  
		  xhttp.open("GET", "PN_Product_Code_Auto.php?code="+document.getElementById("Search_product_code").value);
		  xhttp.send();
		}
		// ----------------------------------------------------------
		
		// MISE EN FORME DE CHIFFRE A 2 DIGITS
		// -----------------------------------
		function two_digits(val)
		{
			if (val<10)
			{
				final_val='0' + val;
			} else {
				final_val=val;	
			}	
			return final_val;
		}
		// ------------------------------------
		
		// LANCEMENT DE LA RECHERCHE AVEC LES PARAMETRES DE RECHERCHE DE L'HISTORIQUE
		// --------------------------------------------------------------------------
		function search_hist_trigger(obj)
		{
			document.getElementById("Search_Draw").value=obj.cells[2].textContent.trim();
			document.getElementById("Search_Ref").value=obj.cells[3].textContent.trim();
			document.getElementById("Search_certif").value=obj.cells[4].textContent.trim();
			document.getElementById("Search_divison").value=obj.cells[5].textContent.trim();			
			document.getElementById("Search_product_code").value=obj.cells[6].textContent.trim();
			document.getElementById("Search_Title").value=obj.cells[7].textContent.trim();
			document.getElementById("limit_val_id").value=obj.cells[8].textContent.trim();
			document.getElementById("Search_Cust_Drawing").value=obj.cells[9].textContent.trim();
			document.getElementById("Search_Alias").value=obj.cells[10].textContent.trim();
			search_start(2);
		}
		// --------------------------------------------------------------------------
		
		// HISTO UPDATE
		// ------------
		function hist_update()
		{
			var draw=document.getElementById("Search_Draw").value;
			var ref=document.getElementById("Search_Ref").value;
			var certif=document.getElementById("Search_certif").value;
			var division=document.getElementById("Search_divison").value;
			var product_code=document.getElementById("Search_product_code").value;
			var title=document.getElementById("Search_Title").value;
			var limit_val=document.getElementById("limit_val_id").value;
			var alias_val=document.getElementById("Search_Alias").value;
			var cust_drawing_val=document.getElementById("Search_Cust_Drawing").value;
			
			// MISE EN FORME DE LA DATE POUR UTILISATION EN NOM DE CHAQUE RECHERCHE DANS L'HISTORIQUE
			var today = new Date();
			var val=today.getMonth()+1;
			var m_val=two_digits(val);
			var t_val=two_digits(today.getDate());
			var min_val=two_digits(today.getMinutes());
			var s_val=two_digits(today.getSeconds());
				
			// ENREGISTREMENT DES CRITERES
			var applied_criteria_list="";
			for (let k=0; k<criteria_available_list.length; k++)
			{
				var sep="";
				if (k>0)
				{
					var l=k-1;
				} else {
					var l=0;
				}
				if (applied_criteria_list!="")
				{
					var sep=" \n -  ";
				}
				if (k!=6 && document.getElementById(criteria_available_list[k]).value!="" && document.getElementById(criteria_available_list[k]).value!="*" && document.getElementById(criteria_available_list[k]).value!="%")
				{
					var field_name=criteria_available_clean_name[k];
					applied_criteria_list=applied_criteria_list + sep + field_name + ' : ' + document.getElementById(criteria_available_list[k]).value;
				}
				
			}
			if (applied_criteria_list=="")
			{
				var applied_search_crit="Aucun critére";
			} else {
				var applied_search_crit="Critère(s) appliqué(s) : " + sep + applied_criteria_list;
			}
			
			//  var search_crit=m_val + '-' + t_val + ' @ ' +  today.getHours() + ':' + min_val+ ':' + s_val;
			 
			// CHANGEMENT DU CONTENU D'AFFICHAGE DANS L'HISTORIQUE
			 // ---------------------------------------------------
			var search_crit="";
			if (ref!="")
			{
				var search_crit=ref;
				if (draw!="")
				{
					if (search_crit!="")
					{
						search_crit = search_crit+ " - ";
					}
					search_crit = search_crit + draw;
				}
			} else if (draw!="")
				{
					var search_crit=draw;
				} else {
					var search_crit=m_val + '-' + t_val + ' @ ' +  today.getHours() + ':' + min_val+ ':' + s_val;
				}
				// ---------------------------------------------------
					
					
			
			// CREATION DE LA LIGNE D'HISTORIQUE
			id_search=historic_content.length+1;
			var new_element_val='<tr onclick="search_hist_trigger(this)">' + 
								'<td style="width:20%">' + id_search + '</td>' + 
								'<td  title="' + applied_search_crit + '">' + search_crit + '</td>' + 
								'<td hidden>' + draw + '</td>' + 
								'<td hidden>' + ref + '</td>' + 
								'<td hidden>' + certif + '</td>' + 
								'<td hidden>' + division + '</td>' + 
								'<td hidden>' + product_code + '</td>' + 
								'<td hidden>' + title + '</td>' + 
								'<td hidden>' + limit_val + '</td>' + 
								'<td hidden>' + cust_drawing_val + '</td>' + 
								'<td hidden>' + alias_val + '</td>' + 
								'</tr>';

			// INTEGRATION DE LA NOUVELLE LIGNE DANS LA VARIABLE GLOBALE
			historic_content.unshift(new_element_val);
			
			
			// AFFICHAGE DE L'HISTORIQUE
			historique_table_update();
		}
		// ------------
	
		// EXECUTION DE LA RECHERCHE 
		// -------------------------
		function search_start(toggle_val)
		{
			var draw=document.getElementById("Search_Draw").value;
			var ref=document.getElementById("Search_Ref").value;
			var certif=document.getElementById("Search_certif").value;
			var division=document.getElementById("Search_divison").value;
			var product_code=document.getElementById("Search_product_code").value;
			var title=document.getElementById("Search_Title").value;
			var limit_val=document.getElementById("limit_val_id").value;
			var alias_val=document.getElementById("Search_Alias").value;
			var cust_drawing_val=document.getElementById("Search_Cust_Drawing").value;
	
			document.getElementById("main_frame_id").src='PN_Search_Results.php?draw=' + draw + '&ref=' + ref + '&certif=' + certif + '&division=' + division + '&product_code=' + product_code + '&title=' + title + '&limit_val=' + limit_val + '&alias=' + alias_val + '&cust_drawing=' + cust_drawing_val ;
			
			if(toggle_val==1)
			{
				banner_toggle_action();
				hist_update();
			} else if (toggle_val>1)
			{
				banner_toggle_action();
			}
		}
		// -------------------------

		// SUPPRESSION DES CRITERES DE RECHERCHE ET MISE A ZERO DU FORMULAIRE
		// ------------------------------------------------------------------
		function reset_search()
		{
			document.getElementById("Search_Draw").value="";
			document.getElementById("Search_Ref").value="";
			document.getElementById("Search_certif").value="";
			document.getElementById("Search_divison").value="";
			document.getElementById("Search_product_code").value="";
			document.getElementById("Search_Title").value="";
			document.getElementById("Search_Cust_Drawing").value="";
			document.getElementById("Search_Alias").value="";
		}
		// ------------------------------------------------------------------
		
		// TEMPORISATION DE L'APPARITION DE LA BANNIERE DE RECHERCHER APRES 250ms
		// -----------------------------------------------------------------------
		function banner_toggle_regul()
		{
			if(document.getElementById("sidepanel_left").style.width=="0%") // SI DEJA REDUIT
			{
				setTimeout(banner_toggle_action, 250); // Temporisation pour faire apparaitre la banniere de recherche
			} else {
				banner_toggle_action();
			}
		}
		// -----------------------------------------------------------------------
		
		// EXTENSION / REDUCTION DE LA BANNIERE DE RECHERCHE 
		// -------------------------------------------------
		function banner_toggle_action()
		{
			if(document.getElementById("left_panel_lock_id").className=="left_panel_slider")
			{
				if(document.getElementById("sidepanel_left").style.width=="0%") // SI BANDEAU DE RECHERCHER DEJA REDUIT
				{
					document.getElementById("sidepanel_left").style.width="11%";
					document.getElementById("main_frame_id").setAttribute("class","main_frame_reduced");
					document.getElementById("banner_toggle_btn_id").style.display="none";
					
					document.getElementById("title_banner").setAttribute("class","title_skrinked");
					
				} else {														// SI BANDEAU DE RECHERCHER DEJA ETENDU
					document.getElementById("sidepanel_left").style.width="0%";
					document.getElementById("main_frame_id").setAttribute("class","main_frame_expanded");
					document.getElementById("banner_toggle_btn_id").style.display="block";
					
					document.getElementById("title_banner").setAttribute("class","title_expanded");
				}
			}
		}
		// -------------------------------------------------
		
		// VERROUILLAGE DE LA RETRACTATION DU PANNEAU DE RECHERCHE
		// -------------------------------------------------------
		function left_panel_toogle()
		{
			var left_banner_locked_result=left_banner_locked();
			 if(left_banner_locked_result==0)
			 {
				document.getElementById("left_panel_lock_id").setAttribute("class","left_panel_locked");
				document.getElementById("left_panel_lock_id").src='/Common_Resources/pin_blue.png';
				document.getElementById("left_panel_lock_id").title="Panneau Figé - Click = Activer la retraction auto";
			 } else {
				document.getElementById("left_panel_lock_id").setAttribute("class","left_panel_slider");
				document.getElementById("left_panel_lock_id").src='/Common_Resources/pin_grey.png';
				document.getElementById("left_panel_lock_id").title="Rétractation Auto Activée - Click = Figer le panneau de recherche";
				
				let src_frame=document.getElementById("main_frame_id").src;
				if (src_frame.indexOf("=")!=-1)
				{
					banner_toggle_action();
				}
			 }
		}
		// -------------------------------------------------------
		
		// VERIFICATION DU VERROUILLLAGE DU PANNEAU DE RECHERCHE
		// -----------------------------------------------------
		function left_banner_locked()
		{
			if (document.getElementById("left_panel_lock_id").className=="left_panel_slider")
			{
				return 0;
			} else {
				return 1;
			}
		}
		//------------------------------------------------------

		// VERIFICATION DU PASSWORD RENTRE POUR ALLER DANS LA PAGE ADMINISTRATION
        // ----------------------------------------------------------------------
        function Admin_PWD_Check()
        {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function()
            {
				var mdp=document.getElementById("pass_input").value;
                //var mdp=prompt("Password","");
                if (mdp!="")
                {
                    pwd= this.responseText.trim();
                    if (mdp == pwd)
                    {
                        const url="PN_Admin_Welcome.php";
						document.getElementById("pass_input").value="";
						document.getElementById("pass_zone").hidden=true;
						document.getElementById("admin_pass_button").hidden=false;
						document.getElementById("pass_input").style.background="transparent";
                        window.open(url);
                    } else if (mdp!="" && mdp!=null)
                    {
						document.getElementById("pass_input").focus();
						document.getElementById("pass_input").style.background="#F5B7B1";
                    } else {
                    }
                }
            }
            xhttp.open("GET", "PN_Admin_PWD.php");
            xhttp.send();
        }
		
		
		function pass_display()
		{
			if (document.getElementById("pass_input")!="")
			{
				document.getElementById("pass_zone").hidden=false;
				document.getElementById("admin_pass_button").hidden=true;
				document.getElementById("pass_input").focus();
			}
		}
		
		function password_field_toggle()
		{
			if(document.getElementById("pass_zone").hidden==false)
			{
				document.getElementById("pass_zone").hidden=true;
				document.getElementById("admin_pass_button").hidden=false;
				document.getElementById("pass_zone").hidden=true;
			}
		}
		// ----------------------------------------------------------------------
		
	</script>
	
	<?php 
	// NOMBRE DE RESULTATS AFFICHES PAR PAGE
	// ------------------
	include('../PN_Connexion_PN.PHP');
	$query_item_per_page = 'SELECT Value FROM tbl_parameters WHERE Parameter like "Nb_Item_Per_Page"';
	$resultat_1 = $mysqli_pn->query($query_item_per_page);
	while ($row = $resultat_1->fetch_assoc())
	{
		$limit_val=$row['Value'];
	}
	$mysqli_pn->close();
	// -----------------
	?>
	
<?php
include('../Common_Resources/Tool_Banner_Deco_Period.php');
?>
	
	
</head>


	<title>V2D</title>

	
	<body>
		
		<?php  
		if ($interval_noel->format('%R%a')>=0 && $interval_noel->format('%R%a')<=$duration_noel ) // AFFICHE BANNIERE NOEL PENDANT LES $DURATION JOURS AVANT $DATE_NOEL
		{
			echo '<div class="w3-container w3-blue-xmas"><h3><div style="font-variant:small-caps;z-index:2" class="title_skrinked" id="title_banner" ><font style="font-family:arial;letter-spacing: 3px;">V2D</font> - Visualisation des Documents Diffusés</div></h3></div>';
			echo '<img src="\Common_Resources\Top_Banner_Xmas.png"  height="46px" style="image: repeat-x;position: absolute;z-index:2;opacity: 0.6; top:0px; left:0px; filter:grayscale(20%)" >';
		} elseif ($interval_hiver->format('%R%a')>0 && $interval_hiver->format('%R%a')<=$duration_hiver ) // AFFICHE BANNIERE HIVER PENDANT LES $DURATION JOURS APRES LA $DATE_HIVER
		{	
			echo '<div class="w3-container w3-blue-scm3"><h3><div style="font-variant:small-caps;z-index:2" class="title_skrinked" id="title_banner" ><font style="font-family:arial;letter-spacing: 3px;">V2D</font> - Visualisation des Documents Diffusés</div></h3></div>';
			echo '<img src="\Common_Resources\snow02.gif"  style="height:100%;image: repeat-x;position: absolute;z-index:0;opacity: 0.6; top:0px; right:0px; filter:grayscale(20%)" >';
		} elseif ($interval_24_auto->format('%R%a')>0 && $interval_24_auto->format('%R%a')<=$duration_24_auto ) // AFFICHE BANNIERE 24H PENDANT LES $DURATION JOURS APRES LA $DATE_24H
		{
			echo '<div class="w3-container w3-blue-scm3"><h3><div style="font-variant:small-caps;z-index:2" class="title_skrinked" id="title_banner" ><font style="letter-spacing: 3px;font-family:Conneqt, sans-serif;"><b>DMO</b></font> - <b>D</b>emande de <b>MO</b>dification</div></h3></div>';
			echo '<img src="\Common_Resources\24H_car.gif"  style="width:560px;repeat:repeat-x;position: absolute;z-index:98;opacity: 0.7;top:-142px; right:130px; filter:grayscale(40%)"/ >';
			echo '<img src="\Common_Resources\24H_Michelin.gif"  style="height:62px;repeat:repeat-x;position: absolute;z-index:99;opacity: 0.7; top:-2px; right:655px; filter:grayscale(60%)"/ >';
			echo '<img src="\Common_Resources\24h_Track.gif"  style=" width:31px;repeat:repeat-x;position: absolute;z-index:97;opacity: 0.7; top:0px; right:120px;transform: rotate(12deg);filter:grayscale(90%)"/ >';
			echo '<img src="\Common_Resources\Checkered_Flag.jpg"  style="repeat:repeat-x;position: absolute;z-index:98;opacity: 0.2; width:calc(11% + 5px);max-width:180px;bottom:0px; left:0px; filter:grayscale(60%)"/ >';
		} else {
			echo '<div class="w3-container w3-blue-scm3"><h3><div style="font-variant:small-caps;" class="title_skrinked" id="title_banner" ><font style="font-family:arial;letter-spacing: 3px;">V2D</font> - Visualisation des Documents Diffusés</div></h3></div>';
		}
		?>
		<!------------------>

		
		
		
		<a href="PN_Welcome.php">
			<img src="\Common_Resources\Logo_SCM_2024_G_W.png" height="52px" style="z-index:99;position: absolute; top:2px; right:4px; filter:grayscale(20%)" >
		</a>
		<input  
			type="image"
			src="\Common_Resources\search_2_icon.png" 
			id="banner_toggle_btn_id" 
			name="banner_toggle_btn_name" 
			class="btn white" 
			style="
			left:10px;
			top:15px;
			font-size:11;
			position:absolute;
			z-index:99;
			width:28px;
			height:28px;
			border-radius:50%;
			border:0.75px solid #D9D9D9;
			background-color:#F4F4F4F0;
			display:none;
			transition: 0.5s;
			box-shadow: rgba(0, 0, 0, 0.07) 0px 1px 2px, rgba(0, 0, 0, 0.47) 0px 2px 4px, rgba(0, 0, 0, 0.07) 0px 4px 8px, rgba(0, 0, 0, 0.07) 0px 8px 16px, rgba(0, 0, 0, 0.07) 0px 16px 32px, rgba(0, 0, 0, 0.07) 0px 32px 64px;"  
			value="|||" 
			title="Menu de Recherche" 
			onmouseover="banner_toggle_regul()" 
			/>
			
		
		<div id="sidepanel_left" class="w3-sidebar w3-light-grey w3-bar-block" onclick="password_field_toggle()">	   
		   
		   <input  
			type="image"
			src="\Common_Resources\pin_blue.png" 
			id="left_panel_lock_id" 
			class="left_panel_locked"
			style="
			right:calc(11% - 10px);
			width:20px;
			height:20px;
			top:6px;
			z-index:99;
			position:absolute;
			opacity:0.8;"
			title="Panneau Figé - Click = Activer la retraction auto"
			onclick="left_panel_toogle()"
			label="Verrouillage du panneau"
			/>
			
		   
		   
		  <div id="FilterTitle">
			Référence Plan
		  </div>
		  <input type="text" class="input_welcome" name="Search_Draw" id="Search_Draw" title="Référence du plan de fabrication ou du document" placeholder="" onchange="search_start(1)">
		  
		  <div id="FilterTitle">
			Référence Article
		  </div>
		  
		  <input type="text" class="input_welcome" name="Search_Ref" id="Search_Ref" title="Référence 18 digits maximum" placeholder="" onchange="search_start(1)">
		  
		  <div id="FilterTitle">
			Titre Référence Article
		  </div>
		  <input type="text" class="input_welcome" name="Search_Title" id="Search_Title" title="Description ou désignation de la référence" placeholder="" onchange="search_start(1)">
		  
		  <div id="FilterTitle">
			Référence Commerciale
		  </div>
		  <input class="input_welcome" type="text" name="Search_Alias" id="Search_Alias" title="Référence commerciale" placeholder=""  onchange="search_start(1)">
		  
		  <div id="FilterTitle">
			Plan Client
		  </div>
		  <input type="text" class="input_welcome" name="Search_Cust_Drawing" id="Search_Cust_Drawing" title="Plan client associé à la référence" placeholder=""  onchange="search_start(1)">
		  
		  <div id="FilterTitle">
			Division / Activité
		  </div>

		  <select id="Search_divison" name="Search_divison" class="select_welcome" style="width:90%;" title="Division/activité auquel la référence ou le plan sont associés"  onchange="search_start(1)">
				<option value="" SELECTED></option>
				<?php
					include('../REL_Connexion_DB.PHP');
					$requete = 'SELECT DISTINCT Activity
								FROM tbl_activity								
								ORDER BY Activity ASC';
					$resultat = $mysqli->query($requete);
					while ($row = $resultat->fetch_assoc())
					{
						echo'<option value ="'.$row['Activity'].'" >'.$row['Activity'].'</option><br/>';
					}
					$mysqli->close();
					?>
				</select>

			
		   <div id="FilterTitle" style="margin-top:20px">
			Certification &nbsp
		  
			<select id="Search_certif" name="Search_certif" class="select_welcome" style="margin-left:5px" title="Certification spécifique à laquelle la référence est qualifiée"  onblur="search_start(1)">
				<option value="%" SELECTED></option>
				<option value="ATEX" >ATEX</option>
				<option value="CSA" >CSA</option>
				<option value="IECEX" >IECEx</option>
			</select>
			</div>
		   
		   
		   <div id="FilterTitle" style="margin-top:20px">
			Code Produit
		  
				<select id="Search_product_code" name="Search_product_code" class="select_welcome" title="Code produit asscoié à la référence" onchange="Product_Code_Description_Auto()"  onblur="search_start(1)">
					<option value="" SELECTED></option>
					<?php
						include('../SCM_Connexion_DB.PHP');
						$requete = 'SELECT DISTINCT Code, Description
									FROM tbl_product_code								
									ORDER BY Code ASC';
						$resultat = $mysqli_scm->query($requete);
						while ($row = $resultat->fetch_assoc())
						{
							echo'<option value ="'.$row['Code'].'" >'.$row['Code'].'&nbsp - &nbsp'.$row['Description'].'</option><br/>';
						}
						$mysqli_scm->close();
						?>
				</select>
				<div HIDDEN style="text-align:center;margin-top:-5px;">
					<input type="text" style="background-color:transparent;border:none;font-size:6pt;color:black;text-decoration:italic" id="product_code_descritpion"></input>
				</div>
			</div>
		 


		  
		  <div style="margin-top:30px; text-align:center">
			<input type="submit" onclick="search_start(1)" style="border-radius:10px;font-family:arial black;font-size:12px; vertical-align:middle;text-align:center;width:75%; height:30px;box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.4), 0 6px 20px 0 rgba(0, 0, 0, 0.19);text-align: center;cursor:pointer;border: none;color:white;" class="btn blue" name="Search_btn" value="Recherche" title="Lancer la recherche"/>
		  </div>
		  <div style="margin-top:10px; text-align:center">
			<input type="submit" onclick="reset_search(0)" style="border-radius:10px;font-family:arial black;font-size:12px; vertical-align:middle;text-align:center;width:75%; height:30px;box-shadow: 0 4px 4px 0 rgba(0, 0, 0, 0.4), 0 6px 20px 0 rgba(0, 0, 0, 0.19);text-align: center;cursor:pointer;border: none;" class="btn grey" name="Reset_btn" value="Reset" title="Réinitialise les critères de recherche et les derniers résultats."/>
		  </div>
			
			
		  
		  <!-- HISTORIQUE DE RECHERCHE -->
		  <!----------------------------->
		  <div>
			<table id="search_hist_table" title="Cliquer pour acceder aux resultats de vos précédentes recherches">
				
			</table>
		  </div> 
		  <!----------------------------->
		  
		  
		  <div  id="FilterTitle" style="bottom:0%;margin-bottom:70px;z-index:99;position:absolute;left:10px;">
			  <a href="Resources\V2D_Presentation.pdf" target="_blank">
				<input 
					type="image" 
					src="\Common_Resources\icon_info.png" 
					id="help_doc" 
					style="width:20px;height:20px;cursor:pointer" 
					title="Aide sur l'utilisation de V2D"
					label="Verrouillage du panneau"
				>
			  </a>
		  </div>
		  
		  <input HIDDEN id="limit_val_id" style="width:20px"  type=text value="<?php  echo $limit_val ?>" enabled>
		</div>



		

		<div id="FilterTitle" style="bottom:0%;margin-bottom:12px;z-index:1;position:absolute;left:35px;z-index:99">
			<!--<a onclick="Admin_PWD_Check()" target="_blank">
				<input type="image" src="\Common_Resources\icon_admin.png" id="help_doc" style="width:20px;height:20px;cursor:pointer" title="Administrateur" label="Administrateur">
			</a>-->
			
			
			
			<span HIDDEN id="pass_zone" style="font-size:9pt;font-weight:bold;font-style:italic;z-index:99">Password:<br> 
				<input type="password" value="" style="width:100px;height:21px;font-size:9pt;z-index:99" id="pass_input">
				<input type="button" class="btn grey" id="pass_btn_validation" onclick="Admin_PWD_Check()" style="z-index:99;border-radius:5px;font-size:7pt;width:35px;height:21px;vertical-align:top;text-align:center" value="GO"/>
				<script>
					var pass_input_validation = document.getElementById("pass_input");
					pass_input_validation.addEventListener("keypress", function(event) {
					  if (event.key === "Enter") {
						event.preventDefault();
						document.getElementById("pass_btn_validation").click();
					  }
					});
				</script>
			</span>
			
			<input type="image" onclick="pass_display()" src="\Common_Resources\icon_admin.png" id="admin_pass_button" style="width:20px;height:20px;cursor:pointer;z-index:99" title="Administrateur" label="Administrateur">
			
		</div>
		
		<!--<span id="version">
			v1.0
		  </span>-->


<!-- Page Content -->


	<div class="w3-container" style="margin-left:11%;" id="main_area">
		<iframe
			name="main_iframe" 
			id="main_frame_id" 
			class="main_frame_reduced" 
			frameborder=0
			scrolling=no
			src="PN_Welcome_Frame.php"
			style="background-image: url(/Common_Resources/logo_scm_zoom_left_transparent.jpg);
				   background-repeat: no-repeat;
				   background-position: left bottom;
				   margin-left:-15px;
				   background-color:transparent;"
		>
	</div>



</body>
</html>