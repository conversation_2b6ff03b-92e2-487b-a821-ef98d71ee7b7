<?php

			  
  if (isset($_GET['date_to']) && isset($_GET['date_from']) && ($_GET['date_to'])!="" && ($_GET['date_from'])!="")
  {
  $from_date=$_GET['date_from'];
  $to_date=$_GET['date_to'];
} else {
  $from_date="2022-10-01";
  $to_date=date("Y-m-d");      
}

	$sql_4 = 'SELECT 
				DATE_SAP,
				count(*) as "total" 
			  FROM
				`tbl_pn` 
			  WHERE 
				DATE_SAP<="'.$to_date.' "
			AND DATE_SAP>="'.$from_date.'"
				AND (DATE_SAP not like "2023-02-01"  OR (DATE_SAP like "2023-02-01" AND DATE_Costing not like "2023-02-01")) AND DATE_SAP not like "2024-03-24"
			  GROUP BY YEAR(DATE_SAP), MONTH(DATE_SAP), DAY(DATE_SAP) 
			  ORDER BY YEAR(DATE_SAP) ASC, MONTH(DATE_SAP) ASC, DAY(DATE_SAP) ASC;';
	$sql_5 = 'SELECT 
				MIN(DATE_SAP) as "MIN_DATE"
			  FROM
				`tbl_pn` 
			  WHERE 
				DATE_SAP<="'.$to_date.' "
			AND DATE_SAP>="'.$from_date.'"
				AND (DATE_SAP not like "2023-02-01" OR (DATE_SAP like "2023-02-01" AND DATE_Costing not like "2023-02-01")) AND  DATE_SAP not like "2024-03-24" '; 

include('../../PN_Connexion_PN.php');
$result_5 = $mysqli_pn->query($sql_5);	
while ($row_5 = $result_5->fetch_assoc()) 
{
	$previous_date=$row_5['MIN_DATE'];
}

$val_list="";
$result_4 = $mysqli_pn->query($sql_4);	
while ($row_4 = $result_4->fetch_assoc()) 
{
	if ($val_list!="")
	{
		$val_list=$val_list . "||" ;
		$next_date=date('Y-m-d', strtotime($previous_date. ' + 1 days'));
	} else {
		$next_date=$previous_date;
	}
	
	//print_r($previous_date." +1 -> ".$next_date."   ");
	if ($row_4['DATE_SAP']!=$next_date)
		{
		while ($row_4['DATE_SAP']!=$next_date)
		{
			$next_date_week = date_format(date_create($next_date),'w');
			if ($next_date_week!=0 && $next_date_week!=6)
			{				
				$val_list=$val_list.$next_date.';0||';
			}
			$next_date=date('Y-m-d', strtotime($next_date. ' + 1 days'));
		}
	} 
		$val_list=$val_list.$row_4['DATE_SAP'].';'.$row_4['total'];
	
	$previous_date=$row_4['DATE_SAP'];
}

echo $val_list;
mysqli_close($mysqli_pn);

?>
