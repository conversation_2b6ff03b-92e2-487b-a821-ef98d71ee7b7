
<!--

###########################################################################
## 																		 ##
## 			PERMET D'OBTERNIR UN RAPPORT D'ACTIVITE SUR L'OUTILS	 	 ##
## 			_____________________________________________________		 ##
## 																		 ##
## 																		 ##
##		CREATION : 2023-02-06			DATE CREATION : M. BAUER		 ##
##																		 ##
###########################################################################


-->

<head>
<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
<script src='https://cdn.plot.ly/plotly-2.18.0.min.js'></script>
<script>

var value_list=[];
var id_list=1;
var val_x_1_plotly=[];
var val_x_2_plotly=[];
var val_y_1_plotly=[];
var val_y_2_plotly=[];
const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sept", "Oct", "Nov", "Dec"];


function graph_building()
{
	const xhttp = new XMLHttpRequest();
        xhttp.onload = function()
		{
            const raw_result = this.responseText.trim();
			var split_raw_result=raw_result.split("||");
			
			// -------
			// PLOTLY
			// -------
			var split_raw_result_plotly=raw_result.split("||");			
				for (let j=0; j<split_raw_result.length;j++)
				{
					var split_record=split_raw_result[j].split(";");
					var date_tmp=new Date(parseFloat(Date.parse(split_record[0])));					
					val_x_1_plotly[j]=(date_tmp.getMonth()+1) + "/" + date_tmp.getDate();
					val_x_1_plotly[j]=date_tmp.getDate() + '-' + months[date_tmp.getMonth()];
					val_y_1_plotly[j]=parseFloat(split_record[1]);

					if (j>0)
					{
						val_y_2_plotly[j]=val_y_2_plotly[j-1] + parseFloat(split_record[1]);	
					} else {
						val_y_2_plotly[j]=parseFloat(split_record[1]);
					}					
				}
				
				
				var layout =
				{
				  title: 'Nombre de ref diffusées',
				  backgroundColor: 'transparent',
				  yaxis: {
						title: 'Daily Count'
						},
				  yaxis2: {
					title: 'Cumulative Count',
					titlefont: {color: 'rgb(148, 103, 189)'},
					tickfont: {color: 'rgb(148, 103, 189)'},
					overlaying: 'y',
					side: 'right'
				  }
				};
				
				var trace1 = 
					{
					  x: val_x_1_plotly,
					  y: val_y_1_plotly,
					  name: 'Daily Count',
					  fill: 'tozeroy', //tozeroy
					  type: 'bar',
					 orientation: 'v',
					  mode: 'lines' //lines
					};

				var trace2 = 
					{
					  x: val_x_1_plotly,
					  y: val_y_2_plotly,
					  name: 'Cumulative Total',
					  yaxis: 'y2',
					  fill: 'tozeroy',
					  type: 'scatter',
					  orientation: 'v',
					  mode: 'lines' //lines
					};

				var data = [trace2, trace1];
		
				Plotly.newPlot('graph_div', data, layout);
			
			// -------
		}
		
	var from=document.getElementById("date_from_id").value;
	var to=document.getElementById("date_to_id").value;
	
	if (from!="" && to!="")
	{
		xhttp.open("GET", "Graph.php?date_from=" + from + "&date_to=" + to);
		xhttp.send();
	} else {
		xhttp.open("GET", "Graph.php?date_from=&date_to=");
		xhttp.send();
	}

}



</script>


</head>
<body onload="graph_building()" style="background-color:#212F3C; color:#EAECEE;font-family: Arial, Helvetica, sans-serif;font-size:10pt;">
<table border=0 style="width:100%;border-collapse:collapse">
<tr>
<td style="border-bottom:1px solid black" colspan=2>
<form method="post" action="">
From:
<input type="date" name="date_from_name" id="date_from_id" onchange="this.form.submit()" value="<?php if (isset($_POST['date_from_name'])) { echo $_POST['date_from_name'];} else { echo date("d/m/Y");}?>"></input>
To:
<input type="date" name="date_to_name" id="date_to_id" onchange="this.form.submit()" value="<?php if (isset($_POST['date_to_name'])) { echo $_POST['date_to_name'];} else { echo date("d/m/Y");}?>"></input>

</form>
</td>
</tr>
<tr style="border-bottom:1px solid black">
<td style="vertical-align:top;padding-right:15px" colspan=2>
 <div id="graph_div" style="width: 100%; height: 65vh"></div>
<span id="essai"></span>
</td>
</tr>
<tr>
<td style="width:50%">
<?php
if (isset($_POST['date_from_name']) && isset($_POST['date_to_name']))
{
	if (substr($_POST['date_from_name'],1)!="y")
	{
		$date_1=date_format(date_create($_POST['date_to_name']),"Y-m-d");
		$date_2=date_format(date_create($_POST['date_from_name']),"Y-m-d");
	} else {
		$date_1="2022-11-21";
	}
} else {
	$date_1=date("Y-m-d");
	$date_2=date("Y-m-").(date("d")-1);

	$date_2_exploded = explode('-', $date_2);
	$date_2_timestamp = mktime(0, 0, 0, $date_2_exploded[1], $date_2_exploded[2], $date_2_exploded[0]);
	$date_2_val = date('w', $date_2_timestamp);

	if ($date_2_val == 0) // supprime les dimanches
	{
		$date_2=date("Y-m-").(date("d")-3);
	} else if ($date_2_val == 6)   // supprime les samedis
		{
			$date_2=date("Y-m-").(date("d")-2);
		}
}





include('../../PN_Connexion_PN.php');
$sql_1 = 'SELECT * FROM tbl_pn WHERE DATE_SAP <= "'.$date_1.'" AND DATE_SAP >= "'.$date_2.'" AND (DATE_SAP not like "2023-02-01" OR (DATE_SAP like "2023-02-01" AND DATE_Costing not like "2023-02-01")) AND DATE_SAP not like "2024-03-24" AND DATE_SAP>"2022-03-10" ORDER by Reference DESC, Ref_Rev Desc';
$sql_2 = 'SELECT distinct Reference, ref_rev FROM tbl_pn WHERE DATE_SAP <= "'.$date_1.'" AND DATE_SAP >= "'.$date_2.'" AND (DATE_SAP not like "2023-02-01" OR (DATE_SAP like "2023-02-01" AND DATE_Costing not like "2023-02-01")) AND DATE_SAP not like "2024-03-24" AND DATE_SAP > "2022-03-10"';
$sql_3 = 'SELECT distinct Prod_draw, Prod_draw_rev FROM tbl_pn WHERE DATE_SAP <= "'.$date_1.'" AND DATE_SAP >= "'.$date_2.'" AND (DATE_SAP not like "2023-02-01" OR (DATE_SAP like "2023-02-01" AND DATE_Costing not like "2023-02-01")) AND DATE_SAP not like "2024-03-24" AND DATE_SAP>"2022-03-10"';

$result_1 = $mysqli_pn->query($sql_1);	
$row_ct_1=mysqli_num_rows($result_1);
$result_2 = $mysqli_pn->query($sql_2);	
$row_ct_2=mysqli_num_rows($result_2);
$result_3 = $mysqli_pn->query($sql_3);	
$row_ct_3=mysqli_num_rows($result_3);
echo '<table style="width:90%;text-align:center;font-size:8pt;vertical-align:middle;border-collapse:collapse">';
echo '<tr><td colspan=6 style="font-size:11pt;font-weight:550;border-bottom:0.5px solid black">Ref créées dans <font color="green">tbl_pn</font> entre '.$date_1.' et '. $date_2.'</td></tr>';
echo '<tr><td colspan=6> Nbre Ligne(s) : '.$row_ct_1.' / '.$row_ct_2.' ref. rev  / '.$row_ct_3.' draw. rev</td></tr>';
echo '<th style="padding-top:10px;border:0.5px solid white;" colspan=2>Reference</th>';
echo '<th style="padding-top:10px;border:0.5px solid white;" colspan=2>Prod Draw</th>';
echo '<th style="padding-top:10px;border:0.5px solid white;" colspan=1>Doc Type</th>';
echo '<th style="padding-top:10px;border:0.5px solid white;" colspan=1>Date SAP</th>';
$i=1;
while ($row_1 = $result_1->fetch_assoc()) 
{
	
	echo '<tr style="height:16px">';
	echo '<td style="border:0.5px solid white;">'.$row_1['Reference']. '</td>';
	echo '<td style="border:0.5px solid white;">'.$row_1['Ref_Rev']. '</td>';
	echo '<td style="border:0.5px solid white;">'.$row_1['Prod_Draw']. '</td>';
	echo '<td style="border:0.5px solid white;">'.$row_1['Prod_Draw_Rev']. '</td>';
	echo '<td style="border:0.5px solid white;">'.$row_1['Doc_Type']. '</td>';
	echo '<td style="border:0.5px solid white;">'.$row_1['DATE_SAP']. '</td>';
	echo '</tr>';
	$i=1+$i;
}

echo '</table>';
mysqli_close($mysqli_pn);

echo '</td>';
echo '<td style="vertical-align:top;border-left:1px solid black;padding-left:15px;">';

include('../../REL_Connexion_DB.php');
$sql_1 = 'SELECT * FROM tbl_released_drawing WHERE DATE_GID <= "'.$date_1.'" AND DATE_GID >= "'.$date_2.'" AND VISA_GID not like "" ORDER by Reference DESC, Ref_Rev Desc';
$sql_2 = 'SELECT distinct Reference, ref_rev FROM tbl_released_drawing WHERE DATE_GID <= "'.$date_1.'" AND DATE_GID >= "'.$date_2.'" AND VISA_GID not like ""  ';
$sql_3 = 'SELECT distinct Prod_draw, Prod_draw_rev FROM tbl_released_drawing WHERE DATE_GID <= "'.$date_1.'" AND DATE_GID >= "'.$date_2.'" AND VISA_GID not like "" ';

$result_1 = $mysqli->query($sql_1);	
$row_ct_1=mysqli_num_rows($result_1);
$result_2 = $mysqli->query($sql_2);	
$row_ct_2=mysqli_num_rows($result_2);
$result_3 = $mysqli->query($sql_3);	
$row_ct_3=mysqli_num_rows($result_3);
echo '<table style="width:90%; text-align:center;font-size:8pt;vertical-align:middle;border-collapse:collapse">';
echo '<tr><td colspan=5 style="border:0.5px solid white;font-size:11pt;font-weight:550;border-bottom:0.5px solid black">Ref créées dans <font color="red">tbl_released_drawing</font> entre '.$date_1.' et '. $date_2.'</td></tr>';
echo '<tr><td colspan=5 style="border:0.5px solid white;"> Nbre Ligne(s) : '.$row_ct_1.' / '.$row_ct_2.' ref. rev  / '.$row_ct_3.' draw. rev</td></tr>';
echo '<th style="padding-top:10px;border:0.5px solid white;" colspan=2>Reference</th>';
echo '<th style="padding-top:10px;border:0.5px solid white;" colspan=2>Prod Draw</th>';
echo '<th style="padding-top:10px;border:0.5px solid white;" colspan=1>Doc Type</th>';
$i=1;
while ($row_1 = $result_1->fetch_assoc()) 
{
	
	echo '<tr style="height:16px">';
	echo '<td style="border:0.5px solid white;">'.$row_1['Reference']. '</td>';
	echo '<td style="border:0.5px solid white;">'.$row_1['Ref_Rev']. '</td>';
	echo '<td style="border:0.5px solid white;"><a href="/REL/DRAWINGS/OFFICIAL/'.$row_1['Drawing_Path'].'" target="_blank" style="color:#33D7FF">'.$row_1['Prod_Draw']. '</a></td>';
	echo '<td style="border:0.5px solid white;">'.$row_1['Prod_Draw_Rev']. '</td>';
	echo '<td style="border:0.5px solid white;">'.$row_1['Doc_Type']. '</td>';
	echo '</tr>';
	$i=1+$i;
}

echo '</table>';


mysqli_close($mysqli);

?>
</td>

</tr>
</table>

</body>