<!DOCTYPE html>
<html>
   <head>
      <meta charset="UTF-8" />
   </head>
  
  
 <body style="background-color:#212F3C; color:#EAECEE">
 
<?php

###############################################################################################
## 																							 ##
## 			PERMET DE VERIFIER QUE TOUS LES PLANS INDIQUES DANS DRAWING_PATH EXISTENT	 	 ##
## 			_________________________________________________________________________		 ##
## 																							 ##
## 																							 ##
##		CREATION : 2023-02-06								DATE CREATION : M. BAUER		 ##
##																							 ##
###############################################################################################

echo '<table border=1 style="border-collapse: collapse;;font-size:11pt;font-family:arial sans-sherif;text-align:center"">';
echo '<tr><td colspan=9>Liste des fichiers pointés mais non existants sur le serveur au chemin '.$_SERVER['DOCUMENT_ROOT'] .'\\REL\\DRAWINGS\\OFFICIAL\\<br></td></tr>';
echo '<tr><td colspan=9>Nombre d\'articles impactés:<span id="impacted_article"></span>&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbsp&nbspNombre de plans manquants:<span id="missing_drawing"></span></td></tr>';
echo '<th>ct art</th>';
echo '<th>ct draw</th>';
echo '<th>#</th>';
echo '<th>ID</th>';
echo '<th>Reference</th>';
echo '<th>Prod Drawing</th>';
echo '<th>Plan associé</th>';
echo '<th>Lien Complet</th>';
echo '<th>Ref/Draw Revs	</th>';

include('../../PN_Connexion_PN.PHP');
	
$sql_1 = 'SELECT ID, Reference, Ref_Rev, Prod_Draw, Prod_Draw_Rev, Drawing_Path FROM tbl_pn ORDER by Reference DESC, Ref_Rev Desc';
$result_1 = $mysqli_pn->query($sql_1);	
$i=1;
$ct=0;
$ct_draw=0;
$ct_prev="";
$ct_ref_draw=0;
while ($row_1 = $result_1->fetch_assoc()) 
{
	
	
	$file_path='\\REL\\DRAWINGS\\OFFICIAL\\'.$row_1['Drawing_Path'];
	if (file_exists($_SERVER['DOCUMENT_ROOT'] .$file_path))
	{
	} else {
		$ct=$ct+1;
		$col="#212F3C";
		if ($ct_prev=="" || $ct_prev!=$row_1['Drawing_Path'])
		{
			$ct_draw=$ct_draw+1;
			$ct_prev=$row_1['Drawing_Path'];
			$col="#EAECEE";
		}
		
		echo '<tr>';
		echo '<td style="width:auto;text-align:center">';
		echo $ct;
		echo '</td>';
		echo '<td style="width:auto;text-align:center;color:'.$col.'">';
		echo $ct_draw;
		echo '</td>';
		echo '<td style="width:auto;text-align:center">';
		echo $i;
		echo '</td>';
		echo '<td style="width:auto;text-align:center">';
		echo $row_1['ID'];
		echo '</td>';
		echo '<td style="width:auto;text-align:center">';
		echo $row_1['Reference'] . ' - ' .$row_1['Ref_Rev'];
		echo '</td>';
		echo '<td style="width:auto;text-align:center">';
		echo $row_1['Prod_Draw'] . ' - ' .$row_1['Prod_Draw_Rev'];
		echo '</td>';
		echo '<td style="width:auto;text-align:center"><a href="'.$file_path.'" target ="_blank" style="color:#33D7FF">';
		echo $row_1['Drawing_Path'];
		echo '</a></td>';
		echo '<td>'.$file_path.'</td>';
		echo '<td> ';
		if ($row_1['Ref_Rev']!=$row_1['Prod_Draw_Rev'])
		{
			echo $row_1['Ref_Rev'].' | '.$row_1['Prod_Draw_Rev'];
			
			$file_path='../../REL/DRAWINGS/OFFICIAL/'.$row_1['Prod_Draw'].'_IND_'.$row_1['Ref_Rev'].'.pdf';
			if (file_exists($_SERVER['DOCUMENT_ROOT'] .$file_path))
			{
				echo ' - <a href="'.$file_path.'" target="_blank" style="color:#33D7FF">plan</a>';
				//
				
				$ct_ref_draw=$ct_ref_draw+1;
				echo ' - '.$ct_ref_draw;
				//$sql_correction='UPDATE tbl_pn SET Drawing_Path="'.$row_1['Prod_Draw'].'_IND_'.$row_1['Ref_Rev'].'.pdf'.'", Prod_Draw="'.$row_1['Prod_Draw'].'", Prod_Draw_Rev="'.$row_1['Ref_Rev'].'";';
				//$resultat = $mysqli_pn->query($sql_correction);
				//echo ' - MOD';
			} else {
				}
		}
		echo '</td>';
		
		// $alternate_file=scandir("/REL/DRAWINGS/OFFICIAL/".$row_1['Prod_Draw']."%.pdf");
		// echo '<td>';
		// echo $alternate_file;
		// echo '</td>';
		echo '</tr>';
		
	}
	
	
	$i=1+$i;
}

echo '</table>';
mysqli_close($mysqli_pn);

?>


<script>
var val_ct=<?php echo $ct; ?>;
var val_ct_draw=<?php echo $ct_draw; ?>;
var val_ct_rev=<?php echo $ct_ref_draw; ?>;

alert(' - Nombre d\'articles impactés : ' + val_ct + '\n - ' + ' Nombre de plans manquants: : ' + val_ct_draw + '\n - ' + 'Plans à la ref article différente : ' + val_ct_rev);
Document.getElementById("impacted_article").innerHTML=" - " + val_ct;
Document.getElementById("missing_drawing").innerHTML=val_ct_draw;


</script>





</body>
</html>