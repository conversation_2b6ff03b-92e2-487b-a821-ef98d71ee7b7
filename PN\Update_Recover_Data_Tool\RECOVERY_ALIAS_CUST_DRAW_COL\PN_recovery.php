<?php

###############################################################################################################################
## 																															 ##
## 			PERMET DE RECUPERER LES DONNES D'UNE BASE DE DONNEES AUTRE QUE DB_PN POUR LES INJECTER DANS LA BASE DB_PN   	 ##
## 			_________________________________________________________________________________________________________		 ##
## 																															 ##
## 																															 ##
##         EXEMPLE: RECUPERATION DES DONNEES DE LA BASE RELEASE APRES AJOUT DES COLONNES ALIAS ET CUST_DRAWING DANS TBL_PN   ##
##																															 ##
##																															 ##
##		CREATION : 2023-02-01																DATE CREATION : M. BAUER		 ##
##																															 ##
###############################################################################################################################

echo '<table border=1>';
echo '<tr>';
echo '<th rowspan=2>#</th>';
echo '<th colspan=4>PN</th>';
echo '<th colspan=4>Release</th>';
echo '</tr>';

echo '<th>ID</th>';
echo '<th>Alias</th>';
echo '<th>Cust D</th>';
echo '<th>Rev</th>';

echo '<th>Alias</th>';
echo '<th>Cust D</th>';
echo '<th>Rev</th>';

############################################
##                                        ##
## 1 = REQUETES DE MISE A JOUR EXECUTEES  ##
## 0 = REQUETES DE MISE NON EXECUTEES     ##
##										  ##
			$apply_update=0;   
############################################

include('../PN_Connexion_PN.PHP');

include('../REL_Connexion_DB.php');
	
	
$sql_1 = 'SELECT ID, Alias, Cust_Drawing, Cust_Drawing_Rev FROM tbl_pn';
$result_1 = $mysqli_pn->query($sql_1);	
$i=1;
while ($row_1 = $result_1->fetch_assoc()) 
{
	echo '<tr>';
	echo '<td style="width:15px;text-align:center">';
	echo $i;
	echo '</td>';
	echo '<td style="width:40px;text-align:center">';
	echo $row_1['ID'];
	echo '</td>';
	echo '<td style="width:280px;text-align:center">';
	echo $row_1['Alias'];
	echo '</td>';
	echo '<td style="width:280px;text-align:center">';
	echo $row_1['Cust_Drawing'];
	echo '</td>';
	echo '<td style="width:15px;text-align:center">';
	echo $row_1['Cust_Drawing_Rev'];
	echo '</td>';
	
	
	
	$sql_2 = 'SELECT ID, Alias, Cust_Drawing, Cust_Drawing_Rev FROM tbl_released_drawing WHERE ID like "'.$row_1['ID'].'"';
	$result_2 = $mysqli->query($sql_2);	
	while ($row_2 = $result_2->fetch_assoc()) 
	{
		echo '</td>';
		echo '<td style="width:280px;text-align:center">';
		echo $row_2['Alias'];
		echo '</td>';
		echo '<td style="width:280px;text-align:center">';
		echo $row_2['Cust_Drawing'];
		echo '</td>';
		echo '<td style="width:15px;text-align:center">';
		echo $row_2['Cust_Drawing_Rev'];
		echo '</td>';
		$report="";
		
		if ($apply_update==1)
		{
			if (strlen($row_2['Alias'])>2 && strlen($row_1['Alias'])==0)
			{
				$sql_update='UPDATE tbl_pn SET Alias="'.$row_2['Alias'].'" WHERE ID like "'.$row_1['ID'].'"';
				$resultat_update = $mysqli_pn->query($sql_update);
				$report=$report.'Alias&nbsp';
			}
			
			if (strlen($row_2['Cust_Drawing'])>2 && strlen($row_1['Cust_Drawing'])==0)
			{
				$sql_update='UPDATE tbl_pn SET Cust_Drawing="'.$row_2['Cust_Drawing'].'", Cust_Drawing_Rev="'.$row_2['Cust_Drawing_Rev'].'" WHERE ID like "'.$row_1['ID'].'"';
				$resultat_update = $mysqli_pn->query($sql_update);
				$report=$report.'Cust_Drw';
			}
			echo '<td>'.$report.'</td>';
		}
	}
	
	echo '</tr>';
	$i=1+$i;
}

echo '</table>';
mysqli_close($mysqli);
mysqli_close($mysqli_pn);

?>