<!DOCTYPE html>
<html>
   <head>
      <meta charset="UTF-8" />
   </head>
   
<?php

###############################################################################################
## 																							 ##
## 			CHARGEMENT DES PROD DRAW, PROD DRAW REV et DRAWING PATH SUITE PB DE BD         	 ##
## 			_________________________________________________________________________		 ##
## 																							 ##
## 																							 ##
##		CREATION : 2023-05-10								DATE CREATION : M. BAUER		 ##
##																							 ##
###############################################################################################

echo '<table border=1 style="border-collapse: collapse;;font-size:11pt;font-family:arial sans-sherif;text-align:center"">';

include('../PN_Connexion_PN.PHP');
$val_slot=570;
// - TO BE MODIFIED -
// ------------------
    $start_val=29250;
	$ct_total=$val_slot; // TO BE EQUAL 0 TO RUN AN UPDATE - TO BE EQUAL VAL_SLOT NOT TO RUN THE UPDATE
// ------------------


$end_val=$start_val+$val_slot;

echo '<div>start : '. $start_val.'</div>
<div>end : '.$end_val.'</div><br>';
$start_time=time();
$sql_1 = 'SELECT * FROM tbl_pn_recovery ORDER BY ID ASC LIMIT '.$start_val.','.$end_val.'';
$result_1 = $mysqli->query($sql_1);	
$i=0;
$ct_ref=0;


	while ($row_1 = $result_1->fetch_assoc()) 
	{
		if ($ct_total<$val_slot)
		{
			echo '<tr>';
			if ($row_1['Reference']!="-" && $row_1['Doc_Type']!="DOC")
			{
				$sql_check_in_tbl_pn='SELECT * FROM tbl_pn WHERE Reference like "'.$row_1['Reference'].'" AND Ref_Rev like "'.$row_1['Ref_Rev'].'" AND  Doc_Type like "'.$row_1['Doc_Type'].'"';
				$result_check_tbl_pn = $mysqli->query($sql_check_in_tbl_pn);	
				$row_cnt_check_tbl_pn=mysqli_num_rows($result_check_tbl_pn);
				if ($row_cnt_check_tbl_pn==1)
				{
					$i=$i+1;
					while ($result_pn = $result_check_tbl_pn->fetch_assoc()) 
					{
						
						// TO BE RUN ONCE ONLY 
						// -------------------
						$sql_correction='
						UPDATE tbl_pn
						SET 
							Prod_Draw = "'.$row_1['Prod_Draw'].'", 
							Prod_Draw_Rev = "'.$row_1['Prod_Draw_Rev'].'", 
							Drawing_Path = "'.$row_1['Drawing_Path'].'"
						WHERE
								Reference like "'.$row_1['Reference'].'" AND
								Ref_Rev like "'.$row_1['Ref_Rev'].'" AND 
								Doc_Type like "'.$row_1['Doc_Type'].'"';
						$resultat = $mysqli->query($sql_correction);
						// -------------------
						
						// echo '<td>';
						// echo $i;
						// echo '</td>';
						
						
						// echo '<td style="background-color:green">';
						// echo ' REF ';
						// echo '</td>';
						
						// echo '<td>';
						// echo $row_1['ID'];
						// echo '</td>';
						// echo '<td>';
						// echo $row_1['Reference'];
						// echo '</td>';
						// echo '<td>';
						// echo $row_1['Ref_Rev'];
						// echo '</td>';
						// echo '<td>';
						// echo $row_1['Prod_Draw'];
						// echo '</td>';
						// echo '<td>';
						// echo $row_1['Prod_Draw_Rev'];
						// echo '</td>';
						// echo '<td>';
						// echo $row_1['Drawing_Path'];
						// echo '</td>';
						// echo '<td>';
						// echo $row_1['Doc_Type'];
						// echo '</td>';
						
						
						// echo '<td>';
						// echo $result_pn['ID'];
						// echo '</td>';
						// echo '<td>';
						// echo $result_pn['Reference'];
						// echo '</td>';
						// echo '<td>';
						// echo $result_pn['Ref_Rev'];
						// echo '</td>';
						// echo '<td>';
						// echo $result_pn['Doc_Type'];
						// echo '</td>';
						
						// echo '</tr>';
					}
				} else if ($row_1['Rel_Pack_Num']<>"")
				{
					$sql_check_in_tbl_pn='SELECT * FROM tbl_pn WHERE Reference like "'.$row_1['Reference'].'" AND Ref_Rev like "'.$row_1['Ref_Rev'].'" AND  Doc_Type like "'.$row_1['Doc_Type'].'" AND Rel_Pack_Num like "'.$row_1['Rel_Pack_Num'].'"';
					$result_check_tbl_pn = $mysqli->query($sql_check_in_tbl_pn);	
					$row_cnt_check_tbl_pn=mysqli_num_rows($result_check_tbl_pn);
					if ($row_cnt_check_tbl_pn==1)
							{
								$i=$i+1;
								while ($result_pn = $result_check_tbl_pn->fetch_assoc()) 
								{
									
									// TO BE RUN ONCE ONLY 
									// -------------------
									$sql_correction='
									UPDATE tbl_pn
									SET
											Prod_Draw = "'.$row_1['Prod_Draw'].'",
											Prod_Draw_Rev = "'.$row_1['Prod_Draw_Rev'].'",
											Drawing_Path = "'.$row_1['Drawing_Path'].'"
									WHERE
											Reference like "'.$row_1['Reference'].'" AND
											Ref_Rev like "'.$row_1['Ref_Rev'].'" AND 
											Doc_Type like "'.$row_1['Doc_Type'].'" AND
											Rel_Pack_Num like "'.$row_1['Rel_Pack_Num'].'"';
									$resultat = $mysqli->query($sql_correction);
									// -------------------
									
									
									// echo '<td>';
									// echo $i;
									// echo '</td>';
									
									
									// echo '<td style="background-color:green">';
									// echo ' REF REL_PACK ';
									// echo '</td>';
									
									// echo '<td>';
									// echo $row_1['ID'];
									// echo '</td>';
									// echo '<td>';
									// echo $row_1['Reference'];
									// echo '</td>';
									// echo '<td>';
									// echo $row_1['Ref_Rev'];
									// echo '</td>';
									// echo '<td>';
									// echo $row_1['Prod_Draw'];
									// echo '</td>';
									// echo '<td>';
									// echo $row_1['Prod_Draw_Rev'];
									// echo '</td>';
									// echo '<td>';
									// echo $row_1['Drawing_Path'];
									// echo '</td>';
									// echo '<td>';
									// echo $row_1['Doc_Type'];
									// echo '</td>';
									
									
									// echo '<td>';
									// echo $result_pn['ID'];
									// echo '</td>';
									// echo '<td>';
									// echo $result_pn['Reference'];
									// echo '</td>';
									// echo '<td>';
									// echo $result_pn['Ref_Rev'];
									// echo '</td>';
									// echo '<td>';
									// echo $result_pn['Doc_Type'];
									// echo '</td>';
									
									// echo '</tr>';
								}
							}
				} else 	{
					echo '<tr>';
					echo '<td colspan=16 style="background-color:yellow">';
					echo  $row_1['Reference'].' rev'.$row_1['Ref_Rev'].' : ' . $row_cnt_check_tbl_pn;
					$ct_ref=$ct_ref+1;
					echo '<td>';
					echo '</tr>';
				}
			
			} else if ($row_1['Reference']=="-" || $row_1['Doc_Type']=="DOC") 
					{
						
						$sql_check_in_tbl_pn='SELECT * FROM tbl_pn WHERE ID like "'.$row_1['ID'].'"';
						$result_check_tbl_pn = $mysqli->query($sql_check_in_tbl_pn);	
						$row_cnt_check_tbl_pn=mysqli_num_rows($result_check_tbl_pn);
						if ($row_cnt_check_tbl_pn==1)
						{
							$i=$i+1;
							while ($result_pn = $result_check_tbl_pn->fetch_assoc()) 
							{
								
								
								// TO BE RUN ONCE ONLY 
								// -------------------
								$sql_correction='
								UPDATE tbl_pn
								SET
										Prod_Draw = "'.$row_1['Prod_Draw'].'",
										Prod_Draw_Rev = "'.$row_1['Prod_Draw_Rev'].'",
										Drawing_Path = "'.$row_1['Drawing_Path'].'"
								WHERE
										ID like "'.$row_1['ID'].'"';
								$resultat = $mysqli->query($sql_correction);
								// -------------------
								
								
								
								
								// echo '<td>';
								// echo $i;
								// echo '</td>';
								
								
								// echo '<td style="background-color:blue">';
								// echo ' ID ';
								// echo '</td>';
								
								// echo '<td>';
								// echo $row_1['ID'];
								// echo '</td>';
								// echo '<td>';
								// echo $row_1['Reference'];
								// echo '</td>';
								// echo '<td>';
								// echo $row_1['Ref_Rev'];
								// echo '</td>';
								// echo '<td>';
								// echo $row_1['Prod_Draw'];
								// echo '</td>';
								// echo '<td>';
								// echo $row_1['Prod_Draw_Rev'];
								// echo '</td>';
								// echo '<td>';
								// echo $row_1['Drawing_Path'];
								// echo '</td>';
								// echo '<td>';
								// echo $row_1['Doc_Type'];
								// echo '</td>';
								
								
								// echo '<td>';
								// echo $result_pn['ID'];
								// echo '</td>';
								// echo '<td>';
								// echo $result_pn['Reference'];
								// echo '</td>';
								// echo '<td>';
								// echo $result_pn['Ref_Rev'];
								// echo '</td>';
								// echo '<td>';
								// echo $result_pn['Doc_Type'];
								// echo '</td>';
								
								// echo '</tr>';
							}
						} else {
								echo '<tr>';
								echo '<td colspan=16 style="background-color:orange">';
								echo  ' ID : ' . $row_cnt_check_tbl_pn . ' - TO BE CLEARED';
								echo '<td>';
								echo '</tr>';
						}
						
					}
		
		$ct_total=$ct_total+1;
		}
	}
	
	

echo '</table>';
mysqli_close($mysqli);
$end_time=time();
$duration=$end_time - $start_time;
$pot_inc=round((120-$duration)*$val_slot/$duration,0);
$pot_max=$pot_inc+$val_slot;
?>

<body>
<div>compteur : <?php echo $i?></div>
<div>error on ref: <?php echo $ct_ref?></div>
<div>duration : <?php echo $duration?> secondes</div>
<div>potential increase :  <?php echo $pot_max.' ('.$pot_inc.')';?></div>
<script type="text/javascript">
alert("END");
</script>
</body>
</html>