<!DOCTYPE html>
<html>
   <head>
      <meta charset="UTF-8" />
   </head>
   
<?php

###############################################################################################
## 																							 ##
## 			CHARGEMENT DES PROD DRAW, PROD DRAW REV et DRAWING PATH SUITE PB DE BD         	 ##
## 			_________________________________________________________________________		 ##
## 																							 ##
## 																							 ##
##		CREATION : 2023-05-10								DATE CREATION : M. BAUER		 ##
##																							 ##
###############################################################################################

echo '<table border=1 style="border-collapse: collapse;font-size:11pt;font-family:arial sans-sherif;text-align:center"">';

include('../PN_Connexion_PN.php');

include('../REL_Connexion_DB.php');
$start_time=time();
$sql_pn = 'SELECT * FROM tbl_pn WHERE Prod_Draw like "" and Rel_Pack_Num not like "" and reference not like "-" and doc_type not like "DOC"';
$result_pn = $mysqli_pn->query($sql_pn);	
$i=0;
$OK_TO_GO=0;
if ($OK_TO_GO==1)
{
	while ($row_pn = $result_pn->fetch_assoc()) 
	{
		$i=$i+1;
		echo '<tr>';
		echo '<td>';
		echo $i;
		echo '</td>';
		echo '<td>';
		echo $row_pn['Reference'];
		echo '</td>';
		
		
		$sql_rel='SELECT * from tbl_released_drawing where Reference like "'.$row_pn['Reference'].'" AND Ref_Rev like "'.$row_pn['Ref_Rev'].'" AND Rel_Pack_Num like "'.$row_pn['Rel_Pack_Num'].'"  ';
		$result_rel = $mysqli->query($sql_rel);	
		while ($row_rel = $result_rel->fetch_assoc()) 
		{
			echo '<td>';
			echo $row_rel['Prod_Draw'];
			echo '</td>';
			echo '<td>';
			echo $row_rel['Prod_Draw_Rev'];
			echo '</td>';
			echo '<td>';
			echo $row_rel['Drawing_Path'];
			echo '</td>';
			$sql_update='UPDATE 
							tbl_pn 
						SET 
							Prod_Draw="'.$row_rel['Prod_Draw'].'", 
							Prod_Draw_Rev="'.$row_rel['Prod_Draw_Rev'].'", 
							Drawing_Path="'.$row_rel['Drawing_Path'].'"
						WHERE Reference like "'.$row_pn['Reference'].'" AND Ref_Rev like "'.$row_pn['Ref_Rev'].'" AND Rel_Pack_Num like "'.$row_pn['Rel_Pack_Num'].'"';
						//print_r($sql_update);
			$resultat_update = $mysqli_pn->query($sql_update);
		}
		echo '</tr>';
	}
	
}
	echo '</table>';
mysqli_close($mysqli_pn);
mysqli_close($mysqli);

?>

<body>

<!--<script type="text/javascript">
alert("END");
</script>-->
</body>
</html>