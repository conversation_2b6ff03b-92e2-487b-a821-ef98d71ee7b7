<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_Drawing_Status_styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
    <link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

    <title>
        REL Pack - DRAWING Review
    </title>

</head>

<body>

    <form enctype="multipart/form-data" action="" method="post">

        <table id="t01" border="0">
            <tr>
                <td colspan="9">
                    <div id="Main_Title">
                        Package Status
                    </div>
                </td>
            </tr>

            <tr>

                <td>
                    <div id="FilterTitle">
                        Package #

                        <!--- Récupération des données de la table tbl_released_drawing de "Rel_Pack_Num" dans la base de données pour le champ Pack-->
						<input list="Rel_Pack_Num_Choice_datalist" name="Rel_Pack_Num_Choice" id="Rel_Pack_Num_Choice" value="<?php if(isset($_POST['Rel_Pack_Num_Choice'])){echo $_POST['Rel_Pack_Num_Choice'] ;}?>" style="font-size:9pt;width:60px;height:11px" onchange="this.form.submit()">
						<datalist id="Rel_Pack_Num_Choice_datalist">
                        <!--<SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;" onchange="this.form.submit()">-->
                            <!--<option value="%"></option>-->
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
                                            FROM tbl_released_drawing
                                            ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                if ($row['Rel_Pack_Num'] != "") {
                                    echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '">' . $row['Rel_Pack_Num'] . '</option><br/>';
                                }
                            }
                            mysqli_close($mysqli);
                            ?>
                        <!--</SELECT>-->
                        </datalist>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Activity

                        <SELECT name="Activity_Choice" type="submit" size="1" style="width:100px;font-size:9pt;height:17px" onchange="this.form.submit()">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_package.Activity 
                                FROM tbl_released_package
                                ORDER BY Activity DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Activity_Choice'])) {
                                    if ($_POST['Activity_Choice'] == $row['Activity']) {
                                        $sel = "SELECTED";
                                    } else {
                                    }
                                }
                                if ($row['Activity'] != "") {
                                    echo '<OPTION value ="' . $row['Activity'] . '"' . $sel . '>' . $row['Activity'] . '</option><br/>';
                                }
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Project

                        <!--- Récupération des données de la table tbl_released_package de "Project" dans la base de données pour le champ Project-->

                        <SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.PHP');
                            $requete = 'SELECT DISTINCT tbl_released_package.Project 
                                FROM tbl_released_package 
                                LEFT JOIN tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                ORDER BY tbl_released_package.Project DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Project_Choice'])) {
                                    if ($_POST['Project_Choice'] == $row['Project']) {
                                        $sel = "SELECTED";
                                    } else {
                                    }
                                }
                                if ($row['Project'] != "") {
                                    echo '<OPTION value ="' . $row['Project'] . '"' . $sel . '>' . $row['Project'] . '</option><br/>';
                                }
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Reference

                        <!--- Création d'une zone de texte -->
                        <input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:90pt;" onchange="this.form.submit()" <?php if (isset($_POST['Reference_Choice'])) {
                                                                                                                                                            echo ' Value="' . $_POST['Reference_Choice'] . '">';
                                                                                                                                                        } ?> </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Description

                        <!--- Création d'une zone de texte -->
                        <input type="text" name="Ref_Title" style="font-size:8pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Ref_Title'])) {
                                                                                                                                                    echo ' Value="' . $_POST['Ref_Title'] . '">';
                                                                                                                                                } ?> </div>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Drawing

                        <!--- Récupération des données de la table tbl_released_drawing de "Prod_Draw" dans la base de données pour le champ Prod_Draw-->
                        <input name="Drawing_Choice" type="text" size=20 style="font-size:9pt;height:9pt;width: 100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Drawing_Choice'])) {
                                                                                                                                                            echo ' Value="' . $_POST['Drawing_Choice'] . '">';
                                                                                                                                                        } ?> </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Ex

                        <SELECT name="Ex_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../SCM_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_ex.Ex 
                                FROM tbl_ex
                                ORDER BY tbl_ex.Ex ASC';
                            $resultat = $mysqli_scm->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Ex_Choice'])) {
                                    if ($_POST['Ex_Choice'] == $row['Ex']) {
                                        $sel = "SELECTED";
                                    } else {
                                    }
                                }
                                if ($row['Ex'] != "") {
                                    echo '<OPTION value ="' . $row['Ex'] . '"' . $sel . '>' . $row['Ex'] . '</option><br/>';
                                }
                            }
                            mysqli_close($mysqli_scm);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Type

                        <SELECT name="Doc_Type_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT *
                                FROM tbl_doc_type';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Doc_Type_Choice'])) {
                                    if ($_POST['Doc_Type_Choice'] == $row['Doc_Type']) {
                                        $sel = "SELECTED";
                                    } else {
                                    }
                                }
                                if ($row['Doc_Type'] != "") {
                                    echo '<OPTION value ="' . $row['Doc_Type'] . '"' . $sel . '>' . $row['Doc_Type'] . '</option><br/>';
                                }
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <!-- !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! -->
                <!-- Ajout d'un bouton reset -->
                <td><div id="FilterTitle">
                    <input type="button" class="btn grey" onclick="window.location.href = 'REL_Drawing_Status.php';" value="Reset" style="font-size:8pt; width:55px;height:18px;vertical-align:middle;text-align:center" />
                 &nbsp
                    
                        <!--- Récupération des données de la table tbl_released_drawing de "Rel_Pack_Num" dans la base de données pour le champ Pack-->
                        <input value="Alive" type="submit" name="flag_2" id="flag2" class="btn blue2" style="font-size:8pt;width:55px;height:18px;vertical-align:middle;text-align:center" />
                     &nbsp
                        <!--- Récupération des données de la table tbl_released_drawing de "Rel_Pack_Num" dans la base de données pour le champ Pack-->
                        <input value="Complete" class="btn green" type="submit" name="flag_" id="flag" style="font-size:8pt;width:55px;height:18px;vertical-align:middle;text-align:center" />
                    
                &nbsp
                    
                        <!--- Récupération des données de la table tbl_released_drawing de "Rel_Pack_Num" dans la base de données pour le champ Pack-->
                        <input value="All" class="btn orange" type="submit" name="flag_3" id="flag" style="font-size:8pt;width:55px;height:18px;vertical-align:middle;text-align:center" />
                    </div>
                </td>
            </tr>

            <!--- Vérification des valeurs --->
            <?php

            // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            // Vérification du flag
            $flag_choice = "0";
			$cat_val="References with validation still being processed - Not completed";
            if (isset($_POST['flag_'])) {
                $flag_choice = "1";
				$cat_val="References with a critical path flow completed - Basket validation might not be completed yet";
            } else if (isset($_POST['flag_2'])) {
                $flag_choice =  "0";
				
            } else if (isset($_POST['flag_3'])) {
                $flag_choice =  "%";
				$cat_val="All the references are shown, whichever their status within the validation flow.";
            }

            if (isset($_POST['Rel_Pack_Num_Choice']) == false || ($_POST['Rel_Pack_Num_Choice'])=="") {
                $rel_pack_num_choice = "%";
            } else {
                $rel_pack_num_choice = str_replace("*", "%", $_POST['Rel_Pack_Num_Choice']);
            }

            if (isset($_POST['Activity_Choice']) == false) {
                $activity_choice = "%";
            } else {
                $activity_choice = $_POST['Activity_Choice'];
            }

            if (isset($_POST['Project_Choice']) == false) {
                $project_choice = "%";
            } else {
                $project_choice = $_POST['Project_Choice'];
            }

            if (isset($_POST['Reference_Choice']) == false) {
                $reference_choice = "%";
            } else {
                if (strlen($_POST['Reference_Choice']) > 0) {
                    $reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
                } else {
                    $reference_choice = "%";
                }
            }


            if (isset($_POST['Ref_Title']) == false) {
                $ref_title_choice = "%";
            } else {
                if (strlen($_POST['Ref_Title']) > 0) {
                    $ref_title_choice = str_replace("*", "%", $_POST['Ref_Title']);
                } else {
                    $ref_title_choice = "%";
                }
            }

            if (isset($_POST['Drawing_Choice']) == false) {
                $drawing_choice = "%";
            } else {
                if (strlen($_POST['Drawing_Choice']) > 0) {
                    $drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
                } else {
                    $drawing_choice = "%";
                }
            }

            if (isset($_POST['Ex_Choice']) == false) {
                $Ex_Choice = "%";
            } else {
                $Ex_Choice = $_POST['Ex_Choice'];
            }

            if (isset($_POST['Doc_Type_Choice']) == false) {
                $Doc_Type_Choice = "%";
            } else {
                $Doc_Type_Choice = $_POST['Doc_Type_Choice'];
            }



            $query_1 = 'SELECT *, tbl_released_drawing.Ex as "Ex_plan"
                FROM  tbl_released_drawing
                LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE tbl_released_package.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                AND tbl_released_drawing.Ref_Title like "' . $ref_title_choice . '"
                    AND tbl_released_package.Activity like "' . $activity_choice . '"
                    AND tbl_released_package.Project like "' . $project_choice . '"
                    AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                    AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                    AND tbl_released_drawing.Ex like "' . $Ex_Choice . '"
                    AND tbl_released_drawing.Doc_Type like "' . $Doc_Type_Choice . '"
                    AND tbl_released_drawing.Critical_Complete like "' . $flag_choice . '"
               ORDER BY tbl_released_package.rel_pack_num DESC';
//print_r($query_1);
            include('../REL_Connexion_DB.php');
            $resultat = $mysqli->query($query_1);
            $rowcount = mysqli_num_rows($resultat);
            echo '<tr>';
            echo '<td colspan=11>';
            echo '<table style="margin-top:-5px;margin-bottom:-5px;width:100%" border=0';
            echo '<tr><td><div id="Result_info">Number of results: ' . $rowcount . '&nbsp&nbsp&nbsp';
			echo '<font style="color:gray;font-style:italic">-&nbsp&nbsp&nbsp'.$cat_val.'</font></div></td>';
			//echo '<td style="text-align:right;vertical-align:middle;padding-top:3px"><font >Default view: Only references with critical path validation flow not complete</font></td></tr>';
            echo '</tr></table>';
            echo '</td>';
            echo '</tr>';
            echo '<tr>';
            echo '<td colspan=11>';
            echo '<table id="t02">';
            echo '<th style="width:40px;background-color: rgb(16, 112, 177);">Pack #</th>';
            echo '<th style="width:50px;background-color: rgb(16, 112, 177);">Activity</th>';
            echo '<th style="width:130px;background-color: rgb(16, 112, 177);">Reference</th>';
            echo '<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
            echo '<th style="width:140px;background-color: rgb(16, 112, 177);">Prod Drawing</th>';
            echo '<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
            echo '<th style="width:35px;background-color: rgb(16, 112, 177);">Type</th>';
            echo '<th style="width:60px;background-color: rgb(16, 112, 177);">Comments</th>';
            echo '<th style="border:none;border-bottom:1px solid black;background-color: #F4F4F4" colspan="1">Critical </th>';
            echo '<th style="border:none;"></th>';

            while ($row = $resultat->fetch_assoc())
			{
				
				$font_size='7pt';
				$title_len=20;
				
                echo '<tr>';
                echo '<td  hidden>';
                echo $row['ID'];
                echo '</td>';

                echo '<td style="border-top:1px solid black;border-bottom:1px solid gray;">';
                echo '<a target="_blank" href="REL_Pack_Overview.php?ID=' . $row['Rel_Pack_Num'] . '">' . $row['Rel_Pack_Num'] . '</a>';
                echo '</td>';

                echo '<td style="border-top:1px solid gray;border-bottom:1px solid gray;">';
				echo '<img src="\Common_Resources\Activity_'.$row['Activity'].'.png" height="16px" title="'.$row['Activity'].'">';
                //echo $row['Activity'] . '<br>' . $row['Project'];
				echo '<br><font style="font-size:'.$font_size.'">'. $row['Project'].'</font>';
                echo '</td>';
				
				
				if (strlen($row['Ref_Title'])>$title_len)
				{
					$ref_title='<font style="font-size:'.$font_size.'">'.substr($row['Ref_Title'],0,$title_len-6). '&nbsp[...]</font>';
				} else {
					$ref_title='<font style="font-size:'.$font_size.'">'.$row['Ref_Title'].'</font>';
				}
				
				if (strlen($row['Reference'])<=0)
				{
					$ref='-';
				} else {
					$ref=$row['Reference'];
				}
                echo '<td style="border-top:1px solid gray;border-bottom:1px solid gray;">' . $ref.'<br>'.$ref_title;
				
                if ($row['Ex_plan'] != "NO") {
                    echo '<FONT color="red"><strong><sup>' . $row['Ex_plan'] . '</sup></strong></FONT>';
                }
                echo '</td>';

                echo '<td style="border-top:1px solid gray;border-bottom:1px solid gray;">';
                echo $row['Ref_Rev'];
                echo '</td>';

                echo '<td style="border-top:1px solid gray;border-bottom:1px solid gray;">';
                if ($row['Drawing_Path'] != "") {

                    $path_file = 'DRAWINGS\\IN_PROCESS\\' . $row['Drawing_Path'];
                    if (file_exists($path_file) == 0) {

                        $path_file = 'DRAWINGS\\OFFICIAL\\' . $row['Drawing_Path'];
                        if (file_exists($path_file) == 0) {
                            $path_file = 'DRAWINGS\\no_drawing.pdf';
                        }
                    }

                    /*echo '<div class="dropdown_prod_drawing">';*/
                    echo '<a target=_blank href="' . $path_file . '">' . $row['Prod_Draw'] . '</a>';

                    /*echo '<div class="dropdown_prod_drawing-content">';
                echo '<p><iframe src="DRAWINGS\\IN_PROCESS\\' . $row['Drawing_Path'] . '#toolbar=0&navpanes=0&scrollbar=0" width="400px" height="280px" scrollbar=no></iframe>
							</p>';
                echo '</div>';*/

                    echo '</div>';
                } else {
                    echo $row['Prod_Draw'];
                }
                echo '</td>';

                echo '<td style="border-top:1px solid gray;border-bottom:1px solid gray;">';
                echo $row['Prod_Draw_Rev'];
                echo '</td>';

                echo '<td style="border-top:1px solid gray;border-bottom:1px solid gray;">';
                echo $row['Doc_Type'] . '<br>' . $row['Proc_Type'];
                echo '</td>';

                echo '<td style="border-top:1px solid gray;border-bottom:1px solid gray;"><div id="Table_results">';
                // Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
                $nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");

                //$nmax = 30;
                $nmax = 0;
                if ((strlen($row['Requestor_Comments']) > $nmax)) {
                    echo '<div class="dropdown">';
                    echo '<span>
						 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:12px; opacity:1" >
					  </span>';
                    echo '<div class="dropdown-content">';
                    echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES) . '</p>';
                    echo '</div>';
                    echo '</div>';
                } else {
                    echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:12px; opacity:0.3;" >';
                }

                echo "<font size=3> | </font>";

                $nmax = 0;
                if ((strlen($row['General_Comments']) > $nmax)) {
                    echo htmlspecialchars(substr(nl2br($row['General_Comments']), 0, $nmax), ENT_QUOTES);
                    echo '<div class="dropdown">';
                    echo '<span>
						<img src="\Common_Resources\general_comment_icon_b.png" style="height:12px; opacity:1" >
					  </span>';
                    echo '<div class="dropdown-content">';
                    echo '<p><b>- <u>General Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['General_Comments']), ENT_QUOTES) . '</p>';
                    echo '</div>';
                    echo '</div>';
                } else {
                    echo '<img src="\Common_Resources\general_comment_icon_b.png" style="height:12px; opacity:0.3" >';
                }

                echo '</td>';

                $col_true = "#6DCB6D";
                $col_false = "#EEE5CC";
                $r = 13;

                //------------------------

                if ($row['VISA_BE_3'] == "") {
                    $col = $col_false;
                    $val = "";
                } else {
                    $col = $col_true;
                    $val = '</br>' . $row['VISA_BE_3'] . '<div id="date">' . $row['DATE_BE_3'] . '</div>';
                }
                echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> BE' . $val . '</div></td>';
                $r--;

                //------------------------
                if ($row['Doc_Type'] != "DOC" && $row['Inventory_Impact'] != "NO IMPACT") {
                    if ($row['VISA_Inventory'] == "") {
                        $col = $col_false;
                        $val = "";
                    } else {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Inventory'] . '<div id="date">' . $row['DATE_Inventory'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results">Invent.' . $val . '</div></td>';
                    $r--;
                }
                //------------------------

                if ($row['VISA_Product'] == "") {
                    $col = $col_false;
                    $val = "";
                } else {
                    $col = $col_true;
                    $val = '<br>' . $row['VISA_Product'] . '<div id="date">' . $row['DATE_Product'] . '</div>';
                }
                echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Product' . $val . '</div></td>';
                $r--;

                //------------------------

                if ($row['Doc_Type'] == "PUR" || $row['Doc_Type'] == "ASSY" || $row['Doc_Type'] == "DOC") {
                    if ($row['VISA_Quality'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_Quality'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Quality'] . '<div id="date">' . $row['DATE_Quality'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Quality' . $val . '</div></td>';
                    $r--;
                }

                //------------------------
                if ((substr($row['Prod_Draw'], 0, 2) == "GA" || substr($row['Prod_Draw'], 0, 2) == "FT") && $row['Project'] != "STAND") {
                    if ($row['VISA_Project'] == "") {
                        $col = $col_false;
                        $val = "";
                    } else {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Project'] . '<div id="date">' . $row['DATE_Project'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results">Project' . $val . '</div></td>';
                    $r--;
                }
                //------------------------

                if (($row['Doc_Type'] == "PUR" || ($row['Doc_Type'] != "PUR" && $row['Doc_Type'] != "DOC" && $row['Proc_Type'] != "E" && $row['Proc_Type'] != ""))) {
                    if ($row['VISA_PUR_1'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_PUR_1'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_PUR_1'] . '<div id="date">' . $row['DATE_PUR_1'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> RFQ' . $val . '</div></td>';
                    $r--;
                }

                //------------------------

                if ($row['Proc_Type'] == "F30" || ($row['Proc_Type'] == "" && $row['Doc_Type'] == "PUR")) {
                    if ($row['VISA_PUR_2'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_PUR_2'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_PUR_2'] . '<div id="date">' . $row['DATE_PUR_2'] . '</div>';
                    }
                    echo '<td style="background-color:' . $col . '; "><div id="Table_results"> Pris Dans' . $val . '</div></td>';
                    $r--;
                }

                //------------------------

                if ($row['Proc_Type'] == "E" || ($row['Proc_Type'] == "" && $row['Doc_Type'] != "PUR")) {
                    if ($row['VISA_Prod'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_Prod'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Prod'] . '<div id="date">' . $row['DATE_Prod'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Prod' . $val . '</div></td>';
                    $r--;
                }

                //------------------------

                if ($row['Proc_Type'] == "E" || ($row['Proc_Type'] == "" && $row['Doc_Type'] != "PUR")) {
                    if ($row['VISA_Supply'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_Supply'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Supply'] . '<div id="date">' . $row['DATE_Supply'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Supply' . $val . '</div></td>';
                    $r--;
                }

                //------------------------

                if ($row['Proc_Type'] == "E" || ($row['Proc_Type'] == "" && $row['Doc_Type'] != "PUR")) {
                    if ($row['VISA_Metro'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_Metro'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Metro'] . '<div id="date">' . $row['DATE_Metro'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Metro' . $val . '</div></td>';
                    $r--;
                }

                //------------------------


                if ($row['VISA_GID'] == "") {
                    $col = $col_false;
                    $val = "";
                } else {
                    $col = $col_true;
                    $val = '</br>' . $row['VISA_GID'] . '<div id="date">' . $row['DATE_GID'] . '</div>';
                }
                echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> SAP Core' . $val . '</div></td>';
                $r--;

                //------------------------

                if ($row['VISA_GID_2'] == "") {
                    $col = $col_false;
                    $val = "";
                } else {
                    $col = $col_true;
                    $val = '</br>' . $row['VISA_GID_2'] . '<div id="date">' . $row['DATE_GID_2'] . '</div>';
                }
                echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> SAP Prod' . $val . '</div></td>';
                $r--;

                //------------------------

                if (($row['Proc_Type'] == "E" || $row['Proc_Type'] == "") && $row['Doc_Type'] != "PUR" && $row['Doc_Type'] != "MACH" && $row['Doc_Type'] != "MOLD") {
                    if ($row['VISA_MOF'] == "") {
                        $col = $col_false;
                        $val = "";
                    } else {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_MOF'] . '<div id="date">' . $row['DATE_MOF'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Assy Rout.' . $val . '</div></td>';
                    $r--;
                }

                //------------------------
                if (($row['Proc_Type'] == "E" || $row['Proc_Type'] == "") && $row['Doc_Type'] != "PUR" && $row['Doc_Type'] != 'DOC') {
                    if ($row['VISA_ROUTING_ENTRY'] == "") {
                        $col = $col_false;
                        $val = "";
                    } else {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_ROUTING_ENTRY'] . '<div id="date">' . $row['DATE_ROUTING_ENTRY'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results">Rout. Entry' . $val . '</div></td>';
                    $r--;
                }
                //------------------------

                if ($row['Proc_Type'] == "F30" || $row['Proc_Type'] == "F" || ($row['Proc_Type'] == "" && $row['Doc_Type'] == "PUR")) {
                    if ($row['VISA_PUR_3'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_PUR_3'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_PUR_3'] . '<div id="date">' . $row['DATE_PUR_3'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> FIA' . $val . '</div></td>';
                    $r--;
                }

                //------------------------
                if ($row['Doc_Type'] != "DOC") {
                    if ($row['VISA_Finance'] == "") {
                        $col = $col_false;
                        $val = "";
                    } else {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Finance'] . '<div id="date">' . $row['DATE_Finance'] . '</div>';
                    }
                    $r--;

                    if ($r == 0) {
                        $bord = 'border-right:3px solid black';
                    } else {
                        $bord = '';
                    }
                    echo '<td  style="' . $bord . '; background-color:' . $col . '; "><div id="Table_results"> Costing' . $val . '</div></td>';
                }

                //------------------------

                /*if($r < 10 && $r > 0){
                while($r !=0){
                    echo '<td></td>';
                    $r--;
                }
            }*/
                if ($r > 0) {
                    echo '<td style="border-top: none; border-bottom: none; border-right:3px solid black;" colspan=' . $r . '><hr></td>';
                }

                //------------------------

                if ($row['Doc_Type'] == "PUR") {
                    if ($row['VISA_Metro'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_Metro'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Metro'] . '<div id="date">' . $row['DATE_Metro'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Metro' . $val . '</div></td>';
                    $r--;
                }

                //------------------------

                if ($row['Proc_Type'] == "F30" || $row['Proc_Type'] == "F" || ($row['Proc_Type'] == "" && $row['Doc_Type'] == "PUR")) {
                    if ($row['VISA_PUR_4'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_PUR_4'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_PUR_4'] . '<div id="date">' . $row['DATE_PUR_4'] . '</div>';
                    }
                    echo '<td style="background-color:' . $col . '; "><div id="Table_results"> RoHS/REACH' . $val . '</div></td>';
                    $r--;
                }

                //------------------------

                if ($row['Proc_Type'] == "F30" || $row['Proc_Type'] == "F" || ($row['Proc_Type'] == "" && $row['Doc_Type'] == "PUR")) {
                    if ($row['VISA_PUR_5'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_PUR_5'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_PUR_5'] . '<div id="date">' . $row['DATE_PUR_5'] . '</div>';
                    }
                    echo '<td style=";background-color:' . $col . '; "><div id="Table_results"> HTS/Origin' . $val . '</div></td>';
                    $r--;
                }

                //------------------------

                if ($row['Doc_Type'] == "MACH" || $row['Doc_Type'] == "MOLD") {
                    if ($row['VISA_Q_PROD'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_Q_PROD'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Q_PROD'] . '<div id="date">' . $row['DATE_Q_PROD'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Qual Prod' . $val . '</div></td>';
                    $r--;
                }

                //------------------------

                if ($row['Doc_Type'] == "ASSY" || $row['Doc_Type'] == "DOC") {
                    if ($row['VISA_Method'] == "") {
                        $col = $col_false;
                        $val = "";
                    } elseif ($row['VISA_Method'] != "") {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_Method'] . '<div id="date">' . $row['DATE_Method'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Method' . $val . '</div></td>';
                    $r--;
                }

                //-----------------------

                if (($row['Proc_Type'] == "E" || $row['Proc_Type'] == "") && $row['Doc_Type'] != "PUR" && $row['Doc_Type'] != "MACH" && $row['Doc_Type'] != "MOLD" && $row['Doc_Type'] != "DOC") {
                    if ($row['VISA_LABO'] == "") {
                        $col = $col_false;
                        $val = "";
                    } else {
                        $col = $col_true;
                        $val = '</br>' . $row['VISA_LABO'] . '<div id="date">' . $row['DATE_LABO'] . '</div>';
                    }
                    echo '<td  style="background-color:' . $col . '; "><div id="Table_results"> Labo' . $val . '</div></td>';
                    $r--;
                }
                echo '</tr>';
            }
            echo '</table>';
            echo '</td>';
            mysqli_close($mysqli);
            ?>
            </tr>
        </table>
    </form>
</body>

</html>