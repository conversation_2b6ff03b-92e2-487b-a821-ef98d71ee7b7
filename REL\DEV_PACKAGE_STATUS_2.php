<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />	
	
    <!--<link rel="stylesheet" type="text/css" href="REL_KPI_Form_styles.css">-->
	
	<?php
    // DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
    include('../REL_Connexion_DB.php');
	
	$requete='
			SELECT 
				count(*) as "Total", 
				tbl_released_drawing.rel_pack_num as "REL", 
				Project,
				count( case when Doc_Type="ASSY" AND Inventory_Impact not like "NO IMPACT" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 END) as "ASSY_IMPACT_PROJET",
				count( case when Doc_Type="ASSY" AND Inventory_Impact="NO IMPACT" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 END) as "ASSY_NO_IMPACT_PROJET",
				count( case when Doc_Type="ASSY" AND Inventory_Impact not like "NO IMPACT" AND (Project like "STAND" OR (Project not like "STAND" AND tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 END) as "ASSY_IMPACT_NO_PROJET",
				count( case when Doc_Type="ASSY" AND Inventory_Impact="NO IMPACT" AND (Project like "STAND" OR (Project not like "STAND" AND tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 END) as "ASSY_NO_IMPACT_NO_PROJET",
				count( case when Doc_Type="MACH" AND Inventory_Impact not like "NO IMPACT" THEN 1 END) as "MACH_IMPACT",
				count( case when Doc_Type="MACH" AND Inventory_Impact="NO IMPACT" THEN 1 END) as "MACH_NO_IMPACT",
				count( case when Doc_Type="MOLD" AND Inventory_Impact not like "NO IMPACT" THEN 1 END) as "MOLD_IMPACT",
				count( case when Doc_Type="MOLD" AND Inventory_Impact="NO IMPACT" THEN 1 END) as "MOLD_NO_IMPACT",
				count( case when Doc_Type="PUR" AND Inventory_Impact="NO IMPACT" AND (Proc_Type="" OR Proc_Type="F30") THEN 1 END) as "PUR_NO_IMPACT_F30",
				count( case when Doc_Type="PUR" AND Inventory_Impact NOT LIKE "NO IMPACT" AND (Proc_Type="" OR Proc_Type="F30") THEN 1 END) as "PUR_IMPACT_F30",
				count( case when Doc_Type="PUR" AND Inventory_Impact="NO IMPACT" AND Proc_Type="F" THEN 1 END) as "PUR_NO_IMPACT_F",
				count( case when Doc_Type="PUR" AND Inventory_Impact NOT LIKE "NO IMPACT" AND Proc_Type="F" THEN 1 END) as "PUR_IMPACT_F",
				count( case when Doc_Type="DOC" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 END) as "DOC_PROJET",
				count( case when Doc_Type="DOC" AND Project like "STAND" OR (Doc_Type="DOC" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 END) as "DOC_NO_PROJET",
				count( case when Doc_Type="" THEN 1 END) as "NA"
			FROM tbl_released_drawing
			LEFT JOIN tbl_released_package on tbl_released_package.rel_pack_num=tbl_released_drawing.rel_pack_num 
			WHERE Creation_VISA not like ""
			GROUP BY tbl_released_drawing.rel_pack_num
			ORDER BY tbl_released_drawing.rel_pack_num desc
			';
			
			
	$requete_a_valider='
			SELECT 
				count(*) as "Total", 
				tbl_released_drawing.rel_pack_num as "REL", 
				Project,
				sum( 
					( case when Doc_Type="ASSY" AND Inventory_Impact not like "NO IMPACT" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 ELSE 0 END)*14 +
					( case when Doc_Type="ASSY" AND Inventory_Impact="NO IMPACT" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 ELSE 0 END)*13 +
					( case when Doc_Type="ASSY" AND Inventory_Impact not like "NO IMPACT" AND (Project like "STAND" OR (Project not like "STAND" AND tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 ELSE 0 END)*13 + 
					( case when Doc_Type="ASSY" AND Inventory_Impact="NO IMPACT" AND (Project like "STAND" OR (Project not like "STAND" AND tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 ELSE 0 END)*11
					) as "ASSY",
				sum( 
					(case when Doc_Type="MACH" AND Inventory_Impact not like "NO IMPACT" THEN 1 ELSE 0 END)*11 +
					(case when Doc_Type="MACH" AND Inventory_Impact="NO IMPACT" THEN 1  ELSE 0 END)*10
					) as "MACH",
				sum(
					( case when Doc_Type="MOLD" AND Inventory_Impact not like "NO IMPACT" THEN 1 ELSE 0 END)*11 +
					( case when Doc_Type="MOLD" AND Inventory_Impact="NO IMPACT" THEN 1 ELSE 0 END)*10
					) as "MOLD",
				sum(
					( case when Doc_Type="PUR" AND Inventory_Impact="NO IMPACT" AND (Proc_Type="" OR Proc_Type="F30") THEN 1 ELSE 0 END)*11 +
					( case when Doc_Type="PUR" AND Inventory_Impact NOT LIKE "NO IMPACT" AND (Proc_Type="" OR Proc_Type="F30") THEN 1 ELSE 0 END)*12 +
					( case when Doc_Type="PUR" AND Inventory_Impact="NO IMPACT" AND Proc_Type="F" THEN 1 ELSE 0 END)*10 +
					( case when Doc_Type="PUR" AND Inventory_Impact NOT LIKE "NO IMPACT" AND Proc_Type="F" THEN 1 ELSE 0 END)*9
				) as "PUR",
				sum(
					( case when Doc_Type="DOC" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 ELSE 0 END)*11 +
					( case when Doc_Type="DOC" AND Project like "STAND" OR (Doc_Type="DOC" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 ELSE 0 END)*11
				) as "DOC",
				count( case when Doc_Type="" THEN 1 END) as "NA"
			FROM tbl_released_drawing
			LEFT JOIN tbl_released_package on tbl_released_package.rel_pack_num=tbl_released_drawing.rel_pack_num 
			WHERE Creation_VISA not like ""
			GROUP BY tbl_released_drawing.rel_pack_num
			ORDER BY tbl_released_drawing.rel_pack_num desc';
	
	
	// CONSTRUCTION DE LA LISTE DES COLONNES TYPE 'VISA' EXISTANTES DANS LES TABLES TBL_RELEASED_DRAWING ET TBL_RELEASED_PACKAGE
    $requete_col='
				SELECT COLUMN_NAME, TABLE_NAME 
				FROM INFORMATION_SCHEMA.COLUMNS 
				WHERE 
					COLUMN_NAME LIKE "%VISA%" AND 
					COLUMN_NAME NOT LIKE "VISA_LABO" AND
					COLUMN_NAME NOT LIKE "VISA_PUR_4" AND
					COLUMN_NAME NOT LIKE "VISA_PUR_5" AND
					COLUMN_NAME NOT LIKE "VISA_Q_PROD" AND
					COLUMN_NAME NOT LIKE "VISA_METHOD" AND
					COLUMN_NAME NOT LIKE "Creation_VISA" AND
					(TABLE_NAME like "tbl_released_drawing" or TABLE_NAME like "tbl_released_package")';

	$resultat_col = $mysqli->query($requete_col);
	while ($row_col = $resultat_col->fetch_assoc()) 
	{
		$col_name[] = array($row_col['COLUMN_NAME'],$row_col['TABLE_NAME']);
	}
	
	// CONSTRUCTION DE LA REQUETE POUR LISTER TOUTES LES DIFF (REL_PACK_NUM) ET LEUR NOMBRE DE CHAMPS DE TYPE 'VISA' NON NULL
	$k=0;
	$requete_check="";
	do {
		if ($requete_check=="")
		{
			$requete_check='SELECT tbl_released_drawing.rel_pack_num as "REL", SUM(';
		} else {
			$requete_check=$requete_check.' + ';
		}
		$requete_check=$requete_check.' (case when '.$col_name[$k][0].' not like "" THEN 1 ELSE 0 END) ';
		$k=$k+1;
	} while ($k < count($col_name));	
	$requete_check=$requete_check.') AS "VISA"
								FROM tbl_released_drawing 
								LEFT JOIN tbl_released_package ON tbl_released_drawing.rel_pack_num=tbl_released_package.rel_pack_num 
								GROUP BY tbl_released_drawing.rel_pack_num';
	
	// EXECUTION DE LA REQUETE ET CONSTRUCTION DE LA LISTE DE RESULTATS
	//print_r($requete_check);
	$resultat_check = $mysqli->query($requete_check);
	while ($row_check = $resultat_check->fetch_assoc()) 
	{
		$visa_in_rel[] = array($row_check['REL'],$row_check['VISA']);
	}


	$resultat = $mysqli->query($requete);
    while ($row = $resultat->fetch_assoc()) 
	{
		

		// RECHERCHE DU NUMERO DE DIFF (REL_PACK_NUM) CORRESPONDANT A LA LIGNE EN COURS POUR RETROUVER LE NOMBRE DE VISA CALCULES DANS LA LISTE VISA_IN_REL
		$visa=0;
		$k=0;
		do {
			if ($visa_in_rel[$k][0]!=$row['REL'])
			{
				$k=$k+1;
			} else {
				$visa=$visa_in_rel[$k][1];
				break;
			}
		} while ($k < count($visa_in_rel));
		
		$NEEDED_Total=0;
		$DOC_Total=$row['DOC_NO_PROJET']*10+$row['DOC_PROJET']*11;
		$PUR_Total=$row['PUR_NO_IMPACT_F30']*11+$row['PUR_IMPACT_F30']*12+$row['PUR_NO_IMPACT_F']*10+$row['PUR_IMPACT_F']*9; // +1 ajouté car la métro, bien que panier, est considéré en VISA critique pour les pieces achetées. Cela permet de faciliter le calcul (VISA_METRO etant critique pour tous les cas sauf dans le cas des articles F/F30)
		$MOLD_Total=$row['MOLD_NO_IMPACT']*10+$row['MOLD_IMPACT']*11;
		$MACH_Total=$row['MACH_NO_IMPACT']*10+$row['MACH_IMPACT']*11;
		$ASSY_Total=$row['ASSY_IMPACT_PROJET']*14+$row['ASSY_NO_IMPACT_PROJET']*13+$row['ASSY_IMPACT_NO_PROJET']*13+$row['ASSY_NO_IMPACT_NO_PROJET']*12;
		
		$NEEDED_Total=$DOC_Total+$PUR_Total+$MOLD_Total+$MACH_Total+$ASSY_Total;
		$progress_percent="-";
		if($NEEDED_Total>0 && $NEEDED_Total>=$visa)
		{
			$progress_percent=round(100*$visa/$NEEDED_Total, 0, PHP_ROUND_HALF_DOWN);
		} else if ($NEEDED_Total<$visa && $NEEDED_Total>0)
		{
			$progress_percent=100;
		} else {
			$progress_percent=0;
		}
		
		// CONSTRUCTION DE LA LISTE CONTENANT LES NUMEROS DE DIFF ET LEURS POURCENTAGES DE PROGRESSION ASSOCIES
		$diff_progress[] = array($row['REL'],$progress_percent);

    }
	
	// CONSTRUCTION DE LA LISTE SPECIFIQUE POUR L'AFFICHAGE GRAPHIQUE
	$diff_progress_graph="";
	$g=0;
	do {
		$diff_progress_graph=$diff_progress_graph."['".$diff_progress[$g][0]."', ".$diff_progress[$g][1]."], ";
		$g=$g+1;
	} while ($g<count($diff_progress));
	$diff_progress_graph=substr(rtrim($diff_progress_graph), 0, -1);
    mysqli_close($mysqli);
	
	
    ?>
	
	
	<style>
  .bold-green-font {
    font-weight: bold;
    color: green;
  }

  .bold-font {
    font-weight: bold;
  }

  .right-text {
    text-align: right;
  }

  .large-font {
    font-size: 15px;
  }

  .italic-darkblue-font {
    font-style: italic;
    color: darkblue;
  }

  .italic-purple-font {
    font-style: italic;
    color: purple;
  }

  .underline-blue-font {
    text-decoration: underline;
    color: blue;
  }

  .gold-border {
    border: 3px solid gold;
  }

  .deeppink-border {
    border: 3px solid deeppink;
  }

  .orange-background {
    background-color: orange;
  }

  .orchid-background {
    background-color: orchid;
  }

  .beige-background {
    background-color: beige;
  }

</style>
	
	<script type="text/javascript" src="https://www.gstatic.com/charts/loader.js"></script>
	<script>
	google.charts.load('current', {packages: ['corechart', 'table']});
	google.charts.setOnLoadCallback(drawBasic);

function drawBasic() {
	
	var data = new google.visualization.DataTable(); 
		data.addColumn('string', 'Diff');
		data.addColumn('number', 'Progres');
		data.addRows([
			<?php echo $diff_progress_graph; ?>
		]);


	var table = new google.visualization.Table(document.getElementById('chart_div'));
	var formatter = new google.visualization.BarFormat({base:0,  min: 0, max:100, width:'100'});
	formatter.format(data, 1); // Apply formatter to second column
	
	var cssClassNames = {
		'headerRow': 'italic-darkblue-font large-font bold-font',
		'tableRow': '',
		'oddTableRow': 'beige-background',
		'selectedTableRow': 'orange-background large-font',
		'hoverTableRow': '',
		'headerCell': 'gold-border',
		'tableCell': '',
		'rowNumberCell': 'underline-blue-font'
		};
	
	var options = {
		allowHtml: true, 
		showRowNumber: false, 
		width: '100%', 
		height: '100%', 
		cssClassNames: 'cssClassNames'
		};
		
		
	table.draw(data, options);

    }
	
	
	</script>
</head>
<body>	

	<div id="chart_div" style="height:97vh;border:1px solid black"></div>
	<div id="tst"></div>
</body>

</html>