<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta charset="utf-8" />

	<link rel="stylesheet" type="text/css" href="REL_FIN_Main_Form_styles.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


	<script>
		// function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné
		function chkName(id_record) {
			const visa = document.getElementById("User_Choice__" + id_record).value;

			if (visa.value == "" || visa.value == "%") {
				alert("Please fill in your name prior to validate.");
				return false;
			}

			var res = confirm("Are you sure you want to validate?");
			if (res == false) {
				return false;
			}

			data_update("signoff", id_record, 1);;

		}


		// MISE A JOUR DE LA BASE DE DONNEES EN VALDIATION ET EN CHANGEMENT DE DOC TYPE

		// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
		// Mise en place d'un bouton Save
		function data_update(action, id_record, validation_flag) {

			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() {
				// acces retour de process ou message utilisateur pour confirmation
			}

			var user_val = "";
			if (validation_flag == 1) {
				var user_val = document.getElementById("User_Choice__" + id_record).value
			}

			if (action == "signoff" || action == 1) {
				action = "signoff";
				const url_a = "REL_FIN_Action.php?ID=" + id_record +
					"&userid=" + user_val +
					"&action=" + action +
					"&comment=" + document.getElementById("comment__" + id_record).value;

				xhttp.open("GET", url_a);
				xhttp.send();
			}

		}

		// PERMET DE VALIDER EN MASSE LES DONNEES
		function mass_update() {
			var res = confirm("Are you sure to validate all the rows where you filled your signature in?");
			if (res == false) {
				visa.value = "";
				return false;
			}

			const data_table = document.getElementById("t02");
			const tr_list = data_table.getElementsByTagName("tr");
			//let updated_nb = 0;

			for (let i = 0; i <= tr_list.length; i++) {
				var id_record = tr_list[i].cells[0].textContent.trim(); // RECUPERE L'ID DE L'ENREGISTREMENT DANS LA TABLE DE LA BDD
				if (isNaN(id_record) == false) // VERIFIER QUE LES ID RECUPERES SOIENT DES NOMBRES/CHIFFRES
				{
					var user_name = document.getElementById("User_Choice__" + id_record).value;

					if (user_name != "" && user_name != "%") // SELECTIONNE LES LIGNES POUR LESQUELS L'UTILISATEUR A RENSEIGNE SON NOM POUR SIGNER
					{
						data_update("signoff", id_record, 1); // LANCEMENT DE LA FONCTION DE MISE A JOUR DE LA BDD
						//updated_nb=updated_nb + 1;
					}
				}
			}
			//alert(updated_nb + ' rows updated!');
		}
	</script>


</head>

<title>
	REL Pack - FINANCE Review
</title>

<body>


	<?php
	// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
	include('REL_Workflow_Conditions.php');
	?>


	<form enctype="multipart/form-data" action="" method="post">

		<table id="t01" border=0>

			<tr>
				<td colspan=10>
					<div id="Main_Title">
						FINANCE Review
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div id="FilterTitle">
						Package #

						<SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
										FROM  tbl_released_drawing
										WHERE ' . $Finance_Conditions . '
										ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Rel_Pack_Num_Choice'])) {
									if ($_POST['Rel_Pack_Num_Choice'] == $row['Rel_Pack_Num']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Rel_Pack_Num'] != "") {
									echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '"' . $sel . '>' . $row['Rel_Pack_Num'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
						<!--</datalist>-->
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Activity

						<SELECT name="Activity_Choice" type="submit" size="1" style="width:100px;font-size:9pt;height:17px" onchange="this.form.submit()">
							<OPTION value="%"></OPTION>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_package.Activity 
                                FROM tbl_released_package
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Finance_Conditions . ' 
                                ORDER BY tbl_released_package.Activity DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Activity_Choice'])) {
									if ($_POST['Activity_Choice'] == $row['Activity']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Activity'] != "") {
									echo '<OPTION value ="' . $row['Activity'] . '"' . $sel . '>' . $row['Activity'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Project

						<SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
							<OPTION value="%"></OPTION>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT Project 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Finance_Conditions . '
                                ORDER BY tbl_released_package.Project DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Project_Choice'])) {
									if ($_POST['Project_Choice'] == $row['Project']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Project'] != "") {
									echo '<OPTION value ="' . $row['Project'] . '"' . $sel . '>' . $row['Project'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Reference

						<input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Reference_Choice'])) {
																																							echo ' Value="' . $_POST['Reference_Choice'] . '">';
																																						} ?> </div>
				</td>

				<td>
					<div id="FilterTitle">
						Drawing

						<input type="text" size=20 name="Drawing_Choice" style="font-size:9pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Drawing_Choice'])) {
																																							echo ' Value="' . $_POST['Drawing_Choice'] . '">';
																																						} ?> </div>
				</td>

				<td>
					<div id="FilterTitle">
						Action

						<!--</div>

                    <div id="Filter">-->
						<SELECT name="Action_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Action 
                                FROM tbl_released_drawing
                                WHERE ' . $Finance_Conditions . '
                                ORDER BY tbl_released_drawing.Action ASC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Action_Choice'])) {
									if ($_POST['Action_Choice'] == $row['Action']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Action'] != "") {
									echo '<OPTION value ="' . $row['Action'] . '"' . $sel . '>' . $row['Action'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>

				<td>
					<div id="FilterTitle">
						Type

						<!-- </div>

                    <div id="Filter"> -->
						<SELECT name="Doc_Type_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Doc_Type 
                                FROM tbl_released_drawing
                                WHERE ' . $Finance_Conditions . '
                                ORDER BY tbl_released_drawing.Doc_Type ASC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Doc_Type_Choice'])) {
									if ($_POST['Doc_Type_Choice'] == $row['Doc_Type']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Doc_Type'] != "") {
									echo '<OPTION value ="' . $row['Doc_Type'] . '"' . $sel . '>' . $row['Doc_Type'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>

				<td>
					<div id="FilterTitle">
						Ex

						<!--</div>
					<div id="Filter">-->
						<SELECT name="Ex_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../SCM_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_ex.Ex 
                                FROM tbl_ex
                                ORDER BY tbl_ex.Ex ASC';
							$resultat = $mysqli_scm->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Ex_Choice'])) {
									if ($_POST['Ex_Choice'] == $row['Ex']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Ex'] != "") {
									echo '<OPTION value ="' . $row['Ex'] . '"' . $sel . '>' . $row['Ex'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli_scm);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Invent. Impact

						<!--</div>
					<div id="Filter">-->
						<SELECT name="Impact_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$mysqli->set_charset("utf8");
							$requete = 'SELECT DISTINCT tbl_inventory_impact.Inventory_Impact 
                                FROM tbl_inventory_impact
                                ORDER BY tbl_inventory_impact.Inventory_Impact ASC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Inventory_Impact'])) {
									if ($_POST['Inventory_Impact'] == $row['Inventory_Impact']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Inventory_Impact'] != "") {
									echo '<OPTION value ="' . $row['Inventory_Impact'] . '"' . $sel . '>' . $row['Inventory_Impact'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>

				<!-- !!!!!!!!!! NOUVEAU !!!!!!!!!! -->
				<!-- bouton reset -->
				<td>
					<input type="button" class="btn grey" onclick="window.location.href = 'REL_FIN_Main_Form.php';" style="font-size:8pt; width:45px;height:18px;vertical-align:middle;text-align:center" value="Reset" />
				</td>
				<!-- ------------ -->
				<!-- !!!!!!!!!!!!!!!!!!!!! -->


			</tr>



			<!--- Vérification des valeurs --->
			<?php

			if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
				$rel_pack_num_choice = "%";
			} else {
				$rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
			}

			if (isset($_POST['Activity_Choice']) == false) {
				$activity_choice = "%";
			} else {
				$activity_choice = $_POST['Activity_Choice'];
			}

			if (isset($_POST['Project_Choice']) == false) {
				$project_choice = "%";
			} else {
				$project_choice = $_POST['Project_Choice'];
			}

			if (isset($_POST['Reference_Choice']) == false) {
				$reference_choice = "%";
			} else {
				if (strlen($_POST['Reference_Choice']) > 0) {
					$reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
				} else {
					$reference_choice = "%";
				}
			}

			if (isset($_POST['Drawing_Choice']) == false) {
				$drawing_choice = "%";
			} else {
				if (strlen($_POST['Drawing_Choice']) > 0) {
					$drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
				} else {
					$drawing_choice = "%";
				}
			}

			if (isset($_POST['Action_Choice']) == false) {
				$action_choice = "%";
			} else {
				$action_choice = $_POST['Action_Choice'];
			}

			if (isset($_POST['Doc_Type_Choice']) == false) {
				$doc_type_choice = "%";
			} else {
				$doc_type_choice = $_POST['Doc_Type_Choice'];
			}

			if (isset($_POST['Ex_Choice']) == false) {
				$Ex_Choice = "%";
			} else {
				$Ex_Choice = $_POST['Ex_Choice'];
			}

			if (isset($_POST['Impact_Choice']) == false) {
				$Impact_Choice = "%";
			} else {
				$Impact_Choice = $_POST['Impact_Choice'];
			}

			//$query_1 = 'SELECT * FROM tbl_dmo where Status like "'.$Status_choice.'" && Decision like "'.$Decision_choice.'" && DMO like "'.$DMO_filter.'" && Requestor_Name like "'.$Requestor_choice.'" && Product_Range like "'.$Product_Range_choice.'" && Description like "'.$Description_filter.'" && Ex like "'.$Ex_choice.'" && Eng_Owner like "'.$EngOwner_choice.'" ORDER BY DMO DESC;';

			$query_1 = 'SELECT *
                FROM tbl_released_package 
                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE 
				
					' . $Finance_Conditions . '
					
                    AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                    AND tbl_released_package.Activity like "' . $activity_choice . '"
                    AND tbl_released_package.Project like "' . $project_choice . '"
                    AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                    AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                    AND tbl_released_drawing.Action like "' . $action_choice . '"
                    AND tbl_released_drawing.Doc_Type like "' . $doc_type_choice . '"
                    AND tbl_released_drawing.Ex like "' . $Ex_Choice . '"
					AND tbl_released_drawing.Inventory_Impact like "' . $Impact_Choice . '"
               ORDER BY tbl_released_drawing.reference DESC';

			include('../REL_Connexion_DB.php');
			$resultat = $mysqli->query($query_1);
			$rowcount = mysqli_num_rows($resultat);

			echo '<tr><td colspan=3><div id="Result_info">Number of results: ' . $rowcount . '&nbsp&nbsp&nbsp';
			echo '</td>';


			// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
			// Lien pour téléchargement du csv (export excel) généré par le bouton Excel Extraction
			$csv_file_name = '.\Report\\' . date("Y_m_d_H_i") . '_FIN_Export.csv';

			echo '<td colspan=5 style="text-align:right;">';
			// BOUTON EXTRACTION EXCEL
			echo '<input onclick="" type="submit" id="b1" class="btn grey" style="font-size:7pt; width:90px;height:15px;vertical-align:middle;text-align:center" name="excel_extract" value="Excel Extraction" title="Excel Extraction"/>';

			if (isset($_POST['excel_extract'])) {
				echo '<a href="' . $csv_file_name . '">' . $csv_file_name . '<a>';
			}
			echo '</td>';

			// BOUTON DE MISE A JOUR EN MASSE
			echo '<td style="text-align:right; padding-right:10px;">
		<input onclick="return mass_update()" type="submit" class="btn green" style="font-size:7pt; width:90px;height:15px;vertical-align:middle;text-align:center" name="mass_update_btn" value="Mass Validation" title="Mass Validation of all the row you indicated your name in" />
		</td>';
			// -----


			echo '</div>';
			echo '</tr>';
			echo '</table>';

			// Création des entetes du tableau
			echo '<table id="t02">';
			echo '<thead>';
			echo '	<th style="width:40px;background-color: rgb(16, 112, 177);">Pack #</th>';
			echo '	<th style="width:70px;background-color: rgb(16, 112, 177);">Activity</th>';
			echo '	<th style="min-width:120px;background-color: rgb(16, 112, 177);">Reference</th>';
			echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
			echo '	<th style="width:150px;background-color: rgb(16, 112, 177);">Prod Drawing</th>';
			echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
			echo '	<th style="min-width:90px;background-color: rgb(16, 112, 177);">Title</th>';
			echo '	<th style="width:40px;background-color: rgb(16, 112, 177);">Action</th>';
			echo '	<th style="width:85px;background-color: rgb(16, 112, 177);">Inventory</th>';
			echo '	<th style="width:100px;background-color: rgb(16, 112, 177);">Mat Type</th>';
			echo '	<th style="width:60px;background-color: rgb(16, 112, 177);">Type</th>';
			echo '	<th style="width:30px;background-color: rgb(16, 112, 177);">CLS</th>';
			echo '	<th style="width:30px;background-color: rgb(16, 112, 177);">MOQ</th>';
			echo '	<th style="width:120px;background-color: rgb(16, 112, 177);">SAP Data</th>';
			echo '	<th title="Requestor and other departments remarks" style="width:70px;background-color: rgb(16, 112, 177);">Remarks</th>';
			echo '	<th>Comments</th>';
			echo '	<th style="width:110px;">Validation</th>';
			echo '</thead>';


			$i = 0;

			// création du tableau dans une iframe
			while ($row = $resultat->fetch_assoc()) {
				echo '<tr id ="' . $i . '">
				<td hidden >
					' . $row['ID'] . '
				</td>';

				echo '<td>';

				$nmax = 0;
				if ((strlen($row['Observations']) > $nmax)) {
					echo htmlspecialchars(substr(nl2br($row['Observations']), 0, $nmax), ENT_QUOTES);
					echo '<div class="dropdown_observations">';
					echo '<span name="Pack" id="Rel_Pack_Num"><b>' . $row['Rel_Pack_Num'] . '</b></span>';
					echo '<div class="dropdown_observations-content">';
					echo '<p id="observation"><b><u>Package Observations</u></b><br>' . htmlspecialchars_decode(nl2br($row['Observations']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo $row['Rel_Pack_Num'];
				}

				echo '</td>';

				echo '<td id="Activity" name="activity">
					' . $row['Activity'] . '<br>' . $row['Project'] . '
				</td>
				<td name="reference">
					<div id="ref">' . $row['Reference'] ;
				if ($row['Ex'] != "NO") {
					echo '<FONT color="red"><strong><sup>' . $row['Ex'] . '</sup><strong></FONT>';
				}
				echo '</div>
				</td>
				<td id="ref_rev" name="ref_rev">
					' . $row['Ref_Rev'] . '
				</td>';

				// echo '<td name="prod_draw">';
				// if ($row['Drawing_Path'] != "") {
				// 	$path_file = 'DRAWINGS\\IN_PROCESS\\' . $row['Drawing_Path'];
				// 	if (file_exists($path_file) == 0) {

				// 		$path_file = 'DRAWINGS\\OFFICIAL\\' . $row['Drawing_Path'];
				// 		if (file_exists($path_file) == 0) {
				// 			$path_file = "";
				// 		}
				// 	}
				// 	if ($path_file != "") {
				// 		echo '<div class="dropdown_prod_drawing">';
				// 		echo '<a target=_blank href="' . $path_file . '">' . $row['Prod_Draw'] . '</a>';
				// 		echo '<div class="dropdown_prod_drawing-content">';
				// 		echo '<p><iframe src="' . $path_file . '#toolbar=0&navpanes=0&scrollbar=0" width="400px" height="280px" scrolbar=no></iframe></p>';
				// 		echo '</div>';
				// 		echo '</div>';
				// 	} else {
				// 		echo $row['Prod_Draw'];
				// 	}
				// } else {
				// 	echo '<div id="id="prod_draw">' . $row['Prod_Draw'] . '</div>';
				// }
				// echo '</td>';
				include('NO_PREVIEW.php');

				echo '<td id="prod_draw_rev" name="prod_draw_rev">
					' . $row['Prod_Draw_Rev'] . '
				</td>
				<td id="ref_title" name="title">
					' . $row['Ref_Title'] . '
				</td>
				<td id="action" name="action">';
				// Si la ligne Action est égal à Modification alors on raccourci le mot pour ecrire "modif" à la place
				if ($row['Action'] == "Modification") {
					echo substr($row['Action'], 0, 5);
				} else {
					echo $row['Action'];
				}
				//---------

				echo '</td>
				<td id="inventory_impact" name="invent_impact">
					' . $row['Inventory_Impact'] . '
				</td>
				<td id="mat_type" name="mat_type">
					' . $row['Material_Type'] . '
					</td>
				<td name="doc_type" style="width:40px">
				
				<div id="doc_type">' . $row['Doc_Type'];
				echo '<br><div id="proc_type>"' . $row['Proc_Type'] . '</div>';
				if ($row['Internal_Mach_Rec'] == 1) {
					echo '<img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15">';
				}
				echo '</div>';
				echo '	</td>

					<td id="cls" name="cls">
					' . $row['CLS'] . '
					</td>
					<td id="moq" name="moq">
					' . $row['MOQ'] . '
					</td>';


				echo '<td>';
				echo '<table id="t04">';
				echo '	<tr>';
				echo '		<td>';
				echo '			Valorisation: ';
				echo '		</td>';
				$class_val = "-";
				if ($row['Mat_Prod_Type'] == "FERT") {
					$class_val = "7920";
				}
				if ($row['Mat_Prod_Type'] == "HALB" && ($row['Proc_Type'] == "F" || $row['Proc_Type'] == "E")) {
					$class_val = "7900";
				}
				if ($row['Mat_Prod_Type'] == "HALB" && $row['Proc_Type'] == "F30") {
					$class_val = "7910";
				}
				if ($row['Mat_Prod_Type'] == "VERP") {
					$class_val = "3050";
				}
				if ($row['Mat_Prod_Type'] == "ROH") {
					if ($row['Doc_Type'] == "DOC") {
						$class_val = "3000";
					} else {
						$class_val = "3030";
					}
				}
				echo '		<td>';
				echo 			$class_val;
				echo '		</td>';
				echo '	</tr>';
				echo '	<tr>';
				echo '		<td>';
				echo '			Code Prix: ';
				echo '		</td>';
				$code_prix = "-";
				if ($row['Proc_Type'] == "F" || $row['Proc_Type'] == "F30") {
					$code_prix = "V";
				} else if ($row['Proc_Type'] == "E") {
					$code_prix = "S";
				}
				echo '		<td>';
				echo 			$code_prix;
				echo '		</td>';
				echo '	</tr>';


				echo '	<tr>';
				echo '		<td>';
				echo '			Grp. Frais Gen.: ';
				echo '		</td>';
				$frai_gen = "-";
				if ($row['Mat_Prod_Type'] != "FERT" && ($row['Proc_Type'] == "F" || $row['Proc_Type'] == "F30")) {
					$frai_gen = "ROH";
				} else if ($row['Mat_Prod_Type'] != "VERP" || $row['Mat_Prod_Type'] != "ROH") {
					$frai_gen = "ROH";
				}

				echo '		<td>';
				echo 			$frai_gen;
				echo '		</td>';
				echo '	</tr>';

				echo '</table>';

				echo '</td>';


				echo '<td name="req_comm">';
				// Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
				$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");

				//$nmax = 30;
				$nmax = 0;
				if ((strlen($row['Requestor_Comments']) > $nmax)) {
					echo '<div class="dropdown">';
					echo '<span>
								 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
							  </span>';
					echo '<div class="dropdown-content">';
					echo '<p id="req_com"><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
				}

				echo "<font size=4> | </font>";

				$nmax = 0;
				if ((strlen($row['General_Comments']) > $nmax)) {
					echo htmlspecialchars(substr(nl2br($row['General_Comments']), 0, $nmax), ENT_QUOTES);
					echo '<div class="dropdown">';
					echo '<span>
								<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:1" >
							  </span>';
					echo '<div class="dropdown-content">';
					echo '<p id="general_com"><b>- <u>General Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['General_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:0.3" >';
				}

				echo '</td>';



				// DEBUT CHAMP SAISIE PAR L'UTILISATEUR



				echo '<td>
                    <textarea tabindex="' . (1000 + $i) . '" id="comment__' . $row['ID'] . '" style="background-color:transparent;font-family:Tahoma;font-size:8pt;height:30px;width:94%;vertical-align:middle" ></textarea>
                </td>';


				echo '<td  style="text-align:center">
                        <SELECT tabindex="' . (2000 + $i) . '" id="User_Choice__' . $row['ID'] . '" name="user_name" type="submit" size="1" style="width:95%;font-size:7.5pt;height:17px;">
                            <option value="%"></option>';

				include('../SCM_Connexion_DB.php');
				$requete_5 = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
										  FROM tbl_user
										  WHERE UPPER(tbl_user.Department) like "%Finance%"';

				$resultat_5 = $mysqli_scm->query($requete_5);

				while ($row4 = $resultat_5->fetch_assoc()) {
					//if (strtoupper(substr($row['Doc_Type'], 0, 3)) == strtoupper(substr($row4['Department'], 0, 3))) {
					echo '<OPTION value ="' . $row4['Fullname'] . '">' . $row4['Fullname'] . '</option><br/>';
					//}
				}
				mysqli_close($mysqli_scm);

				echo '  </SELECT>
                        <input name="saving_form" onclick="return data_update(1,' . $row['ID'] . ',0)" type="submit" class="btn orange" style="font-size:7pt;margin-left:-5px; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Save" title="Save the current data without validating it" />
						&nbsp&nbsp
						<input name="valid_form" onclick="return chkName(' . $row['ID'] . ')" type="submit" class="btn blue2" style="font-size:7pt; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Sign" title="Sign off the current drawing" />					
					</td>
				</tr>';

				$i = $i + 1;
			}

			?>

		</table>
	</form>

</body>

<script>
	let b1 = document.getElementById('b1');

	b1.addEventListener("click", miseEnAttente);

	function miseEnAttente() {
		setTimeout(download_drawing, 2000);
	}

	function download_drawing() {
		// var date = new Date();
		// let formatted_date = date.getFullYear() + "_0" + (date.getMonth() + 1) + "_" + date.getDate() + "_" + (date.getHours() - 1) + "_" + date.getMinutes() + "_" + date.getSeconds();

		// var name = formatted_date + '_FIN_Export.csv';
		// var full_link = "\\RELEASE\\Report\\" + name;
		// const anchor = document.createElement('a');
		// anchor.href = full_link;
		// anchor.download = name;
		// anchor.click();
	}
</script>

</html>

<?php

// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// AU click sur le bouton excel_extract, on va générer un fichier excel

if (isset($_POST['excel_extract'])) {

	$table = [];
	$table[] = ['Pack', 'Activity', 'Reference', 'R', 'Prod Drawing', 'R', 'Title', 'Action', 'Inventory', 'Mat Type', 'Doc_Type', 'Proc_Type', 'CLS', 'MOQ', 'Valorisation', 'Code Prix', 'Grp. Frais Gen', 'Observations', 'Requestor Comments', 'General Comments'];
	

	$resultat = $mysqli->query($query_1);

	//Parcourons ces enregistrements et insérons les dans notre tableau
	while ($row = $resultat->fetch_assoc()) {
		$table[] = [$row['Rel_Pack_Num'], $row['Activity'], $row['Reference'], $row['Ref_Rev'], $row['Prod_Draw'], $row['Prod_Draw_Rev'], $row['Ref_Title'], $row['Action'], $row['Inventory_Impact'], $row['Material_Type'], $row['Doc_Type'], $row['Proc_Type'], $row['CLS'], $row['MOQ'], $class_val, $code_prix, $frai_gen, $row['Observations'], $row['Requestor_Comments'], $row['General_Comments']];
	}

	//File name defintion
	$csv_file_name = '.\Report\\' . date("Y_m_d_H_i") . '_FIN_Export.csv';

	//CSV file opening
	$fichier_csv = fopen($csv_file_name, 'w');

	//afficher correctement par exemple les caractères accentués
	fprintf($fichier_csv, chr(0xEF) . chr(0xBB) . chr(0xBF));

	//Parcourer le tableau et écrivons dans le fichier CSV avec la fonction fputcsv
	foreach ($table as $ligne) {
		fputcsv($fichier_csv, $ligne, ";");
	}

	//Fermer maintenant le fichier
	fclose($fichier_csv);

	//echo "<br><a href='" .$csv_file_name. "'>" .$csv_file_name. "</a>created !";
}
mysqli_close($mysqli);
?>