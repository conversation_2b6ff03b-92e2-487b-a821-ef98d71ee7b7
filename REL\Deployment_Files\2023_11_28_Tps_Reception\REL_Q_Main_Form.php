<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_Q_Main_Form_styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
    <link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">



    <script>
        // function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné

        function chkName() {
            const visa = document.getElementById("User_Choice");
            if (visa.value == "" || visa.value == "%") {
                alert("Please indicate your name prior to validate");
                return false;
            }
            var res = confirm("Etes vous sur de vouloir valider ?");
            if (res == false) {
                return false;
            }
            var id_record = document.getElementById("id_to_update").value;
            data_update("signoff", id_record, 1);

        }

        // function qui permet de na pas valider et envoyer les données dans la bdd si le type n'est pas donné
        function chkChange() {
            const visa = document.getElementById("doc_type_change");
            if (visa.value == "" || visa.value == "%") {
                alert("Please indicate the new doc type prior to validate");
                return false;
            }
            var res = confirm("Are you sure to validate that row?");
            if (res == false) {
                visa.value = "";
                return false;
            }
            var id_record = document.getElementById("id_to_update").value;
            data_update("doctype_change", id_record, 1);
        }


        function selected_page(picked_page, i_val) {
            document.getElementsByClassName("picked_page")[0].setAttribute('class', "unpicked_page");
            picked_page.setAttribute('class', "picked_page");
        }

        function data_update(action, id_record, validation_flag) {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                // acces retour de process ou message utilisateur pour confirmation
            }

            // ETABLISSEMENT DES EXIGEANCES DOCUMENTAIRES
            var doc_req_value = document.getElementsByClassName("doc_req");
            var doc_req_final = '';
            for (let i = 0; i < doc_req_value.length; i++) {
                if (document.getElementsByClassName("doc_req")[i].checked == true) {
                    if (doc_req_final == "") {
                        doc_req_final = document.getElementsByClassName("doc_req")[i].value;
                    } else {
                        doc_req_final = doc_req_final + ";" + document.getElementsByClassName("doc_req")[i].value;
                    }
                } else {

                }
            }
            // --------

            // ETABLISSEMENT DES INSPECTIONS
            var insp_value = document.getElementsByClassName("inspection");
            var insp_value_final = '';
            for (let i = 0; i < insp_value.length; i++) {
                if (document.getElementsByClassName("inspection")[i].checked == true) {
                    if (insp_value_final == "") {
                        insp_value_final = document.getElementsByClassName("inspection")[i].value;
                    } else {
                        insp_value_final = insp_value_final + ";" + document.getElementsByClassName("inspection")[i].value;
                    }
                } else {

                }
            }
            // --------
            // Pour SAVING

            var user_val = "";
            if (validation_flag == 1) {
                var user_val = document.getElementById("User_Choice").value;
            }
            if (id_record == 0) {
                id_record = document.getElementById("id_to_update").value;
            }

            if (action == "signoff" || action == 1) {
                action = "signoff";
                const url_a = "REL_Q_Action.php?ID=" + id_record +
                    "&doc_req=" + doc_req_final +
                    "&insp=" + insp_value_final +
                    "&control_routing=" + document.getElementById("Control_Routing").value +
                    "&dynam=" + document.getElementById("dyn_rule").value +
                    "&userid=" + user_val +
                    "&owner=" + document.getElementById("User_Choice").value +
                    "&action=" + action +
                    "&comment=" + document.getElementById("comment").value;


                //navigator.clipboard.writeText(url_a);

                xhttp.open("GET", url_a);
                xhttp.send();
            } else if (action == "doctype_change") {
                const url_b = "REL_Q_Action.php?ID=" + document.getElementById("id_to_update").value +
                    "&doc_type_change=" + document.getElementById("doc_type_change").value +
                    "&action=" + action;
                xhttp.open("GET", url_b);
                xhttp.send();
            }


        }
    </script>



</head>

<title>
    REL Pack - Quality Review
</title>

<body>

    <?php
    // DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
    include('REL_Workflow_Conditions.php');
    ?>


    <form enctype="multipart/form-data" action="" method="post">

        <table id="t01" border=0>

            <tr>
                <td colspan=10>
                    <div id="Main_Title">
                        Quality Review - Release Process
                    </div>
                </td>
            </tr>

            <tr>
                <td>
                    <div id="FilterTitle">
                        Package #
                        <SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');

                            $requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
										FROM tbl_released_package 
										LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
										WHERE ' . $Quality_Conditions . '                                             
										ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';

                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Rel_Pack_Num_Choice'])) {
                                    if ($_POST['Rel_Pack_Num_Choice'] == $row['Rel_Pack_Num']) {
                                        $sel = "SELECTED";
                                    } else {
                                    }
                                }
                                echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '"' . $sel . '>' . $row['Rel_Pack_Num'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Activity
                        <SELECT name="Activity_Choice" type="submit" size="1" style="width:100px;font-size:9pt;height:17px" onchange="this.form.submit()">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_package.Activity 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Quality_Conditions . ' 
                                ORDER BY tbl_released_package.Activity DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Activity_Choice'])) {
                                    if ($_POST['Activity_Choice'] == $row['Activity']) {
                                        $sel = "SELECTED";
                                    }
                                }
                                echo '<OPTION value ="' . $row['Activity'] . '" ' . $sel . '>' . $row['Activity'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Project
                        <SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT Project 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Quality_Conditions . ' 
                                ORDER BY tbl_released_package.Project DESC';
                            $resultat = $mysqli->query($requete);

                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Project_Choice'])) {
                                    if ($_POST['Project_Choice'] == $row['Project']) {
                                        $sel = "SELECTED";
                                    }
                                }
                                echo '<OPTION value ="' . $row['Project'] . '" ' . $sel . '>' . $row['Project'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Reference
                        <input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;" onblur="this.form.submit()" <?php if (isset($_POST['Reference_Choice'])) {
                                                                                                                                                            echo 'Value="' . $_POST['Reference_Choice'] . '">';
                                                                                                                                                        } ?> </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Drawing
                        <input type="text" size=20 name="Drawing_Choice" style="font-size:9pt;height:9pt;width:100pt;" onblur="this.form.submit()" <?php if (isset($_POST['Drawing_Choice'])) {
                                                                                                                                                        echo 'Value="' . $_POST['Drawing_Choice'] . '">';
                                                                                                                                                    } ?> </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Action
                        <SELECT name="Action_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Action 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Quality_Conditions . ' 
                                ORDER BY tbl_released_drawing.Action ASC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Action_Choice'])) {
                                    if ($_POST['Action_Choice'] == $row['Action']) {
                                        $sel = "SELECTED";
                                    }
                                }
                                echo '<OPTION value ="' . $row['Action'] . '" ' . $sel . '>' . $row['Action'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Type
                        <SELECT name="Doc_Type_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Doc_Type 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Quality_Conditions . ' 
                                ORDER BY tbl_released_drawing.Doc_Type ASC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Doc_Type_Choice'])) {
                                    if ($_POST['Doc_Type_Choice'] == $row['Doc_Type']) {
                                        $sel = "SELECTED";
                                    }
                                }
                                echo '<OPTION value ="' . $row['Doc_Type'] . '" ' . $sel . '>' . $row['Doc_Type'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Proc.
                        <SELECT name="Proc_Type_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Proc_Type 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Quality_Conditions . ' 
                                ORDER BY tbl_released_drawing.Proc_Type ASC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                if ($row['Proc_Type'] <> "") {
                                    $sel = "";
                                    if (isset($_POST['Proc_Type_Choice'])) {
                                        if ($_POST['Proc_Type_Choice'] == $row['Proc_Type']) {
                                            $sel = "SELECTED";
                                        }
                                    }
                                    echo '<OPTION value ="' . $row['Proc_Type'] . '" ' . $sel . '>' . $row['Proc_Type'] . '</option><br/>';
                                }
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Ex
                        <SELECT name="Ex_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../SCM_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_ex.Ex 
                                FROM tbl_ex
                                ORDER BY tbl_ex.Ex ASC';
                            $resultat = $mysqli_scm->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel = "";
                                if (isset($_POST['Ex_Choice'])) {
                                    if ($_POST['Ex_Choice'] == $row['Ex']) {
                                        $sel = "SELECTED";
                                    }
                                }
                                echo '<OPTION value ="' . $row['Ex'] . '" ' . $sel . '>' . $row['Ex'] . '</option><br/>';
                            }
                            mysqli_close($mysqli_scm);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <div id="">
                        Owner

                        <SELECT name="Quality_Owner_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Quality_Owner 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Quality_Conditions . '';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                if ($row['Quality_Owner'] <> "") {
                                    $sel = "";
                                    if (isset($_POST['Quality_Owner_Choice'])) {
                                        if ($_POST['Quality_Owner_Choice'] == $row['Quality_Owner']) {
                                            $sel = "SELECTED";
                                        }
                                    }
                                    echo '<OPTION value ="' . $row['Quality_Owner'] . '" ' . $sel . '>' . $row['Quality_Owner'] . '</option><br/>';
                                }
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <input type="button" class="btn grey" onclick="window.location.href = 'REL_Q_Main_Form.php';" style="font-size:8pt; width:45px;height:18px;vertical-align:middle;text-align:center" value="Reset" />
                </td>
            </tr>



            <!--- Vérification des valeurs --->
            <?php

            if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
                $rel_pack_num_choice = "%";
            } else {
                $rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
            }

            if (isset($_POST['Activity_Choice']) == false) {
                $activity_choice = "%";
            } else {
                $activity_choice = $_POST['Activity_Choice'];
            }

            if (isset($_POST['Project_Choice']) == false) {
                $project_choice = "%";
            } else {
                $project_choice = $_POST['Project_Choice'];
            }

            if (isset($_POST['Reference_Choice']) == false) {
                $reference_choice = "%";
            } else {
                if (strlen($_POST['Reference_Choice']) > 0) {
                    $reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
                } else {
                    $reference_choice = "%";
                }
            }

            if (isset($_POST['Drawing_Choice']) == false) {
                $drawing_choice = "%";
            } else {
                if (strlen($_POST['Drawing_Choice']) > 0) {
                    $drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
                } else {
                    $drawing_choice = "%";
                }
            }

            if (isset($_POST['Action_Choice']) == false) {
                $action_choice = "%";
            } else {
                $action_choice = $_POST['Action_Choice'];
            }

            if (isset($_POST['Doc_Type_Choice']) == false) {
                $doc_type_choice = "%";
            } else {
                $doc_type_choice = $_POST['Doc_Type_Choice'];
            }

            if (isset($_POST['Proc_Type_Choice']) == false) {
                $proc_type_choice = "%";
            } else {
                $proc_type_choice = $_POST['Proc_Type_Choice'];
            }

            if (isset($_POST['Ex_Choice']) == false) {
                $Ex_Choice = "%";
            } else {
                $Ex_Choice = $_POST['Ex_Choice'];
            }

            if (isset($_POST['Quality_Owner_Choice']) == false) {
                $Quality_Owner_Choice = "%";
            } else {
                $Quality_Owner_Choice = $_POST['Quality_Owner_Choice'];
            }

            // Création des filtres
            $query_1 = 'SELECT *
                        FROM tbl_released_package 
                        INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                        WHERE  ' . $Quality_Conditions . ' 
							
                            AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                            AND tbl_released_package.Activity like "' . $activity_choice . '"
                            AND tbl_released_package.Project like "' . $project_choice . '"
                            AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                            AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                            AND tbl_released_drawing.Action like "' . $action_choice . '"
                            AND tbl_released_drawing.Doc_Type like "' . $doc_type_choice . '"
                            AND tbl_released_drawing.Proc_Type like "' . $proc_type_choice . '"
                            AND tbl_released_drawing.Ex like "' . $Ex_Choice . '"
                            AND tbl_released_drawing.Quality_Owner like "' . $Quality_Owner_Choice . '"
                        ORDER BY tbl_released_drawing.reference DESC
						';

            include('../REL_Connexion_DB.php');
            $resultat = $mysqli->query($query_1);
            $rowcount = mysqli_num_rows($resultat);

            if (isset($_POST['nb_per_page'])) // NOMBRE D'ENREGISTREMENT MAX PAR PAGE
            {
                $limit_val = $_POST['nb_per_page'];
            } else {
                $limit_val = 15;
            }

            echo '<tr><td colspan=4 style="text-align:center;padding-top:5px; padding-bottom:3px;">';
            echo '<table id="page_table" border=0>';
            echo '	<tr><td style="padding-left:5px">Pages: </td>';
            $nb_page = ceil($rowcount / $limit_val); // CALCUL DU NOMBRE DE PAGES EN FAISANT L'ARRONDI SUPERIEUR
            $i = 1;
            do {
                if ($i == 1) {
                    $class_val = "picked_page";
                } else {
                    $class_val = "unpicked_page";
                }
                echo '<td style="text-align:center" name="page_td" id="Page_' . $i . '" onclick="selected_page(this,' . $i . ')" class="' . $class_val . '">
					  <a href="REL_Q_Item_List.php?
							start=' . ($i - 1) * $limit_val . '
							&nb=' . $limit_val . '
							&rel_pack_num_choice=' . $rel_pack_num_choice . '
							&project_choice=' . $project_choice . '
							&activity_choice=' . $activity_choice . '
							&reference_choice=' . $reference_choice . '
							&drawing_choice=' . $drawing_choice . '
							&action_choice=' . $action_choice . '
							&doc_type_choice=' . $doc_type_choice . '
							&proc_type_choice=' . $proc_type_choice . '
							&Ex_Choice=' . $Ex_Choice . '
							&Quality_Owner_Choice=' . $Quality_Owner_Choice . '
							"
						target="Item_List_Frame"
						style="text-align:center;text-decoration:none;color:black"
						>' . $i . '</a></td>';
                $i = $i + 1;
            } while ($i <= $nb_page);

            echo '<td style="width:135px">- <input type="text" name="nb_per_page" style="text-align:center;font-size:8pt;height:9pt;width:10pt;" onblur="this.form.submit()"  Value="' . $limit_val . '"> records max per page</td>';
            // FRAME A MODIFIER POUR MISE A JOUR DE LA LISTE LORS DU CHANGEMENT DU NMBRE DENREGISTREMENT PAR LUTILISATEUR --> REMPLACER   onblur="item_list_refresh('.$i.','.$limit_val.')"
            echo '<td style="width:150px"> - Total :' . $rowcount . '</td>';
            echo '</tr></table>';
            echo '</td></tr>';


            echo '<tr>';
            echo '<td colspan=4 style="width:50vw; vertical-align:top">';


            echo '<iframe 
				name="Item_List_Frame" 
				id="Item_List_Frame"
				class="Item_List_Frame" alt="ok" 
				src="REL_Q_Item_List.php?
					start=0
					&nb=' . $limit_val . '
					&rel_pack_num_choice=' . $rel_pack_num_choice . '
					&project_choice=' . $project_choice . '
					&activity_choice=' . $activity_choice . '
					&reference_choice=' . $reference_choice . '
					&drawing_choice=' . $drawing_choice . '
					&action_choice=' . $action_choice . '
					&doc_type_choice=' . $doc_type_choice . '
					&proc_type_choice=' . $proc_type_choice . '
					&Ex_Choice=' . $Ex_Choice . '
					&Quality_Owner_Choice=' . $Quality_Owner_Choice . '"
				frameborder="0"
				scrolling="yes">
			</iframe>';
            ?>
            </td>


            <td colspan=7 id="td_detail" class="disabled" style="vertical-align:top">

                <table id="t03">

                    <th style="width:8%;">Inspect.</th>
                    <th style="width:12%;">Gamme/Dynam.</th>
                    <th style="width:46%">Exigence Documentaire</th>
                    <th>Comments</th>
                    <th style="width:10%;">Validation</th>


                    <tr style="">
                        <td rowspan="2" style="margin-left:-10px;text-align:center;">
                            <div id="Table_results">
                                <?php

                                $query_2 = 'SELECT *
                    FROM tbl_q_inspection_type 
                    ORDER BY Code ASC';
                                include('../REL_Connexion_DB.php');
                                $resultat_2 = $mysqli->query($query_2);
                                while ($ligne_2 = $resultat_2->fetch_assoc()) {
                                    echo '<input style="background-color:transparent;margin-left:-0px;vertical-align:middle" type="checkbox" 
                             id="inspection_' . $ligne_2['Code'] . '" 
                             name="inspection_' . $ligne_2['Code'] . '" 
                             Value="' . $ligne_2['Code'] . '" 
							 class="inspection"
                             Title="' . $ligne_2['Description'] . '"';
                                    if ($ligne_2['Code'] == "Z08") {
                                        echo ' checked';
                                    }
                                    echo '> <label   for="inspection_' . $ligne_2['Code'] . '">' . $ligne_2['Code'] . '</label></br>';
                                }
                                echo '</div></td>';

                                ?>
                        </td>
                        <td style="border-right: 0.25px solid black;border-bottom: 0.25px dotted black;text-align:center">
                            <div id="Table_results" style="font-size:95%;margin-top:-5px;margin-bottom:3px;">Gamme</div>
                            <div id="Filter">
                                <SELECT name="Control_Routing" id="Control_Routing" type="submit" size="1" style="background-color:transparent;font-size:95%;margin-left:-14px;width:50px;vertical-align:middle;margin-top:-5px">
  
                                    <option value=""></option>
                                    <?php
                                    $query_2 = 'SELECT *
												FROM tbl_q_control_routing 
												ORDER BY Code ASC';
                                    $resultat_2 = $mysqli->query($query_2);
                                    $i = 1;
                                    while ($ligne_2 = $resultat_2->fetch_assoc()) {
                                        $selection = "";
										
                                        if ($ligne_2['Code'] == "Z001") {
                                            $selection = "SELECTED";
                                        }
                                        echo '<OPTION value="' . $ligne_2['Code'] . '" Title="' . $ligne_2['Description'] . '" ' . $selection . '>' . $ligne_2['Code'] . '&nbsp&nbsp-&nbsp&nbsp'.$ligne_2['Description'].'</option><br/>';
                                    }

                                    echo '</div></td>';


                                    echo '<td rowspan="2" style="width:50%">';
                                    $query_2 = 'SELECT *
												FROM tbl_q_doc_requirements 
												ORDER BY Code ASC';
                                    $resultat_2 = $mysqli->query($query_2);
                                    $i = 0;
                                    while ($ligne_2 = $resultat_2->fetch_assoc()) {
										echo '<div class="dropdown_checkbox">';
                                        echo '<div id="checkbox_block">
												<input style="background-color:transparent;MARGIN-TOP:0px;vertical-align:middle" type="checkbox" 
													 id="doc_req_' . $ligne_2['Code'] . '" 
													 name="doc_req_' . $ligne_2['Code'] . '"
													 Value="' . $ligne_2['Code'] . '"
													 Class="doc_req"';
											echo '>';
											echo '<span>
													<label style="margin-left:-4px;" for="doc_req_' . $ligne_2['Code'] . '">  ' . substr($ligne_2['Code'], -3) . '</label>
												  </span>
												  <div class="dropdown_checkbox-content">'.$ligne_2['Description'].'</div>
											  </div>
											  &nbsp';
										echo '</div>';
                                    }
                                    echo '</div></td>';
                                    mysqli_close($mysqli);
                                    ?>
                        <td rowspan=2>
                            <div id="Table_results">
                                <textarea id="comment" style="z-index:1;background-color:transparent;height:58px;vertical-align:middle;width:92%" name="comment"></textarea>
                            </div>
                        </td>

                        <td hidden>
                            <input type="text" size=1 id="id_to_update" name="id_to_update" style="width:20pt;">
                        </td>

                        <td rowspan=2>
                            <div id="Filter">
                                <SELECT name="User_Choice" id="User_Choice" type="submit" style="background-color:transparent;font-size:95%;width:95%;height:17px;margin-left:-15px;margin-top:10px;">
                                    <option value="%"></option>
                                    <?php
                                    // connexion à la bdd db_scm
                                    include('../SCM_Connexion_DB.php');

                                    // requète sql qui permet de selectionner le nom des personnes qui appartiennent au department "Quality"
                                    $requete = 'SELECT DISTINCT tbl_user.Fullname
						FROM tbl_user
						WHERE Department like "Quality Project"';

                                    // Lancement de la requete
                                    $resultat = $mysqli_scm->query($requete);

                                    // condition qui permet de parcourir tout le champs Fullname et recuperer ceux qui appartiennet au department Quality et de les afficher dans la combobox
                                    while ($row = $resultat->fetch_assoc()) {
                                        echo '<OPTION value ="' . $row['Fullname'] . '">' . $row['Fullname'] . '</option><br/>';
                                    }
                                    mysqli_close($mysqli_scm);
                                    ?>
                                </SELECT>
                                <input name="save" id="save" onclick="return data_update(1,0,0)" type="submit" class="btn orange" style="font-size:7pt;margin-left:-5px; width:35px;height:15px;vertical-align:middle;text-align:center" value="Save" title="Save the current data without validating it" />
                                

                                <input onclick="return chkName()" type="submit" class="btn blue2" style="margin-left:4px;font-size:7pt; width:35px;height:15px;vertical-align:middle;text-align:center;display:none" id="valid_form" name="valid_form" value="Sign" title="Sign off the current drawing" />
                        </td>

                    </tr>
                    <tr>
                        <td style="border-right: 0.25px solid black;text-align:center">
                            <div id="Table_results" style="font-size:95%;margin-top:-5px;margin-bottom:3px;">Dynamisation</div>
                            <div id="Filter">
                                <SELECT name="dyn_rule" id="dyn_rule" type="submit" size="1" style="background-color:transparent;font-size:95%;margin-left:-14px;width:45px;vertical-align:middle;margin-top:-5px">
                                    <option value=""></option>
                                    <?php include('../REL_Connexion_DB.php');
                                    $query_2 = 'SELECT *
                                    FROM tbl_q_dynamisation_rules 
                                    ORDER BY Code ASC';
                                    $resultat_2 = $mysqli->query($query_2);
                                    $i = 1;

                                    while ($ligne_2 = $resultat_2->fetch_assoc()) {
                                        if ($ligne_2['Code'] == "No") {
                                            $sel='SELECTED';
                                        } else {
                                            $sel='';
                                        }
										echo '<OPTION value ="' . $ligne_2['Code'] . '" Title="' . $ligne_2['Description'] . '" '.$sel.'>' . $ligne_2['Code'] . '&nbsp&nbsp-&nbsp&nbsp'.$ligne_2['Description'].'</font></option><br/>';
                                    }

                                    echo '</div></td>';



                                    mysqli_close($mysqli); ?>

                    </tr>

                    <tr>
                        <td colspan=3 style="border-top:0.5px black solid;border-bottom:0.5px black solid;vertical-align:middle">
                            <div id="Filter">
                                To change the supply type:
                                <SELECT name="doc_type_change" id="doc_type_change" type="submit" size="1" style="background-color:transparent;font-size:95%;vertical-align:middle;">
                                    <option value="%"></option>
                                    <?php
                                    include('../REL_Connexion_DB.php');
                                    $requete = 'SELECT DISTINCT tbl_doc_type.Doc_Type
				FROM tbl_doc_type';
                                    $resultat = $mysqli->query($requete);
                                    while ($row1 = $resultat->fetch_assoc()) {
                                        echo '<OPTION value ="' . $row1['Doc_Type'] . '">' . $row1['Doc_Type'] . '</option><br/>';
                                    }
                                    mysqli_close($mysqli);
                                    ?>
                                </SELECT>
                                <input onclick="return chkChange()" type="submit" class="btn grey" style="font-size:7pt; width:65px;height:15px;vertical-align:middle;text-align:center" name="change_form" value="Change" title="Change the supply type" />
                            </div>
                        </td>
                        <td colspan=2 style="border-top:0.5px black solid;vertical-align:middle;text-align:center;font-size:8pt;font-weight:bold;font-family:Tahoma, sans-serif;">
                            <input type="checkbox" id="pdf_visual" name="pdf_visual" title="Activate the drawing visualisation" onchange="visual_chk()" style="background-color:transparent" checked>Drawing Visualization</br>

                        </td>
                    </tr>
                    <tr>
                        <td colspan=5>
                            <embed id="visu_drawing" name="visu_drawing_name" src="" type="application/pdf" frameborder="1" />
                            <!--<embed id="visu_drawing" name="visu_drawing_name" src="\Common_Resources\logo_scm_zoom_in_transparent_50.jpg" type="application/pdf" frameborder="1" />-->
                        </td>
                    </tr>



                </table>


            </td>



    </form>
</body>

</html>