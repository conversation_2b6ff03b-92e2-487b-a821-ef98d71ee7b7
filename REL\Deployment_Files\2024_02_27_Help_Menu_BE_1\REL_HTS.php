<?php
    // require('login.php');
    // login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="en">

<head>

	<meta http-equiv="X-UA-Compatible" content="IE=edge" />

	<link rel="stylesheet" type="text/css" href="REL_HTS_styles.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

	<link rel="icon" type="image/png" href="\Common_Resources\Release_Icon.png" />

<script>


function select_filling(select_to_fill,  text_list, title_list)
{
	i=1;
	while (i<=text_list.length)
	{
		var option = document.createElement("option");
		option.value="Q2" + (i-1);
		option.text=text_list[i-1];
		option.title=title_list[i-1];
		select_to_fill.add(option);
		i=i+1;
	}
}


function select_cleanup(list_to_clean)
{
	while (list_to_clean.options.length > 0)
		{
			list_to_clean.remove(0);
		}
}




function hts_description_lookup()
{
	// to be finished --> AJAX TO LOOK INTO TBL_HTS  IN DB_SCM
}



function Q1_select_update()
	{
		
		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				var q1_list=document.getElementById("Q1");
				
				let text_result_list="";
				let title_result_list="";
				let split_result="";
				
				const raw_result=this.responseText.trim();
				
				split_result=raw_result.split("//");
				
				text_result_list = split_result[0];
				text_result=text_result_list.split("|");
				title_result_list = split_result[1];
				title_result=title_result_list.split("|");
				
				let i=0;
				while (i<(text_result.length))
				{
					var option = document.createElement("option");
					option.value="Q1" + (i);
					option.text=text_result[i];
					option.title=title_result[i];
					q1_list.add(option);
					i=i+1;
				}
					
			}
		  xhttp.open("GET", "REL_HTS_Questions.php?filled_select=Q1");
		  xhttp.send();	
	}
	
	
function Q2_select_update()
	{
		var q1_list=document.getElementById("Q1");
		var q2_list=document.getElementById("Q2");
		var q3_list=document.getElementById("Q3");
		var picked_item_text = q1_list.options[q1_list.selectedIndex].text;
		
		q3_list.hidden = true;
		q2_list.hidden = true;
		
		select_cleanup(q2_list);
		select_cleanup(q3_list);
		
		// parent.document.getElementById("HTS_id").value="";
		document.getElementById("HTS_id").value="";
		document.getElementById("hts_code_final_desc").innerHTML="";
		document.getElementById("q3_content").innerHTML="";
		document.getElementById("q2_content").innerHTML="";

		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				
				let text_result_list="";
				let title_result_list="";
				let split_result="";
				
				let style_start="<div id=\"hts_eccn\">";
				let style_end="</div>";

				const raw_result=this.responseText.trim();
				
				split_result=raw_result.split("//");
				
				text_result_list = split_result[0];
				text_result=text_result_list.split("|");
				
				if (text_result_list!="HTS")
				{
					
					title_result_list = split_result[1];
					title_result=title_result_list.split("|");
					
					description_result = split_result[2];
					
					let i=0;
					while (i<(text_result.length))
					{
						var option = document.createElement("option");
						option.value="Q2" + (i);
						option.text=text_result[i];
						option.title=title_result[i];
						q2_list.add(option);
						i=i+1;
					}
					
					
					
					
					document.getElementById("q2_content").innerHTML=style_start + description_result + style_end;
					q2_list.hidden = false;
					
				} else {
					// parent.document.getElementById("HTS_id").value=split_result[1];
					document.getElementById("HTS_id").value=split_result[1];
					document.getElementById("hts_code_final_desc").innerHTML=split_result[2];
					q2_list.hidden = true;
				}
		
			}
		  xhttp.open("GET", "REL_HTS_Questions.php?filled_select=Q2&Q1_val=" + picked_item_text);
		  xhttp.send();	
	}
	
function Q3_select_update()
	{
		var q1_list=document.getElementById("Q1");
		var q2_list=document.getElementById("Q2");
		var q3_list=document.getElementById("Q3");
		
		let style_start="<div id=\"hts_eccn\">";
		let style_end="</div>";
		
		var picked_item_text_q1 = q1_list.options[q1_list.selectedIndex].text;
		var picked_item_text_q2 = q2_list.options[q2_list.selectedIndex].text;
		
		select_cleanup(q3_list);
		
		
		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				let text_result_list="";
				let title_result_list="";
				let split_result="";
				
				const raw_result=this.responseText.trim();
				
				split_result=raw_result.split("//");
				
				text_result_list = split_result[0];
				text_result=text_result_list.split("|");
				
				if (text_result_list!="HTS")
				{
					title_result_list = split_result[1];
					title_result=title_result_list.split("|");
					
					description_result = split_result[2];
					
					let i=0;
					while (i<(text_result.length))
					{
						var option = document.createElement("option");
						option.value="Q3" + (i);
						option.text=text_result[i];
						option.title=title_result[i];
						q3_list.add(option);
						i=i+1;
					}
					
					
					
					document.getElementById("q3_content").innerHTML=style_start + description_result + style_end;
					q3_list.hidden = false;
					document.getElementById("HTS_id").value="";
					// parent.document.getElementById("HTS_id").value=split_result[1];
					document.getElementById("hts_code_final_desc").innerHTML="";
					
				} else {
					// parent.document.getElementById("HTS_id").value=split_result[1];
					document.getElementById("HTS_id").value=split_result[1];
					document.getElementById("hts_code_final_desc").innerHTML=split_result[2];
				}
				
		
			}
			
		  xhttp.open("GET", "REL_HTS_Questions.php?filled_select=Q3&Q1_val=" + picked_item_text_q1 + "&Q2_val=" + picked_item_text_q2);
		  xhttp.send();	
		
	}
	
	function Q3_Final()
	{
		var q1_list=document.getElementById("Q1");
		var q2_list=document.getElementById("Q2");
		var q3_list=document.getElementById("Q3");
		
		let style_start="<div id=\"hts_eccn\">";
		let style_end="</div>";
		
		var picked_item_text_q1 = q1_list.options[q1_list.selectedIndex].text;
		var picked_item_text_q2 = q2_list.options[q2_list.selectedIndex].text;
		var picked_item_text_q3 = q3_list.options[q3_list.selectedIndex].text;
		
		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				let title_result_list="";
				let split_result="";
				
				const raw_result=this.responseText.trim();
				
				split_result=raw_result.split("//");
				text_result_list = split_result[0];
				
				// parent.document.getElementById("HTS_id").value=split_result[1];
				document.getElementById("HTS_id").value=split_result[1];	
				document.getElementById("hts_code_final_desc").innerHTML=split_result[2];
				
			}
		  xhttp.open("GET", "REL_HTS_Questions.php?filled_select=final&Q1_val=" + picked_item_text_q1 + "&Q2_val=" + picked_item_text_q2 + "&Q3_val=" + picked_item_text_q3);
		  xhttp.send();
	}
		

</script>

</head>

<body onload="Q1_select_update()">


    <table id="t01_hts" border=0>

	<tr>
		<td colspan=2 style="vertical-align:top;width:30%">
			<div id="Body_hts">
				Code HTS:
			</div>
		</td>	
	</tr>
	<tr>
		<td colspan=2>
			<div id="hts_eccn">
				The reference is:  
			</div>
		</td>
	</tr>
	<tr>
		<!-- <td rowspan=5> -->
			<!-- <div id="hts_code_final_desc"></div> -->
		<!-- </td> -->
		<td colspan=2>
			<div id="InpBox_hts">
			<select name="Q1" id="Q1" type="submit" title="" style="font-size:11;height:17;width:100%" onchange="Q2_select_update()">
				<option value=""></option>
			</select>
			</div> 
		</td>
	</tr>
	<tr>

		<td colspan=2>
			<div id="hts_eccn">
				<p id="q2_content">
				
				</p>
			</div>
		</td>
	</tr>
	<tr>

		<td colspan=2>
			<div id="InpBox_hts">
			<select hidden name="Q2" id="Q2" type="submit" title="" style="font-size:11;height:17;width:100%;"  onchange="Q3_select_update()"> 
				<option value=""></option>
			</select>
			</div> 
		</td>
	</tr>
	<tr>

		<td colspan=2>
			<div id="hts_eccn">
				<p id="q3_content">
			
				</p>
			</div>
		</td>
	</tr>
	<tr>

		<td colspan=2>
			<div id="InpBox_hts">
			<select hidden name="Q3" id="Q3" type="submit" title="" style="font-size:11;height:17;width:100%" onchange="Q3_Final()"> 
				<option value=""></option>
			</select>
			</div> 
		</td>
	</tr>
	<tr>
		<td style="width:120px;">
			<div id="Body_hts">
				Code
				<input list="hts" name="hts" multiple id="HTS_id" title="" style="text-align:center;width:90px;font-size:11;height:13pt;"> 
                <datalist id="hts">
				<option value=""></option>
				<!------------------------------>
				<?php
					include('../SCM_Connexion_DB.php');
					$requete = "SELECT DISTINCT * FROM tbl_hts ORDER BY HTS DESC;";
					$resultat = $mysqli_scm->query($requete);
					while ($row = $resultat->fetch_assoc())
					{
						echo'<option Title ="'.$row['HTS'].'" value ="'.$row['HTS'].'">'.$row['Description'].'</option><br/>'; 
					}
					mysqli_close($mysqli_scm);
				?>
				<!------------------------------>
				</datalist>
			</div>
		</td>
	<!-- </tr> -->
	<!-- <tr style="height:15px"> -->
		<td>
			<div id="hts_code_final_desc"></div>
		</td>
	</tr>
	
	
	</table>

</body> 
</html>