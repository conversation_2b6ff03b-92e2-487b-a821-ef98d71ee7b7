<?php

if ($_GET['filled_select']=="Q1")
{
	include('../REL_Connexion_DB.php');
	
	$requete = 'SELECT DISTINCT Q1_Text, Q1_Title 
				FROM tbl_hts_decision
				ORDER BY ID ASC';
		
	$resultat = $mysqli->query($requete);
	$rowcount=mysqli_num_rows($resultat);
	
	$output_value_Q1_text="";
	$output_value_Q1_title="";
	
	if ($rowcount>0)
	{
		while ($row = $resultat->fetch_assoc())
		{
			if ($output_value_Q1_text!="")
			{
				$output_value_Q1_text=$output_value_Q1_text."|".$row['Q1_Text'];
			} else {
				$output_value_Q1_text=$row['Q1_Text'];
			}
			
			if ($output_value_Q1_title!="")
			{
				$output_value_Q1_title=$output_value_Q1_title."|".$row['Q1_Title'];
			} else {
				$output_value_Q1_title=" ";
			}
		}
		$output_value=$output_value_Q1_text."//".$output_value_Q1_title;
		
		echo $output_value;
		$mysqli->close();
	} else {
		$output_value=""."//"."";
		echo $output_value;
	}
}

if ($_GET['filled_select']=="Q2")
{
	include('../REL_Connexion_DB.php');
	
	$requete = 'SELECT DISTINCT Q2_Text, Q2_Title
				FROM tbl_hts_decision
				WHERE Q1_Text like "'.$_GET['Q1_val'].'"';
				
	$requete_description = 'SELECT DISTINCT Q2_Description 
				FROM tbl_hts_decision
				WHERE Q1_Text like "'.$_GET['Q1_val'].'"';

	$resultat = $mysqli->query($requete);
	$resultat_description = $mysqli->query($requete_description);
	$rowcount=mysqli_num_rows($resultat);
	
	$output_value_Q2_text=" ";
	$output_value_Q2_Title=" ";
	$output_value_Q2_Description=" ";

	if ($rowcount>1)
	{

		while ($row = $resultat->fetch_assoc())
		{
			// DESCRIPTION
			if ($output_value_Q2_text!="")
			{
				$output_value_Q2_text=$output_value_Q2_text."|".$row['Q2_Text'];
			} else {
				$output_value_Q2_text=$row['Q2_Text'];
			}
			
			// TITLE
			if ($output_value_Q2_Title!="")
			{
				$output_value_Q2_Title=$output_value_Q2_Title."|".$row['Q2_Title'];
			} else {
				$output_value_Q2_Title=" ";
			}
		}
		

		
		// DESCRIPTION
		while ($row_description = $resultat_description->fetch_assoc())
		{
			$output_value_Q2_Description=$row_description['Q2_Description'];
		}
		$output_value=$output_value_Q2_text."//".$output_value_Q2_Title."//".$output_value_Q2_Description;
		
		echo $output_value;
		
	} elseif ($rowcount==0)
		{
			$output_value=""."//"."";
			echo $output_value;
		} else {
			// SI $rowcount==1 --> 1 SEULE LIGNE --> CODE HTS A RECUPERER
			$HTS="";
			$HTS_desc="";
			$requete_HTS = 'SELECT DISTINCT HTS
					FROM tbl_hts_decision
					WHERE
						Q1_Text like "'.$_GET['Q1_val'].'"';
			
			$resultat_HTS = $mysqli->query($requete_HTS);
			while ($row_HTS = $resultat_HTS->fetch_assoc())
			{
				$HTS=$row_HTS['HTS'];
			}
			
			include('../SCM_Connexion_DB.php');
			$requete_HTS_desc='
					SELECT Description
					FROM tbl_hts
					WHERE HTS like "'.$HTS.'"';
			
			$resultat_HTS_desc = $mysqli_scm->query($requete_HTS_desc);
			while ($row_HTS_desc = $resultat_HTS_desc->fetch_assoc())
			{
				$HTS_desc=$row_HTS_desc['Description'];
			}
			$mysqli_scm->close();
			
			$output_value="HTS"."//".$HTS."//".$HTS_desc;
			echo $output_value;
		}
	$mysqli->close();
}


if ($_GET['filled_select']=="Q3")
{
	include('../REL_Connexion_DB.php');
	
	$requete = 'SELECT DISTINCT Q3_Text, Q3_Title
				FROM tbl_hts_decision
				WHERE
					Q1_Text like "'.$_GET['Q1_val'].'" AND
					Q2_Text like "'.$_GET['Q2_val'].'"';
				
	$requete_description = 'SELECT Q3_Description 
				FROM tbl_hts_decision
				WHERE
					Q1_Text like "'.$_GET['Q1_val'].'" AND
					Q2_Text like "'.$_GET['Q2_val'].'"';

	$resultat = $mysqli->query($requete);
	$resultat_description = $mysqli->query($requete_description);
	$rowcount_description=mysqli_num_rows($resultat_description);
	
	$output_value_Q3_text=" ";
	$output_value_Q3_Title=" ";
	$output_value_Q3_Description=" ";
	
	if ($rowcount_description>1)
	{

		while ($row = $resultat->fetch_assoc())
		{
			// DESCRIPTION
			if ($output_value_Q3_text!="")
			{
				$output_value_Q3_text=$output_value_Q3_text."|".$row['Q3_Text'];
			} else {
				$output_value_Q3_text=$row['Q3_Text'];
			}
			
			// TITLE
			if ($output_value_Q3_Title!="")
			{
				$output_value_Q3_Title=$output_value_Q3_Title."|".$row['Q3_Title'];
			} else {
				$output_value_Q3_Title=" ";
			}
		}
		

		
		// DESCRIPTION
		while ($row_description = $resultat_description->fetch_assoc())
		{
			$output_value_Q3_Description=$row_description['Q3_Description'];
		}
		$output_value=$output_value_Q3_text."//".$output_value_Q3_Title."//".$output_value_Q3_Description;
		
		echo $output_value;
		
	} else {
		$HTS="";
		$HTS_desc="";
		$requete_HTS = 'SELECT DISTINCT HTS
				FROM tbl_hts_decision
				WHERE
					Q1_Text like "'.$_GET['Q1_val'].'" AND
					Q2_Text like "'.$_GET['Q2_val'].'"';
		
		$resultat_HTS = $mysqli->query($requete_HTS);
		while ($row_HTS = $resultat_HTS->fetch_assoc())
		{
			$HTS=$row_HTS['HTS'];
		}
		
		
		include('../SCM_Connexion_DB.php');
		$requete_HTS_desc='
				SELECT Description
				FROM tbl_hts
				WHERE HTS like "'.$HTS.'"';
		
		$resultat_HTS_desc = $mysqli_scm->query($requete_HTS_desc);
		while ($row_HTS_desc = $resultat_HTS_desc->fetch_assoc())
		{
			$HTS_desc=$row_HTS_desc['Description'];
		}
		$mysqli_scm->close();
		
		$output_value="HTS"."//".$HTS."//".$HTS_desc;
		echo $output_value;
	}
	$mysqli->close();
}


if ($_GET['filled_select']=="final")
{	
	
	$HTS="";
	$HTS_desc="";
	
	$requete_HTS = '
			SELECT HTS
			FROM tbl_hts_decision
			WHERE
				Q1_Text like "'.$_GET['Q1_val'].'" AND
				Q2_Text like "'.$_GET['Q2_val'].'" AND
				Q3_Text like "'.$_GET['Q3_val'].'"
					';
					
					include('../REL_Connexion_DB.php');
	$resultat_HTS = $mysqli->query($requete_HTS);
	//$rowcount=mysqli_num_rows($resultat_HTS);
	
	while ($row_HTS = $resultat_HTS->fetch_assoc())
	{
		$HTS=$row_HTS['HTS'];
	}
	$mysqli->close();
		
	include('../SCM_Connexion_DB.php');
	$requete_HTS_desc='
			SELECT Description
			FROM tbl_hts
			WHERE HTS like "'.$HTS.'"';
	
	$resultat_HTS_desc = $mysqli_scm->query($requete_HTS_desc);
	while ($row_HTS_desc = $resultat_HTS_desc->fetch_assoc())
	{
		$HTS_desc=$row_HTS_desc['Description'];
	}
	$mysqli_scm->close();
	
	$output_value="HTS"."//".$HTS."//".$HTS_desc;
	echo $output_value;
}

?>