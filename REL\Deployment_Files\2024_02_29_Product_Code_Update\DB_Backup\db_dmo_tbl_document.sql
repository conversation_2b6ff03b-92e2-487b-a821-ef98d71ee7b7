-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_dmo
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_document`
--

DROP TABLE IF EXISTS `tbl_document`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_document` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `Document` varchar(45) NOT NULL,
  `Type` varchar(45) NOT NULL,
  `Description` varchar(100) DEFAULT NULL,
  `Division` varchar(45) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=37 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_document`
--

LOCK TABLES `tbl_document` WRITE;
/*!40000 ALTER TABLE `tbl_document` DISABLE KEYS */;
INSERT INTO `tbl_document` VALUES (1,'Drawing','Engineering','Any type of drawings (GA, FT, ID, part, BoM e','ALL'),(2,'FI','Method Assy.','Assembly Instructions/Procedures','ALL'),(3,'NU','Engineering','Installation Procedure','ALL'),(4,'FATP','Method Lab.','FAT Procedure','ALL'),(5,'QPP','Method Lab.','Qualification Procedure','ALL'),(6,'PHI','Method Assy.','Packaging and Handling Instructions','ALL'),(7,'Asembly Tool','Method Assy.','Tool used for assembly','ALL'),(8,'Lab. Tool','Method Lab.','Tool used ofr FAT or qualification','ALL'),(9,'Assy. Checklist','Method Assy.','Routing for product assembly','ALL'),(10,'Specification','Engineering','FMME, CC etc...','ALL'),(11,'QPR','Method Lab.','Qualification Report','ALL'),(12,'NT','Engineering','Technical Note','ALL'),(13,'SAP Drawing','Method Assy.','WG, Tool kit, Spare Kit, Packaging (V371-), O','ALL'),(14,'FI','Method Lab.','Assembly Instruction for Lab Setup','ALL'),(15,'FOL','Method Lab.','Fiche Outil Labo','ALL'),(16,'Security Chklst','Method Lab.','Security Checklist','ALL'),(17,'IRS','Method Assy.','Instruction Report Sheet','ALL'),(18,'OSIR','Method Assy.','On-Site Installation Report','ALL'),(19,'COSIR','Method Assy.','Customer On-Site Installation Report','ALL'),(20,'FUM','Method Assy.','Fiche Utilisation Machine','ALL'),(21,'FUM','Method Lab.','Fiche Utilisation Machine','ALL'),(22,'Bonne Prat.','Method Lab.','Bonne Pratique','ALL'),(23,'Bonne Prat.','Method Assy.','Bonne Pratique','ALL'),(24,'Specification','Method Lab.','Equipment specification','ALL'),(25,'Specification','Method Assy.','Equipment specification','ALL'),(26,'DEO','Method Lab.','Demande d\'Essai','ALL'),(27,'DEO','Method Assy.','Demande d\'Essai','ALL'),(28,'DEO','Engineering','Demande d\'Essai','ALL'),(29,'NU','Method Assy.','Installation procedure update following actual OSIs or customer prep. meeting','ALL'),(34,'Other','Engineering',NULL,'ALL'),(35,'Other','Method Assy.',NULL,'ALL'),(36,'Other','Method Lab.',NULL,'ALL');
/*!40000 ALTER TABLE `tbl_document` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:37
