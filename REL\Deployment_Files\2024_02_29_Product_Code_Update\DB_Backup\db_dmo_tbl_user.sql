-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_dmo
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_user`
--

DROP TABLE IF EXISTS `tbl_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_user` (
  `Key_User` int NOT NULL AUTO_INCREMENT,
  `TE_ID` tinytext NOT NULL,
  `Fullname` tinytext NOT NULL,
  `Email` tinytext NOT NULL,
  `ID_PC` tinytext NOT NULL,
  `Department` tinytext NOT NULL,
  PRIMARY KEY (`Key_User`)
) ENGINE=MyISAM AUTO_INCREMENT=200 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_user`
--

LOCK TABLES `tbl_user` WRITE;
/*!40000 ALTER TABLE `tbl_user` DISABLE KEYS */;
INSERT INTO `tbl_user` VALUES (43,'TE180249','CHATAIN A.','<EMAIL>','','Engineering'),(41,'TE180160','BAUER M.','<EMAIL>','FRK28NBO745Q453$','Engineering'),(17,'TE180431','JADAUD S.','<EMAIL>','','Project'),(4,'TE180197','BONIN R.','<EMAIL>','','Quality'),(13,'TE180338','EON C.','<EMAIL>','','Quality'),(42,'TE180207','BOURRET F.','<EMAIL>','','Assembly'),(54,'TE180447','LAGATHU C.','<EMAIL>','','Engineering'),(45,'TE180320','DORANGE N.','<EMAIL>','','Engineering'),(163,'AMENAND','MENAND A.','<EMAIL>','','Method'),(47,'TE180347','FARIAULT N.','<EMAIL>','','Engineering'),(72,'TE421736','BELLET N.','<EMAIL>','','Laboratory'),(61,'TE180566','PEIGNE G.','<EMAIL>','','Industrialization'),(64,'TE183530','LERUEZ PL.','<EMAIL>','','Assembly'),(65,'TE180451','LANGLOIS J.','<EMAIL>','','Industrialization'),(66,'TE180398','GUITTET Y.','<EMAIL>','','Engineering'),(67,'TE417418','LACROIX N.','<EMAIL>','','Engineering'),(68,'TE180563','PASCAUD S.','<EMAIL>','','Machinning'),(146,'TE476786','LEGEAY J.','<EMAIL>','','Inside Sales'),(70,'TE426193','LELONG T.','<EMAIL>','','Engineering'),(149,'LCHESNE','CHESNE L.','<EMAIL>','','Quality'),(73,'TE180183','BLANCHE J.','<EMAIL>','','Laboratory'),(74,'TE180193','BOIVIN G.','<EMAIL>','','Laboratory'),(75,'TE180201','BOUCHENOIRE S.','<EMAIL>','','Purchasing'),(169,'CLELOUP','LELOUP.C','<EMAIL>','','Quality'),(145,'TE504722','BREGENT M.','<EMAIL>','','Engineering'),(143,'TE483912','MADELIN A.','<EMAIL>','','Project'),(138,'TE183422','GUICHARDON L.','<EMAIL>','','Molding'),(137,'TE180292','CRIBIER JF.','<EMAIL>','','Operation'),(85,'TE183479','GOURDOU JF.','<EMAIL>','','Laboratory'),(134,'TE180220','BRIER F.','<EMAIL>','','Quality'),(133,'TE180172','BELLON F.','<EMAIL>','','Quality'),(132,'TE180332','DURAND S.','<EMAIL>','','Metrology'),(131,'TE180617','RIGUET W.','<EMAIL>','','Purchasing'),(130,'TE180171','BELLANGER F.','<EMAIL>','','Inside Sales'),(129,'TE183439','CISSE N.','<EMAIL>','','Assembly'),(128,'TE180296','DARONDEAU A.','<EMAIL>','','Assembly'),(183,'FJARRIER','JARRIER F.','<EMAIL>','','Field Service'),(124,'TE424288','JEAN J.','<EMAIL>','','Quality'),(125,'TE183456','JANVIER.I','<EMAIL>','','Assembly'),(126,'TE415242','VILLETTE.C','<EMAIL>','','Quality'),(100,'TE180193','BOIVIN G.','<EMAIL>','','Laboratory'),(102,'TE180242','CASSEGRAIN O.','<EMAIL>','','Ourchasing'),(103,'TE180262','CHEVRIER F.','<EMAIL>','','Method'),(104,'TE183527','COTOC A.','<EMAIL>','','Laboratory'),(105,'TE180286','CRAPIS A.','<EMAIL>','','Engineering'),(144,'TE415298','KAROU S.','<EMAIL>','','Quality'),(164,'VPERCIVAL','PERCIVAL V.','<EMAIL>','','Project'),(108,'TE378346','DERET V.','<EMAIL>','','Laboratory'),(136,'TE180531','MEDARD M.','<EMAIL>','','Production'),(110,'TE180379','GOT G.','<EMAIL>','','Engineering'),(135,'TE390704','ROUILLARD N.','<EMAIL>','','Industrialization'),(148,'TE506816','ROBICHON P.','<EMAIL>','','Quality'),(141,'TE180440','JULIEN S.','<EMAIL>','','Engineering'),(115,'TE180515','MARAIS J.','<EMAIL>','','Method'),(116,'TE180560','PARAT P.','<EMAIL>','','Industrialization'),(117,'TE180562','PARME R.','<EMAIL>','','Engineering'),(118,'TE183474','PAULMERY E.','<EMAIL>','','Laboratory'),(119,'TE376107','PERES T.','<EMAIL>','','Method'),(120,'TE180571','PERNET G.','<EMAIL>','','Engineering'),(121,'TE183512','REVAUD E.','<EMAIL>','','Laboratory'),(122,'TE180675','TISON L.','<EMAIL>','','Method'),(123,'TE180704','WENTS G.','<EMAIL>','','Molding'),(150,'CPOULAIN','POULAIN C.','<EMAIL>','','Engineering'),(151,'SFREMONT','FREMONT S.','<EMAIL>','','Metrology'),(153,'WDONNE','DONNE W.','<EMAIL>','','Quality'),(154,'MVRIGNAUD','VRIGNAUD M.','<EMAIL>','','Quality'),(155,'FFOURNIER','FOURNIER F.','<EMAIL>','','Laboratory'),(193,'lprenant','PRENANT L.','<EMAIL>','','Machinning'),(157,'MLOUIS','LOUIS M.','<EMAIL>','','Logistic'),(158,'MROSSI','ROSSI M.','<EMAIL>','','Logistic'),(162,'MBONNIN','BONNIN M.','<EMAIL>','','Industry'),(161,'LPEREZ','PEREZ L.','<EMAIL>','','Quality'),(165,'STOURNIER','TOURNIER S.','<EMAIL>','','Marketing'),(166,'SGUILLARD','GUILLARD S.','<EMAIL>','','Engineering'),(167,'JFGALIPAUD','GALIPAUD JF.','<EMAIL>','','Engineering'),(170,'PMARCHAND','MARCHAND P.','<EMAIL>','','Production'),(199,'dbechet','BECHET D.','<EMAIL>','','Assembly'),(172,'CAUDAT','AUDAT.C','<EMAIL>','FRSCMNBO9LBR253$','Quality'),(173,'VPICHARD','PICHARD V.','<EMAIL>','','Operation'),(174,'SLOIRE','LOIRE S.','<EMAIL>','','Quality'),(175,'JSAILLARD','SAILLARD J.','<EMAIL>','','Marketing'),(181,'SPRIMAULT','PRIMAULT S.','<EMAIL>','','Assembly'),(180,'KCLERET','CLERET K.','<EMAIL>','','Assembly'),(179,'PJOUBERT','JOUBERT P.','<EMAIL>','','Assembly'),(182,'CMARCAIS','MARCAIS C.','<EMAIL>','','Assembly'),(184,'APREAU','PREAU A.','<EMAIL>','','Field Service'),(185,'CBAUDRY','BAUDRY C.','<EMAIL>','','Molding'),(186,'GCORDELET','CORDELET G.','<EMAIL>','','Engineering'),(188,'MLALANDE','LALANDE M.','<EMAIL>','','Purchasing'),(189,'TPISSOT','PISSOT T.','<EMAIL>','','Engineering'),(190,'BLOISON','LOISON B.','<EMAIL>','','Purchasing'),(191,'SPROT','PROT S.','<EMAIL>','','Quality'),(192,'JBERTIAU','BERTIAU J.','<EMAIL>','','Engineering'),(197,'ABOUCAUD','BOUCAUD A.','<EMAIL>','','Engineering'),(198,'SJUGE','JUGE S.','<EMAIL>','','Engineering');
/*!40000 ALTER TABLE `tbl_user` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:38
