-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_release
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_eccn_decision`
--

DROP TABLE IF EXISTS `tbl_eccn_decision`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_eccn_decision` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `Q1_Text` varchar(200) NOT NULL,
  `Q1_Title` varchar(200) NOT NULL,
  `Q2_Description` varchar(200) NOT NULL,
  `Q2_Text` varchar(200) NOT NULL,
  `Q2_Title` varchar(200) NOT NULL,
  `Q3_Description` varchar(200) NOT NULL,
  `Q3_Text` varchar(200) NOT NULL,
  `Q3_Title` varchar(200) NOT NULL,
  `ECCN` varchar(15) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_eccn_decision`
--

LOCK TABLES `tbl_eccn_decision` WRITE;
/*!40000 ALTER TABLE `tbl_eccn_decision` DISABLE KEYS */;
INSERT INTO `tbl_eccn_decision` VALUES (1,'Is or includes an optical penetration','Could be a complete assembly, finished product or just the optical penetration itself','What is the market of the finished product','Military market only','','','','','TBD'),(2,'Is or includes an optical penetration','Could be a complete assembly, finished product or just the optical penetration itself','What is the market of the finished product','Civil market only','Oil&Gas, railway etc…','','','','NOCLASS'),(3,'Is or includes an optical penetration','Could be a complete assembly, finished product or just the optical penetration itself','What is the market of the finished product','Both military and civil markets','','','','','8A002.c'),(4,'A dual use product','Can be used in both civil and military application','','','','','','','8A002.c'),(5,'A military product','Dedicated to a military application','','','','','','','TBD'),(6,'None of the above','','','','','','','','NOCLASS');
/*!40000 ALTER TABLE `tbl_eccn_decision` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:39
