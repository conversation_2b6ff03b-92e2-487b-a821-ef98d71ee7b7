-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_release
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_manuf_routing`
--

DROP TABLE IF EXISTS `tbl_manuf_routing`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_manuf_routing` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `Routing_Num` int NOT NULL,
  `Routing_Rev` varchar(2) NOT NULL,
  `Author` varchar(45) NOT NULL,
  `DATE_Routing_Rev` date NOT NULL,
  `ID_Released_Drawing` int NOT NULL,
  `Reference` varchar(45) NOT NULL,
  `Ref_Rev` varchar(2) NOT NULL,
  `Routing_Title` varchar(45) NOT NULL,
  `Routing_Group` varchar(45) NOT NULL,
  `Routing_Count` int NOT NULL,
  `Routing_Shop` varchar(45) NOT NULL,
  `Routing_Item` int NOT NULL,
  `Routing_Operation` varchar(45) NOT NULL,
  `Routing_Workcenter` varchar(45) NOT NULL,
  `Time_Hold_Start` int NOT NULL,
  `Time_Preparation` int NOT NULL,
  `Time_Setup` int NOT NULL,
  `Time_Unit` int NOT NULL,
  `Time_Cycle` int NOT NULL,
  `Time_Hold_End` int NOT NULL,
  `Item_Instructions` varchar(200) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_manuf_routing`
--

LOCK TABLES `tbl_manuf_routing` WRITE;
/*!40000 ALTER TABLE `tbl_manuf_routing` DISABLE KEYS */;
/*!40000 ALTER TABLE `tbl_manuf_routing` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:39
