-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_release
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_q_doc_requirements`
--

DROP TABLE IF EXISTS `tbl_q_doc_requirements`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_q_doc_requirements` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(7) NOT NULL,
  `Description` tinytext NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=770 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_q_doc_requirements`
--

LOCK TABLES `tbl_q_doc_requirements` WRITE;
/*!40000 ALTER TABLE `tbl_q_doc_requirements` DISABLE KEYS */;
INSERT INTO `tbl_q_doc_requirements` VALUES (1,'DOC_Z01','Z01_Certificat 2.1_(EN10204)'),(2,'DOC_Z02','Z02_Certificat 2.2_(EN10204)'),(3,'DOC_Z03','Z03_Certificat 3.1_(EN10204)'),(4,'DOC_Z04','Z04_Certificat 3.1 NORSOK_(EN10204)'),(5,'DOC_Z05','Z05_Certificat 3.2_(EN10204)'),(6,'DOC_Z06','Z06_Certificat WPS PQR'),(7,'DOC_Z07','Z07_Certificat Choc Thermique'),(8,'DOC_Z08','Z08_Certificat Recuit'),(9,'DOC_Z09','Z09_Certificat Retention'),(10,'DOC_Z10','Z10_Certificat Test non destructif'),(11,'DOC_Z11','Z11_Date de Peremption'),(12,'DOC_Z12','Z12_Emballage Opaque'),(13,'DOC_Z13','Z13_Rapport de contrôle Niveau 0'),(14,'DOC_Z14','Z14_Rapport de contrôle Niveau 1'),(15,'DOC_Z15','Z15_Rapport de contrôle Niveau 2'),(16,'DOC_Z16','Z16_Rapport de contrôle Niveau 3'),(17,'DOC_Z17','Z17_Rapport de contrôle Niveau 4'),(18,'DOC_Z18','Z18_Rapport de contrôle Niveau 5'),(19,'DOC_Z19','Z19_ATEX'),(20,'DOC_Z20','Z20_Traitement de Surface'),(21,'DOC_Z21','Z21_Scellement'),(22,'DOC_Z22','Z22_Cahier des charges'),(23,'DOC_Z23','Z23_Tracabilité Niveau 1'),(24,'DOC_Z24','Z24_Tracabilité Niveau 2'),(25,'DOC_Z25','Z25_Certificat_Passivation');
/*!40000 ALTER TABLE `tbl_q_doc_requirements` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:41
