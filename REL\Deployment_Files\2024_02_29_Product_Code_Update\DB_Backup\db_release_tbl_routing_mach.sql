-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_release
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_routing_mach`
--

DROP TABLE IF EXISTS `tbl_routing_mach`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_routing_mach` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `Workcenter` varchar(45) NOT NULL,
  `Operation_Code` varchar(45) NOT NULL,
  `Item_Comment_Hint` varchar(45) NOT NULL,
  `Type` varchar(45) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=58 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_routing_mach`
--

LOCK TABLES `tbl_routing_mach` WRITE;
/*!40000 ALTER TABLE `tbl_routing_mach` DISABLE KEYS */;
INSERT INTO `tbl_routing_mach` VALUES (3,'DMU70','US_DMU','','POLE_FRAISAGE'),(4,'DMU80','US_DMU','','POLE_FRAISAGE'),(5,'ARROW','US_DMU','','POLE_FRAISAGE'),(6,'EBARBAGE TRIBOFINITION','US_EBARB','','POLE_EBARBAGE'),(7,'SABLAGE','US_SABLE','','AUTRES'),(8,'EBARBAGE','US_EBARB','','POLE_EBARBAGE'),(9,'EBAVURAGE FIN','US_EBFIN','','AUTRES'),(10,'FRAISEUSE DUFOUR','US_FRPER','','POLE_FRAISAGE_RADIAL'),(11,'PERCEUSE RADIALE','US_FRPER','','POLE_FRAISAGE_RADIAL'),(12,'PRESSE CK','US_FRPER','','POLE_FRAISAGE_RADIAL'),(13,'SKT 250 LM','US_KIA25','Prog N°','POLE_TOURS_KIA'),(14,'SKT 250 Y','US_KIA25','Prog N°','POLE_TOURS_KIA'),(15,'SKT 28 LM','US_KIA40','Prog N°','POLE_TOURS_KIA'),(16,'SKT 400M','US_KIA40','Prog N°','POLE_TOURS_KIA'),(17,'MANCHONS SCIAGE','US_MANCH','','POLE_MANCHONS'),(18,'MANCHONS SCIAGE AUTO','US_MANCH','','POLE_MANCHONS'),(19,'LAMAGE','US_PERC','','POLE_MACHINES_TRADITIONNELLES'),(20,'MOUCHAGE','US_PERC','','POLE_MACHINES_TRADITIONNELLES'),(21,'PERCEUSE','US_PERC','','POLE_MACHINES_TRADITIONNELLES'),(22,'TARAUDAGE','US_PERC','','POLE_MACHINES_TRADITIONNELLES'),(23,'RAMO','US_RAMO','','AUTRES'),(24,'CITIZEN L20-8','US_CIT20','Prog N°','AUTRES'),(25,'OKUMA LB300','US_LB300','Prog N°','AUTRES'),(26,'CENTRE HORIZONTAL OKUMA','US_MA40','Prog N°','AUTRES'),(27,'TOUR 2 AXES QT15','US_MAZAK','Prog N°','AUTRES'),(28,'CENTRE VERTICAL MORI SEIKI','US_MORI','Prog N°','AUTRES'),(29,'POLE Proto','US_PROTO','','AUTRES'),(30,'TOUR 2 AXES SOMAB','US_S450','Prog N°','AUTRES'),(31,'TOUR 3 AXES SOMAB','US_S450P','Prog N°','AUTRES'),(32,'TOUR 3 AXES TRAUB','US_TRAUB','Prog N°','AUTRES'),(33,'NAKAMURA TW30','US_TW30','Prog N°','POLE_NAKAMURA'),(34,'NAKAMURA WT100','US_WT100','Prog N°','POLE_NAKAMURA'),(35,'NAKAMURA WT300','US_WT300','Prog N°','POLE_NAKAMURA'),(36,'NAKAMURA NTM3','US_NTM3','Prog N°','POLE_NAKAMURA'),(37,'NAKAMURA NTY3-250','US_NTY3','Prog N°','POLE_NAKAMURA'),(38,'ELECTRO FIL','US_FIL','','AUTRES'),(39,'ELECTRO EROSION SODICK AQ55L','US_EDM','','AUTRES'),(40,'PEINTURE Svt FMTS','US_PEINT','Svt Plan et Svt FMTS','POLE_PEINTURE'),(41,'EPARGNE','US_PEINT','Svt Plan','POLE_PEINTURE'),(42,'FOUR','US_PEINT','','POLE_PEINTURE'),(43,'VERNIS Svt FMTS 5001','US_VERNI','','AUTRES'),(44,'DEGRAISSAGE LESSIVEL','US_CLEAN','','DEGRAISSAGE'),(45,'DEGRAISSAGE SOLVANT','US_CLEAN','','DEGRAISSAGE'),(46,'FOUR 600°','US_FOURS','','FOUR'),(47,'ASSEMBLAGE FERRO SIGNAL','RAIL_SIG','','AUTRES'),(48,'EXTERPRO','EXTERPRO','',''),(49,'SORTIE MAGASIN USINAGE','US_MAG','',''),(50,'DEBIT SCIAGE','US_DEBIT','',''),(51,'ASSEMBLAGE FERRO PUISSANCE','RAIL_PUI','','AUTRES'),(52,'STAR SR38','US_ST38','Prog N°','AUTRES'),(53,'NHX 4000','US_NHX','','AUTRES'),(54,'TRI-DI WENZEL','US_CON3D','Controle sur machine 3D','CON3D'),(55,'AJUSTAGE','US_AJUST','','AJUSTAGE'),(56,'TRI-DI WENZEL','US_CON3D','Controle sur machine 3D','CON3D'),(57,'AJUSTAGE','US_AJUST','','AJUSTAGE');
/*!40000 ALTER TABLE `tbl_routing_mach` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:40
