-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_scm
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_product_code`
--

DROP TABLE IF EXISTS `tbl_product_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_product_code` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `Code` varchar(45) NOT NULL,
  `Description` varchar(100) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=62 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_product_code`
--

LOCK TABLES `tbl_product_code` WRITE;
/*!40000 ALTER TABLE `tbl_product_code` DISABLE KEYS */;
INSERT INTO `tbl_product_code` VALUES (1,'Y23B','SCM'),(2,'BC58','CAM'),(3,'BC59','CMR'),(4,'BC60','CPM'),(5,'BC61','CER'),(6,'BC62','CEV'),(7,'BC63','CUP'),(8,'BC65','FXP'),(9,'BC66','CSR'),(10,'BC67','DTX'),(11,'BC68','EKO'),(12,'BC69','WYE'),(13,'BC70','Other (CEV1695/UTO/CHP/CSI/CUP/DUP/PE..)'),(14,'BC71','ONX'),(15,'BC72','Other (DMO / CSF)'),(16,'BC73','CPP'),(17,'BC74','UIC552'),(18,'BC75','UIC541'),(19,'BC76','AGR'),(20,'BC77','CCA'),(21,'BC78','CLN'),(22,'BC79','NGC'),(23,'BC80','CPE'),(24,'BC81','API / BEC / CVF / CSR1696 / DTR / MCM'),(25,'BC82','SIG'),(26,'BC83','CSF'),(27,'A475','CIR'),(28,'BC84','CMB'),(29,'BC85','CMC'),(30,'BC86','UIC558'),(31,'BC87','SRC'),(32,'BC88','CCI / CMI / CDI'),(33,'BC93','MU27P'),(34,'BC90','Tools OUT'),(35,'BC91','Accessories STD'),(36,'BC92','Misc. (other,semi finished, STD)'),(38,'BA16','UIC552 Solutions'),(39,'BA17','UIC558 Solutions'),(40,'BA18','UIC541 Solutions'),(41,'BA55','Other Solutions (CMC, CUP….)'),(42,'X993','CAM Solutions'),(43,'MODX','MOD'),(44,'W6C4','SEA-VI 6-100'),(45,'MPDX','MPD'),(46,'W33J','SEA-VI 33-400'),(47,'W33A','SEA-VI 33-900'),(48,'W11G','SEA-VI 11-1600'),(49,'W11P','SEA-VI 11-400'),(50,'EFSA','EFS'),(51,'MSDX','MSD'),(52,'W11T','SEA-VI 11-250'),(53,'9316','9316 (9416 etc..)'),(54,'ETHE','ETH/EHTF-electric'),(55,'ETHO','ETH/EHTF-optic'),(56,'DW66','SEA-VI 66-1250'),(57,'ILCO','ILC'),(58,'P11K','P11-PS500 & P11-PS650'),(59,'AMBP','AMB penetrators'),(60,'W132','SEA-VI 132-XXXX'),(61,'X992','CKB Mil/Aero');
/*!40000 ALTER TABLE `tbl_product_code` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:36
