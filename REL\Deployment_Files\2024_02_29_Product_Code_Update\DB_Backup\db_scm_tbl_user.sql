-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_scm
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_user`
--

DROP TABLE IF EXISTS `tbl_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_user` (
  `Key_User` int NOT NULL AUTO_INCREMENT,
  `TE_ID` tinytext NOT NULL,
  `Fullname` tinytext NOT NULL,
  `Email` tinytext NOT NULL,
  `Department` tinytext NOT NULL,
  `Acces` int DEFAULT NULL,
  PRIMARY KEY (`Key_User`)
) ENGINE=MyISAM AUTO_INCREMENT=850 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_user`
--

LOCK TABLES `tbl_user` WRITE;
/*!40000 ALTER TABLE `tbl_user` DISABLE KEYS */;
INSERT INTO `tbl_user` VALUES (207,'GCORDELET','CORDELET G.','<EMAIL>','Eng. Manager',NULL),(211,'SJUGE','JUGE S.','<EMAIL>','Engineering',0),(3,'MBAUER','BAUER M.','<EMAIL>','Engineering',NULL),(5,'FBELLANGER','BELLANGER F.','<EMAIL>','Inside Sales',NULL),(196,'JSAILLARD','SAILLARD J.','<EMAIL>','Product Management',NULL),(7,'NBELLET','BELLET N.','<EMAIL>','Laboratory',NULL),(8,'FBELLON','BELLON F.','<EMAIL>','Quality Project',NULL),(191,'PMARCHARD','MARCHARD P.','<EMAIL>','Supply Chain',NULL),(11,'ABIGOT','BIGOT A.','<EMAIL>','Laboratory',NULL),(12,'JBLANCHE','BLANCHE J.','<EMAIL>','Laboratory',NULL),(206,'SZISSLER','ZISSLER S.','<EMAIL>','Supply Chain',NULL),(14,'GBOIVIN','BOIVIN G.','<EMAIL>','Laboratory',NULL),(15,'RBONIN','BONIN R.','<EMAIL>','Quality Project',NULL),(16,'MBONNIN','BONNIN M.','<EMAIL>','Engineering',NULL),(17,'SBOUCHENOIRE','BOUCHENOIRE S.','<EMAIL>','Supply Chain',NULL),(18,'FBOURRET','BOURRET F.','<EMAIL>','Engineering',NULL),(19,'MBREGENT','BREGENT M.','<EMAIL>','Engineering',NULL),(20,'FBRIER','BRIER F.','<EMAIL>','Quality Prod',NULL),(22,'NCARREAU','CARREAU N.','<EMAIL>','HR',NULL),(23,'OCASSEGRAIN','CASSEGRAIN O.','<EMAIL>','Supply Chain',NULL),(24,'FCERISIER','CERISIER F.','<EMAIL>','Maintenance',NULL),(175,'PPARAT','PARAT P.','<EMAIL>','Industrialization',NULL),(26,'PCHARRON','CHARRON P.','<EMAIL>','Logistic',NULL),(27,'ACHATAIN','CHATAIN A.','<EMAIL>','Engineering',NULL),(205,'MLALANDE','LALANDE M.','<EMAIL>','Supply Chain',NULL),(30,'LCHESNE','CHESNE L.','<EMAIL>','Quality Prod',NULL),(31,'FCHEVRIER','CHEVRIER F.','<EMAIL>','Method',NULL),(32,'CCHOISNET','CHOISNET C.','<EMAIL>','Quality',NULL),(33,'FCHOULLIKH','CHOULLIKH F.','<EMAIL>','Maintenance',NULL),(34,'NCISSE','CISSE N.','<EMAIL>','Assembly',NULL),(35,'BCOATE','COATE B.','<EMAIL>','Logistic',NULL),(204,'LTISON','TISON L.','<EMAIL>','Industrialization',NULL),(203,'CMARCAIS','MARCAIS C.','<EMAIL>','Industrialization',NULL),(39,'ACOTOC','COTOC A.','<EMAIL>','Laboratory',NULL),(202,'FBELLON','BELLON F.','<EMAIL>','Quality Prod',NULL),(41,'ACRAPIS','CRAPIS A.','<EMAIL>','IT',NULL),(42,'CCRENAIS','CRENAIS C.','<EMAIL>','Logistic',NULL),(43,'JCRIBIER','CRIBIER J.','<EMAIL>','Direction',NULL),(201,'SLOIRE','LOIRE S.','<EMAIL>','Quality Prod',NULL),(45,'GDANGELO','D\'ANGELO G.','gd\'<EMAIL>','IT',NULL),(46,'SDANGEARD','DANGEARD S.','<EMAIL>','Logistic',NULL),(47,'ADARONDEAU','DARONDEAU A.','<EMAIL>','Assembly',NULL),(48,'GDAVID','DAVID G.','<EMAIL>','Laboratory',NULL),(49,'EDEAL','DEAL E.','<EMAIL>','Laboratory',NULL),(50,'SDENIS','DENIS S.','<EMAIL>','Marketing',NULL),(51,'VDERET','DERET V.','<EMAIL>','Laboratory',NULL),(195,'FBOURRET','BOURRET F.','<EMAIL>','Eng. Manager',NULL),(53,'CDESHAYES','DESHAYES C.','<EMAIL>','Quality',NULL),(194,'MBAUER','BAUER M.','<EMAIL>','Eng. Manager',NULL),(193,'CPOULAIN','POULAIN C.','<EMAIL>','Eng. Manager',NULL),(57,'WDONNE','DONNE W.','<EMAIL>','Quality Prod',NULL),(58,'NDORANGE','DORANGE N.','<EMAIL>','Engineering',NULL),(59,'EDOUVINET','DOUVINET E.','<EMAIL>','Finance',NULL),(192,'MBONNIN','BONNIN M.','<EMAIL>','Eng. Manager',NULL),(62,'SDURAND','DURAND S.','<EMAIL>','Metrology',NULL),(63,'CEON','EON C.','<EMAIL>','Metrology',NULL),(64,'NFARIAULT','FARIAULT N.','<EMAIL>','Engineering',NULL),(67,'FFOURNIER','FOURNIER F.','<EMAIL>','Logistic',NULL),(68,'NFOUSSIER DRANNE','FOUSSIER DRANNE N.','nfoussier <EMAIL>','Metrology',NULL),(69,'SFREMONT','FREMONT S.','<EMAIL>','Metrology',NULL),(190,'VPICHARD','PICHARD V.','<EMAIL>','Operation',NULL),(189,'FJARRIER','JARRIER F.','<EMAIL>','Operation',NULL),(74,'GGOT','GOT G.','<EMAIL>','Engineering',NULL),(184,'AMENAND','MENAND A.','<EMAIL>','Machining',NULL),(76,'JGOURDOU','GOURDOU J.','<EMAIL>','Laboratory',NULL),(77,'LGUICHARDON','GUICHARDON L.','<EMAIL>','Molding',NULL),(78,'YGUITTET','GUITTET Y.','<EMAIL>','Engineering',NULL),(79,'NHAMME','HAMME N.','<EMAIL>','Metrology',NULL),(80,'JHOCHART','HOCHART J.','<EMAIL>','Maintenance',NULL),(81,'SHOUDAYER','HOUDAYER S.','<EMAIL>','Logistic',NULL),(200,'CBAUDRY','BAUDRY C.','<EMAIL>','Molding',NULL),(181,'JFGALIPAUD','GALIPAUD JF.','<EMAIL>','Engineering',NULL),(86,'SJADAUD','JADAUD S.','<EMAIL>','Project',NULL),(188,'FBOURRET','BOURRET F.','<EMAIL>','Operation',NULL),(89,'FJARRIER','JARRIER F.','<EMAIL>','Engineering',NULL),(90,'JJEAN','JEAN J.','<EMAIL>','Project',NULL),(91,'SJULIEN','JULIEN S.','<EMAIL>','Engineering',NULL),(92,'FJUST','JUST F.','<EMAIL>','Logistic',NULL),(93,'SKAROU','KAROU S.','<EMAIL>','Quality',NULL),(94,'FKLEINDIENST','KLEINDIENST F.','<EMAIL>','Direction',NULL),(95,'NLACROIX','LACROIX N.','<EMAIL>','Engineering',NULL),(96,'CLAGATHU','LAGATHU C.','<EMAIL>','Engineering',NULL),(97,'FLAMY','LAMY F.','<EMAIL>','GID',NULL),(98,'JLANGLOIS','LANGLOIS J.','<EMAIL>','Industrialization',NULL),(199,'JCOUTADEUR','COUTADEUR J.','<EMAIL>','Supply Chain',NULL),(101,'CLEBOULEUX','LEBOULEUX C.','<EMAIL>','Logistic',NULL),(102,'FLECHARTIER','LECHARTIER F.','<EMAIL>','Laboratory',NULL),(103,'JLEGEAY','LEGEAY J.','<EMAIL>','Product Management',NULL),(212,'JBERTIAU','BERTIAU J.','<EMAIL>','Engineering',0),(105,'CLELOUP','LELOUP C.','<EMAIL>','Quality',NULL),(187,'MGAUTIER','GAUTIER M.','<EMAIL>','Engineering',NULL),(186,'GPEIGNE','PEIGNE G.','<EMAIL>','Molding',NULL),(108,'MLOUIS','LOUIS M.','<EMAIL>','Supply Chain',NULL),(109,'AMADELIN','MADELIN A.','<EMAIL>','Project',NULL),(183,'FFOURNIER','FOURNIER F.','<EMAIL>','Supply Chain',NULL),(112,'JMARAIS','MARAIS J.','<EMAIL>','Method',NULL),(182,'JJEAN','JEAN J.','<EMAIL>','Product Management',NULL),(114,'GMARTIN','MARTIN G.','<EMAIL>','Laboratory',NULL),(115,'LMARTINEAU','MARTINEAU L.','<EMAIL>','HR',NULL),(116,'MMEDARD','MEDARD M.','<EMAIL>','Supply Chain',NULL),(185,'PPARAT','PARAT P.','<EMAIL>','Molding',NULL),(119,'AMONTEIRO','MONTEIRO A.','<EMAIL>','Metrology',NULL),(180,'MROSSI','ROSSI M.','<EMAIL>','Finance',NULL),(122,'MNICOL','NICOL M.','<EMAIL>','Maintenance',NULL),(198,'AMARTIN','MARTIN A.','<EMAIL>','Supply Chain',NULL),(124,'PPARAT','PARAT P.','<EMAIL>','Engineering',NULL),(125,'RPARME','PARME R.','<EMAIL>','Engineering',NULL),(126,'SPASCAUD','PASCAUD S.','<EMAIL>','Method',NULL),(127,'EPAULMERY','PAULMERY E.','<EMAIL>','Laboratory',NULL),(128,'GPEIGNE','PEIGNE G.','<EMAIL>','Engineering',NULL),(129,'TPERES','PERES T.','<EMAIL>','Method',NULL),(130,'LPEREZ','PEREZ L.','<EMAIL>','Quality',NULL),(131,'GPERNET','PERNET G.','<EMAIL>','Engineering',NULL),(132,'VPICHARD','PICHARD V.','<EMAIL>','Engineering',NULL),(174,'NROUILLARD','ROUILLARD N.','<EMAIL>','Molding',NULL),(134,'PPICHON','PICHON P.','<EMAIL>','Quality',NULL),(197,'BLOISON','LOISON B.','<EMAIL>','Purchasing',NULL),(136,'CPOULAIN','POULAIN C.','<EMAIL>','Engineering',NULL),(137,'APREAU','PREAU A.','<EMAIL>','Method',NULL),(138,'LPRENANT','PRENANT L.','<EMAIL>','Machining',NULL),(179,'JMARAIS','MARAIS J.','<EMAIL>','Engineering',NULL),(178,'SGUILLARD','GUILLARD S.','<EMAIL>','Engineering',NULL),(177,'TLELONG','LELONG T.','<EMAIL>','Purchasing',NULL),(144,'EREVAUD','REVAUD E.','<EMAIL>','Laboratory',NULL),(173,'VPERCIVAL','PERCIVAL V.','<EMAIL>','Project',NULL),(146,'WRIGUET','RIGUET W.','<EMAIL>','Purchasing',NULL),(176,'GPEIGNE','PEIGNE G.','<EMAIL>','Industrialization',NULL),(149,'PROBICHON','ROBICHON P.','<EMAIL>','Quality',NULL),(150,'AROSELEUR','ROSELEUR A.','<EMAIL>','Finance',NULL),(151,'MROSSI','ROSSI M.','<EMAIL>','GID',NULL),(152,'NROUILLARD','ROUILLARD N.','<EMAIL>','Industrialization',NULL),(153,'PSAINTOT','SAINTOT P.','<EMAIL>','Laboratory',NULL),(154,'LSALE','SALE L.','<EMAIL>','Laboratory',NULL),(155,'MSAVIGNARD','SAVIGNARD M.','<EMAIL>','Inside Sales',NULL),(156,'SSHARP','SHARP S.','<EMAIL>','HR',NULL),(172,'STOURNIER','TOURNIER S.','<EMAIL>','Product Management',NULL),(160,'LTISON','TISON L.','<EMAIL>','Method',NULL),(161,'MVAUTEY','VAUTEY M.','<EMAIL>','Maintenance',NULL),(162,'CVILLETTE','VILLETTE C.','<EMAIL>','Quality Prod',NULL),(171,'YGUITTET','GUITTET Y.','<EMAIL>','GID',NULL),(164,'MVRIGNAUD','VRIGNAUD M.','<EMAIL>','Quality Project',NULL),(165,'GWENTS','WENTS G.','<EMAIL>','Molding',NULL),(166,'SZIANE','ZIANE S.','<EMAIL>','Laboratory',NULL),(167,'SJULIEN','JULIEN S.','<EMAIL>','GID',NULL),(168,'MBAUER','BAUER M.','<EMAIL>','Product Management',NULL),(169,'MBONNIN','BONNIN M.','<EMAIL>','Product Management',NULL),(170,'SPASCAUD','PASCAUD S.','<EMAIL>','Machining',NULL),(208,'GCORDELET','CORDELET G.','<EMAIL>','Engineering',NULL),(209,'CMARCAIS','MARCAIS C.','<EMAIL>','Method',NULL),(210,'TPISSOT','PISSOT T.','<EMAIL>','Engineering',0),(213,'ABOUCAUD','BOUCAUD A.','<EMAIL>','Engineering',0);
/*!40000 ALTER TABLE `tbl_user` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:36
