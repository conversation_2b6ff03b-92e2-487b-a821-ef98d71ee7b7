-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_timesheet
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_rate`
--

DROP TABLE IF EXISTS `tbl_rate`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_rate` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `Workcenter` varchar(45) NOT NULL,
  `Hourly_Rate` float NOT NULL,
  `Fiscal_Year` int NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=179 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_rate`
--

LOCK TABLES `tbl_rate` WRITE;
/*!40000 ALTER TABLE `tbl_rate` DISABLE KEYS */;
INSERT INTO `tbl_rate` VALUES (28,'ZPSQR002',30,2022),(27,'ZPSQR001',43,2022),(26,'ZPSMG003',0,2022),(25,'ZPSMT002',54,2022),(24,'ZPSRDD01',39,2022),(31,'ZPSMT001',0,2022),(22,'ZPSRDE01',54,2022),(21,'ZPSPC001',56,2022),(20,'ZPSQR003',42,2022),(19,'ZPSLB002',46,2022),(18,'ZPSFM001',36,2022),(17,'ZPSMG001',74,2022),(29,'ZPSPR001',34,2022),(30,'ZPSPR004',34,2022),(32,'ZPSFM001',21,2013),(33,'ZPSFM001',23,2014),(34,'ZPSFM001',90,2015),(35,'ZPSFM001',90,2016),(36,'ZPSFM001',23,2017),(37,'ZPSFM001',0,2018),(38,'ZPSFM001',29,2019),(39,'ZPSFM001',32,2020),(40,'ZPSFM001',32,2021),(41,'ZPSLB002',32,2013),(42,'ZPSLB002',30,2014),(43,'ZPSLB002',30,2015),(44,'ZPSLB002',30,2016),(45,'ZPSLB002',30,2017),(46,'ZPSLB002',37,2018),(47,'ZPSLB002',42,2019),(48,'ZPSLB002',46,2020),(49,'ZPSLB002',46,2021),(50,'ZPSMG001',36,2013),(51,'ZPSMG001',35,2014),(52,'ZPSMG001',35,2015),(53,'ZPSMG001',35,2016),(54,'ZPSMG001',35,2017),(55,'ZPSMG001',48,2018),(56,'ZPSMG001',51,2019),(57,'ZPSMG001',52,2020),(58,'ZPSMG001',0,2021),(59,'ZPSMG003',0,2013),(60,'ZPSMG003',0,2014),(61,'ZPSMG003',0,2015),(62,'ZPSMG003',0,2016),(63,'ZPSMG003',0,2017),(64,'ZPSMG003',0,2018),(65,'ZPSMG003',24,2019),(66,'ZPSMG003',41,2020),(67,'ZPSMG003',0,2021),(68,'ZPSMT001',0,2013),(69,'ZPSMT001',0,2014),(70,'ZPSMT001',0,2015),(71,'ZPSMT001',0,2016),(72,'ZPSMT001',36,2017),(73,'ZPSMT001',0,2018),(74,'ZPSMT001',45,2019),(75,'ZPSMT001',48,2020),(76,'ZPSMT001',48,2021),(77,'ZPSMT002',36,2013),(78,'ZPSMT002',36,2014),(79,'ZPSMT002',36,2015),(80,'ZPSMT002',36,2016),(81,'ZPSMT002',36,2017),(82,'ZPSMT002',45,2018),(83,'ZPSMT002',45,2019),(84,'ZPSMT002',48,2020),(85,'ZPSMT002',48,2021),(86,'ZPSRDE01',32,2013),(87,'ZPSRDE01',34,2014),(88,'ZPSRDE01',34,2015),(89,'ZPSRDE01',34,2016),(90,'ZPSRDE01',34,2017),(91,'ZPSRDE01',43,2018),(92,'ZPSRDE01',47,2019),(93,'ZPSRDE01',48,2020),(94,'ZPSRDE01',48,2021),(95,'ZPSRDD01',23,2013),(96,'ZPSRDD01',22,2014),(97,'ZPSRDD01',22,2015),(98,'ZPSRDD01',22,2016),(99,'ZPSRDD01',22,2017),(100,'ZPSRDD01',28,2018),(101,'ZPSRDD01',29,2019),(102,'ZPSRDD01',34,2020),(103,'ZPSRDD01',34,2021),(104,'ZPSPC001',0,2013),(105,'ZPSPC001',0,2014),(106,'ZPSPC001',0,2015),(107,'ZPSPC001',0,2016),(108,'ZPSPC001',0,2017),(109,'ZPSPC001',0,2018),(110,'ZPSPC001',0,2019),(111,'ZPSPC001',0,2020),(112,'ZPSPC001',0,2021),(113,'ZPSPR002',23,2013),(114,'ZPSPR002',22,2014),(115,'ZPSPR002',22,2015),(116,'ZPSPR002',22,2016),(117,'ZPSPR002',22,2017),(118,'ZPSPR002',0,2018),(119,'ZPSPR002',31,2019),(120,'ZPSPR002',0,2020),(121,'ZPSPR002',0,2021),(122,'ZPSQR002',0,2013),(123,'ZPSQR002',0,2014),(124,'ZPSQR002',0,2015),(125,'ZPSQR002',0,2016),(126,'ZPSQR002',0,2017),(127,'ZPSQR002',0,2018),(128,'ZPSQR002',20,2019),(129,'ZPSQR002',32,2020),(130,'ZPSQR002',32,2021),(131,'ZPSQR003',26,2013),(132,'ZPSQR003',26,2014),(133,'ZPSQR003',26,2015),(134,'ZPSQR003',26,2016),(135,'ZPSQR003',35,2017),(136,'ZPSQR003',27,2018),(137,'ZPSQR003',33,2019),(138,'ZPSQR003',38,2020),(139,'ZPSQR003',38,2021),(140,'ZPSQR004',0,2013),(141,'ZPSQR004',0,2014),(142,'ZPSQR004',0,2015),(143,'ZPSQR004',0,2016),(144,'ZPSQR004',0,2017),(145,'ZPSQR004',0,2018),(146,'ZPSQR004',39,2019),(147,'ZPSQR004',41,2020),(148,'ZPSQR004',0,2021),(149,'ZPSAO001',0,2015),(153,'ZPSRDE01',54,2023),(154,'ZPSRDD01',39,2023),(155,'ZPSQR003',42,2023),(156,'ZPSQR002',30,2023),(157,'ZPSQR001',43,2023),(158,'ZPSMT002',54,2023),(159,'ZPSMT001',0,2023),(160,'ZPSMG001',74,2023),(161,'ZPSPR001',34,2023),(162,'ZPSPR004',34,2023),(163,'ZPSLB002',46,2023),(164,'ZPSFM001',36,2023),(165,'ZPSMG001',129,2024),(166,'ZPSPC001',103,2024),(167,'ZPSPR001',70,2024),(168,'ZPSQR002',75,2024),(169,'ZPSQR001',86,2024),(170,'ZPSQR003',75,2024),(171,'ZPSLB002',97,2024),(172,'ZPSFM001',71,2024),(173,'ZPSRDD01',74,2024),(174,'ZPSRDE01',94,2024),(175,'ZPSMT002',110,2024),(176,'ZPSMT001',80,2024),(177,'ZPSPR004',70,2024),(178,'ZPSMG003',0,2024);
/*!40000 ALTER TABLE `tbl_rate` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:42
