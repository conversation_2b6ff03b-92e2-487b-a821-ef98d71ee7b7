-- MySQL dump 10.13  Distrib 8.0.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_timesheet
-- ------------------------------------------------------
-- Server version	8.0.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!50503 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_user`
--

DROP TABLE IF EXISTS `tbl_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!50503 SET character_set_client = utf8mb4 */;
CREATE TABLE `tbl_user` (
  `ID` int NOT NULL AUTO_INCREMENT,
  `Fullname` varchar(45) NOT NULL,
  `SCM_ID` varchar(45) NOT NULL,
  `Department` varchar(45) NOT NULL,
  `Workcenter` varchar(45) NOT NULL,
  `Email` varchar(45) NOT NULL,
  `CIR` tinyint(1) NOT NULL DEFAULT '0',
  `SAP` tinyint(1) NOT NULL DEFAULT '1',
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=105 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_user`
--

LOCK TABLES `tbl_user` WRITE;
/*!40000 ALTER TABLE `tbl_user` DISABLE KEYS */;
INSERT INTO `tbl_user` VALUES (1,'BAUER M.','MBAUER','Engineering','ZPSMG001','<EMAIL>',1,1),(2,'BELLET N.','NBELLET','Laboratory','ZPSFM001','<EMAIL>',1,1),(3,'BELLON F.','FBELLON','Quality','','<EMAIL>',0,0),(4,'BLANCHE J.','JBLANCHE','Laboratory','ZPSFM001','<EMAIL>',1,1),(5,'BOIVIN G.','GBOIVIN','Laboratory','ZPSLB002','<EMAIL>',1,1),(6,'BONIN R.','RBONIN','Quality','ZPSQR001','<EMAIL>',1,1),(7,'BOURRET F.','FBOURRET','Operation','','<EMAIL>',0,0),(8,'BREGENT M.','MBREGENT','Engineering','ZPSRDE01','<EMAIL>',1,1),(9,'CHATAIN A.','ACHATAIN','Engineering','ZPSRDD01','<EMAIL>',1,1),(10,'CHEVRIER F.','FCHEVRIER','Operation','ZPSMT001','<EMAIL>',1,1),(11,'CISSE N.','NCISSE','Operation','ZPSPR001','<EMAIL>',1,1),(12,'COTOC A.','ACOTOC','Laboratory','ZPSFM001','<EMAIL>',1,1),(13,'DAVID G.','GDAVID','Laboratory','ZPSFM001','<EMAIL>',1,1),(14,'DEAL E.','EDEAL','Laboratory','ZPSFM001','<EMAIL>',1,1),(15,'DERET V.','VDERET','Laboratory','ZPSLB002','<EMAIL>',1,1),(16,'DORANGE N.','NDORANGE','Engineering','ZPSRDE01','<EMAIL>',1,1),(17,'DOUVINET E.','EDOUVINET','Finance','ZPSMG003','<EMAIL>',1,1),(18,'FARIAULT N.','NFARIAULT','Engineering','ZPSRDE01','<EMAIL>',1,1),(19,'GOT G.','GGOT','Engineering','ZPSRDE01','<EMAIL>',1,1),(92,'PERCIVAL V.','VPERCIVAL','Project','ZPSMG001','<EMAIL>',1,1),(21,'GOURDOU JF.','JFGOURDOU','Laboratory','ZPSFM001','<EMAIL>',1,1),(22,'GUITTET Y.','YGUITTET','Engineering','ZPSRDD01','<EMAIL>',1,1),(23,'JADAUD S.','SJADAUD','Project','ZPSMG001','<EMAIL>',1,1),(24,'JANVIER I.','IJANVIER','Operation','ZPSPR001','<EMAIL>',1,1),(25,'JARRIER F.','FJARRIER','Operation','ZPSPR004','<EMAIL>',1,1),(26,'JEAN J.','JJEAN','Project','ZPSMG001','<EMAIL>',1,1),(27,'JULIEN S.','SJULIEN','Engineering','ZPSQR002','<EMAIL>',1,1),(28,'KAROU S.','SKAROU','Quality','ZPSQR002','<EMAIL>',1,1),(29,'LACROIX N.','NLACROIX','Engineering','ZPSRDE01','<EMAIL>',1,1),(30,'LAGATHU C.','CLAGATHU','Engineering','ZPSRDD01','<EMAIL>',1,1),(31,'LANGLOIS J.','JLANGLOIS','Industrialization','ZPSMT002','<EMAIL>',1,1),(32,'LECHARTIER F.','FLECHARTIER','Laboratory','ZPSFM001','<EMAIL>',1,1),(33,'LELONG T.','TLELONG','Engineering','ZPSRDE01','<EMAIL>',1,1),(34,'MADELIN A.','AMADELIN','Project','ZPSMG001','<EMAIL>',1,1),(35,'MARAIS J.','JMARAIS','Operation','ZPSRDD01','<EMAIL>',1,1),(36,'OPERATION_ASSEMBLAGE','OPE_GEN','Operation','ZPSPR001','',1,1),(37,'OPERATION_OSI','OSI_GEN','Operation','ZPSPR004','',1,1),(38,'PARAT P.','PPARAT','Engineering','ZPSMT002','<EMAIL>',1,1),(39,'PARME R.','RPARME','Engineering','ZPSRDE01','<EMAIL>',1,1),(40,'PAULMERY E.','EPAULMERY','Laboratory','ZPSFM001','<EMAIL>',1,1),(41,'PEIGNE G.','GPEIGNE','Engineering','ZPSMT002','<EMAIL>',1,1),(42,'PERES T.','TPERES','Operation','ZPSMT001','<EMAIL>',1,1),(43,'PERNET G.','GPERNET','Engineering','ZPSRDD01','<EMAIL>',1,1),(45,'REVAUD E.','EREVAUD','Laboratory','ZPSLB002','<EMAIL>',1,1),(46,'RIGUET W.','WRIGUET','Purchasing','ZPSPC001','<EMAIL>',1,1),(47,'ROSELEUR A.','AROSELEUR','Finance','ZPSMG003','<EMAIL>',1,1),(48,'ROUILLARD N.','NROUILLARD','Industrialization','ZPSMT002','<EMAIL>',1,1),(50,'SALE L.','LSALE','Laboratory','ZPSFM001','<EMAIL>',1,1),(51,'TISON L.','LTISON','Operation','ZPSMT001','<EMAIL>',1,1),(52,'ZIANE S.','SZIANE','Laboratory','ZPSFM001','<EMAIL>',1,1),(83,'BONNIN M.','MBONNIN','Industrial Division','','<EMAIL>',0,0),(77,'LHORME F.','FLHORME','Operation','ZPSPR001','<EMAIL>',1,1),(55,'BELLANGER S.','SBELLANGER','Operation','ZPSPR001','<EMAIL>',1,1),(56,'BENOIT J.','JBENOIT','Operation','ZPSPR001','<EMAIL>',1,1),(57,'CERISIER F.','FCERISIER','Operation','ZPSPR001','<EMAIL>',1,1),(58,'DARONDEAU A.','ADARONDEAU','Operation','ZPSPR001','<EMAIL>',1,1),(59,'DEROUIN A.','ADEROUIN','Operation','ZPSPR001','<EMAIL>',1,1),(60,'DODIER C.','CDODIER','Operation','ZPSPR001','<EMAIL>',1,1),(61,'DUBOIS S.','SDUBOIS','Operation','ZPSPR001','<EMAIL>',1,1),(62,'GAGNEUX B.','BGAGNEUX','Operation','ZPSPR001','<EMAIL>',1,1),(64,'JAHAN L.','LJAHAN','Operation','ZPSPR001','<EMAIL>',1,1),(66,'LAUNAY S.','SLAUNAY','Operation','ZPSPR001','<EMAIL>',1,1),(67,'LERUEZ P.','PLERUEZ','Operation','ZPSPR001','<EMAIL>',1,1),(68,'MAILLARD P.','PMAILLARD','Operation','ZPSPR001','<EMAIL>',1,1),(69,'MARCAIS C.','CMARCAIS','Operation','ZPSPR001','<EMAIL>',1,1),(70,'MOUTON L.','LMOUTON','Operation','ZPSPR001','<EMAIL>',1,1),(71,'PICHON A.','APICHON','Operation','ZPSPR001','<EMAIL>',1,1),(72,'PUISSET F.','FPUISSET','Operation','ZPSPR001','<EMAIL>',1,1),(73,'RIVIERE S.','SRIVIERE','Operation','ZPSPR001','<EMAIL>',1,1),(100,'RENOU T.','TRENOU','Operation','ZPSPR001','<EMAIL>',1,1),(78,'PUCHOT B.','BPUCHOT','Operation','ZPSPR001','<EMAIL>',1,1),(76,'TACHEAU M.','MTACHEAU','Operation','ZPSPR001','<EMAIL>',1,1),(79,'BECHET D.','DBECHET','Operation','ZPSPR001','<EMAIL>',1,1),(80,'THERAIN T.','TTHERAIN','Operation','ZPSPR001','<EMAIL>',1,1),(81,'POULAIN C.','CPOULAIN','Engineering','ZPSMG001','<EMAIL>',1,1),(82,'VRIGNAUD M.','MVRIGNAUD','Quality','ZPSQR001','<EMAIL>',1,1),(85,'PREAU A.','APREAU','Operation','ZPSMT001','<EMAIL>',1,1),(86,'GUILLARD S.','SGUILLARD','Engineering','ZPSRDE01','<EMAIL>',1,1),(87,'GALIPAUD JF.','JFGALIPAUD','Engineering','ZPSRDD01','<EMAIL>',1,1),(99,'DESMAISON C.','CDESMAISON','Operation','ZPSPR001','<EMAIL>',1,1),(90,'CORDELET G.','GCORDELET','Engineering','ZPSRDE01','<EMAIL>',1,1),(91,'AUDAT C.','CAUDAT','Quality','ZPSQR001','<EMAIL>',1,1),(93,'PISSOT T.','TPISSOT','Engineering','ZPSRDE01','<EMAIL>',1,1),(94,'BAUDRY C.','CBAUDRY','Molding','ZPSMT001','<EMAIL>',1,1),(95,'LOISON B.','BLOISON','Purchasing','ZPSPC001','<EMAIL>',1,1),(96,'HOURRIER F.','FHOURRIER','Laboratory','ZPSFM001','<EMAIL>',1,1),(97,'PRENANT L.','LPRENANT','Machining','ZPSMT001','<EMAIL>',1,1),(98,'BERTIAU J.','JBERTIAU','Engineering','ZPSRDD01','<EMAIL>',1,1),(101,'JUGE S.','SJUGE','Engineering','ZPSRDE01','<EMAIL>',1,1),(102,'JOUBERT P.','PJOUBERT','Operation','ZPSPR001','<EMAIL>',1,1),(103,'PRIMAULT S.','SPRIMAULT','Operation','ZPSPR001','<EMAIL>',1,1),(104,'BOUCAUD A.','ABOUCAUD','Engineering','ZPSRDE01','<EMAIL>',1,1);
/*!40000 ALTER TABLE `tbl_user` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2024-02-29  8:41:43
