MIME-Version: 1.0
Content-Type: multipart/related; boundary="----=_NextPart_01DA6AED.5E266960"

This document is a Single File Web Page, also known as a Web Archive file.  If you are seeing this message, your browser or editor doesn't support Web Archive files.  Please download a browser that supports Web Archive.

------=_NextPart_01DA6AED.5E266960
Content-Location: file:///C:/F16BD585/ProductcodePoleindustrie.htm
Content-Transfer-Encoding: quoted-printable
Content-Type: text/html; charset="us-ascii"

<html xmlns:o=3D"urn:schemas-microsoft-com:office:office"
xmlns:w=3D"urn:schemas-microsoft-com:office:word"
xmlns:m=3D"http://schemas.microsoft.com/office/2004/12/omml"
xmlns=3D"http://www.w3.org/TR/REC-html40">

<head>
<meta http-equiv=3DContent-Type content=3D"text/html; charset=3Dus-ascii">
<meta name=3DProgId content=3DWord.Document>
<meta name=3DGenerator content=3D"Microsoft Word 15">
<meta name=3DOriginator content=3D"Microsoft Word 15">
<link rel=3DFile-List href=3D"ProductcodePoleindustrie_files/filelist.xml">
<link rel=3DEdit-Time-Data href=3D"ProductcodePoleindustrie_files/editdata.=
mso">
<link rel=3DthemeData href=3D"ProductcodePoleindustrie_files/themedata.thmx=
">
<link rel=3DcolorSchemeMapping
href=3D"ProductcodePoleindustrie_files/colorschememapping.xml">
<!--[if gte mso 9]><xml>
 <w:WordDocument>
  <w:Zoom>0</w:Zoom>
  <w:DocumentKind>DocumentEmail</w:DocumentKind>
  <w:TrackMoves/>
  <w:TrackFormatting/>
  <w:ValidateAgainstSchemas/>
  <w:SaveIfXMLInvalid>false</w:SaveIfXMLInvalid>
  <w:IgnoreMixedContent>false</w:IgnoreMixedContent>
  <w:AlwaysShowPlaceholderText>false</w:AlwaysShowPlaceholderText>
  <w:DoNotPromoteQF/>
  <w:LidThemeOther>FR</w:LidThemeOther>
  <w:LidThemeAsian>X-NONE</w:LidThemeAsian>
  <w:LidThemeComplexScript>AR-SA</w:LidThemeComplexScript>
  <w:Compatibility>
   <w:DoNotExpandShiftReturn/>
   <w:BreakWrappedTables/>
   <w:SnapToGridInCell/>
   <w:WrapTextWithPunct/>
   <w:UseAsianBreakRules/>
   <w:DontGrowAutofit/>
   <w:SplitPgBreakAndParaMark/>
   <w:EnableOpenTypeKerning/>
   <w:DontFlipMirrorIndents/>
   <w:OverrideTableStyleHps/>
  </w:Compatibility>
  <w:BrowserLevel>MicrosoftInternetExplorer4</w:BrowserLevel>
  <m:mathPr>
   <m:mathFont m:val=3D"Cambria Math"/>
   <m:brkBin m:val=3D"before"/>
   <m:brkBinSub m:val=3D"&#45;-"/>
   <m:smallFrac m:val=3D"off"/>
   <m:dispDef/>
   <m:lMargin m:val=3D"0"/>
   <m:rMargin m:val=3D"0"/>
   <m:defJc m:val=3D"centerGroup"/>
   <m:wrapIndent m:val=3D"1440"/>
   <m:intLim m:val=3D"subSup"/>
   <m:naryLim m:val=3D"undOvr"/>
  </m:mathPr></w:WordDocument>
</xml><![endif]--><!--[if gte mso 9]><xml>
 <w:LatentStyles DefLockedState=3D"false" DefUnhideWhenUsed=3D"false"
  DefSemiHidden=3D"false" DefQFormat=3D"false" DefPriority=3D"99"
  LatentStyleCount=3D"376">
  <w:LsdException Locked=3D"false" Priority=3D"0" QFormat=3D"true" Name=3D"=
Normal"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" QFormat=3D"true" Name=3D"=
heading 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"heading 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"heading 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"heading 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"heading 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"heading 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"heading 7"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"heading 8"/>
  <w:LsdException Locked=3D"false" Priority=3D"9" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"heading 9"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 5"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 6"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 7"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 8"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index 9"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 7"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 8"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"toc 9"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Normal Indent"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"footnote text"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"annotation text"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"header"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"footer"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"index heading"/>
  <w:LsdException Locked=3D"false" Priority=3D"35" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"caption"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"table of figures"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"envelope address"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"envelope return"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"footnote reference"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"annotation reference"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"line number"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"page number"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"endnote reference"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"endnote text"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"table of authorities"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"macro"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"toa heading"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Bullet"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Number"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List 5"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Bullet 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Bullet 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Bullet 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Bullet 5"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Number 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Number 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Number 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Number 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"10" QFormat=3D"true" Name=3D=
"Title"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Closing"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Signature"/>
  <w:LsdException Locked=3D"false" Priority=3D"1" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"Default Paragraph Font"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Body Text"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Body Text Indent"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Continue"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Continue 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Continue 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Continue 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"List Continue 5"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Message Header"/>
  <w:LsdException Locked=3D"false" Priority=3D"11" QFormat=3D"true" Name=3D=
"Subtitle"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Salutation"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Date"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Body Text First Indent"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Body Text First Indent 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Note Heading"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Body Text 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Body Text 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Body Text Indent 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Body Text Indent 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Block Text"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Hyperlink"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"FollowedHyperlink"/>
  <w:LsdException Locked=3D"false" Priority=3D"22" QFormat=3D"true" Name=3D=
"Strong"/>
  <w:LsdException Locked=3D"false" Priority=3D"20" QFormat=3D"true" Name=3D=
"Emphasis"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Document Map"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Plain Text"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"E-mail Signature"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Top of Form"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Bottom of Form"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Normal (Web)"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Acronym"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Address"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Cite"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Code"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Definition"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Keyboard"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Preformatted"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Sample"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Typewriter"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"HTML Variable"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Normal Table"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"annotation subject"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"No List"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Outline List 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Outline List 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Outline List 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Simple 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Simple 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Simple 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Classic 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Classic 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Classic 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Classic 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Colorful 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Colorful 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Colorful 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Columns 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Columns 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Columns 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Columns 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Columns 5"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Grid 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Grid 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Grid 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Grid 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Grid 5"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Grid 6"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Grid 7"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Grid 8"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table List 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table List 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table List 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table List 4"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table List 5"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table List 6"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table List 7"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table List 8"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table 3D effects 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table 3D effects 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table 3D effects 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Contemporary"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Elegant"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Professional"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Subtle 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Subtle 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Web 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Web 2"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Web 3"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Balloon Text"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" Name=3D"Table Grid"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Table Theme"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" Name=3D"Placeholder =
Text"/>
  <w:LsdException Locked=3D"false" Priority=3D"1" QFormat=3D"true" Name=3D"=
No Spacing"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" Name=3D"Light Shading"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" Name=3D"Light List"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" Name=3D"Light Grid"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" Name=3D"Medium Shading 1=
"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" Name=3D"Medium Shading 2=
"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" Name=3D"Medium List 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" Name=3D"Medium List 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" Name=3D"Medium Grid 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" Name=3D"Medium Grid 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" Name=3D"Medium Grid 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" Name=3D"Dark List"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" Name=3D"Colorful Shading=
"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" Name=3D"Colorful List"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" Name=3D"Colorful Grid"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" Name=3D"Light Shading Ac=
cent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" Name=3D"Light List Accen=
t 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" Name=3D"Light Grid Accen=
t 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" Name=3D"Medium Shading 1=
 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" Name=3D"Medium Shading 2=
 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" Name=3D"Medium List 1 Ac=
cent 1"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" Name=3D"Revision"/>
  <w:LsdException Locked=3D"false" Priority=3D"34" QFormat=3D"true"
   Name=3D"List Paragraph"/>
  <w:LsdException Locked=3D"false" Priority=3D"29" QFormat=3D"true" Name=3D=
"Quote"/>
  <w:LsdException Locked=3D"false" Priority=3D"30" QFormat=3D"true"
   Name=3D"Intense Quote"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" Name=3D"Medium List 2 Ac=
cent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" Name=3D"Medium Grid 1 Ac=
cent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" Name=3D"Medium Grid 2 Ac=
cent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" Name=3D"Medium Grid 3 Ac=
cent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" Name=3D"Dark List Accent=
 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" Name=3D"Colorful Shading=
 Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" Name=3D"Colorful List Ac=
cent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" Name=3D"Colorful Grid Ac=
cent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" Name=3D"Light Shading Ac=
cent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" Name=3D"Light List Accen=
t 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" Name=3D"Light Grid Accen=
t 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" Name=3D"Medium Shading 1=
 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" Name=3D"Medium Shading 2=
 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" Name=3D"Medium List 1 Ac=
cent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" Name=3D"Medium List 2 Ac=
cent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" Name=3D"Medium Grid 1 Ac=
cent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" Name=3D"Medium Grid 2 Ac=
cent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" Name=3D"Medium Grid 3 Ac=
cent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" Name=3D"Dark List Accent=
 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" Name=3D"Colorful Shading=
 Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" Name=3D"Colorful List Ac=
cent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" Name=3D"Colorful Grid Ac=
cent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" Name=3D"Light Shading Ac=
cent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" Name=3D"Light List Accen=
t 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" Name=3D"Light Grid Accen=
t 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" Name=3D"Medium Shading 1=
 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" Name=3D"Medium Shading 2=
 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" Name=3D"Medium List 1 Ac=
cent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" Name=3D"Medium List 2 Ac=
cent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" Name=3D"Medium Grid 1 Ac=
cent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" Name=3D"Medium Grid 2 Ac=
cent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" Name=3D"Medium Grid 3 Ac=
cent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" Name=3D"Dark List Accent=
 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" Name=3D"Colorful Shading=
 Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" Name=3D"Colorful List Ac=
cent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" Name=3D"Colorful Grid Ac=
cent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" Name=3D"Light Shading Ac=
cent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" Name=3D"Light List Accen=
t 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" Name=3D"Light Grid Accen=
t 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" Name=3D"Medium Shading 1=
 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" Name=3D"Medium Shading 2=
 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" Name=3D"Medium List 1 Ac=
cent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" Name=3D"Medium List 2 Ac=
cent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" Name=3D"Medium Grid 1 Ac=
cent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" Name=3D"Medium Grid 2 Ac=
cent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" Name=3D"Medium Grid 3 Ac=
cent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" Name=3D"Dark List Accent=
 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" Name=3D"Colorful Shading=
 Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" Name=3D"Colorful List Ac=
cent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" Name=3D"Colorful Grid Ac=
cent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" Name=3D"Light Shading Ac=
cent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" Name=3D"Light List Accen=
t 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" Name=3D"Light Grid Accen=
t 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" Name=3D"Medium Shading 1=
 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" Name=3D"Medium Shading 2=
 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" Name=3D"Medium List 1 Ac=
cent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" Name=3D"Medium List 2 Ac=
cent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" Name=3D"Medium Grid 1 Ac=
cent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" Name=3D"Medium Grid 2 Ac=
cent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" Name=3D"Medium Grid 3 Ac=
cent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" Name=3D"Dark List Accent=
 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" Name=3D"Colorful Shading=
 Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" Name=3D"Colorful List Ac=
cent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" Name=3D"Colorful Grid Ac=
cent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"60" Name=3D"Light Shading Ac=
cent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"61" Name=3D"Light List Accen=
t 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"62" Name=3D"Light Grid Accen=
t 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"63" Name=3D"Medium Shading 1=
 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"64" Name=3D"Medium Shading 2=
 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"65" Name=3D"Medium List 1 Ac=
cent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"66" Name=3D"Medium List 2 Ac=
cent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"67" Name=3D"Medium Grid 1 Ac=
cent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"68" Name=3D"Medium Grid 2 Ac=
cent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"69" Name=3D"Medium Grid 3 Ac=
cent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"70" Name=3D"Dark List Accent=
 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"71" Name=3D"Colorful Shading=
 Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"72" Name=3D"Colorful List Ac=
cent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"73" Name=3D"Colorful Grid Ac=
cent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"19" QFormat=3D"true"
   Name=3D"Subtle Emphasis"/>
  <w:LsdException Locked=3D"false" Priority=3D"21" QFormat=3D"true"
   Name=3D"Intense Emphasis"/>
  <w:LsdException Locked=3D"false" Priority=3D"31" QFormat=3D"true"
   Name=3D"Subtle Reference"/>
  <w:LsdException Locked=3D"false" Priority=3D"32" QFormat=3D"true"
   Name=3D"Intense Reference"/>
  <w:LsdException Locked=3D"false" Priority=3D"33" QFormat=3D"true" Name=3D=
"Book Title"/>
  <w:LsdException Locked=3D"false" Priority=3D"37" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" Name=3D"Bibliography"/>
  <w:LsdException Locked=3D"false" Priority=3D"39" SemiHidden=3D"true"
   UnhideWhenUsed=3D"true" QFormat=3D"true" Name=3D"TOC Heading"/>
  <w:LsdException Locked=3D"false" Priority=3D"41" Name=3D"Plain Table 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"42" Name=3D"Plain Table 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"43" Name=3D"Plain Table 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"44" Name=3D"Plain Table 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"45" Name=3D"Plain Table 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"40" Name=3D"Grid Table Light=
"/>
  <w:LsdException Locked=3D"false" Priority=3D"46" Name=3D"Grid Table 1 Lig=
ht"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"Grid Table 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"Grid Table 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"Grid Table 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"Grid Table 5 Dar=
k"/>
  <w:LsdException Locked=3D"false" Priority=3D"51" Name=3D"Grid Table 6 Col=
orful"/>
  <w:LsdException Locked=3D"false" Priority=3D"52" Name=3D"Grid Table 7 Col=
orful"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"Grid Table 1 Light Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"Grid Table 2 Acc=
ent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"Grid Table 3 Acc=
ent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"Grid Table 4 Acc=
ent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"Grid Table 5 Dar=
k Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"Grid Table 6 Colorful Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"Grid Table 7 Colorful Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"Grid Table 1 Light Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"Grid Table 2 Acc=
ent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"Grid Table 3 Acc=
ent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"Grid Table 4 Acc=
ent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"Grid Table 5 Dar=
k Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"Grid Table 6 Colorful Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"Grid Table 7 Colorful Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"Grid Table 1 Light Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"Grid Table 2 Acc=
ent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"Grid Table 3 Acc=
ent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"Grid Table 4 Acc=
ent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"Grid Table 5 Dar=
k Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"Grid Table 6 Colorful Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"Grid Table 7 Colorful Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"Grid Table 1 Light Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"Grid Table 2 Acc=
ent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"Grid Table 3 Acc=
ent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"Grid Table 4 Acc=
ent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"Grid Table 5 Dar=
k Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"Grid Table 6 Colorful Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"Grid Table 7 Colorful Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"Grid Table 1 Light Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"Grid Table 2 Acc=
ent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"Grid Table 3 Acc=
ent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"Grid Table 4 Acc=
ent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"Grid Table 5 Dar=
k Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"Grid Table 6 Colorful Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"Grid Table 7 Colorful Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"Grid Table 1 Light Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"Grid Table 2 Acc=
ent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"Grid Table 3 Acc=
ent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"Grid Table 4 Acc=
ent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"Grid Table 5 Dar=
k Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"Grid Table 6 Colorful Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"Grid Table 7 Colorful Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"46" Name=3D"List Table 1 Lig=
ht"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"List Table 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"List Table 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"List Table 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"List Table 5 Dar=
k"/>
  <w:LsdException Locked=3D"false" Priority=3D"51" Name=3D"List Table 6 Col=
orful"/>
  <w:LsdException Locked=3D"false" Priority=3D"52" Name=3D"List Table 7 Col=
orful"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"List Table 1 Light Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"List Table 2 Acc=
ent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"List Table 3 Acc=
ent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"List Table 4 Acc=
ent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"List Table 5 Dar=
k Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"List Table 6 Colorful Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"List Table 7 Colorful Accent 1"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"List Table 1 Light Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"List Table 2 Acc=
ent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"List Table 3 Acc=
ent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"List Table 4 Acc=
ent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"List Table 5 Dar=
k Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"List Table 6 Colorful Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"List Table 7 Colorful Accent 2"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"List Table 1 Light Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"List Table 2 Acc=
ent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"List Table 3 Acc=
ent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"List Table 4 Acc=
ent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"List Table 5 Dar=
k Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"List Table 6 Colorful Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"List Table 7 Colorful Accent 3"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"List Table 1 Light Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"List Table 2 Acc=
ent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"List Table 3 Acc=
ent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"List Table 4 Acc=
ent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"List Table 5 Dar=
k Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"List Table 6 Colorful Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"List Table 7 Colorful Accent 4"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"List Table 1 Light Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"List Table 2 Acc=
ent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"List Table 3 Acc=
ent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"List Table 4 Acc=
ent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"List Table 5 Dar=
k Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"List Table 6 Colorful Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"List Table 7 Colorful Accent 5"/>
  <w:LsdException Locked=3D"false" Priority=3D"46"
   Name=3D"List Table 1 Light Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"47" Name=3D"List Table 2 Acc=
ent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"48" Name=3D"List Table 3 Acc=
ent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"49" Name=3D"List Table 4 Acc=
ent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"50" Name=3D"List Table 5 Dar=
k Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"51"
   Name=3D"List Table 6 Colorful Accent 6"/>
  <w:LsdException Locked=3D"false" Priority=3D"52"
   Name=3D"List Table 7 Colorful Accent 6"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Mention"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Smart Hyperlink"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Hashtag"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Unresolved Mention"/>
  <w:LsdException Locked=3D"false" SemiHidden=3D"true" UnhideWhenUsed=3D"tr=
ue"
   Name=3D"Smart Link"/>
 </w:LatentStyles>
</xml><![endif]-->
<style>
<!--
 /* Font Definitions */
 @font-face
	{font-family:"Cambria Math";
	panose-1:2 4 5 3 5 4 6 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:roman;
	mso-font-pitch:variable;
	mso-font-signature:-536869121 1107305727 33554432 0 415 0;}
@font-face
	{font-family:Calibri;
	panose-1:2 15 5 2 2 2 4 3 2 4;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:-469750017 -1073732485 9 0 511 0;}
@font-face
	{font-family:Aptos;
	mso-font-charset:0;
	mso-generic-font-family:swiss;
	mso-font-pitch:variable;
	mso-font-signature:536871559 3 0 0 415 0;}
 /* Style Definitions */
 p.MsoNormal, li.MsoNormal, div.MsoNormal
	{mso-style-unhide:no;
	mso-style-qformat:yes;
	mso-style-parent:"";
	margin:0cm;
	mso-pagination:widow-orphan;
	font-size:11.0pt;
	font-family:"Aptos",sans-serif;
	mso-fareast-font-family:Calibri;
	mso-fareast-theme-font:minor-latin;
	mso-bidi-font-family:Aptos;
	mso-ligatures:standardcontextual;}
a:link, span.MsoHyperlink
	{mso-style-noshow:yes;
	mso-style-priority:99;
	color:#467886;
	text-decoration:underline;
	text-underline:single;}
a:visited, span.MsoHyperlinkFollowed
	{mso-style-noshow:yes;
	mso-style-priority:99;
	color:#96607D;
	text-decoration:underline;
	text-underline:single;}
p.msonormal0, li.msonormal0, div.msonormal0
	{mso-style-name:msonormal;
	mso-style-unhide:no;
	mso-margin-top-alt:auto;
	margin-right:0cm;
	mso-margin-bottom-alt:auto;
	margin-left:0cm;
	mso-pagination:widow-orphan;
	font-size:12.0pt;
	font-family:"Aptos",sans-serif;
	mso-fareast-font-family:Calibri;
	mso-fareast-theme-font:minor-latin;
	mso-bidi-font-family:Aptos;}
span.EmailStyle18
	{mso-style-type:personal;
	mso-style-noshow:yes;
	mso-style-unhide:no;
	font-family:"Aptos",sans-serif;
	mso-ascii-font-family:Aptos;
	mso-hansi-font-family:Aptos;
	color:windowtext;}
.MsoChpDefault
	{mso-style-type:export-only;
	mso-default-props:yes;
	font-size:10.0pt;
	mso-ansi-font-size:10.0pt;
	mso-bidi-font-size:10.0pt;
	mso-font-kerning:0pt;
	mso-ligatures:none;}
@page WordSection1
	{size:612.0pt 792.0pt;
	margin:70.85pt 70.85pt 70.85pt 70.85pt;
	mso-header-margin:36.0pt;
	mso-footer-margin:36.0pt;
	mso-paper-source:0;}
div.WordSection1
	{page:WordSection1;}
-->
</style>
<!--[if gte mso 10]>
<style>
 /* Style Definitions */
 table.MsoNormalTable
	{mso-style-name:"Table Normal";
	mso-tstyle-rowband-size:0;
	mso-tstyle-colband-size:0;
	mso-style-noshow:yes;
	mso-style-priority:99;
	mso-style-parent:"";
	mso-padding-alt:0cm 5.4pt 0cm 5.4pt;
	mso-para-margin:0cm;
	mso-pagination:widow-orphan;
	font-size:10.0pt;
	font-family:"Times New Roman",serif;}
</style>
<![endif]-->
</head>

<body lang=3DEN-US link=3D"#467886" vlink=3D"#96607D" style=3D'tab-interval=
:36.0pt;
word-wrap:break-word'>

<div class=3DWordSection1>

<p class=3DMsoNormal style=3D'margin-left:120.0pt;text-indent:-120.0pt;tab-=
stops:
120.0pt;mso-layout-grid-align:none;text-autospace:none'><b><span
style=3D'font-family:"Calibri",sans-serif;color:black'>From:<span
style=3D'mso-tab-count:1'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&=
nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbs=
p;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&=
nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></b><span
style=3D'font-family:"Calibri",sans-serif;color:black'>Bonnin Maxime<o:p></=
o:p></span></p>

<p class=3DMsoNormal style=3D'margin-left:120.0pt;text-indent:-120.0pt;tab-=
stops:
120.0pt;mso-layout-grid-align:none;text-autospace:none'><b><span
style=3D'font-family:"Calibri",sans-serif;color:black'>Sent:<span
style=3D'mso-tab-count:1'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&=
nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbs=
p;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&=
nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></b><sp=
an
style=3D'font-family:"Calibri",sans-serif;color:black'>mardi 27 f&eacute;vr=
ier
2024 13:45<o:p></o:p></span></p>

<p class=3DMsoNormal style=3D'margin-left:120.0pt;text-indent:-120.0pt;tab-=
stops:
120.0pt;mso-layout-grid-align:none;text-autospace:none'><b><span
style=3D'font-family:"Calibri",sans-serif;color:black'>To:<span style=3D'ms=
o-tab-count:
1'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nb=
sp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;=
&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; </span></span></b><s=
pan
style=3D'font-family:"Calibri",sans-serif;color:black'>Bauer Maxime<o:p></o=
:p></span></p>

<p class=3DMsoNormal style=3D'margin-left:120.0pt;text-indent:-120.0pt;tab-=
stops:
120.0pt;mso-layout-grid-align:none;text-autospace:none'><b><span
style=3D'font-family:"Calibri",sans-serif;color:black'>Subject:<span
style=3D'mso-tab-count:1'>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&=
nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbs=
p;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&=
nbsp;&nbsp;&nbsp; </span></span></b><span
style=3D'font-family:"Calibri",sans-serif;color:black'>Product code Pole
industrie<o:p></o:p></span></p>

<p class=3DMsoNormal><o:p>&nbsp;</o:p></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'>Salut M=
axime,<o:p></o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'><o:p>&n=
bsp;</o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'>&Ccedil=
;a va
bien&nbsp;?<o:p></o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'><o:p>&n=
bsp;</o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'>Comme
discut&eacute; il y a quelques jours, nous avons pr&eacute;par&eacute;s dans
SAP les changements de Product Codes mais avant de switcher nous avons beso=
in
que la modification soit faite dans la base de diffusion.<o:p></o:p></span>=
</p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'>Ci-dess=
ous les
changements que nous avons op&eacute;r&eacute;s.<o:p></o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'><o:p>&n=
bsp;</o:p></span></p>

<table class=3DMsoNormalTable border=3D0 cellspacing=3D0 cellpadding=3D0 wi=
dth=3D268
 style=3D'width:201.0pt;margin-left:-.15pt;border-collapse:collapse;mso-yft=
i-tbllook:
 1184;mso-padding-alt:0cm 0cm 0cm 0cm'>
 <tr style=3D'mso-yfti-irow:0;mso-yfti-firstrow:yes;height:43.5pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  background:#366092;padding:0cm 3.5pt 0cm 3.5pt;height:43.5pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><b><span
  style=3D'font-size:12.0pt;font-family:"Arial",sans-serif;color:white;
  mso-ligatures:none;mso-fareast-language:FR'>Ancien PC<o:p></o:p></span></=
b></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border:solid windowtext 1.0=
pt;
  border-left:none;background:#366092;padding:0cm 3.5pt 0cm 3.5pt;height:43=
.5pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><b><span
  style=3D'font-size:12.0pt;font-family:"Arial",sans-serif;color:white;
  mso-ligatures:none;mso-fareast-language:FR'>Nouveau PC<o:p></o:p></span><=
/b></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:1;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC58<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CAM<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:2;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC59<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CMR<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:3;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC60<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CPM<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:4;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC61<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CER<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:5;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC62<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CEV<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:6;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC63<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CUP<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:7;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC65<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>FXP<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:8;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC66<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CSR <o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:9;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC67<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>DTX<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:10;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC68<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>EKO<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:11;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC69<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>WYE<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:12;height:13.35pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:13.35pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC70<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:13.35pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>POWER<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:13;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC71<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>ONX<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:14;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC72<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap valign=3Dbottom style=3D'width:110.0pt;border-top:=
none;
  border-left:none;border-bottom:solid windowtext 1.0pt;border-right:solid =
windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>POWER_SPEC<o:p></o:p></span><=
/p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:15;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC73<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CPP<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:16;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC74<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>UIC__552<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:17;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border-top:none;border-left:=
solid windowtext 1.0pt;
  border-bottom:none;border-right:solid windowtext 1.0pt;padding:0cm 3.5pt =
0cm 3.5pt;
  height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC75<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border:none;border-right:so=
lid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>UIC__541<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:18;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC76<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border:solid windowtext 1.0=
pt;
  border-left:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>AGR<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:19;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC77<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CCA<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:20;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC78<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CLN<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:21;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC79<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>NGC<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:22;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC80<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CPE<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:23;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC81<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>SIGN_AL<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:24;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC82<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>SIG<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:25;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border-top:none;border-left:=
solid windowtext 1.0pt;
  border-bottom:none;border-right:solid windowtext 1.0pt;padding:0cm 3.5pt =
0cm 3.5pt;
  height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC83<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CSF<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:26;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>A475<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CIR<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:27;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC84<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CMB<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:28;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC85<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CMC<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:29;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC86<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>UIC__558<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:30;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC87<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>SRC<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:31;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC88<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CCI<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:32;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC93<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>MU27P<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:33;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC90<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>OUTIL<o:p></o:p></span></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:34;height:15.0pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC91<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.0pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>ACCESSOIRE<o:p></o:p></span><=
/p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:35;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BC92<o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>AUTRE_PROD<o:p></o:p></span><=
/p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:36;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;background:white;padding:0cm 3.5pt 0cm 3.5pt;height:15.75=
pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BA16</span><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;mso-ligatures:no=
ne;
  mso-fareast-language:FR'><o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>UIC__552__SOL.<o:p></o:p></sp=
an></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:37;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;background:white;padding:0cm 3.5pt 0cm 3.5pt;height:15.75=
pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BA17</span><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;mso-ligatures:no=
ne;
  mso-fareast-language:FR'><o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>UIC__558__SOL.<o:p></o:p></sp=
an></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:38;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;background:white;padding:0cm 3.5pt 0cm 3.5pt;height:15.75=
pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BA18</span><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;mso-ligatures:no=
ne;
  mso-fareast-language:FR'><o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>UIC__541__SOL.<o:p></o:p></sp=
an></p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:39;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;background:white;padding:0cm 3.5pt 0cm 3.5pt;height:15.75=
pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>BA55</span><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;mso-ligatures:no=
ne;
  mso-fareast-language:FR'><o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>AUTRE_SOL.<o:p></o:p></span><=
/p>
  </td>
 </tr>
 <tr style=3D'mso-yfti-irow:40;mso-yfti-lastrow:yes;height:15.75pt'>
  <td width=3D121 nowrap style=3D'width:91.0pt;border:solid windowtext 1.0p=
t;
  border-top:none;background:white;padding:0cm 3.5pt 0cm 3.5pt;height:15.75=
pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>X993</span><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;mso-ligatures:no=
ne;
  mso-fareast-language:FR'><o:p></o:p></span></p>
  </td>
  <td width=3D147 nowrap style=3D'width:110.0pt;border-top:none;border-left=
:none;
  border-bottom:solid windowtext 1.0pt;border-right:solid windowtext 1.0pt;
  padding:0cm 3.5pt 0cm 3.5pt;height:15.75pt'>
  <p class=3DMsoNormal align=3Dcenter style=3D'text-align:center'><span
  style=3D'font-size:10.0pt;font-family:"Arial",sans-serif;color:black;
  mso-ligatures:none;mso-fareast-language:FR'>CAM__SOL.<o:p></o:p></span></=
p>
  </td>
 </tr>
</table>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'><o:p>&n=
bsp;</o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'>Peux-tu=
 remplacer
les Product Codes dans la base de diff stp et me dire quand cela est
fait&nbsp;?<o:p></o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'><o:p>&n=
bsp;</o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'>Merci <=
o:p></o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'><o:p>&n=
bsp;</o:p></span></p>

<p class=3DMsoNormal><span lang=3DFR style=3D'mso-ansi-language:FR'>Maxime<=
o:p></o:p></span></p>

</div>

</body>

</html>

------=_NextPart_01DA6AED.5E266960
Content-Location: file:///C:/F16BD585/ProductcodePoleindustrie_files/themedata.thmx
Content-Transfer-Encoding: base64
Content-Type: application/vnd.ms-officetheme
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------=_NextPart_01DA6AED.5E266960
Content-Location: file:///C:/F16BD585/ProductcodePoleindustrie_files/colorschememapping.xml
Content-Transfer-Encoding: quoted-printable
Content-Type: text/xml

<?xml version=3D"1.0" encoding=3D"UTF-8" standalone=3D"yes"?>
<a:clrMap xmlns:a=3D"http://schemas.openxmlformats.org/drawingml/2006/main"=
 bg1=3D"lt1" tx1=3D"dk1" bg2=3D"lt2" tx2=3D"dk2" accent1=3D"accent1" accent=
2=3D"accent2" accent3=3D"accent3" accent4=3D"accent4" accent5=3D"accent5" a=
ccent6=3D"accent6" hlink=3D"hlink" folHlink=3D"folHlink"/>
------=_NextPart_01DA6AED.5E266960
Content-Location: file:///C:/F16BD585/ProductcodePoleindustrie_files/filelist.xml
Content-Transfer-Encoding: quoted-printable
Content-Type: text/xml; charset="utf-8"

<xml xmlns:o=3D"urn:schemas-microsoft-com:office:office">
 <o:MainFile HRef=3D"../ProductcodePoleindustrie.htm"/>
 <o:File HRef=3D"themedata.thmx"/>
 <o:File HRef=3D"colorschememapping.xml"/>
 <o:File HRef=3D"filelist.xml"/>
</xml>
------=_NextPart_01DA6AED.5E266960--
