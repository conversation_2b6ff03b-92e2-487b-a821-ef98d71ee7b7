<?php
    require('login.php');    
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html translate="no">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<meta http-equiv='cache-control' content='no-cache'>
<meta http-equiv='expires' content='0'>
<meta http-equiv='pragma' content='no-cache'>

<link rel="stylesheet" type="text/css" href="REL_BE_1_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">




<link rel="icon" type="image/png" href="\Common_Resources\Release_Icon.png" />




<script>
    function validation_form() {
        const id_to_update = document.getElementById("id_to_update").value;
        const act = document.getElementById("Action").value;
        const ref = document.getElementById("Reference").value;
        const ref_rev = document.getElementById("ref_rev").value;
        const ref_title = document.getElementById("ref_title").value;
        const doctype = document.getElementById("Doc_Type").value;
        const prod_dr = document.getElementById("prod_draw").value;
        const prod_dr_rev = document.getElementById("prod_draw_rev").value;
        const mat_type = document.getElementById("Material_Type").value;
        const invent_impact = document.getElementById("Inventory_Impact").value;
        const ex_val = document.getElementById("Ex").value;
        const weight_val = document.getElementById("Weight").value.trim();
        const weight_unit = document.getElementById("Weight_Unit_ID").value;

        if (id_to_update > 0 || id_to_update != "") {
            var a = confirm('You are about to update the reference ' + ref + ' rev' + ref_rev + '. \nClick OK to confirm.');
            if (a) {

            } else {
                return false;
            }

        }
        if (mat_type != 'LITERATURE') {
            if (act == "" || ref == "" || ref_rev == "" || ref_title == "" || doctype == "" || prod_dr == "" || prod_dr_rev == "" || mat_type == "" || invent_impact == "" || ex_val == "" || weight_val == "" || weight_val == "0" || weight_unit == "") {
                //if (ref.length!=18)
                //{
                alert("All the fields flagged by a star (*) shall be filled");
                return false;
                //}
            }
        } else {
            if (prod_dr == "" || prod_dr_rev == "") {
                alert('To release a "LITERATURE" type, the production drawing field, and its revision must be filled');
                return false;
            }
        }


        // Recuperation du champ de saisie de la reference pour stockage dans x
        const x = document.getElementById("Alias");
        // Remise en forme de la reference :
        //  - Suppression des espaces de debut et fin (trim)
        //  - Mise en majuscule du v de debut si erreur de saisie
        // -------------------------------------
        let y = x.value.trim();
        y = y.toUpperCase(y);

        if (y.length > 40) {
            alert("There are too many characters in the 'Alias' field (max 40). \nPlease correct the entry.");
            return false;
        }

        if (ref_title.length > 40) {
            alert("There are too many characters in the 'Title' field (max 40). \nPlease correct the entry.");
            return false;
        }

    }


    function delete_validation_form() {
        const ref = document.getElementById("Reference").value;
        const ref_rev = document.getElementById("ref_rev").value;
        var a = confirm('Are you sure you want to delete the row for reference ' + ref + ' rev' + ref_rev + '. \nCClick OK to confirm.');
        if (a) {

        } else {
            return false;
        }
    }

    function visual_chk() {
        //const visual_status=document.getElementById("pdf_visual");
        const pdf_area = document.getElementById("pdf_visual");
        const visual = document.getElementById("visu_drawing");
        let loaded_file = document.getElementById("prod_drawing_file").files[0]['name'];

        //alert(loaded_file + '   ' + pdf_area.checked);
        if (pdf_area.checked == true) {
			
            if (loaded_file != "") {
                var tmppath = URL.createObjectURL(event.target.files[0]);
                tmppath = tmppath + '#toolbar=0&navpanes=0&scrollbar=0&view=Fit';
                visual.setAttribute("src", tmppath);
                alert(tmppath);
                //var tmppath = URL.createObjectURL(event.target.files[0]);
                //tmppath=tmppath + '#toolbar=0&navpanes=0&scrollbar=0';
                //pdf_area.setAttribute("src", tmppath);
            }
        } else {

            tmppath = "";
            //alert(tmppath);
            visual.setAttribute("src", tmppath);
        }

    }


    function ref_input_chk() {
        // Recuperation du champ de saisie de la reference pour stockage dans x
        const x = document.getElementById("Reference");
        const doctype = document.getElementById("Doc_Type").value;

        // Remise en forme de la reference :
        //  - Suppression des espaces de debut et fin (trim)
        //  - Mise en majuscule du v de debut si erreur de saisie
        //  - suppression de l'espace entre le code matiere et le code protection issue d'une copier coller du plan
        // -------------------------------------
        let y = x.value.trim();
        y = y.toUpperCase(y);
        if ((y.length == 19) && (y.substr(0, 1) == "V") && ((y.substr(15, 1) == " ") || (y.substr(15, 1) == "-"))) {
            y = y.substr(0, 15) + y.substr(16, 3);
        }
        let z = y.replace(/ /gi, "-");
        x.value = z;
        // -------------------------------------


        // Verification de la longueur de la reference : 18 caracteres obligatoire sauf si documentaire
        //   - Changement de la couleur du champ de saisie en rouge dans le cas contraire
        // -------------------------------------
        if (z.length != 18 && z.length > 0 && doctype != "LITERATURE") {
            x.style.backgroundColor = "#F5B7B1";
            x.focus();
        } else if (z.length == 18) {
            x.style.backgroundColor = "white";
        }
        // -------------------------------------



    }

    function prod_drw_file_process() {
        const id_to_update = document.getElementById("id_to_update");

        let file_name = document.getElementById("prod_drawing_file").files[0]['name'];
        const prod_dr = document.getElementById("prod_draw");
        const prod_dr_rev = document.getElementById("prod_draw_rev");
        const act = document.getElementById("Action");
        const ref_rev = document.getElementById("ref_rev");
        const ref = document.getElementById("Reference");
        const doctype = document.getElementById("Doc_Type");
        const invent_impact = document.getElementById("Inventory_Impact");
        const Mat_Type = document.getElementById("Material_Type");
        const visual = document.getElementById("visu_drawing");
        const ex_val = document.getElementById("Ex");
        const pdf_visualization = document.getElementById("pdf_visual");


        // Affiche le plan choisi dans la fenetre
        //---------------------------------------
        if (pdf_visualization.checked == true) {
            var tmppath = URL.createObjectURL(event.target.files[0]);
			
            tmppath = tmppath + '#toolbar=0&navpanes=0&scrollbar=0';
            visual.setAttribute("src", tmppath);
        }

        //---------------------------------------

        // Prepare le nom du fichier en supprimant les blancs (trim), le passant en manjuscule (touppercase) et supprimant le suffixe .pdf
        //--------------------------------------
        file_name = file_name.trim();
        file_name = file_name.toUpperCase(file_name);
        file_name = file_name.replace(".PDF", "");
        //--------------------------------------

        // Determination de la revision du plan choisi
        //-------------------------------------------
        let prod_d_rev = file_name.substr(file_name.indexOf("REV") + 3, file_name.length - file_name.indexOf("REV") + 3);
        // ------------------------------------------

        // Determination de la racine du plan choisi
        //-------------------------------------------
        file_name = file_name.substr(0, file_name.search("REV"));
        let prod_dr_root = file_name.trim();
        //-------------------------------------------


        if (id_to_update.value == "" && id_to_update.value == 0) {
            //Oblige de rentrer un ZPF lorsqu'un GA est choisi
            if (ref.value.substr(0, 3) != "ZPF" && prod_dr_root.substr(0, 2) == "GA") {
                ref.value = "ZPF000000000";
                doctype.value = "ASSY";
                Mat_Type.value = "FINISHED PRODUCT"
            } else if (prod_dr_root.substr(0, 1) == "V") {
                ref.value = prod_dr_root.substr(0, 4) + '-' + prod_dr_root.substr(5, 4) + "-" + prod_dr_root.substr(10, 2) + "-";
                doctype.value = "";
                Mat_Type.value = "";
            } else if ((prod_dr_root.substr(0, 1) - 1) >= -1) {
                ref.value = prod_dr_root.substr(0, 3) + '-' + prod_dr_root.substr(4, 4) + "-" + prod_dr_root.substr(9, 2) + "-";
                doctype.value = "";
                Mat_Type.value = "";
            } else if (prod_dr_root.substr(0, 2) == "NU" || prod_dr_root.substr(0, 2) == "ID" || prod_dr_root.substr(0, 2) == "PC") {
                ref.value = "";
                doctype.value = "DOC";
                Mat_Type.value = "LITERATURE";
                invent_impact.value = "NO IMPACT";
                ex_val.value = "NO";
            }



            // Dans le cas d'un plan revA, est imposé
            //   - la creation
            //   - le NO impact sur le stock en cours 
            //   - la revision A sur la rev de la ref
            //--------------------------------------------------------
            if (prod_d_rev == "A") {
                act.value = "Creation";
                ref_rev.value = "A";
                invent_impact.value = "NO IMPACT";
            } else {
                act.value = "";
                ref_rev.value = "";
                if (Mat_Type.value != "LITERATURE") {
                    invent_impact.value = "";
                }
            }
            //--------------------------------------------------------
        }


        // Remplis la racine du plan choisi et sa revision dans les champ reference production drawing et revision
        //---------------------------------------------------------------------------------------------------------
        prod_dr.value = prod_dr_root;
        prod_dr_rev.value = prod_d_rev;
        //---------------------------------------------------------------------------------------------------------



        // Lance la vérification de format du champ reference article
        //-----------------------------------------------------------
        ref_input_chk();
        //-----------------------------------------------------------

        // Lance la vérification de diffusion déjà potentiellement en cours
        //-----------------------------------------------------------------
        alive_check(2);
        //-----------------------------------------------------------------
    }

    function alive_check(check_type) {
		
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function() {
            const raw_result = this.responseText.trim();  
            if (raw_result != "") {
				const result = raw_result.split("__");
                alert("Diffusion de " + result[0] + " rev" + result[1] + " déjà en cours. Assurez-vous que cette diffusion n'a pas d'impact sur celle déjà en cours.");
                document.getElementById("warning_draw").innerHTML = '<img opacity="0.5" title="Reference ou plan déjà en cours de diffusion" src="/Common_Resources/warning.png" height="15px">';
                document.getElementById("warning_ref").innerHTML = '<img opacity="0.5" title="Reference ou plan déjà en cours de diffusion" src="/Common_Resources/warning.png" height="15px">';
				
            }
        }

		

        if (check_type == 1) 
		{

			legacy_values();
            if (document.getElementById("Reference").value != "" && document.getElementById("Reference").value != "ZPF000000000XXXXXX") {
                const ref_1 = document.getElementById("Reference").value + "__" + document.getElementById("ref_rev").value;
                xhttp.open("GET", "REL_REF_Alive_Check_AUTO.php?type=Reference__Ref_Rev&to_be_checked=" + ref_1);
                xhttp.send();

            }
			
		

        }

        if (check_type == 2) {
            if (document.getElementById("prod_draw").value != "") {
                const ref_2 = document.getElementById("prod_draw").value + "__" + document.getElementById("prod_draw_rev").value;
                xhttp.open("GET", "REL_REF_Alive_Check_AUTO.php?type=Prod_Draw__Prod_Draw_Rev&to_be_checked=" + ref_2);
                xhttp.send();
            }
        }
    }
	
	function legacy_values() {
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function() {
            const raw_result = this.responseText.trim();

            if (raw_result != "") {

				const result = raw_result.split("__");
				if (document.getElementById("HTS_id").value=="")
				{
					document.getElementById("HTS_id").value=result[0];
				}
				if (document.getElementById("ECCN_id").value=="")
				{
					document.getElementById("ECCN_id").value=result[1];
				}
				if (document.getElementById("RDO_id").value=="")
				{
					document.getElementById("RDO_id").value=result[2];
				}
				if (document.getElementById("Alias").value=="")
				{
					document.getElementById("Alias").value=result[3];
				}
				if (document.getElementById("ref_title").value=="")
				{
					document.getElementById("ref_title").value=result[4];
				}
				if (document.getElementById("Doc_Type").value=="")
				{
					document.getElementById("Doc_Type").value=result[5];
				}
				if (document.getElementById("Material_Type").value=="")
				{
					document.getElementById("Material_Type").value=result[6];
				}
				if (document.getElementById("Ex").value=="")
				{
					document.getElementById("Ex").value=result[7];
				}
				if (document.getElementById("cust_draw").value=="")
				{
					document.getElementById("cust_draw").value=result[8];
				}
				if (document.getElementById("Weight_Unit_ID").value=="" || document.getElementById("Weight_Unit_ID").value=="g" )
				{
					document.getElementById("Weight_Unit_ID").value=result[9];
				}
				if (document.getElementById("Plating_Surface_Unit_ID").value=="")
				{
					document.getElementById("Plating_Surface_Unit_ID").value=result[10];
				}
				if (document.getElementById("fxxx").value=="")
				{
					document.getElementById("fxxx").value=result[11];
				}
				

            }
        }

		if (document.getElementById("Reference").value!="" && document.getElementById("Reference").value!="ZPF000000000XXXXXX" )
		{
			
			const ref = document.getElementById("Reference").value;
			xhttp.open("GET", "REL_REF_Legacy.php?Ref=" + ref);
			xhttp.send();
		}
  
    }


    function title_check() {
        // Recuperation du champ de saisie de la reference pour stockage dans x
        const x = document.getElementById("ref_title");
        // Remise en forme de la reference :
        //  - Suppression des espaces de debut et fin (trim)
        //  - Mise en majuscule du v de debut si erreur de saisie
        // -------------------------------------
        let y = x.value.trim();
        y = y.toUpperCase(y);
        // -------------------------------------


        // Verification de la longueur de la reference : 40 caracteres max
        //   - Changement de la couleur du champ de saisie en rouge dans le cas contraire
        // -------------------------------------
        if (y.length > 40) {
            alert("The title cannont be longer than 40 digits");
            x.style.backgroundColor = "#F5B7B1";
        } else if (y.length <= 40) {
            x.style.backgroundColor = "white";
        }
        // -------------------------------------
    }


    function alias_check() {
        // Recuperation du champ de saisie de l'alias pour stockage dans x
        const x = document.getElementById("Alias");
        // Remise en forme de la reference :
        //  - Suppression des espaces de debut et fin (trim)
        //  - Mise en majuscule du v de debut si erreur de saisie
        // -------------------------------------
        let y = x.value.trim();
        y = y.toUpperCase(y);
        // -------------------------------------


        // Verification de la longueur de l'alias : 40 caracteres max
        //   - Changement de la couleur du champ de saisie en rouge dans le cas contraire
        // -------------------------------------
        if (y.length > 40) {
            x.style.backgroundColor = "#F5B7B1";
            alert("The alias cannont be longer than 40 digits");
            x.focus();
        } else if (y.length <= 40) {
            x.style.backgroundColor = "white";
        }
        // -------------------------------------
    }


    function auto_doc() {
        var doc_type_value = document.getElementById('Doc_Type').value;
        var mat_type_value = document.getElementById('Material_Type').value;
        var fxxx = document.getElementById('fxxx').value;

        // Remplissage automatique de Mat_Type à LITERATURE quand Doc_Type est rempli par DOC
        if (doc_type_value == 'DOC') {
            document.getElementById('Material_Type').value = 'LITERATURE';
        } else if (mat_type_value == 'LITERATURE' && mat_type_value != '' && doc_type_value != "DOC") {
            document.getElementById('Material_Type').value = '';
        }

        // Rendre le remplissage de FXXX obligatoire si le doc_type est MACH ou MOLD
        if (doc_type_value == 'MACH' || doc_type_value == 'MOLD') {
            document.getElementById('fxxx_').innerHTML = '<input required tabindex="19" onchange="auto_plating_surface()" list="Material" name="Material" multiple id="fxxx" title="" style="width:150px;font-size:11;height:13pt;">';
        } else {
            document.getElementById('fxxx_').innerHTML = '<input tabindex="19" onchange="auto_plating_surface()" list="Material" name="Material" multiple id="fxxx" title="" style="width:150px;font-size:11;height:13pt;">';
        }
    }

    function auto_material_type() {
        var doc_type_value = document.getElementById('Doc_Type').value;
        var mat_type_value = document.getElementById('Material_Type').value;

        // Remplissage automatique de Doc_Type à DOC quand Mat_Type est rempli par LITERATURE

        if (mat_type_value == 'LITERATURE') {
            document.getElementById('Doc_Type').value = 'DOC';
        } else if (doc_type_value == "DOC" && doc_type_value != "" && mat_type_value != "LITERATURE") {
            document.getElementById('Doc_Type').value = "";
        }
    }


    function auto_plating_surface() {
        var fxxx = document.getElementById('fxxx').value;
        var plat_sur = document.getElementById('Plating_Surface_ID').value;

        // Rendre le Plating_Surface Obligatoire si le FXXX contient FMTS
        if (fxxx.startsWith('FMTS')) {
            document.getElementById('plat_surface').innerHTML = '<input required tabindex="17" type="text" size=20 id="Plating_Surface_ID" name="Plating_Surface" title="For components, indicate the surface plated with a coating" style="font-size:11;height:13pt;">';
        } else {
            document.getElementById('plat_surface').innerHTML = '<input tabindex="17" type="text" size=20 id="Plating_Surface_ID" name="Plating_Surface" title="For components, indicate the surface plated with a coating" style="font-size:11;height:13pt;">';
        }
    }
	
	function action_auto_update()
	{
		var action = document.getElementById("Action").value;
		var invent_impact = document.getElementById("Inventory_Impact").value;
		if (action="Creation" && invent_impact=="")
		{
			document.getElementById("Inventory_Impact").value="NO IMPACT";
		}
	}
</script>

<head>



    <title>
        <?php echo 'REL / ' . $_GET['ID'] . ' - New Package Creation '; ?>
    </title>

</head>

<body>


    <form enctype="multipart/form-data" action="" method="POST">

        <table id="t01" border=0>

            <tr style="height:68px;background-color:#2A80B9;color:white">
                <td colspan=5 style="">
                    <div id="Title">
                        <?php echo 'New Reference Creation Form - Package ' . $_GET['ID']; ?>
                    </div>
                    <img src="\Common_Resources\scm_logo.png" height="69px" style="position:absolute;top:0px;right:0.10%;z-index:99;margin-top:-1px;margin-right:-1px;vertical-align:top">
                </td>
            </tr>

            <?php
            include('../REL_Connexion_DB.php');
            $requete_pack = 'SELECT DISTINCT * FROM tbl_released_package WHERE Rel_Pack_Num like "' . $_GET['ID'] . '" and Creation_VISA=""';
            $resultat_Pack = $mysqli->query($requete_pack);
            $pack_exist = mysqli_num_rows($resultat_Pack);
            mysqli_close($mysqli);
			

            if ($pack_exist == 0) 
			{
                echo "<tr><td> !! Package not existing or at a different step of the release process !!</td>";
            } else {

            ?>
                <tr>
                    <td rowspan="15" style="width:90px;text-align:center;width:100px;border-bottom:0.5px solid black;border-right:1px solid black;padding-bottom:10px;vertical-align:top;background-color:#EFF1F2">
                        <?php include('REL_Package_Details_V.php'); ?>
                    </td>
                    <td style="min-width:160px">
                        <div id="Body">
                            Prod. Drawing File:
                        </div>
                    </td>
                    <td style="min-width:260px;">
                        <div id="InpBox">
                            <input tabindex="1" id="prod_drawing_file" name="prod_drawing_file[]" type="file" accept=".pdf" onchange="prod_drw_file_process(this)">
                        </div>
                    </td>
						<td rowspan=7 style="padding-top:7px;vertical-align:top;border-bottom:0.5px solid grey;border-left:0.5px solid grey;">
                        <?php
							include('REL_HTS.PHP');
                        ?>
                    </td>



                    <td rowspan="16" style="width:420px;border-left:1px black solid; vertical-align:middle;text-align:center;font-size:8pt;font-weight:bold;">
                        <input type="checkbox" id="pdf_visual" name="pdf_visual" title="Activate the drawing visualisation" onchange="visual_chk()" checked>Drawing Visualization</br>
                        <embed id="visu_drawing" src="" type="application/pdf" frameborder="1" width="675px" height="477px" />
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="Body">
                            Reference Article (SAP) <FONT color="#EE0000">*</FONT>:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<input tabindex="2" type="text" onblur="alive_check(1)" id="Reference" size=22 name="Reference" title="" style="font-size:11;height:13pt;" onchange="ref_input_chk()">
								<div class="dropdown_checkbox_BE-content"style="text-align:left;width:300px">
									- 18 characters max <br> - If ZPF not created yet -> ZPF000000000XXXXXX revA <br> - If <font style="color:yellow;font-weight:bold">datasheet, FT or PC</font> of a product, indicate the associated <font style="color:yellow;font-weight:bold">ZPF</font>.
								</div>
							</div>
								<input tabindex="3" type="text" id="ref_rev" size=3 name="ref_rev" title=""  style="font-size:11;height:13pt;" placeholder="rev">
                            <span style="vertical-align:middle" id="warning_ref"></span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="Body">
                            Prod. Drawing <FONT color="#EE0000">*</FONT>:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<input tabindex="4" type="text" id="prod_draw" size=22 name="prod_draw" onchange="alive_check(2)" style="font-size:11;height:13pt;">
								<input tabindex="5" type="text" id="prod_draw_rev" size=3 name="prod_draw_rev" title=""  style="font-size:11;height:13pt;" placeholder="rev">
								<div class="dropdown_checkbox_BE-content" style="width:250px;text-align:left">
								<font style="font-weight:bold;">Prod Drawing: </font><br>
								Indicate the name of the production drawing (without the revision, or file extension)
								<br><br>
								<font style="font-weight:bold;">Revision:</font>
								<br>- MAJOR = actual change in design or in BoM<br>- MINOR = document update
								</div>
							</div>
                            <span style="vertical-align:middle" id="warning_draw"></span>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="Body">
                            Alias:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<div id="digit_alias">
									<input tabindex="6" type="text" id="Alias" name="Alias_name" size=35 title="" style="font-size:11;height:13pt;" onchange="alias_check()">
								</div>
								<div class="dropdown_checkbox_BE-content" style="text-align:left">
									<font style="font-weight:bold">Catalogue number - Examples: </font><br>
									- 9316-51H 61-21 PN 8 <br> 
									- 276-8203-64 GT <br> 
									- P18-SW400-HARN-0025 <br> 
									- etc... <br><br>
									<font style="font-weight:bold">40 characters max</font>
								</div>
							</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="Body">
                            Title <FONT color="#EE0000">*</FONT>:
                        </div>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<div id="digit_title">
									<input tabindex="7" type="text" id="ref_title" size=35 name="ref_title" title="" style="font-size:11;height:13pt;" onchange="title_check()">
								</div>
								<div class="dropdown_checkbox_BE-content">
									40 characters max - Description in English
								</div>
							</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="Body">
                            Action <FONT color="#EE0000">*</FONT>:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<select tabindex="8" name="Action" id="Action" type="submit" title="" style="width:120px;font-size:11;height:17" onblur="action_auto_update()">
									<option value=""></option>
									<?php
									include('../REL_Connexion_DB.php');
									$requete = "SELECT DISTINCT Action FROM tbl_action ORDER BY Action DESC;";
									$resultat = $mysqli->query($requete);
									while ($row = $resultat->fetch_assoc()) {
										echo '<option value ="' . $row['Action'] . '">' . $row['Action'] . '</option><br/>';
									}
									mysqli_close($mysqli);
									?>
								</select>
								<div class="dropdown_checkbox_BE-content" style="text-align:left;width:360px">
									- For a reference creation (revA) = CREATION <br>
									- For a reference modification (revB and further) = MODIFICATION <br>
								</div>
							</div>
                        </div>
                    </td>
                </tr>

                <tr>
                    <td>
                        <div id="Body">
                            Type<FONT color="#EE0000">*</FONT>:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<select tabindex="9" name="Doc_Type" id="Doc_Type" type="submit" title="" style="width:240px;font-size:11;height:17" onchange="auto_doc()">
									<option value=""></option>
									<!------------------------------>
									<?php
									include('../REL_Connexion_DB.php');
									$requete = "SELECT DISTINCT Doc_Type, Doc_Type_Description FROM tbl_doc_type ORDER BY Doc_Type DESC;";
									$resultat = $mysqli->query($requete);
									while ($row = $resultat->fetch_assoc()) {
										echo '<option value ="' . $row['Doc_Type'] . '">' . $row['Doc_Type'] . ' - ' . $row['Doc_Type_Description'] . '</option><br/>';
									}
									mysqli_close($mysqli);
									?>
									<!------------------------------>
								</select>
								<div class="dropdown_checkbox_BE-content" style="text-align:left;width:380px">
									<font style="font-weight:bold;">Reference Type: </font><br>
									- PUR = Item outsourced <br>
									- MOLD = Molded item (in-house or outsourced) <br>
									- MACH = Machined item (in-house or outsourced) <br>
									- DOC = PC, ID, E-, FT if GA used for final assembly <br>
									- ASSY = Marking operation, Assembly, BoM, GA, FT if no GA for the ZPF
								</div>
							</div>
                        </div>
                    </td>
                </tr>


                <tr>
                    <td style="width:200;">
                        <div id="Body">
                            Material Type <FONT color="#EE0000">*</FONT>
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<select onchange="auto_material_type()" tabindex="10" name="Material_Type" id="Material_Type" type="submit" title="" style="width:180px;font-size:11;height:17">
									<option value=""></option>
									<!--LISTE DEROULANTE DYNAMIQUE-->
									<!------------------------------>
									<?php
									include('../REL_Connexion_DB.php');
									$requete = "SELECT DISTINCT * FROM tbl_material_type ORDER BY Material_Type DESC;";
									$resultat = $mysqli->query($requete);
									while ($row = $resultat->fetch_assoc()) {
										echo '<option value ="' . $row['Material_Type'] . '" Title="' . $row['Description'] . '">' . $row['Material_Type'] . '</option><br/>';
									}
									mysqli_close($mysqli);
									?>
									<!------------------------------>
								</select>
								<div class="dropdown_checkbox_BE-content" style="text-align:left;width:360px">
									<font style="font-weight:bold;">Material Type: </font><br>
									- FINISHED PRODUCT = ZPF, Saleable item <br>
									- SEMI FINISHED PRODUCT = Component, assemblies, BoM etc... <br>
									- LITTERATURE = Document <br>
									- NON VALUATED MATERIAL = Cable, customer action etc... <br>
									- PACKAGING = Packaging item such as cardboard box, etc... <br>
									- RAW MATERIAL = New material specification <br>
								</div>
							</div>
                        </div>
                    </td>
                    <!--<td rowspan=5 style="padding-top:7px;vertical-align:top;width:200px;border-bottom:0.5px solid grey;border-left:0.5px solid grey;">-->
                    <td rowspan=6 style="padding-top:7px;vertical-align:top;vertical-align:top;border-bottom:0.5px solid grey;border-left:0.5px solid grey;">
                        <div id="hts_eccn">
                            <?php
                            include('REL_ECCN_RDO.PHP');
                            ?>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="Body">
                            Inventory Impact <FONT color="#EE0000">*</FONT>:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<select tabindex="11" name="Inventory_Impact" id="Inventory_Impact" type="submit" title="" style="width:130px;font-size:11;height:17">
									<option value=""></option>
									<!------------------------------>
									<?php
									include('../REL_Connexion_DB.php');
									$requete = "SELECT DISTINCT Inventory_Impact, Description FROM tbl_inventory_impact ORDER BY Inventory_Impact DESC;";
									$resultat = $mysqli->query($requete);
									while ($row = $resultat->fetch_assoc()) {
										echo '<option value="' . $row['Inventory_Impact'] . '" Title="' . $row['Description'] . '">' . $row['Inventory_Impact'] . '</option><br/>';
									}
									mysqli_close($mysqli);
									?>
									<!------------------------------>
								</select>
								<div class="dropdown_checkbox_BE-content" style="text-align:left;width:490px">
									<font style="font-weight:bold;">Does the modification imply the update of the parts in stock or being manufactured? </font><br>
									- NO IMPACT: <br> No extra action needed on the parts in stock or being-manufactured parts <br>
									- TO BE UPDATED:<br> Parts in stock will have to be modified and ongoing orders or internal manufacturing orders must be stopped and continued based on the new drawing revision <br>
									- TO BE SCRAPPED: <br>Parts in stock will be scrapped and ongoing orders or internal manufacturing orders stopped. <br>
								</div>
							</div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="Body">
                            Ex <FONT color="#EE0000">*</FONT>:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
                            <select tabindex="12" name="Ex" id="Ex" type="submit" title="is the drawing/ref ex?" style="width:74px;font-size:11;height:17">
                                <option value=""></option>
                                <!------------------------------>
                                <?php
                                include('../SCM_Connexion_DB.php');
                                $requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex DESC;";
                                $resultat = $mysqli_scm->query($requete);
                                while ($row = $resultat->fetch_assoc()) {
                                    echo '<option value ="' . $row['Ex'] . '">' . $row['Ex'] . '</option><br/>';
                                }
                                mysqli_close($mysqli_scm);
                                ?>
                                <!------------------------------>
                            </select>
                        </div>
                    </td>
                </tr>



                <tr>
                    <td>
                        <div id="Body">
                            Cust. Drawing:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
                            <input tabindex="13" type="text" id="cust_draw" size=22 name="cust_draw" style="font-size:11;height:13pt;">
                            <input tabindex="14" type="text" id="cust_draw_rev" size=3 name="cust_draw_rev" style="font-size:11;height:13pt;" placeholder="rev">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <div id="Body">
                            Weight in air <FONT color="#EE0000">*</FONT>:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
                            <input tabindex="15" type="text" id="Weight" size=22 name="Weight" style="font-size:11;height:13pt;">
                            <select tabindex="16" id="Weight_Unit_ID" name="Weight_Unit_name" type="submit" title="" style="width:50px;font-size:11;height:17;vertical-align:middle;">
                                <option value=""></option>
                                <!--LISTE DEROULANTE DYNAMIQUE-->
                                <!------------------------------>
                                <?php
                                include('../SCM_Connexion_DB.php');
                                $requete = "SELECT DISTINCT Unit FROM tbl_unit WHERE Unit_Type like 'Weight' ORDER BY Unit DESC;";
                                $resultat = $mysqli_scm->query($requete);
                                while ($row = $resultat->fetch_assoc()) {
                                    if ($row['Unit'] == "g") {
                                        $sel = "SELECTED";
                                    } else {
                                        $sel = "";
                                    }
                                    echo '<option ' . $sel . ' value ="' . $row['Unit'] . '">' . $row['Unit'] . '</option><br/>';
                                }
                                mysqli_close($mysqli_scm);
                                ?>
                                <!------------------------------>
                            </select>
                        </div>
                    </td>
                </tr>
				
				 <tr>
                    <td>
                        <div id="Body">
                            Material / Plating:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<div id="fxxx_">
									<input tabindex="17" onchange="auto_plating_surface()" list="Material" name="Material" multiple id="fxxx" title="" style="width:150px;font-size:11;height:13pt;">
								</div>
								<datalist id="Material">
									<!--LISTE DEROULANTE DYNAMIQUE-->
									<!------------------------------>
									<?php
									include('../SCM_Connexion_DB.php');
									$requete = "SELECT DISTINCT fxxx_ref, fxxx_description FROM tbl_fxxx WHERE Status like 'ACTIVE' ORDER BY fxxx_ref DESC;";
									$resultat = $mysqli_scm->query($requete);
									while ($row = $resultat->fetch_assoc()) {
										echo '<option value ="' . $row['fxxx_ref'] . '">' . $row['fxxx_description'] . '</option><br/>';
									}
									mysqli_close($mysqli_scm);
									?>
									<!------------------------------>
								</datalist>
								<div class="dropdown_checkbox_BE-content" style="text-align:left;width:600px">
									<div style="margin-bottom:2px">- For components, indicate the associated specification of the material used to manufacture the part. </div>
									<div style="margin-bottom:2px">- For reference specific to a plating operation (-086, -852, etc...), make sure to indicate the plating material only</div>
									<div style="margin-bottom:2px">- If multiple materials are included in the reference (<b>EXTERPRO</b>, plated machined part in a single ref, 2 materials for a LSR part etc...), indicate all the materials separated by a comma <,>. Ex: FMME 4005, FMTS 12015</div>
								</div>
							</div>
                        </div>
                    </td>
                </tr>
				
				
                <tr>
                    <td>
                        <div id="Body">
                            Plated Surface:
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<span id="plat_surface">
									<input tabindex="18" type="text" size=20 id="Plating_Surface_ID" name="Plating_Surface" title="" style="font-size:11;height:13pt;">
								</span>
								<div class="dropdown_checkbox_BE-content">
									For components, indicate the surface plated with a coating, as well as the associated unit.
								</div>
							</div>
                            <select tabindex="19" id="Plating_Surface_Unit_ID" name="Plating_Surface_Unit_name" type="submit" title="" style="width:50px;font-size:11;height:17px">
                                <option value=""></option>
                                <!--LISTE DEROULANTE DYNAMIQUE-->
                                <!------------------------------>
                                <?php
                                include('../SCM_Connexion_DB.php');
                                $requete = "SELECT DISTINCT Unit FROM tbl_unit WHERE Unit_Type like 'Surface' ORDER BY Unit DESC;";
                                $resultat = $mysqli_scm->query($requete);
                                while ($row = $resultat->fetch_assoc()) {
                                    echo '<option value ="' . $row['Unit'] . '">' . $row['Unit'] . '</option><br/>';
                                }
                                mysqli_close($mysqli_scm);
                                ?>
                                <!------------------------------>
                            </select>
                        </div>
                    </td>
                </tr>


               

                <tr>
                    <td>
                        <div id="Body">
                            Recommanded Inhouse Manuf.
                        </div>
                    </td>
                    <td>
                        <div id="InpBox">
							<div class="dropdown_checkbox_BE">
								<input tabindex="20" type="checkbox" id="inhouse_manuf" name="inhouse_manuf" value=1 title="">
								<div class="dropdown_checkbox_BE-content">
									Pick that option if the part is critical and internal machining or molding expertise preferred. Could be applicable to glass-to-metal seal bodies, PEEK insulator etc...
								</div>
							</div>
						</div>
                    </td>
                </tr>
                <tr>
                    <td style="text-align:left;">
                        <?php
                        echo '<a target="" href="REL_BE_1_Sign_Off_Form.php?ID=' . $_GET['ID'] . '" class="btn green"  target="Main_target" style="text-align:center" title="Download the latest procedure related to the modification management">Start Release</a>';
                        ?>
                    </td>
                    <td>
                        <div id="Body">
                            Comment(s) :
                        </div>
                    </td>
                    <td style="vertical-align:top; padding-bottom:5px">
                        <div id="InpBox">
                            <textarea tabindex="21" rows="3" cols="34" style="font-size:11;font-family:arial" id="requestor_comments" name="requestor_comments" title="Provide the details of the change you want to be applied - The more info, the better it is" /></textarea><br />
                        </div>
                    </td>

                    <td style="text-align:center; vertical-align:middle;">
                        <input HIDDEN type="text" id="id_to_update" size=2 name="id_to_update" style="font-size:11;height:13pt;">
                        <input type="submit" id="delete_row_button" name="delete_row_button" class="btn red" style="display:none;font-size:11; vertical-align:middle;width:135px;height:20px;" value="DELETE REF/DRAWING" title="Delete the ref/drawing from the current release package" onclick="return delete_validation_form()" />
                        <input type="submit" id="validate_new_row" name="validate_new_row" class="btn blue2" style="font-size:11; vertical-align:middle;width:135px;height:20px;" value="ADD REF/DRAWING" title="Add the ref/drawing to the current release package" onclick="return validation_form()" />

                    </td>

                </tr>

                <tr style="border-top:1px black solid;text-align:middle">
                    <td colspan=2>
                        <div id="Frame_Title">
                            Release Package Content / <i>Contenu de la Diffusion</i>
                        </div>
                    </td>
                    <td>
                        <?php
                        // TO BE CONTINUED
                        if (isset($msg)) {
                            echo $msg;
                        }

                        ?>
                    </td>
                </tr>
                <tr>
                    <td colspan=5>
                        <?php
                        echo '<iframe alt="ok" src="./REL_Package_Content.php?Rel_Pack_Num=' . $_GET['ID'] . '" name="Main_target" id="Main_target" frameborder="0" height="500px" width="99.5%" style="margin-left:2px;text-align:middle" ></iframe>';
                        ?>
                    </td>
                </tr>

        </table>

    </form>

<?php
	if (isset($_POST['validate_new_row']) && (($_POST['Action']) != "") && (($_POST['Inventory_Impact']) != "") && (($_POST['Material_Type']) != "") && (($_POST['Ex']) != "") && (($_POST['Doc_Type']) != "")) //
	{

		#FOR DEBUGGING ONLY
		#print_r($_POST);

		$pack = $_GET['ID'];
		$reference = $_POST['Reference'];
		$ref_rev = $_POST['ref_rev'];
		$prod_draw = $_POST['prod_draw'];
		$prod_draw_rev = $_POST['prod_draw_rev'];
		$ref_title = htmlspecialchars($_POST['ref_title'], ENT_QUOTES);
		$alias = $_POST['Alias_name'];
		if ($alias == "") {
			$alias = "";
		}
		$action = $_POST['Action'];
		$doc_type = $_POST['Doc_Type'];
		$material_type = $_POST['Material_Type'];
		$inventory_impact = $_POST['Inventory_Impact'];
		$ex = $_POST['Ex'];
		$proc_type = "";
		$visa_project = "";
		$date_project = "0000-00-00";
		$visa_inventory = "";
		$date_inventory = "0000-00-00";
		$visa_product = "";
		$date_product = "0000-00-00";
		$visa_quality = "";
		$visa_owner_quality = "";
		$date_quality = "0000-00-00";
		$visa_methode = "";
		$date_methode = "0000-00-00";
		$visa_finance = "";
		$date_finance = "0000-00-00";
		$visa_prod = "";
		$date_prod = "0000-00-00";
		$visa_supply = "";
		$date_supply = "0000-00-00";
		$visa_pur_1 = "";
		$date_pur_1 = "0000-00-00";
		$visa_pur_2 = "";
		$date_pur_2 = "0000-00-00";
		$visa_pur_3 = "";
		$date_pur_3 = "0000-00-00";
		$visa_pur_4 = "";
		$date_pur_4 = "0000-00-00";
		$visa_pur_5 = "";
		$date_pur_5 = "0000-00-00";
		$visa_pur_5 = "";
		$date_pur_5 = "0000-00-00";
		$visa_gid = "";
		$date_gid = "0000-00-00";
		$visa_gid_2 = "";
		$date_gid_2 = "0000-00-00";
		$visa_MOF = "";
		$date_MOF = "0000-00-00";
		$visa_Q_PROD = "";
		$date_Q_PROD = "0000-00-00";
		$visa_routing_entry = "";
		$date_routing_entry = "0000-00-00";
		$visa_metro = "";
		$date_metro = "0000-00-00";
		$visa_labo = "";
		$date_labo = "0000-00-00";
		$q_inspection = "Z08";
		$q_dynam = "";
		$q_doc_req = "";
		$q_control_routing = "";
		$weight = intval($_POST['Weight']);
		$weight_unit = $_POST['Weight_Unit_name'];
		$cls = "0";
		$moq = "0";
		$product_code = "";
		$prod_agent = "";
		$mof = "";
		$commodity_code = "";
		$pur_group = "";
		$mat_prod_type = "";
		$leadtime = "0";
		$pris_dans_1 = "";
		$pris_dans_2 = "";
		$supervisor = "";
		$fia = "";
		$metro_time = "0";
		$metro_control = "";
		$eccn = $_POST['eccn'];
		$hts = $_POST['hts'];
		$rdo = $_POST['RDO'];

		$mat_prod_type = "";
		$unit = "";

		if ($weight == 0) {
			$weight_unit = "";
		}

		if (isset($_POST['Plating_Surface'])) {
			$plating_surface = intval($_POST['Plating_Surface']);
			if (isset($_POST['Plating_Surface_Unit_name'])) {
				$plating_surface_unit = $_POST['Plating_Surface_Unit_name'];
			} else {
				$plating_surface_unit = "";
			}
		} else {
			$plating_surface = "";
			$plating_surface_unit = "-";
		}

		$fxxx = $_POST['Material'];
		if ($fxxx == "") {
			$fxxx = "";
		}

		$requestor_comments = htmlspecialchars($_POST['requestor_comments'], ENT_QUOTES);

		if (isset($_POST['inhouse_manuf'])) {
			$inhouse_manuf = 1;
		} else {
			$inhouse_manuf = 0;
		}

		$cust_drawing = $_POST['cust_draw'];
		if ($cust_drawing == "") {
			$cust_drawing = "";
		}

		$cust_drawing_rev = $_POST['cust_draw_rev'];
		if ($cust_drawing_rev == "") {
			$cust_drawing_rev = "";
		}

		//ISOLEMENT DE LA REF DU FICHIER/PLAN CHOISI
		if (isset($_FILES['prod_drawing_file'])) {
			//$drawing_file_name=$_FILES['prod_drawing_file']['tmp_name'][0];
			$drawing_file_name = basename($_FILES['prod_drawing_file']['name'][0]);
		} else {
			$drawing_file_name = "";
		}


		// Incremetation de l'ID pour la diff en cours
		//;

		
		// Incremetation de l'ID pour la diff en cours
		include('../REL_Connexion_DB.php');
		$draw_numbering = 'SELECT MAX(ID) from tbl_released_drawing';
		$draw_max_ID_tmp = $mysqli->query($draw_numbering);
		$draw_max_ID = mysqli_fetch_row($draw_max_ID_tmp);
		$draw_max = intval($draw_max_ID[0]) + 1;

		//include('../REL_Connexion_DB.php');
		// ####################################################################################
		// IF id_to_update=="" ou id_to_update==0 --> CREATION DANS TABLE TBL_RELREASE_DRAWING
		// ####################################################################################
		if (($_POST['id_to_update'] == 0) ||  ($_POST['id_to_update'] == "")) {
			//On prépare la commande sql d'insertion
			//Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
			$sql_1 = 'INSERT INTO tbl_released_drawing VALUES (
			"' . $draw_max . '",
			"' . $pack . '",
			"' . $reference . '",
			"' . $ref_rev . '",
			"' . $prod_draw . '",
			"' . $prod_draw_rev . '",
			"' . $drawing_file_name . '",
			"' . $ref_title . '",
			"' . $alias . '",
			"' . $cust_drawing . '",
			"' . $cust_drawing_rev . '",
			"' . $action . '",
			"' . $doc_type . '",
			"' . $proc_type . '",
			"' . $material_type . '",
			"' . $inventory_impact . '",
			"' . $ex . '",
			"' . $weight . '",
			"' . $weight_unit . '",
			"' . $plating_surface . '",
			"' . $plating_surface_unit . '",
			"' . $fxxx . '",
			"' . $inhouse_manuf . '",
			"' . $requestor_comments . '",
			"' . $visa_project . '",
			"' . $date_project . '",
			"' . $visa_inventory . '",
			"' . $date_inventory . '",
			"' . $visa_product . '",
			"' . $date_product . '",
			"' . $visa_owner_quality . '",
			"' . $visa_quality . '",
			"' . $date_quality . '",
			"' . $visa_methode . '",
			"' . $date_methode . '",
			"' . $visa_finance . '",
			"' . $date_finance . '",
			"' . $cls . '",
			"' . $moq . '",
			"' . $product_code . '",
			"' . $visa_prod . '",
			"' . $date_prod . '",
			"' . $pur_group . '",
			"' . $prod_agent . '",
			"' . $mof . '",
			"' . $commodity_code . '",
			"' . $mat_prod_type . '",
			"' . $unit . '",
			"' . $leadtime . '",
			"' . $pris_dans_1 . '",
			"' . $pris_dans_2 . '",
			"' . $visa_supply . '",
			"' . $date_supply . '",
			"' . $eccn . '",
			"' . $rdo . '",
			"' . $hts . '",
			"' . $supervisor . '",
			"' . $visa_pur_1 . '",
			"' . $date_pur_1 . '",
			"' . $visa_pur_2 . '",
			"' . $date_pur_2 . '",
			"' . $visa_pur_3 . '",
			"' . $date_pur_3 . '",
			"' . $fia . '",
			"' . $visa_pur_4 . '",
			"' . $date_pur_4 . '",
			"' . $visa_pur_5 . '",
			"' . $date_pur_5 . '",
			"' . $visa_gid . '",
			"' . $date_gid . '",
			"' . $visa_gid_2 . '",
			"' . $date_gid_2 . '",
			"' . $visa_MOF . '",
			"' . $date_MOF . '",
			"' . $visa_routing_entry . '",
			"' . $date_routing_entry . '",
			"' . $visa_labo . '",
			"' . $date_labo . '",
			"' . $metro_time . '",
			"' . $metro_control . '",
			"' . $visa_metro . '",
			"' . $date_metro . '",
			"' . $visa_Q_PROD . '",
			"' . $date_Q_PROD . '",
			"' . $q_inspection . '",
			"' . $q_dynam . '",
			"' . $q_doc_req . '",
			"' . $q_control_routing . '",
			"",
			"0"
			
			);';


			$resultat = $mysqli->query($sql_1);

			// ################################
			//  CHARGEMENT FICHIER SUR SERVEUR 
			// ################################
			if ($drawing_file_name != "")   // Confirmer que l'utilisateur a désigné un fichier
			{
				$path_attachment = $_SERVER['DOCUMENT_ROOT'] . "\REL\DRAWINGS\IN_PROCESS";
				if ($_FILES['prod_drawing_file']['size'][0] <= ********) // sS'ssurer que la taille du fichier ne dépasse pas 60Mo
				{
					$tmp_file = $_FILES['prod_drawing_file']['tmp_name'][0];
					move_uploaded_file($_FILES['prod_drawing_file']['tmp_name'][0], $path_attachment . "\\" . basename($_FILES['prod_drawing_file']['name'][0]));
				} else {
					echo 'Le fichier ' . $_FILES['prod_drawing_file']['name'][0] . 'est trop gros';
				}
			}
			// ################################




		} else {

			// ###############################################################################################
			//  SI id_to_update!="" ou id_to_update>0 --> L'UTILISATEUR VEUT MODIFIE UNE LIGNE DEJA EXISTANTE
			//     - MISE A JOUR DANS TABLE TBL_RELEASE_DRAWING
			//     - MISE A JOUR DU FICHIER A REMPLACER (SI DIFFERENT DE CELUI DEJA EN PLACE)
			// ###############################################################################################

			$id_to_update = $_POST['id_to_update'];
			$sql_2 = 'UPDATE tbl_released_drawing 
		  SET 
			Reference="' . $reference . '",
			Ref_Rev="' . $ref_rev . '",
			Prod_Draw="' . $prod_draw . '",
			Prod_Draw_Rev="' . $prod_draw_rev . '",
			Ref_Title="' . $ref_title . '",
			Alias="' . $alias . '",
			Cust_Drawing="' . $cust_drawing . '",
			Cust_Drawing_Rev="' . $cust_drawing_rev . '",
			Action="' . $action . '",
			Doc_Type="' . $doc_type . '",
			Proc_Type="' . $proc_type . '",
			Material_Type="' . $material_type . '",
			Inventory_Impact="' . $inventory_impact . '",
			Ex="' . $ex . '",
			Weight="' . $weight . '",
			Weight_Unit="' . $weight_unit . '",
			Plating_Surface="' . $plating_surface . '",
			Plating_Surface_Unit="' . $plating_surface_unit . '",
			FXXX="' . $fxxx . '",
			Internal_Mach_Rec="' . $inhouse_manuf . '",
			Requestor_Comments="' . $requestor_comments . '",
			ECCN="' . $eccn . '",
			RDO="' . $rdo . '",
			HTS="' . $hts . '"

		 WHERE ID like "' . $id_to_update . '";';




			$resultat = $mysqli->query($sql_2);

			// MISE A JOUR DU FICHIER DEJA ASSOCIE DANS LA BASE DE DONNEE ET SUR LE SERVEUR

			if ($drawing_file_name != "") // Confirmer que l'utilisateur a désigné un fichier
			{
				$sql_file = 'SELECT Drawing_Path FROM tbl_released_drawing WHERE ID like "' . $id_to_update . '"';
				$result = $mysqli->query($sql_file);
				while ($row = $result->fetch_assoc()) {
					$file_path_db = $row['Drawing_Path'];
				}
				$file_path_input_user = basename($_FILES['prod_drawing_file']['name'][0]);

				//if ($file_path_db!=$file_path_input_user)
				//{
				$path_attachment =  $_SERVER['DOCUMENT_ROOT'] . "\REL\DRAWINGS\IN_PROCESS\\";

				if ($file_path_db != "")                                                // SUPPRESION DU FICHIER EXISTANT S'IL EXISTE
				{
					$file_to_delete = $path_attachment . $file_path_db;            // Chemin complet du fichier existant
					unlink($file_to_delete);                                        // Suppression
				}

				$tmp_file = $_FILES['prod_drawing_file']['tmp_name'][0];
				move_uploaded_file($tmp_file, $path_attachment . basename($_FILES['prod_drawing_file']['name'][0])); // COPIE DU NOUVEAU FICHIER

				$sql_file_update = 'UPDATE tbl_released_drawing						
						  SET Drawing_Path = "' . $file_path_input_user . '"
						  WHERE ID like "' . $id_to_update . '"';               // MISE A JOUR DE LA BASE DE DONNEE AVE CLE NOUVEAU NOM DE FICHIER

				$result_file_update = $mysqli->query($sql_file_update);

				//}
			}
		}

		// on ferme la connexion
		mysqli_close($mysqli);
	}
	
	if (isset($_POST['delete_row_button']) && (($_POST['id_to_update'] != 0) ||  ($_POST['id_to_update'] != ""))) 
		{

			// MISE EN FORME DES DONNEES
			$id_to_update = $_POST['id_to_update'];

			//CONNEXION A LA BASE DE DONNEES
			include('../REL_Connexion_DB.php');

			// SUPPRESSION DU FICHIER, SI EXISTANT, ASSOCIE A L'ARTICLE 
			$sql_file = 'SELECT Drawing_Path, Reference, Ref_Rev FROM tbl_released_drawing WHERE ID like "' . $id_to_update . '"';
			$result = $mysqli->query($sql_file);
			while ($row = $result->fetch_assoc()) {
				$file_path_db = $row['Drawing_Path'];
				$ref = $row['Reference'];
				$ref_rev = $row['Ref_Rev'];
			}

			if ($file_path_db != "")                                                // SUPPRESION DU FICHIER EXISTANT S'IL EXISTE
			{
				$path_attachment =  $_SERVER['DOCUMENT_ROOT'] . "\REL\DRAWINGS\IN_PROCESS\\";
				$file_to_delete = $path_attachment . $file_path_db;            // Chemin complet du fichier existant
				unlink($file_to_delete);                                        // Suppression
			}
			// ----------------

			// SUPPRESSION DE LA LIGNE
			$sql = 'DELETE FROM tbl_released_drawing WHERE ID ="' . $id_to_update . '";';

			// Query execution
			$result = $mysqli->query($sql);
			mysqli_close($mysqli);
			// -----------
			$msg = 'Suppression de la la référence ' . $ref . ' rev' . $ref_rev . '" et de son plan associée effectuée avec succès !';
		}

	}

            
?>

</body>

</html>