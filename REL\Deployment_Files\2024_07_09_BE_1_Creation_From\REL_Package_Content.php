<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html>
<head>
<!--<meta http-equiv="X-UA-Compatible" content="IE=edge" />-->

<meta http-equiv='cache-control' content='no-cache'>
<meta http-equiv="cache-control" content="max-age=0" />
<meta http-equiv='expires' content='0'>
<meta http-equiv='pragma' content='no-cache'>


<link rel="stylesheet" type="text/css" href="REL_Package_Content_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


<script>
    function ref_modif()
    {
        const action_cont=document.getElementById("Action_Content");
        //const action_main=document.getElementById("Action");

        //TO BE DEBUGGED
        //--------------
        //action_main.value=action_cont.value;
        //alert(action_cont.value);
        //---------------
    }
	
	
	
	function data_to_form(obj,table)
	{
		//TRANSFERT DES INFO DE LA TABLE VERS LE FORMULAIRE


			// DETERMINATION DE LA PAGE PARENT DANS LAQUELLE CETTE PAGE EST APPELLEE
			let init_path=window.parent.location.href;
			let searched_REL_BE_1="REL_BE_1_Form";
			let searched_REL_BE_2="REL_BE_2_Form";
			let searched_REL_BE_3="REL_BE_3_Form";
			let root_REL_BE_1=init_path.includes(searched_REL_BE_1,0);
			let root_REL_BE_2=init_path.includes(searched_REL_BE_2,0);
			let root_REL_BE_3=init_path.includes(searched_REL_BE_3,0);
			// -----
			
			window.parent.document.getElementById("id_to_update").value=obj.cells[1].textContent.trim();
			
			window.parent.document.getElementById("Action").value=obj.cells[2].textContent.trim();
			window.parent.document.getElementById("Reference").value=obj.cells[3].textContent.trim();
			window.parent.document.getElementById("ref_rev").value=obj.cells[4].textContent.trim();
			window.parent.document.getElementById("prod_draw").value=obj.cells[5].textContent.trim();
			window.parent.document.getElementById("prod_draw_rev").value=obj.cells[6].textContent.trim();
			window.parent.document.getElementById("ref_title").value=obj.cells[7].textContent.trim();
			window.parent.document.getElementById("Alias").value=obj.cells[8].textContent.trim();
			window.parent.document.getElementById("cust_draw").value=obj.cells[9].textContent.trim();
			window.parent.document.getElementById("cust_draw_rev").value=obj.cells[10].textContent.trim();
			window.parent.document.getElementById("Doc_Type").value=obj.cells[11].textContent.trim();
			if (obj.cells[12].textContent.trim()=="X")
			{
				window.parent.document.getElementById("inhouse_manuf").checked = true;
			} else {
				window.parent.document.getElementById("inhouse_manuf").checked = false;
			}
				
			window.parent.document.getElementById("inhouse_manuf").value=obj.cells[12].textContent.trim();
			
			window.parent.document.getElementById("Material_Type").value=obj.cells[13].textContent.trim();
			window.parent.document.getElementById("Inventory_Impact").value=obj.cells[14].textContent.trim();
			window.parent.document.getElementById("Ex").value=obj.cells[15].textContent.trim();
			window.parent.document.getElementById("Weight").value=obj.cells[16].textContent.trim();
			window.parent.document.getElementById("Weight_Unit_ID").value=obj.cells[17].textContent.trim();
			window.parent.document.getElementById("Plating_Surface_ID").value=obj.cells[18].textContent.trim();
			window.parent.document.getElementById("Plating_Surface_Unit_ID").value=obj.cells[19].textContent.trim();
			window.parent.document.getElementById("fxxx").value=obj.cells[20].textContent.trim();
			window.parent.document.getElementById("ECCN_id").value=obj.cells[21].textContent.trim();
			window.parent.document.getElementById("RDO_id").value=obj.cells[22].textContent.trim();
			window.parent.document.getElementById("HTS_id").value=obj.cells[23].textContent.trim();
			
			
			
			//document.getElementById("t01").backgroundColor="white";
			//obj.style.backgroundColor = "red";
			
			// SELECTION DU BULLET RADIO ASSOCIE A LA LIGNE CLIQUEE --> CACHEE !
			const indx="Radio_Picked_" + obj.cells[1].textContent.trim();
			document.querySelector('input[name="Picked_User"]:checked');
			document.getElementById(indx).checked = true;
			
	
			
			
			// SI APPEL DE LA PAGE REL_BE_FORM_1
			//   - CHARGEMENT DE L'APERCU DU PLAN 
			//   - AFFICHAGE DU BOUTON DE SUPPRESSION
			//
			// SI APPEL DE LA PAGE REL_BE_FORM_2
			//   - AFFICHAGE DU BOUTON DE MOFICATION
			
				// APERCU PLAN PDF
				if (root_REL_BE_1==true)
				{
					const pdf_area=window.parent.document.getElementById("pdf_visual");
					const visual=window.parent.document.getElementById("visu_drawing");
					let loaded_file=obj.cells[25].textContent.trim();

					if (pdf_area.checked==true && (loaded_file!="" || loaded_file!=0))
					{
						loaded_file='DRAWINGS/IN_PROCESS/' + loaded_file;
						tmppath=loaded_file + '#toolbar=0&navpanes=0&scrollbar=0&view=Fit';
						visual.setAttribute("src", tmppath);	
					} else {
						tmppath="";
						//alert(tmppath);
						visual.setAttribute("src", tmppath);
					}
					
				// AFFICHAGE DU COMMENTAIRE
					if (obj.cells[24].textContent.trim().indexOf("- Requestor Comments -")>=0)
					{
						const req_comm_tmp=obj.cells[24].textContent.trim().split("- Requestor Comments -");
						window.parent.document.getElementById("requestor_comments").value=req_comm_tmp[1];
				//		if (req_comm_tmp[1].indexOf("|")>0)
				//		{
				//			const comm_tmp=req_comm_tmp[1].split(" | ");
				//			window.parent.document.getElementById("requestor_comments").value=comm_tmp[1];
				//		} else {
				//			window.parent.document.getElementById("requestor_comments").value=req_comm_tmp[1];
				//		}
				//	} else if (obj.cells[24].textContent.trim().indexOf("|")>0)
				//		{
				////			const comm_tmp=obj.cells[24].textContent.trim().split(" | ");
				//			window.parent.document.getElementById("requestor_comments").value=comm_tmp[1];
						} else {
								window.parent.document.getElementById("requestor_comments").value=obj.cells[24].textContent.trim();
						}
						

					// BOUTON DE SUPPRESSION//AFFICHAGE BOUTON SUPPRESSION
					window.parent.document.getElementById("delete_row_button").style.display="inline";
				
				} else if (root_REL_BE_2==true || root_REL_BE_3==true)
				{
					// AFFICHAGE DU COMMENTAIRE
					if (obj.cells[24].textContent.trim().indexOf("- Requestor Comments -")>=0)
					{
						const req_comm_tmp=obj.cells[24].textContent.trim().split("- Requestor Comments -");
						window.parent.document.getElementById("requestor_comments").value=req_comm_tmp[1];
						//alert(req_comm_tmp[0] + "  " + req_comm_tmp[1])
					//	if (req_comm_tmp[1].indexOf("|")>0)
					//	{
					//		const comm_tmp=req_comm_tmp[1].split(" | ");
					//		window.parent.document.getElementById("requestor_comments").value=comm_tmp[1];
					//	} else {
					//		window.parent.document.getElementById("requestor_comments").value=req_comm_tmp[1];
					//	}
					//} else if (obj.cells[24].textContent.trim().indexOf("|")>=0)
					//	{
					//		const comm_tmp=obj.cells[24].textContent.trim().split(" | ");
					//		window.parent.document.getElementById("requestor_comments").value=comm_tmp[1];
						} else {
						window.parent.document.getElementById("requestor_comments").value=obj.cells[24].textContent.trim();
							}
						
					
					
					//window.parent.document.getElementById('tr_detail').style.display="table-row";
					window.parent.document.getElementById("validate_new_row").style.display="inline";
					window.parent.document.querySelector('#td_detail').setAttribute('class','enabled');
					//window.parent.document.getElementById("t_detail").style.backgroundcolor="white";
					//window.parent.document.querySelector('#t_detail').setAttribute('class',"enabled");
				}
			// ------
			
		//CHANGEMENT STYLE BOUTON
		if (window.parent.document.getElementById("id_to_update").value>0)
		{
			window.parent.document.querySelector('#validate_new_row').value="UPDATE";
			window.parent.document.querySelector('#validate_new_row').setAttribute('class', 'btn orange');
			window.parent.document.querySelector('#validate_new_row').setAttribute('Title','Modify the picked row/reference with the data you just changed.');
		} else {
			window.parent.document.querySelector('#validate_new_row').value="ADD REF/DRAWING";
			window.parent.document.querySelector('#validate_new_row').setAttribute('class', 'btn blue2');
		}	
		
		
		// MISE EN LUMIERE DE LA LIGNE SELECTIONNEE
		var tbl = obj.parentNode;
		var rows = document.getElementsByTagName('tr');

;		for (var i=0;i<rows.length;i++)
		{
			if (rows[i]!=obj) 
			{
				rows[i].setAttribute('class',"unpicked_line");
			} else {
				obj.setAttribute('class',"picked_line");
			}
		}
		
	}

</script>

</head>

<body>



<?php 

    if (isset($_GET['Ref_ID']) && (($_GET['Act'])=='Del')) //
    {

			//On recupere l'ID du plan et prepare la requete
            $Ref_ID=$_GET['Ref_ID'];
			$sql_1 = 'DELETE FROM tbl_released_drawing WHERE ID like "'.$Ref_ID.'"';		
		
			//Connexion BD
include ('../REL_Connexion_DB.php');
			
			// On lance la requete de suppre
			$resultat = $mysqli->query($sql_1);
 
			//Suppression du fichier / plan associé à la ref
			// ----------------------------------------------
			$sql_file='SELECT Drawing_Path FROM tbl_released_drawing WHERE ID like "'.$Ref_ID.'"';
			
			$result = $mysqli->query($sql_file);
			while ($row = $result->fetch_assoc())
			{
				$file_path_db=$row['Drawing_Path'];
			}
			
			$path_attachment="DRAWINGS\IN_PROCESS\\";
			if ($file_path_db!="")												// SUPPRESION DU FICHIER EXISTANT S'IL EXISTE
			{
				$file_to_delete=$path_attachment.$file_path_db;			// Chemin complet du fichier existant
				unlink($file_to_delete);										// Suppression
			}
			// ----------------------------------------------
 
			
 
            // on ferme la connexion
            mysqli_close($mysqli);
			
			        
            $ref_link='REL_Package_Content.php?Rel_Pack_Num='.$_GET['Rel_Pack_Num'];

        }
        
?>



    
    <form enctype="multipart/form-data" action="" method="post">
    
    
    <?php
        include('../REL_Connexion_DB.php');
        
        $requete_pack = 'SELECT DISTINCT Rel_Pack_Num FROM tbl_released_package WHERE Rel_Pack_Num like "'.$_GET['Rel_Pack_Num'].'"';
        $resultat_Pack = $mysqli->query($requete_pack);
        $pack_exist=mysqli_num_rows($resultat_Pack);
        
        $requete = 'SELECT * FROM tbl_released_drawing WHERE Rel_Pack_Num like "'.$_GET['Rel_Pack_Num'].'" ORDER BY Reference, Prod_Draw ASC';
        $resultat = $mysqli->query($requete);
	    $rowcount=mysqli_num_rows($resultat);

        if ($rowcount>0)
        {
            echo '
            <table id="t01" border=1>
				<th HIDDEN>o</th>
                <th>Action</th>
                <th colspan=2>Reference</th>
                <th colspan=2>Product. Drawing</th>
                <th>Title</th>
                <th>Alias</th>
                <th colspan=2>Cust. Drawing</th>
                <th colspan=2>Doc Type</th>
                <th>Mat Type</th>
                <th>Inventory Imp.</th>
                <th>Ex</th>
                <th colspan=2>Weight</th>
                <th colspan=2>Plat. Surface</th>
                <th>Material</th>
				<th>ECCN</th>
				<th>RDO</th>
				<th>HTS</th>
                <th>Comments</th>';

            while ($row = $resultat->fetch_assoc())
            {

                echo '<tr title="Double-click to activate the modification" ondblclick="data_to_form(this,t01)" >
				<td HIDDEN >
					<input  type="radio" id="Radio_Picked_'.$row['ID'].'" name="Picked_user" value="'.$row['ID'].'">
				</td>';
                //echo '<td><button class="btn blue2" style="font-size:11; vertical-align:middle;width:20px;height:20px;" name="validate_new_row" value="Add Ref/Draw" title="Add the ref/drawing to the current release package" onclick="ref_modif()"/></td>';
                echo '<td HIDDEN><div id="Body" name="ID" style="height:13pt;">'.$row['ID'].'</div></td>';
                
				echo '<td><div id="Body" name="Action" style="height:13pt;width:4,5%;">'.$row['Action'].'</div></td>';
                        
                echo '<td><div id="Body" name="Reference" style="height:13pt;width:7,5;">'.$row['Reference'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Reference" size=17 name="Reference" style="font-size:11;height:13pt;" value="'.$row['Reference'].'"></div></td>';
				
				echo '<td><div id="Body" name="Ref_Rev" style="height:13pt;width:1,2%;">'.$row['Ref_Rev'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Ref_Rev" name="Ref_Rev" style="font-size:11;height:13pt;width:25px" value="'.$row['Ref_Rev'].'"></div></td>';

				echo '<td><div id="Body" name="Prod_Draw" style="height:13pt;width:7,5%;">';
				if ($row['Prod_Draw'] != "" && $row['Prod_Draw_Rev'] != "") {
					echo '<a href="https://app.aletiq.com/parts/preview/id/' . $row['Prod_Draw'] . '/revision/' . $row['Prod_Draw_Rev'] . '" target="_blank">' . $row['Prod_Draw'] . ' rev' . $row['Prod_Draw_Rev'] . '</a>';
				} else {
					echo $row['Prod_Draw']. ' rev' . $row['Prod_Draw_Rev'];
				}
				echo '</td>';

				echo '<td><div id="Body" name="Prod_Draw_Rev" style="height:13pt;width:1,2%;">'.$row['Prod_Draw_Rev'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Prod_Draw_Rev" name="Prod_Draw_Rev" style="font-size:11;height:13pt;width:25" value="'.$row['Prod_Draw_Rev'].'"></div></td>';
				
				echo '<td><div id="Body" name="Ref_Title" style="height:13pt;width:14,9%;">'.$row['Ref_Title'].'</div></td>';
				//echo '<td><div id="Body"> <input type="text" id="Ref_Title" name="Ref_Title" style="font-size:11;height:13pt;width:185" value="'.$row['Ref_Title'].'"></div></td>';
				
				echo '<td><div id="Body" name="Alias" style="height:13pt;width:9,3%;">'.$row['Alias'].'</div></td>';
				//echo '<td><div id="Body"> <input type="text" id="Alias" name="Alias" style="font-size:11;height:13pt;width:100px" value="'.$row['Alias'].'"></div></td>';
				
				echo '<td><div id="Body" name="Cust_Drawing" style="height:13pt;width:5,3%;">'.$row['Cust_Drawing'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Cust_Drawing" size=18 name="Cust_Drawing" style="font-size:11;height:13pt;" value="'.$row['Cust_Drawing'].'"></div></td>';
				
				echo '<td><div id="Body" name="Cust_Drawing_Rev" style="height:13pt;width:1,1%;">'.$row['Cust_Drawing_Rev'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Cust_Drawing_Rev" name="Cust_Drawing_Rev" style="font-size:11;height:13pt;width:25" value="'.$row['Cust_Drawing_Rev'].'"></div></td>';

				echo '<td><div id="Body" name="Doc_Type" style="height:13pt;width:3,3%;">'.$row['Doc_Type'].'</div></td>';
				if ($row['Internal_Mach_Rec']==true)
				{
					echo '<td><div id="Body" name="inhouse_manuf" title="Pick that option if the part is critical and internal machinning expertise preferred. &#013Could be applicable to glass-to-metal seal bodies, Silver-plated PEEK insulators, LSR Parts etc...">
							<FONT style="font-size:1px;color:white">X</FONT>
						<img style="margin-left:-5px;" src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15">
						 </div></td>';
				} else {
					//echo '<td><div id="Body"><input type="checkbox" id="inhouse_manuf" name="inhouse_manuf"  title="Pick that option if the part is critical and internal machinning expertise preferred. &#013 Could be applicable to glass-to-metal seal bodies, PEEK insulator etc..." onclick="return false;"></div></td>';
					echo '<td><div id="Body" name="inhouse_manuf" title="Pick that option if the part is critical and internal machinning expertise preferred. &#013Could be applicable to glass-to-metal seal bodies, Silver-plated PEEK insulators, LSR Parts etc..."></div></td>';
				
				}

				echo '<td><div id="Body" name="Material_Type" style="height:13pt;">'.$row['Material_Type'].'</div></td>';

				echo '<td><div id="Body" name="Inventory_Impact" style="height:13pt;">'.$row['Inventory_Impact'].'</div></td>';
				
				if ($row['Ex']!="NO")
				{
					$col="red";
				} else {
					$col="black";
				}
				echo '<td><div id="Body" name="Ex" style="height:13pt;color:'.$col.'">'.$row['Ex'].'</div></td>';


				echo '<td>
						<div id="Body" name="Weight" style="height:13pt;">'.$row['Weight'].'</div>
					  </td>
					  <td>
						 <div id="Body" name="Weight_Unit" style="height:13pt;">'.$row['Weight_Unit'].'</div>
					  </td>';

				echo '<td style="text-align:center">
						<div id="Body" name="Plating_Surface" style="height:13pt;text-align:center">'.$row['Plating_Surface'].'</div>
					</td>
					<td>
						<div id="Body" name="Plating_Surface_Unit" style="height:13pt;">'.$row['Plating_Surface_Unit'].'</div>
					</td>';
				

				echo '
				<td>
					<div id="Body" style="width:3,6%">'.$row['FXXX'].'</div>
				</td>
				<td>
					<div id="Body" style="">'.$row['ECCN'].'</div>
				</td>
				<td>
					<div id="Body" style="">'.$row['RDO'].'</div>
				</td>
				<td>
					<div id="Body" style="">'.$row['HTS'].'</div>
				</td>';

				echo '<td>';
				
				
				
				if (stripos($row['Requestor_Comments'],"Verif BE :")>0)
				{
					$P2_comment_str=explode("Verif BE :", $row['Requestor_Comments']);
					$P2_comment=trim(htmlspecialchars_decode(nl2br($P2_comment_str[0]), ENT_QUOTES));
					$P1_comment=trim(htmlspecialchars_decode(nl2br($P2_comment_str[0]), ENT_QUOTES));
				} else {
					$P2_comment="";
					$P1_comment=trim(htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES));
				}
				
				
				$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");
				$nmax = 0;   				//$nmax = 30;
				if ((strlen($row['Requestor_Comments']) > $nmax)) {
					echo '<div class="dropdown">';
					echo '<span>
							 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
						  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . $P1_comment . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
				}
				
				

				$nbre_lignes = substr_count(nl2br($P2_comment), "\n");
				$nmax = 0;   				//$nmax = 30;
				if ((strlen($P2_comment) > $nmax)) {
					echo ' | ';
					echo htmlspecialchars(substr(nl2br($P2_comment), 0, $nmax), ENT_QUOTES);
						echo '<div class="dropdown">';
						echo '<span>
								<img src="\Common_Resources\general_comment_icon_r.png" style="height:15px; opacity:1" >
							  </span>';
						echo '<div class="dropdown-content">';
						echo '<p><b>- <u>Verif BE Comments</u> -</b><br \>' . $P2_comment . '</p>';
						echo '</div>';
						echo '</div>';
				} else {

				}	
				
				echo '
				</td>
				
				<td HIDDEN>
					<div id="Body" id="drawing_fullname" name="drawing_fullname" style="height:13pt;">'.$row['Drawing_Path'].'</div>
				</td>
				</tr>';
				
			}
            
			mysqli_close($mysqli);
            echo '</table>';
        } elseif ($rowcount==0 && $pack_exist==1) {
            echo '<div id="empty">The package is emtpy...</div>';
        } else {
            echo '<div id="empty">The package does not exist...</div>';
        }
		
    ?>
    <!------------------------------>
    
    

    </form>
    



</body> 
</html>