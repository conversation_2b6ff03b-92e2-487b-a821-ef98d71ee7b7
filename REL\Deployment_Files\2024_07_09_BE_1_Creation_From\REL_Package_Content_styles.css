body {
	width:99%;
	font-size:8pt;
	font-weight:normal;
	font-family:Tahoma, sans-serif;
	vertical-align:middle;
	background-image: url("/Common_Resources/logo_scm_zoom_in_transparent.jpg");
	background-repeat: no-repeat;
	background-position: right bottom;
}

div#Body {
	/*text-indent:15px;*/
	font-size:8pt;
	margin-left:1px;
	margin-right:1px;
	text-align:center;
	vertical-align:middle;
	background-color:transparent;
	}
	
input[type="image"]:hover {
  border-bottom: 1px solid black;
  border-right: 1px solid black;
  margin-top: -1px;
  transform: scale(1.15);
  /*box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);*/
}

div#empty{
	font-size:10pt;
	text-align:center;
	vertical-align:middle;
	background-color:transparent;
	font-style:italic;
	color:grey;
}

#t01 {
	border-collapse: collapse;
	vertical-align: middle;
	text-align:center;
	font-family:Tahoma, sans-serif;
	width:100%;

	}
	
#t01 th {
	font-size:8pt;
	font-weight:bold;
	border:1px solid black;
	background-color:rgb(27,79,114);
	color:white;
	padding-bottom:1px;
	padding-top:1px;
	padding-left:1px;
	padding-right:1px;
	height:15px;
	}



#t01 td {
	vertical-align:darkgray;
	border:1px solid darkgray;

	}

#t01 tr { 
	height:25px;
	
	/*background-color:#E9F6FF;*/
	/*background-color:white;*/	
	}

#t01  tr:hover  {
	background-color:#E8F0FF;
  	cursor:pointer;
	font-style:italic;
}


/* #t01  tr:hover td:not(:last-child)  { */
	/* background-color:#E8F0FF; */
  	/* cursor:pointer; */
	/* font-style:italic; */
/* } */


.picked_line
{
background-color:#E8F8F5; /*#C8CAD5*/

border-left: 6px double #afca0b; 
box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
.unpicked_line
{
  background-color:transparent;
  /* border-top:0.5px solid black; */
  /* border-bottom:0.5px solid black; */
}

.copied_line
{
background-color:#A0A0A0; /*#C8CAD5*/
border: 2.5px double red; 
}


