<?php
    require('../login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));

    include('../../REL_Connexion_DB.php');
        
    $requete = 'SELECT Reference FROM tbl_released_drawing WHERE Rel_Pack_Num like "'.$_REQUEST['Rel_Pack_Num'].'" ORDER BY Reference, Prod_Draw ASC';
    $resultat = $mysqli->query($requete);
    $rows = array();
    while($r = $resultat->fetch_assoc()) {
        $rows[] = $r;
    }
    header('Content-Type: application/json');
    echo json_encode($rows);
?>