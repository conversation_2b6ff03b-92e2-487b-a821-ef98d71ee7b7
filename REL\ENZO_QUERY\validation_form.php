<?php
if (isset($_POST['validate_new_row']) && (($_POST['Action']) != "") && (($_POST['Inventory_Impact']) != "") && (($_POST['Material_Type']) != "") && (($_POST['Ex']) != "") && (($_POST['Doc_Type']) != "")) //
	{

		#FOR DEBUGGING ONLY
		#print_r($_POST);

		$pack = $_GET['ID'];
		$reference = $_POST['Reference'];
		$ref_rev = $_POST['ref_rev'];
		$prod_draw = $_POST['prod_draw'];
		$prod_draw_rev = $_POST['prod_draw_rev'];
		$ref_title = htmlspecialchars($_POST['ref_title'], ENT_QUOTES);
		$alias = $_POST['Alias_name'];
		if ($alias == "") {
			$alias = "";
		}
		$action = $_POST['Action'];
		$doc_type = $_POST['Doc_Type'];
		$material_type = $_POST['Material_Type'];
		$inventory_impact = $_POST['Inventory_Impact'];
		$ex = $_POST['Ex'];
		$proc_type = "";
		$visa_project = "";
		$date_project = "0000-00-00";
		$visa_inventory = "";
		$date_inventory = "0000-00-00";
		$visa_product = "";
		$date_product = "0000-00-00";
		$visa_quality = "";
		$visa_owner_quality = "";
		$date_quality = "0000-00-00";
		$visa_methode = "";
		$date_methode = "0000-00-00";
		$visa_finance = "";
		$date_finance = "0000-00-00";
		$visa_prod = "";
		$date_prod = "0000-00-00";
		$visa_supply = "";
		$date_supply = "0000-00-00";
		$visa_pur_1 = "";
		$date_pur_1 = "0000-00-00";
		$visa_pur_2 = "";
		$date_pur_2 = "0000-00-00";
		$visa_pur_3 = "";
		$date_pur_3 = "0000-00-00";
		$visa_pur_4 = "";
		$date_pur_4 = "0000-00-00";
		$visa_pur_5 = "";
		$date_pur_5 = "0000-00-00";
		$visa_pur_5 = "";
		$date_pur_5 = "0000-00-00";
		$visa_gid = "";
		$date_gid = "0000-00-00";
		$visa_gid_2 = "";
		$date_gid_2 = "0000-00-00";
		$visa_MOF = "";
		$date_MOF = "0000-00-00";
		$visa_Q_PROD = "";
		$date_Q_PROD = "0000-00-00";
		$visa_routing_entry = "";
		$date_routing_entry = "0000-00-00";
		$visa_metro = "";
		$date_metro = "0000-00-00";
		$visa_labo = "";
		$date_labo = "0000-00-00";
		$q_inspection = "Z08";
		$q_dynam = "";
		$q_doc_req = "";
		$q_control_routing = "";
		$weight = intval($_POST['Weight']);
		$weight_unit = $_POST['Weight_Unit_name'];
		$cls = "0";
		$moq = "0";
		$product_code = "";
		$prod_agent = "";
		$mof = "";
		$commodity_code = "";
		$pur_group = "";
		$mat_prod_type = "";
		$leadtime = "0";
		$pris_dans_1 = "";
		$pris_dans_2 = "";
		$supervisor = "";
		$fia = "";
		$metro_time = "0";
		$metro_control = "";
		$eccn = $_POST['eccn'];
		$hts = $_POST['hts'];
		$rdo = $_POST['RDO'];
		
		$visa_inventory_stock="";
		$date_inventory_stock="0000-00-00";

		$mat_prod_type = "";
		$unit = "";

		if ($weight == 0) {
			$weight_unit = "";
		}

		if (isset($_POST['Plating_Surface'])) 
		{
			$plating_surface = intval($_POST['Plating_Surface']);
			if (isset($_POST['Plating_Surface_Unit_name'])) {
				$plating_surface_unit = $_POST['Plating_Surface_Unit_name'];
			} else {
				$plating_surface_unit = "";
			}
		} else {
			$plating_surface = "";
			$plating_surface_unit = "-";
		}

		$fxxx = $_POST['Material'];
		if ($fxxx == "") {
			$fxxx = "";
		}

		$requestor_comments = htmlspecialchars($_POST['requestor_comments'], ENT_QUOTES);

		if (isset($_POST['inhouse_manuf'])) {
			$inhouse_manuf = 1;
		} else {
			$inhouse_manuf = 0;
		}

		$cust_drawing = $_POST['cust_draw'];
		if ($cust_drawing == "") {
			$cust_drawing = "";
		}

		$cust_drawing_rev = $_POST['cust_draw_rev'];
		if ($cust_drawing_rev == "") {
			$cust_drawing_rev = "";
		}

		//ISOLEMENT DE LA REF DU FICHIER/PLAN CHOISI
		if (isset($_FILES['prod_drawing_file'])) {
			//$drawing_file_name=$_FILES['prod_drawing_file']['tmp_name'][0];
			$drawing_file_name = basename($_FILES['prod_drawing_file']['name'][0]);
		}  else {
			$drawing_file_name = "";
		}
		if (strlen($_POST['file_copied_from'])>0)
		{
			$drawing_file_name = $_POST['file_copied_from'];
		}


		// Incremetation de l'ID pour la diff en cours
		//;

		
		// Incremetation de l'ID pour la diff en cours
		include('../REL_Connexion_DB.php');
		$draw_numbering = 'SELECT MAX(ID) from tbl_released_drawing';
		$draw_max_ID_tmp = $mysqli->query($draw_numbering);
		$draw_max_ID = mysqli_fetch_row($draw_max_ID_tmp);
		$draw_max = intval($draw_max_ID[0]) + 1;

		//include('../REL_Connexion_DB.php');
		// ####################################################################################
		// IF id_to_update=="" ou id_to_update==0 --> CREATION DANS TABLE TBL_RELREASE_DRAWING
		// ####################################################################################
		if (($_POST['id_to_update'] == 0) ||  ($_POST['id_to_update'] == "")) {
			//On prépare la commande sql d'insertion
			//Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
			$sql_1 = 'INSERT INTO tbl_released_drawing VALUES (
			"' . $draw_max . '",
			"' . $pack . '",
			"' . $reference . '",
			"' . $ref_rev . '",
			"' . $prod_draw . '",
			"' . $prod_draw_rev . '",
			"' . $drawing_file_name . '",
			"' . $ref_title . '",
			"' . $alias . '",
			"' . $cust_drawing . '",
			"' . $cust_drawing_rev . '",
			"' . $action . '",
			"' . $doc_type . '",
			"' . $proc_type . '",
			"' . $material_type . '",
			"' . $inventory_impact . '",
			"' . $ex . '",
			"' . $weight . '",
			"' . $weight_unit . '",
			"' . $plating_surface . '",
			"' . $plating_surface_unit . '",
			"' . $fxxx . '",
			"' . $inhouse_manuf . '",
			"' . $requestor_comments . '",
			"' . $visa_project . '",
			"' . $date_project . '",
			"' . $visa_inventory . '",
			"' . $date_inventory . '",
			"' . $visa_product . '",
			"' . $date_product . '",
			"' . $visa_owner_quality . '",
			"' . $visa_quality . '",
			"' . $date_quality . '",
			"' . $visa_methode . '",
			"' . $date_methode . '",
			"' . $visa_finance . '",
			"' . $date_finance . '",
			"' . $cls . '",
			"' . $moq . '",
			"' . $product_code . '",
			"' . $visa_prod . '",
			"' . $date_prod . '",
			"' . $pur_group . '",
			"' . $prod_agent . '",
			"' . $mof . '",
			"' . $commodity_code . '",
			"' . $mat_prod_type . '",
			"' . $unit . '",
			"' . $leadtime . '",
			"' . $pris_dans_1 . '",
			"' . $pris_dans_2 . '",
			"' . $visa_supply . '",
			"' . $date_supply . '",
			"' . $eccn . '",
			"' . $rdo . '",
			"' . $hts . '",
			"' . $supervisor . '",
			"' . $visa_pur_1 . '",
			"' . $date_pur_1 . '",
			"' . $visa_pur_2 . '",
			"' . $date_pur_2 . '",
			"' . $visa_pur_3 . '",
			"' . $date_pur_3 . '",
			"' . $fia . '",
			"' . $visa_pur_4 . '",
			"' . $date_pur_4 . '",
			"' . $visa_pur_5 . '",
			"' . $date_pur_5 . '",
			"' . $visa_gid . '",
			"' . $date_gid . '",
			"' . $visa_gid_2 . '",
			"' . $date_gid_2 . '",
			"' . $visa_MOF . '",
			"' . $date_MOF . '",
			"' . $visa_routing_entry . '",
			"' . $date_routing_entry . '",
			"' . $visa_labo . '",
			"' . $date_labo . '",
			"' . $metro_time . '",
			"' . $metro_control . '",
			"' . $visa_metro . '",
			"' . $date_metro . '",
			"' . $visa_Q_PROD . '",
			"' . $date_Q_PROD . '",
			"' . $q_inspection . '",
			"' . $q_dynam . '",
			"' . $q_doc_req . '",
			"' . $q_control_routing . '",
			"' . $visa_inventory_stock . '",
			"' . $date_inventory_stock . '",
			"",
			"0"
			
			);';


			$resultat = $mysqli->query($sql_1);

			// ################################
			//  CHARGEMENT FICHIER SUR SERVEUR 
			// ################################
			if ($drawing_file_name != "" && strlen($_POST['file_copied_from'])==0)   // Confirmer que l'utilisateur a désigné un fichier
			{
				$path_attachment = $_SERVER['DOCUMENT_ROOT'] . "\REL\DRAWINGS\IN_PROCESS";
				if ($_FILES['prod_drawing_file']['size'][0] <= ********) // sS'ssurer que la taille du fichier ne dépasse pas 60Mo
				{
					$tmp_file = $_FILES['prod_drawing_file']['tmp_name'][0];
					move_uploaded_file($_FILES['prod_drawing_file']['tmp_name'][0], $path_attachment . "\\" . basename($_FILES['prod_drawing_file']['name'][0]));
				} else {
					echo 'Le fichier ' . $_FILES['prod_drawing_file']['name'][0] . 'est trop gros';
				}
			}
			// ################################
		}
    }