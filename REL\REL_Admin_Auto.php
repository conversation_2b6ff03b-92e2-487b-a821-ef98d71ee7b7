<?php

// --------------------------------------------Paramètre Production----------------------------------------------------------

if($_GET['root']=='rdo'){
    include('../SCM_Connexion_DB.php');
    $requete_rdo="SELECT tbl_rdo.Description FROM tbl_rdo WHERE RDO like '".$_GET['RDO']."'";
    $resultat_rdo = $mysqli_scm->query($requete_rdo);
    $rowcount=mysqli_num_rows($resultat_rdo);
    if($rowcount>0){
        while($row_rdo=$resultat_rdo->fetch_assoc()){
            $output_value_rdo=$row_rdo['Description'];
        }
        echo $output_value_rdo;
        $mysqli_scm->close();
    }else{
        $output_value_rdo="";
        echo $output_value_rdo;
    }
}

if($_GET['root']=='product'){
    include('../SCM_Connexion_DB.php');
    $requete_product="SELECT tbl_product_code.Description FROM tbl_product_code WHERE Code like '".$_GET['Code']."'";
    $resultat_product = $mysqli_scm->query($requete_product);
    $rowcount=mysqli_num_rows($resultat_product);
    if($rowcount>0){
        while($row_product=$resultat_product->fetch_assoc()){
            $output_value_product=$row_product['Description'];
        }
        echo $output_value_product;
        $mysqli_scm->close();
    }else{
        $output_value_product="";
        echo $output_value_product;
    }
}

if($_GET['root']=='fxxx'){
    include('../SCM_Connexion_DB.php');
    $requete_fxxx="SELECT tbl_fxxx.fxxx_description FROM tbl_fxxx WHERE fxxx_ref like '".$_GET['fxxx_ref']."'";
    $resultat_fxxx = $mysqli_scm->query($requete_fxxx);
    $rowcount=mysqli_num_rows($resultat_fxxx);
    if($rowcount>0){
        while($row_fxxx=$resultat_fxxx->fetch_assoc()){
            $output_value_fxxx=$row_fxxx['fxxx_description'];
        }
        echo $output_value_fxxx;
        $mysqli_scm->close();
    }else{
        $output_value_fxxx="";
        echo $output_value_fxxx;
    }
}

// -----------------------------------------------Paramètre Opération------------------------------------------------------------

if($_GET['root']=='buyer'){
    include ('../REL_Connexion_DB.php');
    $requete_buyer="SELECT tbl_buyer.name FROM tbl_buyer WHERE buyer like '".$_GET['Code']."'";
    $resultat_buyer = $mysqli->query($requete_buyer);
    $rowcount=mysqli_num_rows($resultat_buyer);
    if($rowcount>0){
        while($row_buyer=$resultat_buyer->fetch_assoc()){
            $output_value_buyer=$row_buyer['name'];
        }
        echo $output_value_buyer;
        $mysqli->close();
    }else{
        $output_value_buyer="";
        echo $output_value_buyer;
    }
}

if($_GET['root']=='commo'){
    include ('../REL_Connexion_DB.php');
    $requete_commo="SELECT tbl_commodity_code.Description FROM tbl_commodity_code WHERE Code like '".$_GET['Code']."'";
    $resultat_commo = $mysqli->query($requete_commo);
    $rowcount=mysqli_num_rows($resultat_commo);
    if($rowcount>0){
        while($row_commo=$resultat_commo->fetch_assoc()){
            $output_value_commo=$row_commo['Description'];
        }
        echo $output_value_commo;
        $mysqli->close();
    }else{
        $output_value_commo="";
        echo $output_value_commo;
    }
}

if($_GET['root']=='sup'){
    include ('../REL_Connexion_DB.php');
    $requete_sup="SELECT tbl_supervisor.Description FROM tbl_supervisor WHERE Code like '".$_GET['Code']."'";
    $resultat_sup = $mysqli->query($requete_sup);
    $rowcount=mysqli_num_rows($resultat_sup);
    if($rowcount>0){
        while($row_sup=$resultat_sup->fetch_assoc()){
            $output_value_sup=$row_sup['Description'];
        }
        echo $output_value_sup;
        $mysqli->close();
    }else{
        $output_value_sup="";
        echo $output_value_sup;
    }
}

if($_GET['root']=='hts'){
    include ('../SCM_Connexion_DB.php');
    $requete_hts="SELECT tbl_hts.Description FROM tbl_hts WHERE HTS like '".$_GET['HTS']."'";
    $resultat_hts = $mysqli_scm->query($requete_hts);
    $rowcount=mysqli_num_rows($resultat_hts);
    if($rowcount>0){
        while($row_hts=$resultat_hts->fetch_assoc()){
            $output_value_hts=$row_hts['Description'];
        }
        echo $output_value_hts;
        $mysqli_scm->close();
    }else{
        $output_value_hts="";
        echo $output_value_hts;
    }
}

if($_GET['root']=='doc_type'){
    include('../REL_Connexion_DB.php');
    $requete_doc_type="SELECT tbl_doc_type.Doc_Type_Description FROM tbl_doc_type WHERE Doc_Type like '".$_GET['Doc_Type']."'";
    $resultat_doc_type = $mysqli->query($requete_doc_type);
    $rowcount=mysqli_num_rows($resultat_doc_type);
    if($rowcount>0){
        while($row_doc_type=$resultat_doc_type->fetch_assoc()){
            $output_value_doc_type=$row_doc_type['Doc_Type_Description'];
        }
        echo $output_value_doc_type;
        $mysqli->close();
    }else{
        $output_value_doc_type="";
        echo $output_value_doc_type;
    }
}

if($_GET['root']=='sap_type'){
    include('../REL_Connexion_DB.php');
    $requete_sap_type="SELECT tbl_sap_type.Description FROM tbl_sap_type WHERE SAP_Type like '".$_GET['SAP_Type']."'";
    $resultat_sap_type = $mysqli->query($requete_sap_type);
    $rowcount=mysqli_num_rows($resultat_sap_type);
    if($rowcount>0){
        while($row_sap_type=$resultat_sap_type->fetch_assoc()){
            $output_value_sap_type=$row_sap_type['Description'];
        }
        echo $output_value_sap_type;
        $mysqli->close();
    }else{
        $output_value_sap_type="";
        echo $output_value_sap_type;
    }
}

if($_GET['root']=='material_type'){
    include('../REL_Connexion_DB.php');
    $requete_mat_type="SELECT tbl_material_type.Description FROM tbl_material_type WHERE Material_Type like '".$_GET['Material_Type']."'";
    $resultat_mat_type = $mysqli->query($requete_mat_type);
    $rowcount=mysqli_num_rows($resultat_mat_type);
    if($rowcount>0){
        while($row_mat_type=$resultat_mat_type->fetch_assoc()){
            $output_value_mat_type=$row_mat_type['Description'];
        }
        echo $output_value_mat_type;
        $mysqli->close();
    }else{
        $output_value_mat_type="";
        echo $output_value_mat_type;
    }
}

if($_GET['root']=='invent'){
    include('../REL_Connexion_DB.php');
    $requete_invent="SELECT tbl_inventory_impact.Description FROM tbl_inventory_impact WHERE Inventory_Impact like '".$_GET['Inventory_Impact']."'";
    $resultat_invent = $mysqli->query($requete_invent);
    $rowcount=mysqli_num_rows($resultat_invent);
    if($rowcount>0){
        while($row_invent=$resultat_invent->fetch_assoc()){
            $output_value_invent=$row_invent['Description'];
        }
        echo $output_value_invent;
        $mysqli->close();
    }else{
        $output_value_invent="";
        echo $output_value_invent;
    }
}

// ----------------------------------------------------------Paramètre Qualité-----------------------------------------------------------

if($_GET['root']=='inspection'){
    include ('../REL_Connexion_DB.php');
    $requete_inspection="SELECT tbl_q_inspection_type.Description FROM tbl_q_inspection_type WHERE Code like '".$_GET['Code']."'";
    $resultat_inspection = $mysqli->query($requete_inspection);
    $rowcount=mysqli_num_rows($resultat_inspection);
    if($rowcount>0){
        while($row_inspection=$resultat_inspection->fetch_assoc()){
            $output_value_inspection=$row_inspection['Description'];
        }
        echo $output_value_inspection;
        $mysqli->close();
    }else{
        $output_value_inspection="";
        echo $output_value_inspection;
    }
}

if($_GET['root']=='dynam'){
    include ('../REL_Connexion_DB.php');
    $requete_dynam="SELECT tbl_q_dynamisation_rules.Description FROM tbl_q_dynamisation_rules WHERE Code like '".$_GET['Code']."'";
    $resultat_dynam = $mysqli->query($requete_dynam);
    $rowcount=mysqli_num_rows($resultat_dynam);
    if($rowcount>0){
        while($row_dynam=$resultat_dynam->fetch_assoc()){
            $output_value_dynam=$row_dynam['Description'];
        }
        echo $output_value_dynam;
        $mysqli->close();
    }else{
        $output_value_dynam="";
        echo $output_value_dynam;
    }
}

if($_GET['root']=='doc_req'){
    include ('../REL_Connexion_DB.php');
    $requete_doc="SELECT tbl_q_doc_requirements.Description FROM tbl_q_doc_requirements WHERE Code like '".$_GET['Code']."'";
    $resultat_doc = $mysqli->query($requete_doc);
    $rowcount=mysqli_num_rows($resultat_doc);
    if($rowcount>0){
        while($row_doc=$resultat_doc->fetch_assoc()){
            $output_value_doc=$row_doc['Description'];
        }
        echo $output_value_doc;
        $mysqli->close();
    }else{
        $output_value_doc="";
        echo $output_value_doc;
    }
}

if($_GET['root']=='control'){
    include ('../REL_Connexion_DB.php');
    $requete_control="SELECT tbl_q_control_routing.Description FROM tbl_q_control_routing WHERE Code like '".$_GET['Code']."'";
    $resultat_control = $mysqli->query($requete_control);
    $rowcount=mysqli_num_rows($resultat_control);
    if($rowcount>0){
        while($row_control=$resultat_control->fetch_assoc()){
            $output_value_control=$row_control['Description'];
        }
        echo $output_value_control;
        $mysqli->close();
    }else{
        $output_value_control="";
        echo $output_value_control;
    }
}

if($_GET['root']=='metro'){
    include ('../REL_Connexion_DB.php');
    $requete_metro_control="SELECT tbl_metro.Description FROM tbl_metro WHERE Control like '".$_GET['Control']."'";
    $resultat_metro_control = $mysqli->query($requete_metro_control);
    $rowcount=mysqli_num_rows($resultat_metro_control);
    if($rowcount>0){
        while($row_metro_control=$resultat_metro_control->fetch_assoc()){
            $output_value_metro_control=$row_metro_control['Description'];
        }
        echo $output_value_metro_control;
        $mysqli->close();
    }else{
        $output_value_metro_control="";
        echo $output_value_metro_control;
    }
}

// ----------------------------------------------------------Matrice Decision-----------------------------------------------------------

if($_GET['root']=='hts_decision'){
    include('../REL_Connexion_DB.php');
    $requete_hts_decision="SELECT tbl_hts_decision.Q1_Title FROM tbl_hts_decision WHERE HTS like '".$_GET['HTS']."'";
    $resultat_hts_decision = $mysqli->query($requete_hts_decision);
    $rowcount=mysqli_num_rows($resultat_hts_decision);
    if($rowcount>0){
        while($row_hts_decision=$resultat_hts_decision->fetch_assoc()){
            $output_value_hts_decision=$row_hts_decision['Q1_Title'];
        }
        echo $output_value_hts_decision;
        $mysqli->close();
    }else{
        $output_value_hts_decision="";
        echo $output_value_hts_decision;
    }
}

if($_GET['root']=='eccn_decision'){
    include('../REL_Connexion_DB.php');
    $requete_eccn_decision="SELECT tbl_eccn_decision.Q1_Title FROM tbl_eccn_decision WHERE ECCN like '".$_GET['ECCN']."'";
    $resultat_eccn_decision = $mysqli->query($requete_eccn_decision);
    $rowcount=mysqli_num_rows($resultat_eccn_decision);
    if($rowcount>0){
        while($row_eccn_decision=$resultat_eccn_decision->fetch_assoc()){
            $output_value_eccn_decision=$row_eccn_decision['Q1_Title'];
        }
        echo $output_value_eccn_decision;
        $mysqli->close();
    }else{
        $output_value_eccn_decision="";
        echo $output_value_eccn_decision;
    }
}

// ------------------------------------------------AUTRES----------------------------------------------------------------

if($_GET['root']=='unit'){
    include('../SCM_Connexion_DB.php');
    $requete_unit="SELECT tbl_unit.Unit_Type FROM tbl_unit WHERE Unit like '".$_GET['Unit']."'";
    $resultat_unit = $mysqli_scm->query($requete_unit);
    $rowcount=mysqli_num_rows($resultat_unit);
    if($rowcount>0){
        while($row_unit=$resultat_unit->fetch_assoc()){
            $output_value_unit=$row_unit['Unit_Type'];
        }
        echo $output_value_unit;
        $mysqli_scm->close();
    }else{
        $output_value_unit="";
        echo $output_value_unit;
    }
}
?>