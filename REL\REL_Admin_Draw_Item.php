<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="utf-8" />

<link rel="stylesheet" type="text/css" href="REL_Admin_Pack_Draw_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<head>

    <title></title>

</head>

<?php
// MISE A JOUR DES DONNES ASSOCIEES AU NUMERO DE PACKAGE AVEC LES INFO FOURNIES PAR L'UTILISATEUR
$msg = "";
if (isset($_POST['btn_update'])) {
    include('../REL_Connexion_DB.php');
    $id = $_POST['ID_'];
    $pack_num = $_POST['Rel_Pack_Num'];
    $prod_draw_rev = $_POST['Prod_Draw_Rev'];
    $reference = $_POST['ref'];
    $ref_rev = $_POST['ref_rev'];
    $alias = $_POST['Alias'];
    if ($alias == "") {
        $alias = "";
    }
    $ref_title = htmlspecialchars($_POST['Ref_Title'], ENT_QUOTES);
    $action = $_POST['Action'];
    $doc_type = $_POST['Doc_Type'];
    $material_type = $_POST['Material_Type'];
    $unit = $_POST['Unit'];
    $inventory_impact = $_POST['Inventory_Impact'];
    $ex = $_POST['Ex'];
    $cust_drawing = $_POST['Cust_Drawing'];
    if ($cust_drawing == "") {
        $cust_drawing = "";
    }

    $cust_drawing_rev = $_POST['Cust_Drawing_Rev'];
    if ($cust_drawing_rev == "") {
        $cust_drawing_rev = "";
    }

    $weight = intval($_POST['Weight']);
    $weight_unit = $_POST['Weight_Unit'];
    if ($weight == 0) {
        $weight_unit = "";
    }

    if (isset($_POST['Plating_Surface'])) {
        $plating_surface = intval($_POST['Plating_Surface']);
        if (isset($_POST['Plating_Surface_Unit'])) {
            $plating_surface_unit = $_POST['Plating_Surface_Unit'];
        } else {
            $plating_surface_unit = "";
        }
    } else {
        $plating_surface = "";
        $plating_surface_unit = "";
    }

    $fxxx = $_POST['material'];
    if ($fxxx == "") {
        $fxxx = "";
    }

    $inhouse_manuf = "";
    if (isset($_POST['Internal_Mach_Rec'])) {
        $inhouse_manuf = 1;
    } else {
        $inhouse_manuf = 0;
    }
    $product_code = $_POST['Product_Code'];
    $mof = $_POST['MOF'];
    $requestor_comments = htmlspecialchars($_POST['Requestor_Comments'], ENT_QUOTES);

    $q_inspection = "";
    $query_insp = 'SELECT DISTINCT Code FROM tbl_q_inspection_type;';
    $resultat_insp = $mysqli->query($query_insp);
    $control_name = "";
    while ($row = $resultat_insp->fetch_assoc()) {
        $control_name = 'inspection_' . $row['Code'];
        if (isset($_POST[$control_name])) {
            if ($q_inspection != "") {
                $q_inspection = $q_inspection . ';' . $row['Code'];
            } else {
                $q_inspection = $row['Code'];
            }
        }
    }

    $q_doc_req = "";
    $i = 1;
    $query_doc = 'SELECT DISTINCT Code FROM tbl_q_doc_requirements;';
    $resultat_doc = $mysqli->query($query_doc);
    $rowcount = mysqli_num_rows($resultat_doc);
    $control_name = "";
    while ($i <= $rowcount) {
        $doc_name = 'DOC_Z';
        if ($i < 10) {
            $doc_name = $doc_name . '0' . $i;
        } else {
            $doc_name = $doc_name . $i;
        }
        $control_name = 'doc_req_' . $doc_name;
        if (isset($_POST[$control_name])) {
            if ($q_doc_req != "") {
                $q_doc_req = $q_doc_req . ';' . $doc_name;
            } else {
                $q_doc_req = $doc_name;
            }
        }
        $i++;
    }


    $q_control_routing = $_POST['Q_Control_Routing'];
    $q_dynam = $_POST['Q_Dynamization'];
    $pris_dans1 = $_POST['Pris_Dans1_'];
    $pris_dans2 = $_POST['Pris_Dans2'];
    $cls = $_POST['CLS'];
    $proc_type = $_POST['Proc_Type'];
    $rdo = $_POST['RDO'];
    $metro_time = $_POST['Metro_Time'];

    $metro_control = "";
    $query_metro = 'SELECT DISTINCT Control FROM tbl_metro;';
    $resultat_metro = $mysqli->query($query_metro);
    $control_name = "";
    while ($row_metro = $resultat_metro->fetch_assoc()) {
        $control_name = 'Control_Metro_' . $row_metro['Control'];
        if (isset($_POST[$control_name])) {
            if ($metro_control != "") {
                $metro_control = $metro_control . ';' . $row_metro['Control'];
            } else {
                $metro_control = $row_metro['Control'];
            }
        }
    }

    $moq = $_POST['MOQ'];
    $leadtime = $_POST['leadtime'];
    $mat_prod_type = $_POST['Mat_Prod_Type'];
    $fia = $_POST['FIA'];
    $purchasing_group = $_POST['Purchasing_Group'];
    $commodity = $_POST['Commodity_Code'];
    $supervisor = $_POST['Supervisor'];
    $eccn = $_POST['ECCN'];

    $comments = $_POST['Comments'];
    $date_comments = date('Y-m-d');
    $v = 'Admin - ' . $date_comments . ' : Modification ' . '\n' .  htmlspecialchars($_POST['Comments'], ENT_QUOTES);

    $requete_comments = 'SELECT DISTINCT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';
    $resultat_comments = $mysqli->query($requete_comments);
    // On affiche notre message et à la ligne on laisse l'ancien message
    while ($row_comments = $resultat_comments->fetch_assoc()) {
        if ($_POST['Comments'] != "") {
            $v = $v;
        } else {
            $v = $row_comments['General_Comments'];
        }
    }

    $visa_inventory = $_POST['VISA_Inventory'];

    if (isset($_POST['DATE_Inventory']) && $_POST['DATE_Inventory'] != "") {
        $date_inventory = $_POST['DATE_Inventory'];
    } else {
        $date_inventory = "0000-00-00";
    }

    $visa_product = $_POST['VISA_Product'];
    if (isset($_POST['DATE_Product'])  && $_POST['DATE_Product'] != "") {
        $date_product = $_POST['DATE_Product'];
    } else {
        $date_product = "0000-00-00";
    }

    $visa_quality = $_POST['VISA_Quality'];
    if (isset($_POST['DATE_Quality'])   && $_POST['DATE_Quality'] != "") {
        $date_quality = $_POST['DATE_Quality'];
    } else {
        $date_quality = "0000-00-00";
    }

    $visa_method = $_POST['VISA_Method'];
    if (isset($_POST['DATE_Method']) && $_POST['DATE_Method'] != "") {
        $date_method = $_POST['DATE_Method'];
    } else {
        $date_method = "0000-00-00";
    }

    $visa_finance = $_POST['VISA_Finance'];
    if (isset($_POST['DATE_Finance']) && $_POST['DATE_Finance'] != "") {
        $date_finance = $_POST['DATE_Finance'];
    } else {
        $date_finance = "0000-00-00";
    }

    $visa_prod = $_POST['VISA_Prod'];
    if (isset($_POST['DATE_Prod']) && $_POST['DATE_Prod'] != "") {
        $date_prod = $_POST['DATE_Prod'];
    } else {
        $date_prod = "0000-00-00";
    }

    $visa_supply = $_POST['VISA_Supply'];
    if (isset($_POST['DATE_Supply']) && $_POST['DATE_Supply'] != "") {
        $date_supply = $_POST['DATE_Supply'];
    } else {
        $date_supply = "0000-00-00";
    }

    $visa_pur1 = $_POST['VISA_PUR_1'];
    if (isset($_POST['DATE_PUR_1']) && $_POST['DATE_PUR_1'] != "") {
        $date_pur1 = $_POST['DATE_PUR_1'];
    } else {
        $date_pur1 = "0000-00-00";
    }

    $visa_pur2 = $_POST['VISA_PUR_2'];
    if (isset($_POST['DATE_PUR_2']) && $_POST['DATE_PUR_2'] != "") {
        $date_pur2 = $_POST['DATE_PUR_2'];
    } else {
        $date_pur2 = "0000-00-00";
    }

    $visa_pur3 = $_POST['VISA_PUR_3'];
    if (isset($_POST['DATE_PUR_3']) && $_POST['DATE_PUR_3'] != "") {
        $date_pur3 = $_POST['DATE_PUR_3'];
    } else {
        $date_pur3 = "0000-00-00";
    }

    $visa_pur4 = $_POST['VISA_PUR_4'];
    if (isset($_POST['DATE_PUR_4']) && $_POST['DATE_PUR_4'] != "") {
        $date_pur4 = $_POST['DATE_PUR_4'];
    } else {
        $date_pur4 = "0000-00-00";
    }

    $visa_pur5 = $_POST['VISA_PUR_5'];
    if (isset($_POST['DATE_PUR_5']) && $_POST['DATE_PUR_5'] != "") {
        $date_pur5 = $_POST['DATE_PUR_5'];
    } else {
        $date_pur5 = "0000-00-00";
    }

    $visa_gid = $_POST['VISA_GID'];
    if (isset($_POST['DATE_GID']) && $_POST['DATE_GID'] != "") {
        $date_gid = $_POST['DATE_GID'];
    } else {
        $date_gid = "0000-00-00";
    }

    $visa_gid_2 = $_POST['VISA_GID_2'];
    if (isset($_POST['DATE_GID_2']) && $_POST['DATE_GID_2'] != "") {
        $date_gid_2 = $_POST['DATE_GID_2'];
    } else {
        $date_gid_2 = "0000-00-00";
    }

    $visa_mof = $_POST['VISA_MOF'];
    if (isset($_POST['DATE_MOF']) && $_POST['DATE_MOF'] != "") {
        $date_mof = $_POST['DATE_MOF'];
    } else {
        $date_mof = "0000-00-00";
    }

    $visa_metro = $_POST['VISA_Metro'];
    if (isset($_POST['DATE_Metro']) && $_POST['DATE_Metro'] != "") {
        $date_metro = $_POST['DATE_Metro'];
    } else {
        $date_metro = "0000-00-00";
    }

    $visa_project = $_POST['VISA_Project'];
    if (isset($_POST['DATE_Project']) && $_POST['DATE_Project'] != "") {
        $date_project = $_POST['DATE_Project'];
    } else {
        $date_project = "0000-00-00";
    }

    $visa_labo = $_POST['VISA_LABO'];
    if (isset($_POST['DATE_LABO']) && $_POST['DATE_LABO'] != "") {
        $date_labo = $_POST['DATE_LABO'];
    } else {
        $date_labo = "0000-00-00";
    }

    $visa_routing_entry = $_POST['VISA_ROUTING_ENTRY'];
    if (isset($_POST['DATE_ROUTING_ENTRY']) && $_POST['DATE_ROUTING_ENTRY'] != "") {
        $date_routing_entry = $_POST['DATE_ROUTING_ENTRY'];
    } else {
        $date_routing_entry = "0000-00-00";
    }

    $prod_agent = $_POST['Prod_Agent'];
    $hts = $_POST['code_hts'];


    // RDO A AJOUTER
    $sql_2 = 'UPDATE db_release.tbl_released_drawing
				  SET 
					Rel_Pack_Num="' . $pack_num . '",
					Prod_Draw_rev="' . $prod_draw_rev . '",
					Reference="' . $reference . '",
					Ref_Rev="' . $ref_rev . '",
					Alias="' . $alias . '",
					Ref_Title="' . $ref_title . '",
					Action="' . $action . '",
					Doc_Type="' . $doc_type . '",
					Material_Type="' . $material_type . '",
					Unit="' . $unit . '",
					Inventory_Impact="' . $inventory_impact . '",
					Ex="' . $ex . '",
					Internal_Mach_Rec="' . $inhouse_manuf . '",
					Cust_Drawing="' . $cust_drawing . '",
					Cust_Drawing_Rev="' . $cust_drawing_rev . '",
					Weight="' . $weight . '",
					Weight_Unit="' . $weight_unit . '",
					Plating_Surface="' . $plating_surface . '",
					Plating_Surface_Unit="' . $plating_surface_unit . '",
					FXXX="' . $fxxx . '",
					Product_Code="' . $product_code . '",
					MOF="' . $mof . '",
					Requestor_Comments="' . $requestor_comments . '",
					Q_Inspection="' . $q_inspection . '",
					Q_Doc_Req="' . $q_doc_req . '",
					Q_Control_Routing="' . $q_control_routing . '",
					Q_Dynamization="' . $q_dynam . '",
					Pris_Dans1="' . $pris_dans1 . '",
					Pris_Dans2="' . $pris_dans2 . '",
					CLS="' . $cls . '",
					Proc_Type="' . $proc_type . '",
					RDO="' . $rdo . '",
					Metro_Time="' . $metro_time . '",
					Metro_Control="' . $metro_control . '",
					MOQ="' . $moq . '",
					leadtime="' . $leadtime . '",
					Mat_Prod_Type="' . $mat_prod_type . '",
					FIA="' . $fia . '",
					Purchasing_Group="' . $purchasing_group . '",
					Commodity_Code="' . $commodity . '",
					Supervisor="' . $supervisor . '",
					ECCN="' . $eccn . '",
					General_Comments="' . $v . '",
					VISA_Inventory="' . $visa_inventory . '",
					DATE_Inventory="' . $date_inventory . '",
					VISA_Product="' . $visa_product . '",
                    DATE_Product="' . $date_product . '",
                    VISA_Quality="' . $visa_quality . '",
                    DATE_Quality="' . $date_quality . '",
                    VISA_Method="' . $visa_method . '",
                    DATE_Method="' . $date_method . '",
                    VISA_Finance="' . $visa_finance . '",
                    DATE_Finance="' . $date_finance . '",
                    VISA_Prod="' . $visa_prod . '",
                    DATE_Prod="' . $date_prod . '",
                    VISA_Supply="' . $visa_supply . '",
                    DATE_Supply="' . $date_supply . '",
                    VISA_PUR_1="' . $visa_pur1 . '",
                    DATE_PUR_1="' . $date_pur1 . '",
                    VISA_PUR_2="' . $visa_pur2 . '",
                    DATE_PUR_2="' . $date_pur2 . '",
                    VISA_PUR_3="' . $visa_pur3 . '",
                    DATE_PUR_3="' . $date_pur3 . '",
                    VISA_PUR_4="' . $visa_pur4 . '",
                    DATE_PUR_4="' . $date_pur4 . '",
                    VISA_PUR_5="' . $visa_pur5 . '",
                    DATE_PUR_5="' . $date_pur5 . '",
                    VISA_GID="' . $visa_gid . '",
                    DATE_GID="' . $date_gid . '",
                    VISA_GID_2="' . $visa_gid_2 . '",
                    DATE_GID_2="' . $date_gid_2 . '",
                    VISA_MOF="' . $visa_mof . '",
                    DATE_MOF="' . $date_mof . '",
                    VISA_LABO="' . $visa_labo . '",
                    DATE_LABO="' . $date_labo . '",
                    VISA_ROUTING_ENTRY="' . $visa_routing_entry . '",
                    DATE_ROUTING_ENTRY="' . $date_routing_entry . '",
                    VISA_Metro="' . $visa_metro . '",
                    DATE_Metro="' . $date_metro . '",
                    VISA_Project="' . $visa_project . '",
                    DATE_Project="' . $date_project . '",
                    Prod_Agent="' . $prod_agent . '",
                    HTS="' . $hts . '"
				    WHERE ID like "' . $id . '";';

    $resultat = $mysqli->query($sql_2);
    mysqli_close($mysqli);

    $msg = "Data successfully updated !";
}


?>

<script>
    // !!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!
    // Rafraichissement de la page a la validation
    function reloadPage() {
        parent.window.location.href = 'REL_Admin_Draw_Content.php';
    }
</script>

<body>
    <form enctype="multipart/form-data" action="" method="post">

        <?php
        $query =   'SELECT *
				FROM tbl_released_package 
				LEFT JOIN tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE tbl_released_drawing.ID like "' . $_GET['ID'] . '"
				ORDER BY tbl_released_drawing.reference DESC';

        include('../REL_Connexion_DB.php');
        $resul_1 = $mysqli->query($query);
        while ($ligne = $resul_1->fetch_assoc()) {
            echo '<table id="t03" border=0>';
            echo '<tr>';
            echo '<th>Pack #</th>';
            echo '<td><input size="5" type="text" id="Rel_Pack_Num" name="Rel_Pack_Num" value="' . $ligne['Rel_Pack_Num'] . '"></td>';
            // ID recuperé pour envoi avec le formulaire - Non visible dans la page
            echo '<input type="text" size=2 name="ID_" style="height:3pt;width:3pt;" hidden readonly value="' . $_GET['ID'] . '">';

            echo '<th>FIA</th>';
            echo '<td style="min-width:150px"><input type="text" id="FIA" name="FIA" size="5" value="' . $ligne['FIA'] . '"></td>';



            echo '<th title="VISA and DATE of signoff">Inventory</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_Inventory" size="12" name="VISA_Inventory" value="' . htmlspecialchars($ligne['VISA_Inventory'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Inventory" name="DATE_Inventory" value="' . $ligne['DATE_Inventory'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Production Drawing</th>';
            echo '<td>';
            if ($ligne['Prod_Draw'] != "" && $ligne['Prod_Draw_Rev'] != "") {
                echo '<a href="https://app.aletiq.com/parts/preview/id/' . $ligne['Prod_Draw'] . '/revision/' . $ligne['Prod_Draw_Rev'] . '" target="_blank">' . $ligne['Prod_Draw'] . ' rev' . $ligne['Prod_Draw_Rev'] . '</a>';
            } else {
                echo $ligne['Prod_Draw']. ' rev' . $ligne['Prod_Draw_Rev'];
            }
            echo '<input type="text" style="text-align:center" id="Prod_Draw_Rev" size="1" name="Prod_Draw_Rev" value="' . $ligne['Prod_Draw_Rev'] . '">';
            echo '</td>';


            echo '<th>Buyer</th>';
            echo '<td>';
            echo '<select name="Purchasing_Group" id="Purchasing_Group" type="submit">';
            echo '<option value=""></option>';
            $requete_buyer = "SELECT buyer FROM tbl_buyer";
            $resultat_buyer = $mysqli->query($requete_buyer);
            $in_buyer = 0;
            while ($row_buyer = $resultat_buyer->fetch_assoc()) {
                $sel = "";
                if ($ligne['Purchasing_Group'] == $row_buyer['buyer']) {
                    $sel = "SELECTED";
                    $in_buyer = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_buyer['buyer'] . '">' . $row_buyer['buyer'] . '</option><br/>';
            }
            if ($in_buyer == 0 && $ligne['Purchasing_Group'] != "") {
                echo '<option SELECTED value="' . $ligne['Purchasing_Group'] . '">' . $ligne['Purchasing_Group'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';


            echo '<th title="VISA and DATE of signoff">Product</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_Product" size="12" name="VISA_Product" value="' . htmlspecialchars($ligne['VISA_Product'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Product" name="DATE_Product" value="' . $ligne['DATE_Product'] . '">';
            echo '</td>';

            echo '</tr>';

            echo '<tr>';
            echo '<th>Reference</th>';
            echo '<td>
				<input type="text" id="ref" name="ref" value="' . $ligne['Reference'] . '">
				<input type="text" size="1" style="text-align:center" id="ref_rev" name="ref_rev" placeholder="rev" value="' . $ligne['Ref_Rev'] . '">
			  </td>';

            echo '<th>Commodity Code</th>';
            echo '<td>';
            echo '<select name="Commodity_Code" id="Commodity_Code" type="submit">';
            echo '<option value=""></option>';
            $requete_commo = "SELECT Code FROM tbl_commodity_code";
            $resultat_commo = $mysqli->query($requete_commo);
            $in_commo = 0;
            while ($row_commo = $resultat_commo->fetch_assoc()) {
                $sel = "";
                if ($ligne['Commodity_Code'] == $row_commo['Code']) {
                    $sel = "SELECTED";
                    $in_commo = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_commo['Code'] . '">' . $row_commo['Code'] . '</option><br/>';
            }
            if ($in_commo == 0 && $ligne['Commodity_Code'] != "") {
                echo '<option SELECTED value="' . $ligne['Commodity_Code'] . '">' . $ligne['Commodity_Code'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';



            echo '<th title="VISA and DATE of signoff">Quality</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_Quality" size="12" name="VISA_Quality" value="' . htmlspecialchars($ligne['VISA_Quality'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Quality" name="DATE_Quality" value="' . $ligne['DATE_Quality'] . '">';
            echo '</td>';

            echo '</tr>';

            echo '<tr>';
            echo '<th>Alias</th>';
            echo '<td><input type="text" id="Alias" size="30" name="Alias" value="' . $ligne['Alias'] . '"></td>';

            echo '<th>Supervisor</th>';
            echo '<td>';
            echo '<select name="Supervisor" id="Supervisor" type="submit">';
            echo '<option value=""></option>';
            $requete_supervisor = "SELECT Code FROM tbl_supervisor";
            $resultat_supervisor = $mysqli->query($requete_supervisor);
            $in_supervisor = 0;
            while ($row_supervisor = $resultat_supervisor->fetch_assoc()) {
                $sel = "";
                if ($ligne['Supervisor'] == $row_supervisor['Code']) {
                    $sel = "SELECTED";
                    $in_supervisor = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_supervisor['Code'] . '">' . $row_supervisor['Code'] . '</option><br/>';
            }
            if ($in_supervisor == 0 && $ligne['Supervisor'] != "") {
                echo '<option SELECTED value="' . $ligne['Supervisor'] . '">' . $ligne['Supervisor'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';



            echo '<th>Method</th>';
            echo '<td title="VISA and DATE of signoff">';
            echo '<input type="text" id="VISA_Method" size="12" name="VISA_Method" value="' . htmlspecialchars($ligne['VISA_Method'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Method" name="DATE_Method" value="' . $ligne['DATE_Method'] . '">';
            echo '</td>';

            echo '</tr>';

            echo '<tr>';
            echo '<th>Title</th>';
            echo '<td><input type="text" id="Ref_Title" size="30" name="Ref_Title" value="' . $ligne['Ref_Title'] . '"></td>';

            if ($ligne['Pris_Dans1'] <> "") {
                $res = $ligne['Pris_Dans1'];
                $val = 'Value=';
                $sap_fxxx_search = str_replace(" ", "%", substr($res, 0, 10));
            } elseif ($ligne['FXXX'] <> "") {
                $res = $ligne['FXXX'];
                $val = 'Placeholder=';
                $sap_fxxx_search = str_replace(" ", "%", substr($res, 0, 9));
            } else {
                $res = " ";
                $val = "";
                $sap_fxxx_search = "";
            }
            echo '<th>Material Pris dans</th>';
            echo '<td>';
            //echo '<input class="input_datalist" list="Pris_Dans1" name="Pris_Dans1" multiple value="'. $ligne['Pris_Dans1'] .'" id="material" style="font-size:11;border:0.5px solid grey">';
            echo '<input list="Pris_Dans1_" name="Pris_Dans1_" id="Pris_Dans1_id_"  ' . $val . '"' . $res . '" title="" style="font-family:arial;font-size:11px;height:13px;width:140px">';
            echo '<datalist id="Pris_Dans1_">';

            include('../SCM_Connexion_DB.php');

            $requete_10 = 'SELECT DISTINCT Code 
											   FROM tbl_sap_fxxx 
											   WHERE Code like "%' . $sap_fxxx_search . '%"';

            $resultat_10 = $mysqli_scm->query($requete_10);
            while ($row_10 = $resultat_10->fetch_assoc()) {
                if ($ligne['Pris_Dans1'] == $row_10['Code']) {
                    $sel = "SELECTED";
                } else {
                    $sel = "";
                }
                echo '<option value ="' . $row_10['Code'] . '">' . $row_10['Code'] . '</option><br/>';
            }

            mysqli_close($mysqli_scm);
            echo '</datalist>';
            echo '</td>';



            echo '<th title="VISA and DATE of signoff">Finance</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_Finance" size="12" name="VISA_Finance" value="' . htmlspecialchars($ligne['VISA_Finance'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Finance" name="DATE_Finance" value="' . $ligne['DATE_Finance'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Action</th>';
            echo '<td>';
            echo '<select name="Action" id="Action" type="submit">';
            echo '<option value=""></option>';
            $requete_action = "SELECT Action FROM tbl_action";
            $resultat_action = $mysqli->query($requete_action);
            $in_action = 0;
            while ($row_action = $resultat_action->fetch_assoc()) {
                $sel = "";
                if ($ligne['Action'] == $row_action['Action']) {
                    $sel = "SELECTED";
                    $in_action = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_action['Action'] . '">' . $row_action['Action'] . '</option><br/>';
            }
            if ($in_action == 0 && $ligne['Action'] != "") {
                echo '<option SELECTED value="' . $ligne['Action'] . '">' . $ligne['Action'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';

            echo '<th>Quantity Pris Dans</th>';
            echo '<td><input type="text" id="Pris_Dans2" size="15" name="Pris_Dans2" value="' . $ligne['Pris_Dans2'] . '" style="height:13px"></td>';



            echo '<th>Prod</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_Prod" size="12" name="VISA_Prod" value="' . htmlspecialchars($ligne['VISA_Prod'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Prod" name="DATE_Prod" value="' . $ligne['DATE_Prod'] . '">';
            echo '</td>';

            echo '</tr>';


            echo '<tr>';
            echo '<th>Type</th>';
            echo '<td>';
            echo '<select name="Doc_Type" id="Doc_Type" type="submit" title="" style="font-size:9pt;height:17px;width:60px;background-color:transparent;border:1px solid grey">';
            echo '<option value=""></option>';

            $requete_doc = "SELECT Doc_Type FROM tbl_doc_type ";
            $resultat_doc = $mysqli->query($requete_doc);
            $in_doc = 0;
            while ($row_doc = $resultat_doc->fetch_assoc()) {
                $sel = "";
                if ($ligne['Doc_Type'] == $row_doc['Doc_Type']) {
                    $sel = "SELECTED";
                    $in_doc = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_doc['Doc_Type'] . '">' . $row_doc['Doc_Type'] . '</option><br/>';
            }
            if ($in_doc == 0 && $ligne['Doc_Type'] != "") {
                echo '<option SELECTED value="' . $ligne['Doc_Type'] . '">' . $ligne['Doc_Type'] . '</option><br/>';
            }
            echo '</select>';


            echo '<th title"Costing Lot Size / Minimum of Quantity">CLS / MOQ</th>';
            echo '<td><input type="text" size="1" id="CLS" name="CLS" value="' . $ligne['CLS'] . '">';

            echo '<input type="text" id="MOQ" size="1" name="MOQ" value="' . $ligne['MOQ'] . '"></td>';

            echo '<th title="VISA and DATE of signoff">Supply</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_Supply" size="12" name="VISA_Supply" value="' . htmlspecialchars($ligne['VISA_Supply'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Supply" name="DATE_Supply" value="' . $ligne['DATE_Supply'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Material Type</th>';
            echo '<td>';
            echo '<select name="Material_Type" id="Material_Type" type="submit" title="" style="font-size:9pt;height:17px;width:180px;background-color:transparent;border:1px solid grey">';
            echo '<option value=""></option>';
            $requete_mt = "SELECT Material_Type FROM tbl_material_type";
            $resultat_mt = $mysqli->query($requete_mt);
            $in_mt = 0;
            while ($row_mt = $resultat_mt->fetch_assoc()) {
                $sel = "";
                if ($ligne['Material_Type'] == $row_mt['Material_Type']) {
                    $sel = "SELECTED";
                    $in_mt = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_mt['Material_Type'] . '">' . $row_mt['Material_Type'] . '</option><br/>';
            }
            if ($in_mt == 0 && $ligne['Material_Type'] != "") {
                echo '<option SELECTED value="' . $ligne['Material_Type'] . '">' . $ligne['Material_Type'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';

            echo '<th>Product Code</th>';
            echo '<td>';
            echo '<select name="Product_Code" id="Product_Code" type="submit" title="" style="text-align:center;font-size:9pt;height:17px;width:110px;background-color:transparent;border:1px solid grey">';
            echo '<option value=""></option>';

            include('../SCM_Connexion_DB.php');

            $requete_pc = "SELECT Code FROM tbl_product_code";
            $resultat_pc = $mysqli_scm->query($requete_pc);
            $in_pc = 0;
            while ($row_pc = $resultat_pc->fetch_assoc()) {
                $sel = "";
                if ($ligne['Product_Code'] == $row_pc['Code']) {
                    $sel = "SELECTED";
                    $in_pc = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_pc['Code'] . '">' . $row_pc['Code'] . '</option><br/>';
            }
            if ($in_pc == 0 && $ligne['Product_Code'] != "") {
                echo '<option SELECTED value="' . $ligne['Product_Code'] . '">' . $ligne['Product_Code'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';


            echo '<th title="VISA and DATE of signoff">RFQ</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_PUR_1" size="12" name="VISA_PUR_1" value="' . htmlspecialchars($ligne['VISA_PUR_1'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_PUR_1" name="DATE_PUR_1" value="' . $ligne['DATE_PUR_1'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Unit</th>';
            echo '<td>';
            echo '<select name="Unit" id="Unit" type="submit">';
            echo '<option value=""></option>';
            include('../SCM_Connexion_DB.php');
            $requete_unit = "SELECT Unit FROM tbl_unit";
            $resultat_unit = $mysqli_scm->query($requete_unit);
            $in_unit = 0;
            while ($row_unit = $resultat_unit->fetch_assoc()) {
                $sel = "";
                if ($ligne['Unit'] == $row_unit['Unit']) {
                    $sel = "SELECTED";
                    $in_unit = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_unit['Unit'] . '">' . $row_unit['Unit'] . '</option><br/>';
            }
            if ($in_unit == 0 && $ligne['Unit'] != "") {
                echo '<option SELECTED value="' . $ligne['Unit'] . '">' . $ligne['Unit'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';

            echo '<th>Metro Time | Control</th>';
            echo '<td><input type="text" id="Metro_Time" size="3" name="Metro_Time" value="' . $ligne['Metro_Time'] . '"> | ';

            $metro_control_db = explode(";", $ligne['Metro_Control']);
            $query_2 = 'SELECT *
                    FROM tbl_metro';
            $resultat_2 = $mysqli->query($query_2);
            while ($ligne_2 = $resultat_2->fetch_assoc()) {
                $ck = "";
                foreach ($metro_control_db as $metro_control) {
                    if ($metro_control == $ligne_2['Control']) {
                        $ck = "checked";
                    }
                }
                echo '<input style="margin-left:-0px;vertical-align:middle" type="checkbox" 
						 id="Control_Metro' . $ligne_2['Control'] . '" 
						 name="Control_Metro_' . $ligne_2['Control'] . '" 
						 class="control_metro"
						 Value="' . $ligne_2['Control'] . '" 
						 Title="' . $ligne_2['Description'] . '"
						 ' . $ck . '>';
                echo '<label   for="Control_' . $ligne_2['Control'] . '">' . $ligne_2['Control'] . '</label>&nbsp&nbsp';
            }
            echo '</td>';

            echo '<th title="Pris dans VISA and sign off for external sourcing">Pris Dans</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_PUR_2" size="12" name="VISA_PUR_2" value="' . htmlspecialchars($ligne['VISA_PUR_2'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_PUR_2" name="DATE_PUR_2" value="' . $ligne['DATE_PUR_2'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Inventory Impact</th>';
            echo '<td>';
            echo '<select name="Inventory_Impact" id="Inventory_Impact" type="submit">';
            echo '<option value=""></option>';

            $requete_inventory = "SELECT Inventory_Impact FROM tbl_inventory_impact";
            $resultat_inventory = $mysqli->query($requete_inventory);
            $in_inventory = 0;
            while ($row_inventory = $resultat_inventory->fetch_assoc()) {
                $sel = "";
                if ($ligne['Inventory_Impact'] == $row_inventory['Inventory_Impact']) {
                    $sel = "SELECTED";
                    $in_inventory = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_inventory['Inventory_Impact'] . '">' . $row_inventory['Inventory_Impact'] . '</option><br/>';
            }
            if ($in_inventory == 0 && $ligne['Inventory_Impact'] != "") {
                echo '<option SELECTED value="' . $ligne['Inventory_Impact'] . '">' . $ligne['Inventory_Impact'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';

            echo '<th>RDO</th>';
            echo '<td>';
            echo '<select name="RDO" id="RDO" type="submit">';
            echo '<option value=""></option>';
            include('../SCM_Connexion_DB.php');
            $requete_rdo = "SELECT RDO, Description FROM tbl_rdo";
            $result_rdo = $mysqli_scm->query($requete_rdo);
            $in_rdo = 0;
            while ($row_rdo = $result_rdo->fetch_assoc()) {
                $sel = "";
                if ($ligne['RDO'] == $row_rdo['RDO']) {
                    $sel = "SELECTED";
                    $in_rdo = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_rdo['RDO'] . '">' . $row_rdo['RDO'] . ' - ' . $row_rdo['Description'] . '</option><br/>';
            }
            if ($in_rdo == 0 && $ligne['RDO'] != "") {
                echo '<option SELECTED value="' . $ligne['RDO'] . '">' . $ligne['RDO'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';

            echo '<th title="VISA and DATE of signoff">FIA</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_PUR_3" size="12" name="VISA_PUR_3" value="' . htmlspecialchars($ligne['VISA_PUR_3'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_PUR_3" name="DATE_PUR_3" value="' . $ligne['DATE_PUR_3'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Ex</th>';
            echo '<td>';
            echo '<select name="Ex" id="Ex" type="submit">';
            echo '<option value=""></option>';

            include('../SCM_Connexion_DB.php');

            $requete_ex = "SELECT Ex FROM tbl_ex";
            $resultat_ex = $mysqli_scm->query($requete_ex);
            $in_ex = 0;
            while ($row_ex = $resultat_ex->fetch_assoc()) {
                $sel = "";
                if ($ligne['Ex'] == $row_ex['Ex']) {
                    $sel = "SELECTED";
                    $in_ex = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_ex['Ex'] . '">' . $row_ex['Ex'] . '</option><br/>';
            }
            if ($in_ex == 0 && $ligne['Ex'] != "") {
                echo '<option SELECTED value="' . $ligne['Ex'] . '">' . $ligne['Ex'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';

            echo '<th>Proc Type</th>';
            echo '<td>';
            echo '<select name="Proc_Type" id="Proc_Type" type="submit" title="" style="font-size:9pt;height:17px;width:60px;background-color:transparent;border:1px solid grey">';
            echo '<option value=""></option>';
            $requete_proc = "SELECT Supply_Type FROM tbl_proc_type";
            $resultat_proc = $mysqli->query($requete_proc);
            $in_proc = 0;
            while ($row_proc = $resultat_proc->fetch_assoc()) {
                $sel = "";
                if ($ligne['Proc_Type'] == $row_proc['Supply_Type']) {
                    $sel = "SELECTED";
                    $in_proc = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_proc['Supply_Type'] . '">' . $row_proc['Supply_Type'] . '</option><br/>';
            }
            if ($in_proc == 0 && $ligne['Proc_Type'] != "") {
                echo '<option SELECTED value="' . $ligne['Proc_Type'] . '">' . $ligne['Proc_Type'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';


            echo '<th title="RoHS/REACH data from SUBCONTRACTORS">REACH</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_PUR_4" size="12" name="VISA_PUR_4" value="' . htmlspecialchars($ligne['VISA_PUR_4'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_PUR_4" name="DATE_PUR_4" value="' . $ligne['DATE_PUR_4'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Customer Drawing</th>';
            echo '<td><input type="text" id="Cust_Drawing" name="Cust_Drawing" value="' . $ligne['Cust_Drawing'] . '">';
            echo '<input type="text" id="Cust_Drawing_Rev" size="1" name="Cust_Drawing_Rev" value="' . $ligne['Cust_Drawing_Rev'] . '">';
            echo '</td>';

            echo '<th>Leadtime</th>';
            echo '<td><input type="text" id="leadtime" size="1" name="leadtime" value="' . $ligne['leadtime'] . '"></td>';


            echo '<th title="VISA and DATE of signoff for HTS and ORIGIN from SUBCONTRACTORS">Origin</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_PUR_5" size="12" name="VISA_PUR_5" value="' . htmlspecialchars($ligne['VISA_PUR_5'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_PUR_5" name="DATE_PUR_5" value="' . $ligne['DATE_PUR_5'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Weight</th>';
            echo '<td><input type="text" id="Weight" size="1" name="Weight" value="' . $ligne['Weight'] . '">';

            echo '<select name="Weight_Unit" id="Weight_Unit" type="submit">';
            echo '<option value=""></option>';

            include('../SCM_Connexion_DB.php');

            $requete_wu = "SELECT Unit FROM tbl_unit WHERE Unit_Type LIKE 'Weight'";
            $resultat_wu = $mysqli_scm->query($requete_wu);
            $in_wu = 0;
            while ($row_wu = $resultat_wu->fetch_assoc()) {
                $sel = "";
                if ($ligne['Weight_Unit'] == $row_wu['Unit']) {
                    $sel = "SELECTED";
                    $in_wu = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_wu['Unit'] . '">' . $row_wu['Unit'] . '</option><br/>';
            }
            if ($in_wu == 0 && $ligne['Weight_Unit'] != "") {
                echo '<option SELECTED value="' . $ligne['Weight_Unit'] . '">' . $ligne['Weight_Unit'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';

            echo '<th>Material Prod Type</th>';
            echo '<td>';
            echo '<select name="Mat_Prod_Type" id="Mat_Prod_Type" type="submit">';
            echo '<option value=""></option>';

            $requete_sap = "SELECT SAP_Type FROM tbl_sap_type";
            $resultat_sap = $mysqli->query($requete_sap);
            $in_sap = 0;
            while ($row_sap = $resultat_sap->fetch_assoc()) {
                $sel = "";
                if ($ligne['Mat_Prod_Type'] == $row_sap['SAP_Type']) {
                    $sel = "SELECTED";
                    $in_sap = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_sap['SAP_Type'] . '">' . $row_sap['SAP_Type'] . '</option><br/>';
            }
            if ($in_sap == 0 && $ligne['Mat_Prod_Type'] != "") {
                echo '<option SELECTED value="' . $ligne['Mat_Prod_Type'] . '">' . $ligne['Mat_Prod_Type'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';


            echo '<th title="VISA and DATE signoff for SAP CORE DATA">CORE Data</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_GID" size="12" name="VISA_GID" value="' . htmlspecialchars($ligne['VISA_GID'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_GID" name="DATE_GID" value="' . $ligne['DATE_GID'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Plating Surface</th>';
            echo '<td><input type="text" id="Plating_Surface" size="1" name="Plating_Surface" value="' . $ligne['Plating_Surface'] . '">';
            echo '<select name="Plating_Surface_Unit" id="Plating_Surface_Unit" type="submit">';
            echo '<option value=""></option>';

            include('../SCM_Connexion_DB.php');

            $requete_psu = "SELECT Unit FROM tbl_unit WHERE Unit_Type LIKE 'Surface'";
            $resultat_psu = $mysqli_scm->query($requete_psu);
            $in_psu = 0;
            while ($row_psu = $resultat_psu->fetch_assoc()) {
                $sel = "";
                if ($ligne['Plating_Surface_Unit'] == $row_psu['Unit']) {
                    $sel = "SELECTED";
                    $in_psu = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_psu['Unit'] . '">' . $row_psu['Unit'] . '</option><br/>';
            }
            if ($in_psu == 0 && $ligne['Plating_Surface_Unit'] != "") {
                echo '<option SELECTED value="' . $ligne['Plating_Surface_Unit'] . '">' . $ligne['Plating_Surface_Unit'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';

            echo '<th>Production Agent</th>';
            echo '<td>';
            echo '<select name="Prod_Agent" id="Prod_Agent" type="submit">';
            echo '<option value=""></option>';

            $requete_agent = "SELECT scheduling_agent FROM tbl_prod_agent";
            $resultat_agent = $mysqli->query($requete_agent);
            $in_agent = 0;
            while ($row_agent = $resultat_agent->fetch_assoc()) {
                $sel = "";
                if ($ligne['Prod_Agent'] == $row_agent['scheduling_agent']) {
                    $sel = "SELECTED";
                    $in_agent = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_agent['scheduling_agent'] . '">' . $row_agent['scheduling_agent'] . '</option><br/>';
            }
            if ($in_agent == 0 && $ligne['Prod_Agent'] != "") {
                echo '<option SELECTED value="' . $ligne['Prod_Agent'] . '">' . $ligne['Prod_Agent'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';


            echo '<th title="VISA and DATE signoff for SAP PROD DATA">PROD Date</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_GID_2" size="12" name="VISA_GID_2" value="' . htmlspecialchars($ligne['VISA_GID_2'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_GID_2" name="DATE_GID_2" value="' . $ligne['DATE_GID_2'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>Material</th>';
            echo '<td>';
            echo '<input class="input_datalist" list="material" name="material" multiple value="' . $ligne['FXXX'] . '" id="fxxx" style="font-size:11;border:0.5px solid grey">';
            echo '<datalist id="material">';
            include('../SCM_Connexion_DB.php');

            $requete_fxxx = "SELECT DISTINCT fxxx_ref, fxxx_description FROM tbl_fxxx WHERE Status like 'ACTIVE' ORDER BY fxxx_ref DESC;";
            $resultat_fxxx = $mysqli_scm->query($requete_fxxx);
            $in_fxxx = 0;
            while ($row_fxxx = $resultat_fxxx->fetch_assoc()) {
                $sel = "";
                if ($ligne['FXXX'] == $row_fxxx['fxxx_ref']) {
                    $sel = "SELECTED";
                    $in_fxxx = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_fxxx['fxxx_ref'] . '">' . $row_fxxx['fxxx_description'] . '</option><br/>';
            }
            if ($in_fxxx == 0 && $ligne['FXXX'] != "") {
                echo '<option value="' . $ligne['FXXX'] . '">' . $ligne['FXXX'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</datalist>';
            echo '</td>';

            echo '<th title="Exportation / Custom control cote">HTS</th>';
            echo '<td>';
            echo '<input class="input_datalist" list="code_hts" name="code_hts" multiple value="' . $ligne['HTS'] . '" id="hts" title="" style="border:0.5px solid grey;width:100px;font-size:11;">';
            echo '<datalist id="code_hts">';
            include('../SCM_Connexion_DB.php');
            $requete_hts = "SELECT DISTINCT HTS, Description FROM tbl_hts";
            $resultat_hts = $mysqli_scm->query($requete_hts);
            $in_hts = 0;
            while ($row_hts = $resultat_hts->fetch_assoc()) {
                $sel = "";
                if ($ligne['HTS'] == $row_hts['HTS']) {
                    $sel = "SELECTED";
                    $in_hts = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_hts['HTS'] . '">' . $row_hts['Description'] . '</option><br/>';
            }
            if ($in_hts == 0 && $ligne['HTS'] != "") {
                echo '<option value="' . $ligne['HTS'] . '">' . $ligne['HTS'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</datalist>';
            echo '</td>';


            echo '<th>MOF</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_MOF" size="12" name="VISA_MOF" value="' . htmlspecialchars($ligne['VISA_MOF'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_MOF" name="DATE_MOF" value="' . $ligne['DATE_MOF'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th title="Engineering department wish to keep the component manufacturing internally to SCM">Internal Manuf. Pref.</th>';
            echo '<td>';
            if ($ligne['Internal_Mach_Rec'] == 1) {
                echo '<input type="checkbox" id="Internal_Mach_Rec" name="Internal_Mach_Rec" checked>';
            } else {
                echo '<input type="checkbox" id="Internal_Mach_Rec" name="Internal_Mach_Rec">';
            }
            echo '</td>';

            echo '<th>Dynamization Rule</th>';
            echo '<td>';
            echo '<select name="Q_Dynamization" id="Q_Dynamization" type="submit">';
            echo '<option value=""></option>';
            $requete_dynam = "SELECT Code FROM tbl_q_dynamisation_rules";
            $resultat_dynam = $mysqli->query($requete_dynam);
            $in_dynam = 0;
            while ($row_dynam = $resultat_dynam->fetch_assoc()) {
                $sel = "";
                if ($ligne['Q_Dynamization'] == $row_dynam['Code']) {
                    $sel = "SELECTED";
                    $in_dynam = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_dynam['Code'] . '">' . $row_dynam['Code'] . '</option><br/>';
            }
            if ($in_dynam == 0 && $ligne['Q_Dynamization'] != "") {
                echo '<option SELECTED value="' . $ligne['Q_Dynamization'] . '">' . $ligne['Q_Dynamization'] . '</option><br/>';
            }

            echo '</select>';
            echo '</td>';

            echo '<th>Laboratory</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_LABO" size="12" name="VISA_LABO" value="' . htmlspecialchars($ligne['VISA_LABO'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_LABO" name="DATE_LABO" value="' . $ligne['DATE_LABO'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';

            echo '<th title="Export Control Code">ECCN</th>';
            echo '<td>';
            echo '<select name="ECCN" id="ECCN" type="submit" title="" style="width:80px;">';
            echo '<option value=""></option>';

            include('../SCM_Connexion_DB.php');

            $requete_eccn = "SELECT ECCN FROM tbl_eccn";
            $resultat_eccn = $mysqli_scm->query($requete_eccn);
            $in_eccn = 0;
            while ($row_eccn = $resultat_eccn->fetch_assoc()) {
                $sel = "";
                if ($ligne['ECCN'] == $row_eccn['ECCN']) {
                    $sel = "SELECTED";
                    $in_eccn = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_eccn['ECCN'] . '">' . $row_eccn['ECCN'] . '</option><br/>';
            }
            if ($in_eccn == 0 && $ligne['ECCN'] != "") {
                echo '<option SELECTED value="' . $ligne['ECCN'] . '">' . $ligne['ECCN'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';

            $q_inspection_db = explode(";", $ligne['Q_Inspection']);
            echo '<th>Inspection Code</th>';
            echo '<td>';
            $query_2 = 'SELECT *
                    FROM tbl_q_inspection_type 
                    ORDER BY Code ASC';
            $resultat_2 = $mysqli->query($query_2);
            while ($ligne_2 = $resultat_2->fetch_assoc()) {
                $ck = "";

                foreach ($q_inspection_db as $q_ins) {
                    if ($q_ins == $ligne_2['Code']) {
                        $ck = "checked";
                    }
                }
                echo '<input style="background-color:transparent;margin-left:-0px;vertical-align:middle" type="checkbox" 
                                        id="inspection_' . $ligne_2['Code'] . '" 
                                        name="inspection_' . $ligne_2['Code'] . '" 
                                        Value="' . $ligne_2['Code'] . '" 
                                        class="inspection"
                                        Title="' . $ligne_2['Description'] . '"
                                        ' . $ck . '>';
                echo '<label   for="inspection_' . $ligne_2['Code'] . '">' . $ligne_2['Code'] . '</label>&nbsp';
            }
            echo '</td>';

            echo '<th>Routing Entry</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_ROUTING_ENTRY" size="12" name="VISA_ROUTING_ENTRY" value="' . htmlspecialchars($ligne['VISA_ROUTING_ENTRY'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_ROUTING_ENTRY" name="DATE_ROUTING_ENTRY" value="' . $ligne['DATE_ROUTING_ENTRY'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th>MOF</th>';
            echo '<td><input type="text" id="MOF" name="MOF" value="' . $ligne['MOF'] . '"></td></td>';

            echo '<th>Control Routing</th>';
            echo '<td>';
            echo '<select name="Q_Control_Routing" id="Q_Control_Routing" type="submit">';
            echo '<option value=""></option>';

            $requete_control_rout = "SELECT Code FROM tbl_q_control_routing";
            $resultat_control_rout = $mysqli->query($requete_control_rout);
            $in_control_rout = 0;
            while ($row_control_rout = $resultat_control_rout->fetch_assoc()) {
                $sel = "";
                if ($ligne['Q_Control_Routing'] == $row_control_rout['Code']) {
                    $sel = "SELECTED";
                    $in_control_rout = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_control_rout['Code'] . '">' . $row_control_rout['Code'] . '</option><br/>';
            }
            if ($in_control_rout == 0 && $ligne['Q_Control_Routing'] != "") {
                echo '<option SELECTED value="' . $ligne['Q_Control_Routing'] . '">' . $ligne['Q_Control_Routing'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';

            echo '<th>Metro</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_Metro" size="12" name="VISA_Metro" value="' . htmlspecialchars($ligne['VISA_Metro'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Metro" name="DATE_Metro" value="' . $ligne['DATE_Metro'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td></td>';
            echo '<td></td>';
            echo '<td></td>';
            echo '<td></td>'; 
            echo '<th>Project</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_Project" size="12" name="VISA_Project" value="' . htmlspecialchars($ligne['VISA_Project'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_Project" name="DATE_Project" value="' . $ligne['DATE_Project'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th colspan=2>Requestor Comments</th>';


            $q_doc_req_db = explode(";", $ligne['Q_Doc_Req']);
            echo '<th colspan=2 style="padding-top:5px;">Documentation Requirements</th>';
            echo '<th colspan=2>General Comments</th>';


            echo '</tr>';

            echo '<tr>';
            echo '<td colspan=2 style="vertical-align:top;">';
            echo '<textarea id="Requestor_Comments" name="Requestor_Comments" rows="6" cols="50">' . $ligne['Requestor_Comments'] . '</textarea>';
            echo '</td>';


            echo '<td colspan=2 style="vertical-align:top;margin-top:5px;max-width:200px;">';
            $query_2 = 'SELECT *
                    FROM tbl_q_doc_requirements 
                    ORDER BY Code ASC';
            $resultat_2 = $mysqli->query($query_2);
            while ($ligne_2 = $resultat_2->fetch_assoc()) {
                $ck = "";
                foreach ($q_doc_req_db as $doc_req) {
                    if ($doc_req == $ligne_2['Code']) {
                        $ck = "checked";
                    }
                }
                echo '<div id="checkbox_block"><input style="background-color:transparent;vertical-align:middle" type="checkbox" 
                                                     id="doc_req_' . $ligne_2['Code'] . '" 
                                                     name="doc_req_' . $ligne_2['Code'] . '"
                                                     Value="' . $ligne_2['Code'] . '" 
                                                     Class="doc_req"
                                                     ' . $ck . '
                                                     Title="' . $ligne_2['Description'] . '">';
                echo '<label style="margin-left:-4px;" for="doc_req_' . $ligne_2['Code'] . '">  ' . substr($ligne_2['Code'], -3) . '</label></div>';
            }

            echo '</td>';
            echo '<td colspan=2 rowspan=3 style="vertical-align:top;">';
            echo '<textarea id="Comments" name="Comments" rows="6" cols="40">' . $ligne['General_Comments'] . '</textarea>';
            echo '</td>';

            echo '</tr>';
            echo '<tr>';


            echo '</tr>';
            echo '<tr>';
            echo '<td>';
            echo '<input class="btn orange" name="btn_update" onclick="reloadPage()" id="btn_update" type="submit" value="Update">';
            echo '</td>';

            echo '</tr>';




            echo '</table>';
        }
        mysqli_close($mysqli);
        ?>
    </form>
</body>

</html>