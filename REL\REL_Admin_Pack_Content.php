<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" type="text/css" href="REL_Admin_Pack_Draw_styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
    <title></title>

    <script>
        function frame_update(obj) {
            cel_row_data = 0;
            document.getElementById("main_frame").innerHTML = '<iframe name="Main_target" id="Main_target" class="main_frame" alt="ok" src="REL_Admin_Pack_Item.php?ID=' + obj.cells[cel_row_data].textContent + '" frameborder="0" scrollbar="yes"></iframe>';

            // !!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!
            // Mise en lumière de la ligne cliquée
            var rows = document.getElementsByTagName('tr');

            for (var i = 0; i < rows.length; i++) {
                if (rows[i] != obj) {
                    rows[i].setAttribute('class', "unpicked_line");
                } else {
                    obj.setAttribute('class', "picked_line");
                }
            }
        }
    </script>

</head>

<body>
    <form enctype="multipart/form-data" action="" method="post">
        <table border="0" id="t01">
            <tr>
                <td colspan="2">
                    <div id="Main_Title">
                        Release Package Modification Form
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan="2">
                    <table style="width:100%">
                        <tr>
                            <td>
                                <div id="FilterTitle">
                                    Pack #
                                    <input list="Rel_Pack_Num_Choice_datalist" name="Rel_Pack_Num_Choice" id="Rel_Pack_Num_Choice" value="<?php if(isset($_POST['Rel_Pack_Num_Choice'])){echo $_POST['Rel_Pack_Num_Choice'] ;}?>" style="font-size:9pt;width:60px;height:13px" onchange="this.form.submit()">
									<datalist id="Rel_Pack_Num_Choice_datalist">
									<!-- <SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;" onchange="this.form.submit()"> -->
                                        <!--<option value="%"></option>-->
                                        <?php
                                        include('../REL_Connexion_DB.php');
                                        $query_1 = 'SELECT DISTINCT Rel_Pack_Num 
									FROM tbl_released_package
									ORDER BY Rel_Pack_Num DESC';
                                        $resultat_1 = $mysqli->query($query_1);
                                        while ($row = $resultat_1->fetch_assoc()) {
                                            $sel = "";
                                            if (isset($_POST['Rel_Pack_Num_Choice'])) {
                                                if ($_POST['Rel_Pack_Num_Choice'] == $row['Rel_Pack_Num']) {
                                                    $sel = "SELECTED";
                                                }
                                            }
                                            if ($row['Rel_Pack_Num'] != "") {
                                                echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '"' . $sel . '>' . $row['Rel_Pack_Num'] . '</option><br/>';
                                            }
                                        }
                                        mysqli_close($mysqli);
                                        ?>
                                    <!--</SELECT>-->
									</datalist>
                                </div>
                            </td>

                            <td>
                                <div id="FilterTitle">
                                    Project
                                    <!--<SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
                                        <OPTION value="%"></OPTION>-->
									<input list="Project_Choice_datalist" name="Project_Choice" value="<?php if(isset($_POST['Project_Choice'])){echo $_POST['Project_Choice'] ;}?>" style="font-size:9pt;width:60px;height:13px" onchange="this.form.submit()">
									<datalist id="Project_Choice_datalist">
                                        <?php
                                        include('../REL_Connexion_DB.php');
                                        $query_2 = 'SELECT DISTINCT Project 
													FROM tbl_released_package
													ORDER BY tbl_released_package.Project DESC';
                                        $resultat_2 = $mysqli->query($query_2);
                                        while ($row = $resultat_2->fetch_assoc()) {
                                            $sel = "";
                                            if (isset($_POST['Project_Choice'])) {
                                                if ($_POST['Project_Choice'] == $row['Project']) {
                                                    $sel = "SELECTED";
                                                }
                                            }
                                            if ($row['Project'] != "") {
                                                echo '<OPTION value ="' . $row['Project'] . '"' . $sel . '>' . $row['Project'] . '</option><br/>';
                                            }
                                        }
                                        mysqli_close($mysqli);
                                        ?>
                                    <!--</SELECT>-->
									</datalist>
                                </div>
                            </td>

                            <td>
                                <div id="FilterTitle">
                                    Activity
                                    <SELECT name="Activity_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
                                        <option value="%"></option>
                                        <?php
                                        include('../REL_Connexion_DB.php');
                                        $query_3 = 'SELECT DISTINCT Activity
                                FROM tbl_released_package
                                ORDER BY Activity ASC';
                                        $resultat_3 = $mysqli->query($query_3);
                                        while ($row = $resultat_3->fetch_assoc()) {
                                            $sel = "";
                                            if (isset($_POST['Activity_Choice'])) {
                                                if ($_POST['Activity_Choice'] == $row['Activity']) {
                                                    $sel = "SELECTED";
                                                }
                                            }
                                            if ($row['Activity'] != "") {
                                                echo '<OPTION value ="' . $row['Activity'] . '"' . $sel . '>' . $row['Activity'] . '</option><br/>';
                                            }
                                        }
                                        mysqli_close($mysqli);
                                        ?>
                                    </SELECT>
                                </div>
                            </td>

                            <td>
                                <div id="FilterTitle">
                                    Package Owner
                                    <SELECT name="Rel_Pack_Owner_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
                                        <option value="%"></option>
                                        <?php
                                        include('../REL_Connexion_DB.php');
                                        $query_4 = 'SELECT DISTINCT Rel_Pack_Owner 
                                FROM tbl_released_package
                                ORDER BY Rel_Pack_Owner ASC';
                                        $resultat_4 = $mysqli->query($query_4);
                                        while ($row = $resultat_4->fetch_assoc()) {
                                            $sel = "";
                                            if (isset($_POST['Rel_Pack_Owner_Choice'])) {
                                                if ($_POST['Rel_Pack_Owner_Choice'] == $row['Rel_Pack_Owner']) {
                                                    $sel = "SELECTED";
                                                }
                                            }
                                            if ($row['Rel_Pack_Owner'] != "") {
                                                echo '<OPTION value ="' . $row['Rel_Pack_Owner'] . '"' . $sel . '>' . $row['Rel_Pack_Owner'] . '</option><br/>';
                                            }
                                        }
                                        mysqli_close($mysqli);
                                        ?>
                                    </SELECT>
                                </div>
                            </td>

                            <td>
                                <input type="button" class="btn grey" onclick="window.location.href = 'REL_Admin_Pack_Content.php';" style="font-size:8pt; width:45px;height:18px;vertical-align:middle;text-align:center" value="Reset" />
                            </td>
                        </tr>
                    </table>

                    <!--- Vérification des valeurs --->
                    <?php

                    if (isset($_POST['Rel_Pack_Num_Choice']) == false || $_POST['Rel_Pack_Num_Choice']=="") {
                        $rel_pack_num_choice = "%";
                    } else {
                        $rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
                    }

                    if (isset($_POST['Project_Choice']) == false || $_POST['Project_Choice']=="" ) {
                        $project_choice = "%";
                    } else {
                        $project_choice = $_POST['Project_Choice'];
                    }

                    if (isset($_POST['Activity_Choice']) == false) {
                        $activity_choice = "%";
                    } else {
                        $activity_choice = $_POST['Activity_Choice'];
                    }

                    if (isset($_POST['Rel_Pack_Owner_Choice']) == false) {
                        $rel_pack_owner_choice = "%";
                    } else {
                        $rel_pack_owner_choice = $_POST['Rel_Pack_Owner_Choice'];
                    }

                    include('../REL_Connexion_DB.php');

                    $requete_5 = 'SELECT *
                    FROM tbl_released_package
                    WHERE tbl_released_package.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                        AND tbl_released_package.Project like "' . $project_choice . '"
                        AND tbl_released_package.Activity like "' . $activity_choice . '"
                        AND tbl_released_package.Rel_Pack_Owner like "' . $rel_pack_owner_choice . '";';


                    $resultat_5 = $mysqli->query($requete_5);
                    $rowcount = mysqli_num_rows($resultat_5);

                    echo '<tr><td colspan="2"><div id="Result_info">Number of results: ' . $rowcount . '&nbsp&nbsp&nbsp';
                    echo '</td></tr>';

                    echo '<tr style="height:calc(90vh - 4 * 30px)">';
                    echo '<td style="vertical-align:top;width:40%;">';
                    echo '<table id="t02">';
                    echo '<th>Package #</th>';
                    echo '<th>Project</th>';
                    echo '<th>Activity</th>';
                    echo '<th>Owner</th>';

                    while ($row_5 = $resultat_5->fetch_assoc()) {
                        echo '<tr onclick="frame_update(this)">';
                        echo '<td hidden>';
                        echo $row_5['ID'];
                        echo '</td>';
                        echo '<td>';
                        echo $row_5['Rel_Pack_Num'];
                        echo '</td>';
                        echo '<td>';
                        echo $row_5['Project'];
                        echo '</td>';
                        echo '<td>';
                        echo $row_5['Activity'];
                        echo '</td>';
                        echo '<td>';
                        echo $row_5['Rel_Pack_Owner'];
                        echo '</td>';
                        echo '</tr>';
                    }
                    echo '</table>';
                    echo '</td>';
                    echo '<td style="vertical-align:top;">';
                    echo '<p id="main_frame">';
                    echo '<iframe name="Main_target" id="Main_target" class="main_frame" alt="ok" src="" frameborder="0" style="text-align:center;"></iframe>';
                    echo '</p>';
                    echo '</td>';
                    echo '</tr>';
                    ?>
        </table>
    </form>
</body>

</html>