<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="utf-8" />

<link rel="stylesheet" type="text/css" href="REL_Admin_Pack_Draw_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<head>

    <title></title>

</head>

<?php
// MISE A JOUR DES DONNES ASSOCIEES AU NUMERO DE PACKAGE AVEC LES INFO FOURNIES PAR L'UTILISATEUR
$msg = "";
if (isset($_POST['btn_update'])) {
    include('../REL_Connexion_DB.php');
    $id = $_POST['ID_'];
    $pack_num = $_POST['Rel_Pack_Num'];
    $rel_pack_owner = $_POST['Owner'];
    $activity = $_POST['Activity'];
    $ex = $_POST['Ex'];
    $dmo = $_POST['DMO'];
    $project = $_POST['Project'];
    $req_owner = $_POST['BE_3_Owner'];
    $verif_owner = $_POST['Verif_Owner'];

    if (isset($_POST['Reserv_date']) && $_POST['Reserv_date'] != "") {
        $res_date = $_POST['Reserv_date'];
    } else {
        $res_date = "0000-00-00";
    }

    if (isset($_POST['Crea_date']) && $_POST['Crea_date'] != "") {
        $crea_date = $_POST['Crea_date'];
    } else {
        $crea_date = "0000-00-00";
    }

    $crea_visa = $_POST['Crea_visa'];

    $visa_be_2 = $_POST['VISA_BE_2'];
    if (isset($_POST['DATE_BE_2']) && $_POST['DATE_BE_2'] != "") {
        $date_be_2 = $_POST['DATE_BE_2'];
    } else {
        $date_be_2 = "0000-00-00";
    }

    $visa_be_3 = $_POST['VISA_BE_3'];
    if (isset($_POST['DATE_BE_3']) && $_POST['DATE_BE_3']) {
        $date_be_3 = $_POST['DATE_BE_3'];
    } else {
        $date_be_3 = "0000-00-00";
    }

    $observation = $_POST['observation'];


    // RDO A AJOUTER
    $sql_2 = 'UPDATE tbl_released_package
				  SET 
					Rel_Pack_Num="' . $pack_num . '",
					Rel_Pack_Owner="' . $rel_pack_owner . '",
					Activity="' . $activity . '",
					Ex="' . $ex . '",
					DMO="' . $dmo . '",
					Project="' . $project . '",
					BE_3_Req_Owner="' . $req_owner . '",
					Verif_Req_Owner="' . $verif_owner . '",
					Reservation_Date="' . $res_date . '",
					Creation_Date="' . $crea_date . '",
					Creation_VISA="' . $crea_visa . '",
					VISA_BE_2="' . $visa_be_2 . '",
					DATE_BE_2="' . $date_be_2 . '",
					VISA_BE_3="' . $visa_be_3 . '",
					DATE_BE_3="' . $date_be_3 . '",
					Observations="' . $observation . '"
				    WHERE ID like "' . $id . '";';
    //print_r($sql_2);
    $resultat = $mysqli->query($sql_2);
    mysqli_close($mysqli);

    $msg = "Data successfully updated !";
}

?>

<script>
    // !!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!
    // Rafraichissement de la page a la validation
    function reloadPage() {
        parent.window.location.href = 'REL_Admin_Pack_Content.php';
    }
</script>

<body>
    <form enctype="multipart/form-data" action="" method="post">

        <?php
        $query = 'SELECT *
			    FROM tbl_released_package
				WHERE tbl_released_package.ID like "' . $_GET['ID'] . '"';

        include('../REL_Connexion_DB.php');
        $resul_1 = $mysqli->query($query);
        while ($ligne = $resul_1->fetch_assoc()) {
            echo '<table id="t03" border=0>';
            echo '<tr>';
            echo '<th>Pack #</th>';
            echo '<td><input type="text" id="Rel_Pack_Num" name="Rel_Pack_Num" value="' . $ligne['Rel_Pack_Num'] . '"></td>';
            // ID recuperé pour envoi avec le formulaire - Non visible dans la page
            echo '<input type="text" size=2 name="ID_" style="height:3pt;width:3pt;" hidden readonly value="' . $_GET['ID'] . '">';

            echo '<th title="Person who made the reservation of the package number">Package Owner</th>';
            echo '<td>';
            echo '<select name="Owner" id="Owner" type="submit">';
            echo '<option value=""></option>';
            include('../SCM_Connexion_DB.php');

            $requete_owner = "SELECT Fullname FROM tbl_user";
            $resultat_owner = $mysqli_scm->query($requete_owner);
            $in_owner = 0;
            while ($row_owner = $resultat_owner->fetch_assoc()) {
                $sel = "";
                if ($ligne['Rel_Pack_Owner'] == $row_owner['Fullname']) {
                    $sel = "SELECTED";
                    $in_owner = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_owner['Fullname'] . '">' . $row_owner['Fullname'] . '</option><br/>';
            }
            if ($in_owner == 0 && $ligne['Rel_Pack_Owner'] != "") {
                echo '<option SELECTED value="' . $ligne['Rel_Pack_Owner'] . '">' . $ligne['Rel_Pack_Owner'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';



            echo '</tr>';

            echo '<tr>';
            echo '<th>Activity</th>';
            echo '<td>';
            echo '<select name="Activity" id="Activity" type="submit">';
            echo '<option value=""></option>';
            $requete_activity = "SELECT Activity FROM tbl_activity";
            $resultat_activity = $mysqli->query($requete_activity);
            $in_activity = 0;
            while ($row_activity = $resultat_activity->fetch_assoc()) {
                $sel = "";
                if ($ligne['Activity'] == $row_activity['Activity']) {
                    $sel = "SELECTED";
                    $in_activity = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_activity['Activity'] . '">' . $row_activity['Activity'] . '</option><br/>';
            }
            if ($in_activity == 0 && $ligne['Activity'] != "") {
                echo '<option SELECTED value="' . $ligne['Activity'] . '">' . $ligne['Activity'] . '</option><br/>';
            }
            echo '</select>';
            echo '</td>';

            echo '<th title="Date when the engineering owner made a reservation of the package number.">Reservation Date</th>';
            echo '<td>';
            echo '<input type="date" class="input_date" id="Reserv_date" name="Reserv_date" value="' . $ligne['Reservation_Date'] . '">';
            echo '</td>';



            echo '</tr>';

            echo '<tr>';
            echo '<th>DMO</th>';
            echo '<td>';
            echo '<select name="DMO" id="DMO" type="submit">';
            echo '<option value=""></option>';
            include('../DMO_Connexion_DB.php');
            $requete_dmo = "SELECT DISTINCT DMO FROM tbl_dmo";
            $resultat_dmo = $mysqli_dmo->query($requete_dmo);
            $in_dmo = 0;
            while ($row_dmo = $resultat_dmo->fetch_assoc()) {
                $sel = "";
                if ($ligne['DMO'] == $row_dmo['DMO']) {
                    $sel = "SELECTED";
                    $in_dmo = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_dmo['DMO'] . '">' . $row_dmo['DMO'] . '</option><br/>';
            }
            if ($in_dmo == 0 && $ligne['DMO'] != "") {
                echo '<option SELECTED value="' . $ligne['DMO'] . '">' . $ligne['DMO'] . '</option><br/>';
            }
            mysqli_close($mysqli_dmo);
            echo '</select>';
            echo '</td>';

            echo '<th title="Date and name of the persone who sent the package to engineering verification step - Most probably the package owner">Engineering Start</th>';
            echo '<td>';
            echo '<input type="text" id="Crea_visa" size="12" name="Crea_visa" value="' . htmlspecialchars($ligne['Creation_VISA'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="Crea_date" name="Crea_date" value="' . $ligne['Creation_Date'] . '">';
            echo '</td>';



            echo '</tr>';

            echo '<tr>';
            echo '<th>Ex</th>';
            echo '<td>';
            echo '<select name="Ex" id="Ex" type="submit">';
            echo '<option value=""></option>';

            include('../SCM_Connexion_DB.php');

            $requete_ex = "SELECT Ex FROM tbl_ex";
            $resultat_ex = $mysqli_scm->query($requete_ex);
            $in_ex = 0;
            while ($row_ex = $resultat_ex->fetch_assoc()) {
                $sel = "";
                if ($ligne['Ex'] == $row_ex['Ex']) {
                    $sel = "SELECTED";
                    $in_ex = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_ex['Ex'] . '">' . $row_ex['Ex'] . '</option><br/>';
            }
            if ($in_ex == 0 && $ligne['Ex'] != "") {
                echo '<option SELECTED value="' . $ligne['Ex'] . '">' . $ligne['Ex'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';


            echo '<th title="Person designated as the verification owner by the one started the engineering review (engineering start VISA)">Verification Owner</th>';
            echo '<td>';
            echo '<select name="Verif_Owner" id="Verif_Owner" type="submit">';
            echo '<option value=""></option>';
            include('../SCM_Connexion_DB.php');

            $requete_verif = "SELECT Fullname FROM tbl_user";
            $resultat_verif = $mysqli_scm->query($requete_verif);
            $in_verif = 0;
            while ($row_verif = $resultat_verif->fetch_assoc()) {
                $sel = "";
                if ($ligne['Verif_Req_Owner'] == $row_verif['Fullname']) {
                    $sel = "SELECTED";
                    $in_verif = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_verif['Fullname'] . '">' . $row_verif['Fullname'] . '</option><br/>';
            }
            if ($in_verif == 0 && $ligne['Verif_Req_Owner'] != "") {
                echo '<option SELECTED value="' . $ligne['Verif_Req_Owner'] . '">' . $ligne['Verif_Req_Owner'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';



            echo '</tr>';

            echo '<tr>';
            echo '<th>Project</th>';
            echo '<td>';
            echo '<select name="Project" id="Project" type="submit">';
            echo '<option value=""></option>';

            include('../SCM_Connexion_DB.php');

            $requete_project = "SELECT OTP FROM tbl_project";
            $resultat_project = $mysqli_scm->query($requete_project);
            $in_project = 0;
            while ($row_project = $resultat_project->fetch_assoc()) {
                $sel = "";
                if ($ligne['Project'] == $row_project['OTP']) {
                    $sel = "SELECTED";
                    $in_project = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_project['OTP'] . '">' . $row_project['OTP'] . '</option><br/>';
            }
            if ($in_project == 0 && $ligne['Project'] != "") {
                echo '<option SELECTED value="' . $ligne['Project'] . '">' . $ligne['Project'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';

            echo '<th title="VISA and DATE of signoff for Verification Date (2<sup>nd</sup> step of the engineering review)">Engineering Verification</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_BE_2" size="12" name="VISA_BE_2" value="' . htmlspecialchars($ligne['VISA_BE_2'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_BE_2" name="DATE_BE_2" value="' . $ligne['DATE_BE_2'] . '">';
            echo '</td>';

            echo '</tr>';

            echo '<tr>';

            echo '</tr>';


            echo '</tr>';

            echo '<tr>';

            echo '</tr>';

            echo '<tr>';
            echo '<th rowspan=3>Observations</th>';
            echo '<td rowspan=3>';
            echo '<textarea id="observation" name="observation" rows="5" cols="33">' . $ligne['Observations'] . '</textarea>';
            echo '</td>';

            echo '<th title="Person designated as the validation owner by the one whoe verified the pachage (engineering verification VISA) ">Validation Owner</th>';
            echo '<td>';
            echo '<select name="BE_3_Owner" id="BE_3_Owner" type="submit">';
            echo '<option value=""></option>';
            include('../SCM_Connexion_DB.php');

            $requete_req_owner = "SELECT Fullname FROM tbl_user";
            $resultat_req_owner = $mysqli_scm->query($requete_req_owner);
            $in_req_owner = 0;
            while ($row_req_owner = $resultat_req_owner->fetch_assoc()) {
                $sel = "";
                if ($ligne['BE_3_Req_Owner'] == $row_req_owner['Fullname']) {
                    $sel = "SELECTED";
                    $in_req_owner = 1;
                }
                echo '<option ' . $sel . ' value="' . $row_req_owner['Fullname'] . '">' . $row_req_owner['Fullname'] . '</option><br/>';
            }
            if ($in_req_owner == 0 && $ligne['BE_3_Req_Owner'] != "") {
                echo '<option SELECTED value="' . $ligne['BE_3_Req_Owner'] . '">' . $ligne['BE_3_Req_Owner'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '</select>';
            echo '</td>';
            echo '</tr>';
            echo '<tr>';
            echo '<th title="VISA and DATE of signoff for Validation Date (Last step of the engineering review)">Engineering Validation</th>';
            echo '<td>';
            echo '<input type="text" id="VISA_BE_3" size="12" name="VISA_BE_3" value="' . htmlspecialchars($ligne['VISA_BE_3'], ENT_QUOTES) . '">';
            echo '<input type="date" class="input_date" id="DATE_BE_3" name="DATE_BE_3" value="' . $ligne['DATE_BE_3'] . '">';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td style="text-align:right;padding-right:calc(25% - 100px)" colspan=2>';
            echo '<input class="btn orange" name="btn_update" onclick="reloadPage()" id="btn_update" type="submit" value="Update" style="width:100px">';
            echo '</td>';

            echo '</tr>';
            echo '</table>';
        }
        mysqli_close($mysqli);
        ?>
    </form>
</body>

</html>