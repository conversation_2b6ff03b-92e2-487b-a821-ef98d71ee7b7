<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html>
<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_Admin_style.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

    <!--<script src="\Common_Resources\clipboard.min.js"></script>-->

    <script>

        // RECUPERATION DES VALEURS CHOISIES PAR L'UTILISATEUR POUR MODIFICATION/SUPPRESSION
        function frame_update(obj)
        {
            // RECUPERATION DE LA PHASE (CODE) ET DU TITRE ASSOCIE CHOISI PAR L'UTILISATEUR
            //window.parent.document.getElementById("picked_phase").value=obj.cells[2].textContent.trim();
            //const picked_phase_val=document.querySelector('input[name="Picked_user"]:checked').value;
            document.getElementById("Key_User").value=obj.cells[1].textContent.trim();
            document.getElementById("SCM_ID").value=obj.cells[2].textContent.trim();
            document.getElementById("Fullname").value=obj.cells[3].textContent.trim();
            document.getElementById("email").value=obj.cells[4].textContent.trim();
            document.getElementById("Department").value=obj.cells[5].textContent.trim();


            // SELECTION DU BOUTON RADIO ASSOCIE A LA LIGNE DU TABLEAU CHOISI
            const indx="Radio_Picked_User_" + obj.cells[1].textContent.trim();
            document.querySelector('input[name="Picked_User"]:checked');
            document.getElementById(indx).checked = true;

        }


        // copie des adresses emails dans le presse papier
        function copy_to_clipboard()
        {

            var table_user_count = document.getElementById("t04").rows.length;
            var table_user = document.getElementById("t04");
            var i=1;
            var email_list="";

            do
            {
                if(email_list=="")
                {
                    email_list= table_user.rows[i].cells[4].textContent.trim() + ";";
                } else {
                    email_list= email_list + table_user.rows[i].cells[4].textContent.trim() + ";";
                }

                i=i+1;
            } while (i<=(table_user_count-1))



            //Create a dummy input to copy the string array inside it
            var dummy = document.createElement("input");

            //Add it to the document
            document.body.appendChild(dummy);

            //Set its ID
            dummy.setAttribute("id", "dummy_id");

            //Output the array into it
            document.getElementById("dummy_id").value=email_list;

            //Select it
            dummy.select();

            //Copy its contents
            document.execCommand("copy");

            //Remove it as its not needed anymore
            document.body.removeChild(dummy);


            alert("Liste des emails disponibles dans votre presse-papier !");
        }

        function verif_user(){
            button_create = document.getElementById("Create_user_id");
            Identifiant = document.getElementById("SCM_ID").value;
            Name = document.getElementById("Fullname").value;
            Email = document.getElementById("email").value;
            Department = document.getElementById("Department").value;

            if(Identifiant == "" || Name == "" || Email == "" || Departement == ""){
                alert("veuillez remplir tous les champs avec les *");
            }
        }

    </script>
</head>

<!----------------------------->
<!-- SUPPRESSION UTILISATEUR -->
<!----------------------------->
<?php

$msg_conf="";

if (isset ($_POST['Delete_user']) && isset($_POST['Key_User']) && ($_POST['Key_User'])>0)
{
    //Préparation des inputs de l'utilisateur
    $id=$_POST['Key_User'];
    $fullname=$_POST['Fullname'];

    //Connexion à la Base
    include('../SCM_Connexion_DB.php');

    //Préparation de la requete SQL
    $sql = 'DELETE FROM tbl_user WHERE Key_User = "'.$id.'";';

    // Query execution
    $result = $mysqli_scm->query($sql);
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> Utilisateur '.$fullname.' supprimé.</br>';

}

?>

<!-------------------------->
<!-- CREATION UTILISATEUR -->
<!-------------------------->
<?php
if (isset($_POST['Create_user']) && (($_POST['Key_User'])=="") &&  ($_POST['SCM_ID'] != "") && ($_POST['Fullname'] != "") && ($_POST['email'] != "") && ($_POST['Department'] != ""))
{

    //On récupère les valeurs entrées par l'utilisateur :
    $SCM_ID=$_POST['SCM_ID'];
    $Fullname=$_POST['Fullname'];
    $Department=$_POST['Department'];
    $email=$_POST['email'];

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    $user_numbering = 'SELECT MAX(Key_User) from db_scm.tbl_user';
    $user_max_ID_tmp = $mysqli_scm->query($user_numbering);
    $user_max_ID = mysqli_fetch_row($user_max_ID_tmp);
    $user_max = intval($user_max_ID[0]) + 1;

    

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO db_scm.tbl_user VALUES ("'.$user_max.'","'.$SCM_ID.'","'.$Fullname.'","'.$email.'", "'.$Department.'", 0)';
    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> Nouvel utilisateur '.$Fullname.' créé!</br>';

}
?>


<!------------------------>
<!-- UPDATE UTILISATEUR -->
<!------------------------>
<?php

if (isset($_POST['Key_User']) && isset($_POST['Update_user']) && isset ($_POST['SCM_ID']) && ($_POST['SCM_ID'])!="" && isset($_POST['Fullname']) && ($_POST['Fullname'])!="" && (isset($_POST['email']))  && ($_POST['email'])!="")

{
    if ($_POST['Key_User']<>"")
    {

        //Connexion à BD
        include('../SCM_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        // Get user input values and prepare them for SQL query
        $ID=$_POST['Key_User'];
        $SCM_ID=$_POST['SCM_ID'];
        $Fullname=$_POST['Fullname'];
        $Department=$_POST['Department'];
        $email=$_POST['email'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'UPDATE tbl_user
			  SET 
				TE_ID = "'.$SCM_ID.'",
				Fullname = "'.$Fullname.'",
				email = "'.$email.'",
				Department = "'.$Department.'"
			  WHERE Key_User like "'.$ID.'";';

        $resultat = $mysqli_scm->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli_scm);

        // Message confirmation
        $msg_conf='</br> Utilisateur '.$Fullname.' mis à jour !</br>';

    }
}
?>

<!---------------------------------------->
<!-- CREATION DEPARTEMENT DANS BASE SCM -->
<!---------------------------------------->
<?php
if ((isset($_POST['Department_Update']) && ($_POST['department_input'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $department=$_POST['department_input'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_department
			  VALUES ("0","'.$department.'");';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> Nouveau departement '.$department.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION DEPARTEMENT DANS BASE SCM -->
<!------------------------------------------->
<?php
if ((isset($_POST['Department_Delete']) && ($_POST['department_input'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $department=$_POST['department_input'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_department WHERE Department = "'.$department.'";';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> Departement '.$department.' supprimé !</br>';

}
?>


<body>

<form name="name_form" method="post" action="" enctype="multipart/form-data">

    <table border=0>
        <tr>
            <td>
                <table border="0">
                    <tr>
                        <td colspan=3>
                            <div id="body" style="font-weight:bold">
                                Departement
                            </div>
                        </td>
                        <td>
                            <div id="FilterTitle_User" style="font-style:italic">
                                <?php if (isset($_POST['Department_Update']) || isset($_POST['Department_Delete'])){echo $msg_conf;}?>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td colspan=4>
                            <div id="FilterTitle_User" >
                                Création d'un nouveau departement ou suppression d'un département existant. ATTENTION: Ce changement sera effectif pour l'outil de DMO également:
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td colspan=2 style="vertical-align:middle">
                            <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                                Départements <font color=red> *</font>:
                            </div>
                        </td>
                        <td colspan=3 style="vertical-align:middle">
                            <div id="InpBox_User">
                                <input list="department" name="department_input" style="font-size:12px;background-color:white;width:150px">
                                <datalist name="" id="department">
                                    <?php
                                    include('../SCM_Connexion_DB.php');
                                    $requete_dep = 'SELECT Department
                                  FROM tbl_department
                                  ORDER BY department ASC';
                                    $resu = $mysqli_scm->query($requete_dep);
                                    while ($row = $resu->fetch_assoc())
                                    {
                                        echo'<option value ="'.$row['Department'].'">'.$row['Department'].'</option><br/>';
                                    }
                                    $mysqli_scm->close();
                                    ?>
                                </datalist>

                                <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="Department_Update" value="Créer" title="Validation de la création d'un nouveau département"/>
                                <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="Department_Delete" value="Supprimer" title="Suppression du département sélectionné"/>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td colspan=7>
                            <hr>
                        </td>
                    </tr>
        <tr>
            <td colspan=3 style="font-family:Arial">
                <div id="Body">
                    <b>Gestion Utilisateurs </b>
                </div>
            </td>

        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle">
                    Remplissez les champs pour créer un nouvel utilisateur, ou mettez à jour les informations de l'utilisateur que vous avez sélectionnées:
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="FilterTitle_User">
                    Identifiant SCM<font color=red> *</font>:
                </div>
            </td>
            <td colspan=2>
                <div id="InpBox_User">
                    <input type="text" style="font-size:12px;height:20px" size=16 name="SCM_ID" id="SCM_ID" placeholder="XXXXX" title="SCM ID - Mandatory">
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="FilterTitle_User">
                    Nom<font color=red> *</font>:
                </div>
            </td>
            <td colspan=2>
                <div id="InpBox_User">
                    <input type="text" style="font-size:12px;height:20px" size=16 name="Fullname" id="Fullname" placeholder="DUPONT M." title="Nom de famille en majuscule, espace, premiere lettre du prenom en majuscule, point - Mandatory">
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <div id="FilterTitle_User">
                    Adresse Email<font color=red> *</font>:
                </div>
            </td>
            <td colspan=2>
                <div id="InpBox_User">
                    <input type="email" style="font-size:12px;height:20px" size=26 name="email" id="email" title="full email address - Mandatory" placeholder="<EMAIL>">
                </div>
            </td>

        </tr>
        <tr>
            <td>
                <div id="FilterTitle_User">
                    Departement<font color=red> *</font>:
                </div>
            </td>
            <td colspan=4>
                <div id="InpBox_User">
                    <select type="submit" style="font-size:12px;width:130px" name="Department"  id="Department" title="Département de rattachement de l'utilisateur - Mandatory">
                        <option value=""></option>
                        <!--LISTE DEROULANTE DYNAMIQUE-->
                        <!------------------------------>
                        <?php
                        include('../SCM_Connexion_DB.php');
                        $requete = "SELECT DISTINCT Department FROM tbl_department ORDER BY Department ASC;";
                        $resultat = $mysqli_scm->query($requete);
                        while ($row = $resultat->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Department'].'">'.$row['Department'].'</option><br/>';
                        }
                        mysqli_close($mysqli_scm);
                        ?>
                        <!------------------------------>
                    </select>
                </div>
            </td>
        </tr>

        <tr hidden>
            <td>
                <div id="InpBox_User">
                    <input type="text" style="font-size:12px" size=26 name="Key_User" id="Key_User" title="ID in the database of the given user" >
                </div>
            </td>
        </tr>

        <tr>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset ($_POST['Delete_user']) || isset($_POST['Create_user']) || isset($_POST['Update_user'])) { echo $msg_conf; }?>
                </div>
            </td>
            <td>
                <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" onclick="verif_user()" id="Create_user_id" name="Create_user" value="Créer" title="Créer le nouvel utilsateur avec les informations renseignées."/>
                <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue" name="Update_user" value="Mise à jour" title="Mets à jour les informations renseignées dans les champs"/>
                <input type="submit" class="btn orange" name="Email_Copy" value="Copie emails" style="text-align:center;vertical-align:middle;width:80px;height:20px" title="Copie tous les emails des utilisateurs dans le presse papier" onclick="copy_to_clipboard()"/>
                <input type="submit" class="btn red" name="Delete_user" value="Supprimer" style="text-align:center;vertical-align:middle;width:80px;height:20px" title="Supprime l'utilisateur sélectionné dans le tableau ci-dessous"/>
            </td>
        </tr>
                </table>
            </td>
        </tr>
    </table>

</form>


<!--------------------------------->
<!-- AFFICHAGE TABLE UTILISATEUR -->
<!--------------------------------->
<table id="t04" border="1" style="margin-left:20px; margin-top: 20px; max-width:95%;vertical-align:center">

    <tr>
        <th style="width:3%">
            O
        </th>
        <th  style="width:3%">
            ID
        </th>
        <th style="width:15%">
            SCM ID
        </th>
        <th style="width:16%">
            Nom
        </th>
        <th>
            Email
        </th>
        <th style="width:14%">
            Departement
        </th>
    </tr>

    <?php

    include('../SCM_Connexion_DB.php');
    $requete_auto = 'SELECT *, tbl_user.Key_User as K_USER
						 FROM tbl_user
						 ORDER BY Fullname ASC;
						';

    $resultat = $mysqli_scm->query($requete_auto);
    $rowcount=mysqli_num_rows($resultat);

    while ($row = $resultat->fetch_assoc())
    {

        echo '
			<tr onclick="frame_update(this)">
				<td>
					<input type="radio" style="vertical-align:middle" id="Radio_Picked_User_'.$row['K_USER'].'" name="Picked_user" value="'.$row['K_USER'].'">
				</td>
				<td>
					'.$row['K_USER'].'
				</td>
				<td>
					'.$row['TE_ID'].'
				</td>
				<td>
					'.$row['Fullname'].'
				</td>
				<td>
					'.$row['Email'].'
				</td>
				<td>
					'.$row['Department'].'
				</td>
			</tr>';
    }

    $mysqli_scm->close();

    ?>

</table>

</body>
</html>
