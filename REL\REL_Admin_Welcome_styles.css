body {
    background-color: transparent;
    color: black;
    font-size: 9.5pt;
    font-family: Tahoma, sans-serif;
    font-weight: normal;
}

div.format {
    /*background-color: lightgrey;*/
}

@media screen and (min-width: 1370px) {
    div.format {
        font-size:9pt;
    }
}

@media screen and (max-width: 1369px) {
    div.format {
        font-size:8pt;
    }
}

div#Title {
    font-family:Arial;
    margin-left:5px;
    margin-top:5px;
    text-align:left;
    margin-bottom:6px;
    margin-left:10px;
    vertical-align:middle;
    font-weight: Bold;
    font-size: 12pt;
}

div#box_title
{
    text-align:center;
    vertical-align:middle;
    font-family:Tahoma;
    font-weight: Bold;
    color:white;
}

div#box_title:hover
{
    cursor:pointer;
    background-color:#F4F4F4;
    color:black;
}

div#Body {

    text-indent:10px;
    margin-top:5px;
}


#Filter {
    text-indent: 15px;
    margin-left: 2px;
    margin-right: 2px;
    text-align: center;
    vertical-align: middle;
}

#FilterTitle {
    margin-left: 2px;
    margin-right: 2px;
    text-align: center;
    vertical-align: middle;
    background: transparent;
}

#confirmation_message {
    font-weight: bold;
    font-family: Tahoma, sans-serif;
    text-align: center;
    margin-top: 25px;
}

div#Result_info {
    text-indent: 10px;
    margin-left: 8px;
    margin-bottom: 8px;
    text-align: justify;
}

#t01 {
    width: 100%;
    border-collapse: collapse;
    vertical-align: middle;
}

#t01 th {
    border: 1px solid black;
    background-color: rgb(27, 79, 114);
    color: white;
}

#t01 td {
    text-align: left;
    vertical-align: middle;
}

#t01 tr {
    height: 20px;
}

#t02 {
    border-collapse: collapse;
    table-layout: fixed;
    width:100%;
    z-index:1;
}

#t02 th {
    border: 0.5px solid black;
    background-color: rgb(27, 79, 114);
    color: white;
    font-family: Arial, Helvetica, sans-serif;
    text-align: center;
}

#t02 td {
    text-align: left;
    vertical-align: middle;

}
#t02 tr {
    height: 20px;
}


div#Table_results {
    text-align: center;
    color: black;
}

#t03 {

}

#t03 tr {
    height:40px;

}

#t03 td {
    width:100px;
    text-align: center;
    vertical-align: middle;
    border-right: 2px solid white;
    border-left: 2px solid white;
    border-top: 2px solid white;
    border-bottom: 2px solid white;
    border-radius:8px;
}
