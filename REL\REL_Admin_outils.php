<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <link rel="stylesheet" href="REL_Admin_style.css" type="text/css" />
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
    <title>Page Principale</title>
</head>

<body>

    <!------------------------->
    <!-- UPDATE MOT DE PASSE -->
    <!------------------------->
    <?php

    if (isset($_POST['pwd_update']) && isset($_POST['pwd_input']) && ($_POST['pwd_input']) != "") {

        // Get user input values and prepare them for SQL query
        $pwd = $_POST['pwd_input'];

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        // Query preparation
        $sql_1 = 'UPDATE tbl_parameters
			  SET 
				Value="' . $pwd . '"
			  WHERE Parameter like "Admin_Password";';


        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Mot de passe modifiée !</br>';
    }
    ?>

    <!---------------------------------->
    <!--   BACKUP DES BASE DE DONNEES -->
    <!---------------------------------->
    <?php
    $msg_db = "";
    if (isset($_POST['Backup_start'])) {

        if (isset($_POST['db_scm_picked']) && $_POST['db_scm_picked'] == true) {
            backup_bat("db_scm", "");
        }

        if (isset($_POST['db_release_picked']) && $_POST['db_release_picked'] == true) {
            backup_bat("db_release", "");
        }
    }
    ?>

    <?php
    function backup_bat($db, $msg_db)
    {

        //Extraction du dossier de stocage des sauvegarde
        include('../REL_Connexion_DB.php');
        $sql_1 = "SELECT DISTINCT Value from tbl_parameters where Parameter like 'Backup_Folder'";
        $resultat = $mysqli->query($sql_1);
        while ($row = $resultat->fetch_assoc()) {
            $folder_name = $row['Value'];
        }
        mysqli_close($mysqli);
        //-----

        $user_myadmin = '"root"';
        $pass = '"Lemans72!"';

        $param = array(
            $folder_name,
            $user_myadmin,
            $pass,
            $db,
        );

        $param_list = $param[0] . ' ' . $param[1] . ' ' . $param[2] . ' ' . $param[3];
        $bat_to_backup = '"backupdb.bat" ' . $param_list;
        echo exec($bat_to_backup);
        // if ($msg_db == "") {
        //     $msg_db = 'backup OK !';
        // } else {
        //     $msg_db = $msg_db . ' - ' . $db . ' backup OK !';
        // }
    }
    ?>

    <form name="name_form" method="post" action="" enctype="multipart/form-data">
        <table border=0 style="width:95%">

            <tr>
                <td colspan=4 style="font-weight:bold">
                    <div id="FilterTitle">
                        <b>Paramètres</b>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User">
                        Choisir les bases de données à sauvegarder:
                    </div>
                </td>

                <td colspan=2>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['Backup_start'])) {
                            echo "Backup OK !";
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td style="text-indent:55px;font-size:11px">
                    <div id="InpBox">
                        <input type="checkbox" id="db_release" name="db_release_picked" value="db_release" checked>
                        <label for="db_release">db_release</label>
                    </div>
                </td>

                <td rowspan=2 colspan=3 style="text-indent:55px;font-size:11px; text-align:left">
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue" name="Backup_start" value="Backup" title="sauvegarde des bases de données de l'outil" />
                    les sauvegardes sont disponibles dans le dossier <i><a href="VIEW_BACKUP.php" target="#">/Release/Backup</a></i>
                </td>
            </tr>

            <tr>
                <td style="text-indent:55px;font-size:11px">
                    <div id="InpBox">
                        <input type="checkbox" id="db_scm" name="db_scm_picked" value="db_scm" checked>
                        <label for="db_scm">db_scm</label>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        Mot de passe Administration
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['pwd_update'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Changer le mot de passe pour accèder à la page Administration de l'outil:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Mot de passe <font color=red> *</font>:
                    </div>
                </td>
                <?php
                include('../REL_Connexion_DB.php');
                $requete_mdp = 'SELECT Value
                          FROM tbl_parameters
                          WHERE Parameter like "Admin_Password"';
                $res = $mysqli->query($requete_mdp);
                while ($row_mdp = $res->fetch_assoc()) {
                    $pwd = $row_mdp['Value'];
                }
                $mysqli->close();
                ?>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input type="text" style="font-size:10pt;height:15px" size=15 name="pwd_input" id="pwd_input" title="Nouveau mot de passe" placeholder="<?php echo $pwd ?>">

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue" name="pwd_update" value="Mise à jour" title="validation du changement de mot de passe Administrateur" />
                    </div>
                </td>
            </tr>

        </table>
    </form>


</body>

</html>