<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!doctype html>
<html lang="fr">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <link rel="stylesheet" href="REL_Admin_style.css" type="text/css" />
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

    <title>Page Principale</title>
</head>

<body >

    <script>
        function Buyer_Info_Auto_Update() {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                document.getElementById("BUYER_Description").value = this.responseText.trim();
            }
            xhttp.open("GET", "REL_Admin_Auto.php?Code=" + document.getElementById("buyer_select").value + "&root=buyer");
            xhttp.send();
        }

        function Commodity_Info_Auto_Update() {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                document.getElementById("COMMODITY_Description").value = this.responseText.trim();
            }
            xhttp.open("GET", "REL_Admin_Auto.php?Code=" + document.getElementById("commodity_select").value + "&root=commo");
            xhttp.send();
        }

        function Supervisor_Info_Auto_Update() {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                document.getElementById("SUPERVISOR_Description").value = this.responseText.trim();
            }
            xhttp.open("GET", "REL_Admin_Auto.php?Code=" + document.getElementById("supervisor_select").value + "&root=sup");
            xhttp.send();
        }

        function HTS_Info_Auto_Update() {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                document.getElementById("HTS_Description").value = this.responseText.trim();
            }
            xhttp.open("GET", "REL_Admin_Auto.php?HTS=" + document.getElementById("hts_select").value + "&root=hts");
            xhttp.send();
        }

        function DOC_TYPE_Info_Auto_Update() {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                document.getElementById("DOC_TYPE_Description").value = this.responseText.trim();
            }
            xhttp.open("GET", "REL_Admin_Auto.php?Doc_Type=" + document.getElementById("doc_type_select").value + "&root=doc_type");
            xhttp.send();
        }

        function SAP_TYPE_Info_Auto_Update() {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                document.getElementById("SAP_TYPE_Description").value = this.responseText.trim();
            }
            xhttp.open("GET", "REL_Admin_Auto.php?SAP_Type=" + document.getElementById("sap_type_select").value + "&root=sap_type");
            xhttp.send();
        }

        function MATERIAL_TYPE_Info_Auto_Update() {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                document.getElementById("MATERIAL_TYPE_Description").value = this.responseText.trim();
            }
            xhttp.open("GET", "REL_Admin_Auto.php?Material_Type=" + document.getElementById("material_type_select").value + "&root=material_type");
            xhttp.send();
        }

        function INVENTORY_Info_Auto_Update() {
            const xhttp = new XMLHttpRequest();
            xhttp.onload = function() {
                document.getElementById("Inventory_Description").value = this.responseText.trim();
            }
            xhttp.open("GET", "REL_Admin_Auto.php?Inventory_Impact=" + document.getElementById("inventory_select").value + "&root=invent");
            xhttp.send();
        }
    </script>

    <!---------------------------------------->
    <!-- CREATION BUYER DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['buyer_Update']) && ($_POST['buyer']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $buyer = $_POST['buyer'];
        $description = $_POST['BUYER_Description'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_buyer
			  VALUES ("0","' . $buyer . '", "'. $description .'");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau Buyer ' . $buyer . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION BUYER DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['buyer_Delete']) && ($_POST['buyer']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $buyer = $_POST['buyer'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_buyer WHERE buyer = "' . $buyer . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> BUYER ' . $buyer . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION Commodity_Code DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['Commodity_Update']) && ($_POST['commodity']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $commodity = $_POST['commodity'];
        $description = $_POST['Commodity_Description'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_commodity_code
			  VALUES ("0","' . $commodity . '","' . $description . '");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau Commodity Code ' . $commodity . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION Commodity_Code DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['Commodity_Delete']) && ($_POST['commodity']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $commodity = $_POST['commodity'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_commodity_code WHERE Code = "' . $commodity . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Commodity Code ' . $commodity . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION Scheduling DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['scheduling_Update']) && ($_POST['scheduling_input']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $scheduling = $_POST['scheduling_input'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_scheduling_agent
			  VALUES ("0","' . $scheduling . '");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau Scheduling Agent ' . $scheduling . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION scheduling agent DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['scheduling_Delete']) && ($_POST['scheduling_input']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $scheduling = $_POST['scheduling_input'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_scheduling_agent WHERE scheduling_agent = "' . $scheduling . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Scheduling Agent ' . $scheduling . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION Supervisor DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['supervisor_Update']) && ($_POST['supervisor']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $supervisor = $_POST['supervisor'];
        $description = $_POST['supervisor_Description'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_supervisor
			  VALUES ("0","' . $supervisor . '","' . $description . '");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau Supervisor ' . $supervisor . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION Supervisor DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['supervisor_Delete']) && ($_POST['supervisor']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $supervisor = $_POST['supervisor'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_supervisor WHERE Code = "' . $supervisor . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Supervisor ' . $supervisor . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION HTS DANS BASE SCM -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['HTS_Update']) && ($_POST['hts']) != "")) {

        //Connexion à BD
        include('../SCM_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $hts = $_POST['hts'];
        $description = $_POST['HTS_Description'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_hts
			  VALUES ("0","' . $hts . '","' . $description . '");';

        $resultat = $mysqli_scm->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli_scm);

        // Message confirmation
        $msg_conf = '</br> Nouveau HTS ' . $hts . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION HTS DANS BASE SCM -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['HTS_Delete']) && ($_POST['hts']) != "")) {

        //Connexion à BD
        include('../SCM_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $hts = $_POST['hts'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_hts WHERE HTS = "' . $hts . '";';

        $resultat = $mysqli_scm->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli_scm);

        // Message confirmation
        $msg_conf = '</br> HTS ' . $hts . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION Proc_Type DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['PROC_Update']) && ($_POST['proc_input']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $proc_type = $_POST['proc_input'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_proc_type
			  VALUES ("0","' . $proc_type . '");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau Proc_Type ' . $proc_type . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION Proc_Type DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['PROC_Delete']) && ($_POST['proc_input']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $proc_type = $_POST['proc_input'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_proc_type WHERE Supply_Type = "' . $proc_type . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Proc_Type ' . $proc_type . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION Proc_Type DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['DOC_TYPE_Update']) && ($_POST['doc_type']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $doc_type = $_POST['doc_type'];
        $description = $_POST['DOC_TYPE_Description'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_doc_type
			  VALUES ("0","' . $doc_type . '","' . $description . '");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau Doc_Type ' . $doc_type . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION Proc_Type DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['DOC_TYPE_Delete']) && ($_POST['doc_type']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $doc_type = $_POST['doc_type'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_doc_type WHERE Doc_Type = "' . $doc_type . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Doc_Type ' . $doc_type . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION SAP_Type DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['SAP_TYPE_Update']) && ($_POST['sap_type']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $sap_type = $_POST['sap_type'];
        $description = $_POST['SAP_TYPE_Description'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_sap_type
			  VALUES ("0","' . $sap_type . '","' . $description . '");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau SAP_Type ' . $sap_type . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION SAP_Type DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['SAP_TYPE_Delete']) && ($_POST['sap_type']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $sap_type = $_POST['sap_type'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_sap_type WHERE SAP_Type = "' . $sap_type . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> SAP_Type ' . $sap_type . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION Material_Type DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['MATERIAL_TYPE_Update']) && ($_POST['material_type']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $mat_type = $_POST['material_type'];
        $description = $_POST['MATERIAL_TYPE_Description'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_material_type
			  VALUES ("0","' . $mat_type . '","' . $description . '");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau Material_Type ' . $mat_type . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION Material_Type DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['MATERIAL_TYPE_Delete']) && ($_POST['material_type']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $mat_type = $_POST['material_type'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_material_type WHERE Material_Type = "' . $mat_type . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Material_Type ' . $mat_type . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION Inventory Impact DANS BASE RELEASE -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['Inventory_Update']) && ($_POST['inventory_impact']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $invent = $_POST['inventory_impact'];
        $description = $_POST['Inventory_Description'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_inventory_impact
			  VALUES ("0","' . $invent . '","' . $description . '");';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Nouveau Inventory Impact ' . $invent . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION Inventory Impact DANS BASE RELEASE -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['Inventory_Delete']) && ($_POST['inventory_impact']) != "")) {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $invent = $_POST['inventory_impact'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_inventory_impact WHERE Inventory_Impact = "' . $invent . '";';

        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message confirmation
        $msg_conf = '</br> Inventory Impact ' . $invent . ' supprimé !</br>';
    }
    ?>

    <!---------------------------------------->
    <!-- CREATION SAP FXXX DANS BASE SCM -->
    <!---------------------------------------->
    <?php
    if ((isset($_POST['SAP_FXXX_Update']) && ($_POST['sap_fxxx']) != "")) {

        //Connexion à BD
        include('../SCM_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $sap_fxxx = $_POST['sap_fxxx'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'INSERT INTO tbl_sap_fxxx
			  VALUES ("0","' . $sap_fxxx . '");';

        $resultat = $mysqli_scm->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli_scm);

        // Message confirmation
        $msg_conf = '</br> Nouveau SAP_FXXX ' . $sap_fxxx . ' créé!</br>';
    }
    ?>

    <!------------------------------------------->
    <!-- SUPPRESSION SAP FXXX DANS BASE SCM -->
    <!------------------------------------------->
    <?php
    if ((isset($_POST['SAP_FXXX_Delete']) && ($_POST['sap_fxxx']) != "")) {

        //Connexion à BD
        include('../SCM_Connexion_DB.php');

        //On récupère les valeurs entrées par l'utilisateur :
        $sap_fxxx = $_POST['sap_fxxx'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_sap_fxxx WHERE Code = "' . $sap_fxxx . '";';

        $resultat = $mysqli_scm->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli_scm);

        // Message confirmation
        $msg_conf = '</br> SAP_FXXX ' . $sap_fxxx . ' supprimé !</br>';
    }
    ?>

    <form name="name_form" method="post" action="" enctype="multipart/form-data">
        <table border=0 style="width:95%; background-color:transparent">

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        buyer
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['buyer_Update']) || isset($_POST['buyer_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau buyer ou suppression d'un buyer existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Buyer <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="buyer" name="buyer" id="buyer_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="Buyer_Info_Auto_Update()">
                        <datalist name="" id="buyer">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_buyer = 'SELECT buyer, name
                                  FROM tbl_buyer';
                            $resultat_buyer = $mysqli->query($sql_buyer);
                            while ($row = $resultat_buyer->fetch_assoc()) {
                                echo '<option value ="' . $row['buyer'] . '">' . $row['name'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>

                        <input type="text" style="vertical-align:middle;font-size:12px;" name="BUYER_Description" id="BUYER_Description" PLACEHOLDER="Description" title="Description du buyer sélectionné" />

                        <input type="submit" style="vertical-align:middle;text-align:center;width:80px;height:20px;" class="btn blue2" name="buyer_Update" value="Créer" title="Validation de la création d'un nouveau buyer" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="buyer_Delete" value="Supprimer" title="Suppression du buyer sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        Commodity_Code
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['Commodity_Update']) || isset($_POST['Commodity_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau Commodity_Code ou suppression d'un Commodity_Code existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Commodity_Code <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="commodity" name="commodity" id="commodity_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="Commodity_Info_Auto_Update()">
                        <datalist id="commodity">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_commodity_code = 'SELECT Code, Description
                                  FROM tbl_commodity_code';
                            $resultat_commodity_code = $mysqli->query($sql_commodity_code);
                            while ($row = $resultat_commodity_code->fetch_assoc()) {
                                echo '<option value ="' . $row['Code'] . '">' . $row['Description'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>

                        <input type="text" size="100" style="vertical-align:middle;font-size:12px;" name="Commodity_Description" id="COMMODITY_Description" PLACEHOLDER="Description" title="Description du Commodity_Code sélectionné" />

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="Commodity_Update" value="Créer" title="Validation de la création d'un nouveau code" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="Commodity_Delete" value="Supprimer" title="Suppression du code et de la description sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        scheduling_agent
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['scheduling_Update']) || isset($_POST['scheduling_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau scheduling_agent ou suppression d'un scheduling_agent existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        scheduling_agent <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="scheduling_agent" name="scheduling_input" style="vertical-align:middle;font-size:12px;background-color:white;width:150px">
                        <datalist name="" id="scheduling_agent">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_scheduling = 'SELECT scheduling_agent
                                  FROM tbl_scheduling_agent';
                            $resultat_scheduling = $mysqli->query($sql_scheduling);
                            while ($row = $resultat_scheduling->fetch_assoc()) {
                                echo '<option value ="' . $row['scheduling_agent'] . '">' . $row['scheduling_agent'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="scheduling_Update" value="Créer" title="Validation de la création d'un nouveau scheduling_agent" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="scheduling_Delete" value="Supprimer" title="Suppression du scheduling_agent sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        Supervisor
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['supervisor_Update']) || isset($_POST['supervisor_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau supervisor ou suppression d'un supervisor existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        supervisor <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="supervisor" name="supervisor" id="supervisor_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="Supervisor_Info_Auto_Update()">
                        <datalist name="" id="supervisor">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_supervisor = 'SELECT Code, Description
                                  FROM tbl_supervisor';
                            $resultat_supervisor = $mysqli->query($sql_supervisor);
                            while ($row = $resultat_supervisor->fetch_assoc()) {
                                echo '<option value ="' . $row['Code'] . '">' . $row['Description'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>

                        <input type="text" style="vertical-align:middle;font-size:12px;" name="supervisor_Description" id="SUPERVISOR_Description" PLACEHOLDER="DUPOND M." title="Description du supervisor sélectionné" />

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="supervisor_Update" value="Créer" title="Validation de la création d'un nouveau code" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="supervisor_Delete" value="Supprimer" title="Suppression du code et de la description sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        HTS
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['HTS_Update']) || isset($_POST['HTS_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau HTS ou suppression d'un HTS existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        HTS <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="hts" name="hts" id="hts_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="HTS_Info_Auto_Update()">
                        <datalist id="hts">
                            <?php
                            include('../SCM_Connexion_DB.php');
                            $sql_1 = 'SELECT HTS, Description
                                  FROM tbl_hts';
                            $resultat_hts = $mysqli_scm->query($sql_1);
                            while ($row = $resultat_hts->fetch_assoc()) {
                                echo '<option value ="' . $row['HTS'] . '">' . $row['Description'] . '</option><br/>';
                            }
                            $mysqli_scm->close();
                            ?>
                        </datalist>
                        <input type="text" style="vertical-align:middle;font-size:12px;" name="HTS_Description" id="HTS_Description" PLACEHOLDER="Description" title="Description du HTS sélectionné" />

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="HTS_Update" value="Créer" title="Validation de la création d'un nouveau HTS" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="HTS_Delete" value="Supprimer" title="Suppression du HTS sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        proc_type
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['PROC_Update']) || isset($_POST['PROC_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau proc_type ou suppression d'un proc_type existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Proc_Type <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="proc_type" name="proc_input" style="vertical-align:middle;font-size:12px;background-color:white;width:150px">
                        <datalist name="" id="proc_type">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_proc = 'SELECT Supply_Type
                                  FROM tbl_proc_type';
                            $resultat_proc = $mysqli->query($sql_proc);
                            while ($row = $resultat_proc->fetch_assoc()) {
                                echo '<option value ="' . $row['Supply_Type'] . '">' . $row['Supply_Type'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="PROC_Update" value="Créer" title="Validation de la création d'un nouveau proc_type" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="PROC_Delete" value="Supprimer" title="Suppression d'un proc_type' sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        Doc_Type
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['DOC_TYPE_Update']) || isset($_POST['DOC_TYPE_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau Doc_Type ou suppression d'un Doc_Type existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Type <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="doc_type" name="doc_type" id="doc_type_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="DOC_TYPE_Info_Auto_Update()">
                        <datalist id="doc_type">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_1 = 'SELECT Doc_Type, Doc_Type_Description
                                  FROM tbl_doc_type';
                            $resultat_doc_type = $mysqli->query($sql_1);
                            while ($row = $resultat_doc_type->fetch_assoc()) {
                                echo '<option value ="' . $row['Doc_Type'] . '">' . $row['Doc_Type_Description'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>
                        <input type="text" size="27" style="vertical-align:middle;font-size:12px;" name="DOC_TYPE_Description" id="DOC_TYPE_Description" PLACEHOLDER="Description" title="Description du Doc_Type sélectionné" />

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="DOC_TYPE_Update" value="Créer" title="Validation de la création d'un nouveau Doc_Type" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="DOC_TYPE_Delete" value="Supprimer" title="Suppression du Doc_Type sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        Sap Type
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['SAP_TYPE_Update']) || isset($_POST['SAP_TYPE_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau Sap_Type ou suppression d'un Sap_Type existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Sap_Type <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="sap_type" name="sap_type" id="sap_type_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="SAP_TYPE_Info_Auto_Update()">
                        <datalist id="sap_type">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_1 = 'SELECT Sap_Type, Description
                                  FROM tbl_sap_type';
                            $resultat_sap_type = $mysqli->query($sql_1);
                            while ($row = $resultat_sap_type->fetch_assoc()) {
                                echo '<option value ="' . $row['Sap_Type'] . '">' . $row['Description'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>
                        <input type="text" style="vertical-align:middle;font-size:12px;" name="SAP_TYPE_Description" id="SAP_TYPE_Description" PLACEHOLDER="Description" title="Description du Sap_Type sélectionné" />

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="SAP_TYPE_Update" value="Créer" title="Validation de la création d'un nouveau Sap_Type" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="SAP_TYPE_Delete" value="Supprimer" title="Suppression du Sap_Type sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        Material Type
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['MATERIAL_TYPE_Update']) || isset($_POST['MATERIAL_TYPE_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau Material_Type ou suppression d'un Material_Type existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Material_Type <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="material_type" name="material_type" id="material_type_select" style="vertical-align:middle;font-size:12px;background-color:white;width:180px" onchange="MATERIAL_TYPE_Info_Auto_Update()">
                        <datalist id="material_type">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_1 = 'SELECT Material_Type, Description
                                  FROM tbl_material_type';
                            $resultat_material_type = $mysqli->query($sql_1);
                            while ($row = $resultat_material_type->fetch_assoc()) {
                                echo '<option value ="' . $row['Material_Type'] . '">' . $row['Description'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>
                        <input type="text" size="70" style="vertical-align:middle;font-size:12px;" name="MATERIAL_TYPE_Description" id="MATERIAL_TYPE_Description" PLACEHOLDER="Description" title="Description du Material_Type sélectionné" />

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="MATERIAL_TYPE_Update" value="Créer" title="Validation de la création d'un nouveau Material_Type" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="MATERIAL_TYPE_Delete" value="Supprimer" title="Suppression du Material_Type sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        Inventory Impact / Statut Gestion En Cours
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['Inventory_Update']) || isset($_POST['Inventory_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau Inventory_Impact ou suppression d'un Inventory_Impact existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Inventory_Impact <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="inventory_impact" name="inventory_impact" id="inventory_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="INVENTORY_Info_Auto_Update()">
                        <datalist id="inventory_impact">
                            <?php
                            include('../REL_Connexion_DB.php');
                            $sql_1 = 'SELECT Inventory_Impact, Description
                                  FROM tbl_inventory_impact';
                            $resultat_inventory_impact = $mysqli->query($sql_1);
                            while ($row = $resultat_inventory_impact->fetch_assoc()) {
                                echo '<option value ="' . $row['Inventory_Impact'] . '">' . $row['Description'] . '</option><br/>';
                            }
                            $mysqli->close();
                            ?>
                        </datalist>
                        <input type="text" size="73" style="vertical-align:middle;font-size:12px;" name="Inventory_Description" id="Inventory_Description" PLACEHOLDER="Description" title="Description du Inventory sélectionné" />

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="Inventory_Update" value="Créer" title="Validation de la création d'un nouveau Inventory" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="Inventory_Delete" value="Supprimer" title="Suppression du Inventory sélectionné" />
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=7>
                    <hr>
                </td>
            </tr>

            <tr>
                <td colspan=3>
                    <div id="FilterTitle" style="font-weight:bold">
                        Pris Dans SAP
                    </div>
                </td>
                <td>
                    <div id="FilterTitle_User" style="font-style:italic">
                        <?php if (isset($_POST['SAP_FXXX_Update']) || isset($_POST['SAP_FXXX_Delete'])) {
                            echo $msg_conf;
                        } ?>
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=4>
                    <div id="FilterTitle_User">
                        Création d'un nouveau Sap_Fxxx ou suppression d'un Sap_Fxxx existant:
                    </div>
                </td>
            </tr>

            <tr>
                <td colspan=2>
                    <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                        Sap_Fxxx <font color=red> *</font>:
                    </div>
                </td>
                <td colspan=3 style="vertical-align:middle">
                    <div id="InpBox_User">
                        <input list="sap_fxxx" name="sap_fxxx" id="sap_fxxx_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="SAP_FXXX_Info_Auto_Update()">
                        <datalist id="sap_fxxx">
                            <?php
                            include('../SCM_Connexion_DB.php');
                            $sql_1 = 'SELECT Code
                                  FROM tbl_sap_fxxx';
                            $resultat_sap_fxxx = $mysqli_scm->query($sql_1);
                            while ($row = $resultat_sap_fxxx->fetch_assoc()) {
                                echo '<option value ="' . $row['Code'] . '">' . $row['Code'] . '</option><br/>';
                            }
                            $mysqli_scm->close();
                            ?>
                        </datalist>

                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="SAP_FXXX_Update" value="Créer" title="Validation de la création d'un nouveau SAP_FXXX" />
                        <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="SAP_FXXX_Delete" value="Supprimer" title="Suppression du SAP_FXXX sélectionné" />
                    </div>
                </td>
            </tr>

        </table>
    </form>

</body>

</html>