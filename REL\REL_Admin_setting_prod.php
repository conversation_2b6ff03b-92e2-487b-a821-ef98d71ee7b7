<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!doctype html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <link rel="stylesheet" href="REL_Admin_style.css" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

    <title>Page Principale</title>
</head>
<body>

<script>
    function RDO_Info_Auto_Update(){
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function(){
            document.getElementById("RDO_Description").value = this.responseText.trim();
        }
        xhttp.open("GET", "REL_Admin_Auto.php?RDO="+document.getElementById("rdo_select").value + "&root=rdo");
        xhttp.send();
    }

    function Product_Info_Auto_Update(){
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function(){
            document.getElementById("PRODUCT_Description").value = this.responseText.trim();
        }
        xhttp.open("GET", "REL_Admin_Auto.php?Code="+document.getElementById("product_select").value + "&root=product");
        xhttp.send();
    }

    function FXXX_Info_Auto_Update(){
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function(){
            document.getElementById("FXXX_Description").value = this.responseText.trim();
        }
        xhttp.open("GET", "REL_Admin_Auto.php?fxxx_ref="+document.getElementById("fxxx_select").value + "&root=fxxx");
        xhttp.send();
    }
</script>

<!---------------------------------------->
<!-- CREATION RDO DANS BASE SCM -->
<!---------------------------------------->
<?php
if ((isset($_POST['RDO_Update']) && ($_POST['rdo'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $rdo=$_POST['rdo'];
    $description = $_POST['RDO_Description'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_rdo
			  VALUES ("0","'.$rdo.'","'.$description.'");';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> Nouveau RDO '.$rdo.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION RDO DANS BASE SCM -->
<!------------------------------------------->
<?php
if ((isset($_POST['RDO_Delete']) && ($_POST['rdo'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $rdo=$_POST['rdo'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_rdo WHERE RDO = "'.$rdo.'";';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> RDO '.$rdo.' supprimé !</br>';
}
?>

<!---------------------------------------->
<!-- CREATION ECCN DANS BASE SCM -->
<!---------------------------------------->
<?php
if ((isset($_POST['ECCN_Update']) && ($_POST['eccn_input'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $eccn=$_POST['eccn_input'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_eccn
			  VALUES ("0","'.$eccn.'","");';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> Nouveau ECCN '.$eccn.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION ECCN DANS BASE SCM -->
<!------------------------------------------->
<?php
if ((isset($_POST['ECCN_Delete']) && ($_POST['eccn_input'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $eccn=$_POST['eccn_input'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_eccn WHERE ECCN = "'.$eccn.'";';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> ECCN '.$eccn.' supprimé !</br>';
}
?>

<!---------------------------------------->
<!-- CREATION Product Code DANS BASE SCM -->
<!---------------------------------------->
<?php
if ((isset($_POST['Product_Update']) && ($_POST['product'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $product_code=$_POST['product'];
    $description = $_POST['Product_Description'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_product_code
			  VALUES ("0","'.$product_code.'","'.$description.'");';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> Nouveau Product Code '.$product_code.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION Product Code DANS BASE SCM -->
<!------------------------------------------->
<?php
if ((isset($_POST['Product_Delete']) && ($_POST['product'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $product_code=$_POST['product'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_product_code WHERE Code = "'.$product_code.'";';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> Product Code '.$product_code.' supprimé !</br>';
}
?>

<!---------------------------------------->
<!-- CREATION Activity DANS BASE RELEASE -->
<!---------------------------------------->
<?php
if ((isset($_POST['Activity_Update']) && ($_POST['activity_input'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $activity=$_POST['activity_input'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_activity
			  VALUES ("0","'.$activity.'");';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Nouvelle Activity '.$activity.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION Activity DANS BASE RELEASE -->
<!------------------------------------------->
<?php
if ((isset($_POST['Activity_Delete']) && ($_POST['activity_input'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $activity=$_POST['activity_input'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_activity WHERE Activity = "'.$activity.'";';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Activity '.$activity.' supprimé !</br>';
}
?>

<!---------------------------------------->
<!-- CREATION FXXX DANS BASE SCM -->
<!---------------------------------------->
<?php
if ((isset($_POST['FXXX_Update']) && ($_POST['fxxx'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $fxxx=$_POST['fxxx'];
    $description=$_POST['fxxx_Description'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_fxxx
			  VALUES ("0","'.$fxxx.'","'.$description.'","","","","","ACTIVE","","");';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    //print_r($sql_1);
    $msg_conf='</br> Nouveau FXXX '.$fxxx.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION FXXX DANS BASE SCM -->
<!------------------------------------------->
<?php
if ((isset($_POST['FXXX_Delete']) && ($_POST['fxxx'])!=""))
{

    //Connexion à BD
    include('../SCM_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $fxxx=$_POST['fxxx'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_fxxx WHERE fxxx_ref = "'.$fxxx.'";';

    $resultat = $mysqli_scm->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli_scm);

    // Message confirmation
    $msg_conf='</br> FXXX '.$fxxx.' supprimé !</br>';
}
?>

<form name="name_form" method="post" action="" enctype="multipart/form-data">
    <table border=0	 style="width:95%; background-color:transparent">

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    RDO
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['RDO_Update']) || isset($_POST['RDO_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau RDO ou suppression d'un RDO existant:
                </div>
            </td>
        </tr>
        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    RDO <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="rdo" name="rdo" id="rdo_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="RDO_Info_Auto_Update()">
                    <datalist id="rdo">
                        <?php
                        include('../SCM_Connexion_DB.php');
                        $sql_rdo = 'SELECT RDO, Description
                                  FROM tbl_rdo';
                        $resultat_rdo = $mysqli_scm->query($sql_rdo);
                        while ($row_rdo = $resultat_rdo->fetch_assoc())
                        {
                            echo'<option value ="'.$row_rdo['RDO'].'">'.$row_rdo['Description'].'</option><br/>';
                        }
                        $mysqli_scm->close();
                        ?>
                    </datalist>

                    <input type="text" size="25" style="vertical-align:middle;font-size:12px;" name="RDO_Description" id="RDO_Description" PLACEHOLDER="Description" title="Description du RDO sélectionné"/>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="RDO_Update" value="Créer" title="Validation de la création d'un nouveau RDO"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="RDO_Delete" value="Supprimer" title="Suppression du RDO et de la description sélectionné"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    ECCN
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['ECCN_Update']) || isset($_POST['ECCN_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau ECCN ou suppression d'un ECCN existant:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    ECCN <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="eccn" name="eccn_input" style="vertical-align:middle;font-size:12px;background-color:white;width:150px">
                    <datalist name="" id="eccn">
                        <?php
                        include('../SCM_Connexion_DB.php');
                        $requete_eccn = 'SELECT ECCN
                                  FROM tbl_eccn';
                        $resultat_eccn = $mysqli_scm->query($requete_eccn);
                        while ($row = $resultat_eccn->fetch_assoc())
                        {
                            echo'<option value ="'.$row['ECCN'].'">'.$row['ECCN'].'</option><br/>';
                        }
                        $mysqli_scm->close();
                        ?>
                    </datalist>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="ECCN_Update" value="Créer" title="Validation de la création d'un nouveau ECCN"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="ECCN_Delete" value="Supprimer" title="Suppression du ECCN sélectionné"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    Product_Code
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['Product_Update']) || isset($_POST['Product_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau Product_Code ou suppression d'un Product_Code existant:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    Product_Code <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="product" name="product" id="product_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="Product_Info_Auto_Update()">
                    <datalist id="product">
                        <?php
                        include('../SCM_Connexion_DB.php');
                        $sql_product_code = 'SELECT Code, Description
                                  FROM tbl_product_code
								  ORDER BY Code ASC, Description ASC';
                        $resultat_product_code = $mysqli_scm->query($sql_product_code);
                        while ($row = $resultat_product_code->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Code'].'">'.$row['Description'].'</option><br/>';
                        }
                        $mysqli_scm->close();
                        ?>
                    </datalist>

                    <input type="text" style="vertical-align:middle;font-size:12px;" name="Product_Description" id="PRODUCT_Description" PLACEHOLDER="Description" title="Description du Product_Code sélectionné"/>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="Product_Update" value="Créer" title="Validation de la création d'un nouveau code"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="Product_Delete" value="Supprimer" title="Suppression du code et de la description sélectionné"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    Activity
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['Activity_Update']) || isset($_POST['Activity_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'une nouvelle Activity ou suppression d'une Activity existante:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    Activity <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="activity" name="activity_input" style="vertical-align:middle;font-size:12px;background-color:white;width:150px">
                    <datalist name="" id="activity">
                        <?php
                        include('../REL_Connexion_DB.php');
                        $requete_activity = 'SELECT Activity
                                  FROM tbl_activity';
                        $resultat_activity = $mysqli->query($requete_activity);
                        while ($row = $resultat_activity->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Activity'].'">'.$row['Activity'].'</option><br/>';
                        }
                        $mysqli->close();
                        ?>
                    </datalist>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="Activity_Update" value="Créer" title="Validation de la création d'une nouvelle Activity"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="Activity_Delete" value="Supprimer" title="Suppression d'une Activity sélectionnée"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    FXXX
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['FXXX_Update']) || isset($_POST['FXXX_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau FXXX ou suppression d'un FXXX existant:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    FXXX <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="fxxx" name="fxxx" id="fxxx_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="FXXX_Info_Auto_Update()">
                    <datalist id="fxxx">
                        <?php
                        include('../SCM_Connexion_DB.php');
                        $sql_fxxx = 'SELECT fxxx_ref, fxxx_description
                                  FROM tbl_fxxx
                                  ORDER BY fxxx_ref ASC';
                        $resultat_fxxx = $mysqli_scm->query($sql_fxxx);
                        while ($row = $resultat_fxxx->fetch_assoc())
                        {
                            echo'<option value ="'.$row['fxxx_ref'].'">'.$row['fxxx_description'].'</option><br/>';
                        }
                        $mysqli_scm->close();
                        ?>
                    </datalist>

                    <input type="text" size="27" style="vertical-align:middle;font-size:12px;" name="fxxx_Description" id="FXXX_Description" PLACEHOLDER="Description" title="Description du FXXX sélectionné"/>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="FXXX_Update" value="Créer" title="Validation de la création d'un nouveau FXXX"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="FXXX_Delete" value="Supprimer" title="Suppression du FXXX et de la description sélectionné"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

    </table>
</form>

</body>
</html>