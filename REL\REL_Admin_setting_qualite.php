<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!doctype html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">

    <link rel="stylesheet" href="REL_Admin_style.css" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

    <title>Page Principale</title>
</head>
<body>

<script>
    function Q_Inspection_Info_Auto_Update(){
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function(){
            document.getElementById("INSPECTION_Description").value = this.responseText.trim();
        }
        xhttp.open("GET", "REL_Admin_Auto.php?Code="+document.getElementById("inspection_select").value + "&root=inspection");
        xhttp.send();
    }

    function Q_Dynam_Info_Auto_Update(){
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function(){
            document.getElementById("DYNAMISATION_Description").value = this.responseText.trim();
        }
        xhttp.open("GET", "REL_Admin_Auto.php?Code="+document.getElementById("dynam_select").value + "&root=dynam");
        xhttp.send();
    }

    function Q_Doc_Info_Auto_Update(){
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function(){
            document.getElementById("DOC_Description").value = this.responseText.trim();
        }
        xhttp.open("GET", "REL_Admin_Auto.php?Code="+document.getElementById("doc_select").value + "&root=doc_req");
        xhttp.send();
    }

    function Q_Control_Info_Auto_Update(){
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function(){
            document.getElementById("CONTROL_Description").value = this.responseText.trim();
        }
        xhttp.open("GET", "REL_Admin_Auto.php?Code="+document.getElementById("control_select").value + "&root=control");
        xhttp.send();
    }

    function METRO_Control_Info_Auto_Update(){
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function(){
            document.getElementById("METRO_CONTROL_Description").value = this.responseText.trim();
        }
        xhttp.open("GET", "REL_Admin_Auto.php?Control="+document.getElementById("metro_select").value + "&root=metro");
        xhttp.send();
    }

</script>

<!---------------------------------------->
<!-- CREATION Q_Inspection DANS BASE RELEASE -->
<!---------------------------------------->
<?php
if ((isset($_POST['inspection_Update']) && ($_POST['inspection'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $inspection=$_POST['inspection'];
    $description=$_POST['inspection_Description'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_q_inspection_type
			  VALUES ("0","'.$inspection.'","'.$description.'");';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Nouveau Inspection '.$inspection.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION Q_Inspection DANS BASE RELEASE -->
<!------------------------------------------->
<?php
if ((isset($_POST['inspection_Delete']) && ($_POST['inspection'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $inspection=$_POST['inspection'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_q_inspection_type WHERE Code = "'.$inspection.'";';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Inspection '.$inspection.' supprimé !</br>';
}
?>

<!---------------------------------------->
<!-- CREATION Q_Dynamisation DANS BASE RELEASE -->
<!---------------------------------------->
<?php
if ((isset($_POST['dynamisation_Update']) && ($_POST['dynamisation'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $dynamisation=$_POST['dynamisation'];
    $description=$_POST['dynamisation_Description'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_q_dynamisation_rules
			  VALUES ("0","'.$dynamisation.'","'.$description.'");';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Nouveau Dynamisation '.$dynamisation.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION Q_Dynamisation DANS BASE RELEASE -->
<!------------------------------------------->
<?php
if ((isset($_POST['dynamisation_Delete']) && ($_POST['dynamisation'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $dynamisation=$_POST['dynamisation'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_q_dynamisation_rules WHERE Code = "'.$dynamisation.'";';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Dynamisation '.$dynamisation.' supprimé !</br>';
}
?>

<!---------------------------------------->
<!-- CREATION Q_doc_requirements DANS BASE RELEASE -->
<!---------------------------------------->
<?php
if ((isset($_POST['doc_requirement_Update']) && ($_POST['doc_requirement'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $doc_requirement=$_POST['doc_requirement'];
    $description=$_POST['doc_requirement_Description'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_q_doc_requirements
			  VALUES ("0","'.$doc_requirement.'","'.$description.'");';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Nouveau Q_doc_requirements '.$doc_requirement.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION Q_doc_requirements DANS BASE RELEASE -->
<!------------------------------------------->
<?php
if ((isset($_POST['doc_requirement_Delete']) && ($_POST['doc_requirement'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $doc_requirement=$_POST['doc_requirement'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_q_doc_requirements WHERE Code = "'.$doc_requirement.'";';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Q_doc_requirements '.$doc_requirement.' supprimé !</br>';
}
?>

<!---------------------------------------->
<!-- CREATION Q_control_routing DANS BASE RELEASE -->
<!---------------------------------------->
<?php
if ((isset($_POST['control_routing_Update']) && ($_POST['control_routing'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $control_routing=$_POST['control_routing'];
    $description=$_POST['control_routing_Description'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_q_control_routing
			  VALUES ("0","'.$control_routing.'","'.$description.'");';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Nouveau Control Routing '.$control_routing.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION Q_control_routing DANS BASE RELEASE -->
<!------------------------------------------->
<?php
if ((isset($_POST['control_routing_Delete']) && ($_POST['control_routing'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $control_routing=$_POST['control_routing'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_q_control_routing WHERE Code = "'.$control_routing.'";';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Control Routing '.$control_routing.' supprimé !</br>';
}
?>

<!---------------------------------------->
<!-- CREATION METRO CONTROL DANS BASE RELEASE -->
<!---------------------------------------->
<?php
if ((isset($_POST['metro_control_Update']) && ($_POST['metro_control'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $metro_control=$_POST['metro_control'];
    $description=$_POST['METRO_CONTROL_Description'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'INSERT INTO tbl_metro
			  VALUES ("0","'.$metro_control.'","'.$description.'");';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Nouveau Metro Control '.$metro_control.' créé!</br>';

}
?>

<!------------------------------------------->
<!-- SUPPRESSION METRO CONTROL DANS BASE RELEASE -->
<!------------------------------------------->
<?php
if ((isset($_POST['metro_control_Delete']) && ($_POST['metro_control'])!=""))
{

    //Connexion à BD
    include('../REL_Connexion_DB.php');

    //On récupère les valeurs entrées par l'utilisateur :
    $metro_control=$_POST['metro_control'];

    //On prépare la commande sql d'insertion
    //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
    $sql_1 = 'DELETE FROM tbl_metro WHERE Control = "'.$metro_control.'";';

    $resultat = $mysqli->query($sql_1);

    // on ferme la connexion
    mysqli_close($mysqli);

    // Message confirmation
    $msg_conf='</br> Metro Control '.$metro_control.' supprimé !</br>';
}
?>

<form name="name_form" method="post" action="" enctype="multipart/form-data">
    <table border=0	 style="width:95%">

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    Q_inspection
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['inspection_Update']) || isset($_POST['inspection_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau code ou suppression d'un code existant:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    Q_inspection <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="inspection" name="inspection" id="inspection_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="Q_Inspection_Info_Auto_Update()">
                    <datalist name="" id="inspection">
                        <?php
                        include ('../REL_Connexion_DB.php');
                        $sql_inspection = 'SELECT Code, Description
                                  FROM tbl_q_inspection_type';
                        $resultat_inspection = $mysqli->query($sql_inspection);
                        while ($row = $resultat_inspection->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Code'].'">'.$row['Description'].'</option><br/>';
                        }
                        $mysqli->close();
                        ?>
                    </datalist>

                    <input type="text" size="40" style="vertical-align:middle;font-size:12px;" name="inspection_Description" id="INSPECTION_Description" PLACEHOLDER="Description" title="Description du code sélectionné"/>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="inspection_Update" value="Créer" title="Validation de la création d'un nouveau code"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="inspection_Delete" value="Supprimer" title="Suppression du code et de la description sélectionné"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    Q_dynamisation
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['dynamisation_Update']) || isset($_POST['dynamisation_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau code ou suppression d'un code existant:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    Q_dynamisation <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="dynamisation" name="dynamisation" id="dynam_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="Q_Dynam_Info_Auto_Update()">
                    <datalist id="dynamisation">
                        <?php
                        include ('../REL_Connexion_DB.php');
                        $sql_dynamisation = 'SELECT Code, Description
                                  FROM tbl_q_dynamisation_rules';
                        $resultat_dynamisation = $mysqli->query($sql_dynamisation);
                        while ($row = $resultat_dynamisation->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Code'].'">'.$row['Description'].'</option><br/>';
                        }
                        $mysqli->close();
                        ?>
                    </datalist>

                    <input type="text" size="25" style="vertical-align:middle;font-size:12px;" name="dynamisation_Description" id="DYNAMISATION_Description" PLACEHOLDER="Description" title="Description du code sélectionné"/>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="dynamisation_Update" value="Créer" title="Validation de la création d'un nouveau code"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="dynamisation_Delete" value="Supprimer" title="Suppression du code et de la description sélectionné"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    Q_doc_requirement
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['doc_requirement_Update']) || isset($_POST['doc_requirement_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau code ou suppression d'un code existant:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    Q_doc_requirement <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="doc_requirement" name="doc_requirement" id="doc_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="Q_Doc_Info_Auto_Update()">
                    <datalist name="" id="doc_requirement">
                        <?php
                        include ('../REL_Connexion_DB.php');
                        $sql_doc_requirement = 'SELECT Code, Description
                                  FROM tbl_q_doc_requirements';
                        $resultat_doc_requirement = $mysqli->query($sql_doc_requirement);
                        while ($row = $resultat_doc_requirement->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Code'].'">'.$row['Description'].'</option><br/>';
                        }
                        $mysqli->close();
                        ?>
                    </datalist>

                    <input type="text" size="33" style="vertical-align:middle;font-size:12px;" name="doc_requirement_Description" id="DOC_Description" PLACEHOLDER="Description" title="Description du code sélectionné"/>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="doc_requirement_Update" value="Créer" title="Validation de la création d'un nouveau code"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="doc_requirement_Delete" value="Supprimer" title="Suppression du code et de la description sélectionné"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    Q_control_routing
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['control_routing_Update']) || isset($_POST['control_routing_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau code ou suppression d'un code existant:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    Q_control_routing <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="control_routing" name="control_routing" id="control_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="Q_Control_Info_Auto_Update()">
                    <datalist name="" id="control_routing">
                        <?php
                        include ('../REL_Connexion_DB.php');
                        $sql_control_routing = 'SELECT Code, Description
                                  FROM tbl_q_control_routing';
                        $resultat_control_routing = $mysqli->query($sql_control_routing);
                        while ($row = $resultat_control_routing->fetch_assoc())
                        {
                            echo'<option value ="'.$row['Code'].'">'.$row['Description'].'</option><br/>';
                        }
                        $mysqli->close();
                        ?>
                    </datalist>
                    <input type="text" size="30" style="vertical-align:middle;font-size:12px;" name="control_routing_Description" id="CONTROL_Description" PLACEHOLDER="Description" title="Description du code sélectionné"/>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="control_routing_Update" value="Créer" title="Validation de la création d'un nouveau code"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="control_routing_Delete" value="Supprimer" title="Suppression du code et de la description sélectionné"/>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=7>
                <hr>
            </td>
        </tr>

        <tr>
            <td colspan=3>
                <div id="FilterTitle" style="font-weight:bold">
                    Metro Control
                </div>
            </td>
            <td>
                <div id="FilterTitle_User" style="font-style:italic">
                    <?php if (isset($_POST['metro_control_Update']) || isset($_POST['metro_control_Delete'])){echo $msg_conf;}?>
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=4>
                <div id="FilterTitle_User" >
                    Création d'un nouveau control ou suppression d'un code existant:
                </div>
            </td>
        </tr>

        <tr>
            <td colspan=2>
                <div id="FilterTitle_User" style="text-indent:55px;font-size:11px; text-align:left">
                    Metro Control <font color=red> *</font>:
                </div>
            </td>
            <td colspan=3 style="vertical-align:middle">
                <div id="InpBox_User">
                    <input list="metro_control" name="metro_control" id="metro_select" style="vertical-align:middle;font-size:12px;background-color:white;width:150px" onchange="METRO_Control_Info_Auto_Update()">
                    <datalist name="" id="metro_control">
                        <?php
                        include ('../REL_Connexion_DB.php');
                        $sql_metro_control = 'SELECT Control, Description
                                  FROM tbl_metro';
                        $resultat_metro_control = $mysqli->query($sql_metro_control);
                        while ($row_metro_control = $resultat_metro_control->fetch_assoc())
                        {
                            echo'<option value ="'.$row_metro_control['Control'].'">'.$row_metro_control['Description'].'</option><br/>';
                        }
                        $mysqli->close();
                        ?>
                    </datalist>
                    <input type="text" style="vertical-align:middle;font-size:12px;" name="METRO_CONTROL_Description" id="METRO_CONTROL_Description" PLACEHOLDER="Description" title="Description du code sélectionné"/>

                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="metro_control_Update" value="Créer" title="Validation de la création d'un nouveau code"/>
                    <input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn red" name="metro_control_Delete" value="Supprimer" title="Suppression du code et de la description sélectionné"/>
                </div>
            </td>
        </tr>

    </table>
</form>

</body>
</html>