body {
    background-color: transparent;
    color: black;
    font-size: 9pt;
    font-family: Tahoma, sans-serif;
    font-weight: normal;

}

#container {
    width: 400px;
    margin: 0 auto;
    margin-top: 10%;
}

/* Bordered form */
#form_log {
    width: 100%;
    padding: 30px;
    border: 1px solid #f1f1f1;
    background: #fff;
    box-shadow: 0 0 20px 0 rgba(0, 0, 0, 0.2), 0 5px 5px 0 rgba(0, 0, 0, 0.24);
}

#container h1 {
    width: 38%;
    margin: 0 auto;
    padding-bottom: 10px;
}

/* Full-width inputs */
#username, #password {
    width: 100%;
    padding: 12px 20px;
    margin: 8px 0;
    display: inline-block;
    border: 1px solid #ccc;
    box-sizing: border-box;
}

/* Set a style for all buttons */
#submit {
    background-color: #53af57;
    color: white;
    padding: 14px 20px;
    margin: 8px 0;
    border: none;
    cursor: pointer;
    width: 100%;
}

#submit:hover {
    background-color: white;
    color: #53af57;
    border: 1px solid #53af57;
}

div#Body {
    margin-left: 5px;
    margin-top: 5px;
    text-align: left;
    margin-bottom: 6px;
    margin-left: 10px;
    vertical-align: middle;
}

div#FilterTitle {
    text-indent: 20px;
    font-size: 8pt;
    font-weight: normal;
    font-family: Tahoma, sans-serif;
    margin-left: 5px;
    margin-bottom: 5px;
    text-align: justify;
}

div#FilterTitle_User {
    text-indent: 40px;
    font-size: 8pt;
    font-weight: normal;
    font-family: Tahoma, sans-serif;
    text-align: justify;
}

div#InpBox_User {
    font-size: 8pt;
    font-weight: normal;
    font-family: Tahoma, sans-serif;
    text-align: left;
    vertical-align: middle;
    margin-bottom: 0px;
    margin-top: 0px;
    margin-left: 2px;
}

div#InpBox {
    font-size: 8pt;
    font-weight: normal;
    font-family: Tahoma, sans-serif;
    text-align: left;
    vertical-align: middle;
    margin-bottom: 2px;
    margin-top: 2px;
    margin-left: 5px;
}

#t04 {
    border-collapse: collapse;
    width: 100%;
    overflow-y: auto;
    margin-top: -5px;
    margin-left: -5px;
    font-size: 12px;
}

#t04 th {
    border: 0px solid black;
    background-color: rgb(27, 79, 114);
    color: white;
    font-family: Arial Helvetica, sans-serif;
    text-align: center;
}

#t04 td {
    text-align: center;
    vertical-align: middle;
}

#t04 tr {
    height: 20px;
}

#t04 tr:hover {
    background-color: rgb(76, 126, 160);
    color: white;
    cursor: pointer;
}

.btn {
    display: inline-block;
    text-decoration: none;
    font-size: 9pt;
    text-align: left;
    padding: 1px 5px;
    color: white;
    background: rgb(48, 129, 185);
    border: 1px solid #2cabcf;
    width: 130px;
    height: 16px;
    vertical-align: middle;
    /*color: #1f6b81;*/
}

.btn:hover, .btn:focus {
    background: #ccc;
    text-shadow: 0 -1px #eee;
}

.btn:active {
    background: #2cabcf;
    text-shadow: 0 -1px #eee;
    border: 1px solid #1f6b81;
}