<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html translate="no">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<link rel="stylesheet" type="text/css" href="REL_BE_1_Sign_Off_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


<head>

<script>
</script>

<title>
    <?php echo 'REL / '.$_GET['ID'].' Package Released Confirmed ';?>
</title>

</head>

<body>


<?php 
$rowcount_rel_dmo=0;
$related_dmo="";
$status_dmo="";
    if (isset($_POST['Send_Validation']))
    {
		$creation_date=date("Y-m-d");;
		$creation_visa=$_POST['Package_Owner_VISA'];
		$package=$_GET['ID'];
		$verif_req_owner=$_POST['Verif_Req_Owner'];

		include('../REL_Connexion_DB.php');
		$sql = 'UPDATE tbl_released_package 
				SET
						Creation_Date="'.$creation_date.'",
						Creation_VISA="'.$creation_visa.'",
						Verif_Req_Owner="'.$verif_req_owner.'"
				WHERE 
					Rel_Pack_Num like "'.$package.'"';
		$result = $mysqli->query($sql);
		
		// RECUPERATION DE LA DMO ASSOCIEE AU PACKAGE
		$sql_rel_dmo='SELECT DMO from tbl_released_package where Rel_Pack_Num like "'.$_GET['ID'].'"';
		$resultat_rel_dmo = $mysqli->query($sql_rel_dmo);
		$rowcount_rel_dmo = mysqli_num_rows($resultat_rel_dmo);
		while ($row_rel_dmo = $resultat_rel_dmo->fetch_assoc())
		{
			$related_dmo=$row_rel_dmo['DMO'];
		}
		mysqli_close($mysqli);
		
		// SI LA PACKAGE AVAIT BIEN UNE DMO ASSOCIEE, 
		//  - VERIFICATION QUE LA DMO EXISTE DANS L'OUTIL DE DMO
		//  - RECUPERATION DE SON STATUT
		if ($related_dmo!="")
		{
			include('../DMO_Connexion_DB.php');
			$sql_dmo='SELECT Status FROM tbl_dmo where DMO like "'.$related_dmo.'"';

			$resultat_dmo = $mysqli_dmo->query($sql_dmo);
			while ($row_dmo = $resultat_dmo->fetch_assoc())
			{
				$status_dmo=$row_dmo['Status'];
			}
			mysqli_close($mysqli_dmo);
		}

    }
    
    
?>

<table border=0>
    <tr>
        <td colspan=5 style="width:1130px;">
            <div id="Title">
                <?php echo 'Package '.$_GET['ID'].' Release Confirmation';?>
            </div>
        </td>
        <td style="text-align:right">
            <img src="\Common_Resources\scm_logo.png" height="35">
        </td>
    </tr>
    <tr>
        <td>
            <?php
                echo  '<div id="Body" style="text-indent:5px">The package '.$_GET['ID'].' is now released and is sent to verification step - You can now close this window</div>'
            ?>
        </div>
        </td>
    </tr>
	<?php
		// SI UNE DMO EST ASSOCIE AU PACKAGE, ON RAPPELLE QUE LA DMO DOIT ETRE MISE A JOUR.
		if ($rowcount_rel_dmo>=1 && $status_dmo!="")
		{
			echo '
					<tr>
						<td style="padding-top:5px">
							<div id="Body" style="color:darkblue;text-indent:5px;"><font style="font-size:13;color:#943126">&#9888</font> Please, make sure to update the <a href="/DMO/DMO_Modification_form.php?DMO='.$related_dmo.'"> '.$related_dmo.' in the DMO tool and get the DMO requestor aware it is being processed</a>! <font style="font-size:13;color:#943126">&#9888</font> </div>
						</td>
					</tr>
				';
		}
	
	?>
            
</table>



</body> 
</html>