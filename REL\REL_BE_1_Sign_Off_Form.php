<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html  translate="no">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<meta http-equiv='cache-control' content='no-cache'>
<meta http-equiv='expires' content='0'>
<meta http-equiv='pragma' content='no-cache'>

<link rel="stylesheet" type="text/css" href="REL_BE_1_Sign_Off_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<head>

<script>
</script>

<title>
    <?php echo 'REL / '.$_GET['ID'].' - Package Release ';?>
</title>

</head>

<body>


    <?php 
		
		echo '<form enctype="multipart/form-data" action="REL_BE_1_Sign_Off_Confirmation_Form.php?ID='.$_GET['ID'].'" method="post">';
	
	 
            include('../REL_Connexion_DB.php');
            $requete = 'SELECT * FROM tbl_released_package WHERE Rel_Pack_Num like "'.$_GET['ID'].'"';
            $resultat = $mysqli->query($requete);
            while ($row = $resultat->fetch_assoc())
            {
                echo '
                    <table id="t01" border=0>
					<tr>
                       <tr style="height:68px;background-color:#2A80B9;color:white">
						<td colspan=5 style=" top:0px; width:100%">
							<div id="Title">
								Package '.$_GET['ID'].' Overview And Release
							</div>
                            <img id="logo_scm" src="\Common_Resources\scm_logo.png" height="68px" style="position: absolute; top:0px; right:0px; filter:grayscale(20%)">';
							// <img src="\Common_Resources\scm_logo.png" height="" style="position:absolute;top:0px;right:0.10%;z-index:99;margin-top:-1px;margin-right:-1px;vertical-align:top" >
						echo '</td>
                        </tr>
                        <tr>
                            <td style="width:80px;"><div id="Body">Engineering Owner:</div></td>
                            <td style="width:150px;"><div id="InpBox"><input style="border:transparent;background:transparent;border:none;width:80px;font-size:8pt; type="text" name="Rel_Pack_Owner" value ="'.$row['Rel_Pack_Owner'].'"readonly></div></td>
                        
                            <td style="width:100px;"><div id="Body">To be verified by:</div></td>
                            <td>
                                <div id="InpBox">
                                <select name="Verif_Req_Owner" type="submit" title="" style="width:120px;font-size:12"> 
                                <option value=""></option>';

                                //------------------------------>
                                include('../SCM_Connexion_DB.php');
                                    $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Method' or Department like 'Laboratory' ORDER BY Fullname ASC;";
                                    $resultat = $mysqli_scm->query($requete);
                                    while ($line = $resultat->fetch_assoc())
                                    {
                                        echo'<option value ="'.$line['Fullname'].'">'.$line['Fullname'].'</option><br/>'; 
                                    }
                                //------------------------------>
                    echo '
                                </select>
                                </div> 
                            </td>
                        </tr>
                        <tr>
                            <td><div id="Body">Activity:</div></td><td><div id="InpBox">'.$row['Activity'].'</div></td>
                            <td><div id="Body">Sign off (your name):</div></td>
                            <td>
                                <div id="InpBox">
                                <select name="Package_Owner_VISA" type="submit" title="" style="width:120px;font-size:12" REQUIRED> 
                                <option value=""></option>';

                                //------------------------------>
                                    $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Method' or Department like 'Laboratory' ORDER BY Fullname ASC;";
                                    $resultat = $mysqli_scm->query($requete);
                                    while ($line = $resultat->fetch_assoc())
                                    {
                                        echo'<option value ="'.$line['Fullname'].'">'.$line['Fullname'].'</option><br/>'; 
                                    }
                                    mysqli_close($mysqli_scm);
                                //------------------------------>
                    echo '
                                </select>
                                </div> 
                            </td>
                        </tr>
                        <tr>
                            <td><div id="Body">EX ?:</div></td><td><div id="InpBox">'.$row['Ex'].'</div>
                            <td colspan=2><div id="Body">Click the following button to send the package to validation</div></td>
                        </tr>
                        <tr>
                            <td><div id="Body">DMO:</div></td><td><div id="InpBox">'.$row['DMO'].'</div></td>
                            <td style="width:150;"></td>
                            <td style="text-align:center; vertical-align:top;" colspan=2>
                                <div id="InpBox">
                                    <input class="btn green" type="submit" style="font-size:11; vertical-align:middle;height:20px;" name="Send_Validation" value="Send to Verification" title="Send the current release package to validation" />
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td><div id="Body">Project:</div></td><td><div id="InpBox">'.$row['Project'].'</div></td>
                        </tr>
                        <tr style="padding-bottom:10px;">
                            <td><div id="Body">Observations:</div></td><td><div id="InpBox">'.$row['Observations'].'</div></td>
                        </tr>
                    ';
            
                    ?>
                </tr>
				<tr>
					<td colspan=5>
						<div id="Frame_Title">Package Content Overview</div>
					</td>
				</tr>
                <tr>
                    <td colspan=5>
                    
                    
            <?php }
			echo '<iframe class="main_frame"  alt="ok" src="./REL_Package_Content.php?Rel_Pack_Num='.$_GET['ID'].'" name="Main_target" id="Main_target" frameborder="1" style="margin-left:2px;text-align:middle" ></iframe>';
		?>

        </td>
    </tr>
    
    </table>


    </form>

</body> 
</html>