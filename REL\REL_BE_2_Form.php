<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html>

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<meta http-equiv='cache-control' content='no-cache'>
<meta http-equiv='expires' content='0'>
<meta http-equiv='pragma' content='no-cache'>

<link rel="stylesheet" type="text/css" href="REL_BE_2_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<head>

	<script>
		function welcome_page_loading() {
			let pg = window.parent.location.href;

			if (pg.includes("REL_Welcome.php") > -1) {
				document.getElementById("Page_Title").setAttribute('class', 'Title_standalone');
				document.querySelector('#td_title').setAttribute('backgroundColor', '#2A80B9');

			} else {
				document.querySelector('#tr_title').setAttribute('class', 'Title_embedded');
			}
		}

		function data_input_frame_sign_off(rel_pack_num) {
			window.frames[0].document.getElementById('id_to_update').value = rel_pack_num;
		}

		// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
		// Champ title en rouge et required si + de 40 digits

		function title_check() {
			// Recuperation du champ de saisie de la reference pour stockage dans x
			const x = document.getElementById("ref_title");
			// Remise en forme de la reference :
			//  - Suppression des espaces de debut et fin (trim)
			//  - Mise en majuscule du v de debut si erreur de saisie
			// -------------------------------------
			let y = x.value.trim();
			y = y.toUpperCase(y);
			// -------------------------------------


			// Verification de la longueur de la reference : 40 caracteres max
			//   - Changement de la couleur du champ de saisie en rouge dans le cas contraire
			// -------------------------------------
			if (y.length > 40) {
				alert("The title cannont be longer than 40 digits");
				x.style.backgroundColor = "#F5B7B1";
			} else if (y.length <= 40) {
				x.style.backgroundColor = "white";
			}
			// -------------------------------------
		}

		// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
		// Champ Alias en Rouge si + de 40 digits

		function alias_check() {
			// Recuperation du champ de saisie de l'alias pour stockage dans x
			const x = document.getElementById("Alias");
			// Remise en forme de la reference :
			//  - Suppression des espaces de debut et fin (trim)
			//  - Mise en majuscule du v de debut si erreur de saisie
			// -------------------------------------
			let y = x.value.trim();
			y = y.toUpperCase(y);
			// -------------------------------------


			// Verification de la longueur de l'alias : 40 caracteres max
			//   - Changement de la couleur du champ de saisie en rouge dans le cas contraire
			// -------------------------------------
			if (y.length > 40) {
				x.style.backgroundColor = "#F5B7B1";
				alert("The alias cannont be longer than 40 digits");
				x.focus();
			} else if (y.length <= 40) {
				x.style.backgroundColor = "white";
			}
			// -------------------------------------
		}

		// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

		function auto_doc() {
			var doc_type_value = document.getElementById('Doc_Type').value;
			var mat_type_value = document.getElementById('Material_Type').value;
			var fxxx = document.getElementById('fxxx').value;

			// Remplissage automatique de Mat_Type à LITERATURE quand Doc_Type est rempli par DOC
			if (doc_type_value == 'DOC') {
				document.getElementById('Material_Type').value = 'LITERATURE';
			} else if (mat_type_value == 'LITERATURE' && mat_type_value != '' && doc_type_value != "DOC") {
				document.getElementById('Material_Type').value = '';
			}

			// // Rendre le remplissage de FXXX obligatoire si le doc_type est MACH ou MOLD
			// if (doc_type_value == 'MACH' || doc_type_value == 'MOLD') {
			// 	document.getElementById('fxxx_').innerHTML = '<input required tabindex="19" onchange="auto_plating_surface()" list="Material" name="Material" multiple id="fxxx" title="" style="width:100px;font-size:11;height:13pt;">';
			// } else {
			// 	document.getElementById('fxxx_').innerHTML = '<input tabindex="19" onchange="auto_plating_surface()" list="Material" name="Material" multiple id="fxxx" title="" style="width:100px;font-size:11;height:13pt;">';
			// }
		}

		// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

		function auto_material_type() {
			var doc_type_value = document.getElementById('Doc_Type').value;
			var mat_type_value = document.getElementById('Material_Type').value;

			// Remplissage automatique de Doc_Type à DOC quand Mat_Type est rempli par LITERATURE

			if (mat_type_value == 'LITERATURE') {
				document.getElementById('Doc_Type').value = 'DOC';
			} else if (doc_type_value == "DOC" && doc_type_value != "" && mat_type_value != "LITERATURE") {
				document.getElementById('Doc_Type').value = "";
			}
		}

		// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!

		function limit_check() {
			// Recuperation du champ de saisie de la reference pour stockage dans x
			const x = document.getElementById("Alias");
			const ref_title = document.getElementById("ref_title").value;
			const act = document.getElementById("Action").value;
			const ref = document.getElementById("Reference").value;
			const ref_rev = document.getElementById("ref_rev").value;
			const doctype = document.getElementById("Doc_Type").value;
			const prod_dr = document.getElementById("prod_draw").value;
			const prod_dr_rev = document.getElementById("prod_draw_rev").value;
			const mat_type = document.getElementById("Material_Type").value;
			const invent_impact = document.getElementById("Inventory_Impact").value;
			const ex_val = document.getElementById("Ex").value;
			const weight_val = document.getElementById("Weight").value.trim();
			const weight_unit = document.getElementById("Weight_Unit_ID").value;
			// Remise en forme de la reference :
			//  - Suppression des espaces de debut et fin (trim)
			//  - Mise en majuscule du v de debut si erreur de saisie
			// -------------------------------------


			if (mat_type != 'LITERATURE') {
				if (act == "" || ref == "" || ref_rev == "" || ref_title == "" || doctype == "" || prod_dr == "" || prod_dr_rev == "" || mat_type == "" || invent_impact == "" || ex_val == "" || weight_val == "" || weight_val == "0" || weight_unit == "") {
					//if (ref.length!=18)
					//{
					alert("All the fields flagged by a star (*) shall be filled");
					return false;
					//}
				}
			} else if (prod_dr == "" || prod_dr_rev == "") {

				alert('To release a "LITERATURE" type, the production drawing field, and its revision must be filled');
				return false;

			}

			let y = x.value.trim();
			y = y.toUpperCase(y);

			if (y.length > 40) {
				alert("There are too many characters in the 'Alias' field (max 40). \nPlease correct the entry.");
				return false;
			}

			if (ref_title.length > 40) {
				alert("There are too many characters in the 'Title' field (max 40). \nPlease correct the entry.");
				return false;
			}
			// -------------------------------------
		}
	</script>

	<title>
		<?php echo 'REL / ' . $_GET['ID'] . ' - Package Verification'; ?>
	</title>


	<style>


	</style>

</head>

<?php
// MISE A JOUR DES DONNES ASSOCIEES AU NUMERO DE PACKAGE AVEC LES INFO FOURNIES PAR L'UTILISATEUR
$msg = "";
if (isset($_POST['validate_new_row'])) {

	$id_to_update = $_POST['id_to_update'];

	$reference = $_POST['Reference'];
	$ref_rev = $_POST['ref_rev'];
	$prod_draw = $_POST['prod_draw'];
	$prod_draw_rev = $_POST['prod_draw_rev'];
	$ref_title = htmlspecialchars($_POST['ref_title'], ENT_QUOTES);

	$alias = $_POST['Alias_name'];
	if ($alias == "") {
		$alias = "";
	}

	$action = $_POST['Action'];
	$doc_type = $_POST['Doc_Type'];
	$material_type = $_POST['Material_Type'];
	$inventory_impact = $_POST['Inventory_Impact'];
	$ex = $_POST['Ex'];


	$eccn = $_POST['ECCN'];
	if ($eccn == "") {
		$eccn = "";
	}

	$hts = $_POST['HTS'];
	if ($hts == "") {
		$hts = "";
	}

	$rdo = $_POST['RDO'];
	if ($rdo == "") {
		$rdo = "";
	}

	$weight = intval($_POST['Weight']);
	$weight_unit = $_POST['Weight_Unit_name'];
	if ($weight == 0) {
		$weight_unit = "";
	}

	if (isset($_POST['Plating_Surface'])) {
		$plating_surface = intval($_POST['Plating_Surface']);
		if (isset($_POST['Plating_Surface_Unit_name'])) {
			$plating_surface_unit = $_POST['Plating_Surface_Unit_name'];
		} else {
			$plating_surface_unit = "";
		}
	} else {
		$plating_surface = "";
		$plating_surface_unit = "";
	}

	$fxxx = $_POST['Material'];
	if ($fxxx == "") {
		$fxxx = "";
	}

	include('../REL_Connexion_DB.php');
	$requestor_comments = htmlspecialchars($_POST['requestor_comments'], ENT_QUOTES);

	//if(strlen($requestor_comments)>0)
	//{
	//	$query_3 = 'SELECT Requestor_Comments
	//				FROM tbl_released_drawing
	//				WHERE ID ="' . $id_to_update.'"';

	//	$resultat = $mysqli->query($query_3);


	//	$be_2_comments="";

	//	while ($row = $resultat->fetch_assoc())
	//	{
	//		if ($requestor_comments != $row['Requestor_Comments'])
	//		{
	//			if (strlen($row['Requestor_Comments'])>0)
	//			{
	//$i=0;
	//while(substr(trim($requestor_comments),0,$i)!=substr(trim($row['Requestor_Comments']),0,$i))
	//{
	//	$i++;
	//}		
	//print_r($i);
	//echo '<script>alert("'.substr($requestor_comments,$i+1, 10).'")</script>';
	//				$be_2_comments_tmp=explode($_POST['requestor_comments'], $row['Requestor_Comments']);
	//				$be_2_comments=$row['Requestor_Comments']. '\nVerif BE : ' . $be_2_comments_tmp[0];
	//			} else {
	//				$be_2_comments='Verif BE : ' . $requestor_comments;
	//			}
	//		} else {
	//			$be_2_comments=$requestor_comments;
	//		}
	//	} 

	//	$requestor_comments = $be_2_comments;	
	//}


	if (isset($_POST['inhouse_manuf'])) {
		$inhouse_manuf = 1;
	} else {
		$inhouse_manuf = 0;
	}

	$cust_drawing = $_POST['cust_draw'];
	if ($cust_drawing == "") {
		$cust_drawing = "";
	}

	$cust_drawing_rev = $_POST['cust_draw_rev'];
	if ($cust_drawing_rev == "") {
		$cust_drawing_rev = "";
	}


	$sql_2 = 'UPDATE tbl_released_drawing 
				  SET 
					Reference="' . $reference . '",
					Ref_Rev="' . $ref_rev . '",
					Prod_Draw="' . $prod_draw . '",
					Prod_Draw_Rev="' . $prod_draw_rev . '",
					Ref_Title="' . $ref_title . '",
					Alias="' . $alias . '",
					Cust_Drawing="' . $cust_drawing . '",
					Cust_Drawing_Rev="' . $cust_drawing_rev . '",
					Action="' . $action . '",
					Doc_Type="' . $doc_type . '",
					Material_Type="' . $material_type . '",
					Inventory_Impact="' . $inventory_impact . '",
					Ex="' . $ex . '",
					Weight="' . $weight . '",
					Weight_Unit="' . $weight_unit . '",
					Plating_Surface="' . $plating_surface . '",
					Plating_Surface_Unit="' . $plating_surface_unit . '",
					FXXX="' . $fxxx . '",
					Internal_Mach_Rec="' . $inhouse_manuf . '",
					Requestor_Comments="' . $requestor_comments . '",
					ECCN="' . $eccn . '",
					RDO="' . $rdo . '",
					HTS="' . $hts . '"
				 WHERE ID like "' . $id_to_update . '";';


	$resultat_2 = $mysqli->query($sql_2);
	mysqli_close($mysqli);

	$msg = "Data successfully updated !";
}


?>




<!--<body onload="welcome_page_loading()">-->
<?php echo '<body onload="data_input_frame_sign_off(' . $_GET['ID'] . ')">'; ?>


<form enctype="multipart/form-data" action="" method="post">

	<table id="t01" border=0>

		<tr style="height:40px;">
			<!-- <tr id="tr_title" style="height:60px;background-color:#2A80B9;color:white"> -->
			<td id="td_title" colspan=2 style="width:100%">
				<!--<div id="Page_Title" class="">-->
				<div id="Page_Title">
					<?php echo 'Package ' . $_GET['ID'] . ' Verification'; ?>
				</div>
			</td>


		</tr>

		<?php
		include('../REL_Connexion_DB.php');
		$requete_pack = 'SELECT DISTINCT * FROM tbl_released_package WHERE Rel_Pack_Num like "' . $_GET['ID'] . '" and Creation_VISA not like "" and VISA_BE_2 like ""';
		$resultat_Pack = $mysqli->query($requete_pack);
		$pack_exist = mysqli_num_rows($resultat_Pack);
		mysqli_close($mysqli);

		if ($pack_exist == 0) {
			echo "<tr><td>!! Package not existing or at a different step of the release process !!</td>";
		} else {
		?>

			<tr style="height:100px;border-bottom:0.5px solid black;width:30%">
				<td style="vertical-align:middle;width:60vw">
					<?php include('REL_Package_Details_H.php'); ?>
				</td>
				<td style="border-left:0.5px solid black;width:35vw">
					<?php //include('REL_BE_2_Sign_Off_Form.php');
					echo '<iframe name="sign_off" id="sign_off" class="signoff_frame" scrolling="no" alt="ok" src="./REL_BE_2_Sign_Off_Form.php?Rel_Pack_Num=' . $_GET['ID'] . '" frameborder="0" ></iframe>';
					?>
				</td>
			</tr>

			<tr style="height:120px;" id="tr_detail">
				<td id="td_detail" colspan=2 class="disabled" style="width:99%">
					<table id="t_detail" border=0 style="height:100%;width:100%">
						<tr>
							<td>
								<input HIDDEN type="text" id="id_to_update" size=2 name="id_to_update" style="font-size:11;height:13pt;">
								<div id="Body">
									Action<FONT color="#EE0000">*</FONT>
								</div>
							</td>
							<td>
								<div id="InpBox">
									<select name="Action" id="Action" type="submit" title="" style="font-size:11;height:17;background-color:transparent">
										<option value=""></option>

										<?php
										include('../REL_Connexion_DB.php');
										$requete = "SELECT DISTINCT Action FROM tbl_action ORDER BY Action DESC;";
										$resultat = $mysqli->query($requete);
										while ($row = $resultat->fetch_assoc()) {
											echo '<option value ="' . $row['Action'] . '">' . $row['Action'] . '</option><br/>';
										}
										mysqli_close($mysqli);
										?>

									</select>
								</div>
							</td>
							<td>
								<div id="Body">
									Reference<FONT color="#EE0000">*</FONT>
								</div>
							</td>
							<td>
								<div id="InpBox">
									<input type="text" id="Reference" size=20 name="Reference" title="18 characters" style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey" onchange="ref_input_chk()">
									<input type="text" id="ref_rev" name="ref_rev" style="width:22px;font-size:11;height:13pt;background-color:transparent;border:1px solid grey" title="MAJOR = actual change in design or in BoM&#013MINOR = document update" placeholder="rev">
								</div>
							</td>
							<td>
								<div id="Body">
									Type<FONT color="#EE0000">*</FONT>
								</div>
							</td>
							<td>
								<div id="InpBox">
									<select name="Doc_Type" id="Doc_Type" type="submit" title="" onchange="auto_doc()" style="font-size:11;height:17px;width:150px;background-color:transparent;border:1px solid grey">
										<option value=""></option>
										<!------------------------------>
										<?php
										include('../REL_Connexion_DB.php');
										$requete = "SELECT DISTINCT Doc_Type, Doc_Type_Description FROM tbl_doc_type ORDER BY Doc_Type DESC;";
										$resultat = $mysqli->query($requete);
										while ($row = $resultat->fetch_assoc()) {
											echo '<option value ="' . $row['Doc_Type'] . '">' . $row['Doc_Type'] . ' - ' . $row['Doc_Type_Description'] . '</option><br/>';
										}
										mysqli_close($mysqli);
										?>
										<!------------------------------>
									</select>
								</div>
							</td>
							<td>
								<div id="Body">
									Material:
								</div>
							</td>
							<td>
								<div id="InpBox">
									<input list="Material" name="Material" multiple id="fxxx" title="" style="width:100px;font-size:11px;height:13pt;background-color:transparent;border:1px solid grey">
									<datalist id="Material">
										<!--LISTE DEROULANTE DYNAMIQUE-->
										<!------------------------------>
										<?php
										include('../SCM_Connexion_DB.php');
										$requete = "SELECT DISTINCT fxxx_ref, fxxx_description FROM tbl_fxxx WHERE Status like 'ACTIVE' ORDER BY fxxx_ref DESC;";
										$resultat = $mysqli_scm->query($requete);
										while ($row = $resultat->fetch_assoc()) {
											echo '<option value ="' . $row['fxxx_ref'] . '">' . $row['fxxx_description'] . '</option><br/>';
										}
										mysqli_close($mysqli_scm);
										?>
										<!------------------------------>
									</datalist>

								</div>
							</td>
							<td>
								<div id="Body">
									Cust. Drawing
								</div>
							</td>
							<td>
								<div id="InpBox">
									<input type="text" id="cust_draw" size=18 name="cust_draw" style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey">
									<input type="text" id="cust_draw_rev" name="cust_draw_rev" style="width:22px;font-size:11;height:13pt;background-color:transparent;border:1px solid grey" placeholder="rev">
								</div>
							</td>
							<td rowspan=1 style="text-align:center;">
								<div id="Body">
									Comment(s) :
								</div>

								<div id="InpBox">
									<textarea rows="3" style="width:100%" style="font-size:11;background-color:transparent;border:1px solid grey" id="requestor_comments" name="requestor_comments" title="Provide the details of the change you want to be applied - The more info, the better it is" /></textarea><br />
								</div>
							</td>
						</tr>
						<tr>

							<td>
								<div id="Body">
									Invent. Impact<FONT color="#EE0000">*</FONT>
								</div>
							</td>
							<td>
								<div id="InpBox">
									<select name="Inventory_Impact" id="Inventory_Impact" type="submit" title="is the drawing/ref ex?" style="width:100px;font-size:11;height:17;background-color:transparent;border:1px solid grey">
										<option value=""></option>
										<!------------------------------>
										<?php
										include('../REL_Connexion_DB.php');
										$requete = "SELECT DISTINCT Inventory_Impact FROM tbl_inventory_impact ORDER BY Inventory_Impact DESC;";
										$resultat = $mysqli->query($requete);
										while ($row = $resultat->fetch_assoc()) {
											echo '<option value ="' . $row['Inventory_Impact'] . '">' . $row['Inventory_Impact'] . '</option><br/>';
										}
										mysqli_close($mysqli);
										?>
										<!------------------------------>
									</select>
								</div>
							</td>
							<td>
								<div id="Body">
									Title<FONT color="#EE0000">*</FONT>
								</div>
							<td>
								<div id="InpBox">
									<!-- Ajout d'une div pour faire un innerHTML -->
									<div id="digit_title">
										<input type="text" id="ref_title" size=25 name="ref_title" title="XX characters max - Description in English" style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey" onchange="title_check()">
									</div>
								</div>
							</td>
							<td>
								<div id="Body">
									Material Type<FONT color="#EE0000">*</FONT>
								</div>
							</td>
							<td>
								<div id="InpBox">
									<select name="Material_Type" id="Material_Type" type="submit" onchange="auto_material_type()" title="FINISHED PRODUCT = ZPF&#013LITTERATURE = Document &#013NON VALUATED MATERIAL = Cable, customer action etc..." style="font-size:11;height:17;background-color:transparent;border:1px solid grey">
										<option value=""></option>
										<!--LISTE DEROULANTE DYNAMIQUE-->
										<!------------------------------>
										<?php
										include('../REL_Connexion_DB.php');
										$requete = "SELECT DISTINCT Material_Type FROM tbl_material_type ORDER BY Material_Type ASC;";
										$resultat = $mysqli->query($requete);
										while ($row = $resultat->fetch_assoc()) {
											echo '<option value ="' . $row['Material_Type'] . '">' . $row['Material_Type'] . '</option><br/>';
										}
										mysqli_close($mysqli);
										?>
										<!------------------------------>
									</select>
								</div>
							</td>
							<td>
								<div id="Body">
									Plating Surf.:
								</div>
							</td>
							<td>
								<div id="InpBox">
									<input type="text" size=10 id="Plating_Surface_ID" name="Plating_Surface" title="For components, indicate the surface plated with a coating" style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey">
									<select id="Plating_Surface_Unit_ID" name="Plating_Surface_Unit_name" type="submit" title="" style="width:45px;font-size:11;height:17px;background-color:transparent;border:1px solid grey">
										<option value=""></option>
										<!--LISTE DEROULANTE DYNAMIQUE-->
										<!------------------------------>
										<?php
										include('../SCM_Connexion_DB.php');
										$requete = "SELECT DISTINCT Unit FROM tbl_unit WHERE Unit_Type like 'Surface' ORDER BY Unit DESC;";
										$resultat = $mysqli_scm->query($requete);
										while ($row = $resultat->fetch_assoc()) {
											echo '<option value ="' . $row['Unit'] . '">' . $row['Unit'] . '</option><br/>';
										}
										mysqli_close($mysqli_scm);
										?>
										<!------------------------------>
									</select>
								</div>
							</td>
							<td>
								<div id="Body">
									RDO / ECCN / HTS:
								</div>
							</td>
							<td>
								<div id="InpBox">
									<input list="RDO" name="RDO" id="RDO_id" title="" style="text-align:center;width:50px;font-size:11px;height:13pt;background-color:transparent;border:1px solid grey">
									<datalist id="RDO">
										<!------------------------------>
										<?php
										include('../SCM_Connexion_DB.php');
										$requete_2 = "SELECT DISTINCT * FROM tbl_rdo ORDER BY RDO DESC;";
										$resultat_2 = $mysqli_scm->query($requete_2);
										while ($row_2 = $resultat_2->fetch_assoc()) {
											echo '<option Title ="' . $row_2['Description'] . '" value ="' . $row_2['RDO'] . '">' . $row_2['Description'] . '<br/>';
										}
										mysqli_close($mysqli_scm);
										?>
										<!------------------------------>
									</datalist>

									<FONT style="font-weight:normal;font-size:9">/</font>

									<input list="ECCN" name="ECCN" id="ECCN_id" title="" style="width:75px;font-size:11px;height:13pt;background-color:transparent;border:1px solid grey">
									<datalist id="ECCN">
										<!------------------------------>
										<?php
										include('../SCM_Connexion_DB.php');
										$requete_2 = "SELECT DISTINCT * FROM tbl_eccn ORDER BY ECCN DESC;";
										$resultat_2 = $mysqli_scm->query($requete_2);
										while ($row_2 = $resultat_2->fetch_assoc()) {
											echo '<option Title ="' . $row_2['Description'] . '" value ="' . $row_2['ECCN'] . '">' . $row_2['Description'] . '<br/>';
										}
										mysqli_close($mysqli_scm);
										?>
										<!------------------------------>
									</datalist>

									<FONT style="font-weight:normal;font-size:9">/</font>
									<!-- <input type="text" id="ECCN" size=4 name="ECCN" style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey"> -->

									<input list="HTS" name="HTS" id="HTS_id" title="" style="width:85px;font-size:11px;height:13pt;background-color:transparent;border:1px solid grey">
									<datalist id="HTS">
										<!------------------------------>
										<?php
										include('../SCM_Connexion_DB.php');
										$requete_2 = "SELECT DISTINCT * FROM tbl_hts ORDER BY HTS DESC;";
										$resultat_2 = $mysqli_scm->query($requete_2);
										while ($row_2 = $resultat_2->fetch_assoc()) {
											echo '<option Title ="' . $row_2['Description'] . '" value ="' . $row_2['HTS'] . '">' . $row_2['Description'] . '<br/>';
										}
										mysqli_close($mysqli_scm);
										?>
										<!------------------------------>
									</datalist>

								</div>
							</td>
								<input type="hidden" id="ID_ALETIQ" size=10 name="ID_ALETIQ" style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey">

						</tr>
						<tr>


							<td>
								<div id="Body">
									Ex<FONT color="#EE0000">*</FONT>
								</div>
							</td>
							<td>
								<div id="InpBox">
									<select name="Ex" id="Ex" type="submit" title="is the drawing/ref ex?" style="width:54px;font-size:11;height:17;background-color:transparent;border:1px solid grey">
										<option value=""></option>

										<!------------------------------>
										<?php
										include('../SCM_Connexion_DB.php');
										$requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex DESC;";
										$resultat = $mysqli_scm->query($requete);
										while ($row = $resultat->fetch_assoc()) {
											echo '<option value ="' . $row['Ex'] . '">' . $row['Ex'] . '</option><br/>';
										}
										mysqli_close($mysqli_scm);
										?>
										<!------------------------------>
									</select>
								</div>
							</td>
							<td>
								<div id="Body">
									Prod. Draw.
								</div>
							</td>
							<td>
								<div id="InpBox">
									<input type="text" id="prod_draw" size=20 name="prod_draw" style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey">
									<input type="text" id="prod_draw_rev" size=1 name="prod_draw_rev" style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey" placeholder="rev">
								</div>
							</td>
							<td>
								<div id="Body">
									Alias
								</div>
							</td>
							<td>
								<div id="InpBox">
									<!-- Ajout d'une div pour faire un innerHTML -->
									<div id="digit_alias">
										<input type="text" id="Alias" name="Alias_name" size=22 title="Catalogue number - Examples: &#013 9316-51H 61-21 PN 8 &#013 276-8203-64 GT &#013 P18-SW400-HARN-0025 &#013 etc..." style="font-size:11;height:13pt;background-color:transparent;border:1px solid grey" onchange="alias_check()">
									</div>
								</div>
							</td>

							<td>
								<div id="Body">
									Weight (air):
								</div>
							</td>
							<td>
								<input type="text" id="Weight" size=10 name="Weight" style="margin-left:2px;vertical-align:middle;font-size:11;height:13pt;background-color:transparent;border:1px solid grey">
								<select id="Weight_Unit_ID" name="Weight_Unit_name" type="submit" title="" style="width:35px;font-size:11;height:17;vertical-align:middle;background-color:transparent;border:1px solid grey">
									<option value=""></option>
									<!--LISTE DEROULANTE DYNAMIQUE-->
									<!------------------------------>
									<?php
									include('../SCM_Connexion_DB.php');
									$requete = "SELECT DISTINCT Unit FROM tbl_unit WHERE Unit_Type like 'Weight' ORDER BY Unit DESC;";
									$resultat = $mysqli_scm->query($requete);
									while ($row = $resultat->fetch_assoc()) {
										echo '<option value ="' . $row['Unit'] . '">' . $row['Unit'] . '</option><br/>';
									}
									mysqli_close($mysqli_scm);
									?>
									<!------------------------------>
								</select>
							</td>
							<td colspan=2>
								<div id="Body">
									Recommanded Inhouse Manuf.
									<input type="checkbox" id="inhouse_manuf" name="inhouse_manuf" value=1 title="Pick that option if the part is critical and internal machinning expertise preferred. &#013 Could be applicable to glass-to-metal seal bodies, PEEK insulator etc...">
								</div>
							</td>
							<td style="text-align:center">
								<div id="btn_update">
									<input type="submit" id="validate_new_row" name="validate_new_row" class="btn red" onclick="return limit_check()" style="display:none;font-size:11; vertical-align:middle;height:20px;width:80%" value="UPDATE" title="Delete the ref/drawing from the current release package" />
								</div>
							</td>
						</tr>
					</table>

				</td>
			</tr>
			<tr>
				<td colspan=2 style="height:25;border-top:0.5px black dotted;">
					<div id="Frame_Title">
						Release Package Overview
					</div>
				</td>
			</tr>


			<tr>
				<td colspan=3>
					<?php
					echo '<iframe name="Main_target" id="Main_target" class="main_frame" alt="ok" src="./REL_Package_Content.php?Rel_Pack_Num=' . $_GET['ID'] . '" frameborder="1" style="text-align:middle" ></iframe>';
					?>
				</td>
			</tr>
		<?php } ?> <!-- NE PAS EFFACER !!!!!! -->


		</body>

</html>