<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html>

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<link rel="stylesheet" type="text/css" href="REL_BE_2_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<head>

<script>
function form_validation(next_step)
{
	var next_str=3;
	if (next_step==next_str)
	{ 
		var msg = new String("Please confirm you want to sign off this package and send it to validation.");
	} else {
		var msg = new String("Please confirm you want to send this package back to the owner for modification/update.");
	}
	
	msg=msg + '\n\t Click OK to confirm or Cancel to stop the action.';
	const verif_name=document.getElementById("Verification_fullname_id").value;
	const valid_name=document.getElementById("Validation_owner_fullname_id").value;

	if (((verif_name!="" && valid_name!="") && next_step==next_str) || next_step!=next_str)
	{

			var a=confirm(msg);
			if (a)
			{
				
			} else {
				return false;
			}
	} else {
		alert("Please make sure to sign off the form and indicate the validation owner of that package");
		return false;
		} 
		
}

function page_update_validation(rel_pack)
{
	alert("The package " + rel_pack + " has been sent to validation.\nYou are about to be redirected to the Engineering main page");
	let win_path=window.parent.location.href;
	win_path = win_path.replace('REL_BE_2_Form','REL_BE_Form');
	window.parent.document.location=win_path;
}

function page_update_back_be_1(rel_pack)
{
	alert("The package " + rel_pack + " has been sent back to package owner for modification/update.\nYou are about to be redirected to the Engineering main page");
	let win_path=window.parent.location.href;
	win_path = win_path.replace('REL_BE_2_Form','REL_BE_Form');
	window.parent.document.location=win_path;
}



</script>

<title>

</title>

</head>

<body>

<form enctype="multipart/form-data" action="" method="post">

<table id="t03" border=0>
    <tr>
		<td HIDDEN>
			<input type="text" id="id_to_update" size=2 name="id_to_update" style="font-size:11;height:13pt;" >
		</td>
        <td style="min-width:120px">
            <div id="Body">To be approved by:</div>
        </td>
        <td>
            <div id="InpBox">
            <select name="Validation_owner_fullname" id="Verification_fullname_id" type="submit" title="" style="width:100px;font-size:12"> 
            <option value=""></option>';

            //------------------------------>
                <?php
                include('../SCM_Connexion_DB.php');
                $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Eng. Manager' ORDER BY Fullname DESC;";
                $resultat = $mysqli_scm->query($requete);
                while ($line = $resultat->fetch_assoc())
                {
                    echo'<option value ="'.$line['Fullname'].'">'.$line['Fullname'].'</option><br/>'; 
                }?>
            //------------------------------>
            </select>
            </div> 
        </td>
        <td rowspan="2" style="margin-left:10px;text-align:center; ">
            <div id="InpBox">
            <div id="InpBox">
                <input class="btn blue" onclick="return form_validation(3)" type="submit" style="width:100%; font-size:10; vertical-align:middle;height:30px;" name="Send_Approval" value="Send to Validation" title="Send the current release package to final approval" />
            </div>
			
        </td>
    </tr>
    <tr>
        <td>
            <div id="Body">
                Sign off (your name):
            </div>
        </td>
        <td>
            <div id="InpBox">
                <select name="Verification_fullname" id="Validation_owner_fullname_id" type="submit" title="" style="width:120px;font-size:12"> 
                <option value=""></option>';
                <?php
                    $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Method' or Department like 'Laboratory' ORDER BY Fullname ASC;";
                    $resultat = $mysqli_scm->query($requete);
                    while ($line = $resultat->fetch_assoc())
                    {
                        echo'<option value ="'.$line['Fullname'].'">'.$line['Fullname'].'</option><br/>'; 
                    }
                    mysqli_close($mysqli_scm);
                ?>
                </select>
            </div> 
        </td>     
    </tr>

    <tr>
        <td colspan=2>
            <div id="Body" style="text-align:left">
                Click to send the package to final validation
            </div>
        </td>
		<td style="text-align:center;">
			<div id="InpBox">
                <input class="btn red" onclick="return form_validation(0)" type="submit" style="font-size:11;vertical-align:middle;height:30px;" name="Send_back_BE_1" value="Back to Package Owner" title="Send the current release package back to the owner for modification" />
			</div>
		</td>
    </tr>
</table>

</form>



<?php
if (isset($_POST['Send_Approval']) && isset($_POST['id_to_update']))
{
	$validation_owner=$_POST['Validation_owner_fullname'];
	$visa_be_2=$_POST['Verification_fullname'];
	$date_be_2=date("Y-m-d");
	$id_to_update=$_POST['id_to_update'];
	$sql='UPDATE tbl_released_package 
		  SET 
			VISA_BE_2 = "'.$visa_be_2.'",
			DATE_BE_2 = "'.$date_be_2.'",
			BE_3_Req_Owner = "'.$validation_owner.'"
		  WHERE Rel_Pack_Num like "'.$id_to_update.'";
		  ';
	
	include('../REL_Connexion_DB.php');

	$result = $mysqli->query($sql);
	mysqli_close($mysqli);
	echo '<script>page_update_validation('.$id_to_update.');</script>';
}

if (isset($_POST['Send_back_BE_1']) && isset($_POST['id_to_update']))
{
	$creation_Date="0000-00-00";
	$creation_VISA="";
	$id_to_update=$_POST['id_to_update'];
	$sql='UPDATE tbl_released_package 
		  SET 
			Creation_Date = "'.$creation_Date.'",
			Creation_VISA = "'.$creation_VISA.'"
		  WHERE Rel_Pack_Num like "'.$id_to_update.'";
		  ';
	include('../REL_Connexion_DB.php');
	//echo '<script>alert("'.$sql.'");</script>';
	$result = $mysqli->query($sql);
	mysqli_close($mysqli);
	echo '<script>page_update_back_be_1('.$id_to_update.');</script>';
}
?>

</body>
</html>