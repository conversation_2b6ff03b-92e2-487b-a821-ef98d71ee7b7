<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html>

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<link rel="stylesheet" type="text/css" href="REL_BE_file_Overwritting_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<head>

<script>
function ref_pack_list()
{
	const xhttp = new XMLHttpRequest();
	  xhttp.onload = function() 
		{
			const raw_result=this.responseText.trim();
			var split_raw=raw_result.split("||");

			if(parseInt(split_raw[0])>0)
			{
				var raw_ref_list=split_raw[1].split("__");
				var raw_ref_rev_list=split_raw[2].split("__");
				var raw_pack_list=split_raw[3].split("__");
				var draw_path=document.getElementById("listed_file_name").value;
				let final_table='<table id="t03"><tr style="height:12px"><th style="width:50px">Pack#</th><th style="width:190px">Ref</th><th style="width:50px">Rev</th></tr>';
				for (let i = 0; i < (raw_ref_list.length-1); i++)
				{
					final_table=final_table +'<tr><td><a href="REL_Pack_Overview.php?ID=' + raw_pack_list[i] + '" target="_blank">' + raw_pack_list[i] + "</a></td>";
					final_table=final_table +'<td><a target="_blank" href="DRAWINGS/IN_PROCESS/' + draw_path + '" target="_blank">' + raw_ref_list[i] + "</a></td>";
					final_table=final_table +"<td>" + raw_ref_rev_list[i] + "</td>";
					final_table=final_table + "</tr>";
				}
				if (raw_ref_list.length>=5)
				{
					final_table=final_table +'<tr><td colspan=3 style="size:10;text-decoration:italic;text-align:center">and more ...</td></tr>';
				}
				final_table=final_table + "</table>";

                    document.getElementById("impacted_ref").innerHTML=final_table;

            }

		}
	const ref_draw=document.getElementById("listed_file_name").value;
	xhttp.open("GET", "REL_BE_File_Overwritting_Autofill.php?ref=" + ref_draw);
	xhttp.send();
		
}



function prod_draw_update(){
    let file_name = document.getElementById("prod_drawing_file").files[0]['name'];
    var combo_name =  document.getElementById("listed_file_name").value;
    //alert(file_name);
    //alert(file_name + " " + combo_name);

    if(file_name == combo_name){
       
    }else{
        document.getElementById("prod_drawing_file").value = "";
		alert("The files must have the same name !");
    }
}

// function envoie(){
	// alert("test");
    // document.getElementById("Package_owner_fullname").removeAttribute("required");
    // document.getElementById("Activity").removeAttribute("required");
    // document.getElementById("Ex").removeAttribute("required");
    // document.getElementById("Project").removeAttribute("required");
// }
</script>

    <?php
	$msg="";
    if(isset($_POST["valid_form"])){
    // ISOLEMENT DE LA REF DU FICHIER/PLAN CHOISI
            if (isset($_FILES['prod_drawing_file']) )
            {
                //$drawing_file_name=$_FILES['prod_drawing_file']['tmp_name'][0];
                $drawing_file_name=basename($_FILES['prod_drawing_file']['name'][0]);
            } else {
                $drawing_file_name="";
            }

        // MISE A JOUR DU FICHIER DEJA ASSOCIE DANS LA BASE DE DONNEE ET SUR LE SERVEUR

        if ($drawing_file_name!="") // Confirmer que l'utilisateur a désigné un fichier
        {
			
			include('../REL_Connexion_DB.php');
            $ref_draw = $_POST["listed_file_name"];
            $sql_file='SELECT Drawing_Path FROM tbl_released_drawing WHERE Drawing_Path like "'.$ref_draw.'"';

            $result = $mysqli->query($sql_file);
            while ($row = $result->fetch_assoc())
            {
                $file_path_db=$row['Drawing_Path'];
            }
            $file_path_input_user=basename($_FILES['prod_drawing_file']['name'][0]);

			$path_attachment =  $_SERVER['DOCUMENT_ROOT'] . "\REL\DRAWINGS\IN_PROCESS\\";
           // $path_attachment="DRAWINGS\IN_PROCESS\\";

            if ($file_path_db!="")												// SUPPRESION DU FICHIER EXISTANT S'IL EXISTE
            {
                $file_to_delete=$path_attachment.$file_path_db;			// Chemin complet du fichier existant
                unlink($file_to_delete);										// Suppression
            }

            $tmp_file = $_FILES['prod_drawing_file']['tmp_name'][0];
            move_uploaded_file($tmp_file, $path_attachment . basename($_FILES['prod_drawing_file']['name'][0])); // COPIE DU NOUVEAU FICHIER
			clearstatcache();

            $sql_file_update='UPDATE tbl_released_drawing						
									  SET Drawing_Path = "'.$file_path_input_user.'"
									  WHERE Drawing_Path like "'.$ref_draw.'"';               // MISE A JOUR DE LA BASE DE DONNEE AVE CLE NOUVEAU NOM DE FICHIER

            $result_file_update = $mysqli->query($sql_file_update);
			mysqli_close($mysqli);
			
			
			
			$msg=" File successfully overwritten!";
        }
    }
    
    ?>

<title>
	REL / File Overwritting Form
</title>


</head>

<body>
    <form enctype="multipart/form-data" action="" method="post">

    <table border="0" id="t02">
        <tr>
			<td style="width:170px;text-align:left">
				Select the to-be-overwriten file:
			</td>
            <td>
				<div id="InpBox">
					<select tabindex="8" name="listed_file_name" id="listed_file_name" type="submit" title="" onchange="ref_pack_list()" style="width:170px;font-size:11;height:17">
					<option value=""></option>
					<?php
					$scandir = scandir(".\DRAWINGS\IN_PROCESS");
					
					foreach($scandir as $fichier)
					{
						if(preg_match("#\.(jpg|jpeg|png|gif|bmp|tif|pdf)$#",strtolower($fichier)))
						{
							echo '<option value="'.$fichier.'">'.$fichier.'</option><br/>';		
						}
					}
					?>
            </td>
            <td rowspan="3" style="vertical-align:top;">
				<span id="impacted_ref">
					Impacted package(s) list ...
				</span>
            </td>
		</tr>
		<tr>
			<td style="text-align:left">
				Pick the new file:
			</td>
			<td>
				<input tabindex="1" style="width:180px;" id="prod_drawing_file" name="prod_drawing_file[]" type="file" accept=".pdf" onchange="prod_draw_update()">
			</td>
        </tr>
        <tr>
			<td>
			</td>
            <td style="vertical-align:top">
                <input style="vertical-align:middle;width:120px;height:18px;" name="valid_form" type="submit" id="valid_form" class="btn blue" value="Update" title="Change old plan for new plan" />
            </td>
			<td style="color:red;text-align:left;margin-left:10px">
				<?php
				if ($msg!="")
				{
					echo $msg;
				}
				?>
			</td>
        </tr>
    </table>
    </form>
</body> 
</html>


