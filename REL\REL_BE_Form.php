<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html translate="no">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<link rel="stylesheet" type="text/css" href="REL_BE_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<head>

<script>

	function Update_Open(obj)
	{
		const rel_pack_num=obj.cells[0].textContent.trim();
		if (rel_pack_num!="")
		{
			window.open("REL_BE_1_Form.php?ID=" + rel_pack_num);
			// POUR OUVERTURE DIRECTEMENT DANS LA PAGE D'APPEL
			//window.location.assign("REL_BE_1_Form.php?ID=" + rel_pack_num)
		}
	}
	
	function Verification_Open(obj)
	{
		const rel_pack_num=obj.cells[0].textContent.trim();
		if (rel_pack_num!="")
		{
			//window.open("REL_BE_2_Form.php?ID=" + rel_pack_num);
			// POUR OUVERTURE DIRECTEMENT DANS LA PAGE D'APPEL
			window.location.assign("REL_BE_2_Form.php?ID=" + rel_pack_num)
			
		}
	}
	
	
	function Validation_Open(obj)
	{
		const rel_pack_num=obj.cells[0].textContent.trim();
		if (rel_pack_num!="")
		{
			//window.open("REL_BE_3_Form.php?ID=" + rel_pack_num);
			//alert("VALIDATION PAGE NOT EXISTING YET !!");
			// POUR OUVERTURE DIRECTEMENT DANS LA PAGE D'APPEL
			window.location.assign("REL_BE_3_Form.php?ID=" + rel_pack_num)
		}
	}
	
</script>

<title>
	<?php echo 'REL / Package Update & Number Reservation';?>
</title>

<?php
	// Definition de la racine de la requete pour afficher les listes de diff en creation/verif/validation
	$sql_racine='SELECT tbl_released_package.*, count_table.Counter
				 FROM tbl_released_package
				 LEFT JOIN (SELECT tbl_released_drawing.Rel_Pack_Num, count(*) as "Counter" 
							FROM tbl_released_drawing
							GROUP BY tbl_released_drawing.Rel_Pack_Num) 
				 AS count_table 
				 ON tbl_released_package.Rel_Pack_Num = count_table.Rel_Pack_Num
				 WHERE ';
	// Defintion des conditions pour les requetes
	$sql_update_condition=
		   'Creation_VISA like "" 
			AND VISA_BE_2 like ""
			AND VISA_BE_3 like ""';
	$sql_verification_condition=
		   'Creation_VISA not like "" 
			AND VISA_BE_2 like ""
			AND VISA_BE_3 like ""';
	$sql_validation_condition=
		   'Creation_VISA not like "" 
			AND VISA_BE_2 not like ""
			AND VISA_BE_3 like ""';
	$sql_order=
			' ORDER BY tbl_released_package.creation_Date ASC, tbl_released_package.Rel_Pack_Num ASC';
?>

</head>

<body>


    <form enctype="multipart/form-data" action="" method="post">
    <table border=0 style="width:99%">
        <tr>
            <td colspan=2 style="min-width:600px;">
                <div id="Title">
                    Release Package Creation Menu
                </div>
            </td>
            <td style="text-align:right">
                <!--<img src="\Common_Resources\scm_logo.png" height="35">-->
            </td>
        </tr>

		<tr >
            <td stytle="border-bottom:1px dotted black">
                <table style="width:100%">
                    <tr>
                        <td style="vertical-align:top">
						  <div id="Title_2" >Engineering Package Overivew</div>
							<table id="t02" style="margin-top:12px; margin-right:20px">
								<tr>
									<th>Creation</th>
									<th>Verification</th>
									<th>Validation</th>
									<th>Total</th>
									</tr>
								<tr>
									<?php
										$total_count=0;
										include('../REL_Connexion_DB.php');
										$sql_count_table=
											'SELECT count(*) as "counter"
											FROM tbl_released_package 
											WHERE '.$sql_update_condition.'
											UNION ALL
											SELECT count(*)
											FROM tbl_released_package 
											WHERE '.$sql_verification_condition.'
											UNION ALL
											SELECT count(*)
											FROM tbl_released_package 
											WHERE '.$sql_validation_condition;
										$resultat = $mysqli->query($sql_count_table);
										while ($row = $resultat->fetch_assoc())
										{
											$count=(int)$row['counter'];
											$total_count=$total_count+(int)$row['counter'];
											echo '<td style="text-align:center;">'.$count.'</td>';
										}
										echo '<td style="text-align:center;">'.$total_count.'</td>';
										mysqli_close($mysqli);
									?>
								</tr>
							</table>
						</td>
					</tr>
				</table>
			</td>
			
		</tr>

<tr>
	<td>
		<br>
		<br>
		<table style="width:99%;">
			<tr>
				<td style="width:50%;border-right:0.5px dotted black">
					<div id="Title_2">Package Number RESERVATION</div>
					<div id="body">Fill in the fields below to get a new package number</div>
				</td>
				<td style="min-width:600px;">
					<div id="Title_2">Package VERIFICATION</div>
					<div id="body">List of packages waiting for verification</div>
				</td>
			</tr>
        	<tr>
				<td style="vertical-align:top;border-right:0.5px dotted black;">
					<?php include('REL_New_Package_Reservation.php');?>
				</td>
        
				<td style="vertical-align:top;">
					<table id="t01" border=1>
						<th style="width:10%">Pack. #</th>
						<th>Package Owner</th>
						<th>Project</th>
						<th>Activity</th>
						<th>DMO #</th>
						<th style="width:9%">Ex</th>
						<th Title="Date at which the package was released by the package owner">Creation date</th>
						<th Title="Responsible for the package validation as designated by the package owner">Verif. Owner</th>
						<th style="width:3%" Title="Number of drawing/lines">#</th>
						<?php include('../REL_Connexion_DB.php');
						$sql_verification=$sql_racine.$sql_verification_condition.$sql_order;
						$resultat = $mysqli->query($sql_verification);
						while ($row = $resultat->fetch_assoc())
						{
							if ($row['Creation_Date']!="0000-00-00")
							{
								$date1 = new DateTime($row['Creation_Date']);
								$date2 = new DateTime(date("Y-m-d"));
								$diff = $date2->diff($date1)->format("%a");
								//$diff=$diff-1;
								if ($diff>0)
								{
									
									$diff_final='('.$diff.')';
								} else { 
									$diff_final="";
								}
							} else {
								$diff_final="";
							}
							echo '	<tr onclick="Verification_Open(this)">
										<td>'.$row['Rel_Pack_Num'].'</td>
										<td>'.$row['Rel_Pack_Owner'].'</td>
										<td>'.$row['Project'].'</td>
										<td>'.$row['Activity'].'</td>
										<td>'.$row['DMO'].'</td>
										<td>'.$row['Ex'].'</td>
										<td Title="Date at which the package was released by the package owner">'.$row['Creation_Date'].' <FONT style="font-size:7pt">'.$diff_final.'</FONT></td>
										<td Title="Responsible for the package validation as designated by the package owner">'.$row['Verif_Req_Owner'].'</td>
										<td Title="Number of drawings/lines in the package">'.$row['Counter'].'</td>
									</tr>';
						}
						mysqli_close($mysqli);
						?>
					</table>
				</td>
        	</tr>
			<tr>
				<td style="border-right:0.5px dotted black;">
					<div id="Title_2">Package UPDATE</div>
					<div id="body">Pick the package you want to keep populating</div>	
				</td>
				<td>
					<div id="Title_2">Package VALIDATION</div>
					<div id="body">List of packages waiting for final validation</div>
				</td>
			</tr>
			<tr>
				<td style="vertical-align:top; border-right:0.5px dotted black">				
					<table id="t01" border=1>
						<th>Pack. #</th>
						<th>Package Owner</th>
						<th>Project</th>
						<th>Activity</th>
						<th>DMO #</th>
						<th>Ex</th>
						<th style="width:15%" Title="Date at which the package owner reserved the package number">Reservation date</th>
						<th style="width:3%" Title="Number of drawing/lines">#</th>
						<?php include('../REL_Connexion_DB.php');
							
						$sql_update=$sql_racine.$sql_update_condition.$sql_order;
						$resultat = $mysqli->query($sql_update);
						while ($row = $resultat->fetch_assoc())
						{
							echo '	<tr onclick="Update_Open(this)">
										<td>'.$row['Rel_Pack_Num'].'</td>
										<td>'.$row['Rel_Pack_Owner'].'</td>
										<td>'.$row['Project'].'</td>
										<td>'.$row['Activity'].'</td>
										<td>'.$row['DMO'].'</td>
										<td>'.$row['Ex'].'</td>
										<td Title="Date at which the package owner reserved the package number">'.$row['Reservation_Date'].'</td>
										<td Title="Number of drawings/lines in the package">'.$row['Counter'].'</td>

									</tr>';
							
						}
						mysqli_close($mysqli);
						?>
					</table>
				</td>
				<td style="vertical-align:top;">
					<table id="t01" border=1>
						<th>Pack. #</th>
						<th>Package Owner</th>
						<th>Validation Owner</th>
						<th>Project</th>
						<th>Activity</th>
						<th>DMO #</th>
						<th>Ex</th>
						<th>Creation date</th>
						<th>Verif date</th>
						<th style="width:3%" Title="Number of drawing/lines in the package">#</th>
						<?php include('../REL_Connexion_DB.php');
						$sql_validation=$sql_racine.$sql_validation_condition.$sql_order;
						$resultat = $mysqli->query($sql_validation);
						while ($row = $resultat->fetch_assoc())
						{
							if ($row['Creation_Date']!="0000-00-00")
							{
								$date1 = new DateTime($row['DATE_BE_2']);
								$date2 = new DateTime(date("Y-m-d"));
								$diff = $date2->diff($date1)->format("%a");
								//$diff=$diff-1;
								if ($diff>0)
								{
									
									$diff_final='('.$diff.')';
								} else { 
									$diff_final="";
								}
							} else {
								$diff_final="";
							}
							
							echo '	<tr onclick="Validation_Open(this)">
										<td>'.$row['Rel_Pack_Num'].'</td>
										<td>'.$row['Rel_Pack_Owner'].'</td>
										<td>'.$row['BE_3_Req_Owner'].'</td>
										<td>'.$row['Project'].'</td>
										<td>'.$row['Activity'].'</td>
										<td>'.$row['DMO'].'</td>
										<td>'.$row['Ex'].'</td>
										<td>'.$row['Creation_Date'].'</td>
										<td>'.$row['DATE_BE_2'].' <FONT style="font-size:7pt">'.$diff_final.'</FONT></td>
										<td Title="Number of drawings/lines in the package">'.$row['Counter'].'</td>
									</tr>';
							
						}
						mysqli_close($mysqli);
						?>
					</table>
				</td>
			</tr>
		</table>
	</td>
</tr>
    
</table>
</form>
</body> 
</html>