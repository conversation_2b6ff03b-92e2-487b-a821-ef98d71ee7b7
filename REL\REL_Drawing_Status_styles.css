body {
    color: black;
    font-size: 8pt;
    font-family: Arial, sans-serif;
    font-weight: normal;
}

@media screen and (min-width: 1370px) {
    div.format {
        font-size: 9pt;
    }
}

@media screen and (max-width: 1369px) {
    div.format {
        font-size: 8pt;
    }
}

#Main_Title {
    margin-left: 5px;
    margin-top: 5px;
    text-align: left;
    margin-bottom: 10px;
    margin-left: 10px;
    vertical-align: middle;
    font-size: 12pt;
    font-weight: bolder;
}

#FilterTitle {
    margin-left: 2px;
    margin-right: 2px;
    text-align: center;
    vertical-align: middle;
    background: transparent;
}

div#Result_info {
    text-indent: 10px;
    margin-left: 8px;
    margin-bottom: 8px;
    margin-top: 10px;
    text-align: justify;
}

/*TABLEAU PRINCIPAL DE LA PAGE*/
/*-----------------------------*/
#t01 {
    width: 100%;
    border-collapse: collapse;
    vertical-align: middle;
}

#t01 th {
    border: 0.5px solid black;
    background-color: rgb(27, 79, 114);
    color: white;
}

#t01 td {
    text-align: left;
    vertical-align: middle;
}

#t01 tr {
    height: 20px;
}


/*TABLEAU DONNEES*/
/*---------------*/

#t02 {
    border-collapse: collapse;
    width: 100%;
}

#t02 th {
    height:15px;
    background-color: transparent;
    color: white;
    font-family: Arial, Helvetica, sans-serif;
    text-align: center;
    /*width:calc((100% - 504px) / 13);*/
    border-bottom:1px solid black;
    width: 5%;
}

#t02 td {
    text-align: center;
    vertical-align: middle;
    border: 1px solid white;
    border-radius:10px;
    height: 35px;
   /* width: 5%;*/
}

#t02 tr {


}


div#Table_results {
    text-align: center;
    color: black;
}

/*DATE*/
#date{
    font-size: 6pt;
}
