<?php
    // require('login.php');
    // login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html>

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<link rel="stylesheet" type="text/css" href="REL_ECCN_RDO_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<link rel="icon" type="image/png" href="\Common_Resources\Release_Icon.png" />




<script>


function select_filling(select_to_fill,  text_list, title_list)
{
	i=1;
	while (i<=text_list.length)
	{
		var option = document.createElement("option");
		option.value="Q2" + (i-1);
		option.text=text_list[i-1];
		option.title=title_list[i-1];
		select_to_fill.add(option);
		i=i+1;
	}
}


function select_cleanup(list_to_clean)
{
	while (list_to_clean.options.length > 0)
		{
			list_to_clean.remove(0);
		}
}




function hts_description_lookup()
{
	// to be finished --> AJAX TO LOOK INTO TBL_HTS  IN DB_SCM
}



function Q1_select_update_eccn()
	{
		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				var q1_list=document.getElementById("Q1_eccn");
				
				let text_result_list="";
				let title_result_list="";
				let split_result="";
				
				const raw_result=this.responseText.trim();
				
				split_result=raw_result.split("//");
				
				text_result_list = split_result[0];
				text_result=text_result_list.split("|");
				title_result_list = split_result[1];
				title_result=title_result_list.split("|");
				
				let i=0;
				while (i<(text_result.length))
				{
					var option = document.createElement("option");
					option.value="Q1" + (i);
					option.text=text_result[i];
					option.title=title_result[i];
					q1_list.add(option);
					i=i+1;
				}
					
			}
		  xhttp.open("GET", "REL_ECCN_RDO_Questions.php?filled_select=Q1");
		  xhttp.send();	
	}
	
	
function Q2_select_update_eccn()
	{
		var q1_list=document.getElementById("Q1_eccn");
		var q2_list=document.getElementById("Q2_eccn");
		var q3_list=document.getElementById("Q3_eccn");
		var picked_item_text = q1_list.options[q1_list.selectedIndex].text;
		
		q3_list.hidden = true;
		q2_list.hidden = true;
		
		select_cleanup(q2_list);
		select_cleanup(q3_list);
		
		//parent.document.getElementById("ECCN_id").value="";
		
		document.getElementById("ECCN_id").value="";
		document.getElementById("RDO_id").value="";
		//document.getElementById("eccn_desc_id").innerHTML="";
		document.getElementById("q3_content_eccn").innerHTML="";
		document.getElementById("q2_content_eccn").innerHTML="";

		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				
				let text_result_list="";
				let title_result_list="";
				let split_result="";

				const raw_result=this.responseText.trim();
				
				split_result=raw_result.split("//");
				
				text_result_list = split_result[0];
				text_result=text_result_list.split("|");
				
				if (text_result_list!="ECCN")
				{
					
					title_result_list = split_result[1];
					title_result=title_result_list.split("|");
					
					description_result = split_result[2];
					
					let i=0;
					while (i<(text_result.length))
					{
						var option = document.createElement("option");
						option.value="Q2" + (i);
						option.text=text_result[i];
						option.title=title_result[i];
						q2_list.add(option);
						i=i+1;
					}
					
					
					let style_start="<div id=\"hts_eccn\">";
					let style_end="</div>";
					
					document.getElementById("q2_content_eccn").innerHTML=style_start + description_result + style_end;
					q2_list.hidden = false;
					
				} else {
					
					document.getElementById("ECCN_id").value=split_result[1];
		
					document.getElementById("eccn_desc_id").innerHTML=split_result[2];
					q2_list.hidden = true;
					
					RDO_update();
				}
		
			}
		  xhttp.open("GET", "REL_ECCN_RDO_Questions.php?filled_select=Q2&Q1_val=" + picked_item_text);
		  xhttp.send();	
	}
	
function Q3_select_update_eccn()
	{
		var q1_list=document.getElementById("Q1_eccn");
		var q2_list=document.getElementById("Q2_eccn");
		var q3_list=document.getElementById("Q3_eccn");
		
		let style_start="<div id=\"hts_eccn\">";
		let style_end="</div>";
		
		var picked_item_text_q1 = q1_list.options[q1_list.selectedIndex].text;
		var picked_item_text_q2 = q2_list.options[q2_list.selectedIndex].text;
		
		select_cleanup(q3_list);
		
		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				let text_result_list="";
				let title_result_list="";
				let split_result="";
				
				const raw_result=this.responseText.trim();
				
				split_result=raw_result.split("//");
				
				text_result_list = split_result[0];
				text_result=text_result_list.split("|");
				
				if (text_result_list!="ECCN")
				{
					title_result_list = split_result[1];
					title_result=title_result_list.split("|");
					
					description_result = split_result[2];
					
					let i=0;
					while (i<(text_result.length))
					{
						var option = document.createElement("option");
						option.value="Q3" + (i);
						option.text=text_result[i];
						option.title=title_result[i];
						q3_list.add(option);
						i=i+1;
					}
					
					
					document.getElementById("q3_content_eccn").innerHTML=style_start + description_result + style_end;
					q3_list.hidden = false;
					document.getElementById("ECCN_id").value="";
		
					document.getElementById("eccn_desc_id").innerHTML="";
					
				} else {
					document.getElementById("ECCN_id").value=split_result[1];
		
					document.getElementById("eccn_desc_id").innerHTML=split_result[2];
					
					RDO_update();
					
				}
				
		
			}
			
		  xhttp.open("GET", "REL_ECCN_RDO_Questions.php?filled_select=Q3&Q1_val=" + picked_item_text_q1 + "&Q2_val=" + picked_item_text_q2);
		  xhttp.send();	
		
	}
	
	function Q3_Final_eccn()
	{
		var q1_list=document.getElementById("Q1_eccn");
		var q2_list=document.getElementById("Q2_eccn");
		var q3_list=document.getElementById("Q3_eccn");
		
		let style_start="<div id=\"hts_eccn\">";
		let style_end="</div>";
		
		var picked_item_text_q1 = q1_list.options[q1_list.selectedIndex].text;
		var picked_item_text_q2 = q2_list.options[q2_list.selectedIndex].text;
		var picked_item_text_q3 = q3_list.options[q3_list.selectedIndex].text;
		
		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				let title_result_list="";
				let split_result="";
				
				const raw_result=this.responseText.trim();
				
				split_result=raw_result.split("//");
				text_result_list = split_result[0];
				
				document.getElementById("ECCN_id").value=split_result[1];
						
				document.getElementById("eccn_desc_id").innerHTML=split_result[2];
				
				RDO_update();
				
				
				
			}
		  xhttp.open("GET", "REL_ECCN_RDO_Questions.php?filled_select=final&Q1_val=" + picked_item_text_q1 + "&Q2_val=" + picked_item_text_q2 + "&Q3_val=" + picked_item_text_q3);
		  xhttp.send();
	}
		
		
		
	function RDO_update()
	{
		const eccn_val=document.getElementById("ECCN_id").value;
		
		if (eccn_val!="")
		{
			const xhttp = new XMLHttpRequest();
			  xhttp.onload = function()
				{
					const raw_result=this.responseText.trim();
					document.getElementById("RDO_id").value=raw_result;
				}

			  const activity=document.getElementById("Activity").value;
			  xhttp.open("GET", "REL_RDO_auto.php?eccn=" + eccn_val + "&Activity=" + activity);
			  xhttp.send();
		} else {
			document.getElementById("RDO_id").value="";
		}
		
	}

</script>

<head>

<body>

	<SCRIPT>Q1_select_update_eccn()</script>
    
	<table id="t01_eccn_rdo" border=0>
	<tr>
		<td style="vertical-align:top;width 30%">
			<div id="Body_eccn_rdo">
				Classification: 
			</div>
		</td>
		<td style="vertical-align:middle;text-align:right">
				<div class="dropdown_checkbox_BE">
					<img src="\Common_Resources\icon_info_2.png" height="15px" style="cursor:help"></img>
					<span class="dropdown_checkbox_BE-content" style="text-align:left;width:430px">
						<div style="font-weight:bold;margin-bottom:5px">Classification: </div>
						<div style="margin-bottom:2px">Answer the question below to set the proper classification codes:</div>
						<div style="text-indent:12px">- RDO code: Indicate the division responsible for the design of the ref.</div>
						<div style="text-indent:12px">- ECCN code: Indicate the military / dual use / civil use classification of the ref.</div>
					</span>
				</div>
			</div>
		</td>
	</tr>
	<tr>
		<td colspan=2>
			<div id="hts_eccn">
				The reference is:  
			</div>
		</td>
	</tr>
	<tr>
		<td colspan=2>
			<div id="InpBox_eccn_rdo">
			<select name="Q1_eccn" id="Q1_eccn" type="submit" title="" style="font-size:11;height:17;width:100%" onchange="Q2_select_update_eccn()">
				<option value=""></option>
			</select>
			</div> 
		</td>
	</tr>
	<tr>
		<td colspan=2>
			<div id="hts_eccn">
				<p id="q2_content_eccn">
				
				</p>
			</div>
		</td>
	</tr>
	<tr>
		<td colspan=2>
			<div id="InpBox_eccn_rdo">
			<select hidden name="Q2_eccn" id="Q2_eccn" type="submit" title="" style="font-size:11;height:17;width:100%;"  onchange="Q3_select_update_eccn()"> 
				<option value=""></option>
			</select>
			</div> 
		</td>
	</tr>
	<tr>
		<td colspan=2>
			<div id="hts_eccn">
				<p id="q3_content_eccn">
			
				</p>
			</div>
		</td>
	</tr>
	<tr>
		<td colspan=2>
			<div id="InpBox_eccn_rdo">
			<select hidden name="Q3_eccn" id="Q3_eccn" type="submit" title="" style="font-size:11;height:17;width:100%" onchange="Q3_Final_eccn()"> 
				<option value=""></option>
			</select>
			</div> 
		</td>
	</tr>
	<tr>
		<td colspan=2>
			<div id="Body_eccn_rdo">
				<span class="dropdown_checkbox_BE" style="cursor:help">
					ECCN 
					<span class="dropdown_checkbox_BE-content" style="text-align:left;width:430px">
						<div style="font-weight:bold;margin-bottom:5px;cursor:help">ECCN Code</div>
						<div style="margin-bottom:5px">Code used to categorize the product based on its final application.</div>
						Answer the question(s) above so the tool sets the corresponding ECCN code for you.
					</span>
				</span>
				/
				<span class="dropdown_checkbox_BE" style="cursor:help">
					RDO codes 
					<span class="dropdown_checkbox_BE-content" style="text-align:left;width:430px;cursor:help">
						<div style="font-weight:bold;margin-bottom:5px;">RDO Code</div>
						<div style="margin-bottom:5px">Code used to indicate which division is responsible for the design of the reference.</div>
						Answer the question(s) above so the tool sets the corresponding RDO code for you.
					</span>
				</span>
			</div>
		</td>
	</tr>
	<tr>
		<td>
			
			<input list="eccn" name="eccn" id="ECCN_id" title="" style="text-align:center;width:85px;font-size:11;height:13pt;" onchange="RDO_update()"> 
			<datalist id="eccn">
			<!------------------------------>
			<?php
				include('../SCM_Connexion_DB.php');
				$requete_1 = "SELECT DISTINCT * FROM tbl_eccn ORDER BY ECCN DESC;";
				$resultat_1 = $mysqli_scm->query($requete_1);
				while ($row_1 = $resultat_1->fetch_assoc())
				{
					echo'<option Title ="'.$row_1['Description'].'" value ="'.$row_1['ECCN'].'">'.$row_1['Description'].'<br/>'; 
				}
			?>
			<!------------------------------>
			</datalist>

			<input list="RDO" name="RDO" id="RDO_id" title="" style="text-align:center;width:50px;font-size:11;height:13pt;"> 
			<datalist id="RDO">
			<!------------------------------>
			<?php
				$requete_2 = "SELECT DISTINCT * FROM tbl_rdo ORDER BY RDO DESC;";
				$resultat_2 = $mysqli_scm->query($requete_2);
				while ($row_2 = $resultat_2->fetch_assoc())
				{
					echo'<option Title ="'.$row_2['Description'].'" value ="'.$row_2['RDO'].'">'.$row_2['Description'].'<br/>'; 
				}
				mysqli_close($mysqli_scm);
			?>
			<!------------------------------>
			</datalist>
			
		</td>
		<td>
			<div id="eccn_desc_id"></div>
		</td>
	</tr>
	
	
	</table>

</body> 
</html>