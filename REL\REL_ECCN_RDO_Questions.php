<?php

if ($_GET['filled_select']=="Q1")
{
	include('../REL_Connexion_DB.php');
	
	$requete = "SELECT DISTINCT Q1_Text, Q1_Title 
				FROM tbl_eccn_decision";
		
	$resultat = $mysqli->query($requete);
	$rowcount=mysqli_num_rows($resultat);
	
	$output_value_Q1_text="";
	$output_value_Q1_title="";
	
	if ($rowcount>0)
	{
		while ($row = $resultat->fetch_assoc())
		{
			if ($output_value_Q1_text!="")
			{
				$output_value_Q1_text=$output_value_Q1_text."|".$row['Q1_Text'];
			} else {
				$output_value_Q1_text=$row['Q1_Text'];
			}
			
			if ($output_value_Q1_title!="")
			{
				$output_value_Q1_title=$output_value_Q1_title."|".$row['Q1_Title'];
			} else {
				$output_value_Q1_title=" ";
			}
		}
		$output_value=$output_value_Q1_text."//".$output_value_Q1_title;
		
		echo $output_value;
		$mysqli->close();
	} else {
		$output_value=""."//"."";
		echo $output_value;
	}
}

if ($_GET['filled_select']=="Q2")
{
	include('../REL_Connexion_DB.php');
	
	$requete = 'SELECT DISTINCT Q2_Text, Q2_Title
				FROM tbl_eccn_decision
				WHERE Q1_Text like "'.$_GET['Q1_val'].'"';
				
	$requete_description = 'SELECT DISTINCT Q2_Description 
				FROM tbl_eccn_decision
				WHERE Q1_Text like "'.$_GET['Q1_val'].'"';

	$resultat = $mysqli->query($requete);
	$resultat_description = $mysqli->query($requete_description);
	$rowcount=mysqli_num_rows($resultat);
	
	$output_value_Q2_text=" ";
	$output_value_Q2_Title=" ";
	$output_value_Q2_Description=" ";

	if ($rowcount>1)
	{

		while ($row = $resultat->fetch_assoc())
		{
			// DESCRIPTION
			if ($output_value_Q2_text!="")
			{
				$output_value_Q2_text=$output_value_Q2_text."|".$row['Q2_Text'];
			} else {
				$output_value_Q2_text=$row['Q2_Text'];
			}
			
			// TITLE
			if ($output_value_Q2_Title!="")
			{
				$output_value_Q2_Title=$output_value_Q2_Title."|".$row['Q2_Title'];
			} else {
				$output_value_Q2_Title=" ";
			}
		}
		

		
		// DESCRIPTION
		while ($row_description = $resultat_description->fetch_assoc())
		{
			$output_value_Q2_Description=$row_description['Q2_Description'];
		}
		$output_value=$output_value_Q2_text."//".$output_value_Q2_Title."//".$output_value_Q2_Description;
		
		echo $output_value;
		$mysqli->close();
	} elseif ($rowcount==0)
		{
			$output_value=""."//"."";
			echo $output_value;
		} else {
			// SI $rowcount==1 --> 1 SEULE LIGNE --> CODE ECCN A RECUPERER
			$ECCN="";
			$ECCN_desc="";
			$requete_ECCN = 'SELECT DISTINCT ECCN
					FROM tbl_eccn_decision
					WHERE
						Q1_Text like "'.$_GET['Q1_val'].'"';
			
			$resultat_ECCN = $mysqli->query($requete_ECCN);
			while ($row_ECCN = $resultat_ECCN->fetch_assoc())
			{
				$ECCN=$row_ECCN['ECCN'];
			}
			
			include('../SCM_Connexion_DB.php');
			$requete_ECCN_desc='
					SELECT Description
					FROM tbl_eccn
					WHERE ECCN like "'.$ECCN.'"';
			
			$resultat_ECCN_desc = $mysqli_scm->query($requete_ECCN_desc);
			while ($row_ECCN_desc = $resultat_ECCN_desc->fetch_assoc())
			{
				$ECCN_desc=$row_ECCN_desc['Description'];
			}
			$mysqli_scm->close();
			
			$output_value="ECCN"."//".$ECCN."//".$ECCN_desc;
			echo $output_value;
		}
	
}


if ($_GET['filled_select']=="Q3")
{
	include('../REL_Connexion_DB.php');
	
	$requete = 'SELECT DISTINCT Q3_Text, Q3_Title
				FROM tbl_eccn_decision
				WHERE
					Q1_Text like "'.$_GET['Q1_val'].'" AND
					Q2_Text like "'.$_GET['Q2_val'].'"';
				
	$requete_description = 'SELECT Q3_Description 
				FROM tbl_eccn_decision
				WHERE
					Q1_Text like "'.$_GET['Q1_val'].'" AND
					Q2_Text like "'.$_GET['Q2_val'].'"';

	$resultat = $mysqli->query($requete);
	$resultat_description = $mysqli->query($requete_description);
	$rowcount_description=mysqli_num_rows($resultat_description);
	
	$output_value_Q3_text=" ";
	$output_value_Q3_Title=" ";
	$output_value_Q3_Description=" ";
	
	if ($rowcount_description>1)
	{

		while ($row = $resultat->fetch_assoc())
		{
			// DESCRIPTION
			if ($output_value_Q3_text!="")
			{
				$output_value_Q3_text=$output_value_Q3_text."|".$row['Q3_Text'];
			} else {
				$output_value_Q3_text=$row['Q3_Text'];
			}
			
			// TITLE
			if ($output_value_Q3_Title!="")
			{
				$output_value_Q3_Title=$output_value_Q3_Title."|".$row['Q3_Title'];
			} else {
				$output_value_Q3_Title=" ";
			}
		}
		

		
		// DESCRIPTION
		while ($row_description = $resultat_description->fetch_assoc())
		{
			$output_value_Q3_Description=$row_description['Q3_Description'];
		}
		$output_value=$output_value_Q3_text."//".$output_value_Q3_Title."//".$output_value_Q3_Description;
		
		echo $output_value;
		
	} else {
		$ECCN="";
		$ECCN_desc="";
		$requete_ECCN = 'SELECT DISTINCT ECCN
				FROM tbl_eccn_decision
				WHERE
					Q1_Text like "'.$_GET['Q1_val'].'" AND
					Q2_Text like "'.$_GET['Q2_val'].'"';
		
		$resultat_ECCN = $mysqli->query($requete_ECCN);
		while ($row_ECCN = $resultat_ECCN->fetch_assoc())
		{
			$ECCN=$row_ECCN['ECCN'];
		}
		
		
		include('../SCM_Connexion_DB.php');
		$requete_ECCN_desc='
				SELECT Description
				FROM tbl_eccn
				WHERE ECCN like "'.$ECCN.'"';
		
		$resultat_ECCN_desc = $mysqli_scm->query($requete_ECCN_desc);
		while ($row_ECCN_desc = $resultat_ECCN_desc->fetch_assoc())
		{
			$ECCN_desc=$row_ECCN_desc['Description'];
		}
		$mysqli_scm->close();
		
		$output_value="ECCN"."//".$ECCN."//".$ECCN_desc;
		echo $output_value;
	}
	$mysqli->close();
}


if ($_GET['filled_select']=="final")
{	
	
	$ECCN="";
	$ECCN_desc="";
	
	$requete_ECCN = '
			SELECT ECCN
			FROM tbl_eccn_decision
			WHERE
				Q1_Text like "'.$_GET['Q1_val'].'" AND
				Q2_Text like "'.$_GET['Q2_val'].'" AND
				Q3_Text like "'.$_GET['Q3_val'].'"
					';
					
					include('../REL_Connexion_DB.php');
	$resultat_ECCN = $mysqli->query($requete_ECCN);
	//$rowcount=mysqli_num_rows($resultat_ECCN);
	
	while ($row_ECCN = $resultat_ECCN->fetch_assoc())
	{
		$ECCN=$row_ECCN['ECCN'];
	}
	$mysqli->close();
		
	include('../SCM_Connexion_DB.php');
	$requete_ECCN_desc='
			SELECT Description
			FROM tbl_ECCN
			WHERE ECCN like "'.$ECCN.'"';
	
	$resultat_ECCN_desc = $mysqli_scm->query($requete_ECCN_desc);
	while ($row_ECCN_desc = $resultat_ECCN_desc->fetch_assoc())
	{
		$ECCN_desc=$row_ECCN_desc['Description'];
	}
	$mysqli_scm->close();
	
	$output_value="ECCN"."//".$ECCN."//".$ECCN_desc;
	echo $output_value;
}

?>