<?php
if (isset($_GET['action']) && ($_GET['action']) == "signoff") {

	// création des variables
	//-----------------------
	$id = $_GET['ID'];
	$date_fin = date("Y-m-d");

	if ($_GET['userid'] == "%" || $_GET['userid'] == "") {
		$user = "";
	} else {
		$user = $_GET['userid'];
	}

	//Connexion à db_release
	include('../REL_Connexion_DB.php');

	//-----------------------
	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    // Critical_Complete (Flag de fin de validation)
	$query_flag = 'SELECT Doc_Type
					FROM tbl_released_drawing
					WHERE ID ="' . $id . '";';
	$resultat_flag = $mysqli->query($query_flag);
	$ligne_doc = $resultat_flag->fetch_assoc();

	if ($ligne_doc['Doc_Type'] != "DOC") {
		// Si le document n'est pas un DOC, on ne met pas le flag à 1
		$flag = "1";
	}

	//-----------------------
	//Commentaire
	$v = 'Finance: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

	$query_3 = 'SELECT General_Comments
					FROM tbl_released_drawing
					WHERE ID ="' . $id . '";';
	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc()) {
		if ($_GET['comment'] != "") {
			$v = $v . '\r\n' . $row['General_Comments'];
		} else {
			$v = $row['General_Comments'];
		}
	}
	//-----------------------

	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    // Ajout de la valeur de Critical_Complete (flag) dans la requête
	$query = 'UPDATE tbl_released_drawing 
					SET DATE_Finance="' . $date_fin . '",
						VISA_Finance="' . $user . '",
						General_Comments="' . $v . '",
						Critical_Complete="' . $flag . '"
						WHERE ID ="' . $id . '";';

	$resultat = $mysqli->query($query);

// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// Récupération de la date de validation du Costing
// Envoie dans la base db_pn, tbl_pn, Champ DATE_Costing

	//Connexion à db_pn
	include('../PN_Connexion_PN.php');


	$requete_pn = 'UPDATE tbl_pn
	SET DATE_Costing="'. $date_fin .'"
	WHERE ID ="'. $id .'";';

	$resultat_pn = $mysqli_pn->query($requete_pn);

	mysqli_close($mysqli_pn);
	mysqli_close($mysqli);
}
