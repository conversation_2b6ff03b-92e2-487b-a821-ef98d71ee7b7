<?php

// ENREGISTREMEENT DES DONNNEES ET SIGNATURE - FLUX NOMINAL
if (isset($_GET['action']) && ($_GET['action']) == "signoff") {

    $id = $_GET['ID'];
    $date_gid_2 = date('Y-m-d');

    if ($_GET['userid'] == "%" || $_GET['userid'] == "") {
        $user = "";
    } else {
        $user = $_GET['userid'];
    }

    include('../REL_Connexion_DB.php');

    //-------------------------
    // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    // Critical_Complete (Flag de fin de validation)
    $query_flag = 'SELECT Doc_Type, VISA_MOF
        FROM tbl_released_drawing
        WHERE ID ="' . $id . '";';
    $resultat = $mysqli->query($query_flag);
    while ($row_flag = $resultat->fetch_assoc()) {
        // Si le document est un DOC et qu'il a été validé par le MOF alors le flag est à 1
        if ($row_flag['Doc_Type'] == "DOC" && $row_flag['VISA_MOF'] != "") {
            $flag = "1";
        } else {
            // Sinon le flag est à 0
            $flag = "0";
        }
    }

    // Si le textarea dans REL_GID_2_Item.php n'est pas vide alors ont affichent "GID_2 : + le message"
    //Commentaire
    $v = 'GID_2 : ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

    $query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';

    $resultat = $mysqli->query($query_3);

    // On affiche notre message et à la ligne on laisse l'ancien message
    while ($row = $resultat->fetch_assoc()) {
        if ($_GET['comment'] != "") {
            $v = $v . '\r\n' . $row['General_Comments'];
        } else {
            $v = $row['General_Comments'];
        }
    }
    //-----------------------

    // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    // Ajout de la valeur de Critical_Complete (flag) dans la requête
    $query_2 = 'UPDATE tbl_released_drawing 
				SET DATE_GID_2="' . $date_gid_2 . '",
					VISA_GID_2="' . $user . '",
					General_Comments="' . $v . '",
                    Critical_Complete="' . $flag . '"
					WHERE ID ="' . $id . '";';

    $resultat = $mysqli->query($query_2);

    mysqli_close($mysqli);
}
