<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="utf-8" />

<link rel="stylesheet" type="text/css" href="REL_GID_Main_Form_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<head>

    <title>GID 2</title>

</head>

<script>
    // function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné
    function chkName(id_record) {
        const visa = document.getElementById("User_Choice" + id_record);
        if (visa.value == "" || visa.value == "%") {
            alert("Please indicate your name prior to validate");
            return false;
        }
        var res = confirm("Etes vous sur de vouloir valider ?");
        if (res == false) {
            return false;
        }
        data_update("signoff", id_record, 1);
    }

    // MISE A JOUR DE LA BASE DE DONNEES EN VALDIATION ET EN CHANGEMENT DE DOC TYPE
    function data_update(action, id_record, validation_flag) {

        const xhttp = new XMLHttpRequest();
        xhttp.onload = function() {
            // acces retour de process ou message utilisateur pour confirmation
            document.location.reload(true);
            if (validation_flag == 1) {
                window.parent.document.location.reload(true);
            }
        }

        // FOR SAVING
        var user_val = "";
        if (validation_flag == 1) {
            var user_val = document.getElementById("User_Choice" + id_record).value;
        }
        // ----

        if (action == "signoff" || action == 1) {
            action = "signoff";
            const url_a = "REL_GID_2_Action.php?ID=" + id_record +
                "&userid=" + user_val +
                "&action=" + action +
                "&comment=" + document.getElementById("comment_" + id_record).value;

            xhttp.open("GET", url_a);
            xhttp.send();
        }
    }
</script>



<body>
    <form enctype="multipart/form-data" action="" method="post">

        <?php
        include('REL_Workflow_Conditions.php');
        $query = 'SELECT *
                FROM tbl_released_package 
                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE ' . $GID_2_Conditions . '
                    AND tbl_released_drawing.ID like "' . $_GET['ID'] . '"
                    ORDER BY tbl_released_drawing.reference DESC';

        include('../REL_Connexion_DB.php');
        $resul_1 = $mysqli->query($query);
        while ($ligne = $resul_1->fetch_assoc()) {
            echo '<table id="t04" border=0>';
            echo '<tr>';
            echo '<td colspan="2">';
            echo '<fieldset>';
            echo '<legend>Données de base 1</legend>';
            echo '<table border="0" id="case">';
            // ID recuperé pour envoi avec le formulaire - Non visible dans la page
            echo '<input type="text" size=2 name="ID_" style="height:3pt;width:3pt;" hidden readonly value="' . $_GET['ID'] . '">';
            echo '<tr>';
            echo '<th style="width: 15%;">Reference : </th>';
            echo '<td style="width: 19%;">' . $ligne['Reference'] . '<strong style="color: blue"> rev' . $ligne['Ref_Rev'] . '</strong>';
            echo '</td>';

            echo '<th style="width: 13%;">Groupe Articles :</th>';
            $title = "";
            $requete_commo = 'SELECT Code, Description FROM tbl_commodity_code
                                                                      WHERE Code like "' . $ligne['Commodity_Code'] . '"';
            $resultat_commo = $mysqli->query($requete_commo);
            while ($row_commo = $resultat_commo->fetch_assoc()) {
                if ($ligne['Commodity_Code'] == "" || $ligne['Commodity_Code'] != $row_commo['Code']) {
                } else if ($ligne['Commodity_Code'] != ""  && $ligne['Commodity_Code'] == $row_commo['Code']) {
                    $title = $row_commo['Description'];
                }
            }
            echo '<td style="width: 20%;" title="' . $title . '">';
            echo $ligne['Commodity_Code'];
            echo '</td>';

            echo '<th>Unité :</th>';
            if ($ligne['Unit'] != "PC" && $ligne['Unit'] != "") {
                $styl = 'style="color:red"';
            } else {
                $styl = "";
            }
            echo '<td ' . $styl . '>' . $ligne['Unit'] . '</td>';

            echo '</tr>';

            echo '<tr>';
            echo '<th>Groupe Autorisation : </th>';
            $title = "";
            include('../SCM_Connexion_DB.php');
            $requete_rdo = 'SELECT Description FROM tbl_rdo WHERE RDO like "' . $ligne['RDO'] . '"';
            $resultat_rdo = $mysqli_scm->query($requete_rdo);
            while ($row_rdo = $resultat_rdo->fetch_assoc()) {
                if ($ligne['RDO'] == "") {
                } else if ($ligne['RDO'] != "") {
                    $title = $row_rdo['Description'];
                }
            }
            echo '<td title="' . $title . '">';
            echo $ligne['RDO'];
            echo '</td>';
            mysqli_close($mysqli_scm);


            echo '<th>Code Produit : </th>';
            echo '<td>';
            echo $ligne['Product_Code'];
            include('../SCM_Connexion_DB.php');
            $requete_product = 'SELECT Description FROM tbl_product_code WHERE Code like "' . $ligne['Product_Code'] . '"';
            $resultat_product = $mysqli_scm->query($requete_product);
            while ($row_product = $resultat_product->fetch_assoc()) {
                if ($ligne['Product_Code'] == "") {
                    echo '';
                } else if ($ligne['Product_Code'] != "") {
					if ($row_product['Description']!="")
					{
						$description=' / ' . $row_product['Description'];
					} else {
						$description="";
					}
                    echo $description;
                }
            }
            mysqli_close($mysqli_scm);
            echo '</td>';

            echo '<th>Code Projet : </th>';
            echo '<td>' . $ligne['Project'] . '</td>';

            echo '</tr>';
            echo '<tr>';
            echo '<th>Titre : </th>';
            echo '<td colspan="3" style="width: 25%;">' . $ligne['Ref_Title'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="2" style="width: 100%;">';
            echo '<fieldset>';
            echo '<legend>Données de base 2</legend>';
            echo '<table style="width: 100%;">';
            echo '<tr>';
            echo '<th style="width: 25%;">Document : </th>';
            echo '<td style="width: 40%;">';
            if ($ligne['Prod_Draw'] != "" && $ligne['Prod_Draw_Rev'] != "") {
                echo '<a href="https://app.aletiq.com/parts/preview/id/' . $ligne['Prod_Draw'] . '/revision/' . $ligne['Prod_Draw_Rev'] . '" target="_blank">' . $ligne['Prod_Draw'] . ' rev' . $ligne['Prod_Draw_Rev'] . '</a>';
            } else {
                echo $ligne['Prod_Draw']. ' rev' . $ligne['Prod_Draw_Rev'];
            }
            echo '</td>';

            echo '<th style="width: 13%;">Alias : </th>';
            echo '<td style="width: 40%;">' . $ligne['Alias'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td>';
            echo '<table style="width: 100%;">';
            echo '<td style="width: 50%;">';
            echo '<fieldset>';
            echo '<legend>Achats</legend>';
            echo '<table style="width: 100%;">';
            echo '<tr>';
            echo '<th style="width: 65%;">Groupe Acheteur : </th>';
            echo '<td>' . $ligne['Purchasing_Group'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '<td>';
            echo '<fieldset>';
            echo '<legend>CLS</legend>';
            echo '<table style="width: 100%;">';
            echo '<tr>';
            echo '<th style="width: 7%;">Taille de lot :</th>';
            echo '<td style="width: 12%;">' . $ligne['CLS'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</table>';
            echo '</td>';

            echo '<td>';
            echo '<table style="width: 100%;">';
            echo '<tr>';
            echo '<td style="width: 50%;">';
            echo '<fieldset>';
            echo '<legend>Comm Exter import</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 20%;">HTS : </th>';
            echo '<td>';
            echo $ligne['HTS'];
            include('../SCM_Connexion_DB.php');
            $requete_hts = 'SELECT Description FROM tbl_hts WHERE HTS like "' . $ligne['HTS'] . '"';
            $resultat_hts = $mysqli_scm->query($requete_hts);
            while ($row_hts = $resultat_hts->fetch_assoc()) {
                if ($ligne['HTS'] == "") {
                    echo '';
                } else if ($ligne['HTS'] != "") {
                    echo ' / ' . $row_hts['Description'];
                }
            }
            mysqli_close($mysqli_scm);
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';

            echo '<td>';
            echo '<fieldset>';
            echo '<legend>Classification</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 25%;">ECCN : </th>';
            echo '<td>';
            echo $ligne['ECCN'];
            include('../SCM_Connexion_DB.php');
            $requete_eccn = 'SELECT Description FROM tbl_eccn WHERE ECCN like "' . $ligne['ECCN'] . '"';
            $resultat_eccn = $mysqli_scm->query($requete_eccn);
            while ($row_eccn = $resultat_eccn->fetch_assoc()) {
                if ($ligne['ECCN'] == "") {
                    echo '';
                } else if ($ligne['ECCN'] != "") {
                    echo ' / ' . $row_eccn['Description'];
                }
            }
            mysqli_close($mysqli_scm);
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="2">';
            echo '<fieldset>';
            echo '<legend>Planif des besoins 1</legend>';
            echo '<table id="case" border=0>';
            echo '<tr>';
            echo '<th style="width: 14%;">Horizon planif. Fixe : </th>';
            echo '<td style="width: 20%;">';
            $mat_prod = $ligne['Mat_Prod_Type'];
            if ($mat_prod == 'FERT') {
                echo '5';
            } else {
                echo '';
            }
            echo '</td>';

            echo '<th style="width: 10%;">Gestionnaire : </th>';
            echo '<td style="width: 20%;">';
            echo $ligne['Supervisor'];
            $requete_sup = 'SELECT Code, Description FROM tbl_supervisor 
                                            WHERE Code like "' . $ligne['Supervisor'] . '"';
            $resultat_sup = $mysqli->query($requete_sup);
            while ($row_sup = $resultat_sup->fetch_assoc()) {
                if ($ligne['Supervisor'] == "" || $ligne['Supervisor'] != $row_sup['Code']) {
                    echo '';
                } else if ($ligne['Supervisor'] != ""  && $ligne['Supervisor'] == $row_sup['Code']) {
                    echo ' / ' . $row_sup['Description'];
                }
            }
            echo '</td>';
            echo '<th style="width: 15%;">Méthode lotissement : </th>';
            echo '<td>';
            if ($mat_prod == 'HALB') {
                $doc_type = $ligne['Doc_Type'];
                if ($doc_type == 'MACH') {
                    echo '1M';
                } else if ($doc_type == 'MOLD') {
                    echo '6M';
                } else if ($doc_type == 'ASSY') {
                    echo '2Z';
                }
            } else if ($mat_prod == 'FERT') {
                echo 'EX';
            } else if ($mat_prod == 'ROH' || $mat_prod == 'VERP') {
                echo '1M/2M/3M';
            }
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="2">';
            echo '<fieldset>';
            echo '<legend>Planif des besoins 2</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 10%;">Type Appro. : </th>';
            echo '<td style="color: green">' . $ligne['Proc_Type'] . '</td>';

            echo '<th style="width: 12%;">Magasin Prod. : </th>';
            echo '<td style="width: 12%;">';
            if ($mat_prod == 'FERT') {
                echo 'LS10';
            } else if ($mat_prod == 'HALB' || $mat_prod == 'VERP') {
                echo 'LS30';
            } else if ($mat_prod == 'ROH') {
                echo 'LS20 ou LS60';
            }
            echo '</td>';

            echo '<th style="width: 12%;">Mag. Pour appro. Ext : </th>';
            echo '<td style="width: 12%;">';
            if (($mat_prod == 'HALB' && $doc_type == 'PUR') || ($mat_prod == 'VERP') || ($mat_prod == 'ROH')) {
                echo 'LS09';
            } else {
                echo '';
            }
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th style="width: 12%;">Delai Fab Interne : </th>';
            echo '<td style="width: 12%;">';
            if ($mat_prod == 'FERT' || $mat_prod == 'HALB') {
                echo $ligne['leadtime'];
            } else {
                echo '';
            }
            echo '</td>';

            echo '<th>Clé d\'horizon : </th>';
            echo '<td>';
            $doc_type = $ligne['Doc_Type'];
            if ($mat_prod == 'HALB' && ($doc_type == 'MOLD' || $doc_type == 'ASSY' || $doc_type == 'MACH') || $mat_prod == 'FERT') {
                echo 'Z07';
            } else if (($doc_type == 'PUR' && $mat_prod == 'HALB') || $mat_prod == 'VERP' || $mat_prod == 'ROH') {
                echo 'Z02';
            }
            echo '</td>';

            echo '<th>FIA : </th>';
            echo '<td>' . $ligne['FIA'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td style="width: 50%;">';
            echo '<fieldset>';
            echo '<legend>Planif des besoins 4</legend>';
            echo '<table id="case" border=0>';
            echo '<tr>';
            echo '<th style="width: 30%;">Individuel/Collectif : </th>';
            echo '<td>';
            if ($mat_prod == 'FERT') {
                echo '1';
            } else if ($mat_prod == 'HALB' || $mat_prod == 'VERP' || $mat_prod == 'ROH') {
                echo '2';
            }
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';

            echo '<td>';
            echo '<fieldset>';
            echo '<legend>Préparation Travail</legend>';
            echo '<table id="case" border=0>';
            echo '<tr>';
            echo '<th>Superviseur  Prod : </th>';
            echo '<td style="">';
            if ($mat_prod == 'HALB' || $mat_prod == 'FERT') {
                echo $ligne['Prod_Agent'];
            } else {
                echo '';
            }
            echo '</td>';

            echo '<th>Profil Pilot. Atelier : </th>';
            echo '<td>';
            $doc_type = $ligne['Doc_Type'];
            if ($mat_prod == 'FERT' || $mat_prod == 'HALB') {
                echo '000001';
            }
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="3">';
            echo '<fieldset>';
            echo '<legend>Autres Information Diffusion qualité</legend>';
            echo '<table border="0" id="case">';
            echo '<tr>';

            echo '<th style="width: 13%;">Diffusion #:</th>';
            //echo '<td>' . $ligne['Rel_Pack_Num'] . '</td>';
			echo '<td><a target="_blank" href="REL_Pack_Overview.php?ID='. $ligne['Rel_Pack_Num'].'" >'. $ligne['Rel_Pack_Num'] . '</a></td>';


            echo '<th>Activité :</th>';
            echo '<td>' . $ligne['Activity'] . '</td>';

            echo '<th style="width: 10%;">Ex :</th>';
            if ($ligne['Ex'] != "NO") {
                echo '<td style="color: red;">' . $ligne['Ex'] . '</td>';
            } else {
                echo '<td>' . $ligne['Ex'] . '</td>';
            }

            echo '<th>Action :</th>';
            echo '<td style="color: green">' . $ligne['Action'] . '</td>';

            echo '</tr>';
            echo '<tr>';

            echo '<th style="width: 13%;">Impact En-cours:</th>';
            echo '<td style="width: 15%;">' . $ligne['Inventory_Impact'] . '</td>';

            echo '<th style="width: 10%;">Pris Dans 1 :</th>';
            echo '<td style="width: 20%;">' . $ligne['Pris_Dans1'] . '</td>';

            echo '<th>Pris Dans 2 :</th>';
            echo '<td>' . $ligne['Pris_Dans2'] . '</td>';

            echo '<th>Interne Recommandée :</th>';
            if ($ligne['Internal_Mach_Rec'] == 1) {
                echo '<td><img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15px" style="margin-top:-2px;margin-bottom:-2px"""></td>';
            }

            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="2">';
            echo '<fieldset>';
            echo '<legend>Commentaires</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="vertical-align:top">Commentaire Créateur (BE)</th>';
            echo '<th style="vertical-align:top">Observations Diffusion</th>';
            echo '<th  style="vertical-align:top">Commentaires Généraux</th>';
            echo '</tr>';
            echo '<tr>';

            echo '<td style="width: 33%;"><textarea style="width: 100%;" id="requestor_comments" disabled name="requestor_comments" rows="4">' . $ligne['Requestor_Comments'] . '</textarea></td>';

            echo '<td style="width: 33%;"><textarea style="width: 100%;" id="observation_comments" disabled name="observation_comments" rows="4">' . $ligne['Observations'] . '</textarea></td>';

            echo '<td style="width: 33%;"><textarea style="width: 100%;" id="general_comments" disabled name="general_comments" rows="4">' . $ligne['General_Comments'] . '</textarea></td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="2">';
            echo '<fieldset>';
            echo '<legend>Signatures</legend>';
            echo '<table id="case">';
            echo '<tr>';
            //------------------------

            if ($ligne['Creation_VISA'] == "") {
                $val = "";
            } else {
                $val = '</br>' . $ligne['Creation_VISA'] . '<div id="date">' . $ligne['Creation_Date'] . '</div>';
            }
            echo '<td><div id="Table_results"><b>Creation BE</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['VISA_BE_2'] == "") {
                $val = "";
            } else {
                $val = '</br>' . $ligne['VISA_BE_2'] . '<div id="date">' . $ligne['DATE_BE_2'] . '</div>';
            }
            echo '<td><div id="Table_results"> <b>Verification BE</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['VISA_BE_3'] == "") {
                $val = "";
            } else {
                $val = '</br>' . $ligne['VISA_BE_3'] . '<div id="date">' . $ligne['DATE_BE_3'] . '</div>';
            }
            echo '<td><div id="Table_results"> <b>Validation BE</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['VISA_Inventory'] == "") {
                if ($ligne['Inventory_Impact'] == "NO IMPACT") {
                    $val = '</br>--<div id="date" title="Impact En-cours = No Impact">--</div>';;
                } else {
                    $val = "";
                }
            } else {
                $val = '</br>' . $ligne['VISA_Inventory'] . '<div id="date">' . $ligne['DATE_Inventory'] . '</div>';
            }
            echo '<td style="vertical-align:top"><div id="Table_results"><b>En Cours</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['VISA_Product'] == "") {

                $val = "";
            } else {

                $val = '<br>' . $ligne['VISA_Product'] . '<div id="date">' . $ligne['DATE_Product'] . '</div>';
            }
            echo '<td  ><div id="Table_results"> <b>Produit</b>' . $val . '</div></td>';

            //------------------------

            if (((substr($ligne['Prod_Draw'], 0, 2) == "GA" || substr($ligne['Prod_Draw'], 0, 2) == "FT")) && $ligne['Project'] != "STAND") {
                if ($ligne['VISA_Project'] == "") {
                    $val = "";
                } else {
                    $val = '</br>' . $ligne['VISA_Project'] . '<div id="date">' . $ligne['DATE_Project'] . '</div>';
                }
            } else {
                $val = '</br>--<div id="date" title="Impact En-cours = No Impact">--</div>';
            }
            echo '<td style="vertical-align:top"><div id="Table_results"><b>Project</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['Doc_Type'] == "PUR" || $ligne['Doc_Type'] == "ASSY" || $ligne['Doc_Type'] == "DOC") {
                if ($ligne['VISA_Quality'] == "") {

                    $val = "";
                } elseif ($ligne['VISA_Quality'] != "") {

                    $val = '</br>' . $ligne['VISA_Quality'] . '<div id="date">' . $ligne['DATE_Quality'] . '</div>';
                }
                echo '<td  ><div id="Table_results"> <b>Qualité</b>' . $val . '</div></td>';
            }

            //------------------------

            if ($ligne['Proc_Type'] == "F30" || $ligne['Proc_Type'] == "F") {
                if ($ligne['VISA_PUR_1'] == "") {

                    $val = "";
                } elseif ($ligne['VISA_PUR_1'] != "") {

                    $val = '</br>' . $ligne['VISA_PUR_1'] . '<div id="date">' . $ligne['DATE_PUR_1'] . '</div>';
                }
                echo '<td  ><div id="Table_results"> <b>RFQ</b>' . $val . '</div></td>';
            }

            //------------------------

            if ($ligne['Proc_Type'] == "F30") {
                if ($ligne['VISA_PUR_2'] == "") {

                    $val = "";
                } elseif ($ligne['VISA_PUR_2'] != "") {

                    $val = '</br>' . $ligne['VISA_PUR_2'] . '<div id="date">' . $ligne['DATE_PUR_2'] . '</div>';
                }
                echo '<td ><div id="Table_results"> <b>Pris Dans</b>' . $val . '</div></td>';
            }

            //------------------------

            if (($ligne['Doc_Type'] == "MACH" || $ligne['Doc_Type'] == "MOLD" || $ligne['Doc_Type'] == "ASSY") && ($ligne['Proc_Type'] == "" || $ligne['Proc_Type'] == "E")) {
                if ($ligne['VISA_Prod'] == "") {
                    $val = "";
                } elseif ($ligne['VISA_Prod'] != "") {
                    $val = '</br>' . $ligne['VISA_Prod'] . '<div id="date">' . $ligne['DATE_Prod'] . '</div>';
                }
                echo '<td><div id="Table_results"> <b>Production</b>' . $val . '</div></td>';
            }

            //------------------------

            if (($ligne['Doc_Type'] == "MACH" || $ligne['Doc_Type'] == "MOLD" || $ligne['Doc_Type'] == "ASSY") && ($ligne['Proc_Type'] == "" || $ligne['Proc_Type'] == "E")) {
                if ($ligne['VISA_Supply'] == "") {
                    $val = "";
                } elseif ($ligne['VISA_Supply'] != "") {
                    $val = '</br>' . $ligne['VISA_Supply'] . '<div id="date">' . $ligne['DATE_Supply'] . '</div>';
                }
                echo '<td><div id="Table_results"> <b>Supply/Logisitique</b>' . $val . '</div></td>';
            }

            //------------------------

            if ($ligne['VISA_GID'] == "") {
                $val = "";
            } else {
                $val = '</br>' . $ligne['VISA_GID'] . '<div id="date">' . $ligne['DATE_GID'] . '</div>';
            }
            echo '<td ><div id="Table_results"> <b>SAP Core Data</b>' . $val . '</div></td>';

            //------------------------
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td>';
            echo '<textarea style="width: 100%;" id="comment_' . $ligne['ID'] . '" name="comment" rows="4" cols="70" ></textarea>';
            echo '</td>';
            echo '<td style="text-align:center; vertical-align: middle;">';
            echo '<SELECT id="User_Choice' . $ligne['ID'] . '" name="user_name" type="submit" size="1">
                            <option value="%"></option>';
           include('../SCM_Connexion_DB.php');
            $requete_5 = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
                                          FROM tbl_user
										  WHERE tbl_user.Department like "%GID%"';
            $resultat_5 = $mysqli_scm->query($requete_5);
            while ($row4 = $resultat_5->fetch_assoc()) {
                echo '<OPTION value ="' . $row4['Fullname'] . '">' . $row4['Fullname'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '  </SELECT>';
            echo '<input onclick="return data_update(1,' . $ligne['ID'] . ',0)" style="vertical-align:middle;height:18px;margin-left:10px;" name="save" type="submit" id="save" class="btn orange" value="Save" title="Save the current data without validating it" />';
            echo '<input onclick="return chkName(' . $ligne['ID'] . ')" style="vertical-align:middle;height:18px;margin-left:10px;" type="submit" class="btn blue2" id="valid_form" name="valid_form" value="Sign" title="Sign off" />';
            echo '</td>';
            echo '</tr>';

            echo '</table>';
        }
        mysqli_close($mysqli);
        ?>
    </form>
</body>

</html>