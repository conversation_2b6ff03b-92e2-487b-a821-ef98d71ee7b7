<?php

// ENREGISTREMEENT DES DONNNEES ET SIGNATURE - FLUX NOMINAL
$file_move_to_official = 0;
if (isset($_GET['action']) && ($_GET['action']) == "signoff")
{

    $id = $_GET['ID'];
    $date_gid = date('Y-m-d');
	$SWITCH_ALETIQ = $_GET['SWITCH_ALETIQ'];

    if ($_GET['userid'] == "%" || $_GET['userid'] == "") {
        $user = "";
        $file_move_to_official = 0;
    } else {
        $user = $_GET['userid'];
        $file_move_to_official = 1;
    }

    // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    // Récupération de la valeur de la ref dans la variable $reference
    $reference = $_GET['ref'];
	if($reference=="")
	{
		$reference="-";
	}


    //Connexion à db_release

    include('../REL_Connexion_DB.php');
    // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    // Récupération de toutes les données ci-dessous sous Date_Costing si Doc_Type = "DOC"
    $date_costing = "0000-00-00";
    $requete_costing = 'SELECT tbl_released_package.Rel_Pack_Num, tbl_released_package.Activity, tbl_released_drawing.* from tbl_released_package
    LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
    where tbl_released_drawing.ID ="' . $id . '"';
    $resultat_p = $mysqli->query($requete_costing);
    while ($ligne = $resultat_p->fetch_assoc()) {
        if ($ligne['Doc_Type'] == "DOC") {
            $date_costing = date('Y-m-d');
        } else {
            $date_costing = "0000-00-00";
        }
        $ref_rev = $ligne['Ref_Rev'];
        $prod_draw = $ligne['Prod_Draw'];
        $prod_draw_rev = $ligne['Prod_Draw_Rev'];
        $drawing_path = $ligne['Drawing_Path'];
        $product_code = $ligne['Product_Code'];
        $title = $ligne['Ref_Title'];
		$title_fra="";
		$doc_type=$ligne['Doc_Type'];
		$alias=$ligne['Alias'];
		$cust_drawing=$ligne['Cust_Drawing'];
		$cust_drawing_rev=$ligne['Cust_Drawing_Rev'];
		$cust_drawing_path="";
		$certif=$ligne['Ex'];
        $division = $ligne['Activity'];
        $rel_pack_num = $ligne['Rel_Pack_Num'];
    }

    // Si le textarea dans REL_GID_Item.php n'est pas vide alors ont afficher "GID : + le message"
    //Commentaire
    $v = 'GID : ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

    $query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';

    $resultat = $mysqli->query($query_3);

    // On affiche notre message et à la ligne on laisse l'ancien message
    while ($row = $resultat->fetch_assoc()) {
        if ($_GET['comment'] != "") {
            $v = $v . '\r\n' . $row['General_Comments'];
        } else {
            $v = $row['General_Comments'];
        }
    }
    //-----------------------

    // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    // Ajout de la variable $reference dans la requete

    $query_2 = 'UPDATE tbl_released_drawing 
				SET DATE_GID="' . $date_gid . '",
					VISA_GID="' . $user . '",
					Reference="' . $reference . '",
					General_Comments="' . $v . '",
					SWITCH_ALETIQ="' . $SWITCH_ALETIQ . '"
				WHERE ID ="' . $id . '";';

    $resultat = $mysqli->query($query_2);
	
	mysqli_close($mysqli);


    
}

// Changement de dossier pour un plan
// Le plan passe de IN_PROCESS à OFFICIAL
if ($file_move_to_official == 1)
{
    include('../REL_Connexion_DB.php');

    $id = $_GET['ID'];

    $sql_file = 'SELECT Drawing_Path FROM tbl_released_drawing WHERE ID like "' . $id . '"';
    $result = $mysqli->query($sql_file);
    while ($row = $result->fetch_assoc()) 
	{
        $file_path_db = $row['Drawing_Path'];
    }
	
	
	
	
	if ($file_path_db != "")
	{
		
		// PREPARATION DES CHEMINS "IN_PROCESS" ET "OFFICIAL
		// ---------------------------------------------------
		$path_process = "DRAWINGS\IN_PROCESS\\";
        $path_official = "DRAWINGS\OFFICIAL\\";

        $compl_path_process = $path_process . $file_path_db;
        $compl_path_official = $path_official . $file_path_db;
		// ---------------------------------------------------
		
		
		// VERIFICATION SI AUCUNE AUTRE REF UTILISANT LE MEME PLAN N'EST PAS TOUJOURS EN DIFFUSION EN AMONT. 
		// 	- SI OUI, UNE COPIE DU PLAN EST FAITE DANS OFFICIAL
		//  - SI NON, LE FICHIER EST DEPLACE DANS OFFICIAL
		// ---------------------------------------------------
		$sql_check_diff_alive = 'SELECT COUNT(*) as "nb_diff_amont"
							 FROM tbl_released_drawing
							 WHERE 
								   ID not like "' . $id . '" 
							   AND Prod_Draw = (
												SELECT Prod_Draw
												FROM tbl_released_drawing
												WHERE ID like "' . $id . '" 
												)
							   AND Prod_Draw_Rev = (
													SELECT Prod_Draw_Rev
													FROM tbl_released_drawing
													WHERE ID like "' . $id . '" 
													)
							   AND VISA_GID like ""';
		$result_check = $mysqli->query($sql_check_diff_alive);	
		$nb_diff_amont=0;	
		while ($row_check = $result_check->fetch_assoc()) {
			$nb_diff_amont = $row_check['nb_diff_amont'];
		}
		// ---------------------------------------------------
		
		
		
		if ($nb_diff_amont==0)
		{
			// PAS DE DIFF UTILISANT LE MEME PLAN EN AMONT, LE FICHIER EST DEPLACE SI EXISTANT
			// -------------------------------------------
			if (file_exists($compl_path_process)) {
				rename($compl_path_process, $compl_path_official);
			} else {
			}
		} else {
			// DIFF TROUVEE EN AMONT, LE FICHIER EST COPIE
			// -------------------------------------------
			if (file_exists($compl_path_process)) {
				copy($compl_path_process, $compl_path_official);
			} else {
			}
		}
		
    } else {
    }
	// ###################################
	
    mysqli_close($mysqli);
	
	
	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
	// Implementer un INSERT INTO dans la table tbl_pn à la validation GID_1. Remplir tous les champs sauf DATE_Costing sauf si c'est du doc

	// Incremetation de l'ID pour la diff en cours
	
	include('../PN_Connexion_PN.php');
	include('../REL_Connexion_DB.php');
	
	$draw_numbering_pn = 'SELECT MAX(ID) from db_pn.tbl_pn';
	$draw_max_ID_tmp_pn = $mysqli_pn->query($draw_numbering_pn);
	$draw_max_ID_pn = mysqli_fetch_row($draw_max_ID_tmp_pn);
	$ID_max_pn = intval($draw_max_ID_pn[0]) + 1;

	$draw_numbering_rel = 'SELECT MAX(ID) from db_release.tbl_released_drawing';
    $draw_max_ID_tmp_rel = $mysqli->query($draw_numbering_rel);
    $draw_max_ID_rel = mysqli_fetch_row($draw_max_ID_tmp_rel);
    $draw_max_rel = intval($draw_max_ID_rel[0]) + 1;
	
	$ID_max = max($draw_max_rel, $ID_max_pn);

	$id_update_sql='UPDATE db_release.tbl_released_drawing set ID="'.$ID_max.'" WHERE ID like "'.$id.'"';
	$id_update = $mysqli->query($id_update_sql);
	
	mysqli_close($mysqli);


	if($ref_rev=="")
	{
		$ref_rev="-";
	}

	$requete_pn = 'INSERT INTO db_pn.tbl_pn              
					VALUES 
						("' . $ID_max . '",
						"' . $reference . '",
						"' . $ref_rev . '",
						"' . $title . '",
						"' . $title_fra . '",
						"' . $prod_draw . '",
						"' . $prod_draw_rev . '",
						"' . $drawing_path . '",
						"' . $alias . '",
						"' . $cust_drawing . '",
						"' . $cust_drawing_rev . '",
						"' . $cust_drawing_path . '",
						"' . $product_code . '",
						"' . $doc_type . '",
						"' . $certif . '",
						"' . $division . '",
						"' . $rel_pack_num . '",
						"' . $date_gid . '",
						"' . $date_costing . '")';

	$resultat_pn = $mysqli_pn->query($requete_pn);
	
	mysqli_close($mysqli_pn);
	
	
	// PREPA POUR FUTUR REGLE 
	// SI LE PLAN EST UTILISE PAR D'AUTRES REFS DEJA DANS V2D MAIS DONT LES REVISIONS SONT ANTERIEURS A CELLE DU PLAN DIFFSUE, ALORS LA NOUVELLE REVISION EST APPLIQUEE A CES REF
	// EXEMPLE: 
	// 	- DIFFUSION DE LA REF V365-2106-08-06000 REV D AVEC SON PLAN V365 21XX YY REV C3.
	// 	- LES REFS V365-21XX-YY-ZZ000 UTILISENT LE PLAN REV C2
	// 	- LA REV C3 DU PLAN EST ALORS APPLIQUEE A L'ENSEMBLE  DES REFS V365-21XX-YY-ZZ000 DEJA DANS V2D EN PLUS DU PLAN V365-2106-08-06000 SPECIFIQUEMENT DIFFUSE.
	
	$draw_rev_in_query ='SELECT 
							* 
						 FROM 
							tbl_pn 
						 WHERE 
							Prod_Draw like "'.$prod_draw.'" AND
							Reference not like "'. $ref .'" AND
							(
								(
								ASCII(Prod_Draw_Rev) like ASCII("' . $prod_draw_rev . '") AND HEX(Prod_Draw_Rev) < HEX("' . $prod_draw_rev . '")
								) OR (
								ASCII(Prod_Draw_Rev) != ASCII("' . $prod_draw_rev . '")
								)
							  )';
}
