<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="utf-8" />

<link rel="stylesheet" type="text/css" href="REL_GID_Main_Form_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">

<head>

    <title>GID</title>

</head>

<script>
    // function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné
    function chkName(id_record) {
        const visa = document.getElementById("User_Choice" + id_record);
        var switch_aletiq = document.getElementById("SWITCH_ALETIQ"+id_record).value;
        if (visa.value == "" || visa.value == "%") {
            alert("Please indicate your name prior to validate");
            return false;
        }

        if (switch_aletiq == "0") {
            alert("Merci de valider le plan sur Aletiq avant de signer");
            return false;
        }


		
		const ref_zpf_xxxxxx = document.getElementById("ref" + id_record).value;
		if (ref_zpf_xxxxxx.value=="ZPF000000000XXXXXX")
		{
			alert("Before signing off this reference, please make sure to replace the ZPF000000000XXXXXX by the actual ZPF reference");
			return false;
		}
		
        var res = confirm("Etes vous sur de vouloir valider ?");
        if (res == false) {
            return false;
        }

        data_update("signoff", id_record, 1);
    }

    // MISE A JOUR DE LA BASE DE DONNEES EN VALDIATION ET EN CHANGEMENT DE DOC TYPE
    function data_update(action, id_record, validation_flag) {
        var switch_aletiq = document.getElementById("SWITCH_ALETIQ"+id_record).value;
        const xhttp = new XMLHttpRequest();
        xhttp.onload = function() {
            // acces retour de process ou message utilisateur pour confirmation

            // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            // Rafraichissement de la page GID pour afficher les changements
            parent.window.location.href = 'REL_GID_Main_Form.php';
            if (validation_flag == 1) {
                window.parent.document.location.reload(true);
            }
        }

        // FOR SAVING
        var user_val = "";
        if (validation_flag == 1) {
            var user_val = document.getElementById("User_Choice" + id_record).value;
            console.log(user_val);
        }
        // ----
        // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
        // Ajout de la référence dans l'URL pour l'envoyer dans la base de données
        if (action == "signoff" || action == 1) {
            action = "signoff";
            const url_a = "REL_GID_Action.php?ID=" + id_record +
                "&userid=" + user_val +
                "&action=" + action +
                "&ref=" + document.getElementById("ref" + id_record).value +
                "&comment=" + document.getElementById("comment_" + id_record).value +
                "&SWITCH_ALETIQ=" + switch_aletiq;
            xhttp.open("GET", url_a);
            xhttp.send();
        }
    }
</script>


<body>
    <form enctype="multipart/form-data" action="" method="post">

        <?php
        include('REL_Workflow_Conditions.php');
        $query = 'SELECT *
                FROM tbl_released_package 
                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE ' . $GID_1_Conditions . '
                    AND tbl_released_drawing.ID like "' . $_GET['ID'] . '"
                    ORDER BY tbl_released_drawing.reference DESC';

        include('../REL_Connexion_DB.php');
        $resul_1 = $mysqli->query($query);
        while ($ligne = $resul_1->fetch_assoc()) {
            echo '<table id="t04" border=0>';
            echo '<tr>';
            echo '<td colspan="3">';
            echo '<fieldset>';
            echo '<legend>Données de base 1</legend>';
            echo '<table id="case">';
            // ID recuperé pour envoi avec le formulaire - Non visible dans la page
            echo '<input type="text" size=2 id="ID" name="ID" style="height:3pt;width:3pt;" hidden readonly value="' . $_GET['ID'] . '">';
            echo '<td hidden id="doc_type' . $ligne['ID'] . '">' . $ligne['Doc_Type'] . '</td>';
            echo '<tr>';

            echo '<th>Reference :</th>';
            echo '<td style="width: 17%;">';
            // !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
            // Affichage de la référence en rouge quand il y en a plusieurs du même nom dans le flux "avant GID"

            // Requête globale pour vérifier si la référence est présente dans la base de données
            $requete_ref = 'SELECT COUNT(Reference), VISA_GID, VISA_Supply, VISA_Quality, VISA_Project, VISA_Metro FROM tbl_released_drawing
                            WHERE Reference like "' . $ligne['Reference'] . '" AND VISA_GID like ""';
            $resultat_ref = $mysqli->query($requete_ref);
            $result_ref = $resultat_ref->fetch_assoc();

            // Si la référence est présente dans la base de données, on vérifie si elle est validée par GID
			$col="";
			$title_val="";
            if ($result_ref['COUNT(Reference)'] > 1 && trim((strtoupper($ligne['Reference']))!="ZPF000000000XXXXXX") || trim((strtoupper($ligne['Reference']))=="ZPF000000000XXXXXX")) {
                $col="color:red;";
				$title_val="The reference might also be in the release process at a different step. Please check the status of the reference in the Reference Followup section.";
            } 
			echo '<input type="text" id="ref' . $ligne['ID'] . '" name="ref" value="' . $ligne['Reference'] . '" style="'.$col.'text-align:center;width:114px" title="'.$title_val.'"><strong id="ref_rev' . $ligne['ID'] . '"  style="color: blue;"> rev' . $ligne['Ref_Rev'] . '</strong>';
            echo '</td>';

            echo '<th style="width: 12%;">Grp. Articles :</th>';
            $title = "";
            $requete_commo = 'SELECT Code, Description FROM tbl_commodity_code
                                                  WHERE Code like "' . $ligne['Commodity_Code'] . '"';
            $resultat_commo = $mysqli->query($requete_commo);
            while ($row_commo = $resultat_commo->fetch_assoc()) {
                if ($ligne['Commodity_Code'] == "" || $ligne['Commodity_Code'] != $row_commo['Code']) {
                } else if ($ligne['Commodity_Code'] != ""  && $ligne['Commodity_Code'] == $row_commo['Code']) {
                    $title = $row_commo['Description'];
                }
            }
            echo '<td style="width: 20%;" title="' . $title . '">';
            if ($ligne['Commodity_Code'] == "") {
                if ($ligne['Doc_Type'] == 'MACH' && ($ligne['Mat_Prod_Type'] == 'FERT' || $ligne['Mat_Prod_Type'] == 'HALB')) {
                    echo '18010000';
                } elseif ($ligne['Doc_Type'] == 'ASSY' && ($ligne['Mat_Prod_Type'] == 'FERT' || $ligne['Mat_Prod_Type'] == 'HALB')) {
                    echo '36010000';
                }
            } else {
                echo $ligne['Commodity_Code'];
            }
            echo '</td>';

            echo '<th style="width: 10%;">Hiér. produits :</th>';
            echo '<td style="width: 13%;">';
            echo $ligne['Product_Code'];
            include('../SCM_Connexion_DB.php');
            $requete_product = 'SELECT Description FROM tbl_product_code WHERE Code like "' . $ligne['Product_Code'] . '"';
            $resultat_product = $mysqli_scm->query($requete_product);
            while ($row_product = $resultat_product->fetch_assoc()) {
                if ($ligne['Product_Code'] == "") {
                    echo '';
                } else if ($ligne['Product_Code'] != "") {
					if ($row_product['Description']!="")
					{
						$description=' / ' . $row_product['Description'];
					} else {
						$description="";
					}
                    echo $description;
                }
            }
            mysqli_close($mysqli_scm);
            echo '</td>';

            echo '<th>Unité :</th>';
            if ($ligne['Unit'] != "PC" && $ligne['Unit'] != "") {
                $styl = 'style="color:red"';
            } else {
                $styl = "";
            }
            echo '<td ' . $styl . '>' . $ligne['Unit'] . '</td>';
            echo '</tr>';
            echo '<tr>';
            echo '<th>GrpeGénTypPoste&nbsp:</th>';
            echo '<td>';
            $mat_prod = $ligne['Mat_Prod_Type'];
            if ($mat_prod == 'FERT' || $mat_prod == 'HALB' || $mat_prod == 'ROH') {
                echo 'NORM';
            } else if ($mat_prod == 'VERP') {
                echo 'VERP';
            }
            echo '</td>';

            echo '<th>Grp. Autorisation :</th>';
            echo '<td style="width: 25%;">';
            echo $ligne['RDO'];
            include('../SCM_Connexion_DB.php');
            $requete_rdo = 'SELECT Description FROM tbl_rdo WHERE RDO like "' . $ligne['RDO'] . '"';
            $resultat_rdo = $mysqli_scm->query($requete_rdo);
            while ($row_rdo = $resultat_rdo->fetch_assoc()) {
                if ($ligne['RDO'] == "") {
                    echo '';
                } else if ($ligne['RDO'] != "") {
                    echo ' / ' . $row_rdo['Description'];
                }
            }
            mysqli_close($mysqli_scm);
            echo '</td>';

            echo '<th>Masse :</th>';
            echo '<td>' . $ligne['Weight'] . ' ' . $ligne['Weight_Unit'] . '</td>';

            echo '<th style="width: 6%;">Projet :</th>';
            echo '<td>' . $ligne['Project'] . '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<th >Titre :</th>';
            echo '<td id="ref_title' . $ligne['ID'] . '" colspan="3">' . $ligne['Ref_Title'] . '</td>';
            echo '<th>Type Article :</th>';
            echo '<td colspan="3">' . $ligne['Material_Type'] . ' / ' . $ligne['Mat_Prod_Type'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="3">';
            echo '<fieldset>';
            echo '<legend>Données de base 2</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 15%;">Document :</th>';
            echo '<td style="width: 40%;">';
            if ($ligne['Prod_Draw'] != "" && $ligne['Prod_Draw_Rev'] != "") {
                echo '<a href="https://app.aletiq.com/parts/preview/id/' . $ligne['Prod_Draw'] . '/revision/' . $ligne['Prod_Draw_Rev'] . '" target="_blank">' . $ligne['Prod_Draw'] . ' rev' . $ligne['Prod_Draw_Rev'] . '</a>';
            } else {
                echo $ligne['Prod_Draw']. ' rev' . $ligne['Prod_Draw_Rev'];
            }
            echo '</td>';

            echo '<th style="width: 15%;">Alias/Catalogue # :</th>';
            echo '<td>' . $ligne['Alias'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="2">';
            echo '<fieldset>';
            echo '<legend>Classification</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 6%;">ECCN :</th>';

            include('../SCM_Connexion_DB.php');
            $requete_eccn = 'SELECT Description FROM tbl_eccn WHERE ECCN like "' . $ligne['ECCN'] . '"';
            $resultat_eccn = $mysqli_scm->query($requete_eccn);
            $title = "";
            while ($row_eccn = $resultat_eccn->fetch_assoc()) {
                if ($ligne['ECCN'] == "") {
                } else if ($ligne['ECCN'] != "") {
                    $title = $row_eccn['Description'];
                }
            }
            mysqli_close($mysqli_scm);
            echo '<td style="width: 8%;" title="' . $title . '">';
            echo $ligne['ECCN'];
            echo '</td>';

            $q_doc_req_db = explode(";", $ligne['Q_Doc_Req']);
            echo '<th style="width: 13%;">Exigences Doc.:</th>';
            echo '<td style="vertical-align:top;margin-top:5x;width:70%;">';
            $query_2 = 'SELECT *
                        FROM tbl_q_doc_requirements 
                        ORDER BY Code ASC';
            $resultat_2 = $mysqli->query($query_2);
			
			
			
			 echo '<div id="checkbox_block">
					<input 
						  style="background-color:transparent;vertical-align:middle" type="checkbox" 
						  id="doc_req_XX" 
						  name="doc_req_XX"
						  Value="ZXX" 
						  Class="doc_req"
						  CHECKED
						  Title="ACCORDING_TO_FCPQ20234">';
             echo '<label style="margin-left:-4px;" for="doc_req_XX"> ZXX</label></div>';
				
				
				
            while ($ligne_2 = $resultat_2->fetch_assoc()) {
                $ck = "";
                foreach ($q_doc_req_db as $doc_req) {
                    if ($doc_req == $ligne_2['Code']) {
                        $ck = "checked";
                    }
                }
				
                echo '<div id="checkbox_block"><input style="background-color:transparent;vertical-align:middle" type="checkbox" 
                                      id="doc_req_' . $ligne_2['Code'] . '" 
                                      name="doc_req_' . $ligne_2['Code'] . '"
                                      Value="' . $ligne_2['Code'] . '" 
                                      Class="doc_req"
                                      ' . $ck . '
                                      Title="' . $ligne_2['Description'] . '">';
                echo '<label style="margin-left:-4px;" for="doc_req_' . $ligne_2['Code'] . '">  ' . substr($ligne_2['Code'], -3) . '</label></div>';
            }
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td>';
            echo '<table style="width:100%">';
            echo '<tr>';
            echo '<td style="width: 50%;">';
            echo '<fieldset>';
            echo '<legend>ADV org.commerc.1</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 80%;">Qté mini commande (MOQ) :</th>';
            echo '<td>' . $ligne['MOQ'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '<td>';
            echo '<fieldset>';
            echo '<legend>CCR1</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 35%;">Taille de lot :</th>';
            echo '<td>' . $ligne['CLS'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</td>';

            echo '<td style="width: 33%;">';
            echo '<fieldset>';
            echo '<legend>ADV org.commerc.2</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 30%;">Grpe Types de poste :</th>';
            echo '<td style="width: 30%;"> ';
            if ($mat_prod == 'FERT' || $mat_prod == 'HALB' || $mat_prod == 'ROH') {
                echo 'NORM';
            } else if ($mat_prod == 'VERP') {
                echo 'VERP';
            }
            echo '</td>';

            echo '<th style="width: 7%;">Ex :</th>';
            if ($ligne['Ex'] != "NO") {
                echo '<td style="color: red;">' . $ligne['Ex'] . '</td>';
            } else {
                echo '<td>' . $ligne['Ex'] . '</td>';
            }

            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td>';
            echo '<fieldset>';
            echo '<legend>ADV données générales/div</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 30%;">Grpe art. Emb.expéd.</th>';
            echo '<td style="width: 20%;">';
            if ($mat_prod == 'FERT' || $mat_prod == 'HALB' || $mat_prod == 'ROH') {
                echo '0001';
            } else if ($mat_prod == 'VERP') {
                echo '0002';
            }
            echo '</td>';

            echo '<th style="width: 30%;">Type emballage exp.</th>';
            echo '<td style="width: 20%;" >';
            if ($mat_prod == 'FERT' || $mat_prod == 'HALB' || $mat_prod == 'ROH') {
                echo '';
            } else if ($mat_prod == 'VERP') {
                echo '0004';
            }
            echo '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '<td>';
            echo '<fieldset>';
            echo '<legend>Planif des besoins 2</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="width: 17%;">Type appro :</th>';
            echo '<td>' . $ligne['Proc_Type'] . '</td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="3">';
            echo '<fieldset>';
            echo '<legend>Management qualité</legend>';
            echo '<table id="case" border=0>';
            echo '<tr>';
            $q_inspection_db = explode(";", $ligne['Q_Inspection']);
            echo '<th style="width: 13%;">Inspection Qualité :</th>';
            echo '<td style="width: 15%;">';
            $query_2 = 'SELECT *
                                        FROM tbl_q_inspection_type 
                                        ORDER BY Code ASC';
            $resultat_2 = $mysqli->query($query_2);
            while ($ligne_2 = $resultat_2->fetch_assoc()) {
                $ck = "";
                foreach ($q_inspection_db as $q_ins) {
                    if ($q_ins == $ligne_2['Code']) {
                        $ck = "checked";
                    }
                }
                echo '<input style="background-color:transparent;margin-left:-0px;vertical-align:middle" type="checkbox" 
                                                            id="inspection_' . $ligne_2['Code'] . '" 
                                                            name="inspection_' . $ligne_2['Code'] . '" 
                                                            Value="' . $ligne_2['Code'] . '" 
                                                            class="inspection"
                                                            Title="' . $ligne_2['Description'] . '"
                                                            ' . $ck . '>';
                echo '<label   for="inspection_' . $ligne_2['Code'] . '">' . $ligne_2['Code'] . '</label>&nbsp';
            }
            echo '</td>';

            echo '<th style="width: 16%;">Régle de Dynamisation :</th>';

            $requete_dynam = 'SELECT Code, Description FROM tbl_q_dynamisation_rules
                                                WHERE Code like "' . $ligne['Q_Dynamization'] . '"';
            $resultat_dynam = $mysqli->query($requete_dynam);
            while ($row_dynam = $resultat_dynam->fetch_assoc()) {
                if ($ligne['Q_Dynamization'] != $row_dynam['Code']) {
                } else if ($ligne['Q_Dynamization'] == $row_dynam['Code']) {
                    $title = $row_dynam['Description'];
                }
            }
            echo '<td style="width: 12%;" title="' . $title . '">';
            echo $ligne['Q_Dynamization'];
            echo '</td>';
            echo '<th style="width: 12%;">Gamme de contrôle :</th>';
            $title = "";
            $requete_control = 'SELECT Code, Description FROM tbl_q_control_routing
                                                    WHERE Code like "' . $ligne['Q_Control_Routing'] . '"';
            $resultat_control = $mysqli->query($requete_control);
            while ($row_control = $resultat_control->fetch_assoc()) {
                if ($ligne['Q_Control_Routing'] != $row_control['Code']) {
                    echo '';
                } else if ($ligne['Q_Control_Routing'] == $row_control['Code']) {
                    $title = $row_control['Description'];
                }
            }
            echo '<td style="width: 15%;" title="' . $title . '">';
            echo $ligne['Q_Control_Routing'];
            echo '</td>';
			if (isset($ligne['Q_Control_Routing']))
			{ 
				if ($ligne['Q_Control_Routing']=="Z002")
				{
					$time_recept="20 jours";
				} elseif ($ligne['Q_Control_Routing']=="Z001")
				{
					$time_recept="5 jours";
				} else {
					$time_recept="-";
				}
			} else {
				$time_recept="-";
			}
			echo '<th style="width: 12%;">Tps de reception :</th>';
			echo '<td style="width:10%;text-align:left">'.$time_recept.'</td>';
			
			
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="3">';
            echo '<fieldset>';
            echo '<legend>Autres Information Diffusion</legend>';
            echo '<table border="0" id="case">';
            echo '<tr>';

            echo '<th style="width: 13%;">Diffusion # :</th>';
            echo '<td><a target="_blank" href="REL_Pack_Overview.php?ID='. $ligne['Rel_Pack_Num'].'" >'. $ligne['Rel_Pack_Num'] . '</a></td>';

            echo '<th>Activité :</th>';
            echo '<td id="activity' . $ligne['ID'] . '">' . $ligne['Activity'] . '</td>';

            echo '<th style="width: 10%;">Cust Drawing :</th>';
            if ($ligne['Cust_Drawing'] != "") {
                echo '<td style="width: 13%;">' . $ligne['Cust_Drawing'] . '<strong style="color:blue;"> rev' . $ligne['Cust_Drawing_Rev'] . '</strong></td>';
            } else {
                echo '<td style="width: 13%;">' . $ligne['Cust_Drawing'] . '<strong style="color:blue;"> ' . $ligne['Cust_Drawing_Rev'] . '</strong></td>';
            }

            echo '<th>Action :</th>';
            echo '<td><font style="color:blue; font-size:7pt;font-weight:bold">' . strtoupper($ligne['Action']) . '</font></td>';

            echo '</tr>';
            echo '<tr>';

            echo '<th style="width: 13%;">Impact En-cours:</th>';
            echo '<td style="width: 15%;">' . $ligne['Inventory_Impact'] . '</td>';

            echo '<th style="width: 10%;">Pris Dans 1 :</th>';
            echo '<td style="width: 20%;">' . $ligne['Pris_Dans1'] . '</td>';

            echo '<th>Pris Dans 2 :</th>';
            echo '<td>' . $ligne['Pris_Dans2'] . '</td>';

            echo '<th>Fab. SCM Reco. :</th>';
            if ($ligne['Internal_Mach_Rec'] == 1) {
                echo '<td><img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15px" style="margin-top:-2px;margin-bottom:-2px"""></td>';
            }

            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="3">';
            echo '<fieldset>';
            echo '<legend>Commentaires</legend>';
            echo '<table id="case">';
            echo '<tr>';
            echo '<th style="vertical-align:top">Commentaire Créateur (BE)</th>';
            echo '<th style=" vertical-align:top">Observations Diffusion</th>';
            echo '<th  style="; vertical-align:top">Commentaires Généraux</th>';
            echo '</tr>';
            echo '<tr>';
            echo '<td style="width: 33%;"><textarea style="width: 100%;" id="requestor_comments" disabled name="requestor_comments" rows="4">' . $ligne['Requestor_Comments'] . '</textarea></td>';

            echo '<td style="width: 33%;"><textarea style="width: 100%;" id="observation_comments" disabled name="observation_comments" rows="4">' . $ligne['Observations'] . '</textarea></td>';

            echo '<td style="width: 33%;"><textarea style="width: 100%;" id="general_comments" disabled name="general_comments" rows="4">' . $ligne['General_Comments'] . '</textarea></td>';
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td colspan="3">';
            echo '<fieldset>';
            echo '<legend>Signatures</legend>';
            echo '<table id="case">';
            echo '<tr>';
            //------------------------

            if ($ligne['Creation_VISA'] == "") {
                $val = "";
            } else {
                $val = '</br>' . $ligne['Creation_VISA'] . '<div id="date">' . $ligne['Creation_Date'] . '</div>';
            }
            echo '<td><div id="Table_results"> <b>Création BE</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['VISA_BE_2'] == "") {
                $val = "";
            } else {
                $val = '</br>' . $ligne['VISA_BE_2'] . '<div id="date">' . $ligne['DATE_BE_2'] . '</div>';
            }
            echo '<td><div id="Table_results"> <b>Vérification BE</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['VISA_BE_3'] == "") {
                $val = "";
            } else {
                $val = '</br>' . $ligne['VISA_BE_3'] . '<div id="date">' . $ligne['DATE_BE_3'] . '</div>';
            }
            echo '<td><div id="Table_results"> <b>Validation BE</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['VISA_Inventory'] == "") {
                if ($ligne['Inventory_Impact'] == "NO IMPACT") {
                    $val = '</br>--<div id="date" title="Impact En-cours = No Impact">--</div>';
                } else {
                    $val = "";
                }
            } else {
                $val = '</br>' . $ligne['VISA_Inventory'] . '<div id="date">' . $ligne['DATE_Inventory'] . '</div>';
            }
            echo '<td style="vertical-align:top"><div id="Table_results"><b>En Cours</b>' . $val . '</div></td>';


            //------------------------

            if ($ligne['VISA_Product'] == "") {
                $val = "";
            } else {
                $val = '<br>' . trim($ligne['VISA_Product']) . '<div id="date">' . $ligne['DATE_Product'] . '</div>';
            }
            echo '<td  ><div id="Table_results"><b>Produit</b>' . $val . '</div></td>';


            //------------------------

            if (((substr($ligne['Prod_Draw'], 0, 2) == "GA" || substr($ligne['Prod_Draw'], 0, 2) == "FT")) && $ligne['Project'] != "STAND") {
                if ($ligne['VISA_Project'] == "") {
                    $val = "";
                } else {
                    $val = '</br>' . $ligne['VISA_Project'] . '<div id="date">' . $ligne['DATE_Project'] . '</div>';
                }
            } else {
                $val = '</br>--<div id="date" title="Impact En-cours = No Impact">--</div>';
            }
            echo '<td style="vertical-align:top"><div id="Table_results"><b>Project</b>' . $val . '</div></td>';

            //------------------------

            if ($ligne['Doc_Type'] == "PUR" || $ligne['Doc_Type'] == "ASSY" || $ligne['Doc_Type'] == "DOC") {
                if ($ligne['VISA_Quality'] == "") {

                    $val = "";
                } elseif ($ligne['VISA_Quality'] != "") {

                    $val = '</br>' . $ligne['VISA_Quality'] . '<div id="date">' . $ligne['DATE_Quality'] . '</div>';
                }
                echo '<td style="vertical-align:top" ><div id="Table_results"> <b>Qualité</b>' . $val . '</div></td>';
            }

            //------------------------

            if ($ligne['Proc_Type'] == "F30" || $ligne['Proc_Type'] == "F") {
                if ($ligne['VISA_PUR_1'] == "") {
                    $val = "";
                } elseif ($ligne['VISA_PUR_1'] != "") {

                    $val = '</br>' . $ligne['VISA_PUR_1'] . '<div id="date">' . $ligne['DATE_PUR_1'] . '</div>';
                }
                echo '<td  ><div id="Table_results"><b>RFQ</b>' . $val . '</div></td>';
            }

            //------------------------

            if ($ligne['Proc_Type'] == "F30") {
                if ($ligne['VISA_PUR_2'] == "") {

                    $val = "";
                } elseif ($ligne['VISA_PUR_2'] != "") {

                    $val = '</br>' . $ligne['VISA_PUR_2'] . '<div id="date">' . $ligne['DATE_PUR_2'] . '</div>';
                }
                echo '<td ><div id="Table_results"> <b>Pris Dans</b>' . $val . '</div></td>';
            }

            //------------------------

            if (($ligne['Doc_Type'] == "MACH" || $ligne['Doc_Type'] == "MOLD" || $ligne['Doc_Type'] == "ASSY") && ($ligne['Proc_Type'] == "" || $ligne['Proc_Type'] == "E")) {
                if (trim($ligne['VISA_Prod']) == "") {
                    $val = "";
                } elseif (trim($ligne['VISA_Prod']) != "") {
                    $val = '</br>' . trim($ligne['VISA_Prod']) . '<div id="date">' . $ligne['DATE_Prod'] . '</div>';
					
                }
                echo '<td style="vertical-align:top"><div id="Table_results"> <b>Production</b>' . $val . '</div></td>';
            }

            //------------------------

            if (($ligne['Doc_Type'] == "MACH" || $ligne['Doc_Type'] == "MOLD" || $ligne['Doc_Type'] == "ASSY"  || $ligne['Doc_Type'] == "DOC") && ($ligne['Proc_Type'] == "" || $ligne['Proc_Type'] == "E")) {
                if ($ligne['VISA_Metro'] == "") {
                    $val = "";
                } elseif ($ligne['VISA_Metro'] != "") {
                    $val = '</br>' . $ligne['VISA_Metro'] . '<div id="date">' . $ligne['DATE_Metro'] . '</div>';
                }
                echo '<td style="vertical-align:top"><div id="Table_results"> <b>Métro</b>' . $val . '</div></td>';
            }

            //------------------------

            if (($ligne['Doc_Type'] == "MACH" || $ligne['Doc_Type'] == "MOLD" || $ligne['Doc_Type'] == "ASSY") && ($ligne['Proc_Type'] == "" || $ligne['Proc_Type'] == "E")) {
                if ($ligne['VISA_Supply'] == "") {
                    $val = "";
                } elseif ($ligne['VISA_Supply'] != "") {
                    $val = '</br>' . $ligne['VISA_Supply'] . '<div id="date">' . $ligne['DATE_Supply'] . '</div>';
                }
                echo '<td><div id="Table_results"> <b>Supply/Logisitique</b>' . $val . '</div></td>';
            }

            //------------------------
            echo '</tr>';
            echo '</table>';
            echo '</fieldset>';
            echo '</td>';
            echo '</tr>';

            echo '<tr>';
            echo '<td style="width: 50%;">';
            echo '<textarea style="width: 100%;" id="comment_' . $ligne['ID'] . '" name="comment" rows="4" cols="70" ></textarea>';
            echo '</td>';
            echo '<td style="width: 100%; height:18px;text-align:center; vertical-align: middle;">';
            echo '
            <div style="vertical-align:middle;width:20vw;display:inline-block;">
            ';
            if ($ligne['ID_ALETIQ'] != "") {
                echo '<a class="btn" href="https://app.aletiq.com/products/'.$ligne['ID_ALETIQ'].'/definition" target="_blank" style="background-color: #171B69;margin-right:30px">Aletiq</a>';
            }else{
                echo '<a class="btn" target="_blank" style="background-color: #171B69;margin-right:30px" onclick="alert(\'No Aletiq ID found\')">Aletiq</a>';
            }
            echo '<label style="margin-right:10px;" for="SWITCH_ALETIQ_HIDDEN' . $ligne['ID'] . '">Brouillon</label>
                <label class="switch">
                    <input type="checkbox" id="SWITCH_ALETIQ_HIDDEN' . $ligne['ID'] . '" ' . ($ligne['SWITCH_ALETIQ'] == 1 ? 'checked' : '') . '>
                    <span class="slider round"></span>
                </label>
                <label style="margin-left:10px;" for="SWITCH_ALETIQ_HIDDEN' . $ligne['ID'] . '">Validé</label>
                <input type="hidden" id="SWITCH_ALETIQ' . $ligne['ID'] . '" name="SWITCH_ALETIQ" value="' . ($ligne['SWITCH_ALETIQ'] == 1 ? '1' : '0') . '">
            </div>
            <script>
                document.getElementById("SWITCH_ALETIQ_HIDDEN' . $ligne['ID'] . '").addEventListener("change", function() {
                    const hiddenInput = document.getElementById("SWITCH_ALETIQ' . $ligne['ID'] . '");
                    hiddenInput.value = this.checked ? "1" : "0";
                });
            </script>';
            echo '<SELECT id="User_Choice' . $ligne['ID'] . '" name="user_name" type="submit" size="1">
					  <option value="%"></option>';
           include('../SCM_Connexion_DB.php');
            $requete_5 = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
								FROM tbl_user
								WHERE Department like "%GID%"';
            $resultat_5 = $mysqli_scm->query($requete_5);
            while ($row4 = $resultat_5->fetch_assoc()) {
                echo '<OPTION value ="' . $row4['Fullname'] . '">' . $row4['Fullname'] . '</option><br/>';
            }
            mysqli_close($mysqli_scm);
            echo '  </SELECT>';
            echo '<input onclick="return data_update(1,' . $ligne['ID'] . ',0)" style="vertical-align:middle;height:18px;margin-left:10px;" name="save" type="submit" id="save" class="btn orange" value="Save" title="Save the current data without validating it" />';
            echo '<input onclick="return chkName(' . $ligne['ID'] . ')" style="vertical-align:middle;height:18px;margin-left:10px;" type="submit" class="btn blue2" id="valid_form" name="valid_form" value="Sign" title="Sign off" />';
            echo '</td>';
            echo '</tr>';

            echo '</table>';
        }
        mysqli_close($mysqli);
        ?>
    </form>

    <style>
        /* The switch - the box around the slider */
    .switch {
    position: relative;
    display: inline-block;
    width: 30px;
    height: 17px;
    }

    /* Hide default HTML checkbox */
    .switch input {
    opacity: 0;
    width: 0;
    height: 0;
    }

    /* The slider */
    .slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
    }

    .slider:before {
    position: absolute;
    content: "";
    height: 13px;
    width: 13px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
    }

    input:checked + .slider {
    background-color: #15803D;
    }

    input:focus + .slider {
    box-shadow: 0 0 1px #15803D;
    }

    input:checked + .slider:before {
    -webkit-transform: translateX(13px);
    -ms-transform: translateX(13px);
    transform: translateX(13px);
    }

    /* Rounded sliders */
    .slider.round {
    border-radius: 17px;
    }

    .slider.round:before {
    border-radius: 50%;
    }
    </style>
</body>

</html>