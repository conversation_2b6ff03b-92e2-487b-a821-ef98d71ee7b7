<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!doctype html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport"
          content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    
	<link rel="stylesheet" type="text/css" href="REL_GID_Main_Form_styles.css">
	<link rel="icon" type="image/png" href="/Common_Resources/icon_sap_scm.png"/>
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	
    <title>GID Form - SAP Core Data</title>

    <script>
        function frame_update(obj,table) {
            cel_row_data = 0;
            document.getElementById("main_frame_gid").innerHTML = '<iframe name="Main_target" id="Main_target" class="main_frame_gid" alt="ok" src="REL_GID_Item.php?ID=' + obj.cells[cel_row_data].textContent + '&random=' + Math.random() +'" frameborder="0" scrollbar="yes"></iframe>';

            // Mise en lumière de la ligne cliquée
            var rows = document.getElementsByTagName('tr');

            for (var i = 0; i < rows.length; i++) {
                if (rows[i] != obj) {
                    rows[i].setAttribute('class', "unpicked_line");
                } else {
                    obj.setAttribute('class', "picked_line");
                }
            }
        }
    </script>

</head>
<body>
<?php
// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
include('REL_Workflow_Conditions.php');
?>
<form enctype="multipart/form-data" action="" method="post">
    <table border="0" id="t01">
        <tr>
            <td colspan="2">
                <div id="Main_Title">
                    GID Form - SAP Core Data
                </div>
            </td>
        </tr>

        <tr>
            <td colspan="2">
                <table style="width:100%">
                    <tr style="height:15px">
                        <td>
                            <div id="FilterTitle">
                                Pack #
                                <SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;"
                                        onchange="this.form.submit()">
                                    <option value="%"></option>
                                    <?php
                                    include('../REL_Connexion_DB.php');
                                    $query_1 = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
									FROM tbl_released_drawing 
									LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num = tbl_released_package.Rel_Pack_Num
									WHERE '.$GID_1_Conditions.'
									ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
                                    $resultat_1 = $mysqli->query($query_1);
                                    while ($row_pack = $resultat_1->fetch_assoc()) {
                                        $sel = "";
                                        if (isset($_POST['Rel_Pack_Num_Choice'])) {
                                            if ($_POST['Rel_Pack_Num_Choice'] == $row_pack['Rel_Pack_Num']) {
                                                $sel = "SELECTED";
                                            }
                                        }
                                        if ($row_pack['Rel_Pack_Num'] != "") {
                                            echo '<OPTION value ="' . $row_pack['Rel_Pack_Num'] . '"' . $sel . '>' . $row_pack['Rel_Pack_Num'] . '</option><br/>';
                                        }
                                    }
                                    mysqli_close($mysqli);
                                    ?>
                                </SELECT>
                            </div>
                        </td>

                        <td>
                            <div id="FilterTitle">
                                Reference
                                <input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;"
                                       onchange="this.form.submit()"
                                <?php if (isset($_POST['Reference_Choice'])) {
                                    echo ' Value="' . $_POST['Reference_Choice'] . '">';
                                } ?>
                            </div>
                        </td>

                        <td>
                            <div id="FilterTitle">
                                Drawing

                                <input type="text" size=20 name="Drawing_Choice" style="font-size:9pt;height:9pt;width:100pt;"
                                       onchange="this.form.submit()"
                                <?php if (isset($_POST['Drawing_Choice'])) {
                                    echo ' Value="' . $_POST['Drawing_Choice'] . '">';
                                } ?>
                            </div>
                        </td>

                        <td>
                            <div id="FilterTitle">
                                Project
                                <SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px"
                                        onchange="this.form.submit()">
                                    <OPTION value="%"></OPTION>
                                    <?php
                                    include('../REL_Connexion_DB.php');
                                    $query_2 = 'SELECT DISTINCT Project 
                                FROM tbl_released_package
                                LEFT JOIN tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE '.$GID_1_Conditions.'
                                ORDER BY tbl_released_package.Project DESC';
                                    $resultat_2 = $mysqli->query($query_2);
                                    while ($row = $resultat_2->fetch_assoc()) {
                                        $sel = "";
                                        if (isset($_POST['Project_Choice'])) {
                                            if ($_POST['Project_Choice'] == $row['Project']) {
                                                $sel = "SELECTED";
                                            }
                                        }
                                        if ($row['Project'] != "") {
                                            echo '<OPTION value ="' . $row['Project'] . '"' . $sel . '>' . $row['Project'] . '</option><br/>';
                                        }
                                    }
                                    mysqli_close($mysqli);
                                    ?>
                                </SELECT>
                            </div>
                        </td>
						
						
						 <td>
							<div id="FilterTitle">
								Action

							<!--</div>

							<div id="Filter">-->
								<SELECT name="Action_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
									<option value="%"></option>
									<?php
									include('../REL_Connexion_DB.php');
									$requete = 'SELECT DISTINCT tbl_released_drawing.Action 
										FROM tbl_released_drawing 
										WHERE '.$MOF_Conditions.'
										ORDER BY tbl_released_drawing.Action ASC';
									$resultat = $mysqli->query($requete);
									while ($row = $resultat->fetch_assoc())
									{
										$sel="";
										if (isset($_POST['Action_Choice']))
										{
											if ($_POST['Action_Choice']==$row['Action'])
											{
												$sel="SELECTED";
											} else {
												
											}
										}
										if ($row['Action']!="")
										{
											echo '<OPTION value ="' . $row['Action'] . '"'.$sel.'>' . $row['Action'] . '</option><br/>';
										}
									}
									mysqli_close($mysqli);
									?>
								</SELECT>
							</div>
						</td>
						

                        <td>
                            <div id="FilterTitle">
                                Doc Type:
                                <SELECT name="Doc_Type_Choice" type="submit" size="1" style="font-size:9pt;height:17px"
                                        onchange="this.form.submit()">
                                    <option value="%"></option>
                                    <?php
                                    include('../REL_Connexion_DB.php');
                                    $query_3 = 'SELECT DISTINCT tbl_released_drawing.Doc_Type 
                                FROM tbl_released_drawing
								LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num = tbl_released_package.Rel_Pack_Num
                                WHERE '.$GID_1_Conditions.'
                                ORDER BY tbl_released_drawing.Doc_Type ASC';
                                    $resultat_3 = $mysqli->query($query_3);
                                    while ($row = $resultat_3->fetch_assoc()) {
                                        $sel = "";
                                        if (isset($_POST['Doc_Type_Choice'])) {
                                            if ($_POST['Doc_Type_Choice'] == $row['Doc_Type']) {
                                                $sel = "SELECTED";
                                            }
                                        }
                                        if ($row['Doc_Type'] != "") {
                                            echo '<OPTION value ="' . $row['Doc_Type'] . '"' . $sel . '>' . $row['Doc_Type'] . '</option><br/>';
                                        }
                                    }
                                    mysqli_close($mysqli);
                                    ?>
                                </SELECT>
                            </div>
                        </td>

                        <td>
                            <div id="FilterTitle">
                                Proc Type:
                                <SELECT name="Proc_Type_Choice" type="submit" size="1" style="font-size:9pt;height:17px"
                                        onchange="this.form.submit()">
                                    <option value="%"></option>
                                    <?php
                                    include('../REL_Connexion_DB.php');
                                    $query_4 = 'SELECT DISTINCT tbl_released_drawing.Proc_Type 
                                FROM tbl_released_drawing
								LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num = tbl_released_package.Rel_Pack_Num
                                WHERE '.$GID_1_Conditions.'
                                ORDER BY tbl_released_drawing.Proc_Type ASC';
                                    $resultat_4 = $mysqli->query($query_4);
                                    while ($row = $resultat_4->fetch_assoc()) {
                                        $sel = "";
                                        if (isset($_POST['Proc_Type_Choice'])) {
                                            if ($_POST['Proc_Type_Choice'] == $row['Proc_Type']) {
                                                $sel = "SELECTED";
                                            }
                                        }
                                        if ($row['Proc_Type'] != "") {
                                            echo '<OPTION value ="' . $row['Proc_Type'] . '"' . $sel . '>' . $row['Proc_Type'] . '</option><br/>';
                                        }
                                    }
                                    mysqli_close($mysqli);
                                    ?>
                                </SELECT>
                            </div>
                        </td>

                        <td>
                            <input type="button" class="btn grey" onclick="window.location.href = 'REL_GID_Main_Form.php';" style="font-size:8pt; width:45px;height:18px;vertical-align:middle;text-align:center"value="Reset"/>
                        </td>
                    </tr>
                </table>

                <!--- Vérification des valeurs --->
                <?php

                if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
                    $rel_pack_num_choice = "%";
                } else {
                    $rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
                }

                if (isset($_POST['Project_Choice']) == false) {
                    $project_choice = "%";
                } else {
                    $project_choice = $_POST['Project_Choice'];
                }

                if (isset($_POST['Reference_Choice']) == false) {
                    $reference_choice = "%";
                } else {
                    if (strlen($_POST['Reference_Choice']) > 0) {
                        $reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
                    } else {
                        $reference_choice = "%";
                    }
                }

                if (isset($_POST['Drawing_Choice']) == false) {
                    $drawing_choice = "%";
                } else {
                    if (strlen($_POST['Drawing_Choice']) > 0) {
                        $drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
                    } else {
                        $drawing_choice = "%";
                    }
                }
				
				if (isset($_POST['Action_Choice']) == false) {
					$action_choice = "%";
				} else {
					$action_choice = $_POST['Action_Choice'];
				}

                if (isset($_POST['Doc_Type_Choice']) == false) {
                    $doc_type_choice = "%";
                } else {
                    $doc_type_choice = $_POST['Doc_Type_Choice'];
                }

                if (isset($_POST['Proc_Type_Choice']) == false) {
                    $proc_type_choice = "%";
                } else {
                    $proc_type_choice = $_POST['Proc_Type_Choice'];
                }

                include('../REL_Connexion_DB.php');

                $query_5 = 'SELECT *
                    FROM tbl_released_package 
                    LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                    WHERE '.$GID_1_Conditions.'
                        AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                        AND tbl_released_package.Project like "' . $project_choice . '"
                        AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                        AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                        AND tbl_released_drawing.Doc_Type like "' . $doc_type_choice . '"
						AND tbl_released_drawing.Action like "' . $action_choice . '"
                        AND tbl_released_drawing.Proc_Type like "' . $proc_type_choice . '"
                   ORDER BY tbl_released_drawing.Rel_Pack_Num ASC, tbl_released_drawing.reference ASC';


                $resultat_5 = $mysqli->query($query_5);
                $rowcount = mysqli_num_rows($resultat_5);

                echo '<tr><td colspan="2" style="margin-top:0px;padding-bottom:0px;"><div id="Result_info">Number of results: ' . $rowcount;
                echo '</td></tr>';

                echo '<tr style="height:calc(90vh - 4 * 30px)">';
                echo '<td style="vertical-align:top;width:34%;">';
                echo '<table id="t02">';
                echo '<th>Pack #</th>';
                echo '<th>Project</th>';
                echo '<th colspan=2>Reference SAP</th>';
                echo '<th>Drawing</th>';
                echo '<th title="Document Type">Doc</th>';
                echo '<th title="Procurement Type">Proc</th>';

                while ($row_5 = $resultat_5->fetch_assoc()) {
					
					$pos = strpos($row_5['General_Comments'], 'GID :');
					if ($pos === false) {
						$flag=''; // PAS DE COMMENTAIRE GID TROUVE
					} else {
						$flag='<img src="\Common_Resources\flag_red_icon.png" title="waiting on other reference(s) to be completed" style="height:10px;vertical-align:middle;margin-right:-5px;margin-left:5px;z-index:99">';
					}
					
                    echo '<tr onclick="frame_update(this, t02)">';
                    echo '<td hidden>';
                    echo $row_5['ID'];
                    echo '</td>';
                    echo '<td>';
                    echo $row_5['Rel_Pack_Num'];
                    echo '</td>';
                    echo '<td>';
                    echo $row_5['Project'];
                    echo '</td>';
                    echo '<td style="vertical-align:bottom;border-right:0px transparent">';
					if($row_5['Action']=="Creation")
					{
						echo '<img src="\Common_Resources\icon_new.png" height="9px" title="Creation of the Reference" style="z-index:1"/>';
					}
					echo '</td>';
					echo '<td style="border-left:0px transparent">';
                    echo $row_5['Reference'];
					// if ($row_5['Ex']!="NO")
					// {
						// echo '<sup><font color=red>'.$row_5['Ex'].'</font></sup>';
					// }
                    echo '</td>';
                    echo '<td>';
                    echo $row_5['Prod_Draw'];
                    echo '</td>';
                    echo '<td>';
                    echo $row_5['Doc_Type'];
                    echo '</td>';
                    echo '<td>';
                    echo $row_5['Proc_Type'].$flag;
                    echo '</td>';
                    echo '</tr>';
                }
                echo'</table>';
                echo '</td>';
                echo '<td style="vertical-align:top;">';
                echo '<p id="main_frame_gid">';
                echo '<iframe name="Main_target" id="Main_target" class="main_frame_gid" alt="ok" src="" frameborder="0" style="text-align:center;"></iframe>';
                echo '</p>';
                echo '</td>';
                echo '</tr>';
                ?>
    </table>
</form>
</body>
</html>