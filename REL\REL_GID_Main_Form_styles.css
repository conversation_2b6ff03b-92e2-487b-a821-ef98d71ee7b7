body {
  background-color: transparent;
  color: black;
  font-size: 8pt;
  font-family: Arial, sans-serif;
  font-weight: normal;
}

@media screen and (min-width: 1370px) {
  div.format {
    font-size: 9pt;
  }
}

@media screen and (max-width: 1369px) {
  div.format {
    font-size: 8pt;
  }
}

input {
  background-color: transparent;
  font-size: 8pt;
  border: 0.5px solid grey;
  height: 9.5pt;
}

select {
  background-color: transparent;
  font-size: 8pt;
  height: 13pt;
  border: 1px solid grey;
}

.input_date {
  font-family: Arial, sans-serif;
  width: 80px;
  border: 0.5px solid grey;
  height: 11pt;
}

.input-datalist {
  background-color: transparent;
  font-size: 8pt;
  font-family: Arial, sans-serif;
  height: 8pt;
  width: 80px;
}

#checkbox_block {
  display: inline-block;
  margin-right: 10px;
}

textarea {
  background-color: transparent;
  font-size: 8pt;
  font-family: Arial, sans-serif;
}

datalist {
  background-color: red;
  font-size: 8pt;
  width: 40px;
}

#Main_Title {
  margin-left: 5px;
  margin-top: 5px;
  text-align: left;
  margin-bottom: 10px;
  margin-left: 10px;
  vertical-align: middle;
  font-size: 12pt;
  font-weight: bolder;
}

#FilterTitle {
  margin-left: 2px;
  margin-right: 2px;
  text-align: center;
  vertical-align: middle;
  background: transparent;
}

div#Result_info {
  text-indent: 10px;
  margin-left: 8px;
  margin-bottom: 8px;
  margin-top: 10px;
  text-align: justify;
}

/*TABLEAU PRINCIPAL DE LA PAGE
/*-----------------------------*/
#t01 {
  width: 100%;
  border-collapse: collapse;
}

#t01 td {
  text-align: left;
  vertical-align: middle;
}

#t01 tr {
  height: 20px;
}

/*TABLEAU DONNEES
/*---------------*/

#t02 {
  border-collapse: collapse;
  width: 100%;
}

#t02 th {
  border: 0.5px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
  font-family: Arial, Helvetica, sans-serif;
  text-align: center;
}

#t02 td {
  text-align: center;
  vertical-align: middle;
  border-bottom: 0.5px solid black;
  border-left: 0.5px solid black;
  border-right: 0.5px solid black;
  border-top: 0.5px solid black;
}

#t02 tr {
  height: 15px;
}

#t02 tr:hover {
  background-color: rgb(76, 126, 160);
  cursor: pointer;
}

div#Table_results {
  text-align: center;
}

.main_frame_gid {
  width: 100%;
  height: calc(100vh - 15px - 3 * 33px);
  background-color: transparent;
  margin-top: -20px;
}

#t03 {
  border-collapse: collapse;
  width: 100%;
}

#t03 th {
  background-color: transparent;
  color: black;
  font-family: Arial, Helvetica, sans-serif;
  font-weight: bold;
  text-align: left;
  word-break: keep-all;
}

#t03 td {
  padding-top: 3px;
  padding-bottom: 3px;
}

#btn_update {
  width: 100px;
}

/*GID*/

#t04 {
  border-collapse: collapse;
  width: 100%;
}

#t04 th {
  background-color: transparent;
  color: black;
  font-family: Arial, Helvetica, sans-serif;
  font-weight: bold;
  text-align: left;
  word-break: keep-all;
}

#t04 td {
  text-align: left;
}

#case {
  width: calc(100% - 10px);
  word-break: keep-all;
}

#case td {
  word-break: keep-all;
}

#case th {
  word-break: keep-all;
}

.picked_line {
  background-color: #e8f0ff; /*#C8CAD5*/
  border: 1.5px solid grey;
  box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
}
.unpicked_line {
  background-color: transparent;
  /*border-top:0.5px solid black;*/
  /*border-bottom:0.5px solid black;*/
}
