<?php

	// création des variables
	//-----------------------
	$id = $_GET['ID'];
	
	$query_update="";
	$user="";
	$date_inven = "0000-00-00";
	if ($_GET['action']=="saving")
	{
		
	} elseif ($_GET['action']=="stock") {
		$user_stock=$_GET['userid'];
		$date_inven_stock = date('Y-m-d');
		$query_update = ' DATE_Inventory_Stock="' . $date_inven_stock . '",
						 VISA_Inventory_Stock="' . $user_stock . '",';
	} elseif ($_GET['action']=="supply") {
		$user=$_GET['userid'];
		$date_inven = date('Y-m-d');
	}
	
	$query_update = $query_update . ' DATE_Inventory="' . $date_inven . '",
									 VISA_Inventory="' . $user . '",';
	
	//Connexion à BD
	include('../REL_Connexion_DB.php');

	// Preparation Commentaire
	
	$v = 'Inventory: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

	$query_3 = 'SELECT General_Comments
				FROM tbl_released_drawing
				WHERE ID ="' . $id . '";';

	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc())
	{
		if ($_GET['comment'] != "")
		{
			$v = $v . '\r\n' . $row['General_Comments'];
		} else {
			$v = $row['General_Comments'];
		}
	} 
	//-----------------------

	$query_update = $query_update . 'General_Comments="' . $v . '"';

	$query = 'UPDATE tbl_released_drawing 
			  SET '.$query_update.'
			  WHERE ID ="' . $id . '";';

	$resultat = $mysqli->query($query);

	mysqli_close($mysqli);

?>