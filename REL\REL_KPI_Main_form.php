<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <link rel="stylesheet" type="text/css" href="REL_KPI_Form_styles.css">
</head>
<body>
    <?php
    // DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
    include('REL_Workflow_Conditions.php');
    ?>


    <?php
	
	$calc_period=180; // Nombre de jour considéré pour calculer le temps de traitement sur les diffs passées. Ex: 180 => Calcul du temps de traitement sur les 6 derniers (6*30)
	
    // -------------------------------
    // 			   BE_2
    // -------------------------------
    $input_date_conditions = "DATE(Creation_Date)";

    // Temps moyen des plans dans la page BE_2 pour les diff en cours
    $requete_be_2_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_be_2_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_be_2_alive
                    FROM `tbl_released_package`
                    WHERE ' . $BE_2_condition;

    // Temps moyen des plans dans la page BE_2 pour les diff déjà traitées
    $requete_be_2_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_BE_2), ' . $input_date_conditions . ')))  as moyen_be_2_done,
                    MAX(DATEDIFF(DATE(DATE_BE_2), ' . $input_date_conditions . ')) as max_be_2_done
                    FROM `tbl_released_package`
                    WHERE 
						VISA_BE_2 not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_BE_2))<='.$calc_period.'
                    AND Creation_VISA not like ""';

    // -------------------------------
    // 			   BE_3
    // -------------------------------
    $input_date_conditions = "DATE(DATE_BE_2)";

    // Temps moyen des plans dans la page BE_3 pour les diff en cours
    $requete_be_3_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_be_3_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_be_3_alive
                    FROM `tbl_released_package`
                    WHERE ' . $BE_3_condition;

    // Temps moyen des plans dans la page BE_3 pour les diff déjà traitées
    $requete_be_3_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_BE_3), ' . $input_date_conditions . ')))  as moyen_be_3_done,
                    MAX(DATEDIFF(DATE(DATE_BE_3), ' . $input_date_conditions . ')) as max_be_3_done
                    FROM `tbl_released_package`
                    WHERE 
						VISA_BE_3 not like ""
                    AND Creation_VISA not like ""
		            AND VISA_BE_2 not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_BE_3))<='.$calc_period.'';
					

    // -------------------------------
    // 			   Product
    // -------------------------------
    $input_date_conditions = "DATE(DATE_BE_3)";

    // Temps moyen des plans dans la page product pour les diff en cours
    $requete_product_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_product_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_product_alive
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE ' . $Product_Conditions . ' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page product pour les diff déjà traitées
    $requete_product_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_Product), ' . $input_date_conditions . ')))  as moyen_product_done,
                    MAX(DATEDIFF(DATE(DATE_Product), ' . $input_date_conditions . ')) as max_product_done
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE 
						tbl_released_drawing.VISA_Product not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_Product))<='.$calc_period.'
					AND DATEDIFF(DATE(DATE_Product), DATE(DATE_BE_3))>=0
                    AND tbl_released_package.VISA_BE_3 not like ""';

    // -------------------------------
    // 			   Inventory
    // -------------------------------
    $input_date_conditions = "DATE(DATE_BE_3)";

    // Temps moyen des plans dans la page Inventory pour les diff en cours
    $requete_inven_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_inven_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_inven_alive
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE ' . $Inventory_Conditions .' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Inventory pour les diff déjà traitées
    $requete_inven_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_Inventory), ' . $input_date_conditions . ')))  as moyen_inven_done,
                    MAX(DATEDIFF(DATE(DATE_Inventory), ' . $input_date_conditions . ')) as max_inven_done
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE 
						tbl_released_drawing.VISA_Inventory not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_Inventory))<='.$calc_period.'
                    AND tbl_released_package.VISA_BE_3 not like ""
					AND DATEDIFF(DATE(DATE_Inventory), DATE(DATE_BE_3))>=0
                    AND tbl_released_drawing.Doc_Type not like "DOC"
                    AND tbl_released_drawing.Inventory_Impact not like "NO IMPACT"';

    // -------------------------------
    // 			   Quality
    // -------------------------------
    $input_date_conditions = "DATE(DATE_BE_3)";

    // Temps moyen des plans dans la page Quality pour les diff en cours
    $requete_qual_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_qual_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_qual_alive
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE ' . $Quality_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Quality pour les diff déjà traitées
    $requete_qual_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_Quality), ' . $input_date_conditions . ')))  as moyen_qual_done,
                    MAX(DATEDIFF(DATE(DATE_Quality), ' . $input_date_conditions . ')) as max_qual_done
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE (tbl_released_drawing.Doc_Type like "PUR"
                     OR tbl_released_drawing.Doc_Type like "ASSY"
                     OR tbl_released_drawing.Doc_Type like "DOC")
                    AND tbl_released_package.VISA_BE_3 not like ""
                    AND tbl_released_drawing.VISA_Quality not like ""
					AND DATEDIFF(DATE(DATE_Quality), DATE(DATE_BE_3))>=0
					AND DATEDIFF(DATE(NOW()), DATE(DATE_Quality))<='.$calc_period.'
					AND DATEDIFF(DATE(DATE_Quality), '.$input_date_conditions.')>=0
					AND General_Comments not like "%Change of Supply%"';

    // -------------------------------
    // 			   PROJECT
    // -------------------------------

    // Date de reference avant d'arrivée dans le panier projet
    $input_date_conditions = "DATE(DATE_BE_3)";

    // Temps moyen des plans dans la page Project pour les diff en cours
    $requete_project_alive = '
	SELECT 
		count(*) as counter_alive,
		ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . '))) as moyen_project_alive,
		MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_project_alive
	FROM tbl_released_package
	LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
	WHERE'
        . $Project_Conditions.' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Project pour les diff déjà traitées
    $requete_project_done = '
	SELECT count(*) as counter_done,
		ROUND(AVG(DATEDIFF(DATE(DATE_Project), ' . $input_date_conditions . '))) as moyen_project_done,
		MAX(DATEDIFF(DATE(DATE_Project), ' . $input_date_conditions . ')) as max_project_done
        FROM tbl_released_package
        LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
        WHERE 
			tbl_released_drawing.VISA_Project not like ""
		AND DATEDIFF(DATE(NOW()), DATE(DATE_Project))<='.$calc_period.'
		AND DATEDIFF(DATE(DATE_Project), DATE(DATE_BE_3))>=0
        AND (tbl_released_package.VISA_BE_3 not like ""
        AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%"))';

    // -------------------------------
    // 			   Metro
    // -------------------------------
    $input_date_PUR_conditions = "DATE(DATE_Quality)";
    $input_date_prod_conditions = "DATE(DATE_Prod)";

    // Temps moyen des plans dans la page Metro pour les diff en cours
    $requete_metro_alive = 'SELECT COUNT(*) as counter_alive,
                    CASE WHEN Doc_Type LIKE "PUR" THEN ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_PUR_conditions . '))) END as moyen_metro_p_alive,
                    CASE WHEN Doc_Type LIKE "PUR" THEN MAX(DATEDIFF(DATE(NOW()), ' . $input_date_PUR_conditions . ')) END as max_metro_p_alive,
                    CASE WHEN Doc_Type not LIKE "PUR" THEN ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_prod_conditions . '))) END as moyen_metro_alive,
                    CASE WHEN Doc_Type not LIKE "PUR" THEN MAX(DATEDIFF(DATE(NOW()), ' . $input_date_prod_conditions . ')) END as max_metro_alive
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE ' . $METRO_Conditions;

    // Temps moyen des plans dans la page Metro pour les diff déjà traitées
    $requete_metro_done = 'SELECT
                    COUNT(*) as counter_done,
                    CASE WHEN Doc_Type LIKE "PUR" THEN ROUND(AVG(DATEDIFF(DATE(DATE_Metro), ' . $input_date_PUR_conditions . '))) END as moyen_metro_p_done,
                    CASE WHEN Doc_Type LIKE "PUR" THEN MAX(DATEDIFF(DATE(DATE_Metro), ' . $input_date_PUR_conditions . ')) END as max_metro_p_done,
                    CASE WHEN Doc_Type not LIKE "PUR" THEN ROUND(AVG(DATEDIFF(DATE(DATE_Metro), ' . $input_date_prod_conditions . '))) END as moyen_metro_done,
                    CASE WHEN Doc_Type not LIKE "PUR" THEN MAX(DATEDIFF(DATE(DATE_Metro), ' . $input_date_prod_conditions . ')) END as max_metro_done
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE ((tbl_released_drawing.VISA_Prod not like "" 
                    AND tbl_released_drawing.Proc_Type like "E")
                    OR (tbl_released_package.VISA_BE_3 not like ""
                    AND tbl_released_drawing.Doc_Type like "PUR"))
                    AND tbl_released_drawing.VISA_Metro not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_Metro))<='.$calc_period.'
					';

    // -------------------------------
    // 			   Q_PROD
    // -------------------------------

    $input_date_metro_conditions = "DATE(DATE_Metro)";

    // Temps moyen des plans dans la page Quality prod pour les diff en cours
    $requete_q_prod_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_metro_conditions . ')))  as moyen_q_prod_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_metro_conditions . ')) as max_q_prod_alive
                    FROM `tbl_released_drawing`
                    WHERE ' . $Q_PROD_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_metro_conditions . ')>0';

    // Temps moyen des plans dans la page Quality prod pour les diff déjà traitées
    $requete_q_prod_done = 'SELECT
                            COUNT(*) as counter_done,
                            ROUND(AVG(DATEDIFF(DATE(DATE_Q_PROD), ' . $input_date_metro_conditions . ')))  as moyen_q_prod_done,
                            MAX(DATEDIFF(DATE(DATE_Q_PROD), ' . $input_date_metro_conditions . ')) as max_q_prod_done
                            FROM `tbl_released_drawing`
                            WHERE 
								tbl_released_drawing.VISA_Metro not like "" 
							AND DATEDIFF(DATE(NOW()), DATE(DATE_Q_PROD))<='.$calc_period.'
							AND (tbl_released_drawing.Doc_Type like "MACH" OR tbl_released_drawing.Doc_Type like "MOLD")
                            AND tbl_released_drawing.VISA_Q_PROD not like ""
							AND DATE_Q_PROD not like "2023-05-12"';

    // -------------------------------
    // 			   Prod_ASSY
    // -------------------------------
    $input_date_conditions = "DATE(DATE_Product)";

    // Temps moyen des plans dans la page Prod_ASSY pour les diff en cours
    $requete_assy_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_assy_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_assy_alive
                    FROM `tbl_released_drawing`
                    WHERE ' . $Prod_ASSY_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Prod_ASSY pour les diff déjà traitées
    $requete_assy_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_Prod), ' . $input_date_conditions . ')))  as moyen_assy_done,
                    MAX(DATEDIFF(DATE(DATE_Prod), ' . $input_date_conditions . ')) as max_assy_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.VISA_Product not like "" 
					AND DATEDIFF(DATE(NOW()), DATE(DATE_Prod))<='.$calc_period.'
                    AND ((tbl_released_drawing.Doc_Type like "ASSY" AND (left(tbl_released_drawing.Proc_Type,1) not like "F")) 
                            OR (tbl_released_drawing.Doc_Type like "DOC"))
                    AND tbl_released_drawing.VISA_Prod not like ""';

    // -------------------------------
    // 			   Method
    // -------------------------------
    $input_date_conditions = "DATE(DATE_Prod)";

    // Temps moyen des plans dans la page Method pour les diff en cours
    $requete_meth_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_meth_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_meth_alive
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE ' . $Method_Conditions .' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Method pour les diff déjà traitées
    $requete_meth_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_Method), ' . $input_date_conditions . ')))  as moyen_meth_done,
                    MAX(DATEDIFF(DATE(DATE_Method), ' . $input_date_conditions . ')) as max_meth_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.VISA_Method not like "" 
					AND DATEDIFF(DATE(NOW()), DATE(DATE_Method))<='.$calc_period.'
                    AND (tbl_released_drawing.Doc_Type like "ASSY" 
                         OR tbl_released_drawing.Doc_Type like "DOC" 
                         OR tbl_released_drawing.Material_Type like "PACKAGING")
                    AND tbl_released_drawing.VISA_Prod not like ""';

    // -------------------------------
    // 			   Prod_MACH
    // -------------------------------
    $input_date_conditions = "DATE(DATE_Product)";

    // Temps moyen des plans dans la page Prod_MACH pour les diff en cours
    $requete_mach_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_mach_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_mach_alive
                    FROM `tbl_released_drawing`
                    WHERE ' . $Prod_MACH_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Prod_MACH pour les diff déjà traitées
    $requete_mach_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_Prod), ' . $input_date_conditions . ')))  as moyen_mach_done,
                    MAX(DATEDIFF(DATE(DATE_Prod),' . $input_date_conditions . ')) as max_mach_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.VISA_Product not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_Prod))<='.$calc_period.'
                    AND tbl_released_drawing.Doc_Type like "MACH"
                    AND tbl_released_drawing.VISA_Prod not like ""';

    // -------------------------------
    // 			   Prod_MOLDs
    // -------------------------------
    $input_date_conditions = "DATE(DATE_Product)";

    // Temps moyen des plans dans la page Prod_MOLD pour les diff en cours
    $requete_mold_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_mold_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_mold_alive
                    FROM `tbl_released_drawing`
                    WHERE ' . $Prod_MOLD_Conditions.' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Prod_MOLD pour les diff déjà traitées
    $requete_mold_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_Prod), ' . $input_date_conditions . ')))  as moyen_mold_done,
                    MAX(DATEDIFF(DATE(DATE_Prod), ' . $input_date_conditions . ')) as max_mold_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.VISA_Product not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_Prod))<='.$calc_period.'
                    AND tbl_released_drawing.Doc_Type like "MOLD"
                    AND tbl_released_drawing.VISA_Prod not like ""';

    // -------------------------------
    // 			   PUR_1
    // -------------------------------

    // Date de reference avant d'arrivée dans le panier RFQ/PUR_1
    $input_date_conditions = "GREATEST(DATE_Product, DATE_Quality)";

    // Temps moyen des plans dans la page RFQ/PUR_1 pour les diff en cours
    $requete_pur_1_alive = '
	SELECT 
		count(*) as counter_alive,
		ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . '))) as moyen_pur_1_alive,
		MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_pur_1_alive
	FROM tbl_released_drawing
	WHERE'
        . $PUR_1_RFQ_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page RFQ/PUR_1 pour les diff déjà traitées
    $requete_pur_1_done = '
	SELECT 
		count(*) as counter_done,
		ROUND(AVG(DATEDIFF(DATE(DATE_PUR_1), ' . $input_date_conditions . '))) as moyen_pur_1_done,
		MAX(DATEDIFF(DATE(DATE_PUR_1), ' . $input_date_conditions . ')) as max_pur_1_done
	FROM tbl_released_drawing
	WHERE
		tbl_released_drawing.VISA_PUR_1 not like "" 
	AND DATEDIFF(DATE(NOW()), DATE(DATE_PUR_1))<='.$calc_period.'
	AND tbl_released_drawing.Doc_Type like "PUR"
	AND (tbl_released_drawing.Proc_Type like "" OR tbl_released_drawing.Proc_Type like "F" OR tbl_released_drawing.Proc_Type like "F30")
	AND tbl_released_drawing.VISA_Quality not like ""
	AND General_Comments not like "%Change of Supply%"';

    // -------------------------------
    //           PUR_2
    // -------------------------------
    $input_date_conditions = "DATE(DATE_PUR_1)";

    // Temps moyen des plans dans la page Pris_Dans pour les diff en cours
    $requete_PUR_2_alive = 'SELECT
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_pur_2_alive, 
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_pur_2_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $PUR_2_PRISDANS_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Pris_Dans pour les diff déjà traitées
    $requete_PUR_2_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_PUR_2), ' . $input_date_conditions . ')))  as moyen_pur_2_done,
                    MAX(DATEDIFF(DATE(DATE_PUR_2), ' . $input_date_conditions . ')) as max_pur_2_done
                    FROM `tbl_released_drawing` 
                    WHERE
						tbl_released_drawing.VISA_PUR_2 not like "" 
					AND DATEDIFF(DATE(NOW()), DATE(DATE_PUR_2))<='.$calc_period.'
                    AND tbl_released_drawing.Doc_Type like "PUR"
                    AND (tbl_released_drawing.Proc_Type like "" OR tbl_released_drawing.Proc_Type like "F" OR tbl_released_drawing.Proc_Type like "F30")
                    AND tbl_released_drawing.VISA_PUR_1 not like ""';

    // -------------------------------
    //           PUR_3
    // -------------------------------
    $input_date_conditions = "DATE(DATE_GID_2)";

    // Temps moyen des plans dans la page FIA pour les diff en cours
    $requete_PUR_3_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_pur_3_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_pur_3_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $PUR_3_Conditions .' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page FIA pour les diff déjà traitées
    $requete_PUR_3_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_PUR_3), ' . $input_date_conditions . ')))  as moyen_pur_3_done,
                    MAX(DATEDIFF(DATE(DATE_PUR_3), ' . $input_date_conditions . ')) as max_pur_3_done
                    FROM `tbl_released_drawing` 
                    WHERE 
						tbl_released_drawing.VISA_PUR_3 not like "" 
					AND DATEDIFF(DATE(NOW()), DATE(DATE_PUR_3))<='.$calc_period.'
                    AND tbl_released_drawing.Doc_Type like "PUR"
                    AND (tbl_released_drawing.Proc_Type like "" OR tbl_released_drawing.Proc_Type like "F" OR tbl_released_drawing.Proc_Type like "F30")
                    AND tbl_released_drawing.VISA_GID_2 not like ""';

    // -------------------------------
    //           PUR_4
    // -------------------------------
    $input_date_conditions = "DATE(DATE_PUR_1)";

    // Temps moyen des plans dans la page RoHS/REACH Conformity pour les diff en cours
    $requete_PUR_4_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_pur_4_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_pur_4_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $PUR_4_Conditions .' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page RoHS/REACH Conformity pour les diff déjà traitées
    $requete_PUR_4_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_PUR_4),' . $input_date_conditions . ')))  as moyen_pur_4_done,
                    MAX(DATEDIFF(DATE(DATE_PUR_4), ' . $input_date_conditions . ')) as max_pur_4_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.VISA_PUR_4 not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_PUR_4))<='.$calc_period.'
                    AND tbl_released_drawing.Doc_Type like "PUR"
                    AND (tbl_released_drawing.Proc_Type like "" OR tbl_released_drawing.Proc_Type like "F" OR tbl_released_drawing.Proc_Type like "F30")
                    AND tbl_released_drawing.VISA_PUR_1 not like ""';

    // -------------------------------
    //           PUR_5
    // -------------------------------
    $input_date_conditions = "DATE(DATE_PUR_3)";

    // Temps moyen des plans dans la page Origine/HTS Fournisseur pour les diff en cours
    $requete_PUR_5_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_pur_5_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_pur_5_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $PUR_5_Conditions .' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Origine/HTS Fournisseur pour les diff déjà traitées
    $requete_PUR_5_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_PUR_5), ' . $input_date_conditions . ')))  as moyen_pur_5_done,
                    MAX(DATEDIFF(DATE(DATE_PUR_5), ' . $input_date_conditions . ')) as max_pur_5_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.VISA_PUR_5 not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_PUR_5))<='.$calc_period.'
                    AND tbl_released_drawing.Doc_Type like "PUR"
                    AND (tbl_released_drawing.Proc_Type like "" OR tbl_released_drawing.Proc_Type like "F" OR tbl_released_drawing.Proc_Type like "F30")
                    AND tbl_released_drawing.VISA_PUR_3 not like ""';

    // -------------------------------
    //           Supply
    // -------------------------------
    $input_date_conditions = "DATE(DATE_Prod)";

    // Temps moyen des plans dans la page Supply pour les diff en cours
    $requete_supply_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_supply_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_supply_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $Supply_Conditions .' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Supply pour les diff déjà traitées
    $requete_supply_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_Supply), ' . $input_date_conditions . ')))  as moyen_supply_done,
                    MAX(DATEDIFF(DATE(DATE_Supply), ' . $input_date_conditions . ')) as max_supply_done
                    FROM `tbl_released_drawing`
                    WHERE 
							tbl_released_drawing.VISA_Supply not like ""
						AND DATEDIFF(DATE(NOW()), DATE(DATE_Supply))<='.$calc_period.'
						AND DATEDIFF(DATE(DATE_Supply), '.$input_date_conditions.')>=0
                    	AND (tbl_released_drawing.Doc_Type like "ASSY"
                         OR tbl_released_drawing.Doc_Type like "MACH"
                         OR tbl_released_drawing.Doc_Type like "MOLD")
                        AND tbl_released_drawing.VISA_Prod not like ""';

    // -------------------------------
    //           MOF
    // -------------------------------
    $input_date_conditions = "DATE(DATE_Metro)";

    // Temps moyen des plans dans la page MOF pour les diff en cours
    $requete_mof_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_mof_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_mof_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $MOF_Conditions .' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page MOF pour les diff déjà traitées
    $requete_mof_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_MOF), ' . $input_date_conditions . ')))  as moyen_mof_done,
                    MAX(DATEDIFF(DATE(DATE_MOF), ' . $input_date_conditions . ')) as max_mof_done
                    FROM `tbl_released_drawing`
                    WHERE 
						DATEDIFF(DATE(NOW()), DATE(DATE_MOF))<='.$calc_period.'
					AND	((tbl_released_drawing.Doc_type like "ASSY" 
                    AND tbl_released_drawing.VISA_METRO not like "")
                    OR (tbl_released_drawing.Doc_type like "DOC"))
                    AND tbl_released_drawing.VISA_PROD not like ""
                    AND tbl_released_drawing.VISA_MOF not like ""';

    // -------------------------------
    //           GID_1
    // -------------------------------
    $input_date_conditions = "GREATEST(DATE_Product,DATE_Quality,DATE_Project,DATE_Metro)";

    // Temps moyen des plans dans la page GID_1 pour les diff en cours
    $requete_gid_1_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_gid_1_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_gid_1_alive
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE ' . $GID_1_Conditions .' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page GID_1 pour les diff déjà traitées
    $requete_gid_1_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_GID), ' . $input_date_conditions . ')))  as moyen_gid_1_done,
                    MAX(DATEDIFF(DATE(DATE_GID), ' . $input_date_conditions . ')) as max_gid_1_done
                    FROM `tbl_released_drawing`
                    LEFT JOIN tbl_released_package ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num 
                    WHERE (
                        (
                            (
                                tbl_released_drawing.VISA_PUR_1 not like ""
                            AND tbl_released_drawing.Proc_Type like "F"
                            ) 
                        OR  (
                                tbl_released_drawing.VISA_PUR_2 not like ""
                            AND tbl_released_drawing.Proc_Type like "F30"
                            )
                        OR  (
                                tbl_released_drawing.Doc_Type not like "ASSY"  
                            AND tbl_released_drawing.VISA_Metro not like ""
                            AND tbl_released_drawing.VISA_Supply not like ""
                            )
                        OR  (	
                                tbl_released_drawing.Doc_Type like "ASSY"
                            AND tbl_released_drawing.VISA_Quality not like "" 
                            AND tbl_released_drawing.VISA_Metro not like ""
                            AND tbl_released_drawing.VISA_Supply not like ""
                            AND (
                                (tbl_released_package.Project not like "STAND" AND tbl_released_drawing.VISA_Project not like "" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%"))  
                                OR 
                                (tbl_released_package.Project not like "STAND" AND tbl_released_drawing.VISA_Project like "" AND (tbl_released_drawing.Prod_Draw not like "GA%" OR tbl_released_drawing.Prod_Draw not like "FT%")) 
                                OR 
                                (tbl_released_package.Project like "STAND" AND tbl_released_drawing.VISA_Project like "")
                                )
                            )
                        OR  (	
                                tbl_released_drawing.Doc_Type like "DOC"
                            AND tbl_released_drawing.VISA_Quality not like "" 
                            AND tbl_released_drawing.VISA_Metro not like ""
                            AND (
                                (tbl_released_package.Project not like "STAND" AND tbl_released_drawing.VISA_Project not like "" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%"))  
                                OR 
                                (tbl_released_package.Project not like "STAND" AND tbl_released_drawing.VISA_Project like "" AND (tbl_released_drawing.Prod_Draw not like "GA%" OR tbl_released_drawing.Prod_Draw not like "FT%")) 
                                OR 
                                (tbl_released_package.Project like "STAND" AND tbl_released_drawing.VISA_Project like "")
                                )
                            )
                            
                        )
                    
                        AND 
                        
                        (
                            (tbl_released_drawing.VISA_Inventory not like "" AND  (tbl_released_drawing.Inventory_Impact like "TO BE SCRAPPED" OR tbl_released_drawing.Inventory_Impact like "TO BE UPDATED"))
                        OR  (tbl_released_drawing.VISA_Inventory like "" AND (tbl_released_drawing.Inventory_Impact like "NO IMPACT" OR tbl_released_drawing.Doc_Type like "DOC"))
                        )
                        
                        
                    ) 
                    AND DATEDIFF(DATE(NOW()), DATE(DATE_GID))<='.$calc_period.'
                    AND VISA_GID like ""';

    // -------------------------------
    //           GID_2
    // -------------------------------
    $input_date_conditions = "DATE(DATE_GID)";

    // Temps moyen des plans dans la page GID_2 pour les diff en cours
    $requete_gid_2_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_gid_2_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_gid_2_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $GID_2_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page GID_2 pour les diff déjà traitées
    $requete_gid_2_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_GID_2), ' . $input_date_conditions . ')))  as moyen_gid_2_done,
                    MAX(DATEDIFF(DATE(DATE_GID_2), ' . $input_date_conditions . ')) as max_gid_2_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.VISA_GID not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_GID_2))<='.$calc_period.'
                    AND tbl_released_drawing.VISA_GID_2 not like ""';

    // -------------------------------
    //           Laboratory
    // -------------------------------
    $input_date_conditions = "DATE(DATE_MOF)";

    // Temps moyen des plans dans la page Laboratory pour les diff en cours
    $requete_labo_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_labo_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_labo_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $LABO_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Laboratory pour les diff déjà traitées
    $requete_labo_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_LABO), ' . $input_date_conditions . ')))  as moyen_labo_done,
                    MAX(DATEDIFF(DATE(DATE_LABO), ' . $input_date_conditions . ')) as max_labo_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.Doc_type like "ASSY" 
					AND DATEDIFF(DATE(NOW()), DATE(DATE_LABO))<='.$calc_period.'
                    AND tbl_released_drawing.VISA_MOF not like ""
                    AND tbl_released_drawing.VISA_LABO not like ""';

    // -------------------------------
    //           Saisie Gamme
    // -------------------------------
    $input_date_conditions = "GREATEST(DATE_MOF,DATE_GID_2)";

    // Temps moyen des plans dans la page Saisie Gamme pour les diff en cours
    $requete_routing_entry_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_routing_entry_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_routing_entry_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $ROUTING_ENTRY_Conditions .'AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Saisie Gamme pour les diff déjà traitées
    $requete_routing_entry_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_ROUTING_ENTRY), ' . $input_date_conditions . ')))  as moyen_routing_entry_done,
                    MAX(DATEDIFF(DATE(DATE_ROUTING_ENTRY), ' . $input_date_conditions . ')) as max_routing_entry_done
                    FROM `tbl_released_drawing`
                    WHERE (( tbl_released_drawing.Doc_Type like "ASSY"
                    AND tbl_released_drawing.VISA_MOF not like "") 
                    OR( (tbl_released_drawing.Doc_Type like "MOLD" OR tbl_released_drawing.Doc_Type like "MACH")
                    AND tbl_released_drawing.VISA_PROD not like ""))
                    AND tbl_released_drawing.VISA_GID_2 not like ""
                    AND tbl_released_drawing.VISA_ROUTING_ENTRY not like ""
					AND DATEDIFF(DATE(NOW()), DATE(DATE_ROUTING_ENTRY))<='.$calc_period;

    // -------------------------------
    //           Costing
    // -------------------------------
    $input_date_conditions = "GREATEST(DATE_ROUTING_ENTRY,DATE_PUR_3)";

    // Temps moyen des plans dans la page Costing pour les diff en cours
    $requete_fin_alive = 'SELECT 
                    COUNT(*) as counter_alive,
                    ROUND(AVG(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')))  as moyen_fin_alive,
                    MAX(DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')) as max_fin_alive
                    FROM `tbl_released_drawing` 
                    WHERE ' . $Finance_Conditions.' AND DATEDIFF(DATE(NOW()), ' . $input_date_conditions . ')>0';

    // Temps moyen des plans dans la page Costing pour les diff déjà traitées
    $requete_fin_done = 'SELECT
                    COUNT(*) as counter_done,
                    ROUND(AVG(DATEDIFF(DATE(DATE_FINANCE), ' . $input_date_conditions . ')))  as moyen_fin_done,
                    MAX(DATEDIFF(DATE(DATE_FINANCE), ' . $input_date_conditions . ')) as max_fin_done
                    FROM `tbl_released_drawing`
                    WHERE 
						tbl_released_drawing.Doc_type not like "DOC"
					AND DATEDIFF(DATE(NOW()), DATE(DATE_FINANCE))<='.$calc_period.'
                    AND tbl_released_drawing.VISA_Finance not like ""
                    AND ((tbl_released_drawing.VISA_PUR_3 not like ""  AND tbl_released_drawing.Doc_type like "PUR")
                    OR (tbl_released_drawing.VISA_ROUTING_ENTRY not like ""  AND tbl_released_drawing.Doc_type not like "PUR"))';


    include('../REL_Connexion_DB.php');

    // Temps moyen et Temps max ---------------------------------

    $resultat_be_2_alive = $mysqli->query($requete_be_2_alive);
    while ($row = $resultat_be_2_alive->fetch_assoc()) {
        $be_2_counter_alive = $row['counter_alive'];
        $be_2_moyen_alive = $row['moyen_be_2_alive'];
        $be_2_max_alive = $row['max_be_2_alive'];
    }
    $resultat_be_2_done = $mysqli->query($requete_be_2_done);
    while ($row = $resultat_be_2_done->fetch_assoc()) {
        $be_2_counter_done = $row['counter_done'];
        $be_2_moyen_done = $row['moyen_be_2_done'];
        $be_2_max_done = $row['max_be_2_done'];
    }

    $resultat_be_3_alive = $mysqli->query($requete_be_3_alive);
    while ($row = $resultat_be_3_alive->fetch_assoc()) {
        $be_3_counter_alive = $row['counter_alive'];
        $be_3_moyen_alive = $row['moyen_be_3_alive'];
        $be_3_max_alive = $row['max_be_3_alive'];
    }
    $resultat_be_3_done = $mysqli->query($requete_be_3_done);
    while ($row = $resultat_be_3_done->fetch_assoc()) {
        $be_3_counter_done = $row['counter_done'];
        $be_3_moyen_done = $row['moyen_be_3_done'];
        $be_3_max_done = $row['max_be_3_done'];
    }

    $resultat_product_alive = $mysqli->query($requete_product_alive);
    while ($row = $resultat_product_alive->fetch_assoc()) {
        $product_counter_alive = $row['counter_alive'];
        $product_moyen_alive = $row['moyen_product_alive'];
        $product_max_alive = $row['max_product_alive'];
    }
    $resultat_product_done = $mysqli->query($requete_product_done);
    while ($row = $resultat_product_done->fetch_assoc()) {
        $product_counter_done = $row['counter_done'];
        $product_moyen_done = $row['moyen_product_done'];
        $product_max_done = $row['max_product_done'];
    }

    $resultat_inven_alive = $mysqli->query($requete_inven_alive);
    while ($row = $resultat_inven_alive->fetch_assoc()) {
        $inven_counter_alive = $row['counter_alive'];
        $inven_moyen_alive = $row['moyen_inven_alive'];
        $inven_max_alive = $row['max_inven_alive'];
    }
    $resultat_inven_done = $mysqli->query($requete_inven_done);
    while ($row = $resultat_inven_done->fetch_assoc()) {
        $inven_counter_done = $row['counter_done'];
        $inven_moyen_done = $row['moyen_inven_done'];
        $inven_max_done = $row['max_inven_done'];
    }

    $resultat_qual_alive = $mysqli->query($requete_qual_alive);
    while ($row = $resultat_qual_alive->fetch_assoc()) {
        $qual_counter_alive = $row['counter_alive'];
        $qual_moyen_alive = $row['moyen_qual_alive'];
        $qual_max_alive = $row['max_qual_alive'];
    }
    $resultat_qual_done = $mysqli->query($requete_qual_done);
    while ($row = $resultat_qual_done->fetch_assoc()) {
        $qual_counter_done = $row['counter_done'];
        $qual_moyen_done = $row['moyen_qual_done'];
        $qual_max_done = $row['max_qual_done'];
    }

    //     PROJECT
    // ---------------
    $resultat_project_alive = $mysqli->query($requete_project_alive);
    while ($row = $resultat_project_alive->fetch_assoc()) {
        $project_counter_alive = $row['counter_alive'];
        $project_moyen_alive = $row['moyen_project_alive'];
        $project_max_alive = $row['max_project_alive'];
    }
    $resultat_project_done = $mysqli->query($requete_project_done);
    while ($row = $resultat_project_done->fetch_assoc()) {
        $project_counter_done = $row['counter_done'];
        $project_moyen_done = $row['moyen_project_done'];
        $project_max_done = $row['max_project_done'];
    }
    // ---------------

    $resultat_metro_alive = $mysqli->query($requete_metro_alive);
    while ($row = $resultat_metro_alive->fetch_assoc()) {
        $metro_counter_alive = $row['counter_alive'];
        $metro_moyen_p_alive = $row['moyen_metro_p_alive'];
        $metro_max_p_alive = $row['max_metro_p_alive'];
        $metro_moyen_alive = $row['moyen_metro_alive'];
        $metro_max_alive = $row['max_metro_alive'];
    }
    $resultat_metro_done = $mysqli->query($requete_metro_done);
    while ($row = $resultat_metro_done->fetch_assoc()) {
        $metro_counter_done = $row['counter_done'];
        $metro_moyen_p_done = $row['moyen_metro_p_done'];
        $metro_max_p_done = $row['max_metro_p_done'];
        $metro_moyen_done = $row['moyen_metro_done'];
        $metro_max_done = $row['max_metro_done'];
    }

    $resultat_q_prod_alive = $mysqli->query($requete_q_prod_alive);
    while ($row = $resultat_q_prod_alive->fetch_assoc()) {
        $q_prod_counter_alive = $row['counter_alive'];
        $q_prod_moyen_alive = $row['moyen_q_prod_alive'];
        $q_prod_max_alive = $row['max_q_prod_alive'];
    }
    $resultat_q_prod_done = $mysqli->query($requete_q_prod_done);
    while ($row = $resultat_q_prod_done->fetch_assoc()) {
        $q_prod_counter_done = $row['counter_done'];
        $q_prod_moyen_done = $row['moyen_q_prod_done'];
        $q_prod_max_done = $row['max_q_prod_done'];
    }

    $resultat_assy_alive = $mysqli->query($requete_assy_alive);
    while ($row = $resultat_assy_alive->fetch_assoc()) {
        $assy_counter_alive = $row['counter_alive'];
        $assy_moyen_alive = $row['moyen_assy_alive'];
        $assy_max_alive = $row['max_assy_alive'];
    }
    $resultat_assy_done = $mysqli->query($requete_assy_done);
    while ($row = $resultat_assy_done->fetch_assoc()) {
        $assy_counter_done = $row['counter_done'];
        $assy_moyen_done = $row['moyen_assy_done'];
        $assy_max_done = $row['max_assy_done'];
    }

    $resultat_meth_alive = $mysqli->query($requete_meth_alive);
    while ($row = $resultat_meth_alive->fetch_assoc()) {
        $meth_counter_alive = $row['counter_alive'];
        $meth_moyen_alive = $row['moyen_meth_alive'];
        $meth_max_alive = $row['max_meth_alive'];
    }
    $resultat_meth_done = $mysqli->query($requete_meth_done);
    while ($row = $resultat_meth_done->fetch_assoc()) {
        $meth_counter_done = $row['counter_done'];
        $meth_moyen_done = $row['moyen_meth_done'];
        $meth_max_done = $row['max_meth_done'];
    }

    $resultat_mach_alive = $mysqli->query($requete_mach_alive);
    while ($row = $resultat_mach_alive->fetch_assoc()) {
        $mach_counter_alive = $row['counter_alive'];
        $mach_moyen_alive = $row['moyen_mach_alive'];
        $mach_max_alive = $row['max_mach_alive'];
    }
    $resultat_mach_done = $mysqli->query($requete_mach_done);
    while ($row = $resultat_mach_done->fetch_assoc()) {
        $mach_counter_done = $row['counter_done'];
        $mach_moyen_done = $row['moyen_mach_done'];
        $mach_max_done = $row['max_mach_done'];
    }

    $resultat_mold_alive = $mysqli->query($requete_mold_alive);
    while ($row = $resultat_mold_alive->fetch_assoc()) {
        $mold_counter_alive = $row['counter_alive'];
        $mold_moyen_alive = $row['moyen_mold_alive'];
        $mold_max_alive = $row['max_mold_alive'];
    }
    $resultat_mold_done = $mysqli->query($requete_mold_done);
    while ($row = $resultat_mold_done->fetch_assoc()) {
        $mold_counter_done = $row['counter_done'];
        $mold_moyen_done = $row['moyen_mold_done'];
        $mold_max_done = $row['max_mold_done'];
    }

    $resultat_pur_1_alive = $mysqli->query($requete_pur_1_alive);
    while ($row = $resultat_pur_1_alive->fetch_assoc()) {
        $pur_1_counter_alive = $row['counter_alive'];
        $pur_1_moyen_alive = $row['moyen_pur_1_alive'];
        $pur_1_max_alive = $row['max_pur_1_alive'];
    }
    $resultat_pur_1_done = $mysqli->query($requete_pur_1_done);
    while ($row = $resultat_pur_1_done->fetch_assoc()) {
        $pur_1_counter_done = $row['counter_done'];
        $pur_1_moyen_done = $row['moyen_pur_1_done'];
        $pur_1_max_done = $row['max_pur_1_done'];
    }

    $resultat_pur_2_alive = $mysqli->query($requete_PUR_2_alive);
    while ($row = $resultat_pur_2_alive->fetch_assoc()) {
        $pur_2_counter_alive = $row['counter_alive'];
        $pur_2_moyen_alive = $row['moyen_pur_2_alive'];
        $pur_2_max_alive = $row['max_pur_2_alive'];
    }
    $resultat_pur_2_done = $mysqli->query($requete_PUR_2_done);
    while ($row = $resultat_pur_2_done->fetch_assoc()) {
        $pur_2_counter_done = $row['counter_done'];
        $pur_2_moyen_done = $row['moyen_pur_2_done'];
        $pur_2_max_done = $row['max_pur_2_done'];
    }

    $resultat_pur_3_alive = $mysqli->query($requete_PUR_3_alive);
    while ($row = $resultat_pur_3_alive->fetch_assoc()) {
        $pur_3_counter_alive = $row['counter_alive'];
        $pur_3_moyen_alive = $row['moyen_pur_3_alive'];
        $pur_3_max_alive = $row['max_pur_3_alive'];
    }
    $resultat_pur_3_done = $mysqli->query($requete_PUR_3_done);
    while ($row = $resultat_pur_3_done->fetch_assoc()) {
        $pur_3_counter_done = $row['counter_done'];
        $pur_3_moyen_done = $row['moyen_pur_3_done'];
        $pur_3_max_done = $row['max_pur_3_done'];
    }

    $resultat_pur_4_alive = $mysqli->query($requete_PUR_4_alive);
    while ($row = $resultat_pur_4_alive->fetch_assoc()) {
        $pur_4_counter_alive = $row['counter_alive'];
        $pur_4_moyen_alive = $row['moyen_pur_4_alive'];
        $pur_4_max_alive = $row['max_pur_4_alive'];
    }
    $resultat_pur_4_done = $mysqli->query($requete_PUR_4_done);
    while ($row = $resultat_pur_4_done->fetch_assoc()) {
        $pur_4_counter_done = $row['counter_done'];
        $pur_4_moyen_done = $row['moyen_pur_4_done'];
        $pur_4_max_done = $row['max_pur_4_done'];
    }

    $resultat_pur_5_alive = $mysqli->query($requete_PUR_5_alive);
    while ($row = $resultat_pur_5_alive->fetch_assoc()) {
        $pur_5_counter_alive = $row['counter_alive'];
        $pur_5_moyen_alive = $row['moyen_pur_5_alive'];
        $pur_5_max_alive = $row['max_pur_5_alive'];
    }
    $resultat_pur_5_done = $mysqli->query($requete_PUR_5_done);
    while ($row = $resultat_pur_5_done->fetch_assoc()) {
        $pur_5_counter_done = $row['counter_done'];
        $pur_5_moyen_done = $row['moyen_pur_5_done'];
        $pur_5_max_done = $row['max_pur_5_done'];
    }

    $resultat_supply_alive = $mysqli->query($requete_supply_alive);
    while ($row = $resultat_supply_alive->fetch_assoc()) {
        $supply_counter_alive = $row['counter_alive'];
        $supply_moyen_alive = $row['moyen_supply_alive'];
        $supply_max_alive = $row['max_supply_alive'];
    }
    $resultat_supply_done = $mysqli->query($requete_supply_done);
    while ($row = $resultat_supply_done->fetch_assoc()) {
        $supply_counter_done = $row['counter_done'];
        $supply_moyen_done = $row['moyen_supply_done'];
        $supply_max_done = $row['max_supply_done'];
    }

    $resultat_mof_alive = $mysqli->query($requete_mof_alive);
    while ($row = $resultat_mof_alive->fetch_assoc()) {
        $mof_counter_alive = $row['counter_alive'];
        $mof_moyen_alive = $row['moyen_mof_alive'];
        $mof_max_alive = $row['max_mof_alive'];
    }
    $resultat_mof_done = $mysqli->query($requete_mof_done);
    while ($row = $resultat_mof_done->fetch_assoc()) {
        $mof_counter_done = $row['counter_done'];
        $mof_moyen_done = $row['moyen_mof_done'];
        $mof_max_done = $row['max_mof_done'];
    }

    $resultat_gid_1_alive = $mysqli->query($requete_gid_1_alive);
    while ($row = $resultat_gid_1_alive->fetch_assoc()) {
        $gid_1_counter_alive = $row['counter_alive'];
        $gid_1_moyen_alive = $row['moyen_gid_1_alive'];
        $gid_1_max_alive = $row['max_gid_1_alive'];
    }
    $resultat_gid_1_done = $mysqli->query($requete_gid_1_done);
    while ($row = $resultat_gid_1_done->fetch_assoc()) {
        $gid_1_counter_done = $row['counter_done'];
        $gid_1_moyen_done = $row['moyen_gid_1_done'];
        $gid_1_max_done = $row['max_gid_1_done'];
    }

    $resultat_gid_2_alive = $mysqli->query($requete_gid_2_alive);
    while ($row = $resultat_gid_2_alive->fetch_assoc()) {
        $gid_2_counter_alive = $row['counter_alive'];
        $gid_2_moyen_alive = $row['moyen_gid_2_alive'];
        $gid_2_max_alive = $row['max_gid_2_alive'];
    }
    $resultat_gid_2_done = $mysqli->query($requete_gid_2_done);
    while ($row = $resultat_gid_2_done->fetch_assoc()) {
        $gid_2_counter_done = $row['counter_done'];
        $gid_2_moyen_done = $row['moyen_gid_2_done'];
        $gid_2_max_done = $row['max_gid_2_done'];
    }

    $resultat_labo_alive = $mysqli->query($requete_labo_alive);
    while ($row = $resultat_labo_alive->fetch_assoc()) {
        $labo_counter_alive = $row['counter_alive'];
        $labo_moyen_alive = $row['moyen_labo_alive'];
        $labo_max_alive = $row['max_labo_alive'];
    }
    $resultat_labo_done = $mysqli->query($requete_labo_done);
    while ($row = $resultat_labo_done->fetch_assoc()) {
        $labo_counter_done = $row['counter_done'];
        $labo_moyen_done = $row['moyen_labo_done'];
        $labo_max_done = $row['max_labo_done'];
    }

    $resultat_routing_entry_alive = $mysqli->query($requete_routing_entry_alive);
    while ($row = $resultat_routing_entry_alive->fetch_assoc()) {
        $routing_entry_counter_alive = $row['counter_alive'];
        $routing_entry_moyen_alive = $row['moyen_routing_entry_alive'];
        $routing_entry_max_alive = $row['max_routing_entry_alive'];
    }
    $resultat_routing_entry_done = $mysqli->query($requete_routing_entry_done);
    while ($row = $resultat_routing_entry_done->fetch_assoc()) {
        $routing_entry_counter_done = $row['counter_done'];
        $routing_entry_moyen_done = $row['moyen_routing_entry_done'];
        $routing_entry_max_done = $row['max_routing_entry_done'];
    }

    $resultat_fin_alive = $mysqli->query($requete_fin_alive);
    while ($row = $resultat_fin_alive->fetch_assoc()) {
        $fin_counter_alive = $row['counter_alive'];
        $fin_moyen_alive = $row['moyen_fin_alive'];
        $fin_max_alive = $row['max_fin_alive'];
    }
    $resultat_fin_done = $mysqli->query($requete_fin_done);
    while ($row = $resultat_fin_done->fetch_assoc()) {
        $fin_counter_done = $row['counter_done'];
        $fin_moyen_done = $row['moyen_fin_done'];
        $fin_max_done = $row['max_fin_done'];
    }


    mysqli_close($mysqli);

    ?>



    <table id="t09" border=0>

        <tr>
            <td style="padding-top:15px; padding-left:5px;font-weight:bold;text-align:left">
                <u>TABLEAU DE BORD - DIFFUSION</u>
                </div>
            </td>
        </tr>
        <tr>
            <td style="padding-left:5px;text-align:justify">
                <div id="Body">
                    Pour chaque département, le temps moyen et maximum de passage en jours sont calculés.<Br> Les temps sont calculés sur les diffusions en cours (A traiter), mais aussi celles qui ont été traitées les <?php echo $calc_period; ?> derniers jours
                </div>
            </td>
        </tr>
        <tr>
            <td>

                <?php
                echo '<table id="t02">';
                echo '<thead>';
                echo '<tr><td colspan=5 style="font-weight:bold">Chemin Critique</td></tr>';

                echo '	<th rowspan=2 style="background-color: rgb(16, 112, 177);">Dpt</th>';
                echo '	<th colspan=2 style="background-color: rgb(16, 112, 177);">A Traiter</th>';
                echo '	<th colspan=2 style="background-color: rgb(16, 112, 177);">'.$calc_period.' derniers jours</th>';
                echo '</tr>';

                echo '	<th style="background-color: rgb(16, 112, 177);" hidden>#&nbspRef</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);">Moyen (j)</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);">Max (j)</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);" hidden>#&nbspRef</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);">Moyen (j)</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);">Max (j)</th>';

                echo '</thead>';

                echo '<tr>';
                echo '<td style="background-color:rgb(216,93,202);color: white;">Verif. BE</td>';
                echo '<td style="width:13%" hidden>' . $be_2_counter_alive . '</td>';
                echo '<td style="width:18%">' . $be_2_moyen_alive . '</td>';
                echo '<td style="width:18%">' . $be_2_max_alive . '</td>';
                echo '<td style="width:13%" hidden>' . $be_2_counter_done . '</td>';
                echo '<td  title="' . $be_2_counter_done.' références" style="width:18%">' . $be_2_moyen_done . '</td>';
                echo '<td style="width:18%">' . $be_2_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(216,93,202);color: white;">Val. BE</td>';
                echo '<td hidden>' . $be_3_counter_alive . '</td>';
                echo '<td>' . $be_3_moyen_alive . '</td>';
                echo '<td>' . $be_3_max_alive . '</td>';
                echo '<td hidden>' . $be_3_counter_done . '</td>';
                echo '<td title="' . $be_3_counter_done.' références">' . $be_3_moyen_done . '</td>';
                echo '<td>' . $be_3_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(0,0,0);color: white;">Product</td>';
                echo '<td hidden>' . $product_counter_alive . '</td>';
                echo '<td>' . $product_moyen_alive . '</td>';
                echo '<td>' . $product_max_alive . '</td>';
                echo '<td hidden>' . $product_counter_done . '</td>';
                echo '<td title="' . $product_counter_done.' références">' . $product_moyen_done . '</td>';
                echo '<td>' . $product_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(239,117,33);color: white;">En-cours/Inventory</td>';
                echo '<td hidden>' . $inven_counter_alive . '</td>';
                echo '<td>' . $inven_moyen_alive . '</td>';
                echo '<td>' . $inven_max_alive . '</td>';
                echo '<td hidden>' . $inven_counter_done . '</td>';
                //echo '<td title="' . $inven_counter_done.'">' . $inven_moyen_done . ' &nbsp<font style="font-size:7pt">(' . $inven_counter_done.')</font></td>';
                echo '<td title="' . $inven_counter_done.' références">' . $inven_moyen_done . '</font></td>';
				echo '<td>' . $inven_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(252,196,0);color: white;">Quality</td>';
                echo '<td hidden>' . $qual_counter_alive . '</td>';
                echo '<td>' . $qual_moyen_alive . '</td>';
                echo '<td>' . $qual_max_alive . '</td>';
                echo '<td hidden>' . $qual_counter_done . '</td>';
                echo '<td title="' . $qual_counter_done.' références">' . $qual_moyen_done . '</td>';
                echo '<td>' . $qual_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(194,194,194);color: white;">Project</td>';
                echo '<td hidden>' . $project_counter_alive . '</td>';
                echo '<td>' . $project_moyen_alive . '</td>';
                echo '<td>' . $project_max_alive . '</td>';
                echo '<td hidden>' . $project_counter_done . '</td>';
                echo '<td title="' . $project_counter_done.' références">' . $project_moyen_done . '</td>';
                echo '<td>' . $project_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(255,198,33);color: white;">Metrology</td>';
                echo '<td hidden>' . $metro_counter_alive . '</td>';
                echo '<td>';
                if ($metro_moyen_alive != "") {
                    echo $metro_moyen_alive;
                    if ($metro_moyen_p_alive != "") {
                        echo ' / ' . $metro_moyen_p_alive;
                    }
                } else if ($metro_moyen_p_done != "") {
                    echo $metro_moyen_p_done;
                }
                echo '</td>';
                echo '<td>';
                if ($metro_max_alive != "") {
                    echo $metro_max_alive;
                    if ($metro_max_p_alive != "") {
                        echo ' / ' . $metro_max_p_alive;
                    }
                } else if ($metro_max_p_alive != "") {
                    echo $metro_max_p_alive;
                }
                echo '</td>';
                echo '<td hidden>' . $metro_counter_done . '</td>';
                echo '<td title="' . $metro_counter_done.' références">';
                if ($metro_moyen_done != "") {
                    echo $metro_moyen_done;
                    if ($metro_moyen_p_done != "") {
                        echo ' / ' . $metro_moyen_p_done;
                    }
                } else if ($metro_moyen_p_done != "") {
                    echo $metro_moyen_p_done;
                }
                echo '</td>';
                echo '<td>';
                if ($metro_max_done != "") {
                    echo $metro_max_done;
                    if ($metro_max_p_done != "") {
                        echo ' / ' . $metro_max_p_done;
                    }
                } else if ($metro_max_p_done != "") {
                    echo $metro_max_p_done;
                }
                echo '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,178,67);color: white;">Assembly</td>';
                echo '<td hidden>' . $assy_counter_alive . '</td>';
                echo '<td>' . $assy_moyen_alive . '</td>';
                echo '<td>' . $assy_max_alive . '</td>';
                echo '<td hidden>' . $assy_counter_done . '</td>';
                echo '<td title="' . $assy_counter_done.' références">' . $assy_moyen_done . '</td>';
                echo '<td>' . $assy_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,178,67);color: white;">Machining</td>';
                echo '<td hidden>' . $mach_counter_alive . '</td>';
                echo '<td>' . $mach_moyen_alive . '</td>';
                echo '<td>' . $mach_max_alive . '</td>';
                echo '<td hidden>' . $mach_counter_done . '</td>';
                echo '<td title="' . $mach_counter_done.' références">' . $mach_moyen_done . '</td>';
                echo '<td>' . $mach_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,178,67);color: white;">Molding</td>';
                echo '<td hidden>' . $mold_counter_alive . '</td>';
                echo '<td>' . $mold_moyen_alive . '</td>';
                echo '<td>' . $mold_max_alive . '</td>';
                echo '<td hidden>' . $mold_counter_done . '</td>';
                echo '<td title="' . $mold_counter_done.' références">' . $mold_moyen_done . '</td>';
                echo '<td>' . $mold_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,201,58);color: white;">RFQ</td>';
                echo '<td hidden>' . $pur_1_counter_alive . '</td>';
                echo '<td>' . $pur_1_moyen_alive . '</td>';
                echo '<td>' . $pur_1_max_alive . '</td>';
                echo '<td hidden>' . $pur_1_counter_done . '</td>';
                echo '<td title="' . $pur_1_counter_done.' références">' . $pur_1_moyen_done . '</td>';
                echo '<td>' . $pur_1_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,201,58);color: white;">Pris Dans</td>';
                echo '<td hidden>' . $pur_2_counter_alive . '</td>';
                echo '<td>' . $pur_2_moyen_alive . '</td>';
                echo '<td>' . $pur_2_max_alive . '</td>';
                echo '<td hidden>' . $pur_2_counter_done . '</td>';
                echo '<td title="' . $pur_2_counter_done.' références">' . $pur_2_moyen_done . '</td>';
                echo '<td>' . $pur_2_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,201,58);color: white;">FIA</td>';
                echo '<td hidden>' . $pur_3_counter_alive . '</td>';
                echo '<td>' . $pur_3_moyen_alive . '</td>';
                echo '<td>' . $pur_3_max_alive . '</td>';
                echo '<td hidden>' . $pur_3_counter_done . '</td>';
                echo '<td title="' . $pur_3_counter_done.' références">' . $pur_3_moyen_done . '</td>';
                echo '<td>' . $pur_3_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(239,117,33);color: white;">Logistics</td>';
                echo '<td hidden>' . $supply_counter_alive . '</td>';
                echo '<td>' . $supply_moyen_alive . '</td>';
                echo '<td>' . $supply_max_alive . '</td>';
                echo '<td hidden>' . $supply_counter_done . '</td>';
                echo '<td title="' . $supply_counter_done.' références">' . $supply_moyen_done . '</td>';
                echo '<td>' . $supply_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(66,114,202);color: white;">Assembly Routings</td>';
                echo '<td hidden>' . $mof_counter_alive . '</td>';
                echo '<td>' . $mof_moyen_alive . '</td>';
                echo '<td>' . $mof_max_alive . '</td>';
                echo '<td hidden>' . $mof_counter_done . '</td>';
                echo '<td title="' . $mof_counter_done.' références">' . $mof_moyen_done . '</td>';
                echo '<td>' . $mof_max_done . '</td>';
                echo '</tr>';

                echo '<tr hidden>';
                echo '<td style="background-color:rgb();color: white;">SAP Core Data</td>';
                echo '<td hidden>' . $gid_1_counter_alive . '</td>';
                echo '<td>' . $gid_1_moyen_alive . '</td>';
                echo '<td>' . $gid_1_max_alive . '</td>';
                echo '<td hidden>' . $gid_1_counter_done . '</td>';
                echo '<td title="' . $gid_1_counter_done.' références">' . $gid_1_moyen_done . '</td>';
                echo '<td>' . $gid_1_max_done . '</td>';
                echo '</tr>';

                echo '<tr hidden>';
                echo '<td style="background-color:rgb();color: white;">SAP Prod Data</td>';
                echo '<td hidden>' . $gid_2_counter_alive . '</td>';
                echo '<td>' . $gid_2_moyen_alive . '</td>';
                echo '<td>' . $gid_2_max_alive . '</td>';
                echo '<td hidden>' . $gid_2_counter_done . '</td>';
                echo '<td title="' . $gid_2_counter_done.' références">' . $gid_2_moyen_done . '</td>';
                echo '<td>' . $gid_2_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(123,182,86);color: white;">Routing Entry</td>';
                echo '<td hidden>' . $routing_entry_counter_alive . '</td>';
                echo '<td>' . $routing_entry_moyen_alive . '</td>';
                echo '<td>' . $routing_entry_max_alive . '</td>';
                echo '<td hidden>' . $routing_entry_counter_done . '</td>';
                echo '<td title="' . $routing_entry_counter_done.' références">' . $routing_entry_moyen_done . '</td>';
                echo '<td>' . $routing_entry_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(75,92,117);color: white;">Costing</td>';
                echo '<td hidden>' . $fin_counter_alive . '</td>';
                echo '<td>' . $fin_moyen_alive . '</td>';
                echo '<td>' . $fin_max_alive . '</td>';
                echo '<td hidden>' . $fin_counter_done . '</td>';
                echo '<td title="' . $fin_counter_done.' références">' . $fin_moyen_done . '</td>';
                echo '<td>' . $fin_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '</tr>';

                echo '<tr><td colspan=5 style="font-weight:bold">Paniers</td></tr>';
                echo '	<th rowspan=2 style="background-color: rgb(16, 112, 177);width:90px;">Dpt</th>';
                echo '	<th colspan=2 style="background-color: rgb(16, 112, 177);">A Traiter</th>';
                echo '	<th colspan=2 style="background-color: rgb(16, 112, 177);">'.$calc_period.' derniers jours</th>';
                echo '</tr>';

                echo '<tr>';
                echo '	<th style="background-color: rgb(16, 112, 177);width:20px"  hidden># Ref</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);">Moyen (j)</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);">Max (j)</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);" hidden># Ref</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);">Moyen (j)</th>';
                echo '	<th style="background-color: rgb(16, 112, 177);">Max (j)</th>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,178,67);color: white;">Method</td>';
                echo '<td hidden>' . $meth_counter_alive . '</td>';
                echo '<td>' . $meth_moyen_alive . '</td>';
                echo '<td>' . $meth_max_alive . '</td>';
                echo '<td hidden>' . $meth_counter_done . '</td>';
                echo '<td title="' . $meth_counter_done.' références">' . $meth_moyen_done . '</td>';
                echo '<td>' . $meth_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,178,67);color: white;">Quality prod</td>';
                echo '<td hidden>' . $q_prod_counter_alive . '</td>';
                echo '<td>' . $q_prod_moyen_alive . '</td>';
                echo '<td>' . $q_prod_max_alive . '</td>';
                echo '<td hidden>' . $q_prod_counter_done . '</td>';
                echo '<td title="' . $q_prod_counter_done.' références">' . $q_prod_moyen_done . '</td>';
                echo '<td>' . $q_prod_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,201,58);color: white;">RoHS / REACH</td>';
                echo '<td hidden>' . $pur_4_counter_alive . '</td>';
                echo '<td>' . $pur_4_moyen_alive . '</td>';
                echo '<td>' . $pur_4_max_alive . '</td>';
                echo '<td hidden>' . $pur_4_counter_done . '</td>';
                echo '<td title="' . $pur_4_counter_done.' références">' . $pur_4_moyen_done . '</td>';
                echo '<td>' . $pur_4_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:rgb(112,201,58);color: white;">HTS/Origin Supplier</td>';
                echo '<td hidden>' . $pur_5_counter_alive . '</td>';
                echo '<td>' . $pur_5_moyen_alive . '</td>';
                echo '<td>' . $pur_5_max_alive . '</td>';
                echo '<td hidden>' . $pur_5_counter_done . '</td>';
                echo '<td title="' . $pur_5_counter_done.' références">' . $pur_5_moyen_done . '</td>';
                echo '<td>' . $pur_5_max_done . '</td>';
                echo '</tr>';

                echo '<tr>';
                echo '<td style="background-color:#68A3DB;color: white;color: white;); background-size: 100% 20px;">Laboratory</td>';
                echo '<td hidden>' . $labo_counter_alive . '</td>';
                echo '<td>' . $labo_moyen_alive . '</td>';
                echo '<td>' . $labo_max_alive . '</td>';
                echo '<td hidden>' . $labo_counter_done . '</td>';
                echo '<td title="' . $labo_counter_done.' références">' . $labo_moyen_done . '</td>';
                echo '<td>' . $labo_max_done . '</td>';
                echo '</tr>';

                echo '</table>';

                // TEMPS MOYEN
                $f_moyen_total = $be_2_moyen_done + $be_3_moyen_done + max($inven_moyen_done, max($product_moyen_done, $qual_moyen_done) + $pur_1_moyen_done) + $gid_1_moyen_done + $gid_2_moyen_done + $pur_3_moyen_done + $fin_moyen_done;
                $f30_moyen_total = $be_2_moyen_done + $be_3_moyen_done +  max($inven_moyen_done, max($product_moyen_done, $qual_moyen_done) + $pur_1_moyen_done + $pur_2_moyen_done)  + $gid_1_moyen_done + $gid_2_moyen_done + $pur_3_moyen_done + $fin_moyen_done;
                $ASSY_moyen_total = $be_2_moyen_done + $be_3_moyen_done + max($product_moyen_done + $assy_moyen_alive + max($metro_moyen_done, $supply_moyen_done), $inven_moyen_done, $project_moyen_done, $qual_moyen_done) + $gid_1_moyen_done + max($gid_2_moyen_done, $mof_moyen_done) + $routing_entry_moyen_done + $fin_moyen_done;
                $MACH_moyen_total= $be_2_moyen_done + $be_3_moyen_done + max($inven_moyen_done, $product_moyen_done + $mach_moyen_done + max($metro_moyen_done, $supply_moyen_done)) + $gid_1_moyen_done + $gid_2_moyen_done + $routing_entry_moyen_done + $fin_moyen_done;
                $MOLD_moyen_total= $be_2_moyen_done + $be_3_moyen_done + max($inven_moyen_done, $product_moyen_done + $mold_moyen_done + max($metro_moyen_done, $supply_moyen_done)) + $gid_1_moyen_done + $gid_2_moyen_done + $routing_entry_moyen_done + $fin_moyen_done;
				
				// TEMPS MOYEN GID COMPLETED
				$f_moyen_GID_Completed = $be_2_moyen_done + $be_3_moyen_done + max($inven_moyen_done, max($product_moyen_done, $qual_moyen_done) + $pur_1_moyen_done) + $gid_1_moyen_done + $gid_2_moyen_done;
				$f30_moyen_GID_Completed = $be_2_moyen_done + $be_3_moyen_done +  max($inven_moyen_done, max($product_moyen_done, $qual_moyen_done) + $pur_1_moyen_done + $pur_2_moyen_done)  + $gid_1_moyen_done + $gid_2_moyen_done ;
				$ASSY_moyen_GID_Completed = $be_2_moyen_done + $be_3_moyen_done + max($product_moyen_done + $assy_moyen_alive + max($metro_moyen_done, $supply_moyen_done), $inven_moyen_done, $project_moyen_done, $qual_moyen_done) + $gid_1_moyen_done + $gid_2_moyen_done;
                $MACH_moyen_GID_Completed= $be_2_moyen_done + $be_3_moyen_done + max($inven_moyen_done, $product_moyen_done + $mach_moyen_done + max($metro_moyen_done, $supply_moyen_done)) + $gid_1_moyen_done + $gid_2_moyen_done;
				$MOLD_moyen_GID_Completed= $be_2_moyen_done + $be_3_moyen_done + max($inven_moyen_done, $product_moyen_done + $mold_moyen_done + max($metro_moyen_done, $supply_moyen_done)) + $gid_1_moyen_done + $gid_2_moyen_done;
								
                // TEMPS MAX
                $f_max_total = $be_2_max_done + $be_3_max_done + max($inven_max_done, max($product_max_done, $qual_max_done) + $pur_1_max_done) + $gid_1_max_done + $gid_2_max_done + $pur_3_max_done + $fin_moyen_done;
                $f30_max_total = $be_2_max_done + $be_3_max_done +  max($inven_max_done, max($product_max_done, $qual_max_done) + $pur_1_max_done + $pur_2_max_done)  + $gid_1_max_done + $gid_2_max_done + $pur_3_max_done + $fin_moyen_done;
                $ASSY_max_total = $be_2_max_done + $be_3_max_done + max($product_max_done + $assy_max_alive + max($metro_max_done, $supply_max_done), $inven_max_done, $project_max_done, $qual_max_done) + $gid_1_max_done + max($gid_2_max_done, $mof_max_done) + $routing_entry_max_done + $fin_moyen_done;
                $MACH_max_total= $be_2_max_done + $be_3_max_done + max($inven_max_done, $product_max_done + $mach_max_done + max($metro_max_done, $supply_max_done)) + $gid_1_max_done + $gid_2_max_done + $mof_max_done + $fin_max_done;
                $MOLD_max_total= $be_2_max_done + $be_3_max_done + max($inven_max_done, $product_max_done + $mold_max_done + max($metro_max_done, $supply_max_done)) + $gid_1_max_done + $gid_2_max_done + $mof_max_done + $fin_max_done;


                echo '</td>';
                echo '</tr>';
                echo '<tr>';
                echo '<td>';
                echo '<table id="t10" border=0>';
                echo '<tr>';
                echo '<td colspan=2>';
                echo 'Temps moyen en jours calendaires de diffusion totale hors "panier" - '.$calc_period.' derniers jours';
                echo '</td>';
                // echo '<td>';
                // echo 'SAP';
                // echo '</td>';
                echo '</tr>';
                echo '<tr title="Temps jusqu\'à la création dans SAP (étapes GID complétées) / Temps pour l\'ensemble du process">';
                echo '<td>';
                echo ' - Sous-traitée sans "pris dans" (type = PUR / F)';
                echo '</td>';
                echo '<td>';
                echo $f_moyen_GID_Completed . ' / ' . $f_moyen_total;
				//echo $f_moyen_total;
                echo '</td>';
                // echo '<td>';
                // echo $f_max_total;
                // echo '</td>';
                echo '</tr>';
                echo '<tr title="Temps jusqu\'à la création dans SAP (étapes GID complétées) / Temps pour l\'ensemble du process">';
                echo '<td>';
                echo ' - Sous-traitée avec fourniture SCM (type = PUR / F30)';
                echo '</td>';
                echo '<td>';
                echo $f30_moyen_GID_Completed . ' / ' . $f30_moyen_total;
				// echo $f30_moyen_total;
                echo '</td>';
                // echo '<td>';
                // echo $f30_max_total;
                // echo '</td>';
                echo '</tr>';
                echo '<tr title="Temps jusqu\'à la création dans SAP (étapes GID complétées) / Temps pour l\'ensemble du process">';
                echo '<td>';
                echo ' - Assemblée (type = ASSY / E)';
                echo '</td>';
                echo '<td>';
                echo $ASSY_moyen_GID_Completed . ' / ' . $ASSY_moyen_total;
				// echo $ASSY_moyen_total;
                echo '</td>';
                // echo '<td>';
                // echo $ASSY_max_total;
                // echo '</td>';
                echo '</tr>';
                echo '<tr title="Temps jusqu\'à la création dans SAP (étapes GID complétées) / Temps pour l\'ensemble du process">';
                echo '<td>';
                echo ' - Usinée en interne (type = MACH / E)';
                echo '</td>';
                echo '<td>';
                echo $MACH_moyen_GID_Completed . ' / ' . $MACH_moyen_total;
				// echo $MACH_moyen_total;
                echo '</td>';
                // echo '<td>';
                // echo $MACH_max_total;
                // echo '</td>';
                echo '</tr>';
                echo '<tr title="Temps jusqu\'à la création dans SAP (étapes GID complétées) / Temps pour l\'ensemble du process">';
                echo '<td>';
                echo ' - Moulée en interne(type = MOLD / E)';
                echo '</td>';
                echo '<td>';
                echo $MOLD_moyen_GID_Completed . ' / ' . $MOLD_moyen_total;
				// echo $MOLD_moyen_total;
                echo '</td>';
                // echo '<td>';
                // echo $MOLD_max_total;
                // echo '</td>';
                echo '</tr>';
                echo '</table>';
                ?>
            </td>
        </tr>
    </table>
    </form>

</body>

</html>