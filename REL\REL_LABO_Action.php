<?php
//print_r($_POST);


	// création des variables
	//-----------------------
	$id = $_GET['ID'];
	$user = $_GET['userid'];
	$date_meth = date("Y-m-d");

	//Connexion à BD
	include('../REL_Connexion_DB.php');

	//-----------------------
	//Commentaire
	$v = 'Labo: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

	$query_3 = 'SELECT General_Comments
					FROM tbl_released_drawing
					WHERE ID ="' . $id . '";';
	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc())
	{
		if ($_GET['comment'] != "")
		{
			$v = $v . '\r\n' . $row['General_Comments'];
		} else {
			$v = $row['General_Comments'];
		}
	} 
	//-----------------------

	$query = 'UPDATE tbl_released_drawing 
					SET DATE_Labo="' . $date_meth . '",
						VISA_Labo="' . $user . '",
						General_Comments="' . $v . '"
						WHERE ID ="' . $id . '";';

	$resultat = $mysqli->query($query);

	mysqli_close($mysqli);



?>
