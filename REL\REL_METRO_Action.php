<?php

// ENREGISTREMEENT DES DONNNEES ET SIGNATURE - FLUX NOMINAL
    if (isset($_GET['action']) && ($_GET['action'])=="signoff")
	{

        $id = $_GET['ID'];		
        

		
		if ($_GET['userid']=="%" || $_GET['userid']=="")
		{
			$user="";
			$date_metro = "0000-00-00";
		} else {
			$user = $_GET['userid'];
			$date_metro = date('Y-m-d');
		}
		
		if ($_GET['metrotime']=="%" || $_GET['metrotime']=="")
		{
			$metrotime="0";
		} else {
			$metrotime = $_GET['metrotime'];
		}
		
		if ($_GET['control_metro']=="%" || $_GET['control_metro']=="")
		{
			$control_metro="";
		} else {
			$control_metro = $_GET['control_metro'];
		}

		include('../REL_Connexion_DB.php');
        
		// Si le textarea dans REL_PRODUCT_Item.php n'est pas videalors ont afficher "Product : + le message"
        //Commentaire
		$v = 'Metro: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

		$query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';

		$resultat = $mysqli->query($query_3);

		// On affiche notre message et à la ligne on laisse l'ancien message
		while ($row = $resultat->fetch_assoc())
		{
			if ($_GET['comment'] != "")
			{
				$v = $v . '\r\n' . $row['General_Comments'];
			} else {
				$v = $row['General_Comments'];
			}
		} 
		//-----------------------

        $query_2 = 'UPDATE tbl_released_drawing 
                        SET DATE_Metro="' . $date_metro . '",
                            VISA_Metro="' . $user . '",
							Metro_Control ="' . $control_metro . '",
							Metro_Time ="' . $metrotime . '",
                            General_Comments="' . $v . '"
                            WHERE ID ="' . $id . '";';
		print_r($query_2);

        $resultat = $mysqli->query($query_2);

        mysqli_close($mysqli);

    }
?>