body {
    background-color: transparent;
    color: black;
    font-size: 8pt;
    font-family: Arial,sans-serif;
    font-weight: normal;
  }
  
  @media screen and (min-width: 1370px) {
    div.format {
      font-size:9pt;
    }
  }
  
  @media screen and (max-width: 1369px) {
    div.format {
      font-size:8pt;
    }
  }
  
  #Main_Title {
      margin-left:5px;
      margin-top:5px;
      text-align:left;
      margin-bottom:10px;
      margin-left:10px;
      vertical-align:middle;
      font-size: 12pt;
      font-weight: bolder;
      }
  
  
  #FilterTitle {
    margin-left: 2px;
    margin-right: 2px;
    text-align: center;
    vertical-align: middle;
    background: transparent;
  }
  
  
  div#Result_info {
    text-indent: 10px;
    margin-left: 8px;
    margin-bottom: 8px;
  margin-top:10px;
    text-align: justify;
  }
  
  
  /*TABLEAU PRINCIPAL DE LA PAGE*/
  /*-----------------------------*/
  #t01 {
    width: 100%;
    border-collapse: collapse;
    vertical-align: middle;
  }
  
  #t01 th {
    border: 0.5px solid black;
    background-color: rgb(27, 79, 114);
    color: white;
  }
  
  #t01 td {
    text-align: left;
    vertical-align: middle;
  }
  
  #t01 tr {
    height: 20px;
  }
  
  
  /*TABLEAU DONNEES*/
  /*---------------*/
  
  #t02 {
    border-collapse: collapse;
    width: 100%;
  }
  
  #t02 th {
    border: 0.5px solid black;
    background-color: rgb(27, 79, 114);
    color: white;
    font-family: Arial, Helvetica, sans-serif;
    text-align: center;
  }
  
  #t02 td {
    text-align: center;
    vertical-align: middle;
  border-bottom:0.5px solid black; 
  border-left:0.5px solid black; 
  border-right:0.5px solid black; 
  border-top:0.5px solid black; 
  }
  
  #t02 tr {
    height: 15px; 
  }
  
  #t02 tr:nth-child(even) {
    background:rgba(244, 244, 244, .5);
  }
  