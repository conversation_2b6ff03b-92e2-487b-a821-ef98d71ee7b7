<?php
echo '<script>alert("' . $_GET['action'] . '")</script>';
// ENREGISTREMEENT DES DONNNEES ET SIGNATURE - FLUX NOMINAL
if (isset($_GET['action']) && ($_GET['action']) == "signoff") {

	$id = $_GET['ID'];
	$date_mof = date('Y-m-d');


	if ($_GET['userid'] == "%" || $_GET['userid'] == "") {
		$user = "";
	} else {
		$user = $_GET['userid'];
	}

	if ($_GET['mof'] == "%" || $_GET['mof'] == "") {
		$mof = "";
	} else {
		$mof = htmlspecialchars($_GET['mof'], ENT_QUOTES);
	}

	include('../REL_Connexion_DB.php');

	//-------------------------
	// Critical_Complete (Flag de fin de validation)
	$query_flag = 'SELECT Doc_Type, VISA_GID_2
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';
	$resultat = $mysqli->query($query_flag);
	while ($row_flag = $resultat->fetch_assoc()) {
		// Si le document est un DOC et qu'il a été validé par le MOF alors le flag est à 1
		if ($row_flag['Doc_Type'] == "DOC" && $row_flag['VISA_GID_2'] != "") {
			$flag = "1";
		} else {
			// Sinon le flag est à 0
			$flag = "0";
		}
	}

	// Si le textarea dans REL_PRODUCT_Item.php n'est pas videalors ont afficher "Product : + le message"
	//Commentaire
	$v = 'MOF ASSY: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

	$query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';

	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc()) {
		if ($_GET['comment'] != "") {
			$v = $v . '\r\n' . $row['General_Comments'];
		} else {
			$v = $row['General_Comments'];
		}
	}
	//----------------------

	// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    // Ajout de la valeur de Critical_Complete (flag) dans la requête-
	$query_2 = 'UPDATE tbl_released_drawing 
                        SET DATE_MOF="' . $date_mof . '",
                            VISA_MOF="' . $user . '",
							MOF ="' . $mof . '",
                            General_Comments="' . $v . '",
							Critical_Complete="' . $flag . '"
                            WHERE ID ="' . $id . '";';

	$resultat = $mysqli->query($query_2);

	mysqli_close($mysqli);
}
