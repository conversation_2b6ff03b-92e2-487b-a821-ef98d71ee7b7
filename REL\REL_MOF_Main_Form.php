<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
	
	<meta http-equiv='cache-control' content='no-cache'>
	<meta http-equiv='expires' content='0'>
	<meta http-equiv='pragma' content='no-cache'>
	
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_MOF_Main_Form_styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


<script>

	// function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné
	function chkName(id_record)
	{
		
		const visa = document.getElementById("User_Choice__" + id_record);
		
		if (visa.value == "" || visa.value == "%")
		{
			alert("Please indicate your name prior to validate");
			return false;
		}
		
		var res = confirm("Etes vous sur de vouloir valider ?");
		if (res == false) 
		{
			return false;
		}
		
		data_update("signoff", id_record, 1);
		
	}


	// MISE A JOUR DE LA BASE DE DONNEES EN VALDIATION ET EN CHANGEMENT DE DOC TYPE
	function data_update(action, id_record, validation_flag)
	{

		const xhttp = new XMLHttpRequest();
		xhttp.onload = function() 
		{
			// acces retour de process ou message utilisateur pour confirmation
		}
		
		// Pour SAVING
		var user_val="";
		if (validation_flag==1)
		{
			var user_val=document.getElementById("User_Choice__" + id_record).value;
		}
		// ----
		
		
		// ETABLISSEMENT DES IMPACT
		var impact=document.getElementById("impact__" + id_record).checked;
		if (impact==true)
		{
			var impact_final="true";
		} else {
			var impact_final="false";
		}

	  // --------
	  
	  // Preparation de la zone de MOF
	  var mof_value="";
	  //var mof_value_tmp=document.getElementById("MOF__" + id_record).replace("<br>","\r\n");
	  var mof_value_tmp=document.getElementById("MOF__" + id_record).value;
	  mof_value=mof_value_tmp.replace("\n"," - ");
	  
	  // ---------
		
		if (action=="signoff" || action==1)
		{		
			action="signoff";
			const url_a="REL_MOF_Action.php?ID=" + id_record
									 + "&impact=" + impact_final
									 + "&mof=" + mof_value
									 + "&userid=" + user_val
									 + "&action=" + action
									 + "&comment=" + document.getElementById("comment__" + id_record).value;	  
			xhttp.open("GET", url_a);			
			xhttp.send();
		} 

	}
	
	// PERMET DE VALIDER EN MASSE LES DONNEES
	function mass_update()
	{
		var res = confirm("Are you sure to validate all the rows where you filled your signature in?");
		if (res == false)
		{
			visa.value="";
			return false;
		}
		
		const data_table=document.getElementById("t02");
		const tr_list=data_table.getElementsByTagName("tr");

		for (let i = 0; i<=tr_list.length; i++)
		{
			var id_record=tr_list[i].cells[0].textContent.trim(); // RECUPERE L'ID DE L'ENREGISTREMENT DANS LA TABLE DE LA BDD QUI SE TROUVE EN PREMIERE COLONNE DU TABLEAU T02
			if (isNaN(id_record)==false)						  // VERIFIER QUE LES ID RECUPERES SOIENT DES NOMBRES/CHIFFRES
			{
				var user_name=document.getElementById("User_Choice__" + id_record).value;
				if (user_name!="" && user_name!="%")			  // SELECTIONNE LES LIGNES POUR LESQUELS L'UTILISATEUR A RENSEIGNE SON NOM POUR SIGNER
				{
					data_update("signoff", id_record, 1); 			  // LANCEMENT DE LA FONCTION DE MISE A JOUR DE LA BDD
				}
			}
		}
	
	}
	
	
	function mof_field_toggle(obj)
	{
		const id=obj.name.split("__");
		//	alert(document.getElementById("MOF__" + id));
		//document.getElementById("MOF__" + id).setAttribute('disabled', '')
		//document.getElementById("MOF__" + id).removeAttribute('disabled');
	}
	
	function impact_check(id_val)
	{	
		
		var mof_field = document.getElementById("MOF__"+id_val).value;
		mof_field=mof_field.trim();
		var lg_mof_field=mof_field.length;
		if (lg_mof_field>0)
		{
			document.getElementById("impact__" + id_val).checked=true;
		} else {
			document.getElementById("impact__" + id_val).checked=false;
		}
	}
	
	
	
</script>


</head>

<title>
        REL Pack - MOF Review
 </title>
<body>


<?php
	// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
	include('REL_Workflow_Conditions.php');
?>


    <form enctype="multipart/form-data" action="" method="post">

        <table id="t01" border=0>

            <tr>
                <td colspan=10>
                    <div id="Main_Title">
                        Assembly Routing Review
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <div id="FilterTitle">
                        Package #
                        <SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
							
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
                                            FROM tbl_released_drawing 
                                            WHERE '.$MOF_Conditions.'
                                            ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
                            
							$resultat = $mysqli->query($requete);
							
                            while ($row = $resultat->fetch_assoc()) {
								$sel="";
								if (isset($_POST['Rel_Pack_Num_Choice']))
								{
									if ($_POST['Rel_Pack_Num_Choice']==$row['Rel_Pack_Num'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Rel_Pack_Num']!="")
								{
									echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '"'.$sel.'>' . $row['Rel_Pack_Num'] . '</option><br/>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                        <!--</datalist>-->
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Activity
                        <SELECT name="Activity_Choice" type="submit" size="1" style="width:100px;font-size:9pt;height:17px" onchange="this.form.submit()">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_package .Activity 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE '.$MOF_Conditions.'
                                ORDER BY tbl_released_package.Activity DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel="";
								if (isset($_POST['Activity_Choice']))
								{
									if ($_POST['Activity_Choice']==$row['Activity'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Activity']!="")
								{
									echo '<OPTION value ="' . $row['Activity'] . '"'.$sel.'>' . $row['Activity'] . '</option><br/>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Project
                        <SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT Project 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE '.$MOF_Conditions.' 
                                ORDER BY tbl_released_package.Project DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc())
							{
								$sel="";
								if (isset($_POST['Project_Choice']))
								{
									if ($_POST['Project_Choice']==$row['Project'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Project']!="")
								{
									echo '<OPTION value ="' . $row['Project'] . '"'.$sel.'>' . $row['Project'] . '</option><br/>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Reference

                        <input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;" onchange="this.form.submit()"
						<?php if (isset($_POST['Reference_Choice'])){echo ' Value="'.$_POST['Reference_Choice'].'">';}?>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Drawing

                        <input type="text" size=20 name="Drawing_Choice" style="font-size:9pt;height:9pt;width:100pt;" onchange="this.form.submit()"
						<?php if (isset($_POST['Drawing_Choice'])){echo ' Value="'.$_POST['Drawing_Choice'].'">';}?>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Action

                    <!--</div>

                    <div id="Filter">-->
                        <SELECT name="Action_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Action 
                                FROM tbl_released_drawing 
                                WHERE '.$MOF_Conditions.'
                                ORDER BY tbl_released_drawing.Action ASC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc())
							{
								$sel="";
								if (isset($_POST['Action_Choice']))
								{
									if ($_POST['Action_Choice']==$row['Action'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Action']!="")
								{
									echo '<OPTION value ="' . $row['Action'] . '"'.$sel.'>' . $row['Action'] . '</option><br/>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Type

                    <!-- </div>

                    <div id="Filter"> -->
                        <SELECT name="Doc_Type_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Doc_Type 
                                FROM tbl_released_drawing 
                                WHERE '.$MOF_Conditions.' 
                                ORDER BY tbl_released_drawing.Doc_Type ASC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc())
							{
								$sel="";
								if (isset($_POST['Doc_Type_Choice']))
								{
									if ($_POST['Doc_Type_Choice']==$row['Doc_Type'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Doc_Type']!="")
								{
									echo '<OPTION value ="' . $row['Doc_Type'] . '"'.$sel.'>' . $row['Doc_Type'] . '</option><br/>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Proc. Type

                    <!--</div>

                    <div id="Filter"> -->
                        <SELECT name="Proc_Type_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Proc_Type 
                                FROM tbl_released_drawing 
                                WHERE '.$MOF_Conditions.'
                                ORDER BY tbl_released_drawing.Proc_Type ASC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc())
							{
								$sel="";
								if (isset($_POST['Proc_Type_Choice']))
								{
									if ($_POST['Proc_Type_Choice']==$row['Proc_Type'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Proc_Type']!="")
								{
									echo '<OPTION value ="' . $row['Proc_Type'] . '"'.$sel.'>' . $row['Proc_Type'] . '</option><br/>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>

                <td>
                    <div id="FilterTitle">
                        Ex
                        
                     <!--</div>
					<div id="Filter">-->
                        <SELECT name="Ex_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../SCM_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_ex.Ex 
                                FROM tbl_ex
                                ORDER BY tbl_ex.Ex ASC';
                            $resultat = $mysqli_scm->query($requete);
                            while ($row = $resultat->fetch_assoc())
							{
								$sel="";
								if (isset($_POST['Ex_Choice']))
								{
									if ($_POST['Ex_Choice']==$row['Ex'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Ex']!="")
								{
									echo '<OPTION value ="' . $row['Ex'] . '"'.$sel.'>' . $row['Ex'] . '</option><br/>';
								}
                            }
                            mysqli_close($mysqli_scm);
                            ?>
                        </SELECT>
                    </div>
                </td>


            </tr>
        


        <!--- Vérification des valeurs --->
        <?php

        if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
            $rel_pack_num_choice = "%";
        } else {
            $rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
        }

        if (isset($_POST['Activity_Choice']) == false) {
            $activity_choice = "%";
        } else {
            $activity_choice = $_POST['Activity_Choice'];
        }

        if (isset($_POST['Project_Choice']) == false) {
            $project_choice = "%";
        } else {
            $project_choice = $_POST['Project_Choice'];
        }

        if (isset($_POST['Reference_Choice']) == false) {
            $reference_choice = "%";
        } else {
            if (strlen($_POST['Reference_Choice']) > 0) {
                $reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
            } else {
                $reference_choice = "%";
            }
        }

        if (isset($_POST['Drawing_Choice']) == false) {
            $drawing_choice = "%";
        } else {
            if (strlen($_POST['Drawing_Choice']) > 0) {
                $drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
            } else {
                $drawing_choice = "%";
            }
        }

        if (isset($_POST['Action_Choice']) == false) {
            $action_choice = "%";
        } else {
            $action_choice = $_POST['Action_Choice'];
        }

        if (isset($_POST['Doc_Type_Choice']) == false) {
            $doc_type_choice = "%";
        } else {
            $doc_type_choice = $_POST['Doc_Type_Choice'];
        }

        if (isset($_POST['Proc_Type_Choice']) == false) {
            $proc_type_choice = "%";
        } else {
            $proc_type_choice = $_POST['Proc_Type_Choice'];
        }

        if (isset($_POST['Ex_Choice']) == false) {
            $Ex_Choice = "%";
        } else {
            $Ex_Choice = $_POST['Ex_Choice'];
        }

        //$query_1 = 'SELECT * FROM tbl_dmo where Status like "'.$Status_choice.'" && Decision like "'.$Decision_choice.'" && DMO like "'.$DMO_filter.'" && Requestor_Name like "'.$Requestor_choice.'" && Product_Range like "'.$Product_Range_choice.'" && Description like "'.$Description_filter.'" && Ex like "'.$Ex_choice.'" && Eng_Owner like "'.$EngOwner_choice.'" ORDER BY DMO DESC;';

        $query_1 = 'SELECT *, datediff(Now(),DATE_Metro) as "Delay"
                        FROM tbl_released_package 
                        LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                        WHERE 
						
							'.$MOF_Conditions.'
						
                            AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                            AND tbl_released_package.Activity like "' . $activity_choice . '"
                            AND tbl_released_package.Project like "' . $project_choice . '"
                            AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                            AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                            AND tbl_released_drawing.Action like "' . $action_choice . '"
                            AND tbl_released_drawing.Doc_Type like "' . $doc_type_choice . '"
                            AND tbl_released_drawing.Proc_Type like "' . $proc_type_choice . '"
                            AND tbl_released_drawing.Ex like "' . $Ex_Choice . '"
                        ORDER BY tbl_released_drawing.Prod_Draw DESC';

        include('../REL_Connexion_DB.php');
        $resultat = $mysqli->query($query_1);
        $rowcount = mysqli_num_rows($resultat);

        echo '<tr><td colspan=3><div id="Result_info">Number of results: ' . $rowcount . '&nbsp&nbsp&nbsp';
        echo '</td>';
		
		
		// BOUTON DE MISE A JOUR EN MASSE
		echo '<td  colspan=8 style="text-align:right; padding-right:10px;">
		<input onclick="return mass_update()" type="submit" class="btn green" style="font-size:7pt; width:90px;height:15px;vertical-align:middle;text-align:center" name="mass_update_btn" value="Mass Validation" title="Validation of all the lines where a VISA is present" />
		</td>';
		// -----
		
		
        echo '</div>';
        echo '</tr>';
		echo '</table>';

        // Création des entetes du tableau
        echo '<table id="t02">';
        echo '<thead>';
		echo '	<th style="width:15px;background-color: rgb(16, 112, 177);" title="Delay in Days">D</th>';
        echo '	<th style="width:40px;background-color: rgb(16, 112, 177);">Pack #</th>';
        echo '	<th style="width:70px;background-color: rgb(16, 112, 177);">Activity</th>';
        echo '	<th style="min-width:120px;background-color: rgb(16, 112, 177);">Reference</th>';
        echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
        echo '	<th style="min-width:120px;background-color: rgb(16, 112, 177);">Prod Drawing</th>';
        echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
        echo '	<th style="width:45px;background-color: rgb(16, 112, 177);">Action</th>';
        echo '	<th style="width:70px;background-color: rgb(16, 112, 177);">DMO</th>';
        echo '	<th style="width:60px;background-color: rgb(16, 112, 177);">Type</th>';
		echo '	<th style="width:150px;background-color: rgb(16, 112, 177);">Mat Type</th>';
        echo '	<th style="width:50px;background-color: rgb(16, 112, 177);">Leadtime (d)</th>';
		echo '	<th style="width:35px;background-color: rgb(16, 112, 177);">MOQ</th>';
		echo '	<th style="width:35px;background-color: rgb(16, 112, 177);">CLS</th>';
		echo '	<th style="width:50px;background-color: rgb(16, 112, 177);">Metro Time</th>';
		echo '	<th style="width:80px;background-color: rgb(16, 112, 177);">Metro Control</th>';
        echo '	<th title="Requestor Remarks" style="width:70px;background-color: rgb(16, 112, 177);">Remarks</th>';
        echo '	<th style="width:100px;">Impact Routing?</th>';
		echo '	<th style="min-width:150px;">Routing Group/Counter</th>';
        echo '	<th>Comments</th>';
        echo '	<th style="width:110px;">Validation</th>';
        echo '</thead>';


       $i=0;

        // création du tableau dans une iframe
        while ($row = $resultat->fetch_assoc()) {
           echo '<tr id ="'.$i.'">
				<td hidden >
					'.$row['ID'].'
				</td>';
		  echo '<td title="Number of days of presence - Waiting Time" style="font-size:9px;font-weight:bold">
					' . $row['Delay'] . '
				</td>';
				// <td >
					// <a target ="_blank" href="REL_Pack_Overview.php?ID='.$row['Rel_Pack_Num'].'"> '.$row['Rel_Pack_Num'].'</a>
				// </td>
				echo '<td> ';
			$nmax = 0;
			if ((strlen($row['Observations']) > $nmax)) {
				echo htmlspecialchars(substr(nl2br($row['Observations']), 0, $nmax), ENT_QUOTES);
				echo '<div class="dropdown_observations">';
				echo '<span><b><a target ="_blank" href="REL_Pack_Overview.php?ID='.$row['Rel_Pack_Num'].'">'.$row['Rel_Pack_Num'].'</a></b></span>';
				echo '<div class="dropdown_observations-content">';
				echo '<p><b><u>Package Observations</u></b><br>' . htmlspecialchars_decode(nl2br($row['Observations']), ENT_QUOTES) . '</p>';
				echo '</div>';
				echo '</div>';
			} else {
				echo '<a target ="_blank" href="REL_Pack_Overview.php?ID='.$row['Rel_Pack_Num'].'">'.$row['Rel_Pack_Num'].'</a>';
			}
			echo '</td>';
				
				
			echo '<td >
					'.$row['Activity'].'<br>'.$row['Project'].'
				</td>
				<td >
					'.$row['Reference'];
				 if ($row['Ex'] != "NO") {
                        echo '<FONT color="red"><strong><sup>'.$row['Ex'].'</sup><strong></FONT>';
                    } 
				echo '
				</td>
				<td >
					'.$row['Ref_Rev'].'
				</td>';
				
				// echo '<td>';
				// if ($row['Drawing_Path']!="")
				// { 
				// 	$path_file='DRAWINGS\\IN_PROCESS\\'.$row['Drawing_Path'];
				// 	if (file_exists($path_file)==0)
				// 	{
					
				// 		$path_file='DRAWINGS\\OFFICIAL\\'.$row['Drawing_Path'];
				// 		if (file_exists($path_file)==0)
				// 		{
				// 			$path_file="";
				// 		}
				// 	}
				// 	if ($path_file!="")
				// 	{
				// 	echo '<div class="dropdown_prod_drawing">';
				// 	echo '<a target=_blank href="'.$path_file.'">' . $row['Prod_Draw'] . '</a>';
				// 	echo '<div class="dropdown_prod_drawing-content">';
				// 	echo '<p><iframe src="'.$path_file.'#toolbar=0&navpanes=0&scrollbar=0" width="400px" height="280px" scrolbar=no></iframe></p>';
				// 	echo '</div>';
				// 	echo '</div>';
				// 	} else {
				// 		echo $row['Prod_Draw'];
				// 		}
				// } else {
				// 	echo $row['Prod_Draw'];
				// }
				// echo '</td>';
				include('NO_PREVIEW.php');
				
			echo '<td >
					'.$row['Prod_Draw_Rev'].'
				</td>
				<td >';
					// Si la ligne Action est égal à Modification alors on raccourci le mot pour ecrire "modif" à la place
					if ($row['Action'] == "Modification")
					{
						echo substr($row['Action'], 0, 5);
					} else {
						echo $row['Action'];
					}
					//---------

			echo '</td>';
				echo '<td>';
            /*$mysqli_1 = new mysqli('localhost', 'root', '', 'db_dmo');
            $mysqli_1->set_charset("utf8");
            $requete = "SELECT DISTINCT DMO FROM tbl_dmo ORDER BY DMO DESC;";
            $resultat = $mysqli_1->query($requete);
            while ($ligne = $resultat->fetch_assoc()) {*/
                echo '<a target="_blank" href="/DMO/DMO_Modification_form.php?dmo='. $row['DMO'] .'">' . $row['DMO'] . '</a>';
            /*}
            mysqli_close($mysqli_1);*/
				echo '</td>
				<td style="width:40px">
				'.$row['Doc_Type'];
				if ($row['Internal_Mach_Rec']==1)
				{
					echo '<img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15">';
				}
			echo '	<br>'.$row['Proc_Type'].'</td>';
			echo '
				<td>'.$row['Material_Type'].'</td>
				<td>'.$row['leadtime'].'</td>
				<td>'.$row['MOQ'].'</td>
				<td>'.$row['CLS'].'</td>
				<td>'.$row['Metro_Time'].'</td>';
			
			
			echo '<td style="text-align:left; padding-left:5px">';
			$metro_control_db=explode(";",$row['Metro_Control']);
			$query_2 = 'SELECT *
						FROM tbl_metro';
			$resultat_2 = $mysqli->query($query_2);
			while ($ligne_2 = $resultat_2->fetch_assoc()) {
				$ck="";
				foreach ($metro_control_db as $metro_control){
					if($metro_control==$ligne_2['Control']){
						$ck="checked";
					}
				}
				echo '<input style="margin-left:-0px;vertical-align:middle" type="checkbox" 
						 id="Control_Metro' . $ligne_2['Control'] . '" 
						 name="Control_Metro_' . $ligne_2['Control'] . '" 
						 class="control_metro"
						 Value="' . $ligne_2['Control'] . '" 
						 Title="' . $ligne_2['Description'] . '"
						 '.$ck.'>';
				echo '<label   for="Control_' . $ligne_2['Control'] . '">' . $ligne_2['Control'] . '</label><br>';
			}
			echo '</td>';
			
			
				
			echo '<td>';
			// Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
			$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");
			
			//$nmax = 30;
			$nmax = 0;
			if ((strlen($row['Requestor_Comments']) > $nmax)) {
				echo '<div class="dropdown">';
				echo '<span>
						 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
					  </span>';
				echo '<div class="dropdown-content">';
				echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES) . '</p>';
				echo '</div>';
				echo '</div>';
			} else {
				echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
			}
			
			echo "<font size=4> | </font>";
			
			$nmax = 0;
			if ((strlen($row['General_Comments']) > $nmax)) {
				echo htmlspecialchars(substr(nl2br($row['General_Comments']), 0, $nmax), ENT_QUOTES);
				echo '<div class="dropdown">';
				echo '<span>
						<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:1" >
					  </span>';
				echo '<div class="dropdown-content">';
				echo '<p><b>- <u>General Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['General_Comments']), ENT_QUOTES) . '</p>';
				echo '</div>';
				echo '</div>';
			} else {
				echo '<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:0.3" >';
			}
			
			echo '</td>';
				

				
				
				
				// ------------------------------------
				// DEBUT CHAMP SAISIE PAR L'UTILISATEUR
				// ------------------------------------
				echo '<td style="">';

						$chk="";
						if ($row['MOF']<>"")
						{
							$chk="CHECKED";
						}
						
                        echo '<input tabindex="'.(1000+$i).'" style="vertical-align:middle" type="checkbox" 
                             id="impact__' . $row['ID'] . '" 
                             name="impact__' . $row['ID'] . '" 
							 class="impact__'.$row['ID'].'"
							 onclick="mof_field_toggle(this)"
                             Value=""'
							  . $chk;
                        echo '> <label   for="impact__' . $row['ID'] . '">impact</label><br/>';
                    
                echo '</td>';
				
				echo '<td>
                    <div id="FilterTitle">
                        <textarea tabindex="'.(2000+$i).'" id="MOF__'.$row['ID'].'" onchange="impact_check('.$row['ID'].')" title="MOF Number" type="text" style="text-align:center;height:30px;vertical-align:middle;font-family:arial;font-size:8pt;width:95%;">'.htmlspecialchars_decode($row['MOF']).'</textarea>
                    </div>
                </td>';
				
					
				echo '<td>
                    <textarea tabindex="'.(3000+$i).'" id="comment__'.$row['ID'].'" style="background-color:transparent;font-family:Tahoma;font-size:8pt;height:30px;width:94%;vertical-align:middle" ></textarea>
                </td>';
				
				
				echo '<td  style="text-align:center">
                        <SELECT tabindex="'.(4000+$i).'" id="User_Choice__'.$row['ID'].'" name="user_name" type="submit" size="1" style="width:95%;font-size:7.5pt;height:17px;">
                            <option value="%"></option>';

                            include('../SCM_Connexion_DB.php');
                            $requete_5 = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
										  FROM tbl_user
										  WHERE UPPER(tbl_user.Department) like "INDUS%"';

                            $resultat_5 = $mysqli_scm->query($requete_5);

                            while ($row4 = $resultat_5->fetch_assoc()) {
                                //if (strtoupper(substr($row['Doc_Type'], 0, 3)) == strtoupper(substr($row4['Department'], 0, 3))) {
                                    echo '<OPTION value ="' . $row4['Fullname'] . '">' . $row4['Fullname'] . '</option><br/>';
                                //}
                            }
                            mysqli_close($mysqli_scm);
                   
                echo '  </SELECT>
						<input name="saving_form" onclick="return data_update(1,'.$row['ID'].',0)" type="submit" class="btn orange" style="font-size:7pt;margin-left:-5px; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Save" title="Save the current data without validating it" />
						&nbsp
						<input name="valid_form" onclick="return chkName('.$row['ID'].')" type="submit" class="btn blue2" style="font-size:7pt; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Sign" title="Sign off the current drawing" />
					</td>
				</tr>';
			
				$i=$i+1;
        }
        mysqli_close($mysqli);
        ?>

        </table>
    </form>

</body>

</html>