<?php 
	$msg="";
	
	if (isset ($_POST['Create']) && (($_POST['Package_owner_fullname'])!="") )
	{
	
	//Préparation des inputs de l'utilisateur
	$package_owner_fullname=$_POST['Package_owner_fullname'];
	$activity=$_POST['Activity'];
	$ex=$_POST['Ex'];
	$dmo=$_POST['DMO'];
	$project=$_POST['Project'];
	$observations=$_POST['Observations'];
	$reservation_date=date("Y-m-d");
	$creation_VISA="";
	$verif_owner="";
	$creation_date="0000-00-00";
	$be_2_date="0000-00-00";
	$be_2_visa="";
	$be_3_visa="";
	$be_3_date="0000-00-00";
	$be_3_req_owner="";
	
	

	//Connexion à la Base + determination du nouveau numero de package
	include('../REL_Connexion_DB.php');
	$rel_pack_numbering = 'SELECT MAX(Rel_Pack_Num) from tbl_released_package';
	$rel_pack_numbering_raw = $mysqli->query($rel_pack_numbering);
	$rel_pack_num = mysqli_fetch_row($rel_pack_numbering_raw);
	$rel_pack_num_new = intval($rel_pack_num[0]) + 1;	
    
    $rel_numbering = 'SELECT MAX(ID) from tbl_released_package';
    $rel_max_ID_tmp = $mysqli->query($rel_numbering);
    $rel_max_ID = mysqli_fetch_row($rel_max_ID_tmp);
    $ID_max = intval($rel_max_ID[0]) + 1;
	
	
	//Préparation de la requete SQL
	$sql_1 = 'INSERT INTO tbl_released_package 
			  VALUES (
			     "'.$ID_max.'",
				 "'.$rel_pack_num_new.'",
				 "'.$package_owner_fullname.'",
				 "'.$activity.'",
				 "'.$ex.'",
				 "'.$dmo.'",
				 "'.$project.'",
				 "'.$observations.'",
				 "'.$reservation_date.'",
				 "'.$creation_date.'",
				 "'.$creation_VISA.'",
				 "'.$verif_owner.'",
				 "'.$be_2_visa.'",
				 "'.$be_2_date.'",
				 "'.$be_3_visa.'",
				 "'.$be_3_date.'",
				 "'.$be_3_req_owner.'"
				 )';
	// print_r($sql_1);
	// Query execution
	$result = $mysqli->query($sql_1);
	mysqli_close($mysqli);
	
	$msg='New release package reservation done under number '.$rel_pack_num_new.'  ';
	}
	
	?>

<table border=0>
    
    <tr>
        <td style="width:50%;">
            <div id="FilterTitle">
                Indicate the engineering package owner*:
            </div> 
        </td>
        <td>
            <div id="InpBox">
            <select name="Package_owner_fullname" type="submit" title="" style="width:120px;font-size:12" REQUIRED> 
            <option value=""></option>
            <!--LISTE DEROULANTE DYNAMIQUE-->
            <!------------------------------>
            <?php
                include('../SCM_Connexion_DB.php');
                $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Method' or Department like 'Laboratory' ORDER BY Fullname ASC;";
                $resultat = $mysqli_scm->query($requete);
                while ($row = $resultat->fetch_assoc())
                {
                    //echo'<option value ="'.$row['OTP'].'">'.$row['OTP'].' - '.$row['Title'].'</option><br/>';
                    echo'<option value ="'.$row['Fullname'].'">'.$row['Fullname'].'</option><br/>'; 
                }
                mysqli_close($mysqli_scm);
            ?>
            <!------------------------------>
            </select>
            </div> 
        </td>
    </tr>
    <tr>
        <td>
            <div id="FilterTitle">
                Activity*:
            </div> 
        </td>
        <td>
            <div id="InpBox">
            <select name="Activity" type="submit" title="Activity type" style="width:120px;font-size:12" REQUIRED> 
            <option value=""></option>
            <!--LISTE DEROULANTE DYNAMIQUE-->
            <!------------------------------>
            <?php
                include('../REL_Connexion_DB.php');
                $requete = "SELECT DISTINCT Activity FROM tbl_activity ORDER BY Activity DESC;";
                $resultat = $mysqli->query($requete);
                while ($row = $resultat->fetch_assoc())
                {
                    //echo'<option value ="'.$row['OTP'].'">'.$row['OTP'].' - '.$row['Title'].'</option><br/>';
                    echo'<option value ="'.$row['Activity'].'">'.$row['Activity'].'</option><br/>'; 
                }
                mysqli_close($mysqli);
            ?>
            <!------------------------------>
            </select>
            </div> 
        </td>

    </tr>
    <tr>
        <td>
            <div id="FilterTitle">
                Ex*:
            </div> 
        </td>
        <td>
            <div id="InpBox">
            <select name="Ex" type="submit" title="is the drawing/ref ex?" style="width:68px;font-size:12" REQUIRED> 
            <option value=""></option>
            <!--LISTE DEROULANTE DYNAMIQUE-->
            <!------------------------------>
            <?php
                include('../SCM_Connexion_DB.php');
                $requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex DESC;";
                $resultat = $mysqli_scm->query($requete);
                while ($row = $resultat->fetch_assoc())
                {
                    //echo'<option value ="'.$row['OTP'].'">'.$row['OTP'].' - '.$row['Title'].'</option><br/>';
                    echo'<option value ="'.$row['Ex'].'">'.$row['Ex'].'</option><br/>'; 
                }
                mysqli_close($mysqli_scm);
            ?>
            <!------------------------------>
            </select>
            </div> 
        </td>
    </tr>
    <tr>
        <td>
            <div id="FilterTitle">
                DMO:
            </div> 
        </td>
        <td>
            <div id="InpBox">
            <select name="DMO" type="submit" title="DMO the package is related to" style="width:90px;font-size:12"> 
            <option value=""></option>
            <!--LISTE DEROULANTE DYNAMIQUE-->
            <!------------------------------>
            <?php
                include('../DMO_Connexion_DB.php');
                $requete = "SELECT DISTINCT DMO,Description FROM tbl_dmo ORDER BY DMO DESC;";
                $resultat = $mysqli_dmo->query($requete);
                while ($row = $resultat->fetch_assoc())
                {
                    //echo'<option value ="'.$row['OTP'].'">'.$row['OTP'].' - '.$row['Title'].'</option><br/>';
                    echo'<option value ="'.$row['DMO'].'">'.$row['DMO'].' - '.substr($row['Description'],0,60).'[...]</option><br/>'; 
                }
                mysqli_close($mysqli_dmo);
            ?>
            <!------------------------------>
            </select>
            </div> 
        </td>
    </tr>
    <tr>
        <td>
            <div id="FilterTitle">
                Project*:
            </div> 
        </td>
        <td>
            <div id="InpBox">
                <select name="Project" type="submit" title="Project the change impacts" style="width:68px;font-size:12" REQUIRED> 
                <option value=""></option>
                <?php
                    include('../SCM_Connexion_DB.php');
                    $requete = "SELECT DISTINCT OTP, Title FROM tbl_project ORDER BY OTP DESC;";
                    $resultat = $mysqli_scm->query($requete);
                    while ($row = $resultat->fetch_assoc())
                    {
                        echo'<option value ="'.$row['OTP'].'">'.$row['OTP'].' - '.$row['Title'].'</option><br/>'; 
                    }
                    mysqli_close($mysqli_scm);
                ?>
                </select>
            </div> 
        </td>
    </tr>

    <tr>
        <td rowspan=2>
            <div id="FilterTitle">
                Observation(s):
            </div> 
        </td>
        <td  rowspan=2 style="vertical-align:top">
            <div id="InpBox">
                <textarea rows="3" cols="40" style="font-size:12" name="Observations" title="Provide the details of package - The more info, the better it is"/></textarea><br/>
            </div>
        </td>
    </tr>

    <tr>
    </tr>

    <tr>
        <td colspan=2>
		
            <div id="InpBox" style="text-align:right">
				<?php echo $msg;?>
                <input type="submit" name="Create" class="btn blue2" value=" => Get a new package number " style="width:200px" title="Create a new release package and make a reservation of its number"/>
            </div>
        </td>

    </tr>

</table>

