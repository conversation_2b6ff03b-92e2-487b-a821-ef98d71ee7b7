<table border=0>
    <tr style="border-top:0.5px solid black;" >
        <td colspan=4>
            <div id="Body">
                Package Access:
            </div> 
        </td>
        
    </tr>

    <tr>
        <td style="vertical-align:top;width:140px;text-align:left">
            <div id="FilterTitle">
                Package number list:
            </div> 
            <span id="update_button">
            <div id="indication">
                Pick a package number ...
            </div> 
        </span>
        </td>
        <td>
            <div id="InpBox">
            <select id="Picked_Package_Num_id" size=15 name="Picked_Package_Num" type="submit" title="" style="width:70px;font-size:11" onchange="update_button_script(this.value)"> 
            <!--LISTE DEROULANTE DYNAMIQUE-->
            <!------------------------------>
            <?php
                include('../REL_Connexion_DB.php');
                $requete = 'SELECT DISTINCT Rel_Pack_Num FROM tbl_released_package WHERE Creation_Date="0000-00-00" and Creation_Visa like "" ;';
                $resultat = $mysqli->query($requete);
                while ($row = $resultat->fetch_assoc())
                {
                    echo'<option value ="'.$row['Rel_Pack_Num'].'">'.$row['Rel_Pack_Num'].'</option><br/>'; 
                }
                mysqli_close($mysqli);
            ?>
            <!------------------------------>
            </select>
            </div> 
        </td>

        <td colspan=2 style="text-align:right; width:20%">
        
       
        
        </td>

                <td colspan=3 style="width:550px">
                <span id="REL_Output">
                    <div id="indication">
						Package details ...
                    </div>
            </span>
                </td>
            </tr>
                
    </table>

        <script>
		
		
            function update_button_script(str)
            {
                if (str == "") 
                {
                    document.getElementById("update_button").innerHTML = '<div id="indication">Pick a package number ...</div>';
                    return;
                } else {
                    //const x=document.getElementById("Picked_Package_Num_id").value;
                    //document.getElementById("update_button").innerHTML='<a href=\"REL_BE_1_Form.php?ID=' + x + '\" class=\"btn\"   title=\"Go to the picked package for update\">Go to package ' + x + '</a>';
                       
                    xhttp = new XMLHttpRequest();
                    xhttp.onreadystatechange = function()
                    {
                        if (this.readyState == 4 && this.status == 200) {
                        document.getElementById("REL_Output").innerHTML = this.responseText;
                        }
                    };
                         xhttp.open("GET", "REL_New_Package_Update_Dynamique.php?Rel_Pack_Num="+str, true);
                        xhttp.send();
                    }
            }
        </script>


