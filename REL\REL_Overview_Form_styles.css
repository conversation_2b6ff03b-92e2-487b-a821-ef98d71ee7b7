body {
  background-color: transparent;
  color: black;
  font-size: 9pt;
  font-family: Tahoma, sans-serif;
  font-weight: normal;
}

div#Title {
  font-family: Conneqt,  sans-serif;
  margin-left: 5px;
  margin-top: 5px;
  text-align: left;
  margin-bottom: 6px;
  margin-left: 10px;
  vertical-align: middle;
  font-weight: Bold;
  font-size: 11pt;
  
}

@font-face {
	font-family: "Conneqt";
	src: url("/Common_Resources/font_Conneqt.otf");
}


div#News_Title {
  color: #1a5276;
  font-family: Avenir, fantasy;
  text-align: left;
  text-indent:10px;
  font-size: 14pt;
  font-weight: normal;
}

div#box_title {
  text-align: center;
  vertical-align: middle;
  font-family: Tahoma;
  font-weight: Bold;
  color: white;
}

div#box_title:hover {
  cursor: pointer;
  background-color: #f4f4f4;
  color: black;
}

div#Body {
  text-indent: 10px;
  margin-top: 5px;
}

#Filter {
  text-indent: 15px;
  margin-left: 2px;
  margin-right: 2px;
  text-align: center;
  vertical-align: middle;
}

#FilterTitle {
  margin-left: 2px;
  margin-right: 2px;
  text-align: center;
  vertical-align: middle;
  background: transparent;
}

#confirmation_message {
  font-weight: bold;
  font-family: Tahoma, sans-serif;
  text-align: center;
  margin-top: 25px;
}

div#Result_info {
  text-indent: 10px;
  margin-left: 8px;
  margin-bottom: 8px;
  text-align: justify;
}

#t01 {
  width: 100%;
  border-collapse: collapse;
  vertical-align: middle;
}

#t01 th {
  border: 1px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
}

#t01 td {
  text-align: left;
  vertical-align: middle;
}

#t01 tr {
  height: 20px;
}


