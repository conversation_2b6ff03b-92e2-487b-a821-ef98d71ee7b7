<?php

// ENREGISTREMEENT DES DONNNEES ET SIGNATURE - FLUX NOMINAL
    if (isset($_GET['action']) && ($_GET['action'])=="signoff")
	{

        $id = $_GET['ID'];
        $date_product = date('Y-m-d');
        $cls = htmlspecialchars($_GET['cls'], ENT_QUOTES);
        $moq = htmlspecialchars($_GET['moq'], ENT_QUOTES);
		$eccn = htmlspecialchars($_GET['eccn'], ENT_QUOTES);
		
		if ($_GET['userid']=="%" || $_GET['userid']=="")
		{
			$user="";
		} else {
			$user = $_GET['userid'];
		}
		
		if ($_GET['product_code']=="%" || $_GET['product_code']=="")
		{
			$product_code="";
		} else {
			$product_code = $_GET['product_code'];
		}

		include('../REL_Connexion_DB.php');
        
		// Si le textarea dans REL_PRODUCT_Item.php n'est pas videalors ont afficher "Product : + le message"
        //Commentaire
		$v = 'Product: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

		$query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';

		$resultat = $mysqli->query($query_3);

		// On affiche notre message et à la ligne on laisse l'ancien message
		while ($row = $resultat->fetch_assoc())
		{
			if ($_GET['comment'] != "")
			{
				$v = $v . '\r\n' . $row['General_Comments'];
			} else {
				$v = $row['General_Comments'];
			}
		} 
		//-----------------------

        $query_2 = 'UPDATE tbl_released_drawing 
                        SET DATE_Product="' . $date_product . '",
                            VISA_Product="' . $user . '",
							CLS ="' . $cls . '",
							MOQ ="' . $moq . '",
							ECCN ="' . $eccn . '",
							Product_Code ="' . $product_code . '",
                            General_Comments="' . $v . '"
                            WHERE ID ="' . $id . '";';

        $resultat = $mysqli->query($query_2);

        mysqli_close($mysqli);

    }
	
//
// CHANGEMENT DE DOC TYPE
//
	if (isset($_GET['action']) && ($_GET['action'])=="doctype_change")
	{
	//Connexion à BD
	include('../REL_Connexion_DB.php');

	// création des variables
	$id = $_GET['ID'];
	$doc_type = $_GET['doc_type_change'];
	$date_product = "0000-00-00";
	$date_change_doc_type = date('Y-m-d');
	$user_none="";

	$v = $date_change_doc_type . " - Product : change of supply to " . $doc_type;

	// On parcours le champ General_Comments de la table released_drawing en fonction de l'id
	$query_3 = 'SELECT General_Comments
					FROM tbl_released_drawing
					WHERE ID ="' . $id . '";';

	// Lancement de la requete
	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc()) {
		$v = $v . '\r\n' . $row['General_Comments'];
	}

	// On modifie la base de donnée pour afficher le message qui a été ecrit + tout ce qui a été selectionné
	$query_2 = 'UPDATE tbl_released_drawing 
					SET Doc_Type="' . $doc_type . '",
						VISA_Product="' . $user_none . '",
						DATE_Product="' . $date_product . '",
						General_Comments="' . $v . '"
						WHERE ID ="' . $id . '";';

	// On lance la requete
	$resultat = $mysqli->query($query_2);

	// on ferme la connexion
	mysqli_close($mysqli);
}

    ?>
