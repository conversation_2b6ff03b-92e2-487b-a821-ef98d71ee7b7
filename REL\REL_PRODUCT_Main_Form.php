<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta charset="utf-8" />

	<link rel="stylesheet" type="text/css" href="REL_PRODUCT_Main_Form_styles.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


	<script>
		function chkName(id_record, line_num) {

			const visa = document.getElementById("User_Choice__" + id_record).value;
			const cls = document.getElementById("CLS__" + id_record).value;
			const moq = document.getElementById("MOQ__" + id_record).value;
			const product_code = document.getElementById("Product_Code_Choice__" + id_record).value;
			const eccn = document.getElementById("ECCN__" + id_record).value;

			// RECUPERATION DE LA VALEUR DE ACTION DANS LE TABLEAU
			const data_table = document.getElementById("t02");
			const tr_list = data_table.getElementsByTagName("tr");
			const action = tr_list[line_num + 1].cells[9].textContent.trim();

			// --> SI MODIFICATION, PAS DE MOQ/CLS/PRODUCT CODE OBLIGATOIRE 
			// --> SI CREATION, TOUTES LES INFO SONT OBLIGATOIRES
			if ((visa == "" || visa == "%") || (action == "Creation" && ((cls == "" || cls == "%" || cls == "0") || (moq == "" || moq == "%" || moq == "0") || (product_code == "" || product_code == "%" || product_code == "TBD") || (eccn == "" || eccn == "%" || eccn == "TBD")))) {
				alert("Please fill in the fields CLS, MOQ, Product Code, ECCN and your name prior to validate.");
				return false;
			}

			var res = confirm("Are you sure you want to validate?");
			if (res == false) {
				return false;
			}

			data_update("signoff", id_record, 1);

		}

		// function qui permet de na pas valider et envoyer les données dans la bdd si le type n'est pas donné
		function chkChange(id_record) {
			const visa = document.getElementById("doc_type_change__" + id_record).value;
			if (visa == "" || visa == "%") {
				alert("Please indicate the new doc type prior to validate");
				return false;
			}
			var res = confirm("Are you sure to validate that row?");
			if (res == false) {
				visa = "";
				return false;
			}

			data_update("doctype_change", id_record, 1);
		}

		// MISE A JOUR DE LA BASE DE DONNEES EN VALDIATION ET EN CHANGEMENT DE DOC TYPE
		function data_update(action, id_record, validation_flag) {

			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() {
				// UPDATE OF THE WELCOME PAGE COUNTER
				parent.count_update("id_count_product");
			}

			// FOR SAVING
			var user_val = "";
			if (validation_flag == 1) {
				var user_val = document.getElementById("User_Choice__" + id_record).value;
			}
			// ----

			if (action == "signoff" || action == 1) {
				action = "signoff";
				const url_a = "REL_PRODUCT_Action.php?ID=" + id_record +
					"&cls=" + document.getElementById("CLS__" + id_record).value +
					"&moq=" + document.getElementById("MOQ__" + id_record).value +
					"&eccn=" + document.getElementById("ECCN__" + id_record).value +
					"&product_code=" + document.getElementById("Product_Code_Choice__" + id_record).value +
					"&userid=" + user_val +
					"&action=" + action +
					"&comment=" + document.getElementById("comment__" + id_record).value;

				xhttp.open("GET", url_a);
				xhttp.send();

			} else if (action == "doctype_change") {
				const url_b = "REL_PRODUCT_Action.php?ID=" + id_record +
					"&doc_type_change=" + document.getElementById("doc_type_change__" + id_record).value +
					"&action=" + action;

				xhttp.open("GET", url_b);
				xhttp.send();
			}


		}

		// PERMET DE VALIDER EN MASSE LES DONNEES
		function mass_update()
		{
			
			
			var res = confirm("Are you sure to validate all the rows where you filled your signature in?");
			if (res == false) {
				return false;
			}

			const data_table = document.getElementById("t02");
			const tr_list = data_table.getElementsByTagName("tr");
			//let updated_nb = 0;
			var updated_nb = 0;
			var total_row = 0;
			for (let i = 1; i < tr_list.length; i++)
			{
				var id_record = tr_list[i].cells[0].textContent.trim(); // RECUPERE L'ID DE L'ENREGISTREMENT DANS LA TABLE DE LA BDD
				if (isNaN(id_record) == false) // VERIFIER QUE LES ID RECUPERES SOIENT DES NOMBRES/CHIFFRES
				{
					var user_name = document.getElementById("User_Choice__" + id_record).value;
					if (user_name != "" && user_name != "%") // SELECTIONNE LES LIGNES POUR LESQUELS L'UTILISATEUR A RENSEIGNE SON NOM POUR SIGNER
					{
						const cls = document.getElementById("CLS__" + id_record).value;
						const moq = document.getElementById("MOQ__" + id_record).value;
						const product_code = document.getElementById("Product_Code_Choice__" + id_record).value;
						const action = tr_list[i].cells[9].textContent.trim();
						const eccn = document.getElementById("ECCN__" + id_record).value;
						//alert(eccn + "  " + action + "  " + product_code  + "  " + cls  + "  " + moq);
						if (action == "Creation" && ((cls == "" || cls == "%"  || cls == "0") || (moq == "" || moq == "%" || moq == "0") || (product_code == "" || product_code == "%" || product_code == "TBD") || (eccn == "" || eccn == "%" || eccn == "TBD"))) 
						{
							// --> SI MODIFICATION, PAS DE MOQ/CLS/PRODUCT CODE OBLIGATOIRE 
							// --> SI CREATION, TOUTES LES INFO SONT OBLIGATOIRES
							data_update(1, id_record, 0)
							
						} else {
							data_update("signoff", id_record, 1); // LANCEMENT DE LA FONCTION DE MISE A JOUR DE LA BDD
							updated_nb=updated_nb + 1;
						}
						total_row=total_row+1;
					}
				}
			}
			
			if (total_row>0 && updated_nb<total_row)
			{	
				alert(updated_nb + " out of " + total_row + " update(s) successfully achieved. Non-updated rows were showing missing data. \nREMINDER: for CREATION, all of the MOQ, CLS, Product Code, ECCN values must be filled.");
			}
			//alert(updated_nb + ' rows updated!');
		}
	</script>


</head>

<title>
	REL Pack - PRODUCT Review
</title>

<body>


	<?php
	// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
	include('REL_Workflow_Conditions.php');
	?>


	<form enctype="multipart/form-data" action="" method="post">

		<table id="t01" border=0>

			<tr>
				<td colspan=9>
					<div id="Main_Title">
						PRODUCT Review
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div id="FilterTitle">
						Package #

						<SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
										FROM tbl_released_package 
										LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
										WHERE ' . $Product_Conditions . '
										ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Rel_Pack_Num_Choice'])) {
									if ($_POST['Rel_Pack_Num_Choice'] == $row['Rel_Pack_Num']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Rel_Pack_Num'] != "") {
									echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '"' . $sel . '>' . $row['Rel_Pack_Num'] . '</option>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
						<!--</datalist>-->
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Activity

						<SELECT name="Activity_Choice" type="submit" size="1" style="width:100px;font-size:9pt;height:17px" onchange="this.form.submit()">
							<OPTION value="%"></OPTION>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_package .Activity 
                                FROM tbl_released_package
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Product_Conditions . ' 
                                ORDER BY tbl_released_package.Activity DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Activity_Choice'])) {
									if ($_POST['Activity_Choice'] == $row['Activity']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Activity'] != "") {
									echo '<OPTION value ="' . $row['Activity'] . '"' . $sel . '>' . $row['Activity'] . '</option>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Project

						<SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
							<OPTION value="%"></OPTION>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT Project 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Product_Conditions . '
                                ORDER BY tbl_released_package.Project DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Project_Choice'])) {
									if ($_POST['Project_Choice'] == $row['Project']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Project'] != "") {
									echo '<OPTION value ="' . $row['Project'] . '"' . $sel . '>' . $row['Project'] . '</option>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Reference

						<input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Reference_Choice'])) {
																																							echo ' Value="' . $_POST['Reference_Choice'] . '">';
																																						} ?> </div>
				</td>

				<td>
					<div id="FilterTitle">
						Drawing

						<input type="text" size=20 name="Drawing_Choice" style="font-size:9pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Drawing_Choice'])) {
																																							echo ' Value="' . $_POST['Drawing_Choice'] . '">';
																																						} ?> </div>
				</td>

				<td>
					<div id="FilterTitle">
						Action

						<!--</div>

                    <div id="Filter">-->
						<SELECT name="Action_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Action 
                                FROM tbl_released_package
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Product_Conditions . '
                                ORDER BY tbl_released_drawing.Action ASC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Action_Choice'])) {
									if ($_POST['Action_Choice'] == $row['Action']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Action'] != "") {
									echo '<OPTION value ="' . $row['Action'] . '"' . $sel . '>' . $row['Action'] . '</option>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>

				

				<td>
					<div id="FilterTitle">
						Ex

						<!--</div>
					<div id="Filter">-->
						<SELECT name="Ex_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../SCM_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_ex.Ex 
                                FROM tbl_ex
                                ORDER BY tbl_ex.Ex ASC';
							$resultat = $mysqli_scm->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Ex_Choice'])) {
									if ($_POST['Ex_Choice'] == $row['Ex']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Ex'] != "") {
									echo '<OPTION value ="' . $row['Ex'] . '"' . $sel . '>' . $row['Ex'] . '</option>';
								}
							}
							mysqli_close($mysqli_scm);
							?>
						</SELECT>
					</div>
				</td>

				<td>
					<input type="button" class="btn grey" onclick="window.location.href = 'REL_PRODUCT_Main_Form.php';" style="font-size:8pt; width:45px;height:18px;vertical-align:middle;text-align:center" value="Reset" />
				</td>

			</tr>



			<!--- Vérification des valeurs --->
			<?php

			if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
				$rel_pack_num_choice = "%";
			} else {
				$rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
			}

			if (isset($_POST['Activity_Choice']) == false) {
				$activity_choice = "%";
			} else {
				$activity_choice = $_POST['Activity_Choice'];
			}

			if (isset($_POST['Project_Choice']) == false) {
				$project_choice = "%";
			} else {
				$project_choice = $_POST['Project_Choice'];
			}

			if (isset($_POST['Reference_Choice']) == false) {
				$reference_choice = "%";
			} else {
				if (strlen($_POST['Reference_Choice']) > 0) {
					$reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
				} else {
					$reference_choice = "%";
				}
			}

			if (isset($_POST['Drawing_Choice']) == false) {
				$drawing_choice = "%";
			} else {
				if (strlen($_POST['Drawing_Choice']) > 0) {
					$drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
				} else {
					$drawing_choice = "%";
				}
			}

			if (isset($_POST['Action_Choice']) == false) {
				$action_choice = "%";
			} else {
				$action_choice = $_POST['Action_Choice'];
			}

			if (isset($_POST['Ex_Choice']) == false) {
				$Ex_Choice = "%";
			} else {
				$Ex_Choice = $_POST['Ex_Choice'];
			}

			//$query_1 = 'SELECT * FROM tbl_dmo where Status like "'.$Status_choice.'" && Decision like "'.$Decision_choice.'" && DMO like "'.$DMO_filter.'" && Requestor_Name like "'.$Requestor_choice.'" && Product_Range like "'.$Product_Range_choice.'" && Description like "'.$Description_filter.'" && Ex like "'.$Ex_choice.'" && Eng_Owner like "'.$EngOwner_choice.'" ORDER BY DMO DESC;';

			$query_1 = 'SELECT  *, datediff(Now(),DATE_BE_3) as "Delay"
                FROM tbl_released_package
                LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE 
					' . $Product_Conditions . '
                    AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                    AND tbl_released_package.Activity like "' . $activity_choice . '"
                    AND tbl_released_package.Project like "' . $project_choice . '"
                    AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                    AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                    AND tbl_released_drawing.Action like "' . $action_choice . '"
                    AND tbl_released_drawing.Ex like "' . $Ex_Choice . '"
               ORDER BY tbl_released_package.Rel_Pack_Num DESC';

			include('../REL_Connexion_DB.php');
			$resultat = $mysqli->query($query_1);
			$rowcount = mysqli_num_rows($resultat);

			echo '<tr><td colspan=3><div id="Result_info">Number of results: ' . $rowcount . '&nbsp&nbsp&nbsp';
			echo '</td>';


			// BOUTON DE MISE A JOUR EN MASSE
			echo '<td  colspan=8 style="text-align:right; padding-right:10px;">
		<input onclick="return mass_update()" type="submit" class="btn green" style="font-size:7pt; width:90px;height:15px;vertical-align:middle;text-align:center" name="mass_update_btn" value="Mass Validation" title="Validation of all the lines where a VISA is present" />
		</td>';
			// -----


			echo '</div>';
			echo '</tr>';
			echo '</table>';

			// Création des entetes du tableau
			echo '<table id="t02">';
			echo '<thead>';
			echo '	<th style="width:15px;background-color: rgb(16, 112, 177);" title="Delay in Days">D</th>';
			echo '	<th style="width:40px;background-color: rgb(16, 112, 177);">Pack #</th>';
			echo '	<th style="width:70px;background-color: rgb(16, 112, 177);">Activity</th>';
			echo '	<th style="width:160px;background-color: rgb(16, 112, 177);">Reference</th>';
			echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
			echo '	<th style="width:180px;background-color: rgb(16, 112, 177);">Prod Drawing</th>';
			echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
			echo '	<th style="min-width:100px;background-color: rgb(16, 112, 177);">Title</th>';
			echo '	<th style="width:40px;background-color: rgb(16, 112, 177);">Action</th>';
			echo '	<th style="width:85px;background-color: rgb(16, 112, 177);">Inventory</th>';
			echo '	<th colspan=2 style="width:100px;background-color: rgb(16, 112, 177);">Type</th>';
			echo '	<th style="width:45px;background-color: rgb(16, 112, 177);" title="Responsible Design Office">RDO</th>';
			echo '	<th title="Requestor Remarks" style="width:70px;background-color: rgb(16, 112, 177);">Remarks</th>';
			echo '	<th style="width:50px;" title="Costing Lot Size">CLS</th>';
			echo '	<th style="width:50px;" title="Minimum Of Quantity">MOQ</th>';
			echo '	<th style="width:110px;">Product Code</th>';
			echo '	<th style="width:90px;">ECCN</th>';
			echo '	<th>Comments</th>';
			echo '	<th style="width:90px;">Validation</th>';
			echo '</thead>';


			$i = 0;

			// CONNEXION A LA BASE DB_SCM
			include('../SCM_Connexion_DB.php');
			
			while ($row = $resultat->fetch_assoc()) {
				echo '<tr id ="' . $i . '">
				<td hidden >
					' . $row['ID'] . '
				</td>
				<td title="Number of days of presence - Waiting Time" style="font-size:9px;font-weight:bold">
					' . $row['Delay'] . '
				</td>
				<td >
					<a target ="_blank" href="REL_Pack_Overview.php?ID=' . $row['Rel_Pack_Num'] . '"> ' . $row['Rel_Pack_Num'] . '</a>
				</td>
				<td >
					' . $row['Activity'] . '<br>' . $row['Project'] . '
				</td>
				<td >
					' . $row['Reference'];
				if ($row['Ex'] != "NO") {
					echo '<FONT color="red"><strong><sup>' . $row['Ex'] . '</sup><strong></FONT>';
				}
				echo '
				</td>
				<td>
					' . $row['Ref_Rev'] . '
				</td>';

				// echo '<td>';
				// if ($row['Drawing_Path'] != "") {
				// 	echo '<div class="dropdown_prod_drawing">';

				// 	echo '<a target=_blank href="DRAWINGS\\IN_PROCESS\\' . $row['Drawing_Path'] . '">' . $row['Prod_Draw'] . '</a>';
				// 	echo '<div class="dropdown_prod_drawing-content">';
				// 	echo '<p><iframe src="DRAWINGS\\IN_PROCESS\\' . $row['Drawing_Path'] . '#toolbar=0&navpanes=0&scrollbar=0" width="400px" height="280px" scrolbar=no></iframe>
				// 			</p>';

				// 	echo '</div>';
				// 	echo '</div>';
				// } else {
				// 	echo $row['Prod_Draw'];
				// }
				// echo '</td>';
			
					include('NO_PREVIEW.php');
				

				echo '<td>
					' . $row['Prod_Draw_Rev'] . '
				</td>
				<td >
					' . $row['Ref_Title'] . '
				</td>
				<td classs="Action__' . $row['ID'] . '">';
				// Si la ligne Action est égal à Modification alors on raccourci le mot pour ecrire "modif" à la place
				if ($row['Action'] == "Modification") {
					echo substr($row['Action'], 0, 5);
				} else {
					echo $row['Action'];
				}
				//---------

				echo '</td>
				<td >
					' . $row['Inventory_Impact'] . '
				</td>
				<td style="width:40px">
				
				' . $row['Doc_Type'];
				if ($row['Internal_Mach_Rec'] == 1) {
					echo '<br><img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15">';
				}

				echo '	</td>
				<td style="width:58px">
					<SELECT id="doc_type_change__' . $row['ID'] . '" type="submit" size="1" style="font-size:10px;height:15px;min-width:45px;vertical-align:middle">
						<option value="%"></option>';
				$requete_6 = 'SELECT DISTINCT tbl_doc_type.Doc_Type
						FROM tbl_doc_type';
				$resultat_6 = $mysqli->query($requete_6);
				while ($row_6 = $resultat_6->fetch_assoc()) {
					if ($row['Doc_Type'] != $row_6['Doc_Type']) {
						echo '<OPTION value ="' . $row_6['Doc_Type'] . '">' . $row_6['Doc_Type'] . '</option>';
					}
				}
				echo '		</SELECT>
						<input onclick="return chkChange(' . $row['ID'] . ')" type="submit" class="btn grey" style="font-size:7pt;width:55px;height:15px;vertical-align:middle;text-align:center" name="change_form" value="Change" title="Change the supply type" />
				</td>
				<td>
					' . $row['RDO'] . '
				</td>';

				echo '<td>';
				// Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
				$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");

				//$nmax = 30;
				$nmax = 0;
				if ((strlen($row['Requestor_Comments']) > $nmax)) {
					echo '<div class="dropdown">';
					echo '<span>
								 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
							  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
				}

				echo "<font size=4> | </font>";

				$nmax = 0;
				if ((strlen($row['General_Comments']) > $nmax)) {
					echo htmlspecialchars(substr(nl2br($row['General_Comments']), 0, $nmax), ENT_QUOTES);
					echo '<div class="dropdown">';
					echo '<span>
								<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:1" >
							  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>General Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['General_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:0.3" >';
				}

				echo '</td>';

				// DEBUT CHAMP SAISIE PAR L'UTILISATEUR

				// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
				// pour les champs ECCN, Product CODE, CLS et MOQ, pre-remplir avec les valeurs déjà rentrée sur la revision précédente (si existante).

				$requete_cls = 'SELECT MAX(DATE_Product), Reference, Ref_Rev, CLS, MOQ, ECCN, Product_Code, VISA_Product FROM tbl_released_drawing WHERE Reference like "' . $row['Reference'] . '" AND VISA_Product not like "" AND UPPER(Reference) not like "ZPF000000000XXXXXX" GROUP BY DATE_Product';
				$resultat_cls = $mysqli->query($requete_cls);
				while ($row_cls = $resultat_cls->fetch_assoc()) {
					// $row['CLS'] = $row_cls['CLS'];
					// $row['MOQ'] = $row_cls['MOQ'];
					// $row['ECCN'] = $row_cls['ECCN'];

					if ($row['CLS'] != "0") {
						$row['CLS'] = $row['CLS'];
					} else {
						$row['CLS'] = $row_cls['CLS'];
					}
					if ($row['MOQ'] != "0") {
						$row['MOQ'] = $row['MOQ'];
					} else {
						$row['MOQ'] = $row_cls['MOQ'];
					}
					if($row['Product_Code']!=""){
						$row['Product_Code'] = $row['Product_Code'];
					}else{
						$row['Product_Code'] = $row_cls['Product_Code'];
					}
					if ($row['ECCN'] != "") {
						$row['ECCN'] = $row['ECCN'];
					} else {
						$row['ECCN'] = $row_cls['ECCN'];
					}
				}

				echo '
				<td>
                    <div>
                        <input value="' . $row['CLS'] . '" type="text" tabindex="' . (1000 + $i) . '" size=20 name="CLS__' . $row['ID'] . '" id="CLS__' . $row['ID'] . '" style="text-align:center;font-size:8pt;height:8pt;width:80%;">
                    </div>
                </td>

                <td>
                    <div>
                        <input value="' . $row['MOQ'] . '" type="text" tabindex="' . (2000 + $i) . '" size=20 name="MOQ__' . $row['ID'] . '" id="MOQ__' . $row['ID'] . '" style="text-align:center;font-size:8pt;height:8pt;width:80%;">
                    </div>
                </td>';




				
				$requete_product = 'SELECT *
							   FROM tbl_product_code
							   ORDER BY Code ASC';
				$resultat_product = $mysqli_scm->query($requete_product);
				$row_cnt = mysqli_num_rows($resultat_product);
				if ($row_cnt == 1 && $row['Product_Code'] == "") {
					while ($row_product = $resultat_product->fetch_assoc()) {
						$res = $row_product['Code'];
					}
				} else if ($row['Product_Code'] <> "") {
					$res = $row['Product_Code'];
				} else {
					$res = "";
				}

				echo '<td>
						<div id="FilterTitle">
							<input tabindex="' . (3000 + $i) . '" list="Product_Code' . $row['ID'] . '" name="Product_Code' . $row['ID'] . '" id="Product_Code_Choice__' . $row['ID'] . '" value="' . $res . '" title="Product Code" style="text-align:center;font-family:arial;font-size:10px;height:10px;width:91%">
						<datalist id="Product_Code' . $row['ID'] . '">';

				while ($row_product_2 = $resultat_product->fetch_assoc()) {
					if ($row['Product_Code'] == $row_product_2['Code']) {
						$sel = "SELECTED";
					} else {
						$sel = "";
					}
					echo '<option ' . $sel . ' value ="' . $row_product_2['Code'] . '">' . $row_product_2['Description'] . '</option>';
				}
				//mysqli_close($mysqli_scm);

				echo '</datalist>
						</div>
                    </td>';

				if ($row['ECCN'] <> "") {
					$res = $row['ECCN'];
				} else {
					$res = "";
				}

				echo '<td>
                <div id="FilterTitle">
                <input tabindex="' . (4000 + $i) . '" list="eccn' . $row['ID'] . '" name="eccn' . $row['ID'] . '" id="ECCN__' . $row['ID'] . '" value="' . $res . '" title="ECCN" style="text-align:center;font-family:arial;font-size:10px;height:10px;width:91%">
                     <datalist id="eccn' . $row['ID'] . '">';
				
				$requete_eccn = 'SELECT *
                                       FROM tbl_eccn';
				$resultat_eccn = $mysqli_scm->query($requete_eccn);
				while ($row_eccn = $resultat_eccn->fetch_assoc()) {
					if ($row['ECCN'] == $row_eccn['ECCN']) {
						$sel = "SELECTED";
					} else {
						$sel = "";
					}
					echo '<option ' . $sel . ' value ="' . $row_eccn['ECCN'] . '">' . $row_eccn['Description'] . '</option>';
				}
				//mysqli_close($mysqli_scm);
				echo '</datalist>
                    </div>
            </td>';




				echo '<td>
                    <textarea tabindex="' . (5000 + $i) . '" id="comment__' . $row['ID'] . '" style="background-color:transparent;font-family:Tahoma;font-size:8pt;height:30px;width:94%;vertical-align:middle" ></textarea>
                </td>';


				echo '<td  style="text-align:center">
                        <SELECT tabindex="' . (6000 + $i) . '" id="User_Choice__' . $row['ID'] . '" name="user_name" type="submit" size="1" style="width:95%;font-size:7.5pt;height:17px;">
                            <option value="%"></option>';


				$requete_5 = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
										  FROM tbl_user
										  WHERE UPPER(tbl_user.Department) like "%Product %"';

				$resultat_5 = $mysqli_scm->query($requete_5);

				while ($row4 = $resultat_5->fetch_assoc()) {
					//if (strtoupper(substr($row['Doc_Type'], 0, 3)) == strtoupper(substr($row4['Department'], 0, 3))) {
					echo '<OPTION value ="' . $row4['Fullname'] . '">' . $row4['Fullname'] . '</option>';
					//}
				}
				

				echo '  </SELECT>
						<input name="saving_form" onclick="return data_update(1,' . $row['ID'] . ',0)" type="submit" class="btn orange" style="font-size:7pt;margin-left:-5px; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Save" title="Save the current data without validating it" />
						&nbsp
						<input name="valid_form" onclick="return chkName(' . $row['ID'] . ',' . $i . ')" type="submit" class="btn blue2" style="font-size:7pt; margin-right:-5px;width:35px;height:15px;vertical-align:middle;text-align:center"  value="Sign" title="Sign off the current drawing" />
					</td>
				</tr>';

				$i = $i + 1;
			}
			mysqli_close($mysqli_scm);
			mysqli_close($mysqli);

			?>

		</table>
	</form>

</body>

</html>