<?php

// Vérification des valeurs
if (isset($_GET['action']) && ($_GET['action'])=="signoff")
{

	// création d'autres variables
	//-----------------------------
	$id = $_GET['ID'];
	$date_prod = date("Y-m-d");
	if ($_GET['proc_type']=="%" || $_GET['proc_type']=="")
	{
		$proc_type="";
	} else {
		$proc_type = $_GET['proc_type'];
	}
	if ($_GET['prod_sup']=="%" || $_GET['prod_sup']=="")
	{
		$prod_agent="";
	} else {
		$prod_agent = $_GET['prod_sup'];
	}

	if ($_GET['userid']=="%" || $_GET['userid']=="")
	{
		$user="";
	} else {
		$user = $_GET['userid'];
	}

	if ($_GET['root']!="ASSY")
	{
		$MOF = htmlspecialchars($_GET['mof'], ENT_QUOTES);
		$pris_dans1 = htmlspecialchars($_GET['Pris_Dans1'], ENT_QUOTES);
		$pris_dans2 = htmlspecialchars($_GET['Pris_Dans2'], ENT_QUOTES);
	} else {
		$MOF="";
		$pris_dans1 = "";
		$pris_dans2 = "";
	}
	
	if ($_GET['mat_type']=="%" || $_GET['mat_type']=="")
	{
		$mat_prod_type="";
	} else {
		$mat_prod_type = $_GET['mat_type'];
	}
	if ($_GET['unit']=="%" || $_GET['unit']=="")
	{
		$unit="";
	} else {
		$unit = $_GET['unit'];
	}

	$leadtime = htmlspecialchars($_GET['leadtime'], ENT_QUOTES);

	

	// connexion à la bdd
	include('../REL_Connexion_DB.php');

	// Si le textarea dans REL_PROD_ASSY_Item.php et celui de REL_PROD_MACH_MOLD_Item.php n'est pas vide alors ont afficher "Prod : + le message" 
	// sinon si il n'y a pas de message alors on affiche dans la bdd "Prod : none"
	//Commentaire
		$v = 'Prod: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

		// Requete qui permet de voir le champ General_Comments dans la bdd 
		$query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';

		// Lancement de la requete
		$resultat = $mysqli->query($query_3);

		// On affiche notre message et à la ligne on laisse l'ancien message
		while ($row = $resultat->fetch_assoc())
		{
			if ($_GET['comment'] != "")
			{
				$v = $v . '\r\n' . $row['General_Comments'];
			} else {
				$v = $row['General_Comments'];
			}
		} 
	//-----------------------
	//-----------------------


	$query_2 = 'UPDATE tbl_released_drawing 
					SET Proc_Type="' . $proc_type . '",
						Prod_Agent="' . $prod_agent . '",
						Mat_Prod_Type="' . $mat_prod_type . '",
						UNIT="' . $unit . '",
						leadtime="' . $leadtime . '",
						VISA_Prod="' . $user . '",
						DATE_Prod="' . $date_prod . '",
						Mof="' . $MOF . '",
						Pris_Dans1="' . $pris_dans1 . '",
						Pris_Dans2="' . $pris_dans2 . '",
						General_Comments="' . $v . '"
						WHERE ID ="' . $id . '";';

	$resultat = $mysqli->query($query_2);
	mysqli_close($mysqli);

}


if (isset($_GET['action']) && ($_GET['action'])=="doctype_change")
{
	//Connexion à BD
	include('../REL_Connexion_DB.php');

	// création des variables
	$id = $_GET['ID'];
	$doc_type = $_GET['doc_type_change'];
	$proc_type = "";
	$date_prod = "0000-00-00";
	$user_none = "";
	$pris_dans1 = "";
	$pris_dans2 = "";
	$prod_agent = "";
	$mof = "";
	$leadtime = 0;
    $date_change_doc_type = date('Y-m-d');

	$v = $date_change_doc_type . " - Prod : change of supply to " . $doc_type;

	// On parcours le champ General_Comments de la table released_drawing en fonction de l'id
	$query_3 = 'SELECT General_Comments
					FROM tbl_released_drawing
					WHERE ID ="' . $id . '";';

	// Lancement de la requete
	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc()) {
		$v = $v . '\r\n' . $row['General_Comments'];
	}

	// On modifie la base de donnée pour afficher le message qui a été ecrit + tout ce qui a été selectionné
	$query_2 = 'UPDATE tbl_released_drawing 
					SET Doc_Type="' . $doc_type . '",
						Proc_Type="' . $proc_type . '",
						leadtime="' . $leadtime . '",
						mof="' . $mof . '",
						Prod_Agent="' . $prod_agent . '",
						Pris_Dans1="' . $pris_dans1 . '",
						Pris_Dans2="' . $pris_dans2 . '",
						
						VISA_Quality="",
						DATE_Quality="0000-00-00",
						
						VISA_Prod="' . $user_none . '",
						DATE_Prod="' . $date_prod . '",
						
						General_Comments="' . $v . '"
						WHERE ID ="' . $id . '";';
	//print_r($query_2);

	// On lance la requete
	$resultat = $mysqli->query($query_2);

	// on ferme la connexion
	mysqli_close($mysqli);
}
?>