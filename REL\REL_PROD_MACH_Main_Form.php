<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta charset="utf-8" />

	<link rel="stylesheet" type="text/css" href="REL_PROD_Main_Form_styles.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


	<script>
		// function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné
		function chkName(id_record) {

			const visa = document.getElementById("User_Choice__" + id_record).value;

			// SI BESOIN DE VERROU AVANT VALIATION DU FORMULAIRE
			const pris_dans_fxxx = document.getElementById("Pris_Dans1_id_" + id_record).value;
			// const unit = document.getElementById("unit__" + id_record).value;
			// const leadtime = document.getElementById("leadtime__" + id_record).value;
			// const agent = document.getElementById("Prod_Agent__" + id_record).value;
			// const prisdans1 = document.getElementById("Pris_Dans1_id_" + id_record).value;
			// const prisdans2 = document.getElementById("Pris_Dans2__" + id_record).value;
			const mof = document.getElementById("MOF__" + id_record).value;

			if (pris_dans_fxxx == "%" || pris_dans_fxxx == "") {
				alert("No \"pris dans\" value \r\n - If the reference is a CREATION, a \"pris dans\" must be indicated. \r\n - For a MODIFICATION, this is acceptable as long as there is no impact on the already-defined \"pris dans\" \r\n\r\n Click ok to continue");
			} else if (mof == "" || mof == "%") {
				alert("No MOF value ! \r\n - If the reference is a CREATION, a MOF must be indicated. \r\n - For a MODIFICATION, this is acceptable as long as there is no impact on the MOF \r\n\r\nClick ok to continue");
			} else if (visa == "" || visa == "%") {
				alert("Please indicate your name prior to validate");
				return false;
			}


			var res = confirm("Etes vous sur de vouloir valider ?");
			if (res == false) {
				return false;
			}

			data_update("signoff", id_record, 1);

		}

		// function qui permet de na pas valider et envoyer les données dans la bdd si le type n'est pas donné
		function chkChange(id_record) {
			const visa = document.getElementById("doc_type_change__" + id_record).value;
			if (visa == "" || visa == "%") {
				alert("Please indicate the new doc type prior to validate");
				return false;
			}
			var res = confirm("Are you sure to change the doc type? \r\nThis will reset the following data\r\n\tleadtime \r\n\tProd Supervisor \r\n\tPris Dans\r\n\tMof  ");
			if (res == false) {
				visa = "";
				return false;
			}

			data_update("doctype_change", id_record, 1);
		}

		// MISE A JOUR DE LA BASE DE DONNEES EN VALDIATION ET EN CHANGEMENT DE DOC TYPE
		function data_update(action, id_record, validation_flag) {

			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() {
				// acces retour de process ou message utilisateur pour confirmation
			}

			// FOR SAVING
			var user_val = "";
			if (validation_flag == 1) {
				var user_val = document.getElementById("User_Choice__" + id_record).value;
			}
			// ----

			if (action == "signoff" || action == 1) {
				action = "signoff";
				const url_a = "REL_PROD_Action.php?ID=" + id_record +
					"&proc_type=" + document.getElementById("proc_type__" + id_record).value +
					"&unit=" + document.getElementById("unit__" + id_record).value +
					"&leadtime=" + document.getElementById("leadtime__" + id_record).value +
					"&prod_sup=" + document.getElementById("Prod_Agent__" + id_record).value +
					"&mat_type=" + document.getElementById("mat_type__" + id_record).value +
					"&userid=" + user_val +
					"&mof=" + document.getElementById("MOF__" + id_record).value +
					"&Pris_Dans1=" + document.getElementById("Pris_Dans1_id_" + id_record).value +
					"&Pris_Dans2=" + document.getElementById("Pris_Dans2__" + id_record).value +
					"&action=" + action +
					"&comment=" + document.getElementById("comment__" + id_record).value +
					"&root=MACH_MOLD";

				xhttp.open("GET", url_a);
				xhttp.send();
			} else if (action == "doctype_change") {
				const url_b = "REL_PROD_Action.php?ID=" + id_record +
					"&doc_type_change=" + document.getElementById("doc_type_change__" + id_record).value +
					"&action=" + action +
					"&root=MACH_MOLD";
				xhttp.open("GET", url_b);
				xhttp.send();
			}


		}

		// PERMET DE VALIDER EN MASSE LES DONNEES
		function mass_update() {
			var res = confirm("Are you sure to validate all the rows where you filled your signature in?");
			if (res == false) {
				visa.value = "";
				return false;
			}

			const data_table = document.getElementById("t02");
			const tr_list = data_table.getElementsByTagName("tr");
			//let updated_nb = 0;

			for (let i = 0; i <= tr_list.length; i++) {
				var id_record = tr_list[i].cells[0].textContent.trim(); // RECUPERE L'ID DE L'ENREGISTREMENT DANS LA TABLE DE LA BDD
				if (isNaN(id_record) == false) // VERIFIER QUE LES ID RECUPERES SOIENT DES NOMBRES/CHIFFRES
				{
					var user_name = document.getElementById("User_Choice__" + id_record).value;
					if (user_name != "" && user_name != "%") // SELECTIONNE LES LIGNES POUR LESQUELS L'UTILISATEUR A RENSEIGNE SON NOM POUR SIGNER
					{
						data_update("signoff", id_record, 1); // LANCEMENT DE LA FONCTION DE MISE A JOUR DE LA BDD
						//updated_nb=updated_nb + 1;
					}
				}
			}
			//alert(updated_nb + ' rows updated!');


		}
	</script>


</head>

<title>
	REL Pack - PROD MACHINING Review
</title>

<body>


	<?php
	// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
	include('REL_Workflow_Conditions.php');
	?>


	<form enctype="multipart/form-data" action="" method="post">

		<table id="t01" border=0>

			<tr>
				<td colspan=10>
					<div id="Main_Title">
						PROD MACHINING Review
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<div id="FilterTitle">
						Package #

						<SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
                                            FROM tbl_released_drawing 
                                            WHERE ' . $Prod_MACH_Conditions . '
                                            ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Rel_Pack_Num_Choice'])) {
									if ($_POST['Rel_Pack_Num_Choice'] == $row['Rel_Pack_Num']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Rel_Pack_Num'] != "") {
									echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '"' . $sel . '>' . $row['Rel_Pack_Num'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
						<!--</datalist>-->
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Activity

						<SELECT name="Activity_Choice" type="submit" size="1" style="width:100px;font-size:9pt;height:17px" onchange="this.form.submit()">
							<OPTION value="%"></OPTION>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_package .Activity 
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Prod_MACH_Conditions . '
                                ORDER BY tbl_released_package.Activity DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Activity_Choice'])) {
									if ($_POST['Activity_Choice'] == $row['Activity']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Activity'] != "") {
									echo '<OPTION value ="' . $row['Activity'] . '"' . $sel . '>' . $row['Activity'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Project

						<SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
							<OPTION value="%"></OPTION>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT Project 
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $Prod_MACH_Conditions . ' 
                                ORDER BY tbl_released_package.Project DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Project_Choice'])) {
									if ($_POST['Project_Choice'] == $row['Project']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Project'] != "") {
									echo '<OPTION value ="' . $row['Project'] . '"' . $sel . '>' . $row['Project'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Reference

						<input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Reference_Choice'])) {
																																							echo ' Value="' . $_POST['Reference_Choice'] . '">';
																																						} ?> </div>
				</td>

				<td>
					<div id="FilterTitle">
						Drawing

						<input type="text" size=20 name="Drawing_Choice" style="font-size:9pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Drawing_Choice'])) {
																																							echo ' Value="' . $_POST['Drawing_Choice'] . '">';
																																						} ?> </div>
				</td>

				<td>
					<div id="FilterTitle">
						Action

						<!--</div>

                    <div id="Filter">-->
						<SELECT name="Action_Choice" type="submit" size="1" style="font-size:9pt;height:17px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Action 
                                FROM tbl_released_drawing 
                                WHERE ' . $Prod_MACH_Conditions . '
                                ORDER BY tbl_released_drawing.Action ASC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Action_Choice'])) {
									if ($_POST['Action_Choice'] == $row['Action']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Action'] != "") {
									echo '<OPTION value ="' . $row['Action'] . '"' . $sel . '>' . $row['Action'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>

				

				

				<td>
					<div id="FilterTitle">
						Ex

						<!--</div>
					<div id="Filter">-->
						<SELECT name="Ex_Choice" type="submit" size="1" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../SCM_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_ex.Ex 
                                FROM tbl_ex
                                ORDER BY tbl_ex.Ex ASC';
							$resultat = $mysqli_scm->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Ex_Choice'])) {
									if ($_POST['Ex_Choice'] == $row['Ex']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Ex'] != "") {
									echo '<OPTION value ="' . $row['Ex'] . '"' . $sel . '>' . $row['Ex'] . '</option><br/>';
								}
							}
							mysqli_close($mysqli_scm);
							?>
						</SELECT>
					</div>
				</td>

				<!-- !!!!!!!!!! NOUVEAU !!!!!!!!!! -->
				<!-- bouton reset -->
				<td>
					<input type="button" class="btn grey" onclick="window.location.href = 'REL_PROD_MACH_Main_Form.php';" style="font-size:8pt; width:45px;height:18px;vertical-align:middle;text-align:center" value="Reset" />
				</td>
				<!-- ------------ -->
				<!-- !!!!!!!!!!!!!!!!!!!!! -->


			</tr>



			<!--- Vérification des valeurs --->
			<?php

			if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
				$rel_pack_num_choice = "%";
			} else {
				$rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
			}

			if (isset($_POST['Activity_Choice']) == false) {
				$activity_choice = "%";
			} else {
				$activity_choice = $_POST['Activity_Choice'];
			}

			if (isset($_POST['Project_Choice']) == false) {
				$project_choice = "%";
			} else {
				$project_choice = $_POST['Project_Choice'];
			}

			if (isset($_POST['Reference_Choice']) == false) {
				$reference_choice = "%";
			} else {
				if (strlen($_POST['Reference_Choice']) > 0) {
					$reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
				} else {
					$reference_choice = "%";
				}
			}

			if (isset($_POST['Drawing_Choice']) == false) {
				$drawing_choice = "%";
			} else {
				if (strlen($_POST['Drawing_Choice']) > 0) {
					$drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
				} else {
					$drawing_choice = "%";
				}
			}

			if (isset($_POST['Action_Choice']) == false) {
				$action_choice = "%";
			} else {
				$action_choice = $_POST['Action_Choice'];
			}


			if (isset($_POST['Ex_Choice']) == false) {
				$Ex_Choice = "%";
			} else {
				$Ex_Choice = $_POST['Ex_Choice'];
			}

			//$query_1 = 'SELECT * FROM tbl_dmo where Status like "'.$Status_choice.'" && Decision like "'.$Decision_choice.'" && DMO like "'.$DMO_filter.'" && Requestor_Name like "'.$Requestor_choice.'" && Product_Range like "'.$Product_Range_choice.'" && Description like "'.$Description_filter.'" && Ex like "'.$Ex_choice.'" && Eng_Owner like "'.$EngOwner_choice.'" ORDER BY DMO DESC;';

			$query_1 = 'SELECT *, datediff(Now(),DATE_PRODUCT) as "Delay"
                        FROM tbl_released_package 
                        INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                        WHERE 
						
							' . $Prod_MACH_Conditions . '
						
                            AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                            AND tbl_released_package.Activity like "' . $activity_choice . '"
                            AND tbl_released_package.Project like "' . $project_choice . '"
                            AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                            AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                            AND tbl_released_drawing.Action like "' . $action_choice . '"
                            AND tbl_released_drawing.Ex like "' . $Ex_Choice . '"
                        ORDER BY 
							tbl_released_drawing.prod_draw ASC,
							tbl_released_drawing.prod_draw_rev ASC,							
							tbl_released_drawing.Reference ASC, 
							tbl_released_drawing.Rel_Pack_Num ASC';

			include('../REL_Connexion_DB.php');
			$resultat = $mysqli->query($query_1);
			$rowcount = mysqli_num_rows($resultat);

			echo '<tr><td colspan=3><div id="Result_info">Number of results: ' . $rowcount . '&nbsp&nbsp&nbsp';
			echo '</td>';


			// BOUTON DE MISE A JOUR EN MASSE
			echo '<td  colspan=8 style="text-align:right; padding-right:10px;">
		<input onclick="return mass_update()" type="submit" class="btn green" style="font-size:7pt; width:90px;height:15px;vertical-align:middle;text-align:center" name="mass_update_btn" value="Mass Validation" title="Validation of all the lines where a VISA is present" />
		</td>';
			// -----


			echo '</div>';
			echo '</tr>';
			echo '</table>';

			// Création des entetes du tableau
			echo '<table id="t02">';
			echo '<thead>';
			echo '	<th style="width:15px;background-color: rgb(16, 112, 177);" title="Delay in Days">D</th>';
			echo '	<th style="width:40px;background-color: rgb(16, 112, 177);">Pack #</th>';
			echo '	<th style="width:70px;background-color: rgb(16, 112, 177);">Activity</th>';
			echo '	<th style="min-width:120px;background-color: rgb(16, 112, 177);">Reference</th>';
			echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
			echo '	<th style="min-width:120px;background-color: rgb(16, 112, 177);">Prod Drawing</th>';
			echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
			echo '	<th style="width:45px;background-color: rgb(16, 112, 177);">Action</th>';
			echo '	<th style="width:85px;background-color: rgb(16, 112, 177);">Inventory</th>';
			echo '	<th colspan=2 style="width:100px;background-color: rgb(16, 112, 177);">Type</th>';
			echo '	<th style="width:30px;background-color: rgb(16, 112, 177);">CLS</th>';
			echo '	<th style="width:30px;background-color: rgb(16, 112, 177);">MOQ</th>';
			echo '	<th title="Requestor and other departemnts remarks" style="width:75px;background-color: rgb(16, 112, 177);">Remarks</th>';
			echo '	<th style="width:60px;">Proc Type</th>';
			echo '	<th style="width:60px;">Unit</th>';
			echo '	<th style="width:60px;">Leadtime</th>';
			echo '	<th style="width:60px;" title="Prod Supervisor">Prod Sup.</th>';
			echo '	<th style="width:60px;">Mat Type</th>';
			echo '	<th style="width:145px">"Pris Dans"<br>Material  |  Quantity</th>';
			echo '	<th style="width:70px">MOF #</th>';
			echo '	<th>Comments</th>';
			echo '	<th style="width:110px;">Validation</th>';
			echo '</thead>';


			$i = 0;

			// création du tableau dans une iframe
			while ($row = $resultat->fetch_assoc()) {
				echo '<tr id ="' . $i . '">
				<td hidden >
					' . $row['ID'] . '
				</td>
				<td title="Number of days of presence - Waiting Time" style="font-size:9px;font-weight:bold">
					' . $row['Delay'] . '
				</td>
				<td >
					<a target ="_blank" href="REL_Pack_Overview.php?ID=' . $row['Rel_Pack_Num'] . '"> ' . $row['Rel_Pack_Num'] . '</a>
				</td>
				<td >
					' . $row['Activity'] . '<br>' . $row['Project'] . '
				</td>
				<td >
					' . $row['Reference'];
				if ($row['Ex'] != "NO") {
					echo '<FONT color="red"><strong><sup>' . $row['Ex'] . '</sup><strong></FONT>';
				}
				echo '
				</td>
				<td >
					' . $row['Ref_Rev'] . '
				</td>';

				// echo '<td  >';
				// if ($row['Drawing_Path'] != "") {
					
				// 	$path_file = 'DRAWINGS\\IN_PROCESS\\' . $row['Drawing_Path'];
                //     if (file_exists($path_file) == 0) {

                //         $path_file = 'DRAWINGS\\OFFICIAL\\' . $row['Drawing_Path'];
                //         if (file_exists($path_file) == 0) {
                //             $path_file = 'DRAWINGS\\no_drawing.pdf';
                //         }
                //     }
					
				// 	echo '<div class="dropdown_prod_drawing">';

				// 	echo '<a target=_blank href="' . $path_file . '">' . $row['Prod_Draw'] . '</a>';
				// 	echo '<div class="dropdown_prod_drawing-content">';
				// 	echo '<p><iframe src="' . $path_file . '#toolbar=0&navpanes=0&scrollbar=0" width="400px" height="280px" scrolbar=no></iframe>
				// 			</p>';

				// 	echo '</div>';
				// 	echo '</div>';
				// } else {
				// 	echo $row['Prod_Draw'];
				// }
				// echo '</td>';
				include('NO_PREVIEW.php');

				echo '<td >
					' . $row['Prod_Draw_Rev'] . '
				</td>
				<td >';
				// Si la ligne Action est égal à Modification alors on raccourci le mot pour ecrire "modif" à la place
				if ($row['Action'] == "Modification") {
					echo substr($row['Action'], 0, 5);
				} else {
					echo $row['Action'];
				}
				//---------

				echo '</td>
				<td>
					' . $row['Inventory_Impact'] . '
				</td>
				<td style="width:40px">
				' . $row['Doc_Type'];

				if ($row['Internal_Mach_Rec'] == 1) {
					echo '<img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15">';
				}


				echo '
				</td>
				<td style="width:58px">
					<SELECT id="doc_type_change__' . $row['ID'] . '" type="submit" size="1" style="font-size:10px;height:15px;min-width:45px;vertical-align:middle">
						<option value="%"></option>';
				$requete_6 = 'SELECT DISTINCT tbl_doc_type.Doc_Type
						FROM tbl_doc_type';
				$resultat_6 = $mysqli->query($requete_6);
				while ($row_6 = $resultat_6->fetch_assoc()) {
					if ($row['Doc_Type'] != $row_6['Doc_Type']) {
						echo '<OPTION value ="' . $row_6['Doc_Type'] . '">' . $row_6['Doc_Type'] . '</option><br/>';
					}
				}
				echo '    </SELECT>
		   <input onclick="return chkChange(' . $row['ID'] . ')" type="submit" class="btn grey" style="font-size:7pt;width:55px;height:15px;vertical-align:middle;text-align:center" name="change_form" value="Change" title="Change the supply type" />
				</td>';

				echo '</td><td>
					' . $row['CLS'] . '
				</td>
				<td >
					' . $row['MOQ'] . '
				</td>';

				echo '<td>';
				// Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
				$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");

				//$nmax = 30;
				$nmax = 0;
				if ((strlen($row['Requestor_Comments']) > $nmax)) {
					echo '<div class="dropdown">';
					echo '<span>
						 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
					  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
				}

				echo "<font size=4> | </font>";

				$nmax = 0;
				if ((strlen($row['General_Comments']) > $nmax)) {
					echo htmlspecialchars(substr(nl2br($row['General_Comments']), 0, $nmax), ENT_QUOTES);
					echo '<div class="dropdown">';
					echo '<span>
						<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:1" >
					  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>General Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['General_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:0.3" >';
				}

				echo '</td>';



				// DEBUT CHAMP SAISIE PAR L'UTILISATEUR

				// !!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
				// pour les champs Unit, Leadtime, Prod Sup, Mat_Prod_Type, pris dans 1, pris dans 2, et MOF pre-remplir avec les valeurs déjà rentrée sur la revision précédente (si existante).
				
				$requete_champ = 'SELECT MAX(DATE_Prod), Reference, Ref_Rev, VISA_Prod, Proc_Type, Unit, leadtime, Prod_Agent, Mat_Prod_Type, Pris_Dans1, Pris_Dans2, MOF FROM tbl_released_drawing WHERE Reference like "' . $row['Reference'] . '" AND VISA_Prod not like "" AND Doc_Type like "MACH" AND UPPER(Reference) not like "ZPF000000000XXXXXX" GROUP BY DATE_Prod';
				$resultat_champ = $mysqli->query($requete_champ);
				while ($row_champ = $resultat_champ->fetch_assoc()) {
					// $row['Proc_Type'] = $row_champ['Proc_Type'];
					// $row['Unit'] = $row_champ['Unit'];
					// $row['leadtime'] = $row_champ['leadtime'];
					// $row['Prod_Agent'] = $row_champ['Prod_Agent'];
					// $row['Mat_Prod_Type'] = $row_champ['Mat_Prod_Type'];
					// $row['Pris_Dans1'] = $row_champ['Pris_Dans1'];
					// $row['Pris_Dans2'] = $row_champ['Pris_Dans2'];
					// $row['MOF'] = $row_champ['MOF'];

					if ($row['Proc_Type'] != "") {
						$row['Proc_Type'] = $row['Proc_Type'];
					} else {
						$row['Proc_Type'] = $row_champ['Proc_Type'];
					}
					if ($row['Unit']) {
						$row['Unit'] = $row['Unit'];
					} else {
						$row['Unit'] = $row_champ['Unit'];
					}
					if ($row['leadtime'] != "0") {
						$row['leadtime'] = $row['leadtime'];
					} else {
						$row['leadtime'] = $row_champ['leadtime'];
					}
					if ($row['Prod_Agent'] != "") {
						$row['Prod_Agent'] = $row['Prod_Agent'];
					} else {
						$row['Prod_Agent'] = $row_champ['Prod_Agent'];
					}
					if ($row['Mat_Prod_Type'] != "") {
						$row['Mat_Prod_Type'] = $row['Mat_Prod_Type'];
					} else {
						$row['Mat_Prod_Type'] = $row_champ['Mat_Prod_Type'];
					}
					if ($row['Pris_Dans1'] != "") {
						$row['Pris_Dans1'] = $row['Pris_Dans1'];
					} else {
						$row['Pris_Dans1'] = $row_champ['Pris_Dans1'];
					}
					if ($row['Pris_Dans2'] != "") {
						$row['Pris_Dans2'] = $row['Pris_Dans2'];
					} else {
						$row['Pris_Dans2'] = $row_champ['Pris_Dans2'];
					}
					if ($row['MOF'] != "") {
						$row['MOF'] = $row['MOF'];
					} else {
						$row['MOF'] = $row_champ['MOF'];
					}
				}

				echo '<td>';
				echo '<div id="FilterTitle">';
				echo '<SELECT tabindex="' . (10000 + $i) . '" id="proc_type__' . $row['ID'] . '" type="submit" size="1" style="text-align:center;font-size:8pt;height:17px;width:90%;height:15px;vertical-align:middle;>';
				echo '<option value="%"></option>';
				$requete_1 = 'SELECT DISTINCT tbl_proc_type.Supply_Type
                                FROM tbl_proc_type
                                ORDER BY tbl_proc_type.Supply_Type DESC';
				$resultat_1 = $mysqli->query($requete_1);
				while ($row1 = $resultat_1->fetch_assoc()) {
					$sel = "";
					if ($row['Proc_Type'] != "") {
						if ($row1['Supply_Type'] == $row['Proc_Type']) {
							$sel = "SELECTED";
						}
					} else if ($row1['Supply_Type'] == "E") {
						$sel = "SELECTED";
					}
					if ($row1['Supply_Type']!="F" && $row1['Supply_Type']!="F30")
					{
						echo '<OPTION ' . $sel . ' value ="' . $row1['Supply_Type'] . '">' . $row1['Supply_Type'] . '</option><br/>';
					}
				}

				echo '  </SELECT>
						</div>
					  </td>';

				echo '<td >
						<div id="FilterTitle">
                        <SELECT tabindex="' . (20000 + $i) . '" id="unit__' . $row['ID'] . '" type="submit" size="1" style="text-align:center;font-size:8pt;height:17px;width:90%;height:15px;vertical-align:middle;">
                            <option value="%"></option>';
							include('../SCM_Connexion_DB.php');
				$requete_2 = 'SELECT DISTINCT tbl_unit.Unit
                                FROM tbl_unit';
				$resultat_2 = $mysqli_scm->query($requete_2);
				while ($row1 = $resultat_2->fetch_assoc()) {
					if ($row['Unit'] == $row1['Unit']) {
						$sel = "SELECTED";
					} else {
						$sel = "";
					}
					echo '<OPTION ' . $sel . ' value ="' . $row1['Unit'] . '">' . $row1['Unit'] . '</option><br/>';
				}
				mysqli_close($mysqli_scm);

				echo ' </SELECT>
						</div>
					   </td>';

				echo '<td  title="(Working days)">
                    <div id="FilterTitle">
                        <input tabindex="' . (30000 + $i) . '" value="' . $row['leadtime'] . '" title="Leadtime (Working days)" type="text" id="leadtime__' . $row['ID'] . '" style="text-align:center;font-size:8pt;height:9px;width:85%;">
                    </div>
                </td>';

				echo '<td  >
                    <div id="FilterTitle">
                        <SELECT tabindex="' . (40000 + $i) . '"  id="Prod_Agent__' . $row['ID'] . '" type="submit" size="1" style="font-size:8pt;height:17px;width:90%;height:15px;vertical-align:middle;">
                            <option value="%"></option>';


				$requete_3 = 'SELECT DISTINCT tbl_prod_agent.scheduling_agent
                                FROM tbl_prod_agent';
				$resultat_3 = $mysqli->query($requete_3);
				while ($row1 = $resultat_3->fetch_assoc()) {
					if ($row['Doc_Type'] == "MACH") {
						$to_add = "US";
					} else {
						$to_add = strtoupper(substr($row['Doc_Type'], 0, 2));
					}
					$sel = "";
					if ($row['Prod_Agent'] != "" && $row1['scheduling_agent'] == $row['Prod_Agent']) {
						$sel = "SELECTED";
					}
					if (strtoupper(substr($row1['scheduling_agent'], 0, 2)) == $to_add) {
						echo '<OPTION ' . $sel . ' value ="' . $row1['scheduling_agent'] . '">' . $row1['scheduling_agent'] . '</option><br/>';
					}
				}


				echo '       </SELECT>
                    </div>
                </td>';

				echo '<td >
                        <div id="FilterTitle">
                            <SELECT tabindex="' . (50000 + $i) . '" class="mat_type"  id="mat_type__' . $row['ID'] . '" type="submit" size="1" style="text-align:center;font-size:8pt;height:17px;width:92%;height:15px;vertical-align:middle">
                                <option value="%"></option>';

				$mat_type_name = $row['Material_Type'];
				$requete_4 = 'SELECT DISTINCT *
                                FROM tbl_sap_type';
				$resultat_4 = $mysqli->query($requete_4);
				$in = 0;
				while ($row3 = $resultat_4->fetch_assoc()) {
					if ($row['Mat_Prod_Type'] != "" && $row3['SAP_Type'] == $row['Mat_Prod_Type']) {
						$in = 1;
						$sel = "SELECTED";
					} else if (strtoupper($mat_type_name) == "FINISHED PRODUCT" && strtoupper($row3['SAP_Type']) == "FERT" && $row['Mat_Prod_Type'] == "") {
						$sel = "SELECTED";
						$in = 1;
					} else if (strtoupper($mat_type_name) == "SEMI-FINISHED PRODUCT" && strtoupper($row3['SAP_Type']) == "HALB"  && $row['Mat_Prod_Type'] == "") {
						$sel = "SELECTED";
						$in = 1;
					} else if (strtoupper($mat_type_name) == "RAW MATERIAL" && strtoupper($row3['SAP_Type']) == "ROH"  && $row['Mat_Prod_Type'] == "") {
						$sel = "SELECTED";
						$in = 1;
					} else {
						$sel = "";
					}
					echo '<OPTION ' . $sel . ' value ="' . $row3['SAP_Type'] . '" title="' . $row3['Description'] . '">' . $row3['SAP_Type'] . '</option><br/>';
				}
				if ($in == 0) {
					echo '<OPTION SELECTED value ="' . $row['Mat_Prod_Type'] . '">' . $row['Mat_Prod_Type'] . '</option><br/>';
				}

				echo '</SELECT>
                        </div>
                    </td>';

				if ($row['Pris_Dans1'] <> "") {
					$res = $row['Pris_Dans1'];
					$val = 'Value=';
					$sap_fxxx_search = str_replace(" ", "%", substr($res, 0, 10));
				} elseif ($row['FXXX'] <> "") {
					$res = $row['FXXX'];
					$val = 'Placeholder=';
					$sap_fxxx_search = str_replace(" ", "%", substr($res, 0, 9));
				} else {
					$res = " ";
					$val = "";
					$sap_fxxx_search = "";
				}

				echo '<td style="width:130px;">
						<input tabindex="' . (60000 + $i) . '" list="Pris_Dans1_' . $row['ID'] . '" name="Pris_Dans1_' . $row['ID'] . '" id="Pris_Dans1_id_' . $row['ID'] . '"  ' . $val . '"' . $res . '" title="Material" style="text-align:center;font-family:arial;font-size:10px;height:10px;width:91%"> 
                            <datalist id="Pris_Dans1_' . $row['ID'] . '">';

							include('../SCM_Connexion_DB.php');

				$requete_10 = 'SELECT DISTINCT Code 
							   FROM tbl_sap_fxxx 
							   WHERE Code like "%' . $sap_fxxx_search . '%" 
							   ORDER BY Code Asc';

				$resultat_10 = $mysqli_scm->query($requete_10);
				
				$rowcount_10 = mysqli_num_rows($resultat_10);
				if ($rowcount_10==0)
				{
					$requete_10 = 'SELECT DISTINCT Code 
							   FROM tbl_sap_fxxx';
					$resultat_10 = $mysqli_scm->query($requete_10);
				}					
				while ($row_10 = $resultat_10->fetch_assoc()) {
					if ($row['Pris_Dans1'] == $row_10['Code']) {
						$sel = "SELECTED";
					} else {
						$sel = "";
					}
					echo '<option value ="' . $row_10['Code'] . '">' . $row_10['Code'] . '</option><br/>';
				}

				mysqli_close($mysqli_scm);

				echo '</datalist>
                    <input tabindex="' . (60000 + $i) . '" value="' . $row['Pris_Dans2'] . '" type="text" id="Pris_Dans2__' . $row['ID'] . '" title="Quantity" style="text-align:center;font-size:8pt;height:10px;width:91%;">
					</td>	
					<td>
                            <input tabindex="' . (70000 + $i) . '" value="' . $row['MOF'] . '" type="text" name="MOF__' . $row['ID'] . '" id="MOF__' . $row['ID'] . '" style="text-align:center;font-size:8pt;height:10px;width:90%;">
                    </td>';


				echo '<td>
                    <textarea tabindex="' . (80000 + $i) . '" id="comment__' . $row['ID'] . '" style="background-color:transparent;font-family:Tahoma;font-size:8pt;height:30px;width:94%;vertical-align:middle" ></textarea>
                </td>';


				echo '<td  style="text-align:center">
                        <SELECT tabindex="' . (90000 + $i) . '" id="User_Choice__' . $row['ID'] . '" name="user_name" type="submit" size="1" style="width:95%;font-size:7.5pt;height:17px;">
                            <option value="%"></option>';

				include('../SCM_Connexion_DB.php');
				$requete_5 = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
										  FROM tbl_user
										  WHERE UPPER(tbl_user.Department) like "MACH%"';

				$resultat_5 = $mysqli_scm->query($requete_5);

				while ($row4 = $resultat_5->fetch_assoc()) {
					//if (strtoupper(substr($row['Doc_Type'], 0, 3)) == strtoupper(substr($row4['Department'], 0, 3))) {
					echo '<OPTION value ="' . $row4['Fullname'] . '">' . $row4['Fullname'] . '</option><br/>';
					//}
				}
				mysqli_close($mysqli_scm);

				echo '  </SELECT>
						<input name="saving_form" onclick="return data_update(1,' . $row['ID'] . ',0)" type="submit" class="btn orange" style="font-size:7pt;margin-left:-5px; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Save" title="Save the current data without validating it" />
						&nbsp&nbsp
						<input name="valid_form" onclick="return chkName(' . $row['ID'] . ')" type="submit" class="btn blue2" style="font-size:7pt; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Sign" title="Sign off the current drawing" />
					</td>
				</tr>';

				$i = $i + 1;
			}
			mysqli_close($mysqli);
			?>

		</table>
	</form>

</body>

</html>