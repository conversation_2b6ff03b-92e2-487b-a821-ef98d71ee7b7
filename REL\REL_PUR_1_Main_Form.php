<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
	<meta charset="utf-8" />

	<link rel="stylesheet" type="text/css" href="REL_PUR_Main_Form_styles.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


	<script>
	
	
		// function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné
		function chkName(id_record, line_num) {
			const visa = document.getElementById("User_Choice__" + id_record).value;
			const commodity = document.getElementById("commodity__" + id_record).value;
			const unit = document.getElementById("unit__" + id_record).value;
			const pur_group = document.getElementById("purchasing__" + id_record).value;
			const proc_type = document.getElementById("proc_type__" + id_record).value;

			// RECUPERATION DE LA VALEUR DE ACTION DANS LE TABLEAU
			const data_table = document.getElementById("t02");
			const tr_list = data_table.getElementsByTagName("tr");
			const action = tr_list[line_num].cells[9].textContent.trim();

			if ((visa == "" || visa == "%") || (action == "Creation" && ((commodity == "" || commodity == "%") || (unit == "" || unit == "%") || (pur_group == "" || pur_group == "%") || (proc_type == "" || proc_type == "%")))) {
				alert("Please fill in the fields commodity code, unit, purchasing group, procurrement type and your name prior to validate.");
				return false;
			}

			var res = confirm("Are you sure to validate?");
			if (res == false) {
				return false;
			}
	
			data_update("signoff", id_record, 1);

		}

		// function qui permet de na pas valider et envoyer les données dans la bdd si le type n'est pas donné
		function chkChange(id_record) {
			const visa = document.getElementById("doc_type_change__" + id_record).value;
		
			if (visa == "" || visa == "%") {
				alert("Please indicate the new doc type prior to validate");
				return false;
			} else {
				var res = confirm("Are you sure to change the doc type? \r\nThis will reset the following data\r\n\tCommodity Group \r\n\tPurchasing Group\r\n\tProcurement Type");
				if (res == false) {
					visa.value = "";
					return false;
				}
				data_update("doctype_change", id_record, 1);
			}
		}

		// MISE A JOUR DE LA BASE DE DONNEES EN VALDIATION ET EN CHANGEMENT DE DOC TYPE
		function data_update(action, id_record, validation_flag)
		{		
			
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() {
							// UPDATE OF THE WELCOME PAGE COUNTER --> PARENT / PARENT
							//parent.count_update("id_count_pur");
							}

			// Pour SAVING
			var user_val = "";
			if (validation_flag == 1) {
				var user_val = document.getElementById("User_Choice__" + id_record).value;
			}
			// ----

			//if (((action == "signoff" || action == 1) &&  validation_flag==1) || (validation_flag==0 && action != "signoff")) {
			
			if (((action == "signoff" || action == 1) &&  validation_flag==1)  || (validation_flag==0 && action != "signoff"))
			{
				// Proc type (F/F30) obligatoire pour pouvoir valider une ligne
				const proc_type = document.getElementById("proc_type__" + id_record).value;
				if (validation_flag==0 || ((proc_type != "" && proc_type != "%") && validation_flag==1))
				{
					action = "signoff";
					const url_a = "REL_PUR_Action.php?ID=" + id_record +
						"&proc_type=" + document.getElementById("proc_type__" + id_record).value +
						"&unit=" + document.getElementById("unit__" + id_record).value +
						"&commodity=" + document.getElementById("commodity__" + id_record).value +
						"&purgroup=" + document.getElementById("purchasing__" + id_record).value +
						"&mat_type=" + document.getElementById("mat_type__" + id_record).value +
						"&userid=" + user_val +
						"&action=" + action +
						"&comment=" + document.getElementById("comment__" + id_record).value +
						"&root=PUR_1";

					xhttp.open("GET", url_a);
					xhttp.send();
				}
			} else if (action == "doctype_change") {
				const url_b = "REL_PUR_Action.php?ID=" + id_record +
					"&doc_type_change=" + document.getElementById("doc_type_change__" + id_record).value +
					"&action=" + action;

				xhttp.open("GET", url_b);
				xhttp.send();
			} 
		}

		// PERMET DE VALIDER EN MASSE LES DONNEES
		function mass_update() {
			var res = confirm("Are you sure to validate all the rows where you filled your signature in?");
			if (res == false) {
				visa.value = "";
				return false;
			}

			const data_table = document.getElementById("t02");
			const tr_list = data_table.getElementsByTagName("tr");
			//let updated_nb = 0;

			for (let i = 0; i <= tr_list.length; i++) {
				var id_record = tr_list[i].cells[0].textContent.trim(); // RECUPERE L'ID DE L'ENREGISTREMENT DANS LA TABLE DE LA BDD
				if (isNaN(id_record) == false) // VERIFIER QUE LES ID RECUPERES SOIENT DES NOMBRES/CHIFFRES
				{
					var user_name = document.getElementById("User_Choice__" + id_record).value;
					if (user_name != "" && user_name != "%") // SELECTIONNE LES LIGNES POUR LESQUELS L'UTILISATEUR A RENSEIGNE SON NOM POUR SIGNER
					{
						data_update("signoff", id_record, 1); // LANCEMENT DE LA FONCTION DE MISE A JOUR DE LA BDD
					}
				}
			}
		}

		async function download_drawing() {
			// Récupération de tous les noms de plans dans un tableau
			var plans = document.querySelectorAll('td[name="drawing_name[]"]');
			var plan_folder = "\\REL\\DRAWINGS\\IN_PROCESS\\";
			var downloaded_plans = [];
			var group_size = 7;
			var delay_between_groups = 2000; // 2 secondes

			// Boucle pour télécharger les plans par groupe
			for (var i = 0; i < plans.length; i += group_size) {
				// Récupération du groupe de plans actuel
				var group = Array.prototype.slice.call(plans, i, i + group_size);

				// Boucle pour télécharger chaque plan du groupe
				for (var j = 0; j < group.length; j++) {
					var plan_name = group[j].textContent.trim();
					if (plan_name != "" && plan_name != "-" && !downloaded_plans.includes(plan_name)) {
						var plan_file = plan_folder + plan_name;
						downloadFile(plan_file, plan_name);
						downloaded_plans.push(plan_name);
					}
				}

				// Attente du délai entre chaque groupe de téléchargement
				await new Promise(resolve => setTimeout(resolve, delay_between_groups));
			}
		}

		function downloadFile(url, name) {
			var a = document.createElement("a");
			a.href = url;
			a.setAttribute("download", name);
			a.click();
		}
		
	</script>
</head>

<title>
	REL Pack - PUR RFQ Review
</title>

<body>


	<?php
	// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
	include('REL_Workflow_Conditions.php');
	?>


	<form enctype="multipart/form-data" action="" method="post">

		<table id="t01" border=0>

			<tr>
				<td>
					<div id="FilterTitle">
						Package #

						<SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
										FROM tbl_released_drawing 
										WHERE ' . $PUR_1_RFQ_Conditions . '
										ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Rel_Pack_Num_Choice'])) {
									if ($_POST['Rel_Pack_Num_Choice'] == $row['Rel_Pack_Num']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Rel_Pack_Num'] != "") {
									echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '"' . $sel . '>' . $row['Rel_Pack_Num'] . '</option>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
						<!--</datalist>-->
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Activity
						<SELECT name="Activity_Choice" type="submit" style="width:100px;font-size:9pt;height:17px" onchange="this.form.submit()">
							<OPTION value="%"></OPTION>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_package.Activity 
                                FROM tbl_released_package 
                                LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $PUR_1_RFQ_Conditions . '
                                ORDER BY tbl_released_package.Activity DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Activity_Choice'])) {
									if ($_POST['Activity_Choice'] == $row['Activity']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Activity'] != "") {
									echo '<OPTION value ="' . $row['Activity'] . '"' . $sel . '>' . $row['Activity'] . '</option>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Project
						<SELECT name="Project_Choice" type="submit" style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
							<OPTION value="%"></OPTION>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT Project 
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE ' . $PUR_1_RFQ_Conditions . ' 
                                ORDER BY tbl_released_package.Project DESC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Project_Choice'])) {
									if ($_POST['Project_Choice'] == $row['Project']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Project'] != "") {
									echo '<OPTION value ="' . $row['Project'] . '"' . $sel . '>' . $row['Project'] . '</option>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Reference
						<input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Reference_Choice'])) {
																																							echo ' Value="' . $_POST['Reference_Choice'] . '">';
																																						} ?> </div>
				</td>
				<td>
					<div id="FilterTitle">
						Drawing
						<input type="text" size=20 name="Drawing_Choice" style="font-size:9pt;height:9pt;width:100pt;" onchange="this.form.submit()" <?php if (isset($_POST['Drawing_Choice'])) {
																																							echo ' Value="' . $_POST['Drawing_Choice'] . '">';
																																						} ?> </div>
				</td>
				<td>
					<div id="FilterTitle">
						Action
						<SELECT name="Action_Choice" type="submit" style="font-size:9pt;height:17px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../REL_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_released_drawing.Action 
                                FROM tbl_released_drawing 
                                WHERE ' . $PUR_1_RFQ_Conditions . '
                                ORDER BY tbl_released_drawing.Action ASC';
							$resultat = $mysqli->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Action_Choice'])) {
									if ($_POST['Action_Choice'] == $row['Action']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Action'] != "") {
									echo '<OPTION value ="' . $row['Action'] . '"' . $sel . '>' . $row['Action'] . '</option>';
								}
							}
							mysqli_close($mysqli);
							?>
						</SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Buyer
                         <SELECT name="Buyer_Choice" type="submit" style="font-size:9pt;height:17px" onchange="this.form.submit()">
                             <option value="%"></option>
                             <?php
                             include('../REL_Connexion_DB.php');
                             $requete = 'SELECT DISTINCT tbl_released_drawing.Purchasing_Group 
										 FROM tbl_released_drawing 
										 WHERE '.$PUR_1_RFQ_Conditions.'
										 ORDER BY tbl_released_drawing.Purchasing_Group ASC';
                             $resultat = $mysqli->query($requete);
                             while ($row = $resultat->fetch_assoc())
							 {
								$sel="";
								if (isset($_POST['Buyer_Choice']))
								{
									if ($_POST['Buyer_Choice']==$row['Purchasing_Group'])
									 {
										$sel="SELECTED";
									 } else {
										
									 }
								}
								if ($row['Purchasing_Group']!="")
								{
									echo '<OPTION value ="' . $row['Purchasing_Group'] . '"'.$sel.'>' . $row['Purchasing_Group'] . '</option>';
								}
                             }
                             mysqli_close($mysqli);
                             ?>
                         </SELECT>
					</div>
				</td>
				<td>
					<div id="FilterTitle">
						Ex

						<!--</div>
					<div id="Filter">-->
						<SELECT name="Ex_Choice" type="submit" style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
							<option value="%"></option>
							<?php
							include('../SCM_Connexion_DB.php');
							$requete = 'SELECT DISTINCT tbl_ex.Ex 
                                FROM tbl_ex
                                ORDER BY tbl_ex.Ex ASC';
							$resultat = $mysqli_scm->query($requete);
							while ($row = $resultat->fetch_assoc()) {
								$sel = "";
								if (isset($_POST['Ex_Choice'])) {
									if ($_POST['Ex_Choice'] == $row['Ex']) {
										$sel = "SELECTED";
									} else {
									}
								}
								if ($row['Ex'] != "") {
									echo '<OPTION value ="' . $row['Ex'] . '"' . $sel . '>' . $row['Ex'] . '</option>';
								}
							}
							mysqli_close($mysqli_scm);
							?>
						</SELECT>
					</div>
				</td>

				<td>
					<input type="button" class="btn grey" onclick="window.location.href = 'REL_PUR_1_Main_Form.php';" style="font-size:8pt; width:45px;height:18px;vertical-align:middle;text-align:center" value="Reset" />
				</td>
			</tr>


			<!--- Vérification des valeurs --->
			<?php

			if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
				$rel_pack_num_choice = "%";
			} else {
				$rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
			}

			if (isset($_POST['Activity_Choice']) == false) {
				$activity_choice = "%";
			} else {
				$activity_choice = $_POST['Activity_Choice'];
			}

			if (isset($_POST['Project_Choice']) == false) {
				$project_choice = "%";
			} else {
				$project_choice = $_POST['Project_Choice'];
			}

			if (isset($_POST['Reference_Choice']) == false) {
				$reference_choice = "%";
			} else {
				if (strlen($_POST['Reference_Choice']) > 0) {
					$reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
				} else {
					$reference_choice = "%";
				}
			}

			if (isset($_POST['Drawing_Choice']) == false) {
				$drawing_choice = "%";
			} else {
				if (strlen($_POST['Drawing_Choice']) > 0) {
					$drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
				} else {
					$drawing_choice = "%";
				}
			}

			if (isset($_POST['Action_Choice']) == false) {
				$action_choice = "%";
			} else {
				$action_choice = $_POST['Action_Choice'];
			}

			// if (isset($_POST['Doc_Type_Choice']) == false) {
				// $doc_type_choice = "%";
			// } else {
				// $doc_type_choice = $_POST['Doc_Type_Choice'];
			// }
			
			if (isset($_POST['Buyer_Choice']) == false) {
				$buyer_choice = "%";
			} else {
				$buyer_choice = $_POST['Buyer_Choice'];
			}

			if (isset($_POST['Proc_Type_Choice']) == false) {
				$proc_type_choice = "%";
			} else {
				$proc_type_choice = $_POST['Proc_Type_Choice'];
			}

			if (isset($_POST['Ex_Choice']) == false) {
				$Ex_Choice = "%";
			} else {
				$Ex_Choice = $_POST['Ex_Choice'];
			}

		
			$query_1 = 'SELECT *, datediff(Now(),GREATEST(DATE_PRODUCT, DATE_Quality)) as "Delay"
                        FROM tbl_released_package 
                        INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                        WHERE 
						
							' . $PUR_1_RFQ_Conditions . '
						
                            AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                            AND tbl_released_package.Activity like "' . $activity_choice . '"
                            AND tbl_released_package.Project like "' . $project_choice . '"
                            AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                            AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                            AND tbl_released_drawing.Action like "' . $action_choice . '"
                            AND tbl_released_drawing.Purchasing_Group like "' . $buyer_choice .'"
                            AND tbl_released_drawing.Proc_Type like "' . $proc_type_choice . '"
                            AND tbl_released_drawing.Ex like "' . $Ex_Choice . '"
                        ORDER BY tbl_released_drawing.reference DESC';

			include('../REL_Connexion_DB.php');
			$resultat = $mysqli->query($query_1);
			$rowcount = mysqli_num_rows($resultat);

			echo '<tr><td colspan=3><div id="Result_info">Number of results: ' . $rowcount . '&nbsp&nbsp&nbsp';
			echo '</td>';


			// BOUTON DE MISE A JOUR EN MASSE
			$csv_file_name = '.\Report\\' . date("Y_m_d_H_i") . '_RFQ_Export.csv';
			
			echo '<td border=1 colspan=4 style="text-align:right;">';
			// BOUTON EXTRACTION EXCEL
			if (isset($_POST['excel_extract'])) {
				echo '<a href="' . $csv_file_name . '">' . $csv_file_name . '<a>&nbsp&nbsp';
			}
			echo '<input onclick="" type="submit" id="b1" class="btn grey" style="font-size:8pt; width:95px;height:18px;vertical-align:middle;text-align:center" name="excel_extract" value="Excel Extraction" title="Excel Extraction"/>';

			
			echo '</td>';


			
			echo '<td style="text-align:right;">
		<input onclick="return mass_update()" type="submit" class="btn green" style="font-size:8pt; width:95px;height:18px;vertical-align:middle;text-align:center" name="mass_update_btn" value="Mass Validation" title="Validation of all the lines where a VISA is present" />
		</td>';

			// -----


			echo '</div>';
			echo '</tr>';
			echo '</table>';

			// Création des entetes du tableau
			echo '<table id="t02">';
			echo '<thead>';
			echo '	<th style="width:15px;background-color: rgb(16, 112, 177);" title="Delay in Days">D</th>';
			echo '	<th style="width:40px;background-color: rgb(16, 112, 177);">Pack #</th>';
			echo '	<th style="width:70px;background-color: rgb(16, 112, 177);">Activity</th>';
			echo '	<th style="min-width:120px;background-color: rgb(16, 112, 177);">Reference</th>';
			echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
			echo '	<th style="width:120px;background-color: rgb(16, 112, 177);">Prod Drawing</th>';
			echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
			//echo '	<th style="min-width:100px;background-color: rgb(16, 112, 177);">Title</th>';
			echo '	<th style="width:50px;background-color: rgb(16, 112, 177);">Action</th>';
			echo '	<th style="width:85px;background-color: rgb(16, 112, 177);">Inventory</th>';
			echo '	<th colspan=2 style="width:70px;background-color: rgb(16, 112, 177);">Type</th>';
			//echo '	<th style="width:80px;background-color: rgb(16, 112, 177);">Type</th>';
			echo '	<th style="width:30px;background-color: rgb(16, 112, 177);">CLS</th>';
			echo '	<th style="width:30px;background-color: rgb(16, 112, 177);">MOQ</th>';
			echo '	<th style="width:110px;background-color: rgb(16, 112, 177);" class="quality_col">Documentation Quality Req</th>';
			echo '	<th style="width:120px;background-color: rgb(16, 112, 177);">"Pris Dans"<br>Material  |  Quantity</th>';
			echo '	<th title="Requestor and other departments remarks" style="width:70px;background-color: rgb(16, 112, 177);">Remarks</th>';
			echo '	<th>Mat / Proc Type / Unit</th>';
			echo '	<th style="width:60px;">Commodity / <br> Buyer</th>';
			//echo '<button onclick="test()">+</button>';

			echo '	<th style="width:300px;">Comments</th>';
			echo '	<th style="width:90px;">Validation</th>';
			echo '</thead>';


			$i = 10;
			
			// création du tableau dans une iframe
			while ($row = $resultat->fetch_assoc()) {
				echo '<tr id ="' . $i . '">
				<td hidden>
					' . $row['ID'] . '
				</td>';

				echo '<td hidden id="drawing_id" name="drawing_name[]">' . $row['Drawing_Path'] . '</td>';
				
				echo '<td title="Number of days of presence - Waiting Time" style="font-size:9px;font-weight:bold">
					' . $row['Delay'] . '
				</td>';
				
				echo '<td> ';
				$nmax = 0;
				if ((strlen($row['Observations']) > $nmax)) {
					echo htmlspecialchars(substr(nl2br($row['Observations']), 0, $nmax), ENT_QUOTES);
					echo '<div class="dropdown_observations">';
					echo '<span><b><a tabindex="10000" target ="_blank" href="REL_Pack_Overview.php?ID=' . $row['Rel_Pack_Num'] . '">' . $row['Rel_Pack_Num'] . '</a></b></span>';
					echo '<div class="dropdown_observations-content">';
					echo '<p><b><u>Package Observations</u></b><br>' . htmlspecialchars_decode(nl2br($row['Observations']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<a target ="_blank" href="REL_Pack_Overview.php?ID=' . $row['Rel_Pack_Num'] . '">' . $row['Rel_Pack_Num'] . '</a>';
				}
				echo '</td>';
			
				echo
				'<td>
					' . $row['Activity'] . '<br>' . $row['Project'] . '
				</td>';
				
				
				$font_size='10px';
				$title_len=40;
				if (strlen($row['Ref_Title'])>$title_len)
				{
					$ref_title='<p style="font-size:'.$font_size.';margin-top:3px;margin-bottom:-3px">'.substr($row['Ref_Title'],0,$title_len-6). '&nbsp[...]</p>';
				} else {
					$ref_title='<p style="font-size:'.$font_size.';margin-top:3px;margin-bottom:-3px">'.$row['Ref_Title'].'</p>';
				}
				
				if (strlen($row['Reference'])<=0)
				{
					$ref='-';
				} else {
					$ref=$row['Reference'];
				}
				
				if ($row['Ex'] != "NO") {
                    $ex_val ='<FONT color="red"><strong><sup>' . $row['Ex'] . '</sup></strong></FONT>';
                } else {
					$ex_val="";
				}
                echo '</td>';
				
				echo '<td>' . $ref.$ex_val.'<br>'.$ref_title;
				
                
				
				// echo '
				// <td >
					// ' . $row['Reference'];
				// if ($row['Ex'] != "NO") {
					// echo '<FONT color="red"><strong><sup>' . $row['Ex'] . '</sup><strong></FONT>';
				// }
				// echo '
				// </td>
				echo '<td >
					' . $row['Ref_Rev'] . '
				</td>';

				// echo '<td  >';
				// if ($row['Drawing_Path'] != "") {
				// 	echo '<div class="dropdown_prod_drawing">';

				// 	echo '<a target=_blank href="DRAWINGS\\IN_PROCESS\\' . $row['Drawing_Path'] . '">' . $row['Prod_Draw'] . '</a>';
				// 	echo '<div class="dropdown_prod_drawing-content">';
				// 	echo '<p><iframe src="DRAWINGS\\IN_PROCESS\\' . $row['Drawing_Path'] . '#toolbar=0&navpanes=0&scrollbar=0" width="400px" height="280px" scrolbar=no></iframe>
				// 			</p>';

				// 	echo '</div>';
				// 	echo '</div>';
				// } else {
				// 	echo $row['Prod_Draw'];
				// }
				// echo '</td>';
				include('NO_PREVIEW.php');

				echo '<td >
					' . $row['Prod_Draw_Rev'] . '
				</td>
				
				<td >';
				// Si la ligne Action est égal à Modification alors on raccourci le mot pour ecrire "modif" à la place
				if ($row['Action'] == "Modification") {
					echo substr($row['Action'], 0, 5);
				} else {
					echo $row['Action'];
				}
				//---------




				echo '	</td>
					<td>' . $row['Inventory_Impact'] . '</td>
					<td>';

				echo '		<SELECT tabindex="10000" id="doc_type_change__' . $row['ID'] . '" type="submit"  style="font-size:10px;height:15px;min-width:45px;vertical-align:middle">
						<option value="%"></option>';
				$requete_6 = 'SELECT DISTINCT tbl_doc_type.Doc_Type
						FROM tbl_doc_type';
				$resultat_6 = $mysqli->query($requete_6);
				while ($row_6 = $resultat_6->fetch_assoc()) {
					if ($row['Doc_Type'] != $row_6['Doc_Type']) {
						echo '<OPTION value ="' . $row_6['Doc_Type'] . '">' . $row_6['Doc_Type'] . '</option>';
					}
				}
				echo '	</SELECT>
						<br><input tabindex="10000" onclick="return chkChange(' . $row['ID'] . ')" type="button" class="btn grey" style="font-size:7pt;width:55px;height:15px;vertical-align:middle;text-align:center" name="change_form" value="Change" title="Change the supply type" />
						</td><td style="width:20px";>';
						
				if ($row['Internal_Mach_Rec'] == 1) {
					echo '<img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="20">';
				}

				echo '</td><td>
					' . $row['CLS'] . '
				</td>
				<td >
					' . $row['MOQ'] . '
				</td>';

				echo '<td class="quality_col" style="word-wrap:break-all;text-align:left">';
				echo '<div title="" >' . str_replace("DOC_", "", $row['Q_Doc_Req']) . '</div>';
				//echo '<div title="" >Inspect°: ' . $row['Q_Inspection'] . '</div>';
				//echo '<div title="" >Dynam.: ' . $row['Q_Dynamization'] . '</div>';
				//echo '<div title="" >Contr.:' . $row['Q_Control_Routing'] . '</div>';
				echo '</td>';

				$requete_auto = 'SELECT MAX(DATE_PUR_1), Reference, Pris_Dans2, Pris_Dans1, VISA_PUR_1 FROM tbl_released_drawing WHERE Reference like "' . $row['Reference'] . '" AND VISA_PUR_1 not like "" AND UPPER(Reference) not like "ZPF000000000XXXXXX" GROUP BY DATE_PUR_1';
				$resultat_auto = $mysqli->query($requete_auto);
				while ($row_auto = $resultat_auto->fetch_assoc()) {

					if ($row['Pris_Dans1'] != "") {
						$row['Pris_Dans1'] = $row['Pris_Dans1'];
					} else {
						$row['Pris_Dans1'] = $row_auto['Pris_Dans1'];
					}
					if ($row['Pris_Dans2'] != "") {
						$row['Pris_Dans2'] = $row['Pris_Dans2'];
					} else {
						$row['Pris_Dans2'] = $row_auto['Pris_Dans2'];
					}
				}

				echo '<td>' . $row['Pris_Dans1'] . '<br>' . $row['Pris_Dans2'] . '</td>';

				echo '<td>';
				// Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
				$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");

				//$nmax = 30;
				$nmax = 0;
				if ((strlen($row['Requestor_Comments']) > $nmax)) {
					echo '<div class="dropdown">';
					echo '<span>
						 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
					  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
				}

				echo '</td>';


				// ------------------------------------
				// DEBUT CHAMP SAISIE PAR L'UTILISATEUR
				// ------------------------------------
				echo '<td style="width:100px;">
                        <div id="FilterTitle">
                            <SELECT tabindex="' . $i . '" class="mat_type"  id="mat_type__' . $row['ID'] . '" type="submit" style="text-align:center;margin-bottom:4px;font-size:8pt;height:17px;width:95%;vertical-align:middle">
                                <option value="%"></option>';

				$mat_type_name = $row['Material_Type'];
				$requete_4 = 'SELECT DISTINCT *
                                FROM tbl_sap_type';
				$resultat_4 = $mysqli->query($requete_4);
				$in = 0;
				while ($row3 = $resultat_4->fetch_assoc()) {
					if ($row['Mat_Prod_Type'] != "" && $row3['SAP_Type'] == $row['Mat_Prod_Type']) {
						$in = 1;
						$sel = "SELECTED";
					} else if (strtoupper($mat_type_name) == "FINISHED PRODUCT" && strtoupper($row3['SAP_Type']) == "FERT" && $row['Mat_Prod_Type'] == "") {
						$sel = "SELECTED";
						$in = 1;
					} else if (strtoupper($mat_type_name) == "SEMI-FINISHED PRODUCT" && strtoupper($row3['SAP_Type']) == "HALB"  && $row['Mat_Prod_Type'] == "") {
						$sel = "SELECTED";
						$in = 1;
					} else if (strtoupper($mat_type_name) == "RAW MATERIAL" && strtoupper($row3['SAP_Type']) == "ROH"  && $row['Mat_Prod_Type'] == "") {
						$sel = "SELECTED";
						$in = 1;
					} else {
						$sel = "";
					}
					echo '<OPTION ' . $sel . ' value ="' . $row3['SAP_Type'] . '" title="' . $row3['Description'] . '">' . $row3['SAP_Type'] . '</option>';
				}
				if ($in == 0) {
					echo '<OPTION SELECTED value ="' . $row['Mat_Prod_Type'] . '" Title="OLD DESCRIPTION - DO NOT USE">' . $row['Mat_Prod_Type'] . '</option>';
				}

				echo '     </SELECT>';

				echo '
				<br>
				
				<SELECT tabindex="' . $i . '" id="proc_type__' . $row['ID'] . '" type="submit" style="text-align:center;margin-bottom:4px;font-size:8pt;height:17px;width:95%;">
                            <OPTION  value="%"></option>';
				$requete_10 = 'SELECT DISTINCT tbl_proc_type.Supply_Type
                                FROM tbl_proc_type
								WHERE tbl_proc_type.Supply_Type like "F%"
                                ORDER BY tbl_proc_type.Supply_Type DESC';
				$resultat_10 = $mysqli->query($requete_10);
				while ($row10 = $resultat_10->fetch_assoc()) {
					$sel = "";
					if ($row['Proc_Type'] != "") {
						if ($row10['Supply_Type'] == $row['Proc_Type']) {
							$sel = "SELECTED";
						}
					} else if ($row10['Supply_Type'] == "E") {
						$sel = "SELECTED";
					}
					echo '<OPTION ' . $sel . ' value ="' . $row10['Supply_Type'] . '">' . $row10['Supply_Type'] . '</option>';
				}

				echo '  </SELECT>';

				echo '

				<SELECT tabindex="' . $i . '" id="unit__' . $row['ID'] . '" type="submit" style="text-align:center;font-size:8pt;height:17px;width:95%;">
                            <option value="%"></option>';
							include('../SCM_Connexion_DB.php');
				$requete_2 = 'SELECT DISTINCT tbl_unit.Unit
                                FROM tbl_unit';
				$resultat_2 = $mysqli_scm->query($requete_2);
				while ($row1 = $resultat_2->fetch_assoc()) {
					if ($row['Unit'] == $row1['Unit']) {
						$sel = "SELECTED";
					} else {
						$sel = "";
					}
					echo '<OPTION ' . $sel . ' value ="' . $row1['Unit'] . '">' . $row1['Unit'] . '</option>';
				}
				mysqli_close($mysqli_scm);

				echo ' </SELECT>

                        </div>
                    </td>';

				if ($row['Commodity_Code'] <> "") {
					$res = $row['Commodity_Code'];
				} else {
					$res = "";
				}

				echo '
					<td style="width:100px;">
					<div id="FilterTitle">			
						<input tabindex="' . $i . '" list="commodity' . $row['ID'] . '" name="commodity' . $row['ID'] . '" id="commodity__' . $row['ID'] . '" value="' . $res . '" title="Commodity Code" style="text-align:center;margin-bottom:4px;font-family:arial;font-size:10px;height:11px;width:91%"> 
                        <datalist id="commodity' . $row['ID'] . '">';
				$requete_10 = 'SELECT DISTINCT *
                                       FROM tbl_commodity_code';
				$resultat_10 = $mysqli->query($requete_10);
				while ($row_10 = $resultat_10->fetch_assoc()) {
					if ($row['Commodity_Code'] == $row_10['Code']) {
						$sel = "SELECTED";
					} else {
						$sel = "";
					}
					echo '<option ' . $sel . ' value ="' . $row_10['Code'] . '">' . $row_10['Description'] . '</option><br/>';
				}
				echo '</datalist>
				<SELECT tabindex="' . $i . '" name="purchasing__' . $row['ID'] . '" id="purchasing__' . $row['ID'] . '" type="submit" style="text-align:center;font-size:8pt;height:16px;width:95%;">
                                <option value="%"></option>';
				$requete_4 = 'SELECT DISTINCT tbl_buyer.buyer, tbl_buyer.name
                                FROM tbl_buyer';
				$resultat_4 = $mysqli->query($requete_4);
				while ($row_4 = $resultat_4->fetch_assoc()) {
					if ($row['Purchasing_Group'] == $row_4['buyer']) {
						$sel = "SELECTED";
					} else {
						$sel = "";
					}
					echo '<OPTION ' . $sel . ' value ="' . $row_4['buyer'] . '" title="' . $row_4['name'] . '">' . $row_4['buyer'] . '</option>';
				}
				echo '</SELECT>
				</div>
					</td>';



				echo '<td>
                    <textarea placeholder="' . $row['General_Comments'] . '" tabindex="' . $i . '" id="comment__' . $row['ID'] . '" style="background-color:transparent;font-family:Tahoma;font-size:8pt;height:50px;width:94%" ></textarea>
                </td>';


				echo '<td  style="text-align:center">
                        <SELECT tabindex="' . $i . '" id="User_Choice__' . $row['ID'] . '" name="user_name" type="submit" style="width:95%;font-size:7.5pt;height:17px;">
                            <option value="%"></option>';

				include('../SCM_Connexion_DB.php');
				$requete_5 = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
										  FROM tbl_user
										  WHERE UPPER(tbl_user.Department) like "PUR%"';

				$resultat_5 = $mysqli_scm->query($requete_5);

				while ($row4 = $resultat_5->fetch_assoc()) {
					//if (strtoupper(substr($row['Doc_Type'], 0, 3)) == strtoupper(substr($row4['Department'], 0, 3))) {
					echo '<OPTION value ="' . $row4['Fullname'] . '">' . $row4['Fullname'] . '</option>';
					//}
				}
				mysqli_close($mysqli_scm);

				echo '  </SELECT>
					<input name="saving_form" tabindex="' . $i . '" onclick="return data_update(1,' . $row['ID'] . ',0)" type="submit" class="btn orange" style="font-size:7pt;margin-left:-3px; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Save" title="Save the current data without validating it" />
						
                    <input name="valid_form" tabindex="' . $i . '" onclick="return chkName(' . $row['ID'] . ',' . $i-9 . ')" type="submit" class="btn blue2" style="font-size:7pt; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Sign" title="Sign off the current drawing" />
					</td>
				</tr>';

				$i = $i + 1;
			}

			?>

		</table>
	</form>

</body>

</html>

<?php

// !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!! NOUVEAU !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
// AU click sur le bouton excel_extract, on va générer un fichier excel

if (isset($_POST['excel_extract'])) {

	$table = [];
	$table[] = ['Diffusion', 'Date', 'Responsable', 'Reference', 'Indice plan', 'Designation', 'Codes Exigences Qualité', 'Fiche technique', 'Matiere / ref fournisseur', 'Fourniture Matiere', 'Dimensionnel de la matière fournie Pris Dans', 'Quantités à chiffrer CLS','Fournisseur'];


	$resultat = $mysqli->query($query_1);

	// OUVERTURE DE LA CONNEXION A LA DB SCM POUR L'ASSOCIATION DE LA DESCRIPTION A LA FXXX
	// -------------------------------------------------------------------------------------
	include('../SCM_Connexion_DB.php');
	

	//Parcourons ces enregistrements et insérons les dans notre tableau
	while ($row = $resultat->fetch_assoc()) {
		
		// ASSOCIATION DE LA DESCRIPTION A LA FXXX DONNEE
		// ----------------------------------------------
		$sql_fxxx_desc='SELECT fxxx_description FROM tbl_fxxx WHERE fxxx_ref like "'.$row['FXXX'].'"';
		$result_fxxx_desc = $mysqli_scm->query($sql_fxxx_desc);
		$ct_fxxx_desc = mysqli_num_rows($result_fxxx_desc);
		if($ct_fxxx_desc==1)
		{
			while ($row_desc_fxxx = $result_fxxx_desc->fetch_assoc())
			{
				$fxxx_desc=$row_desc_fxxx['fxxx_description'];
			}
		} else {
			$fxxx_desc="";
		}
		// ----------------------------------------------
		
		//$table[] = [$row['Rel_Pack_Num'], '', '', $row['Activity'] . ' / ' . $row['Project'], $row['Reference'], $row['Prod_Draw_Rev'], $row['Ref_Title'], str_replace(";", " ; ", $row['Q_Doc_Req']), $row['FXXX'], $fxxx_desc, '', $row['Pris_Dans1'] . '/' . $row['Pris_Dans2'], $row['CLS']];
		$table[] = [$row['Rel_Pack_Num'], '', '', $row['Reference'], $row['Prod_Draw_Rev'], $row['Ref_Title'], str_replace(";", " ; ", $row['Q_Doc_Req']), $row['FXXX'], $fxxx_desc, '', $row['Pris_Dans1'] . '/' . $row['Pris_Dans2'], $row['CLS']];
	}
	
	// FERMETURE DE LA CONNEXION A LA DB SCM
	mysqli_close($mysqli_scm);

	//File name defintion
	$csv_file_name = '.\Report\\' . date("Y_m_d_H_i") . '_RFQ_Export.csv';

	//CSV file opening
	$fichier_csv = fopen($csv_file_name, 'w');

	//afficher correctement par exemple les caractères accentués
	fprintf($fichier_csv, chr(0xEF) . chr(0xBB) . chr(0xBF));

	//Parcourer le tableau et écrivons dans le fichier CSV avec la fonction fputcsv
	foreach ($table as $ligne) {
		fputcsv($fichier_csv, $ligne, ";");
	}

	//Fermer maintenant le fichier
	fclose($fichier_csv);

	//echo "<br><a href='" .$csv_file_name. "'>" .$csv_file_name. "</a>created !";
}
mysqli_close($mysqli);

?>