<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_PUR_Main_Form_styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


<script>


	function chkName(id_record, line_num)
	{
		
		const visa = document.getElementById("User_Choice__" + id_record).value;
		const fia = document.getElementById("FIA__" + id_record).value;
		
		if ((visa == "" || visa == "%") || (fia == "" || fia == "%"))
		 {
			alert("Please fill in the FIA and your name prior to validate.");
			return false;
		}
		
		var res = confirm("Are you sure you want to validate?");
		if (res == false) 
		{
			return false;
		}
		
		data_update("signoff", id_record, 1);
		
	}


	// MISE A JOUR DE LA BASE DE DONNEES EN VALDIATION ET EN CHANGEMENT DE DOC TYPE
	function data_update(action, id_record, validation_flag)
	{		

		const xhttp = new XMLHttpRequest();
		xhttp.onload = function() 
		{
			// acces retour de process ou message utilisateur pour confirmation
		}
		
		// Pour SAVING
		var user_val="";
		if (validation_flag==1)
		{
			var user_val=document.getElementById("User_Choice__" + id_record).value;
		}
		// ----
		
		if (action=="signoff" || action==1)
		{
			action="signoff";
			
			var url_a='REL_PUR_Action.php?ID=' + id_record
									 + '&userid=' + user_val
									 + '&action=' + action
									 + '&comment=' + document.getElementById("comment__" + id_record).value
									 + '&fia=' + document.getElementById("FIA__" + id_record).value
									 + '&root=PUR_3'
									 ;
		 
			xhttp.open("GET", url_a);			
			xhttp.send();
		}

			
		
	}
	
	// PERMET DE VALIDER EN MASSE LES DONNEES
	function mass_update()
	{
		var res = confirm("Are you sure to validate all the rows where you filled your signature in?");
		if (res == false)
		{
			visa.value="";
			return false;
		}
		
		const data_table=document.getElementById("t02");
		const tr_list=data_table.getElementsByTagName("tr");
		//let updated_nb = 0;
		
		for (let i = 0; i<=tr_list.length; i++)
		{
			var id_record=tr_list[i].cells[0].textContent.trim(); // RECUPERE L'ID DE L'ENREGISTREMENT DANS LA TABLE DE LA BDD
			if (isNaN(id_record)==false)						  // VERIFIER QUE LES ID RECUPERES SOIENT DES NOMBRES/CHIFFRES
			{
				var user_name=document.getElementById("User_Choice__" + id_record).value;
				if (user_name!="" && user_name!="%")			  // SELECTIONNE LES LIGNES POUR LESQUELS L'UTILISATEUR A RENSEIGNE SON NOM POUR SIGNER
				{
					data_update("signoff", id_record, 1); 			  // LANCEMENT DE LA FONCTION DE MISE A JOUR DE LA BDD
					//updated_nb=updated_nb + 1;
				}
			}
		}
		//alert(updated_nb + ' rows updated!');
	

	}
	

	
	
	
</script>


</head>

<title>
        REL Pack - PURCHASING "FIA" Review
 </title>
<body>


<?php
	// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
	include('REL_Workflow_Conditions.php');
?>


    <form enctype="multipart/form-data" action="" method="post">

        <table id="t01" border=0>

            <!--<tr>
                <td colspan=10>
                    <div id="Main_Title">
                        PROD FIA Review
                    </div>
                </td>
            </tr>-->
            <tr>
                <td>
                    <div id="FilterTitle">
                        Package #

                        <SELECT name="Rel_Pack_Num_Choice" type="submit"  style="font-size:9pt;" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
										FROM tbl_released_drawing 
										WHERE '.$PUR_3_Conditions.'
										ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
								$sel="";
								if (isset($_POST['Rel_Pack_Num_Choice']))
								{
									if ($_POST['Rel_Pack_Num_Choice']==$row['Rel_Pack_Num'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Rel_Pack_Num']!="")
								{
									echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '"'.$sel.'>' . $row['Rel_Pack_Num'] . '</option>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                        <!--</datalist>-->
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Activity

                        <SELECT name="Activity_Choice"  type="submit"  style="width:100px;font-size:9pt;height:17px" onchange="this.form.submit()">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_package .Activity 
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE '.$PUR_3_Conditions.'
                                ORDER BY tbl_released_package.Activity DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                $sel="";
								if (isset($_POST['Activity_Choice']))
								{
									if ($_POST['Activity_Choice']==$row['Activity'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Activity']!="")
								{
									echo '<OPTION value ="' . $row['Activity'] . '"'.$sel.'>' . $row['Activity'] . '</option>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Project

                        <SELECT name="Project_Choice"  type="submit"  style="width:80px;font-size:9pt;height:17px" onchange="this.form.submit()">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT Project 
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE '.$PUR_3_Conditions.' 
                                ORDER BY tbl_released_package.Project DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc())
							{
								$sel="";
								if (isset($_POST['Project_Choice']))
								{
									if ($_POST['Project_Choice']==$row['Project'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Project']!="")
								{
									echo '<OPTION value ="' . $row['Project'] . '"'.$sel.'>' . $row['Project'] . '</option>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Reference

                        <input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;" onchange="this.form.submit()"
						<?php if (isset($_POST['Reference_Choice'])){echo ' Value="'.$_POST['Reference_Choice'].'">';}?>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Drawing

                        <input type="text" size=20 name="Drawing_Choice" style="font-size:9pt;height:9pt;width:100pt;" onchange="this.form.submit()"
						<?php if (isset($_POST['Drawing_Choice'])){echo ' Value="'.$_POST['Drawing_Choice'].'">';}?>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Action
                        <SELECT name="Action_Choice" type="submit"  style="font-size:9pt;height:17px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Action 
                                FROM tbl_released_drawing 
                                WHERE '.$PUR_3_Conditions.'
                                ORDER BY tbl_released_drawing.Action ASC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc())
							{
								$sel="";
								if (isset($_POST['Action_Choice']))
								{
									if ($_POST['Action_Choice']==$row['Action'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Action']!="")
								{
									echo '<OPTION value ="' . $row['Action'] . '"'.$sel.'>' . $row['Action'] . '</option>';
								}
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
						Buyer
                         <SELECT name="Buyer_Choice" type="submit"  style="font-size:9pt;height:17px" onchange="this.form.submit()">
                             <option value="%"></option>
                             <?php
                             include('../REL_Connexion_DB.php');
                             $requete = 'SELECT DISTINCT tbl_released_drawing.Purchasing_Group 
										 FROM tbl_released_drawing 
										 WHERE '.$PUR_3_Conditions.'
										 ORDER BY tbl_released_drawing.Purchasing_Group ASC';
                             $resultat = $mysqli->query($requete);
                             while ($row = $resultat->fetch_assoc())
							 {
								$sel="";
								if (isset($_POST['Buyer_Choice']))
								{
									if ($_POST['Buyer_Choice']==$row['Purchasing_Group'])
									 {
										$sel="SELECTED";
									 } else {
										
									 }
								}
								if ($row['Purchasing_Group']!="")
								{
									echo '<OPTION value ="' . $row['Purchasing_Group'] . '"'.$sel.'>' . $row['Purchasing_Group'] . '</option>';
								}
                             }
                             mysqli_close($mysqli);
                             ?>
                         </SELECT>
                    </div>
                </td>
                <td>
                    <div id="FilterTitle">
                        Ex
                        <SELECT name="Ex_Choice" type="submit"  style="font-size:9pt;height:17px;width:60px" onchange="this.form.submit()">
                            <option value="%"></option>
                            <?php
                            include('../SCM_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_ex.Ex 
                                FROM tbl_ex
                                ORDER BY tbl_ex.Ex ASC';
                            $resultat = $mysqli_scm->query($requete);
                            while ($row = $resultat->fetch_assoc())
							{
								$sel="";
								if (isset($_POST['Ex_Choice']))
								{
									if ($_POST['Ex_Choice']==$row['Ex'])
									{
										$sel="SELECTED";
									} else {
										
									}
								}
								if ($row['Ex']!="")
								{
									echo '<OPTION value ="' . $row['Ex'] . '"'.$sel.'>' . $row['Ex'] . '</option>';
								}
                            }
                            mysqli_close($mysqli_scm);
                            ?>
                        </SELECT>
                    </div>
                </td>
            </tr>
        


        <!--- Vérification des valeurs --->
        <?php

        if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
            $rel_pack_num_choice = "%";
        } else {
            $rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
        }

        if (isset($_POST['Activity_Choice']) == false) {
            $activity_choice = "%";
        } else {
            $activity_choice = $_POST['Activity_Choice'];
        }

        if (isset($_POST['Project_Choice']) == false) {
            $project_choice = "%";
        } else {
            $project_choice = $_POST['Project_Choice'];
        }

        if (isset($_POST['Reference_Choice']) == false) {
            $reference_choice = "%";
        } else {
            if (strlen($_POST['Reference_Choice']) > 0) {
                $reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
            } else {
                $reference_choice = "%";
            }
        }

        if (isset($_POST['Drawing_Choice']) == false) {
            $drawing_choice = "%";
        } else {
            if (strlen($_POST['Drawing_Choice']) > 0) {
                $drawing_choice = str_replace("*", "%", $_POST['Drawing_Choice']);
            } else {
                $drawing_choice = "%";
            }
        }

        if (isset($_POST['Action_Choice']) == false) {
            $action_choice = "%";
        } else {
            $action_choice = $_POST['Action_Choice'];
        }

		if (isset($_POST['Buyer_Choice']) == false) {
            $buyer_choice = "%";
        } else {
            $buyer_choice = $_POST['Buyer_Choice'];
        }
		

        if (isset($_POST['Proc_Type_Choice']) == false) {
            $proc_type_choice = "%";
        } else {
            $proc_type_choice = $_POST['Proc_Type_Choice'];
        }

        if (isset($_POST['Ex_Choice']) == false) {
            $Ex_Choice = "%";
        } else {
            $Ex_Choice = $_POST['Ex_Choice'];
        }

        //$query_1 = 'SELECT * FROM tbl_dmo where Status like "'.$Status_choice.'" && Decision like "'.$Decision_choice.'" && DMO like "'.$DMO_filter.'" && Requestor_Name like "'.$Requestor_choice.'" && Product_Range like "'.$Product_Range_choice.'" && Description like "'.$Description_filter.'" && Ex like "'.$Ex_choice.'" && Eng_Owner like "'.$EngOwner_choice.'" ORDER BY DMO DESC;';

        $query_1 = 'SELECT *, datediff(Now(),DATE_GID_2) as "Delay"
                        FROM tbl_released_package 
                        LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                        WHERE 
						
							'.$PUR_3_Conditions.'
						
                            AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                            AND tbl_released_package.Activity like "' . $activity_choice . '"
                            AND tbl_released_package.Project like "' . $project_choice . '"
                            AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                            AND tbl_released_drawing.Prod_Draw like "' . $drawing_choice . '"
                            AND tbl_released_drawing.Action like "' . $action_choice . '"
							AND tbl_released_drawing.Purchasing_Group like "' . $buyer_choice .'"
                            AND tbl_released_drawing.Proc_Type like "' . $proc_type_choice . '"
                            AND tbl_released_drawing.Ex like "' . $Ex_Choice . '"
                        ORDER BY tbl_released_drawing.reference DESC';

        include('../REL_Connexion_DB.php');
        $resultat = $mysqli->query($query_1);
        $rowcount = mysqli_num_rows($resultat);

        echo '<tr><td colspan=3><div id="Result_info">Number of results: ' . $rowcount . '&nbsp&nbsp&nbsp';
        echo '</td>';
		
		
		// BOUTON DE MISE A JOUR EN MASSE
		echo '<td  colspan=8 style="text-align:right; padding-right:10px;">
		<input onclick="return mass_update()" type="submit" class="btn green" style="font-size:7pt; width:90px;height:15px;vertical-align:middle;text-align:center" name="mass_update_btn" value="Mass Validation" title="Validation of all the lines where a VISA is present" />
		
		</td>';
		// -----
		
		
        echo '</div>';
        echo '</tr>';
		echo '</table>';

        // Création des entetes du tableau
        echo '<table id="t02">';
        echo '<thead>';
		echo '	<th style="width:15px;background-color: rgb(16, 112, 177);" title="Delay in Days">D</th>';
        echo '	<th style="width:40px;background-color: rgb(16, 112, 177);">Pack #</th>';
        echo '	<th style="width:70px;background-color: rgb(16, 112, 177);">Activity</th>';
        echo '	<th style="width:160px;background-color: rgb(16, 112, 177);">Reference</th>';
        echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
        echo '	<th style="width:120px;background-color: rgb(16, 112, 177);">Prod Drawing</th>';
        echo '	<th style="width:12px;background-color: rgb(16, 112, 177);">R</th>';
        echo '	<th style="width:50px;background-color: rgb(16, 112, 177);">Action</th>';
        echo '<th style="width:15px;background-color: rgb(16, 112, 177);"><img src="\Common_Resources\logo_scm_tron.png" style="filter: saturate(10);" title="In house manufacturing preferred" height="15"></th>';
        echo '	<th title="Requestor and other departments remarks" style="width:60px;background-color: rgb(16, 112, 177);">Remarks</th>';
        echo '	<th style="width:130px; background-color: rgb(16, 112, 177);">"Pris Dans"<br>Material  |  Quantity</th>';
		echo '	<th style="width:110px;background-color: rgb(16, 112, 177);">Purchasing Info</th>';
		echo '	<th hidden style="width:60px;background-color: rgb(16, 112, 177);">Proc Type</th>';
		echo '	<th hidden style="width:50px;background-color: rgb(16, 112, 177);">Unit</th>';
		echo '	<th hidden style="width:95px;background-color: rgb(16, 112, 177);">Commodity</th>';
		echo '	<th style="width:50px;background-color: rgb(16, 112, 177);" title="Purchasing Group">Pur. Gr.</th>';
		echo '	<th style="width:70px;" >FIA</th>';
        echo '	<th>Comments</th>';
        echo '	<th style="width:110px;">Validation</th>';
        echo '</thead>';


       $i=0;

        // création du tableau dans une iframe
        while ($row = $resultat->fetch_assoc()) {
           echo '<tr style="height:55px" id ="'.$i.'">
				<td hidden >
					'.$row['ID'].'
				</td>';
			echo '<td title="Number of days of presence - Waiting Time" style="font-size:9px;font-weight:bold">
					' . $row['Delay'] . '
				</td>';
			
			echo '<td> ';
			$nmax = 0;
			if ((strlen($row['Observations']) > $nmax)) {
				echo htmlspecialchars(substr(nl2br($row['Observations']), 0, $nmax), ENT_QUOTES);
				echo '<div class="dropdown_observations">';
				echo '<span><b><a target ="_blank" href="REL_Pack_Overview.php?ID='.$row['Rel_Pack_Num'].'">'.$row['Rel_Pack_Num'].'</a></b></span>';
				echo '<div class="dropdown_observations-content">';
				echo '<p><b><u>Package Observations</u></b><br>' . htmlspecialchars_decode(nl2br($row['Observations']), ENT_QUOTES) . '</p>';
				echo '</div>';
				echo '</div>';
			} else {
				echo '<a target ="_blank" href="REL_Pack_Overview.php?ID='.$row['Rel_Pack_Num'].'">'.$row['Rel_Pack_Num'].'</a>';
			}
			echo '</td>';
				
		   echo '<td >
					'.$row['Activity'].'<br>'.$row['Project'].'
				</td>';
				
					$font_size='10px';
				$title_len=40;
				if (strlen($row['Ref_Title'])>$title_len)
				{
					$ref_title='<p style="font-size:'.$font_size.';margin-top:3px;margin-bottom:-3px">'.substr($row['Ref_Title'],0,$title_len-6). '&nbsp[...]</p>';
				} else {
					$ref_title='<p style="font-size:'.$font_size.';margin-top:3px;margin-bottom:-3px">'.$row['Ref_Title'].'</p>';
				}
				
				if (strlen($row['Reference'])<=0)
				{
					$ref='-';
				} else {
					$ref=$row['Reference'];
				}
				
				if ($row['Ex'] != "NO") {
                    $ex_val ='<FONT color="red"><strong><sup>' . $row['Ex'] . '</sup></strong></FONT>';
                } else {
					$ex_val="";
				}
               
				
				echo '<td>' . $ref.$ex_val.'<br>'.$ref_title.'</td>';
				
			echo '
				<td>
					'.$row['Ref_Rev'].'
				</td>';
				
				include('NO_PREVIEW.php');
				
			echo '<td >
					'.$row['Prod_Draw_Rev'].'
				</td>';
					
					
			echo '
				<td >';
					// Si la ligne Action est égal à Modification alors on raccourci le mot pour ecrire "modif" à la place
					if ($row['Action'] == "Modification")
					{
						echo substr($row['Action'], 0, 5);
					} else {
						echo $row['Action'];
					}
					//---------

			echo '</td>';


				echo '<td>';
				if ($row['Internal_Mach_Rec']==1)
				{
					echo '<img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15">';
				}
				
			echo '</td>';
			
			echo '<td>';
			// Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
			$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");
			
			//$nmax = 30;
			$nmax = 0;
			if ((strlen($row['Requestor_Comments']) > $nmax)) {
				echo '<div class="dropdown">';
				echo '<span>
						 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
					  </span>';
				echo '<div class="dropdown-content">';
				echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES) . '</p>';
				echo '</div>';
				echo '</div>';
			} else {
				echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
			}
			
			echo "<font size=4> | </font>";
			
			$nmax = 0;
			if ((strlen($row['General_Comments']) > $nmax)) {
				echo htmlspecialchars(substr(nl2br($row['General_Comments']), 0, $nmax), ENT_QUOTES);
				echo '<div class="dropdown">';
				echo '<span>
						<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:1" >
					  </span>';
				echo '<div class="dropdown-content">';
				echo '<p><b>- <u>General Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['General_Comments']), ENT_QUOTES) . '</p>';
				echo '</div>';
				echo '</div>';
			} else {
				echo '<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:0.3" >';
			}
			
			echo '</td>';

				echo '<td style="width:80px;">
						'.$row['Pris_Dans1'].' <br>
                        '.$row['Pris_Dans2'].'
					</td>';
				
				echo '<td style="padding:2px">
						<table style="border-collapse:collapse;border:none">
							<tr><td style="border:none;padding:1px">Mat Type: </td><td style="border:none">'.$row['Mat_Prod_Type'].'</td></tr>
							<tr style="padding:1px;background-color:#F0F0F0;border-top:1px solid lightgray;border-bottom:1px solid lightgray;border-right:none;border-left:none"><td style="border:none">Proc Type: </td><td style="border:none">'.$row['Proc_Type'].'</td></tr>
							<tr style="padding:1px;border-top:1px solid lightgray;border-bottom:1px solid lightgray;border-right:none;border-left:none"><td style="border:none">Unit: </td><td style="border:none">'.$row['Unit'].'</td></tr>
							<tr><td style="padding:1px;background-color:#F0F0F0;border:none;">Commodity: </td><td style="border:none">'.$row['Commodity_Code'].'</td></tr>
						</table>
                    </td>';
				
				echo '<td hidden>
						'.$row['Proc_Type'].'
					  </td>';
					  
				echo '<td hidden>
                        '.$row['Unit'].'
					   </td>';
					   
				echo '
					<td hidden>
					    '.$row['Commodity_Code'].'
					</td>';
				
				
				echo '<td>
                        '.$row['Purchasing_Group'].'
                    </td>';
				
				
				echo '<td>
                          <input value="'.$row['FIA'].'" tabindex="'.(80000+$i).'" type="text" id="FIA__'.$row['ID'].'" style="text-align:center;font-size:8pt;height:10px;width:86%;">
					 </td>';
					
				echo '
				<td>
                    <textarea placeholder="'.$row['General_Comments'].'" tabindex="'.(90000+$i).'" id="comment__'.$row['ID'].'" style="vertical-align:middle;background-color:transparent;font-family:Tahoma;font-size:8pt;height:55px;width:98.5%;border:none" ></textarea>
                </td>';
				
				
				echo '<td  style="text-align:center">
                        <SELECT tabindex="'.(100000+$i).'" id="User_Choice__'.$row['ID'].'" name="user_name" type="submit" style="width:97%;font-size:7.5pt;height:17px;">
                            <option value="%"></option>';

                            include('../SCM_Connexion_DB.php');
                            $requete_5 = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
										  FROM tbl_user
										  WHERE UPPER(tbl_user.Department) like "PUR%"';

                            $resultat_5 = $mysqli_scm->query($requete_5);

                            while ($row4 = $resultat_5->fetch_assoc()) {
                                //if (strtoupper(substr($row['Doc_Type'], 0, 3)) == strtoupper(substr($row4['Department'], 0, 3))) {
                                    echo '<OPTION value ="' . $row4['Fullname'] . '">' . $row4['Fullname'] . '</option>';
                                //}
                            }
                            mysqli_close($mysqli_scm);
                   
                echo '  </SELECT>

                    <input name="saving_form" onclick="return data_update(1,'.$row['ID'].',0)" type="submit" class="btn orange" style="font-size:7pt;margin-left:-5px; width:35px;height:15px;vertical-align:middle;text-align:center;"  value="Save" title="Save the current data without validating it" />
						&nbsp&nbsp
					<input name="valid_form" onclick="return chkName('.$row['ID'].','.$i.')" type="submit" class="btn blue2" style="font-size:7pt; width:35px;height:15px;vertical-align:middle;text-align:center"  value="Sign" title="Sign off the current drawing" />

					</td>
				</tr>';

				$i=$i+1;
        }
        mysqli_close($mysqli);
        ?>

        </table>
    </form>

</body>

</html>