<?php

if (isset($_GET['action']) && ($_GET['action'])=="signoff")
{

	$id = $_GET['ID'];
	$date = date("Y-m-d");

	include('../REL_Connexion_DB.php');

	//Commentaire
		$v = 'Purchasing: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

		// Requete qui permet de voir le champ General_Comments dans la bdd 
		$query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';

		// Lancement de la requete
		$resultat = $mysqli->query($query_3);

		// On affiche notre message et à la ligne on laisse l'ancien message
		while ($row = $resultat->fetch_assoc())
		{
			if ($_GET['comment'] != "")
			{
				$v = $v . '\r\n' . $row['General_Comments'];
			} else {
				$v = $row['General_Comments'];
			}
		} 
	//-----------------------

	$query_2 = 'UPDATE tbl_released_drawing
				SET ';
							
	// -----
	//  RFQ
	// -----
	if (isset($_GET['proc_type']))
	{
		if ($_GET['proc_type']=="%" || $_GET['proc_type']=="")
		{
			$proc_type="";
		} else {
			$proc_type = $_GET['proc_type'];
		}
		$query_2 = $query_2 . 'Proc_Type="' . $proc_type . '",';
	}
	if (isset($_GET['mat_type']))
	{
		if ($_GET['mat_type']=="%" || $_GET['mat_type']=="")
		{
			$mat_prod_type="";
		} else {
			$mat_prod_type = $_GET['mat_type'];
		}
		$query_2 = $query_2 . 'Mat_Prod_Type="' . $mat_prod_type . '",';
	}
	if (isset($_GET['unit']))
	{
		if ($_GET['unit']=="%" || $_GET['unit']=="")
		{
			$unit="";
		} else {
			$unit = $_GET['unit'];
		}
		$query_2 = $query_2 . 'Unit="' . $unit . '",';
	}
	if (isset($_GET['commodity']))
	{
		if ($_GET['commodity']=="%" || $_GET['commodity']=="")
		{
			$commodity="";
		} else {
			$commodity = $_GET['commodity'];
		}
		$query_2 = $query_2 . 'Commodity_Code="' . $commodity . '",';
	}
	if (isset($_GET['purgroup']))
	{
		if ($_GET['purgroup']=="%" || $_GET['purgroup']=="")
		{
			$purchasing="";
		} else {
			$purchasing = $_GET['purgroup'];
		}
		$query_2 = $query_2 . 'Purchasing_Group="' . $purchasing . '",';
	}
	if (isset($_GET['userid']))
	{
		$userid = htmlspecialchars($_GET['userid'], ENT_QUOTES);
		if (isset($_GET['root']) && $_GET['root']=="PUR_1")
		{
			$query_2 = $query_2 . 'VISA_PUR_1="' . $userid . '",';
			$query_2 = $query_2 . 'DATE_PUR_1="' . $date . '",';
		} else if (isset($_GET['root']) && $_GET['root']=="PUR_2")
		{
			$query_2 = $query_2 . 'VISA_PUR_2="' . $userid . '",';
			$query_2 = $query_2 . 'DATE_PUR_2="' . $date . '",';
		} else if (isset($_GET['root']) && $_GET['root']=="PUR_3")
		{
			$query_2 = $query_2 . 'VISA_PUR_3="' . $userid . '",';
			$query_2 = $query_2 . 'DATE_PUR_3="' . $date . '",';
		} else if (isset($_GET['root']) && $_GET['root']=="PUR_4")
		{
			$query_2 = $query_2 . 'VISA_PUR_4="' . $userid . '",';
			$query_2 = $query_2 . 'DATE_PUR_4="' . $date . '",';
		} else if (isset($_GET['root']) && $_GET['root']=="PUR_5")
		{
			$query_2 = $query_2 . 'VISA_PUR_5="' . $userid . '",';
			$query_2 = $query_2 . 'DATE_PUR_5="' . $date . '",';
		}
	} 
	// -----
	
	// -----------
	//  Pris Dans
	// -----------
	if (isset($_GET['Pris_Dans1']) && isset($_GET['Pris_Dans2']))
	{
		$pris_dans1 = htmlspecialchars($_GET['Pris_Dans1'], ENT_QUOTES);
		$query_2 = $query_2 .  'Pris_Dans1="' . $pris_dans1 . '",';
	}
	if (isset($_GET['Pris_Dans2']))
	{
		$pris_dans2 = htmlspecialchars($_GET['Pris_Dans2'], ENT_QUOTES);
		$query_2 = $query_2 . 'Pris_Dans2="' . $pris_dans2 . '",';
	}
	// -----
	
	
	// ------
	//  FIA
	// ------
	if (isset($_GET['fia']))
	{
		$FIA = htmlspecialchars($_GET['fia'], ENT_QUOTES);
		$query_2 = $query_2 . 'FIA="' . $FIA . '",';
	}
	// -----
	
	// ------------
	//  ROHS/REACH
	// ------------
	//
	// -----
	
	// -----
	//  HTS
	// -----
	if (isset($_GET['hts']))
	{
		$HTS = htmlspecialchars($_GET['hts'], ENT_QUOTES);
		$query_2 = $query_2 . 'HTS="' . $HTS . '",';
	}
	// -----
	
	
	$query_2 = $query_2 . 'General_Comments="' . $v . '"
							WHERE ID ="' . $id . '";';
	$resultat = $mysqli->query($query_2);
	mysqli_close($mysqli);

}


if (isset($_GET['action']) && ($_GET['action'])=="doctype_change")
{
	//Connexion à BD

	include('../REL_Connexion_DB.php');

	// création des variables
	$id = $_GET['ID'];
	$doc_type = $_GET['doc_type_change'];
	$proc_type = "";
	$commodity = "";
	$purchasing = "";
	$VISA_PUR="";
	$date_z="0000-00-00";
	$pris_dans_1="";
	$pris_dans_2="";
	$fia = "";
	$hts = "";
	$date_change_doc_type = date('Y-m-d');
	
	$metro_time="0";
	$metro_control="";
	
	
	$v = $date_change_doc_type . " - Purchasing : change of supply to " . $doc_type;

	// On parcours le champ General_Comments de la table released_drawing en fonction de l'id
	$query_3 = 'SELECT General_Comments
					FROM tbl_released_drawing
					WHERE ID ="' . $id . '";';

	// Lancement de la requete
	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc()) {
		$v = $v . '\r\n' . $row['General_Comments'];
	}

	$query_2 = 'UPDATE tbl_released_drawing 
					SET 
						Doc_Type="' . $doc_type . '",
						Commodity_Code="' . $commodity . '",
						Purchasing_Group="' . $purchasing . '",
						Proc_Type="' . $proc_type . '",
						Pris_Dans1="'.$pris_dans_1.'",
						Pris_Dans2="'.$pris_dans_2.'",
						FIA="'.$fia.'",
						
						VISA_PUR_1="'.$VISA_PUR.'",
						VISA_PUR_2="'.$VISA_PUR.'",
						VISA_PUR_3="'.$VISA_PUR.'",
						VISA_PUR_4="'.$VISA_PUR.'",
						VISA_PUR_5="'.$VISA_PUR.'",
						
						VISA_Quality="'.$VISA_PUR.'",
						
						VISA_Metro="'.$VISA_PUR.'",
						DATE_Metro="'.$date_z.'",
						Metro_Time="'.$metro_time.'",
						Metro_Control="'.$metro_control.'",
						
						
						DATE_PUR_1="'.$date_z.'",
						DATE_PUR_2="'.$date_z.'",
						DATE_PUR_3="'.$date_z.'",
						DATE_PUR_4="'.$date_z.'",
						DATE_PUR_5="'.$date_z.'",
						
						DATE_Quality="'.$date_z.'",
						
						General_Comments="' . $v . '"
					
					WHERE ID ="' . $id . '";';
					

	$resultat = $mysqli->query($query_2);
	mysqli_close($mysqli);

}
?>
