<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!doctype html>
<html lang="fr">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
	<meta http-equiv="X-UA-Compatible" content="ie=edge">

	<link rel="stylesheet" type="text/css" href="REL_Pack_Overview_style.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

	<title>Overview</title>
</head>

<body>

	<form enctype="multipart/form-data" action="" method="post">
		<table id="t01">
			<tr>
				<td>
					<div id="Main_Title">
						Package <?php echo $_GET['ID']; ?> Overview
					</div>
				</td>
			</tr>
			<tr>
				<td>
					<?php
					$query = 'SELECT * 
                            FROM tbl_released_package
                            LEFT JOIN tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                            WHERE tbl_released_drawing.Rel_Pack_Num like "' . $_GET['ID'] . '"';
					include('../REL_Connexion_DB.php');
					$resultat = $mysqli->query($query);
					$ligne = $resultat->fetch_assoc();

					$pack_exist = mysqli_num_rows($resultat);
					if ($pack_exist > 0) {
						echo '<table id="t03" style="min-width:400px;max-width:100%">';
						echo '<tr>';
						echo '<input type="text" size=20 name="ID" hidden readonly value="' . $_GET['ID'] . '">';

						echo '<td>Pack# :</td>';
						echo '<td>' . $ligne['Rel_Pack_Num'] . '</td>';

						echo '<td title="Demande de Modification - Modification Request">DMO :</td>';
						echo '<td>';
						echo '<a href="..\DMO\DMO_Modification_form.php?dmo=' . $ligne['DMO'] . '">' . $ligne['DMO'] . '</a>';
						echo '</td>';

						echo '<td>EX :</td>';
						echo '<td>' . $ligne['Ex'] . '</td>';

						echo '<td rowspan=2 title="Owner of the package, who created it and sent it through the release process">Package Owner :</td>';
						echo '<td rowspan=2 >' . $ligne['Rel_Pack_Owner'] . '</td>';

						echo '<td rowspan=2 title="Date when the package owner released the package and send it to the engineering verification date">Creation :</td>';
						echo '<td>' . $ligne['Creation_VISA'] . '</td>';

						echo '<td rowspan=2 title="Date and VISA of the person who verified the package">Verification :</td>';
						echo '<td>' . $ligne['VISA_BE_2'] . '</td>';

						echo '<td rowspan=2 title="Date and VISA of the person who validated the package">Validation :</td>';
						echo '<td>' . $ligne['VISA_BE_3'] . '</td>';

						echo '</tr>';

						echo '<tr>';
						echo '<td>Project :</td>';
						echo '<td>' . $ligne['Project'] . '</td>';

						echo '<td>Observations :</td>';
						echo '<td title="Observations added by the package owner">' . $ligne['Observations'] . '</td>';

						echo '<td>Activity :</td>';
						echo '<td>' . $ligne['Activity'] . '</td>';

						echo '<td>' . $ligne['Creation_Date'] . '</td>';

						echo '<td>' . $ligne['DATE_BE_2'] . '</td>';

						echo '<td>' . $ligne['DATE_BE_3'] . '</td>';

						echo '</tr>';
						echo '</table>';
						echo '</td>';
						echo '</tr>';
						echo '<tr>';
						echo '<td style="position:sticky;top:0;">';
						echo '<table style="width: 100%;">';
						echo '<tr>';
						echo '<td>';
						echo '<table id="t04">';
						echo '<tr style="background-color: rgb(27, 79, 114);">';
						echo '<th style="width: 10.8%;">Reference</th>';
						echo '<th style="width: 11.5%;">Prod_Draw</th>';
						echo '<th style="width: 20.2%;">Title</th>';
						echo '<th style="width: 10%;">MOF</th>';
						echo '<th style="width: 10%;">Mat_Type</th>';
						echo '<th style="width: 10%;">Alias</th>';
						echo '<th style="width: 12%;">Pris_Dans1 / 2</th>';
						echo '<th style="width: 7%;">Inven_Imp</th>';
						echo '<th style="width: 4.5%;">Action</th>';
						echo '<th style="width: 3.8%;" >Comments</th>';
						echo '</tr>';
						echo '</table>';
						echo '</td>';
						echo '</tr>';

						echo '<tr>';
						echo '<td>';
						echo '<table style="top:0; margin-top: -4px;" id="t04">';
						echo '<tr>';
						echo '<th style="width: 4.3%;">HTS</th>';
						echo '<th style="width: 4.3%;">ECCN</th>';
						echo '<th style="width: 2.2%;">RDO</th>';
						echo '<th style="width: 7.5%;">Cust_Draw</th>';
						echo '<th style="width: 4.3%;">Doc</th>';
						echo '<th style="width: 2.8%;">Weight</th>';
						echo '<th style="width: 2.7%;">Plat_S</th>';
						echo '<th style="width: 3.1%;">Unit</th>';
						echo '<th style="width: 3.2%;">Prod</th>';
						echo '<th style="width: 4.2%;">FIA</th>';
						echo '<th style="width: 3%;">Sup</th>';
						echo '<th style="width: 3%;">Met.Time</th>';
						echo '<th style="width: 4%;">Commodity</th>';
						echo '<th style="width: 4%;">FXXX</th>';
						echo '<th style="width: 2%;">CLS</th>';
						echo '<th style="width: 2%;">MOQ</th>';
						echo '<th style="width: 2%;">Proc</th>';
						echo '<th style="width: 2%;">Agent</th>';
						echo '<th style="width: 3%;">leadtime</th>';
						echo '<th style="width: 3%;">Buyer</th>';
						echo '<th style="width: 5%;">Product</th>';
						echo '<th style="width: 3.2%;">Control</th>';
						echo '<th style="width: 9.2%;">Quality</th>';
						echo '<th style="width: 3.8%;"></th>';
						echo '</tr>';
						echo '</table>';
						echo '</td>';
						echo '</tr>';
						echo '</table>';
						echo '</td>';
						echo '</tr>';

						echo '<tr>';
						echo '<td>';
						$resultat = $mysqli->query($query);
						while ($ligne = $resultat->fetch_assoc()) {

							echo '<table id="t03">';
							echo '<tr>';
							echo '<td>';
							echo '<table style="background-color: #EBEDEF" id="t04">';
							echo '<tr>';
							echo '<td style="width: 11%;">' . $ligne['Reference'] . ' rev' . $ligne['Ref_Rev'] . '</td>';
							echo '<td style="width: 11.5%;">';
							if ($ligne['Drawing_Path'] != "") {
								$path_file = 'DRAWINGS\\IN_PROCESS\\' . $ligne['Drawing_Path'];
								if (file_exists($path_file) == 0) {

									$path_file = 'DRAWINGS\\OFFICIAL\\' . $ligne['Drawing_Path'];
									if (file_exists($path_file) == 0) {
										$path_file = "";
									}
								}
								if ($path_file != "") {
									echo '<div class="dropdown_prod_drawing">';
									echo '<a target=_blank href="' . $path_file . '">' . $ligne['Prod_Draw'] . ' rev' . $ligne['Prod_Draw_Rev'] . '</a>';
									echo '<div class="dropdown_prod_drawing-content">';
									echo '<p><iframe src="' . $path_file . '#toolbar=0&navpanes=0&scrollbar=0" width="400px" height="280px" scrolbar=no></iframe></p>';
									echo '</div>';
									echo '</div>';
								} else if ($ligne['Prod_Draw'] != "") {
									echo $ligne['Prod_Draw'] . ' rev' . $ligne['Prod_Draw_Rev'];
								} else {
									echo $ligne['Prod_Draw'];
								}
							} else {
								echo $ligne['Prod_Draw'];
							}
							echo '</td>';
							echo '<td style="width: 20%;">' . $ligne['Ref_Title'] . '</td>';
							echo '<td style="width: 10%;">' . htmlspecialchars($ligne['MOF'], ENT_QUOTES) . '</td>';
							echo '<td style="width: 10%;">' . $ligne['Material_Type'] . '</td>';
							echo '<td style="width: 10%;">' . $ligne['Alias'] . '</td>';
							echo '<td style="width: 12%;">';
							if ($ligne['Pris_Dans1'] != "") {
								echo htmlspecialchars($ligne['Pris_Dans1'], ENT_QUOTES);
								if ($ligne['Pris_Dans2'] != "") {
									echo ' / ' . htmlspecialchars($ligne['Pris_Dans2'], ENT_QUOTES);
								}
							} else if ($ligne['Pris_Dans2'] != "") {
								echo htmlspecialchars($ligne['Pris_Dans2'], ENT_QUOTES);
							}
							echo '</td>';
							echo '<td style="width: 7%;">' . $ligne['Inventory_Impact'] . '</td>';
							echo '<td style="width: 4.5%;">' . $ligne['Action'] . '</td>';
							echo '<td style="width: 10%;">';
							$nbre_lignes = substr_count(nl2br($ligne['Requestor_Comments']), "\n");
							$nmax = 0;
							if ((strlen($ligne['Requestor_Comments']) > $nmax)) {
								echo '<div class="dropdown">';
								echo '<span>
												 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
											  </span>';
								echo '<div style="z-index:2;" class="dropdown-content">';
								echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($ligne['Requestor_Comments']), ENT_QUOTES) . '</p>';
								echo '</div>';
								echo '</div>';
							} else {
								echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
							}
							echo '</td>';
							echo '</tr>';
							echo '</table>';
							echo '</td>';
							echo '</tr>';

							echo '<tr>';
							echo '<td>';
							echo '<table style="background-color: #ffffff" id="t04">';
							echo '<tr>';
							echo '<td style="width: 4%;">' . $ligne['HTS'] . '</td>';
							echo '<td style="width: 4%;">' . $ligne['ECCN'] . '</td>';
							echo '<td style="width: 2%;">' . $ligne['RDO'] . '</td>';
							if ($ligne['Cust_Drawing'] != "") {
								echo '<td style="width: 7%;">' . $ligne['Cust_Drawing'] . ' rev' . $ligne['Cust_Drawing_Rev'] . '</td>';
							} else {
								echo '<td style="width: 7%;"></td>';
							}
							echo '<td style="width: 4%;">' . $ligne['Doc_Type'];
							if ($ligne['Internal_Mach_Rec'] == 1) {
								echo '<img src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15">';
							}
							echo '</td>';
							echo '<td style="width: 2.6%;">' . $ligne['Weight'] . ' ' . $ligne['Weight_Unit'] . '</td>';
							echo '<td style="width: 2.6%;">' . $ligne['Plating_Surface'] . ' ' . $ligne['Plating_Surface_Unit'] . '</td>';
							echo '<td style="width: 3%;">' . $ligne['Unit'] . '</td>';
							echo '<td style="width: 3%;">' . $ligne['Mat_Prod_Type'] . '</td>';
							echo '<td style="width: 4%;">' . htmlspecialchars($ligne['FIA'], ENT_QUOTES) . '</td>';
							echo '<td style="width: 2.5%;">' . $ligne['Supervisor'] . '</td>';
							echo '<td style="width: 3%;">' . $ligne['Metro_Time'] . '</td>';
							echo '<td style="width: 4%;">' . $ligne['Commodity_Code'] . '</td>';
							echo '<td style="width: 4%;">' . $ligne['FXXX'] . '</td>';
							echo '<td style="width: 2%;">' . htmlspecialchars($ligne['CLS'], ENT_QUOTES) . '</td>';
							echo '<td style="width: 2%;">' . htmlspecialchars($ligne['MOQ'], ENT_QUOTES) . '</td>';
							echo '<td style="width: 1.5%;">' . $ligne['Proc_Type'] . '</td>';
							echo '<td style="width: 2%;">' . $ligne['Prod_Agent'] . '</td>';
							echo '<td style="width: 2.7%;">' . htmlspecialchars($ligne['leadtime'], ENT_QUOTES) . '</td>';
							echo '<td style="width: 2.8%;">' . $ligne['Purchasing_Group'] . '</td>';
							echo '<td style="width: 5%;">' . $ligne['Product_Code'] . '</td>';
							echo '<td style="width: 3%;">' . $ligne['Metro_Control'] . '</td>';
							echo '<td style="width: 1%;">';
							$nbre_lignes = substr_count(nl2br($ligne['Q_Doc_Req']), "\n");
							$nmax = 0;
							if ((strlen($ligne['Q_Doc_Req']) > $nmax)) {
								echo '<div class="dropdown">';
								echo '<span>
                                                                                                 <img src="\Common_Resources\doc_req_inspection_icon.png" style="height:15px; opacity:1" >
                                                                                              </span>';
								echo '<div class="dropdown-content">';
								echo '<p><b>- <u>Q_Doc_Requirements</u> -</b><br \>' . htmlspecialchars_decode(nl2br($ligne['Q_Doc_Req']), ENT_QUOTES) . '</p>';
								echo '</div>';
								echo '</div>';
							} else {
								echo '<img src="\Common_Resources\doc_req_inspection_icon.png" style="height:15px; opacity:0.3;" >';
							}
							echo '</td>';
							echo '<td style="width: 3%;">' . $ligne['Q_Dynamization'] . '</td>';
							echo '<td style="width: 1%;">';
							$nbre_lignes = substr_count(nl2br($ligne['Q_Inspection']), "\n");
							$nmax = 0;
							if ((strlen($ligne['Q_Inspection']) > $nmax)) {
								echo '<div class="dropdown">';
								echo '<span>
														 <img src="\Common_Resources\doc_req_inspection_icon.png" style="height:15px; opacity:1" >
													  </span>';
								echo '<div class="dropdown-content">';
								echo '<p><b>- <u>Q_Inspection</u> -</b><br \>' . htmlspecialchars_decode(nl2br($ligne['Q_Inspection']), ENT_QUOTES) . '</p>';
								echo '</div>';
								echo '</div>';
							} else {
								echo '<img src="\Common_Resources\doc_req_inspection_icon.png" style="height:15px; opacity:0.3;" >';
							}
							echo '</td>';
							echo '<td style="width: 3%;">' . $ligne['Q_Control_Routing'] . '</td>';
							echo '<td style="width: 3.4%;">';
							$nbre_lignes = substr_count(nl2br($ligne['General_Comments']), "\n");
							$nmax = 0;
							if ((strlen($ligne['General_Comments']) > $nmax)) {
								echo htmlspecialchars(substr(nl2br($ligne['General_Comments']), 0, $nmax), ENT_QUOTES);
								echo '<div style="z-index:1; position: relative;" class="dropdown">';
								echo '<span>
												<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px;" >
											  </span>';
								echo '<div class="dropdown-content">';
								echo '<p><b>- <u>General Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($ligne['General_Comments']), ENT_QUOTES) . '</p>';
								echo '</div>';
								echo '</div>';
							} else {
								echo '<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:0.3" >';
							}
							echo '</td>';
							echo '</tr>';
							echo '</table>';
							echo '</td>';
							echo '</tr>';
							echo '</table>';
						}
						echo '</td>';
						echo '</tr>';
						echo '</table>';
					} else {
						echo 'Release package not existing - If the pacakge was processed between 2012 and 2022, information might be available in the old release package tool (Access database).';
					}
					?>
				</td>
			</tr>
		</table>
	</form>
</body>

</html>