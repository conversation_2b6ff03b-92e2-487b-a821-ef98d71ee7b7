<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html>
<head>
<!--<meta http-equiv="X-UA-Compatible" content="IE=edge" />-->

<meta http-equiv='cache-control' content='no-cache'>
<meta http-equiv="cache-control" content="max-age=0" />
<meta http-equiv='expires' content='0'>
<meta http-equiv='pragma' content='no-cache'>


<link rel="stylesheet" type="text/css" href="REL_Package_Content_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


<script>

	function root_test(id)
	{
		// DETERMINATION DE LA PAGE PARENT DANS LAQUELLE CETTE PAGE EST APPELLEE
		let init_path=window.parent.location.href;
		let searched_REL_BE_1="REL_BE_1_Form";
		let root_REL_BE_1=init_path.includes(searched_REL_BE_1,0);
		// -----
		
		if (root_REL_BE_1==true)
		{
			document.getElementById("creation_based_on_" + id).style.display="inline";
		} else {
			document.getElementById("creation_based_on_" + id).style.display="none";
		}
	}
	
	function Creation_Based_On(row_num) {
    // Déterminer si la page parent contient REL_BE_1_Form dans son URL
    const init_path = window.parent.location.href;
    const isRootREL_BE_1 = init_path.includes("REL_BE_1_Form");

    if (!isRootREL_BE_1) return;

    const tableExt = document.getElementById("t01");
    const trList = tableExt.getElementsByTagName("tr");

    // Ajuster l'indice de la ligne
    const rowIndex = row_num - 1;
    const selectedRow = trList[rowIndex];

    // Fonction utilitaire pour mettre à jour un champ
	const updateField = (id, value) => {
		const element = window.parent.document.getElementById(id);
		if (element) {
			if (element.type === "checkbox") {
				element.checked = value === "X";
			} else {
				element.value = value;
				element.setAttribute("value", value); // Synchronise l'attribut
			}
		}
	};


    // Mettre à jour les champs du formulaire parent
    updateField("id_to_update", 0);
    updateField("Action", selectedRow.cells[2].textContent.trim());
    updateField("Reference", selectedRow.cells[3].textContent.trim());
    updateField("ref_rev", selectedRow.cells[4].textContent.trim());
	updateField("ID_ALETIQ", selectedRow.cells[5].textContent.trim());
	updateField("prod_draw", selectedRow.cells[6].textContent.trim());
	updateField("prod_draw_rev", selectedRow.cells[7].textContent.trim());
	updateField("ref_title", selectedRow.cells[8].textContent.trim());
	updateField("Alias", selectedRow.cells[9].textContent.trim());
	updateField("cust_draw", selectedRow.cells[10].textContent.trim());
	updateField("cust_draw_rev", selectedRow.cells[11].textContent.trim());
	updateField("Doc_Type", selectedRow.cells[12].textContent.trim());
	updateField("inhouse_manuf", selectedRow.cells[13].textContent.trim());
	updateField("Material_Type", selectedRow.cells[14].textContent.trim());
	updateField("Inventory_Impact", selectedRow.cells[15].textContent.trim());
	updateField("Ex", selectedRow.cells[16].textContent.trim());
	updateField("Weight", selectedRow.cells[17].textContent.trim());
	updateField("Weight_Unit_ID", selectedRow.cells[18].textContent.trim());
	updateField("Plating_Surface_ID", selectedRow.cells[19].textContent.trim());
	updateField("Plating_Surface_Unit_ID", selectedRow.cells[20].textContent.trim());
	updateField("fxxx", selectedRow.cells[21].textContent.trim());
	updateField("ECCN_id", selectedRow.cells[22].textContent.trim());
	updateField("RDO_id", selectedRow.cells[23].textContent.trim());
	updateField("HTS_id", selectedRow.cells[24].textContent.trim());
	updateField("file_copied_from_id", selectedRow.cells[27].textContent.trim());


    // Mettre à jour les commentaires
    const comments = selectedRow.cells[24].textContent.trim();
    if (comments.includes("- Requestor Comments -")) {
        const [, requestorComments] = comments.split("- Requestor Comments -");
        updateField("requestor_comments", requestorComments.trim());
    } else {
        updateField("requestor_comments", comments);
    }

    // Mettre en surbrillance la ligne sélectionnée
    Array.from(trList).forEach((tr, index) => {
        tr.className = index === rowIndex ? "copied_line" : "unpicked_line";
    });

    // Appeler la fonction de mise à jour du bouton
    button_update();
	window.parent.load_preview();
}

	
	function data_to_form(obj,table)
	{
		//TRANSFERT DES INFO DE LA TABLE VERS LE FORMULAIRE


			// DETERMINATION DE LA PAGE PARENT DANS LAQUELLE CETTE PAGE EST APPELLEE
			let init_path=window.parent.location.href;
			let searched_REL_BE_1="REL_BE_1_Form";
			let searched_REL_BE_2="REL_BE_2_Form";
			let searched_REL_BE_3="REL_BE_3_Form";
			let root_REL_BE_1=init_path.includes(searched_REL_BE_1,0);
			let root_REL_BE_2=init_path.includes(searched_REL_BE_2,0);
			let root_REL_BE_3=init_path.includes(searched_REL_BE_3,0);
			// -----
			
			window.parent.document.getElementById("id_to_update").value=obj.cells[1].textContent.trim();
			window.parent.document.getElementById("Action").value=obj.cells[2].textContent.trim();
			window.parent.document.getElementById("Reference").value=obj.cells[3].textContent.trim();
			window.parent.document.getElementById("ref_rev").value=obj.cells[4].textContent.trim();
			window.parent.document.getElementById("ID_ALETIQ").value=obj.cells[5].textContent.trim();
			window.parent.document.getElementById("prod_draw").value = obj.cells[6].textContent.trim();
			window.parent.document.getElementById("prod_draw_rev").value = obj.cells[7].textContent.trim();
			window.parent.document.getElementById("ref_title").value = obj.cells[8].textContent.trim();
			window.parent.document.getElementById("Alias").value = obj.cells[9].textContent.trim();
			window.parent.document.getElementById("cust_draw").value = obj.cells[10].textContent.trim();
			window.parent.document.getElementById("cust_draw_rev").value = obj.cells[11].textContent.trim();
			window.parent.document.getElementById("Doc_Type").value = obj.cells[12].textContent.trim();

			if (obj.cells[12].textContent.trim()=="X")
			{
				window.parent.document.getElementById("inhouse_manuf").checked = true;
			} else {
				window.parent.document.getElementById("inhouse_manuf").checked = false;
			}
				
			window.parent.document.getElementById("inhouse_manuf").value = obj.cells[13].textContent.trim();
			window.parent.document.getElementById("Material_Type").value = obj.cells[14].textContent.trim();
			window.parent.document.getElementById("Inventory_Impact").value = obj.cells[15].textContent.trim();
			window.parent.document.getElementById("Ex").value = obj.cells[16].textContent.trim();
			window.parent.document.getElementById("Weight").value = obj.cells[17].textContent.trim();
			window.parent.document.getElementById("Weight_Unit_ID").value = obj.cells[18].textContent.trim();
			window.parent.document.getElementById("Plating_Surface_ID").value = obj.cells[19].textContent.trim();
			window.parent.document.getElementById("Plating_Surface_Unit_ID").value = obj.cells[20].textContent.trim();
			window.parent.document.getElementById("fxxx").value = obj.cells[21].textContent.trim();
			window.parent.document.getElementById("ECCN_id").value = obj.cells[22].textContent.trim();
			window.parent.document.getElementById("RDO_id").value = obj.cells[23].textContent.trim();
			window.parent.document.getElementById("HTS_id").value = obj.cells[24].textContent.trim();
			
			
			
			// SI APPEL DE LA PAGE REL_BE_FORM_1
			//   - CHARGEMENT DE L'APERCU DU PLAN 
			//   - AFFICHAGE DU BOUTON DE SUPPRESSION
			//
			// SI APPEL DE LA PAGE REL_BE_FORM_2
			//   - AFFICHAGE DU BOUTON DE MOFICATION
			
				// APERCU PLAN PDF
				if (root_REL_BE_1==true)
				{
					const pdf_area=window.parent.document.getElementById("pdf_visual");
					const visual=window.parent.document.getElementById("visu_drawing");
					let loaded_file=obj.cells[27].textContent.trim();

					
					
				// AFFICHAGE DU COMMENTAIRE
					if (obj.cells[25].textContent.trim().indexOf("- Requestor Comments -")>=0)
					{
						const req_comm_tmp=obj.cells[25].textContent.trim().split("- Requestor Comments -");
						window.parent.document.getElementById("requestor_comments").value=req_comm_tmp[1];
						} else {
								window.parent.document.getElementById("requestor_comments").value=obj.cells[25].textContent.trim();
						}
					// BOUTON DE SUPPRESSION//AFFICHAGE BOUTON SUPPRESSION
					window.parent.document.getElementById("delete_row_button").style.display="inline";
				
				} else if (root_REL_BE_2==true || root_REL_BE_3==true)
				{
					// AFFICHAGE DU COMMENTAIRE
					if (obj.cells[25].textContent.trim().indexOf("- Requestor Comments -")>=0)
					{
						const req_comm_tmp=obj.cells[25].textContent.trim().split("- Requestor Comments -");
						window.parent.document.getElementById("requestor_comments").value=req_comm_tmp[1];
					} else {
						window.parent.document.getElementById("requestor_comments").value=obj.cells[25].textContent.trim();
					}
					window.parent.document.getElementById("validate_new_row").style.display="inline";
					window.parent.document.querySelector('#td_detail').setAttribute('class','enabled');
				}
			// ------
		
		const elements = window.parent.document.getElementsByClassName("ligne-active");
		for (let el of elements) {
			el.classList.remove("ligne-active");
		}

		button_update();
		
		
		// MISE EN LUMIERE DE LA LIGNE SELECTIONNEE
		var tbl = obj.parentNode;
		var rows = document.getElementsByTagName('tr');
		for (var i=0;i<rows.length;i++)
		{
			if (rows[i]!=obj) 
			{
				rows[i].setAttribute('class',"unpicked_line");
			} else {
				rows[i].setAttribute('class',"picked_line");
			}
		}
		
	}
	
	function button_update()
	{
		//CHANGEMENT STYLE BOUTON
		if (window.parent.document.getElementById("id_to_update").value>0)
		{
			window.parent.document.querySelector('#validate_new_row').value="UPDATE";
			window.parent.document.querySelector('#validate_new_row').setAttribute('class', 'btn orange');
			window.parent.document.querySelector('#validate_new_row').setAttribute('Title','Modify the picked row/reference with the data you just changed.');

		} else {
			
			window.parent.document.querySelector('#validate_new_row').value="ADD REF/DRAWING";
			window.parent.document.querySelector('#validate_new_row').setAttribute('class', 'btn blue2');
			
			window.parent.document.getElementById("delete_row_button").style.display="none";
		}	
	}
	


</script>

</head>

<body>

<?php 

    if (isset($_GET['Ref_ID']) && (($_GET['Act'])=='Del')) //
    {
		$Ref_ID=$_GET['Ref_ID'];
		$sql_1 = 'DELETE FROM tbl_released_drawing WHERE ID like "'.$Ref_ID.'"';		

		include ('../REL_Connexion_DB.php');

		$resultat = $mysqli->query($sql_1);

		$sql_file='SELECT Drawing_Path FROM tbl_released_drawing WHERE ID like "'.$Ref_ID.'"';

		$result = $mysqli->query($sql_file);
		while ($row = $result->fetch_assoc())
		{
			$file_path_db=$row['Drawing_Path'];
		}

		$path_attachment="DRAWINGS\IN_PROCESS\\";
		if ($file_path_db!="")												// SUPPRESION DU FICHIER EXISTANT S'IL EXISTE
		{
			$file_to_delete=$path_attachment.$file_path_db;					// Chemin complet du fichier existant
			unlink($file_to_delete);										// Suppression
		}

		mysqli_close($mysqli);

		$ref_link='REL_Package_Content.php?Rel_Pack_Num='.$_GET['Rel_Pack_Num'];
	}
        
?>



    
    <form enctype="multipart/form-data" action="" method="post">
    
    
    <?php
        include('../REL_Connexion_DB.php');
        
        $requete_pack = 'SELECT DISTINCT Rel_Pack_Num FROM tbl_released_package WHERE Rel_Pack_Num like "'.$_GET['Rel_Pack_Num'].'"';
        $resultat_Pack = $mysqli->query($requete_pack);
        $pack_exist=mysqli_num_rows($resultat_Pack);
        
        $requete = 'SELECT * FROM tbl_released_drawing WHERE Rel_Pack_Num like "'.$_GET['Rel_Pack_Num'].'" ORDER BY Reference, Prod_Draw ASC';
        $resultat = $mysqli->query($requete);
	    $rowcount=mysqli_num_rows($resultat);

        if ($rowcount>0)
        {
            echo '
            <table id="t01">
				<th HIDDEN>o</th>
                <th>Action</th>
                <th colspan=2>Reference</th>
				<th style="display:none">ID_ALETIQ</th>
                <th colspan=2>Product. Drawing</th>
                <th>Title</th>
                <th>Alias</th>
                <th colspan=2>Cust. Drawing</th>
                <th colspan=2>Doc Type</th>
                <th>Mat Type</th>
                <th>Inventory Imp.</th>
                <th>Ex</th>
                <th colspan=2>Weight</th>
                <th colspan=2>Plat. Surface</th>
                <th>Material</th>
				<th>ECCN</th>
				<th>RDO</th>
				<th>HTS</th>
                <th>Comments</th>';
			
			$row_num=2;

            while ($row = $resultat->fetch_assoc())
            {

                echo '<tr title="Double-click to activate the modification" ondblclick="data_to_form(this,t01)" >';
				echo '<td HIDDEN ></td>';
               echo '<td HIDDEN><div id="Body" name="ID" style="">'.$row['ID'].'</div></td>';
                
				echo '<td><div id="Body" name="Action" style="width:4,5%;">'.$row['Action'].'</div></td>';
                        
                echo '<td><div id="Body" name="Reference" style="width:7,5;">'.$row['Reference'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Reference" size=17 name="Reference" style="font-size:11;" value="'.$row['Reference'].'"></div></td>';
				
				echo '<td><div id="Body" name="Ref_Rev" style="width:1,2%;">'.$row['Ref_Rev'].'</div></td>';

				echo '<td style="display:none"><div id="Body" name="Id_ALETIQ" style="width:1,2%;">'.$row['ID_ALETIQ'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Ref_Rev" name="Ref_Rev" style="font-size:11;width:25px" value="'.$row['Ref_Rev'].'"></div></td>';

				echo '<td><div id="Body" name="Prod_Draw" style="width:7,5%;">';
				if ($row['Prod_Draw'] != "" && $row['Prod_Draw_Rev'] != "") {
					echo '<a href="https://app.aletiq.com/parts/preview/id/' . $row['Prod_Draw'] . '/revision/' . $row['Prod_Draw_Rev'] . '" target="_blank">' . $row['Prod_Draw'] . '</a>';
				} else {
					echo $row['Prod_Draw'];
				}
				echo '</td>';

				echo '<td><div id="Body" name="Prod_Draw_Rev" style="width:1,2%;">'.$row['Prod_Draw_Rev'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Prod_Draw_Rev" name="Prod_Draw_Rev" style="font-size:11;width:25" value="'.$row['Prod_Draw_Rev'].'"></div></td>';
				
				echo '<td><div id="Body" name="Ref_Title" style="width:14,9%;">'.$row['Ref_Title'].'</div></td>';
				//echo '<td><div id="Body"> <input type="text" id="Ref_Title" name="Ref_Title" style="font-size:11;width:185" value="'.$row['Ref_Title'].'"></div></td>';
				
				echo '<td><div id="Body" name="Alias" style="width:9,3%;">'.$row['Alias'].'</div></td>';
				//echo '<td><div id="Body"> <input type="text" id="Alias" name="Alias" style="font-size:11;width:100px" value="'.$row['Alias'].'"></div></td>';
				
				echo '<td><div id="Body" name="Cust_Drawing" style="width:5,3%;">'.$row['Cust_Drawing'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Cust_Drawing" size=18 name="Cust_Drawing" style="font-size:11;" value="'.$row['Cust_Drawing'].'"></div></td>';
				
				echo '<td><div id="Body" name="Cust_Drawing_Rev" style="width:1,1%;">'.$row['Cust_Drawing_Rev'].'</div></td>';
				//echo '<td><div id="Body"><input type="text" id="Cust_Drawing_Rev" name="Cust_Drawing_Rev" style="font-size:11;width:25" value="'.$row['Cust_Drawing_Rev'].'"></div></td>';

				echo '<td><div id="Body" name="Doc_Type" style="width:3,3%;">'.$row['Doc_Type'].'</div></td>';
				if ($row['Internal_Mach_Rec']==true)
				{
					echo '<td><div id="Body" name="inhouse_manuf" title="Pick that option if the part is critical and internal machinning expertise preferred. &#013Could be applicable to glass-to-metal seal bodies, Silver-plated PEEK insulators, LSR Parts etc...">
							<FONT style="font-size:1px;color:white">X</FONT>
						<img style="margin-left:-5px;" src="\Common_Resources\logo_scm_tron.png" title="In house manufacturing preferred" height="15">
						 </div></td>';
				} else {
					//echo '<td><div id="Body"><input type="checkbox" id="inhouse_manuf" name="inhouse_manuf"  title="Pick that option if the part is critical and internal machinning expertise preferred. &#013 Could be applicable to glass-to-metal seal bodies, PEEK insulator etc..." onclick="return false;"></div></td>';
					echo '<td><div id="Body" name="inhouse_manuf" title="Pick that option if the part is critical and internal machinning expertise preferred. &#013Could be applicable to glass-to-metal seal bodies, Silver-plated PEEK insulators, LSR Parts etc..."></div></td>';
				
				}

				echo '<td><div id="Body" name="Material_Type" style="">'.$row['Material_Type'].'</div></td>';

				echo '<td><div id="Body" name="Inventory_Impact" style="">'.$row['Inventory_Impact'].'</div></td>';
				
				if ($row['Ex']!="NO")
				{
					$col="red";
				} else {
					$col="black";
				}
				echo '<td><div id="Body" name="Ex" style="color:'.$col.'">'.$row['Ex'].'</div></td>';


				echo '<td>
						<div id="Body" name="Weight" style="">'.$row['Weight'].'</div>
					  </td>
					  <td>
						 <div id="Body" name="Weight_Unit" style="">'.$row['Weight_Unit'].'</div>
					  </td>';

				echo '<td style="text-align:center">
						<div id="Body" name="Plating_Surface" style="">'.$row['Plating_Surface'].'</div>
					</td>
					<td>
						<div id="Body" name="Plating_Surface_Unit" style="">'.$row['Plating_Surface_Unit'].'</div>
					</td>';
				

				echo '
				<td>
					<div id="Body" style="width:3,6%">'.$row['FXXX'].'</div>
				</td>
				<td>
					<div id="Body" style="">'.$row['ECCN'].'</div>
				</td>
				<td>
					<div id="Body" style="">'.$row['RDO'].'</div>
				</td>
				<td>
					<div id="Body" style="">'.$row['HTS'].'</div>
				</td>';

				echo '<td>';
				
				
				
				if (stripos($row['Requestor_Comments'],"Verif BE :")>0)
				{
					$P2_comment_str=explode("Verif BE :", $row['Requestor_Comments']);
					$P2_comment=trim(htmlspecialchars_decode(nl2br($P2_comment_str[0]), ENT_QUOTES));
					$P1_comment=trim(htmlspecialchars_decode(nl2br($P2_comment_str[0]), ENT_QUOTES));
				} else {
					$P2_comment="";
					$P1_comment=trim(htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES));
				}
				
				
				$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");
				$nmax = 0;   				//$nmax = 30;
				if ((strlen($row['Requestor_Comments']) > $nmax)) {
					echo '<div class="dropdown">';
					echo '<span>
							 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
						  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . $P1_comment . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
				}
				
				

				$nbre_lignes = substr_count(nl2br($P2_comment), "\n");
				$nmax = 0;   				//$nmax = 30;
				if ((strlen($P2_comment) > $nmax)) {
					echo ' | ';
					echo htmlspecialchars(substr(nl2br($P2_comment), 0, $nmax), ENT_QUOTES);
						echo '<div class="dropdown">';
						echo '<span>
								<img src="\Common_Resources\general_comment_icon_r.png" style="height:15px; opacity:1" >
							  </span>';
						echo '<div class="dropdown-content">';
						echo '<p><b>- <u>Verif BE Comments</u> -</b><br \>' . $P2_comment . '</p>';
						echo '</div>';
						echo '</div>';
				} else {

				}	
				
				echo '
				</td>';
				
				echo '<td HIDDEN></td>';
				$row_num=$row_num+1;
				
				echo '
				<td HIDDEN>
					<div id="Body" id="drawing_fullname" name="drawing_fullname" style="">'.$row['Drawing_Path'].'</div>
				</td>

				</tr>';
				
				echo '<script type="text/javascript">root_test('.$row['ID'].')</script>';
				
			}
            
			mysqli_close($mysqli);
            echo '</table>';
        } elseif ($rowcount==0 && $pack_exist==1) {
            echo '<div id="empty">The package is emtpy...</div>';
        } else {
            echo '<div id="empty">The package does not exist...</div>';
        }
		

    ?>
    <!------------------------------>
    
    

    </form>
    



</body> 
</html>