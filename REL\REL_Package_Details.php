

<?php 
if (isset($_POST['update_package_details']))
    {

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        //print_r($_POST);
        //Preparation des variables a utiliser pour la requete de mise a jour
        $pack=$_GET['ID'];
        $rel_pack_owner = $_POST['Rel_Pack_Owner'];
        $activity=$_POST['Activity'];
        $ex_package = $_POST['Ex_Package'];
        $dmo = $_POST['DMO'];
        $project_package = $_POST['Project_Package'];
        $observations = $_POST['Observations'];
        $res_date = $_POST['Reservation_Date'];


        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
            $sql_1 = 'UPDATE tbl_released_package SET
                        Activity="'.$activity.'",
                        Ex="'.$ex_package.'",
                        DMO="'.$dmo.'",
                        Project="'.$project_package.'",
                        Observations="'.$observations.'"
                WHERE Rel_Pack_Num ="'.$pack.
                '";';

        // On lance la requete
        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);
       
    }

    ?>



<table id="t02" border=0 style="border-bottom:0.5px black solid;border-top:0.5px black solid;background-color:#EFF1F2">

        <?php
            include('../REL_Connexion_DB.php');
            $requete = 'SELECT * FROM tbl_released_package WHERE Rel_Pack_Num like "'.$_GET['ID'].'"';
            $resultat = $mysqli->query($requete);
            while ($row = $resultat->fetch_assoc())
            {
                echo '
                    <tr>
                    <td>
                    <div id="Body">
                            Engineering Owner:
                        </div> 
                    </td>
                    </tr>
                    <tr>
                    <td>
                        <div id="InpBox">
                            <input style="border:transparent;background:transparent;border:none;width:80px;font-size:8pt; type="text" name="Rel_Pack_Owner" value ="'.$row['Rel_Pack_Owner'].'"readonly>';
                        // SI MODIFICATION DU NOM A IMPLEMENTER, DECOMMENTER LA SECTION CI-DESSOUS ET COMMENTER LA LIGNE AU DESSUS
                        //--------------------------------------------------------------------------------------------------------
                        // <select name="Package_owner_fullname" type="submit" title="" style="width:120px;font-size:11">
                        // <option value=""></option>';  
                        // $mysqli_1 = new mysqli('localhost', 'root', '', 'db_release');
                        // $mysqli_1->set_charset("utf8");
                        // $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Method' ORDER BY Fullname ASC;";
                        // $resultat = $mysqli_1->query($requete);
                        // while ($ligne = $resultat->fetch_assoc())
                        // {
                        //     if ($ligne['Fullname']==$row['Rel_Pack_Owner'])
                        //     {
                        //         echo'<option value ="'.$ligne['Fullname'].'" Selected>'.$row['Rel_Pack_Owner'].'</option><br/>';
                        //         $in='1';
                        //     } else {
                        //             echo'<option value ="'.$ligne['Fullname'].'">'.$ligne['Fullname'].'</option><br/>';
                        //             }
                        // }
                        // if ($in=='0')
                        // {
                        //     echo'<option value ="'.$row['Rel_Pack_Owner'].'" Selected>'.$row['Rel_Pack_Owner'].'</option><br/>';
                        // }
                        // mysqli_close($mysqli_1);
                        // echo '</select>';
                        //----------------------------------------------------------------------------------------------------------
                    echo '
                        </div>
                        </td> 
                    </tr>
                    <tr">
                    <td>
                        <div id="Body">
                            Activity:
                        </div> 
                        </td>
                    </tr>

                    <tr>
                    <td>
                        <div id="InpBox">
                        <select name="Activity" type="submit" title="" style="width:100px;font-size:11">
                        <option value=""></option>';
                        $requete = "SELECT DISTINCT Activity FROM tbl_activity ORDER BY Activity ASC;";
                        $resultat = $mysqli_1->query($requete);
                        while ($ligne = $resultat->fetch_assoc())
                        {
                            if ($ligne['Activity']==$row['Activity'])
                            {
                                echo'<option value ="'.$ligne['Activity'].'" Selected>'.$row['Activity'].'</option><br/>';
                                $in='1';
                            } else {
                                    echo'<option value ="'.$ligne['Activity'].'">'.$ligne['Activity'].'</option><br/>';
                                    }
                        }
                        if ($in=='0')
                        {
                            echo'<option value ="'.$row['Activity'].'" Selected>'.$row['Activity'].'</option><br/>';
                        }
                        echo '</select>';
                    echo '
                        </div> 
                        </td>
                    </tr>
                   
                    <tr>
                    <td>
                        <div id="Body">
                            DMO:
                        </div> 
                        </td>
                    </tr>
                    <tr>
                    <td>
                        <div id="InpBox">
                        <select name="DMO" type="submit" title="" style="width:85px;font-size:11">
                        <option value=""></option>';  
                        include('../DMO_Connexion_DB.php');
                        $requete = "SELECT DISTINCT DMO FROM tbl_dmo ORDER BY DMO DESC;";
                        $resultat = $mysqli_dmo->query($requete);
                        while ($ligne = $resultat->fetch_assoc())
                        {
                            if ($ligne['DMO']==$row['DMO'])
                            {
                                echo'<option value ="'.$ligne['DMO'].'" Selected>'.$row['DMO'].'</option><br/>';
                                $in='1';
                            } else {
                                    echo'<option value ="'.$ligne['DMO'].'">'.$ligne['DMO'].'</option><br/>';
                                    }
                        }
                        if ($in=='0')
                        {
                            echo'<option value ="'.$row['DMO'].'" Selected>'.$row['DMO'].'</option><br/>';
                        }
                        mysqli_close($mysqli_dmo);
                        echo '</select></div>';
                    echo '
                    </td>
                    </tr>

                        <tr>
                            <td>
                                <div id="Body">
                                    Project:
                                </div> 
                            </td>
                        </tr>
                        <tr>
                        <td>
                            <div id="InpBox">
                            <select name="Project_Package" type="submit" title="" style="width:70px;font-size:11">'; 
                            include('../SCM_Connexion_DB.php');
                            $requete = "SELECT DISTINCT OTP FROM tbl_project ORDER BY OTP DESC;";
                            $resultat = $mysqli_scm->query($requete);
                            while ($ligne = $resultat->fetch_assoc())
                            {
                                if ($ligne['OTP']==$row['Project'])
                                {
                                    echo'<option value ="'.$ligne['OTP'].'" Selected>'.$row['Project'].'</option><br/>';
                                    $in='1';
                                } else {
                                        echo'<option value ="'.$ligne['OTP'].'">'.$ligne['OTP'].'</option><br/>';
                                        }
                            }
                            if ($in=='0')
                            {
                                echo'<option value ="'.$row['Project'].'" Selected>'.$row['Project'].'</option><br/>';
                            }
                            mysqli_close($mysqli_scm);
                            echo '</select>';
                        echo '
                            </div> 
                        </td>
                        </tr>

                        <tr>
                        <td>
                            <div id="Body">
                                Ex:
                            </div> 
                            </td>
                    </tr>
                    <tr>
                        <td>
                            <div id="InpBox">
                            <select name="Ex_Package" type="submit" title="" style="width:60px;font-size:11">
                            <option value=""></option>';  
include ('../REL_Connexion_DB.php');
                            $requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex ASC;";
                            $resultat = $mysqli_1->query($requete);
                            while ($ligne = $resultat->fetch_assoc())
                            {
                                if ($ligne['Ex']==$row['Ex'])
                                {
                                    echo'<option value ="'.$ligne['Ex'].'" Selected>'.$row['Ex'].'</option><br/>';
                                    $in='1';
                                } else {
                                        echo'<option value ="'.$ligne['Ex'].'">'.$ligne['Ex'].'</option><br/>';
                                        }
                            }
                            if ($in=='0')
                            {
                                echo'<option value ="'.$row['Ex'].'" Selected>'.$row['Ex'].'</option><br/>';
                            }
                            mysqli_close($mysqli_1);
                        echo '
                        </select></div>
                        </td> 
                    </tr>


                   <tr>
                        <td>
                            <div id="Body">
                                Reserv. Date:
                            </div> 
                            </td>
                    </tr>
                    <tr>
                        <td>
                            <div id="InpBox">
                                <input style="border:transparent;background:transparent;border:none;width:80px;font-size:8pt;" type="text" name="Reservation_Date" value ="'.$row['Reservation_Date'].'"readonly>
                            </div> 
                            </td>
                    </tr>

                    <tr>
                        <td>
                        <div id="Body">
                            Observation(s):
                        </div> 
                        </td>
                    </tr>
                    <tr>
                        <td>
                        <div id="InpBox">
                            <textarea rows="8" cols="23" style="border:0.5 solid black;font-size:8pt;font-family:Tahoma, sans-serif;" type="text" name="Observations" >'.$row['Observations'].'</textarea>
                        </div> 
                        </td>
                    </tr>
                    <tr rowspan=2>
                        <td>
                        <div id="InpBox">
                            <button type="submit" class="btn grey" style="text-align:center; font-size:10; vertical-align:middle;width:120px;height:25px;" name="update_package_details" title="Update the package with the left-hand side info"/>Update Package Info</button>
                        </div>
                        </td>
                    </tr>

                    
                    '; 
            }
            mysqli_close($mysqli);
        ?>

</table>
