

<?php 

if (isset($_POST['update_package_details']))
    {

        //Connexion à BD
        include ('../REL_Connexion_DB.php');

        //print_r($_POST);
        //Preparation des variables a utiliser pour la requete de mise a jour
        $pack=$_GET['ID'];
        $rel_pack_owner = $_POST['Rel_Pack_Owner'];
        $activity=$_POST['Activity'];
        $ex_package = $_POST['Ex_Package'];
        $dmo = $_POST['DMO'];
        $project_package = $_POST['Project_Package'];
        $observations = $_POST['Observations'];
        $res_date = $_POST['Reservation_Date'];


        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
            $sql_1 = 'UPDATE tbl_released_package SET
                        Activity="'.$activity.'",
                        Ex="'.$ex_package.'",
                        DMO="'.$dmo.'",
                        Project="'.$project_package.'",
                        Observations="'.$observations.'"
                WHERE Rel_Pack_Num ="'.$pack.
                '";';

        // On lance la requete
        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);
        
    }


    include('../REL_Connexion_DB.php');
        $requete = 'SELECT * FROM tbl_released_package WHERE Rel_Pack_Num like "'.$_GET['ID'].'"';
        $resultat = $mysqli->query($requete);
        $pack_exist=mysqli_num_rows($resultat);    
        


            while ($row = $resultat->fetch_assoc())
            {

                echo '<table id="t02" border=0">
                        <tr>
                            <td>
                                <div id="Body">
                                    Engineering Owner:
                                </div> 
                                <div id="InpBox">
                                    <input style="border:transparent;background:transparent;border:none;width:80px;font-size:8pt; type="text" id="Rel_Pack_Owner_ID" name="Rel_Pack_Owner" value ="'.$row['Rel_Pack_Owner'].'"readonly>
                                </div>
                            </td>';

							if (stristr($_SERVER['PHP_SELF'], 'REL_BE_2')==='REL_BE_2_Form.php')
							{
				echo '		<td>
                                <div id="Body">
                                    Verification by:
                                </div> 
                                <div id="InpBox">
                                    <input style="border:transparent;background:transparent;border:none;font-size:8pt;" type="text" name="Creation_VISA" value ="'.$row['Verif_Req_Owner'].'"readonly>
                                </div> 
                            </td>
							<td>
                                <div id="Body">
                                    Reserv. Date:
                                </div> 

                                <div id="InpBox">
                                    <input style="border:transparent;background:transparent;border:none;font-size:8pt;width:100px;" type="text" name="Reservation_Date" value ="'.$row['Reservation_Date'].'"readonly>
                                </div> 
                            </td>
							';	
							}
							if (stristr($_SERVER['PHP_SELF'], 'REL_BE_3')==='REL_BE_3_Form.php')
							{
				echo '		<td>
                                <div id="Body">
                                    Verification by:
                                </div> 
                                <div id="InpBox">
                                    <input style="border:transparent;background:transparent;border:none;font-size:8pt;" type="text" name="Creation_VISA" value ="'.$row['VISA_BE_2'].'"readonly>
                                </div> 
                            </td><td>
                                <div id="Body">
                                    Creation Date:
                                </div> 

                                <div id="InpBox">
                                    <input style="border:transparent;background:transparent;border:none;font-size:8pt;" type="text" name="Reservation_Date" value ="'.$row['Creation_Date'].'"readonly>
                                </div> 
                            </td>';	
							}
                      

                echo '      

                            <td>
                                <div id="Body">
                                    Release Date:
                                </div> 
                                <div id="InpBox">
                                    <input style="border:transparent;background:transparent;border:none;font-size:8pt;width:100px;" type="text" name="Creation_Date" value ="'.$row['Creation_Date'].'"readonly>
                                </div> 
                            </td>

                            <td rowspan=2 style="vertical-align:top;min-width:250px;">
                                <div id="Body">
                                    Observation(s):<br>
       
                                    <textarea rows="5" style="width:100%;border:0.5 solid black;font-size:8pt;font-family:Tahoma, sans-serif;text-align:left;" type="text" name="Observations" >'.$row['Observations'].'</textarea>
                                </div> 
                            </td>
                            <td rowspan=2 style="text-align:center;padding-top:15px">
                                <div id="InpBox">
                                    <button type="submit" class="btn grey" style="text-align:center; font-size:10; vertical-align:bottom;width:80px;height:55px;" name="update_package_details" title="Update the package with the left-hand side info"/>Update <br>Package<br>Info</button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <div id="Body">
                                    Activity:
                                </div> 
                                <div id="InpBox">
                                    <select name="Activity" type="submit" title="" style="width:100px;font-size:11">
                                    <option value=""></option>';
                                    $requete = "SELECT DISTINCT Activity FROM tbl_activity ORDER BY Activity ASC;";
                                    $resultat = $mysqli->query($requete);
									$in='0';
                                    while ($ligne = $resultat->fetch_assoc())
                                    {
                                        if ($ligne['Activity']==$row['Activity'])
                                        {
                                            echo'<option value ="'.$ligne['Activity'].'" Selected>'.$row['Activity'].'</option><br/>';
                                            $in='1';
                                        } else {
                                                echo'<option value ="'.$ligne['Activity'].'">'.$ligne['Activity'].'</option><br/>';
                                                }
                                    }
                                    if ($in=='0' && $row['Activity']!="")
                                    {
                                        echo'<option value ="'.$row['Activity'].'" Selected>'.$row['Activity'].'</option><br/>';
                                    }
                                echo '</select>
                                </div> 
                            </td>
                            <td>
                                <div id="Body">
                                    DMO:
                                </div> 

                                <div id="InpBox">
                                    <select name="DMO" type="submit" title="" style="width:85px;font-size:11">
                                    <option value=""></option>';  
                                    include('../DMO_Connexion_DB.php');
                                    $requete = "SELECT DISTINCT DMO FROM tbl_dmo ORDER BY DMO DESC;";
                                    $resultat = $mysqli_dmo->query($requete);
									$in='0';
                                    while ($ligne = $resultat->fetch_assoc())
                                    {
                                        if ($ligne['DMO']==$row['DMO'])
                                        {
                                            echo'<option value ="'.$ligne['DMO'].'" Selected>'.$row['DMO'].'</option><br/>';
                                            $in='1';
                                        } else {
                                                echo'<option value ="'.$ligne['DMO'].'">'.$ligne['DMO'].'</option><br/>';
                                                }
                                    }
                                    mysqli_close($mysqli_dmo);
                                    echo '</select>';
									if ($in!='0'  && $row['DMO']!="")
                                    {
										$link='https://frscmbe.scmlemans.com/DMO/DMO_Modification_form.php?dmo='.$row['DMO'].'&modif=1';
										echo '&nbsp<a href="'.$link.'" class="btn grey2" style="border-radius:3px;border:0.5px solid grey;width:15px;height:8px; vertical-align:middle; text-align:center"  target="_blank" title="go to DMO detail">&#9620</a></div>';
                                    }
                       echo '</td>
                            <td>
                                <div id="Body">
                                    Project:
                                </div> 

                                <div id="InpBox">
                                <select name="Project_Package" type="submit" title="" style="width:70px;font-size:11">'; 
                                include('../SCM_Connexion_DB.php');
                                $requete = "SELECT DISTINCT OTP FROM tbl_project ORDER BY OTP DESC;";
                                $resultat = $mysqli_scm->query($requete);
								$in='0';
                                while ($ligne = $resultat->fetch_assoc())
                                {
                                    if ($ligne['OTP']==$row['Project'])
                                    {
                                        echo'<option value ="'.$ligne['OTP'].'" Selected>'.$row['Project'].'</option><br/>';
                                        $in='1';
                                    } else {
                                            echo'<option value ="'.$ligne['OTP'].'">'.$ligne['OTP'].'</option><br/>';
                                            }
                                }
                                if ($in=='0' && $row['Project']!="")
                                {
                                    echo'<option value ="'.$row['Project'].'" Selected>'.$row['Project'].'</option><br/>';
                                }
                                mysqli_close($mysqli_scm);
                                echo '</select>';
                            echo '
                                </div> 
                            </td>

                            <td>
                                <div id="Body">
                                    Ex:
                                </div> 

                                <div id="InpBox">
                                <select name="Ex_Package" type="submit" title="" style="width:60px;font-size:11">
                                <option value=""></option>';  
                                include('../SCM_Connexion_DB.php');
                                $requete = "SELECT DISTINCT Ex FROM tbl_ex ORDER BY Ex ASC;";
                                $resultat = $mysqli_scm->query($requete);
								$in='0';
                                while ($ligne = $resultat->fetch_assoc())
                                {
                                    if ($ligne['Ex']==$row['Ex'])
                                    {
                                        echo'<option value ="'.$ligne['Ex'].'" Selected>'.$row['Ex'].'</option><br/>';
                                        $in='1';
                                    } else {
                                            echo'<option value ="'.$ligne['Ex'].'">'.$ligne['Ex'].'</option><br/>';
                                            }
                                }
                                if ($in=='0' && $row['Ex']!="")
                                {
                                    echo'<option value ="'.$row['Ex'].'" Selected>'.$row['Ex'].'</option><br/>';
                                }
                                mysqli_close($mysqli_scm);
                            echo '
                            </select></div>
                            </td> 
                            </tr> ';
					echo '</table>';
                            
                            // SI MODIFICATION DU NOM A IMPLEMENTER, DECOMMENTER LA SECTION CI-DESSOUS ET COMMENTER LA LIGNE AU DESSUS
                            //--------------------------------------------------------------------------------------------------------
                            // <select name="Package_owner_fullname" type="submit" title="" style="width:120px;font-size:11">
                            // <option value=""></option>';  
                            // $mysqli_1 = new mysqli('localhost', 'root', '', 'db_release');
                            // $mysqli_1->set_charset("utf8");
                            // $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Method' ORDER BY Fullname ASC;";
                            // $resultat = $mysqli_1->query($requete);
                            // while ($ligne = $resultat->fetch_assoc())
                            // {
                            //     if ($ligne['Fullname']==$row['Rel_Pack_Owner'])
                            //     {
                            //         echo'<option value ="'.$ligne['Fullname'].'" Selected>'.$row['Rel_Pack_Owner'].'</option><br/>';
                            //         $in='1';
                            //     } else {
                            //             echo'<option value ="'.$ligne['Fullname'].'">'.$ligne['Fullname'].'</option><br/>';
                            //             }
                            // }
                            // if ($in=='0')
                            // {
                            //     echo'<option value ="'.$row['Rel_Pack_Owner'].'" Selected>'.$row['Rel_Pack_Owner'].'</option><br/>';
                            // }
                            // mysqli_close($mysqli_1);
                            // echo '</select>';
                            //----------------------------------------------------------------------------------------------------------
                        
            }
        mysqli_close($mysqli);
        
        ?>
