<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <link rel="stylesheet" type="text/css" href="REL_KPI_Form_styles.css">
	

	
</head>
<body>
	<form method="post">
    <?php
	
	include('../REL_Connexion_DB.php');
	
	
    // CREATION DE LA REQUETE DEFINISSANT LE TYPE DE DIFF POUR CHACUNE D'ENTRE ELLES
	// ----------------	
	$requete='
			SELECT 
				count(*) as "Total", 
				tbl_released_drawing.rel_pack_num as "REL", 
				Project,
				count( case when Doc_Type="ASSY" AND Inventory_Impact not like "NO IMPACT" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 END) as "ASSY_IMPACT_PROJET",
				count( case when Doc_Type="ASSY" AND Inventory_Impact="NO IMPACT" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 END) as "ASSY_NO_IMPACT_PROJET",
				count( case when Doc_Type="ASSY" AND Inventory_Impact not like "NO IMPACT" AND (Project like "STAND" OR (Project not like "STAND" AND tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 END) as "ASSY_IMPACT_NO_PROJET",
				count( case when Doc_Type="ASSY" AND Inventory_Impact="NO IMPACT" AND (Project like "STAND" OR (Project not like "STAND" AND tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 END) as "ASSY_NO_IMPACT_NO_PROJET",
				count( case when Doc_Type="MACH" AND Inventory_Impact not like "NO IMPACT" THEN 1 END) as "MACH_IMPACT",
				count( case when Doc_Type="MACH" AND Inventory_Impact="NO IMPACT" THEN 1 END) as "MACH_NO_IMPACT",
				count( case when Doc_Type="MOLD" AND Inventory_Impact not like "NO IMPACT" THEN 1 END) as "MOLD_IMPACT",
				count( case when Doc_Type="MOLD" AND Inventory_Impact="NO IMPACT" THEN 1 END) as "MOLD_NO_IMPACT",
				count( case when Doc_Type="PUR" AND Inventory_Impact="NO IMPACT" AND (Proc_Type="" OR Proc_Type="F30") THEN 1 END) as "PUR_NO_IMPACT_F30",
				count( case when Doc_Type="PUR" AND Inventory_Impact NOT LIKE "NO IMPACT" AND (Proc_Type="" OR Proc_Type="F30") THEN 1 END) as "PUR_IMPACT_F30",
				count( case when Doc_Type="PUR" AND Inventory_Impact="NO IMPACT" AND Proc_Type="F" THEN 1 END) as "PUR_NO_IMPACT_F",
				count( case when Doc_Type="PUR" AND Inventory_Impact NOT LIKE "NO IMPACT" AND Proc_Type="F" THEN 1 END) as "PUR_IMPACT_F",
				count( case when Doc_Type="DOC" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 END) as "DOC_PROJET",
				count( case when Doc_Type="DOC" AND Project like "STAND" OR (Doc_Type="DOC" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 END) as "DOC_NO_PROJET",
				count( case when Doc_Type="" THEN 1 END) as "NA",
				Creation_Date
			FROM tbl_released_drawing
			LEFT JOIN tbl_released_package on tbl_released_package.rel_pack_num=tbl_released_drawing.rel_pack_num 
			WHERE Creation_VISA not like "" 
			GROUP BY tbl_released_drawing.rel_pack_num
			ORDER BY tbl_released_drawing.rel_pack_num desc
			';
	$requete_BIS_A_VALIDER='
	SELECT 
		count(*) as "Total", 
		tbl_released_drawing.rel_pack_num as "REL", 
		Project,
		sum( 
			( case when Doc_Type="ASSY" AND Inventory_Impact not like "NO IMPACT" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 ELSE 0 END)*14 +
			( case when Doc_Type="ASSY" AND Inventory_Impact="NO IMPACT" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 ELSE 0 END)*13 +
			( case when Doc_Type="ASSY" AND Inventory_Impact not like "NO IMPACT" AND (Project like "STAND" OR (Project not like "STAND" AND tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 ELSE 0 END)*13 + 
			( case when Doc_Type="ASSY" AND Inventory_Impact="NO IMPACT" AND (Project like "STAND" OR (Project not like "STAND" AND tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 ELSE 0 END)*11
			) as "ASSY",
		sum( 
			(case when Doc_Type="MACH" AND Inventory_Impact not like "NO IMPACT" THEN 1 ELSE 0 END)*11 +
			(case when Doc_Type="MACH" AND Inventory_Impact="NO IMPACT" THEN 1  ELSE 0 END)*10
			) as "MACH",
		sum(
			( case when Doc_Type="MOLD" AND Inventory_Impact not like "NO IMPACT" THEN 1 ELSE 0 END)*11 +
			( case when Doc_Type="MOLD" AND Inventory_Impact="NO IMPACT" THEN 1 ELSE 0 END)*10
			) as "MOLD",
		sum(
			( case when Doc_Type="PUR" AND Inventory_Impact="NO IMPACT" AND (Proc_Type="" OR Proc_Type="F30") THEN 1 ELSE 0 END)*11 +
			( case when Doc_Type="PUR" AND Inventory_Impact NOT LIKE "NO IMPACT" AND (Proc_Type="" OR Proc_Type="F30") THEN 1 ELSE 0 END)*12 +
			( case when Doc_Type="PUR" AND Inventory_Impact="NO IMPACT" AND Proc_Type="F" THEN 1 ELSE 0 END)*10 +
			( case when Doc_Type="PUR" AND Inventory_Impact NOT LIKE "NO IMPACT" AND Proc_Type="F" THEN 1 ELSE 0 END)*9
		) as "PUR",
		sum(
			( case when Doc_Type="DOC" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw like "GA%" OR tbl_released_drawing.Prod_Draw like "FT%") THEN 1 ELSE 0 END)*11 +
			( case when Doc_Type="DOC" AND Project like "STAND" OR (Doc_Type="DOC" AND Project not like "STAND" AND (tbl_released_drawing.Prod_Draw not like "GA%" AND tbl_released_drawing.Prod_Draw not like "FT%")) THEN 1 ELSE 0 END)*11
		) as "DOC",
		count( case when Doc_Type="" THEN 1 END) as "NA",
		Creation_Date
	FROM tbl_released_drawing
	LEFT JOIN tbl_released_package on tbl_released_package.rel_pack_num=tbl_released_drawing.rel_pack_num 
	WHERE Creation_VISA not like ""
	GROUP BY tbl_released_drawing.rel_pack_num
	ORDER BY tbl_released_drawing.rel_pack_num desc';

	
	// CONSTRUCTION DE LA LISTE DES COLONNES TYPE 'VISA' EXISTANTES DANS LES TABLES TBL_RELEASED_DRAWING ET TBL_RELEASED_PACKAGE EN IGNORANT LES VISA NON CRITIQUES
	// ----------------
    $requete_col='
				SELECT COLUMN_NAME, TABLE_NAME 
				FROM INFORMATION_SCHEMA.COLUMNS 
				WHERE 
					COLUMN_NAME LIKE "%VISA%" AND 
					COLUMN_NAME NOT LIKE "VISA_LABO" AND
					COLUMN_NAME NOT LIKE "VISA_PUR_4" AND
					COLUMN_NAME NOT LIKE "VISA_PUR_5" AND
					COLUMN_NAME NOT LIKE "VISA_Q_PROD" AND
					COLUMN_NAME NOT LIKE "VISA_METHOD" AND
					COLUMN_NAME NOT LIKE "Creation_VISA" AND
					(TABLE_NAME like "tbl_released_drawing" or TABLE_NAME like "tbl_released_package")';
	
	$resultat_col = $mysqli->query($requete_col);
	while ($row_col = $resultat_col->fetch_assoc()) 
	{
		$col_name[] = array($row_col['COLUMN_NAME'],$row_col['TABLE_NAME']);
	}
	// ----------------
	
	// CONSTRUCTION DE LA REQUETE POUR LISTER TOUTES LES DIFF (REL_PACK_NUM) ET LEUR NOMBRE DE CHAMPS DE TYPE 'VISA' NON NULL
	// ----------------
	$k=0;
	$requete_check="";
	do {
		if ($requete_check=="")
		{
			$requete_check='SELECT tbl_released_drawing.rel_pack_num as "REL", SUM(';
		} else {
			$requete_check=$requete_check.' + ';
		}
		$requete_check=$requete_check.' (case when '.$col_name[$k][0].' not like "" THEN 1 ELSE 0 END) ';
		$k=$k+1;
	} while ($k < count($col_name));	
	$requete_check=$requete_check.') AS "VISA"
								FROM tbl_released_drawing 
								LEFT JOIN tbl_released_package ON tbl_released_drawing.rel_pack_num=tbl_released_package.rel_pack_num 
								GROUP BY tbl_released_drawing.rel_pack_num';
	// ----------------

	// EXECUTION DE LA REQUETE ET CONSTRUCTION DE LA LISTE DE RESULTATS DE TOUTES LES DIFF AVEC LEUR NOMBRE DE CHAMP VISA NON NULL
	// ----------------
	$resultat_check = $mysqli->query($requete_check);
	while ($row_check = $resultat_check->fetch_assoc()) 
	{
		$visa_in_rel[] = array($row_check['REL'],$row_check['VISA']);
	}
	// ----------------
	
	// CALCUL POUR CHAQUE 
	$resultat = $mysqli->query($requete);
    while ($row = $resultat->fetch_assoc()) 
	{

		// RECHERCHE DU NUMERO DE DIFF (REL_PACK_NUM) CORRESPONDANT A LA LIGNE EN COURS POUR RETROUVER LE NOMBRE DE VISA CALCULES DANS LA LISTE VISA_IN_REL
		// ----------------
		$visa=0;
		$k=0;
		do {
			if ($visa_in_rel[$k][0]!=$row['REL'])
			{
				$k=$k+1;
			} else {
				$visa=$visa_in_rel[$k][1];
				break;
			}
		} while ($k < count($visa_in_rel));
		// ----------------
		
		// CALCUL DU NOMBRE DE VISA ATTENDUS SUR LE CHEMIN CRITIQUE POUR CHAQUE CATEGORIE
		// ----------------
		$NEEDED_Total=0;
		$DOC_Total=$row['DOC_NO_PROJET']*10+$row['DOC_PROJET']*11;
		$PUR_Total=$row['PUR_NO_IMPACT_F30']*11+$row['PUR_IMPACT_F30']*12+$row['PUR_NO_IMPACT_F']*10+$row['PUR_IMPACT_F']*9; // +1 ajouté car la métro, bien que panier, est considérée en VISA critique pour les pieces achetées. Cela permet de faciliter le calcul (VISA_METRO etant critique pour tous les cas sauf dans le cas des articles F/F30)
		$MOLD_Total=$row['MOLD_NO_IMPACT']*10+$row['MOLD_IMPACT']*11;
		$MACH_Total=$row['MACH_NO_IMPACT']*10+$row['MACH_IMPACT']*11;
		$ASSY_Total=$row['ASSY_IMPACT_PROJET']*14+$row['ASSY_NO_IMPACT_PROJET']*13+$row['ASSY_IMPACT_NO_PROJET']*13+$row['ASSY_NO_IMPACT_NO_PROJET']*12;
		// ----------------
		
		// CALCUL DU POURCENTAGE DE PROGRESSION DE LA DIFF
		// ----------------
		$NEEDED_Total=$DOC_Total+$PUR_Total+$MOLD_Total+$MACH_Total+$ASSY_Total;
		$progress_percent="-";
		if($NEEDED_Total>0 && $NEEDED_Total>=$visa)
		{
			$progress_percent=round(100*$visa/$NEEDED_Total, 0, PHP_ROUND_HALF_DOWN);
			
		} else if ($NEEDED_Total<$visa && $NEEDED_Total>0)
		{
			$progress_percent=100;
		} else {
			$progress_percent=0;
		}
		// ----------------
		
		// CONSTRUCTION DE LA LISTE CONTENANT LES NUMEROS DE DIFF ET LEURS POURCENTAGES DE PROGRESSION ASSOCIES
		// ----------------
		if (isset($_POST['Sort_Progress_Btn']))
		{
			$diff_progress[] = array($progress_percent,$row['REL'],$row['Total'],$visa,$NEEDED_Total,$row['Project'],$row['Creation_Date']);
			
		} else if (isset($_POST['Sort_Creation_Date_Btn']))
		{
			$diff_progress[] = array($row['Creation_Date'], $progress_percent,$row['REL'],$row['Total'],$visa,$NEEDED_Total,$row['Project']);
		} 
		else if (isset($_POST['Sort_Rel_Btn']) || (isset($_POST['Sort_Rel_Btn'])==false && isset($_POST['Sort_Progress_Btn'])==false && isset($_POST['Sort_Creation_Date_Btn'])==false))
		{
			$diff_progress[] = array($row['REL'],$row['Total'],$visa,$NEEDED_Total,$row['Project'],$row['Creation_Date'],$progress_percent);
		}
		
		echo '</tr>';
	
    }
	
	
    mysqli_close($mysqli);
    ?>
	
	
	
	<?php
	if (isset($_POST['Sort_Progress_Btn']))
	{
		sort($diff_progress);
		$idx_list_progress_progress_percent=0;
		$idx_list_progress_rel_num=1;
		$idx_list_progress_total=2;
		$idx_list_progress_visa=3;
		$idx_list_progress_needed_total=4;
		$idx_list_progress_project=5;
		$idx_list_progress_creation_date=6;
		$stl_btn_sort_progress="background-color:gray";
		$stl_btn_sort_rel="background-color:white";
		$stl_btn_sort_creation_date="background-color:white";
	} else if (isset($_POST['Sort_Creation_Date_Btn']))
	{
		rsort($diff_progress);
		$idx_list_progress_progress_percent=1;
		$idx_list_progress_rel_num=2;
		$idx_list_progress_total=3;
		$idx_list_progress_visa=4;
		$idx_list_progress_needed_total=5;
		$idx_list_progress_project=6;
		$idx_list_progress_creation_date=0;
		$stl_btn_sort_progress="background-color:white";
		$stl_btn_sort_rel="background-color:white";
		$stl_btn_sort_creation_date="background-color:gray";
	} else if (isset($_POST['Sort_Rel_Btn']) || (isset($_POST['Sort_Rel_Btn'])==false && isset($_POST['Sort_Progress_Btn'])==false && isset($_POST['Sort_Creation_Date_Btn'])==false))
	{
		rsort($diff_progress);
		$idx_list_progress_progress_percent=6;
		$idx_list_progress_rel_num=0;
		$idx_list_progress_total=1;
		$idx_list_progress_visa=2;
		$idx_list_progress_needed_total=3;
		$idx_list_progress_project=4;
		$idx_list_progress_creation_date=5;
		$stl_btn_sort_progress="background-color:white";
		$stl_btn_sort_rel="background-color:gray";
		$stl_btn_sort_creation_date="background-color:white";
	}
	$stl_th='style="width:65px"';
	if (isset($_POST['Display_100']))
	{
		$ck="checked";
	} else {
		$ck="";
	}
	echo '<table style="width:100%;border-collapse:collapse;width:100%;text-align:center">';
	echo '<tr style="border-bottom:1px solid black">
			<th style="width:55px">
				Diff
				<span><input type="submit" name="Sort_Rel_Btn" value="q" style="font-family:\'Wingdings 3\';font-size:8pt;'.$stl_btn_sort_rel.';border-radius:4px;width:20px;height:15px;border:1px solid gray;cursor:pointer;text-align:center;text-indent:-2px;margin-top:-3px"/></span>
            </th>
			<th style="width:35px">
				# art.
            </th>
			<th style="width:65px">
				# visa
            </th>
			<th style="width:65px">
				Project
            </th>
			<th style="width:75px">
				Verif BE
				<span><input type="submit" name="Sort_Creation_Date_Btn" value="q" style="font-family:\'Wingdings 3\';font-size:8pt;'.$stl_btn_sort_creation_date.';border-radius:4px;width:20px;height:15px;border:1px solid gray;cursor:pointer;text-align:center;text-indent:-2px;margin-top:-3px"/></span>
            </th>
			<th colspan=2>
				Progress
				<span><input type="submit" name="Sort_Progress_Btn" value="p" style="font-family:\'Wingdings 3\';font-size:8pt;'.$stl_btn_sort_progress.';border-radius:4px;width:20px;height:15px;border:1px solid gray;cursor:pointer;text-align:center;text-indent:-2px;margin-top:-3px"/></span>
				<span style="margin-left:100px"><input type="checkbox" name="Display_100" value="" style="padding-top:-4px" onChange="this.form.submit()" '.$ck.' >Afficher tout</input></span>
			</th>
		</tr>';
		//<span><input type="submit" name="Sort_Progress_Btn" value="&#9947" style="background-color:white;border-radius:2px;width:20px;height:15px;border:1px solid gray;cursor:pointer"/></span>
	$i=1;
	$j=3;
	$total_article=0;
	$total_visa=0;
	$total_needed=0;
	foreach ($diff_progress as $diff_el)
	{
		
		if ($diff_el[$idx_list_progress_progress_percent]==100)
		{
			$progress_cl="#afca0b";
			if (isset($_POST['Display_100']))
			{
			} else {
				continue;
			}
		} else if ($diff_el[$idx_list_progress_progress_percent]>1){
			$progress_cl="#0089a8";
		} else {
			$progress_cl="red";
		}
		
		if(intval($i/$j)==($i/$j))
		{
			$stl="background-color:#E7E7E7;";
		} else {
			$stl="";
		}
		
		$total_article=$total_article+$diff_el[$idx_list_progress_total];
		$total_visa=$total_visa+$diff_el[$idx_list_progress_visa];
		$total_needed=$total_needed+$diff_el[$idx_list_progress_needed_total];
		
		echo '<tr>';
		echo '<td style="'.$stl.';border-radius:5px 0px 0px 5px">';
		echo '<a style="color:black" href="https://frscmbe.scmlemans.com/rel/REL_Pack_Overview.php?ID='.$diff_el[$idx_list_progress_rel_num].'">'.$diff_el[$idx_list_progress_rel_num].'</a>';
		echo '</td>';
		echo '<td style="'.$stl.'">';
		echo $diff_el[$idx_list_progress_total];
		echo '</td>';
		echo '<td style="'.$stl.'">';
		echo $diff_el[$idx_list_progress_visa] .' / '.$diff_el[$idx_list_progress_needed_total];
		echo '</td>';
		echo '<td style="'.$stl.'">';
		echo $diff_el[$idx_list_progress_project];
		echo '</td>';
		echo '<td style="'.$stl.';border-radius:0px 3px 3px 0px">';
		echo $diff_el[$idx_list_progress_creation_date];
		echo '</td>';
		echo '</td><td style="width:30px;border-radius:6px;background-color:'.$progress_cl.';color:white;font-weight:bold;border:1px solid white;">';
		echo $diff_el[$idx_list_progress_progress_percent];
		echo '</td>';
		echo '<td>';
		echo '<table style="border-collpase:collapse;width:calc('.$diff_el[$idx_list_progress_progress_percent].'%);height:17px;margin-top:-2px;margin-bottom:-2px"><tr><td style="background-color:'.$progress_cl.';border-radius:3px"></td><td style="width:20px;font-weight:bold;color:'.$progress_cl.'">'.$diff_el[$idx_list_progress_progress_percent].'</td></tr></table>';
		echo '</td>';
		echo '</tr>';
		$i=$i+1;
	}
	$stl_last='style="font-size:7pt;font-weight:bold;border-top:1px solid black;padding-top:3px;"';
	echo '<tr >';
	echo '<td '.$stl_last.'>';
	
	echo '</td>';
	echo '<td '.$stl_last.'>';
	echo $total_article;
	echo '</td>';
	echo '<td '.$stl_last.'>';
	echo $total_visa . ' / ' . $total_needed;
	echo '</td>';
	echo '<td '.$stl_last.'>';
	
	echo '</td>';
	echo '<td '.$stl_last.'>';
	
	echo '</td>';
	

	$percent_total=round(100*$total_visa/$total_needed, 0, PHP_ROUND_HALF_DOWN);
	echo '<td '.$stl_last.'>';
	echo $percent_total;
	echo '</td>';
	echo '<td '.$stl_last.'>';
	echo '<table style="border-collpase:collapse;width:calc('. $percent_total.'%);height:17px;margin-top:-2px;margin-bottom:-2px"><tr><td style=";border-radius:3px;background-color:lightgray"></td><td style="width:20px;font-weight:bold;font-size:7pt">'.$percent_total.'</td></tr></table>';
	echo '</td>';
	echo '</tr>';
	echo '</table>';
	
	?>
    
	</form>


</body>

</html>