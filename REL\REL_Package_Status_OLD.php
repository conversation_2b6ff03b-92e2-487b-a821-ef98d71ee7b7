<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_Package_Status_styles.css">
	<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


    <title>
        REL Pack - PACKAGE Review
    </title>



</head>

<body>


    <form enctype="multipart/form-data" action="" method="post">

        <table id="t01" border="0" style="vertical-align:top;">

            <tr>
                <td colspan="6" style="">
                    <div id="Title">
                        PACKAGE
                    </div>
                </td>
                <td>
                </td>
                <td>
                    <img src="\Common_Resources\scm_logo.png" height="45" width="100px">
                </td>
            </tr>

            <tr>
                <td style="width:20%;">
                    <div id="FilterTitle">
                        Package #
                        <!-- Titre -->
                    </div>

                    <!--- Récupération des données de la table tbl_released_drawing de "Rel_Pack_Num" dans la base de données pour le champ Pack-->
                    <div id="Filter">
                        <SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
                                            FROM tbl_released_drawing 
                                            WHERE tbl_released_drawing.VISA_GID like "" 
                                              AND tbl_released_drawing.Doc_Type like "%%" 
                                            ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '">' . $row['Rel_Pack_Num'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                        <!--</datalist>-->
                    </div>
                </td>
                <td style="width:20%;">
                    <div id="FilterTitle">
                        Project
                        <!---Titre --->
                    </div>

                    <!--- Récupération des données de la table tbl_released_package de "Project" dans la base de données pour le champ Project-->
                    <div id="Filter">
                        <SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_package.Project 
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                ORDER BY tbl_released_package.Project DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<OPTION value ="' . $row['Project'] . '">' . $row['Project'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td style="width:20%;">
                    <div id="FilterTitle">
                        Reference
                        <!-- Titre -->
                    </div>
                    <!--- Création d'une zone de texte -->
                    <div id="Filter">
                        <input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;">
                    </div>
                </td>
                <td style="width:20%;">
                    <div id="FilterTitle">
                        Ref_Rev
                        <!-- Titre -->
                    </div>
                    <!--- Création d'une zone de texte -->
                    <div id="Filter">
                        <input type="text" size=20 name="Ref_Rev" style="font-size:8pt;height:9pt;width:20pt;">
                    </div>
                </td>
                <td style="width:20%;">
                    <div id="FilterTitle">
                        Prod_Draw
                        <!---Titre --->
                    </div>

                    <!--- Récupération des données de la table tbl_released_drawing de "Prod_Draw" dans la base de données pour le champ Prod_Draw-->
                    <div id="Filter">
                        <SELECT name="Prod_Draw" type="submit" size="1" style="width:80px;font-size:9pt;height:17px">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Prod_Draw 
                                FROM tbl_released_drawing
                                ORDER BY Prod_Draw DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<OPTION value ="' . $row['Prod_Draw'] . '">' . $row['Prod_Draw'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td style="width:20%;">
                    <div id="FilterTitle">
                        Activity
                        <!---Titre --->
                    </div>

                    <!--- Récupération des données de la table tbl_released_drawing de "Prod_Draw" dans la base de données pour le champ Prod_Draw-->
                    <div id="Filter">
                        <SELECT name="Activity" type="submit" size="1" style="width:80px;font-size:9pt;height:17px">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_package.Activity 
                                FROM tbl_released_package
                                ORDER BY Activity DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<OPTION value ="' . $row['Activity'] . '">' . $row['Activity'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                </td>
                <td>
                    <!--- Les boutons en bout de ligne "Apply Filters" et "Reset" --->
                    <input onclick="" type="submit" value="Apply Filters" title="Apply the picked filters to the list below " Name="Filter" style="font-size:7pt;margin-bottom:3pt;margin-right:3pt" />
                    <br>
                    <input onclick="" type="submit" value="Reset" title="Reset all the previously-applied filters" Name="Reset_Filter" style="font-size:7pt;margin-bottom:3pt" />
                </td>
            </tr>

            <!--- Vérification des valeurs --->
            <?php

            if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
                $rel_pack_num_choice = "%";
            } else {
                $rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
            }

            if (isset($_POST['Project_Choice']) == false) {
                $project_choice = "%";
            } else {
                $project_choice = $_POST['Project_Choice'];
            }

            if (isset($_POST['Reference_Choice']) == false) {
                $reference_choice = "%";
            } else {
                if (strlen($_POST['Reference_Choice']) > 0) {
                    $reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
                } else {
                    $reference_choice = "%";
                }
            }

            if (isset($_POST['Ref_Rev']) == false) {
                $Ref_Rev = "%";
            } else {
                if (strlen($_POST['Ref_Rev']) > 0) {
                    $Ref_Rev = str_replace("*", "%", $_POST['Ref_Rev']);
                } else {
                    $Ref_Rev = "%";
                }
            }

            if (isset($_POST['Prod_Draw']) == false) {
                $Prod_Draw = "%";
            } else {
                $Prod_Draw = $_POST['Prod_Draw'];
            }

            if (isset($_POST['Activity']) == false) {
                $Activity = "%";
            } else {
                $Activity = $_POST['Activity'];
            }


            echo '<td colspan="8" style="width:100%;vertical-align:top;border-right:0.5px black solid;border-left:0.5px black solid">';

            // Création des filtres
            $query_1 = 'SELECT *
                FROM tbl_released_package 
                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                    AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                    AND tbl_released_drawing.Ref_Rev like "' . $Ref_Rev . '"
                    AND tbl_released_package.Project like "' . $project_choice . '"
                    AND tbl_released_drawing.Prod_Draw like "' .  $Prod_Draw . '"
                    AND tbl_released_package.Activity like "' . $Activity . '"';

            /* $query_2 = 'SELECT VISA_Inventory
            FROM tbl_released_drawing
            WHERE VISA_Inventory not like ""';*/

            //print_r($query_1);
            include('../REL_Connexion_DB.php');
            $resultat = $mysqli->query($query_1);
            $rowcount = mysqli_num_rows($resultat);
            print_r($rowcount);
            /*$resultat2 = $mysqli->query($query_2);
            $rowcount2 = mysqli_num_rows($resultat2);
            print_r($rowcount2);*/

            echo '<table id="t02" style="height:100px;">';
            echo '<th>Package #</th>';
            echo '<th>Reference</th>';
            echo '<th>Rev</th>';
            echo '<th>Prod_Draw</th>';
            echo '<th>Activity</th>';
            echo '<th>VISA_Inven</th>';
            echo '<th>VISA_Product</th>';
            echo '<th>VISA_Quality</th>';
            echo '<th>VISA_Method</th>';
            echo '<th>VISA_Fin</th>';
            echo '<th>VISA_Prod</th>';
            echo '<th>VISA_Sup</th>';
            echo '<th>VISA_PUR1</th>';
            echo '<th>VISA_PUR2</th>';
            echo '<th>VISA_PUR3</th>';
            echo '<th>VISA_PUR4</th>';
            echo '<th>VISA_PUR5</th>';
            echo '<th>VISA_GID</th>';
            echo '<th>VISA_ROUT</th>';

            while ($row = $resultat->fetch_assoc()) {

                echo '<tr>';
                echo '<td rowspan="2" hidden>';
                echo $row['ID'];
                echo '</td>';
                echo '<td rowspan="2" style="">';
                echo $row['Rel_Pack_Num'];
                echo '</td>';
                echo '<td rowspan="2" style="padding:5px;">';
                echo $row['Reference'];
                echo '</td>';
                echo '<td rowspan="2" style="padding:5px;">';
                echo $row['Ref_Rev'];
                echo '</td>';
                echo '<td rowspan="2" style="padding:5px;">';
                echo $row['Prod_Draw'];
                echo '</td>';
                echo '<td rowspan="2" style="padding:5px;">';
                echo $row['Activity'];
                echo '</td>';
                if (isset($row['VISA_Inventory']) && $row['VISA_Inventory'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_Inventory'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_Inventory'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_Product']) && $row['VISA_Product'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_Product'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_Product'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_Quality']) && $row['VISA_Quality'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_Quality'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_Quality'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_Method']) && $row['VISA_Method'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_Method'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_Method'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_Finance']) && $row['VISA_Finance'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_Finance'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_Finance'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_Prod']) && $row['VISA_Prod'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_Prod'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_Prod'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_Supply']) && $row['VISA_Supply'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_Supply'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_Supply'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_PUR_1']) && $row['VISA_PUR_1'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_PUR_1'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_PUR_1'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_PUR_2']) && $row['VISA_PUR_2'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_PUR_2'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_PUR_2'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_PUR_3']) && $row['VISA_PUR_3'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_PUR_3'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_PUR_3'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_PUR_4']) && $row['VISA_PUR_4'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_PUR_4'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_PUR_4'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_PUR_5']) && $row['VISA_PUR_5'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_PUR_5'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_PUR_5'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_GID']) && $row['VISA_GID'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_GID'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_GID'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['VISA_ROUTING']) && $row['VISA_ROUTING'] == "") {
                    echo '<td style="background-color:red; padding:5px;"><DIV>' . $row['VISA_ROUTING'] . '</DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['VISA_ROUTING'] . '</DIV></td>';
                    $bool = true;
                }
                echo '</tr>';


                echo '<tr>';
                if (isset($row['DATE_Inventory']) && $row['DATE_Inventory'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_Inventory'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_Product']) && $row['DATE_Product'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_Product'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_Quality']) && $row['DATE_Quality'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_Quality'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_Method']) && $row['DATE_Method'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_Method'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_Finance']) && $row['DATE_Finance'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_Finance'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_Prod']) && $row['DATE_Prod'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_Prod'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_Supply']) && $row['DATE_Supply'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_Supply'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_PUR_1']) && $row['DATE_PUR_1'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_PUR_1'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_PUR_2']) && $row['DATE_PUR_2'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_PUR_2'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_PUR_3']) && $row['DATE_PUR_3'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_PUR_3'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_PUR_4']) && $row['DATE_PUR_4'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_PUR_4'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_PUR_5']) && $row['DATE_PUR_5'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_PUR_5'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_GID']) && $row['DATE_GID'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_GID'] . '</DIV></td>';
                    $bool = true;
                }
                if (isset($row['DATE_ROUTING']) && $row['DATE_ROUTING'] == "0000-00-00") {
                    echo '<td style="background-color:red; padding:5px;"><DIV></DIV></td>';
                    $bool = false;
                } else {
                    echo '<td style="background-color:green; padding:5px;"><DIV>' . $row['DATE_ROUTING'] . '</DIV></td>';
                    $bool = true;
                }
                echo '</tr>';
            }
            echo '</table>';
            echo '</td>';

            mysqli_close($mysqli);
            ?>
            </tr>
        </table>
    </form>
</body>

</html>