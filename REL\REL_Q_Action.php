<?php

// MISE A JOUR DES DONNEES POUR POUSSER CET ENREGISTREMENT DANS LE FLUX NORMAL

if (isset($_GET['action']) && ($_GET['action']=="signoff")) 
{
	// création des variables
	//------------------------
	$id = $_GET['ID'];
	$resultat_dynam = $_GET['dynam'];
	$user = $_GET['userid'];
    $owner = $_GET['owner'];
	$date_quality = date("Y-m-d");

	if ($_GET['control_routing']=="%" || $_GET['control_routing']=="")
	{	
		$control_routing="";
	} else {
		$control_routing = $_GET['control_routing'];
	}
	
	if ($_GET['doc_req']=="%" || $_GET['doc_req']=="")
	{
		$resultat_req_doc="";
	} else {
		$resultat_req_doc = $_GET['doc_req'];
	}
	
	if ($_GET['insp']=="%" || $_GET['insp']=="")
	{
		$resultat_ins_type="";
	} else {
		$resultat_ins_type = $_GET['insp'];
	}


	include('../REL_Connexion_DB.php');
	
		//-----------------------
		//Commentaire
		$v = 'Quality: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

		$query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';
		$resultat = $mysqli->query($query_3);

		// On affiche notre message et à la ligne on laisse l'ancien message
		while ($row = $resultat->fetch_assoc())
		{
			if ($_GET['comment'] != "")
			{
				$v = $v . '\r\n' . $row['General_Comments'];
			} else {
				$v = $row['General_Comments'];
			}
		} 
		//-----------------------

	$query_2 = 'UPDATE tbl_released_drawing 
					SET Q_Inspection="' . $resultat_ins_type . '",
						Q_Dynamization="' . $resultat_dynam . '",
						Q_Doc_Req="' . $resultat_req_doc . '",
						Q_Control_Routing="'. $control_routing .'",
						Quality_Owner="'.$owner.'",
						VISA_Quality="' . $user . '",
						DATE_Quality="' . $date_quality . '",
						General_Comments="' . $v . '"
						WHERE ID ="' . $id . '";';
print_r($query_2);
	$resultat = $mysqli->query($query_2);

	mysqli_close($mysqli);

}


// MISE A JOUR DES DONNEES POUR CHANGER LE TYPE D'APPRO ET RE-AIGUILLER L'ENREGISTREMENT DANS LE FLUX
if (isset($_GET['action']) && ($_GET['action']=="doctype_change")) 
{
	//Connexion à BD
	include('../REL_Connexion_DB.php');

	// création des variables
	$id = $_GET['ID'];
	$doc_type = $_GET['doc_type_change'];
	$proc_type = "";
	$date_quality = "0000-00-00";
	$user_none = "";
	$visa_prod="";
	$date_prod="0000-00-00";
	$metro_time="";
	$metro_control="";
    $date_change_doc_type = date('Y-m-d');

	$v = $date_change_doc_type . " - Quality : change of supply to " . $doc_type;

	// On parcours le champ General_Comments de la table released_drawing en fonction de l'id
	$query_3 = 'SELECT General_Comments
					FROM tbl_released_drawing
					WHERE ID ="' . $id . '";';

	// Lancement de la requete
	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc())
	{
		$v = $v . '\r\n' . $row['General_Comments'];
	}

	$query_2 = 'UPDATE tbl_released_drawing 
					SET Doc_Type="' . $doc_type . '",
						Proc_Type="' . $proc_type . '",  
						
						VISA_Quality="' . $user_none . '",
						DATE_Quality="' . $date_quality . '",
						
						VISA_Prod="'.$visa_prod.'",
						DATE_Prod="'.$date_prod.'",
						
						VISA_Metro="'.$VISA_PUR.'",
						DATE_Metro="'.$VISA_PUR.'",
						Metro_Time="'.$metro_time.'",
						Metro_Control="'.$metro_control.'",
						
						General_Comments="' . $v . '"
						WHERE ID ="' . $id . '";';
	$resultat = $mysqli->query($query_2);
	mysqli_close($mysqli);

}
?>