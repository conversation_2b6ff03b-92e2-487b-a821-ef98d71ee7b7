<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_Q_Main_Form_styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
	<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

    

<script>
	function frame_update(obj,table)
	{
        // CAPTURE DU L'ID DE L'ENREGISTREMENT A METTRE A JOUR
        parent.document.getElementById("id_to_update").value=obj.cells[0].textContent.trim()
		var doc_type=obj.cells[10].textContent.trim();

        const xhttp = new XMLHttpRequest();
        xhttp.onload = function (){
           let Q = this.responseText.trim();
           const myArray = Q.split("___");
           const list_doc = myArray[0].split(";");
           const list_insp = myArray[1].split(";");
           const list_dynam = myArray[2];
           const list_control = myArray[3];
           const list_owner = myArray[4];
           //alert(list_doc.length + '   ' + list_doc[0]);
           if(list_doc[0] != "") {
               list_doc.forEach(element => parent.document.getElementById("doc_req_" + element).checked = true);
           }
           if(list_insp[0] != "") {
                list_insp.forEach(element => parent.document.getElementById("inspection_" + element).checked = true);
           }
		   

			parent.document.getElementById("dyn_rule").value = list_dynam;
			
			
			if (list_control!="")
			{
				parent.document.getElementById("Control_Routing").value = list_control;
			}  else if (doc_type.substring(0,3)!="DOC" && doc_type.substring(0,3)!="ASS")
			{
				parent.document.getElementById("Control_Routing").value="Z001";
				parent.document.getElementById("recpt_tps").innerHTML="5 days";
			} else if (doc_type.substring(0,3)=="DOC" || doc_type.substring(0,3)=="ASS") {
				parent.document.getElementById("Control_Routing").value="";
				parent.document.getElementById("recpt_tps").innerHTML="-";
			}
			
			parent.document.getElementById("User_Choice").value = list_owner;
				

           // for(let i =1; i < list_doc.length; i++){
           //     let resultat = "doc_req_" + list_doc[i];
           //     if(resultat !== undefined){
           //         parent.document.getElementById(resultat).checked=true;
           //         //alert(resultat);
           //     }else{
           //         parent.document.getElementById(resultat).checked=false;
           //     }
           // }
        }
		
        xhttp.open("GET", "REL_Q_Recup_Value.php?ID="+parent.document.getElementById("id_to_update").value);
        xhttp.send();
			
		// CHARGEMENT DE L'APERCU DU PLAN
		const pdf_area=parent.document.getElementById("pdf_visual");
		const visual=parent.document.getElementById("visu_drawing");
		let loaded_file=obj.cells[14].textContent.trim();
		

			if (loaded_file!='' && pdf_area.checked==true)
			{
				loaded_file= loaded_file;

				if (loaded_file!=visual.src)
				{
				tmppath=loaded_file;
				visual.setAttribute("src", tmppath);
				}
			} else {
				tmppath="";
				//alert(tmppath);
				visual.setAttribute("src", tmppath);
						}


		// MISE EN AVANT DE LA LIGNE SELECTIONNEE
		var tbl = obj.parentNode;
		var rows = document.getElementsByTagName('tr');

;		for (var i=0;i<rows.length;i++)
		{
			if (rows[i]!=obj) 
			{
				//rows[i].style.bgcolor='#00AA00';
				rows[i].setAttribute('class',"unpicked_line");
			} else {
				//obj.style.bgcolor='#0000AA';
				obj.setAttribute('class',"picked_line");
			}
		}
		
		
		// ACTIVATION DE LA ZONE DE REMPLISSAGE DES DONNEES 
		// ------------------------------------------------
		if (window.parent.document.getElementById('valid_form').style.display=="none")
		{
			window.parent.document.querySelector('#td_detail').setAttribute('class','enabled');
			window.parent.document.getElementById('valid_form').style.display="inline";
		}
		// ----------
		
		// RESET DES CHAMPS A REMPLIR
		// --------------------------
			
			// inspection reset
			var inspect_list = window.parent.document.getElementsByClassName('inspection');
			for (var i=0;i<inspect_list.length;i++)
			{
				if (inspect_list[i].value=="Z08")
				{
					inspect_list[i].checked=true;
				} else {
					inspect_list[i].checked=false;
				}
			}

			
			// Dynamization
			window.parent.document.getElementById("dyn_rule").value="No";

			// Comment
			window.parent.document.getElementById("comment").value="";

			// Nom Utilisateur
			window.parent.document.getElementById("User_Choice").value="";

			// Doc Requirement reset
			var doc_req_list = window.parent.document.getElementsByClassName('doc_req');
			for (var i=0;i<doc_req_list.length;i++)
			{
				doc_req_list[i].checked=false;
			}
		
		// --------------------------
		

		// PREREMPLISSAGE FONCTIONNELLE METIER
		// SI ARTICLE EX --> Z19 CHECKED
		var ref_val=obj.cells[4].textContent.trim();
		if (ref_val.indexOf('EX')!=-1 || ref_val.indexOf('CSA')!=-1 || ref_val.indexOf('IECEX')!=-1 || ref_val.indexOf('ATEX')!=-1 || ref_val.indexOf('YES')!=-1)
		{
			parent.document.getElementById("doc_req_DOC_Z19").checked=true;
		} else {
			parent.document.getElementById("doc_req_DOC_Z19").checked=false;
		}
		
		// SI ARTICLE DE TYPE DOC OU ASSY --> PAS DE GAMME DE CONTROL PAR DEFAUT
		// SI ARTICLE PUR/MACH/
		
		// if (doc_type=="ASSY" || doc_type=="DOC")
		// {
			// parent.document.getElementById("Control_Routing").value="-";
		// } else {
			// parent.document.getElementById("Control_Routing").checked="Z001";
		// }
	}
</script>

</head>

<title>
        REL Pack - Quality Review
    </title>
<body>

<?php
	// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
	include('REL_Workflow_Conditions.php');
?>


        <table id="t01" border=0>



        <!--- Vérification des valeurs --->
        <?php

 
        // Création des filtres
        $query_1 = 'SELECT *, datediff(Now(),DATE_BE_3) as "Delay"
                        FROM tbl_released_package 
                        LEFT JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                        WHERE 
							
							'.$Quality_Conditions.' 
							
                            AND tbl_released_drawing.Rel_Pack_Num like "' . $_GET['rel_pack_num_choice'] . '"
                            AND tbl_released_package.Activity like "' . $_GET['activity_choice'] . '"
                            AND tbl_released_package.Project like "' . $_GET['project_choice'] . '"
                            AND tbl_released_drawing.Reference like "' . $_GET['reference_choice'] . '"
                            AND tbl_released_drawing.Prod_Draw like "' . $_GET['drawing_choice'] . '"
                            AND tbl_released_drawing.Action like "' . $_GET['action_choice'] . '"
                            AND tbl_released_drawing.Doc_Type like "' . $_GET['doc_type_choice'] . '"
                            AND tbl_released_drawing.Proc_Type like "' . $_GET['proc_type_choice'] . '"
                            AND tbl_released_drawing.Ex like "' . $_GET['Ex_Choice'] . '"
							AND tbl_released_drawing.Quality_Owner like "' . $_GET['Quality_Owner_Choice'] . '"
                        ORDER BY tbl_released_drawing.reference DESC
						LIMIT '.$_GET['start'].','.$_GET['nb'];
					

	    echo '<table id="t02">';
		echo '	<th style="width:15px;background-color: rgb(16, 112, 177);" title="Delay in Days">D</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">Diff #</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">Activité</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">Reference</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">R</th>';
        echo '	<th HIDDEN style="background-color: rgb(16, 112, 177);">Titre</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">Plan</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">R</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">Action</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">Type</th>';
        echo '	<th style="background-color: rgb(16, 112, 177);">Owner</th>';
		echo '	<th style="width:80px; background-color: rgb(16, 112, 177);">Invent. Impact</th>';
        echo '	<th style="width:75px;background-color: rgb(16, 112, 177);"  title="Requestor and other departments comments" >Remarks</th>';

        $i = 1;

		include('../REL_Connexion_DB.php');
		$resultat = $mysqli->query($query_1);
        while ($row = $resultat->fetch_assoc()) {
            echo '<tr onclick="frame_update(this,t02)" class="unpicked_line">
				<td hidden>
					'.$row['ID'].'
				</td>
				<td title="Number of days of presence - Waiting Time" style="font-size:9px;font-weight:bold">
					' . $row['Delay'] . '
				</td>
				<td>
					<a target ="_blank" href="REL_Pack_Overview.php?ID='.$row['Rel_Pack_Num'].'"> '.$row['Rel_Pack_Num'].'</a>
				</td>
				<td>
					'.$row['Activity'].'<br>'.$row['Project'].'
				</td>
				<td>
					'.$row['Reference'];
				 if ($row['Ex'] != "NO") {
                        echo '<FONT color="red"><strong><sup>'.$row['Ex'].'</sup><strong></FONT>';
                    } 
				echo '
				</td>
				<td>
					'.$row['Ref_Rev'].'
				</td>
				<td hidden>
					'.$row['Ref_Title'].'
				</td>';

				// echo '<td>';
				// if ($row['Drawing_Path']!="")
				// { 
				// 	echo '<div class="dropdown_prod_drawing">';
				// 		echo '<a target=_blank href="DRAWINGS\\IN_PROCESS\\'.$row['Drawing_Path'].'">' . $row['Prod_Draw'] . '</a>';
				// 		echo '<div class="dropdown_prod_drawing-content">';
				// 		echo '<p><iframe src="DRAWINGS\\IN_PROCESS\\'.$row['Drawing_Path'].'#toolbar=0&navpanes=0&scrollbar=0" width="300px" height="210px" scrolbar=no></iframe>
				// 			</p>';
				// 		echo '</div>';
				// 	echo '</div>';
				// } else {
				// 	echo $row['Action'];
				// }
				// echo '</td>';

				include('NO_PREVIEW.php');
				
			echo '<td>
					'.$row['Prod_Draw_Rev'].'
				</td>
				<td>';
					// Si la ligne Action est égal à Modification alors on raccourci le mot pour ecrire "modif" à la place
					if ($row['Action'] == "Modification")
					{
						echo substr($row['Action'], 0, 5);
					} else {
						echo $row['Action'];
					}
					//---------

			echo '</td>
				<td>
					'.$row['Doc_Type'].'<br>'.$row['Proc_Type'].'
				</td>
				<td>
				    '.$row['Quality_Owner'].'
                </td>
				<td>
					'.$row['Inventory_Impact'].'
				</td>
				<td>';
				// Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
				$nbre_lignes = substr_count(nl2br($row['Requestor_Comments']), "\n");
				
				//$nmax = 30;
				$nmax = 0;
				if ((strlen($row['Requestor_Comments']) > $nmax)) {
					echo '<div class="dropdown">';
					echo '<span>
							 <img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:1" >
						  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>Requestor Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['Requestor_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\requestor_comment_icon_b.png" style="height:15px; opacity:0.3;" >';
				}
				
				echo "<font size=4> | </font>";
				
				$nmax = 0;
				if ((strlen($row['General_Comments']) > $nmax)) {
					echo htmlspecialchars(substr(nl2br($row['General_Comments']), 0, $nmax), ENT_QUOTES);
					echo '<div class="dropdown">';
					echo '<span>
							<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:1" >
						  </span>';
					echo '<div class="dropdown-content">';
					echo '<p><b>- <u>General Comments</u> -</b><br \>' . htmlspecialchars_decode(nl2br($row['General_Comments']), ENT_QUOTES) . '</p>';
					echo '</div>';
					echo '</div>';
				} else {
					echo '<img src="\Common_Resources\general_comment_icon_b.png" style="height:15px; opacity:0.3" >';
				}
				
				echo '</td>
			
				<td hidden>
					https://app.aletiq.com/parts/preview/id/' . $row['Prod_Draw'] . '/revision/' . $row['Prod_Draw_Rev'] . '
				</td>
				</tr>';
            $i = $i + 1;
        }
        mysqli_close($mysqli);

		echo '</table>';
        ?>


</body>

</html>