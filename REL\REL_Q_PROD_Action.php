<?php

// ENREGISTREMEENT DES DONNNEES ET SIGNATURE - FLUX NOMINAL
    if (isset($_GET['action']) && ($_GET['action'])=="signoff")
	{

        $id = $_GET['ID'];		
        

		
		if ($_GET['userid']=="%" || $_GET['userid']=="")
		{
			$user="";
			$date_q_prod = "0000-00-00";
		} else {
			$user = $_GET['userid'];
			$date_q_prod = date('Y-m-d');
		}

		include('../REL_Connexion_DB.php');
        
		// Si le textarea dans REL_PRODUCT_Item.php n'est pas videalors ont afficher "Product : + le message"
        //Commentaire
		$v = 'Q_PROD: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

		$query_3 = 'SELECT General_Comments
						FROM tbl_released_drawing
						WHERE ID ="' . $id . '";';

		$resultat = $mysqli->query($query_3);

		// On affiche notre message et à la ligne on laisse l'ancien message
		while ($row = $resultat->fetch_assoc())
		{
			if ($_GET['comment'] != "")
			{
				$v = $v . '\r\n' . $row['General_Comments'];
			} else {
				$v = $row['General_Comments'];
			}
		} 
		//-----------------------

        $query_2 = 'UPDATE tbl_released_drawing 
                        SET DATE_Q_PROD="' . $date_q_prod . '",
                            VISA_Q_PROD="' . $user . '",
                            General_Comments="' . $v . '"
                            WHERE ID ="' . $id . '";';
		// print_r($query_2);

        $resultat = $mysqli->query($query_2);

        mysqli_close($mysqli);

    }
?>