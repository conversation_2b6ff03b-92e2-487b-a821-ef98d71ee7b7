<?php

	$type = explode("__",$_GET['type']);
	$ref = explode("__",$_GET['to_be_checked']);


	include('../REL_Connexion_DB.php');

	$query_3 = 'SELECT '.$type[0].','.$type[1].'
				FROM tbl_released_drawing
				LEFT JOIN tbl_released_package ON  tbl_released_package.Rel_Pack_Num=tbl_released_drawing.Rel_Pack_Num
				WHERE '.$type[0].' like "' . $ref[0] . '"
				  AND VISA_GID like ""
				  AND Creation_VISA not like ""
				  ;';
	$resultat = $mysqli->query($query_3);
	$rowcount = mysqli_num_rows($resultat);
	if ($rowcount>0)
	{
		
		while ($row = $resultat->fetch_assoc())
		{
			$output=$row[$type[0]];
			$output=$output . "__" . $row[$type[1]];
		} 

		mysqli_close($mysqli);

		echo $output;
	} else {
	}

	


?>
