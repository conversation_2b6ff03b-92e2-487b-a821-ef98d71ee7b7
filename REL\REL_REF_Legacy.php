<?php

	$ref = $_GET['Ref'];

	include('../REL_Connexion_DB.php');

	$query = 'SELECT 
					Reference,
					Ref_Rev,
					RDO,
					ECCN,
					HTS,
					Alias,
					Ref_Title,
					Doc_Type,
					Material_Type,
					Ex,
					Cust_Drawing,
					Weight_Unit,
					Plating_Surface_Unit,
					FXXX
				FROM tbl_released_drawing
				WHERE Reference like "' . $ref . '"
				  AND VISA_GID not like ""
				ORDER BY Ref_Rev Desc
				LIMIT 0,1;';
	$resultat = $mysqli->query($query);
	$rowcount = mysqli_num_rows($resultat);
	if ($rowcount>0)
	{
		
		while ($row = $resultat->fetch_assoc())
		{
			$output=$row['HTS'] . '__' . $row['ECCN'] . '__' . $row['RDO'] . '__' . $row['Alias'] . '__' . $row['Ref_Title'] . '__' . $row['Doc_Type'] . '__' .$row['Material_Type'] . '__' .$row['Ex'] . '__' .$row['Cust_Drawing'] . '__' .$row['Weight_Unit'] . '__' .$row['Plating_Surface_Unit'] . '__' .$row['FXXX'] ;
		} 
		//

		mysqli_close($mysqli);

		echo $output;
	} else {
	}

	


?>
