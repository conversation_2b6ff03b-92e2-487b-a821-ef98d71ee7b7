<?php

if (isset($_GET['userid']) && ($_GET['userid'] != '%'))
{

	// création des variables
	//-----------------------
	$id = $_GET['ID'];
	$user = $_GET['userid'];
	if ($_GET['eccn']=="%" || $_GET['eccn']=="")
	{
		$eccn="";
	} else {
		$eccn = $_GET['eccn'];
	}
	if ($_GET['hts']=="%" || $_GET['hts']=="")
	{
		$hts="";
	} else {
		$hts = $_GET['hts'];
	}
	if ($_GET['supervisor']=="%" || $_GET['supervisor']=="")
	{
		$supervisor="";
	} else {
		$supervisor = $_GET['supervisor'];
	}
	

	$date_supply = date("Y-m-d");


	include('../REL_Connexion_DB.php');

	//-----------------------
	//Commentaire
	$v = 'Supply: ' . htmlspecialchars($_GET['comment'], ENT_QUOTES);

	$query_3 = 'SELECT General_Comments
					FROM tbl_released_drawing
					WHERE ID ="' . $id . '";';
	$resultat = $mysqli->query($query_3);

	// On affiche notre message et à la ligne on laisse l'ancien message
	while ($row = $resultat->fetch_assoc())
	{
		if ($_GET['comment'] != "")
		{
			$v = $v . '\r\n' . $row['General_Comments'];
		} else {
			$v = $row['General_Comments'];
		}
	} 
	//-----------------------

	$query = 'UPDATE tbl_released_drawing 
					SET DATE_Supply="' . $date_supply . '",
						VISA_Supply="' . $user . '",
						ECCN = "' . $eccn . '",
						HTS = "' . $hts . '",
						Supervisor = "' . $supervisor . '",
						General_Comments="' . $v . '"
						WHERE ID ="' . $id . '";';

	$resultat = $mysqli->query($query);
	
	mysqli_close($mysqli);
}

?>
