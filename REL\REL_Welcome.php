<?php
	require('login.php');
	login(explode("\\", $_SERVER['REMOTE_USER']));
	$logged_user=explode("\\",$_SERVER['REMOTE_USER'])[1];
?>

<!DOCTYPE html>
<html translate="no">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="REL_Welcome_styles.css">
<link rel="stylesheet" type="text/css" href="REL_vertical_tab.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<link rel="icon" type="image/png" href="\Common_Resources\icon_diff.png" />


	<script>
	
		// COUNT UPDATE AFTER SIGNOFF 
		// --------------------------
		function count_update(control_name)
		{					
			if (control_name!="")
			{
				let curr=parseInt(document.getElementById(control_name).innerHTML);
				curr--;
				if (curr>=0)
				{
					document.getElementById(control_name).innerHTML=curr;
				} else {
					document.getElementById(control_name).innerHTML=0;
				}
			}
		}
		// --------------------------
	
	
		// GESTION DU BOUTON DE MASQUAGE/AFFFICHAGE DE LA BANNIERE DE GAUCHE
		// ------------------------------------------------------------------
		function banner_toggle_action() {
			if (document.getElementById("left_banner").style.display != "none") {
				document.getElementById("left_banner").style.display = "none";
				document.getElementById("main_frame_id").setAttribute("class", "main_frame_extended");
				document.getElementById("left_banner_section").style.display = "inline";
				//document.getElementById("button_5_reduced").style.display="inline";
			} else {
				document.getElementById("left_banner").style.display = "inline";
				document.getElementById("main_frame_id").setAttribute("class", "main_frame_reduced");
				document.getElementById("left_banner_section").style.display = "none";
				//document.getElementById("button_5_reduced").style.display="none";
			}
		}
		// -------------------------------------------------------------------

		//
 
		function title_toggle_action() {

			if (document.getElementById("title_banner").style.display != "none") {
				document.getElementById("title_banner").style.display = "none";
				document.getElementById("title_toggle").style.top = 0;
				document.getElementById("title_toggle").style.left = "calc(100% - 102px)";
				document.getElementById("logo_scm").style.height = "25px";
				document.getElementById("main_frame_id").style.height = "calc(100% - 32px)";
				document.getElementById("main_frame_id").style.top = "30px";
				document.getElementById("banner_toggle").style.top = "0px";
				document.getElementById("title_banner_reduced").style.display = "block";
				if (document.getElementById("24h_banner"))
				{
					document.getElementById("24h_banner").style.display="none";
				}

			} else {
				if (document.getElementById("24h_banner"))
				{
					document.getElementById("24h_banner").style.display="block";
				}
				document.getElementById("title_banner").style.display = "block";
				document.getElementById("banner_toggle").style.top = "68px";
				document.getElementById("logo_scm").style.display = "block";
				document.getElementById("logo_scm").style.height = "60px";
				document.getElementById("main_frame_id").style.height = "calc(100% - 70px)";
				document.getElementById("main_frame_id").style.top = "68px";
				document.getElementById("title_banner_reduced").style.display = "none";
				document.getElementById("title_toggle").style.left = "calc(100% - " + document.getElementById("logo_scm").width + "px - " + document.getElementById("title_toggle").style.width + " - 10px)";
				document.getElementById("title_toggle").style.top = "calc(68px - " + document.getElementById("title_toggle").style.width + ")";
			}
		}


		// FONCTION AU CHARGEMENT DE LA PAGE
		// ---------------------------------
		async function welcome_page_loading() 
		{
		
			const counter_cells=document.getElementsByName("counter_cell");
			let i=0;
			while (i<counter_cells.length)
			{
				counter_cells[i].innerHTML='<img src="/Common_Resources/gif_load_3.gif" width="15px">';
				i++;
			}
			
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() 
			{
				raw_val = this.responseText.trim();
				var counter_val=raw_val.split("||");
				let i=0;
				while (i < counter_val.length) 
				{
					var table_val=counter_val[i].split("__");
					document.getElementById(table_val[0]).innerHTML = table_val[1];
					i++;
				}
			}
	
			xhttp.open("GET", "REL_Welcome_Counter.php");
			xhttp.send();
		}
		// ---------------------------------


		// MISE EN GRAS DU MENU SUR LEQUEL L'UTILISATEUR CLIQUE
		//-----------------------------------------------------
		function focus_in(el)
		{
			
			let i = 1;
			const list_button = document.getElementsByClassName("w3-bar-item w3-button");
			while (i <= list_button.length) {
				document.getElementById("button_" + i).style.fontWeight = 100;
				i++;
			}

			el.style.fontWeight = 700;
			
		}


		// VERIFICATION DU PASSWORD RENTRE POUR ALLER DANS LA PAGE ADMINISTRATION
		// ----------------------------------------------------------------------
		function Admin_PWD_Check() {
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() {
				var mdp=document.getElementById("pass_input").value;
				//var mdp = prompt("Password", "");
				if (mdp != "") {
					pwd = this.responseText.trim();
					if (mdp == pwd) {
						
						document.getElementById("pass_input").value="";
						document.getElementById("pass_zone").hidden=true;
						document.getElementById("admin_pass_button").hidden=false;
						
						const url = "REL_Admin_Form.php";
						window.open(url);
					} else if (mdp != "" && mdp != null) {
						window.alert("Incorrect Password!");
					} else {}
				}
			}
			xhttp.open("GET", "REL_Admin_PWD.php");
			xhttp.send();
		}
		
		
		function pass_display()
		{
			if (document.getElementById("pass_input")!="")
			{
				document.getElementById("pass_zone").hidden=false;
				document.getElementById("admin_pass_button").hidden=true;
				document.getElementById("pass_input").focus();
			}
		}
		// ----------------------------------------------------------------------

		function reduced_banner_target(page_url) {
			document.getElementById("main_frame_id").src = page_url + "?random=" + Math.random();
		}

		function text_link(el) {
			document.getElementById("main_frame_id").src = el.attributes.link.value + "?random=" + Math.random();
			focus_in(el);
		}

	</script>
</head>



<title>RELEASE Process - Welcome </title>



<?php 
include('../Common_Resources/Tool_Banner_Deco_Period.php');
?>


<body>
<script>
welcome_page_loading();
</script>
	<!-- TITRE PRINCIPALE & BANNIERE -->
	<!--------------------------------->
	<input type="submit" id="title_toggle" name="title_toggle" class="btn blue2" style="z-index:99;border-radius:1px;font-size:11;position:absolute;z-index:99;left:calc(100% - 193px);top:38px;width:30px;height:30px;text-align:center;" value="&#9776" title="title banner toggle" onclick="title_toggle_action()" />
	
	
	
	<?php 

		$class_value="w3-container w3-black-scm1";
		if ($interval_noel->format('%R%a')>=0 && $interval_noel->format('%R%a')<=$duration_noel ) // AFFICHE BANNIERE NOEL PENDANT LES $DURATION_NOEL JOURS AVANT FIN ANNEE
		{ 
			echo '';
		} elseif ($interval_hiver->format('%R%a')>0 && $interval_hiver->format('%R%a')<=$duration_hiver ) // AFFICHE BANNIERE HIVER PENDANT LES $DURATION_HIVER JOURS APRES LA $DATE_HIVER
		{
			echo '';
		} elseif ($interval_24_auto->format('%R%a')>0 && $interval_24_auto->format('%R%a')<=$duration_24_auto) 
		{
			echo '';
		} else {
			
		}
	?>
	
	<div id="title_banner" style="z-index:99" class=" <?php echo $class_value; ?>">
		<h1 style="font-family:Conneqt, sans-serif;">Release Process Tool / Aletiq</h1>
	</div>
	
	
	<div hidden id="title_banner_reduced" class="w3-container w3-blue-scm1" style="height:30px;z-index:99">
		<div id="reduced_title">Release Process Tool / Aletiq</div>
	</div>
	<a onclick="text_link(this)" link="REL_Overview_Form.php"><img id="logo_scm" src="\Common_Resources\Logo SCM_Blanc_01.png" height="60px" style="z-index:99;position: absolute; top:2px; right:14px; filter:grayscale(20%)"></a>
	
	
	

	<!--------------------------------->

	<!-- BANNIERE GAUCHE REDUITE -->
	<!------------------------------>
	<input type="submit" id="banner_toggle" name="banner_toggle" class="btn grey" style="border-radius:1px;font-size:11;position:absolute;z-index:99;top:68px;width:30px;height:30px;text-align:center" value="|||" title="toggle left banner" onclick="banner_toggle_action()" />
	<div hidden id="left_banner_section" class="w3-sidebar w3-light-grey w3-bar-block" style="width:30px">
		<!-- ICONE POUR ACCES VERS LES AUTRES PAGES -- A FINALISER -->
		<input onclick="reduced_banner_target('REL_Drawing_Status.php')" type="image" src="\Common_Resources\search_icon.png" class="" style="position:absolute;z-index:99;top:35px;width:27px;margin-left:3px;text-align:center" title="Status" />
		<input onclick="reduced_banner_target('REL_Package_Status.php')" type="image" src="\Common_Resources\search_icon.png" class="" style="position:absolute;z-index:99;top:35px;width:27px;margin-left:3px;text-align:center" title="Status_Package" />
		<input onclick="reduced_banner_target('REL_BE_Form.php')" type="image" src="\Common_Resources\engineering2_icon.png" class="" style="position:absolute;z-index:99;top:70px;width:27px;margin-left:3px;text-align:center" title="Engineering" />
		<input onclick="reduced_banner_target('REL_PRODUCT_Main_Form.php')" type="image" src="\Common_Resources\product_icon.png" class="" style="position:absolute;z-index:99;top:109px;width:27px;margin-left:3px;text-align:center" title="Product Management" />
		<!--<input  onclick="reduced_banner_target('REL_BE_Form.php')" type="submit" class="btn grey" style="font-size:11;position:absolute;z-index:99;top:70px;width:30px;height:30px;text-align:center"  value="E" title="Engineering" />-->
		<input onclick="reduced_banner_target('REL_Q_Main_Form.php')" type="image" src="\Common_Resources\qualite_icon.png" type="submit" class="" style="position:absolute;z-index:99;top:150px;width:27px;margin-left:3px;text-align:center" width="100%" value="Q" title="Quality" />
		<input onclick="reduced_banner_target('REL_Project_Main_Form.php')" type="image" src="\Common_Resources\project_icon.png" type="submit" class="" style="position:absolute;z-index:99;top:190px;width:27px;margin-left:3px;text-align:center" width="100%" value="P" title="Project" />
		<input onclick="reduced_banner_target('REL_METRO_Main_Form.php')" type="image" src="\Common_Resources\metro_icon.png" type="submit" class="" style="position:absolute;z-index:99;top:230px;width:27px;margin-left:3px;text-align:center" width="100%" value="Metro" title="Metro" />
		<input onclick="reduced_banner_target('REL_PROD_ASSY_Main_Form.php')" type="image" src="\Common_Resources\assemblage_icon.png" class="" style="position:absolute;z-index:99;top:270px;width:27px;margin-left:3px;text-align:center" value="A" title="Assemblage" />
		<input onclick="reduced_banner_target('REL_METH_Main_Form.php')" type="image" src="\Common_Resources\method_icon.jpg" class="" style="position:absolute;z-index:99;top:310px;width:27px;margin-left:3px;text-align:center" value="M" title="Method" />
		<input onclick="reduced_banner_target('REL_PROD_MACH_Main_Form.php')" type="image" src="\Common_Resources\machining-icon.png" class="" style="position:absolute;z-index:99;top:355px;width:27px;margin-left:3px;text-align:center" value="M" title="Machining" />
		<input onclick="reduced_banner_target('REL_PROD_MOLD_Main_Form.php')" type="image" src="\Common_Resources\molding_icon.png" class="" style="position:absolute;z-index:99;top:400px;width:27px;margin-left:3px;text-align:center" value="Mold" title="Molding" />
		<input onclick="reduced_banner_target('REL_PUR_Main_Form.php')" type="image" src="\Common_Resources\achat_icon.png" class="" style="position:absolute;z-index:99;top:445px;width:27px;margin-left:3px;text-align:center" value="Achat" title="Purchasing" />
		<input onclick="reduced_banner_target('REL_SUPPLY_Main_Form.php')" type="image" src="\Common_Resources\supply_icon.png" class="" style="position:absolute;z-index:99;top:490px;width:27px;margin-left:3px;text-align:center" value="supply" title="Supply" />
		<input onclick="reduced_banner_target('REL_MOF_Main_Form.php')" type="image" src="\Common_Resources\mof_icon.png" class="" style="position:absolute;z-index:99;top:535px;width:27px;margin-left:3px;text-align:center" value="MOF" title="Assembly Routing" />
		<input onclick="reduced_banner_target('REL_GID_Main_Form.php')" type="image" src="\Common_Resources\icon_sap.png" class="" style="position:absolute;z-index:99;top:578px;width:27px;margin-left:3px;text-align:center" value="GID" title="Core Data" />
		<input onclick="reduced_banner_target('REL_GID_2_Main_Form.php')" type="image" src="\Common_Resources\icon_sap.jpg" class="" style="position:absolute;z-index:99;top:608px;width:27px;margin-left:3px;text-align:center" value="GID 2" title="Prod Data" />
		<input onclick="reduced_banner_target('REL_LABO_Main_Form.php')" type="image" src="\Common_Resources\labo_icon.png" class="" style="position:absolute;z-index:99;top:635px;width:27px;margin-left:3px;text-align:center" value="LABO" title="Laboratory routing review" />
		<input onclick="reduced_banner_target('REL_FIN_Main_Form.php')" type="image" src="\Common_Resources\finance_icon.png" class="" style="position:absolute;z-index:99;top:677px;width:27px;margin-left:3px;text-align:center" value="F" title="Costing" />
		
	</div>
	<!------------------------------>


	<!-- SIDEBAR LEFT -->
	<!------------------>
	<div id="left_banner" class="w3-sidebar w3-light-grey w3-bar-block" style="width:13%;">
		<h5 class="w3-bar-item" style="margin-top:20px;margin-bottom:-10px;font-family:Conneqt, sans-serif;font-size:10pt;">Process Overview</h5>
		<?php $i = 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_Drawing_Status.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-family:Arial, sans-serif;font-size:10pt;font-size:9pt; margin-left:2px;margin-top:-6px" >Reference Followup</a>
		
		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_Package_Status.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-family:Arial, sans-serif;font-size:10pt;font-size:9pt; margin-left:2px;margin-top:-6px" >Package Status</a>
		
		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<h5 class="w3-bar-item" style="margin-top:-10px;margin-bottom:-10px;font-family:Conneqt, sans-serif;font-size:10pt;">Department</h5>
		<a onclick="text_link(this)" link="REL_BE_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;" >
			<table id="t_counter">
				<tr>
					<td>Engineering</td>
					<td id="id_count_be" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_PRODUCT_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Product Management</td>
					<td id="id_count_product" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_Inven_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Inventory</td>
					<td id="id_count_inven" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_Q_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Quality</td>
					<td id="id_count_qual" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_Project_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Project</td>
					<td id="id_count_project" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_METRO_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Metrology</td>
					<td id="id_count_metro" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_Q_PROD_Main.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Quality Prod</td>
					<td id="id_count_qualprod" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_PROD_ASSY_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Assembly</td>
					<td id="id_count_prod_assy" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_METH_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Assy. Methods</td>
					<td id="id_count_method" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_PROD_MACH_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Machining</td>
					<td id="id_count_prod_mach" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_Prod_MOLD_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Molding</td>
					<td id="id_count_prod_mold" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_PUR_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Purchasing</td>
					<td id="id_count_pur" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_SUPPLY_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Logistics</td>
					<td id="id_count_prod_log" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_MOF_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px; margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Assy. Routings</td>
					<td id="id_count_prod_routing" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_GID_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>SAP Core Data</td>
					<td id="id_count_prod_gid_1" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_GID_2_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>SAP Prod Data</td>
					<td id="id_count_prod_gid_2" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_LABO_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Laboratory</td>
					<td id="id_count_labo" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_Saisie_Gamme_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Routing Entry</td>
					<td id="id_count_prod_rounting_entry" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a onclick="text_link(this)" link="REL_FIN_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" >
			<table id="t_counter">
				<tr>
					<td>Costing</td>
					<td id="id_count_fin" name="counter_cell" style="text-align:right;font-size:8pt"><img src="/Common_Resources/gif_load_3.gif" width="15px"></td>
				</tr>
			</table>
		</a>
		<?php $i = $i + 1;
			$var_but = "button_" . $i; ?>
		<a id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="pass_display()">
				<table id="t_counter">
					<tr HIDDEN id="pass_zone" >
						<td style="font-size:9pt;font-weight:bold;font-style:italic">
							Password:<br>
							<input type="password" value="" style="width:150px;height:21px;font-size:9pt;" id="pass_input">
						</td>
						<td>
							<input type="button" class="btn black" onclick="Admin_PWD_Check()" style="font-size:7pt;width:35px;height:21px;vertical-align:middle;text-align:center" value="GO"/>
						</td>
					</tr>
					<tr id="admin_pass_button">
						<td>Admin</td>
						<td style="text-align:right;font-size:8pt"></td>
					</tr>
				</table>
			</a>
		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
	
		<!--
		div style="width:90%;background-color:WhiteSmoke;border-radius:5px;font-size:8px;font-weight:bold;position:absolute; bottom:60px;padding-left:10px;left:10px;padding-top:5px;padding-bottom:5px;font-style:italic;color:Gray;text-transform:uppercase">
		-->
		<div style="font-size:8px;font-weight:normal;position:absolute; bottom:60px;padding-left:10px;left:10px;padding-top:5px;padding-bottom:5px;font-style:italic;color:silver;text-transform:uppercase">		
			<img src="\Common_Resources\icon_user.png" height="15px" style="margin-right:5px;filter : grayscale(90%);"> <?php echo $logged_user;?>
		</div>
	
	</div>


	<!-- PAGE CONTENT -->
	<!------------------>
	<div style="margin-left:13%;z-index:99;">

		<div class="w3-container" >
			<iframe name="main_iframe" id="main_frame_id" class="main_frame_reduced" src="REL_Overview_form.php" style="z-index:99;background-image: url(/Common_Resources/logo_scm_zoom_in_transparent.jpg);background-repeat: no-repeat;background-position: right bottom;margin-left:-15px;background-color:transparent" frameborder=0 scrolling=yes>
		</div>

	</div>

</body>

</html>