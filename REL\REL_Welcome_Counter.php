<?php
// DEFINITION DES CONDITIONS DU FLUX DE DIFFUSION
// ----------------------------------------------
include('REL_Workflow_Conditions.php');


$m=0;
$query_be = 'SELECT  count(*) as "counter"
                FROM tbl_released_package
                INNER JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE 
					' . $BE_1_condition . ' OR ' . $BE_2_condition . ' OR ' . $BE_3_condition;

$query_array[$m] = $query_be;
$id_table_array[$m]="id_count_be";
$m=$m+1;			

$query_product = 'SELECT  count(*) as "counter"
                FROM tbl_released_package
                LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE 
					' . $Product_Conditions;
$query_array[$m] = $query_product;
$id_table_array[$m]="id_count_product";
$m=$m+1;		

$query_inven = 'SELECT  count(*) as "counter"
                FROM tbl_released_package
                LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE 
					' . $Inventory_Conditions;
$query_array[$m] = $query_inven;
$id_table_array[$m] = "id_count_inven";
$m=$m+1;	

$query_qual = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Quality_Conditions;
$query_array[$m] = $query_qual;
$id_table_array[$m] = "id_count_qual";
$m=$m+1;	

$query_metro = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $METRO_Conditions;
$query_array[$m] = $query_metro;
$id_table_array[$m] = "id_count_metro";
$m=$m+1;

$query_q_prod = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Q_PROD_Conditions;
$query_array[$m] = $query_q_prod;
$id_table_array[$m] = "id_count_qualprod";
$m=$m+1;

$query_assy = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Prod_ASSY_Conditions;
$query_array[$m] = $query_assy;
$id_table_array[$m] = "id_count_prod_assy";
$m=$m+1;

$query_method = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Method_Conditions;
$query_array[$m] = $query_method;
$id_table_array[$m] = "id_count_method";
$m=$m+1;

$query_mach = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Prod_MACH_Conditions;
$query_array[$m] = $query_mach;
$id_table_array[$m] = "id_count_prod_mach";
$m=$m+1;

$query_mold = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Prod_MOLD_Conditions;
$query_array[$m] = $query_mold;
$id_table_array[$m] = "id_count_prod_mold";
$m=$m+1;

$query_supply = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Supply_Conditions;
$query_array[$m] = $query_supply;
$id_table_array[$m] = "id_count_prod_log";
$m=$m+1;

$query_assy_routing = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $MOF_Conditions;
$query_array[$m] = $query_assy_routing;
$id_table_array[$m] = "id_count_prod_routing";
$m=$m+1;

$query_gid_1 = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $GID_1_Conditions;
$query_array[$m] = $query_gid_1;
$id_table_array[$m] = "id_count_prod_gid_1";
$m=$m+1;

$query_gid_2 = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $GID_2_Conditions;
$query_array[$m] = $query_gid_2;
$id_table_array[$m] = "id_count_prod_gid_2";
$m=$m+1;

$query_fin = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Finance_Conditions;
$query_array[$m] = $query_fin;
$id_table_array[$m] = "id_count_fin";
$m=$m+1;

$query_pur = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $PUR_1_RFQ_Conditions . ' OR ' . $PUR_2_PRISDANS_Conditions . ' OR ' . $PUR_3_Conditions;
$query_array[$m] = $query_pur;
$id_table_array[$m] = "id_count_pur";
$m=$m+1;

$query_labo = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $LABO_Conditions;
$query_array[$m] = $query_labo;
$id_table_array[$m] = "id_count_labo";
$m=$m+1;

$query_routing_entry = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $ROUTING_ENTRY_Conditions;
$query_array[$m] = $query_routing_entry;
$id_table_array[$m] = "id_count_prod_rounting_entry";
$m=$m+1;

$query_project = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Project_Conditions;
$query_array[$m] = $query_project;
$id_table_array[$m] = "id_count_project";
$m=$m+1;

include('../REL_Connexion_DB.php');


for($i = 0; $i < count($query_array); ++$i)
{
	$resultat = $mysqli->query($query_array[$i]);
	while ($row = $resultat->fetch_assoc())
	{
		$result_val[$i]=$row['counter'];
	}
}


$output_val="";

for($i = 0; $i < count($result_val); ++$i)
{
	if ($output_val=="")
	{
	$output_val=$id_table_array[$i]."__".$result_val[$i];
	} else {
		$output_val=$output_val."||".$id_table_array[$i]."__".$result_val[$i];
	}
	
}

echo $output_val;


mysqli_close($mysqli);


?>