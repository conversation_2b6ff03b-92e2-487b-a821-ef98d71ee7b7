body {
  background-color: rgb(255, 255, 255);
  color: black;
  font-size: 9pt;
  font-family: Tahoma, sans-serif;
  font-weight: normal;

}

@font-face {
	font-family: "Conneqt";
	src: url("/Common_Resources/font_Conneqt.otf");
}





.main_frame_reduced{
    position:absolute;
    z-index:1;
    top:68px;
    left:14%;
    width:86.5%;
    height:calc(100% - 70px);
}

.main_frame_extended{
    position:absolute;
    z-index:1;
    top:68px;
    left:50px;
    width:calc(99vw - 30px);
    height:calc(100% - 70px);
}

div#reduced_title
{
margin-left:30px;
margin-top:5px;
letter-spacing:5px;
font-family: Arial, sans-serif;
}



div#Body {
	margin-left:5px;
	margin-top:5px;
	text-align:left;
	margin-bottom:6px;
	margin-left:10px;
	vertical-align:middle;
	}

div#FilterTitle {
	text-indent:20px;
	font-size:8pt;
	font-weight:normal;
	font-family:Tahoma, sans-serif;
	margin-left:5px;
	margin-bottom:5px;
	text-align:justify;
	}
	
div#FilterTitle_User {
	text-indent:40px;
	font-size:8pt;
	font-weight:normal;
	font-family:Tahoma, sans-serif;
	text-align:justify;
	}

div#InpBox_User {
	font-size:8pt;
	font-weight:normal;
	font-family:Tahoma, sans-serif;
	text-align:left;
	vertical-align:middle;
	margin-bottom:0px;
	margin-top:0px;
	margin-left:2px;
	}

div#InpBox {
	font-size:8pt;
	font-weight:normal;
	font-family:Tahoma, sans-serif;
	text-align:left;
	vertical-align:middle;
	margin-bottom:2px;
	margin-top:2px;
	margin-left:5px;
	}

div#footer {
	border-top: 1px solid #A6A6A6;
	text-align:right;
	font-size:8pt;
	margin-top:2px;
	margin-bottom:2px;
	color:#A6A6A6;
	font-weight:bold;
	font-family:Tahoma, sans-serif;
	}
	
div#Result_info {
	text-indent:10px;
	font-size:7pt;
	font-weight:normal;
	font-family:Tahoma, sans-serif;
	margin-left:5px;
	margin-top:5px;
	text-align:justify;
	}

#t01 {
  border-collapse: collapse;
  vertical-align: middle;
  padding:5px;
  height:100%;
}

#t01 td {
  text-align: left;
}

#t02 {
  border-collapse: collapse;
  vertical-align: middle;
  text-align: center;
  
}

#t02 td {
  text-align: center;
}

#t03 {
  border-collapse: collapse;
  overflow-y: auto;
  text-align: center;
  vertical-align: middle;
}

#t03 th {
  border: 0.0px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
  font-family: Arial, Helvetica, sans-serif;
  text-align: center;
}

#t03 td {
  text-align: center;
  vertical-align: middle;

}


#t04 {
  border-collapse: collapse;
  width:100%;
  overflow-y: auto;
  margin-top:-5px;
  margin-left:-5px;
  font-size:12px;
}

#t04 th {
  border: 0px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
  font-family: Arial Helvetica, sans-serif;
  text-align: center;
}

#t04 td {
  text-align: center;
  vertical-align: middle;
}
#t04 tr {
  height: 20px;
}

#t04 tr:hover {
  background-color:rgb(76, 126, 160);
  color:white;
  cursor:pointer;
}

#field_title:hover {
  background-color:rgb(76, 126, 160);
  color:white;
  cursor:pointer;
}


#t_counter {
border-collapse: collapse;
width:100%;
font-family:Arial;
}

#t_counter td {
margin-top:-15px;
vertical-align:middle;
font-size:9pt;
font-weight:light;
}

div#Table_results {
  text-align: middle;
  color: black;
}





.btn {
	display: inline-block;
	text-decoration:none; 
	font-size:9pt;
	text-align:left;
	padding: 1px 5px;
	color: white;
	background: rgb(48,129,185);
	border: 1px solid #2cabcf;
	width: 130px;
	height:16px;
	vertical-align:middle;
	/*color: #1f6b81;*/
}
	
.btn:hover, .btn:focus {
	background: #ccc;
	text-shadow: 0 -1px #eee;
}
	
.btn:active {
	background: #2cabcf;
	text-shadow: 0 -1px #eee;
	border: 1px solid #1f6b81;
}