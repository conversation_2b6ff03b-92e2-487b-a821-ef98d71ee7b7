@echo off

rem For /f "tokens=2-4 delims=/ " %%a in ('date /t') do (set mydate=%%a_%%b)

FOR /F "skip=1 tokens=1-6" %%A IN ('WMIC Path Win32_LocalTime Get Day^,Hour^,Minute^,Month^,Second^,Year /Format:table') DO (
    if "%%B" NEQ "" (
        SET /A FDATE=%%F*10000+%%D*100+%%A
    )
)
rem @echo on
rem echo date=%FDATE%
rem echo year=%FDATE:~0,4%
rem echo month=%FDATE:~4,2%
rem echo day=%FDATE:~6,2%

set mydate=%FDATE:~0,4%_%FDATE:~4,2%_%FDATE:~6,2%


SET backupdir=%~1
SET mysqluername=%~2
SET mysqlpassword=%~3
SET database=%~4
SET backup_file=%database%_%mydate%.sql

"C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe" -u%mysqluername% -p%mysqlpassword% %database% > %backupdir%\%backup_file%

rem "C:\Program Files\MySQL\MySQL Server 8.0\bin\mysqldump.exe" -u%mysqluername% %mysqlpassword% %database% > %backupdir%\%backup_file%