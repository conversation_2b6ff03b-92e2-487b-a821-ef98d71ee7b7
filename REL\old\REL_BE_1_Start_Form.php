<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<html>

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<link rel="stylesheet" type="text/css" href="REL_BE_1_Start_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


<head>

<script>
</script>

<title>
    <?php echo 'REL / '.$_GET['ID'].' - Package Release ';?>
</title>

</head>

<body>


    <form enctype="multipart/form-data" action="" method="post">

   
        
        <?php

            include('../REL_Connexion_DB.php');
            $requete = 'SELECT * FROM tbl_released_package WHERE Creation_Date="0000-00-00" and Creation_Visa like "" and Rel_Pack_Num like "'.$_GET['ID'].'"';
            $resultat = $mysqli->query($requete);
            while ($row = $resultat->fetch_assoc())
            {
                echo '
                    <table id="t01" border=0>

                    <tr>
                        <td colspan=5 style="width:1130px;">
                            <div id="Title">
                                Package "'.$_GET['ID'].'" Overview And Release"
                            </div>
                        </td>
                        <td style="text-align:right">
                            <img src="\Common_Resources\scm_logo.png" height="35">
                        </td>
                
                    </tr>
                    <tr>
                        <td style="width:150;"><div id="Body">Engineering Owner:</div></td>
                        <td style="width:250;"><div id="InpBox"><input style="border:transparent;background:transparent;border:none;width:80px;font-size:8pt; type="text" name="Rel_Pack_Owner" value ="'.$row['Rel_Pack_Owner'].'"readonly></div></td>
                        <td style="width:150;"></td>
                        <td style="width:200;"><div id="Body">To be validated by:</div></td>
                        <td>
                            <div id="InpBox">
                            <select name="Validation_owner_fullname" type="submit" title="" style="width:120px;font-size:12"> 
                            <option value=""></option>';

                            //------------------------------>
                                $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Method' or Department like 'Laboratory' ORDER BY Fullname ASC;";
                                $resultat = $mysqli->query($requete);
                                while ($line = $resultat->fetch_assoc())
                                {
                                    echo'<option value ="'.$line['Fullname'].'">'.$line['Fullname'].'</option><br/>'; 
                                }
                            //------------------------------>
                echo '
                            </select>
                            </div> 
                        </td>
                    </tr>
                    <tr>
                        <td><div id="Body">Activity:</div></td><td><div id="InpBox">'.$row['Activity'].'</div></td>
                        <td style="width:150;"></td>
                        <td><div id="Body">Sign off:</div></td>
                        <td>
                            <div id="InpBox">
                            <select name="Validation_owner_fullname" type="submit" title="" style="width:120px;font-size:12" REQUIRED> 
                            <option value=""></option>';

                            //------------------------------>
                                $requete = "SELECT DISTINCT Fullname FROM tbl_user WHERE Department like 'Engineering' or Department like 'Industrialization' or Department like 'Method' or Department like 'Laboratory' ORDER BY Fullname ASC;";
                                $resultat = $mysqli->query($requete);
                                while ($line = $resultat->fetch_assoc())
                                {
                                    echo'<option value ="'.$line['Fullname'].'">'.$line['Fullname'].'</option><br/>'; 
                                }
                            //------------------------------>
                echo '
                            </select>
                            </div> 
                        </td>
                        
                        
                        
                    </tr>
                    <tr>
                        <td><div id="Body">EX ?:</div></td><td><div id="InpBox">'.$row['Ex'].'</div>
                        <td style="width:150;"></td>
                        <td colspan=2><div id="Body">Click the following button to send the package to validation</div></td>
                    </tr>
                    <tr>
                        <td><div id="Body">DMO:</div></td><td><div id="InpBox">'.$row['DMO'].'</div></td>
                        <td style="width:150;"></td>
                        <td style="text-align:center; vertical-align:top;" colspan=2>
                            <div id="InpBox">
                                <input class="btn blue" type="submit" style="font-size:11; vertical-align:middle;height:20px;" name="Send_Validation" value="Send to Validation" title="Send the current release package to validation" />
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td><div id="Body">Project:</div></td><td><div id="InpBox">'.$row['Project'].'</div></td>
                    </tr>
                    <tr style="padding-bottom:10px;">
                        <td><div id="Body">Observations:</div></td><td><div id="InpBox">'.$row['Observations'].'</div></td>
                    </tr>
                    ';
            
                    ?>
                </tr>

                <tr>
                    <td colspan=6>
                    <table id="t02"  border=0>
                    
                        <th style="width:110px;">Reference</th>
                        <th style="">Rev</th>
                        <th style="width:130px;">Product. Drawing</th>
                        <th style="">Rev</th>
                        <th style="width:220px;">Title</th>
                        <th style="width:110px;">Alias</th>
                        <th style="width:60px;">Action</th>
                        <th style="width:60px;">Doc Type</th>
                        <th style="width:170px;">Mat Type</th>
                        <th style="width:100px;">Inventory Imp.</th>
                        <th style="width:20px;">Ex</th>
                        <th style="width:70px;" colspan=2>Weight</th>
                        <th style="width:95px;" colspan=2>Plat. Surface</th>
                        <th style="width:70px;">Material</th>
                        <th style="width:150px;">Comments</th>

                    
                    <?php

                    $requete = 'SELECT * FROM tbl_released_drawing WHERE Rel_Pack_Num like "'.$_GET['ID'].'" ORDER BY Reference ASC';
                    $resultat = $mysqli->query($requete);
                    while ($row = $resultat->fetch_assoc())
                    {
                        echo '<tr style="background-color:#DCEEFB">
                                <td><div id="InpBox">'.$row['Reference'].'</div></td>
                                <td><div id="InpBox">'.$row['Ref_Rev'].'</div></td>
                                <td><div id="InpBox">'.$row['Prod_Draw'].'</div></td>
                                <td><div id="InpBox">'.$row['Prod_Draw_Rev'].'</div></td>
                                <td><div id="InpBox">'.$row['Ref_Title'].'</div></td>
                                <td><div id="InpBox">'.$row['Alias'].'</div></td>
                                <td><div id="InpBox">'.$row['Action'].'</div></td>
                                <td><div id="InpBox">'.$row['Doc_Type'].'</div></td>
                                <td><div id="InpBox">'.$row['Material_Type'].'</div></td>
                                <td><div id="InpBox">'.$row['Inventory_Impact'].'</div></td>
                                <td><div id="InpBox">'.$row['Ex'].'</div></td>
                                <td><div id="InpBox">'.$row['Weight'].'</div></td>
                                <td><div id="InpBox">'.$row['Weight_Unit'].'</div></td>
                                <td><div id="InpBox">'.$row['Plating_Surface'].'</div></td>
                                <td><div id="InpBox">'.$row['Plating_Surface_Unit'].'</div></td>
                                <td><div id="InpBox">'.$row['FXXX'].'</div></td>
                                <td><div id="InpBox">'.$row['Requestor_Comments'].'</div></td>
                            </tr>';
                    }
                    mysqli_close($mysqli);
                    
            }?>
        </table>




        </td>
    </tr>
    
    </table>


    </form>

    <?php 

    if (isset($_POST['Send_Validation']))
    {
        echo "<script>alert(".$_POST['Validation_owner_fullname'].")</script>";
        if ($_POST['Validation_owner_fullname']!=$_POST['Rel_Pack_Owner'])
        { 
            echo '<script>alert("The engineering owner must be the person signing off the package release - The package has then not been released.")</script>';
        } ELSE 
            {
            //echo '<script>alert("'.$_POST['Rel_Pack_Owner'].'")</script>';
            $creation_date=date("Y-m-d");;
            $creation_visa=$_POST['Validation_owner_fullname'];
            $package=$_GET['ID'];

            include('../REL_Connexion_DB.php');

            $sql = 'UPDATE tbl_released_package 
                    SET
                            Creation_Date="'.$creation_date.'",
                            Creation_VISA="'.$creation_visa.'"
                    WHERE 
                        Rel_Pack_Num="'.$package.'"';
                            
			//print_r($sql);

            $result = $mysqli->query($sql);
            mysqli_close($mysqli);
            }
        //Preparation des variables a utiliser pour la requete de mise a jour
        // $pack=$_GET['ID'];
        // $reference = $_POST['Reference'];
        // $ref_rev = $_POST['ref_rev'];
        // $prod_draw = $_POST['prod_draw'];
        // $prod_draw_rev = $_POST['prod_draw_rev'];
        // $ref_title = $_POST['ref_title'];
        //echo "<script>alert(".$pack.")</script>"; // for test only - to be delted
    }
    
    
?>

</body> 
</html>