<?php
// Ici nous créeons des listes au lieu d'avoi des tables inutiles dans la base de données

// --------------------------------------------------------------------------- DB_RELEASE

// Liste Action qui remplace tbl_action dans bdd (db_release)
$action = [
    '1' => 'Creation',
    '2' => 'Modification',
    '3' => 'Deletion'
];

$activity = [
    '1' => 'ENERGY_SIGNAL',
    '2' => 'ENERGY_POWER',
    '3' => 'METHOD',
    '4' => 'OSI',
    '5' => 'MOB_AERO',
    '6' => 'MOB_INDUS',
    '7' => 'ENERGY_RENEW'
];

// Liste Doc_Type qui remplace tbl_doc_type dans bdd (db_release)
$doc_type = [
    '1' => array('ASSY', 'Marking, BoM, Assembly, FT'),
    '2' => array('MACH', 'Machined Part (Internal or External)'),
    '3' => array('MOLD', 'Molded Part (Internal or External)'),
    '4' => array('PUR', 'Purchased'),
    '5' => array('DOC', 'PC, ID, E-, AD')
];


// Pour afficher la première valeur de la list $doc_type et la première valeur du tableau stocker a l'indice 1 :
// echo $doc_type['1'][0];


// Liste Proc_Type qui remplace tbl_proc_type dans bdd (db_release)
$proc_type = [
    '1' => 'E',
    '2' => 'F',
    '3' => 'F30'
];

// Liste Material_Type qui remplace tbl_material_type dans bdd (db_release)
$material_type = [
    '1' => array('FINISHED PRODUCT', 'Salable top level assembly'),
    '2' => array('SEMI-FINISHED PRODUCT', 'Components'),
    '3' => array('RAW MATERIAL', 'Raw material used to manufacture components'),
    '4' => array('PACKAGING', 'Packaging items'),
    '5' => array('NON VALUATED MATERIAL', 'Free-issued items. ex: cables provided by customers'),
    '6' => array('LITERATURE', 'Documentation related to a product. Ex: PC, ID, FT for products which have GA, etc...')
];

$unit = [
    '1' => array('PC', 'Piece'),
    '2' => array('kg', 'Weight'),
    '3' => array('dm', 'Length'),
    '4' => array('m', 'Length'),
    '5' => array('g', 'Weight'),
    '6' => array('mm²', 'Surface')
];

$prod_agent = [
    '1' => array('ASF', ''),
    '2' => array('ASO', ''),
    '3' => array('ASP', ''),
    '4' => array('MOU', ''),
    '5' => array('MOO', ''),
    '6' => array('USI', ''),
    '7' => array('USO', '')
];

$buyer = [
    '1' => array('DL0', 'RIGUET W.'),
    '2' => array('DL1', 'LELONG T.'),
    '3' => array('DL2', 'LOISON B.')
];

$sap_type = [
    '1' => array('FERT', 'Finished Product'),
    '2' => array('HALB', 'Semi Finished Product'),
    '3' => array('VERP', 'Packaging'),
    '4' => array('ROH', 'Material')
];

$supervisor = [
    '1' => array('PA1', 'LOUIS M.'),
    '2' => array('PA2', 'CASSEGRAIN O.'),
    '3' => array('PA3', 'MONFORTE SANCHEZ S.'),
    '4' => array('PA4', 'BOUCHENOIRE S.'),
    '5' => array('PA5', 'MACHARD P.'),
    '6' => array('PA6', 'MARTIN A.'),
    '7' => array('PA7', 'COUTADEUR J.')
];

$metro = [
    '1' => array('P1', 'First Part Inspection'),
    '2' => array('3D Final', 'Finale 3D')
];

$q_control_routing = [
    '1' => array('Z001', 'Aucune gamme de contrôle appliquée'),
    '2' => array('Z002', 'Gamme de contrôle à appliquer')
];

$q_dynamisation_rules = [
    '1' => array('Z20', 'FAI / 100 CTRL'),
    '2' => array('Z21', 'FAI / 1 CTRL / 10 SKIP / 1 CTRL'),
    '3' => array('Z22', 'CTRL'),
    '4' => array('Z23', 'DELEGATION CTRL')
];

$q_dynamisation_rules = [
    '1' => array('Z01', 'LOT QM pour entrée marchandise en réception'),
    '2' => array('Z04', 'LOT QM pour entrée marchandise en production'),
    '3' => array('Z08', 'LOT QM pour transfert de stock inspection')
];

$inventory_impact = [
    '1' => array('NO IMPACT', 'No impact on the current stock or any ongoing orders or manufacturing'),
    '2' => array('TO BE UPDATED', 'Ongoing orders in SCM, orders at suppliers or parts in stock at SCM must be reworkedn '),
    '3' => array('TO BE SCRAPPED', 'Every parts in stock, being used in assembly, or being manufactured/ordered must be scrapped ')
];



// --------------------------------------------------------------------------- DB_SCM

$division =[
    '1' => array('Energy', 'Renewable, Nuclear, Oil&Gas'),
    '2' => array('Industry', 'Railway, Defense'),
    '3' => array('Aerospace', 'Aerospace, Marine')
];

$eccn =[
    '1' => array('NOCLASS', 'Out of any military product/market'),
    '2' => array('8A002.c', 'Dual Use Products'),
    '3' => array('8A992.e', ''),
    '4' => array('ML10.A', 'Military application only')
];

$ex =[
    '1' => 'ATEX',
    '2' => 'IECEX',
    '3' => 'CSA',
    '4' => 'NO'
];

$rdo =[
    '1' => array('0578', 'SCM_ENERGY'),
    '2' => array('1229', 'SCM_ENERGY_Sensitive'),
    '3' => array('1226', 'SCM_MOB_INDUS_Sensitive'),
    '4' => array('0579', 'SCM_MOB_INDUS'),
    '5' => array('0580', 'SCM_MOB_AERO'),
    '6' => array('0581', 'SCM_MOB_AERO_Sensitive'),
    '7' => array('0825', 'SCM_MATERIAL'),
    '8' => array('0826', 'SCM_TOOLING')
];

$unit = [
    '1' => array('PC', 'Piece'),
    '2' => array('kg', 'Weight'),
    '3' => array('dm', 'Length'),
    '4' => array('m', 'Length'),
    '5' => array('g', 'Weight'),
    '6' => array('mm²', 'Surface'),
    '6' => array('cm²', 'Surface')
];