<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="utf-8" />

<link rel="stylesheet" type="text/css" href="REL_Q_Main_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">


<head>


    <script>
        // function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné
        function chkName() {
            const visa = document.getElementById("User_Choice");
            if (visa.value == "" || visa.value == "%") {
                alert("Please indicate your name prior to validate");
                return false;
            }
            var res = confirm("Etes vous sur de vouloir valider ?");
            if (res == false) {
                return false;
            }
        }

        // function qui permet de na pas valider et envoyer les données dans la bdd si le type n'est pas donné
        function chkChange() {
            const visa = document.getElementById("doc_type_change");
            if (visa.value == "" || visa.value == "%") {
                alert("Please indicate the new doc type prior to validate");
                return false;
            }
            var res = confirm("Are you sure to validate that row?");
            if (res == false) {
                visa.value="";
				return false;
            }
        }
    </script>

    <title></title>

</head>

<body>

    <form enctype="multipart/form-data" action="REL_Q_Item_Confirm.php" method="post">
        <div class="format">
            <table id="t03">

                <?php
                $query_1 = 'SELECT *
                FROM tbl_released_drawing 
                WHERE tbl_released_drawing.ID like "' . $_GET['ID'] . '"
                AND ((VISA_Product not like "" 
                AND Doc_Type like "PUR")
                OR (Doc_Type not like "PUR"
                AND VISA_Product not like "")
                OR (Proc_Type like "F"
                AND VISA_Product not like ""))
                AND VISA_Quality like ""
                ORDER BY tbl_released_drawing.reference DESC';
                include('../REL_Connexion_DB.php');
                $resultat = $mysqli->query($query_1);
                while ($ligne = $resultat->fetch_assoc()) {
                    echo '<tr>';
                    echo '<td style="width:3.2%" rowspan="2"><div id="Table_results">' . $ligne['Rel_Pack_Num'] . '</div></td>';

                    // ID recuperé pour envoi avec le formulaire - Non visible dans la page
                    echo '<input type="text" size=20 name="ID" style="height:13pt;width:30pt;" hidden readonly value="' . $_GET['ID'] . '"></div>';
                    //

                    // Si dans la champ Ex est égal à autre chose que "NO" alors on affiche EX en rouge sinon on affiche rien
                    if ($ligne['Ex'] != "NO") {
                        $ex_test = '<FONT color="red"><strong><sup>'.$ligne['Ex'].'</sup><strong></FONT>';
                    } else {
                        $ex_test = "";
                    }

                    echo '<td rowspan="2" style="width:4.8%;"><div id="Table_results">' . $_GET['Activity'] . '</br></br>' .  $_GET['Project'] . '</div></td>';
                    echo '<td rowspan="2" style="width:9.6%;"><div id="Table_results">' . $ligne['Reference'] . ' ' . $ex_test . '</div></td>';
                    echo '<td rowspan="2" style="width:1.2%;"><div id="Table_results">' . $ligne['Ref_Rev'] . '</div></td>';
                    echo '<td rowspan="2" style="width:8%;"><div id="Table_results">' . htmlspecialchars($ligne['Ref_Title'], ENT_QUOTES) . '</div></td>';
					
					echo '<td rowspan="2" style="width:9.6%;"><div id="Table_results">';
					if ($ligne['Drawing_Path']!="")
					{ 
				
				
						$path_file='DRAWINGS\\IN_PROCESS\\'.$v['Drawing_Path'];
						if (file_exists($path_file)==0)
						{
						
							$path_file='DRAWINGS\\OFFICIAL\\'.$ligne['Drawing_Path'];
							if (file_exists($path_file)==0)
							{
								$path_file="";
							}
						}
				
				
				
				
						echo '<a target=_blank href="'.$path_file.'">' . $ligne['Prod_Draw'] . '</div>
							</a>';
					} else {
						echo $ligne['Prod_Draw'] . '</div>';
					}
					echo '</td>';
					
				
					
					
                    echo '<td rowspan="2" style="width:1.2%;"><div id="Table_results">' . $ligne['Prod_Draw_Rev'] . '</div></td>';
                    echo '<td rowspan="2" style="width:3.6%;"><div id="Table_results">';
					
						// Si la ligne Action est égal à Modification alors on raccourci le mot pour ecrire "modif" à la place
						if ($ligne['Action'] == "Modification")
						{
							echo substr($ligne['Action'], 0, 5);
						} else {
							echo $ligne['Action'];
						}
						//---------
                    echo '</div></td>';

                    echo '<td style="width:2.8%;"><div id="Table_results">' . $ligne['Doc_Type'] . '</div></td>';
                    $doc_type_name = $ligne['Doc_Type'];

                    echo '<td style="width:2.4%;"><div id="Table_results">' . $ligne['Proc_Type'] . '</div></td>';
                    echo '<td rowspan="2" style="width:8%;" ><div id="Table_results">';

                    // Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
                    $nbre_lignes = substr_count(nl2br($ligne['Requestor_Comments']), "\n");
                    $nmax = 50;
                    if ((strlen($ligne['Requestor_Comments']) > $nmax)) {
                        echo htmlspecialchars(substr(nl2br($ligne['Requestor_Comments']), 0, $nmax), ENT_QUOTES);
                        echo '<div class="dropdown">';
                        echo '<span>[...]</span>';
                        echo '<div class="dropdown-content">';
                        echo '<p>' . htmlspecialchars(nl2br($ligne['Requestor_Comments']), ENT_QUOTES) . '</p>';
                        echo '</div>';
                        echo '</div>';
                    } else {
                        echo htmlspecialchars(substr(nl2br($ligne['Requestor_Comments']), 0, $nmax), ENT_QUOTES);
                    }
                    echo '</div>';
                    echo '</td>';
                    /*. $ligne['Requestor_Comments'] . '</div></td>';*/


                    echo '<td rowspan="2" style="margin-left:-20px;width:3.6%;"><div id="Table_results">';
                    $query_2 = 'SELECT *
                    FROM tbl_q_inspection_type 
                    ORDER BY Code ASC';
                    $resultat_2 = $mysqli->query($query_2);
                    while ($ligne_2 = $resultat_2->fetch_assoc()) {
                        echo '<input style="margin-left:-0px;vertical-align:middle" type="checkbox" 
                             id="inspection_' . $ligne_2['Code'] . '" 
                             name="inspection_' . $ligne_2['Code'] . '" 
                             Value="' . $ligne_2['Code'] . '" 
                             Title="' . $ligne_2['Description'] . '"';
                        if ($ligne_2['Code'] == "Z08") {
                            echo ' checked';
                        }
                        echo '> <label   for="inspection_' . $ligne_2['Code'] . '">' . $ligne_2['Code'] . '</label></br>';
                    }
                    echo '</div></td>';


 
					echo '<td rowspan="1" style="width:5.5%; border-right: 0.25px solid black;border-bottom: 0.25px dotted black;text-align:center">';
					echo '<div id="Table_results" style="font-size:95%;margin-top:-5px;margin-bottom:3px;">Gamme</div>';
					echo '<div id="Filter">
							<SELECT name="Control_Routing" id="Control_Routing" type="submit" size="1" style="font-size:95%;margin-left:-14px;width:70%;vertical-align:middle;margin-top:-5px">	
							';
                    
                    $query_2 = 'SELECT *
                    FROM tbl_q_control_routing 
                    ORDER BY Code ASC';
                    $resultat_2 = $mysqli->query($query_2);
                    $i = 1;
					
                    while ($ligne_2 = $resultat_2->fetch_assoc()) 
						{
							$selection="";
							if ($ligne_2['Code'] == "Z001")
							{
								$selection="SELECTED";
							}
							echo '<OPTION value="' . $ligne_2['Code'] . '" Title="' . $ligne_2['Description'] . '"'.$selection.'>' . $ligne_2['Code'] . '</option><br/>';
						}
						
                    echo '</div></td>';
					

                    
					


                    echo '<td rowspan="2"  style="width:19.2%; ">';
                    $query_2 = 'SELECT *
                    FROM tbl_q_doc_requirements 
                    ORDER BY Code ASC';
                    $resultat_2 = $mysqli->query($query_2);
                    $i = 0;
                    while ($ligne_2 = $resultat_2->fetch_assoc()) {
                        echo '<input style="MARGIN-TOP:0px;vertical-align:middle" type="checkbox" 
                             id="doc_req_' . $ligne_2['Code'] . '" 
                             name="doc_req_' . $ligne_2['Code'] . '" 
                             Value="' . $ligne_2['Code'] . '" 
                             Title="' . $ligne_2['Description'] . '"';

                        if (($ligne['Ex'] != "NO" && $ligne['Ex'] != "") && ($ligne_2['Code'] == "DOC_Z19"))
						{
                            echo ' checked';
                        }

                        echo '><label style="margin-left:-4px;" for="doc_req_' . $ligne_2['Code'] . '">  ' . substr($ligne_2['Code'], -3) . '</label>&nbsp';
                        //$i = $i + 1;
                        //$splt = 6;
                        //if (floor($i / $splt) == ($i / $splt) && $i > 1) {
                        //    echo '<br/>';
                        //}
                    }
                    echo '</div></td>';
                }
                mysqli_close($mysqli);
                ?>

                <!--- Création d'un textarea --->
                <td rowspan="2">
                    <div id="Table_results">
                        <textarea id="comment" style="height:58px;vertical-align:middle;width:92%" name="comment"></textarea>
                    </div>
                </td>

                <td style="width:6%;">
                    <!--- Récupération des données de la table de "tbl_user" dans la base de données pour le champ Fullname -->
                    <!--- Ce code permet d'afficher les prénom de toutes les personnes de la table tbl_user dans une checkbox --->
                    <div id="Filter">
                        <SELECT name="User_Choice" id="User_Choice" type="submit" style="font-size:95%;width:95%;height:17px;margin-left:-15px;margin-top:10px;">
                            <option value="%"></option>
                            <?php

                            // connexion à la bdd db_scm
                            include('../SCM_Connexion_DB.php');

                            // requète sql qui permet de selectionner le nom des personnes qui appartiennent au department "Quality"
                            $requete = 'SELECT DISTINCT tbl_user.Fullname
                                FROM tbl_user
                                WHERE Department like "%Quality%"';

                            // Lancement de la requete
                            $resultat = $mysqli_scm->query($requete);

                            // condition qui permet de parcourir tout le champs Fullname et recuperer ceux qui appartiennet au department Quality et de les afficher dans la combobox
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<OPTION value ="' . $row['Fullname'] . '">' . $row['Fullname'] . '</option><br/>';
                            }
                            mysqli_close($mysqli_scm);
                            ?>
                        </SELECT>
                    </div>
                </td>
				
                </tr>

                <tr>
                    <td colspan="2" style="border-top:1px black dotted">
                        <div id="Filter">
                            <SELECT name="doc_type_change" id="doc_type_change" type="submit" size="1" style="font-size:95%;margin-left:-14px;width:70%;vertical-align:middle;margin-top:-5px">
                                <option value="%"></option>
                                <?php
                                include('../REL_Connexion_DB.php');
                                $requete = 'SELECT DISTINCT tbl_doc_type.Doc_Type
                                FROM tbl_doc_type';
                                $resultat = $mysqli->query($requete);
                                while ($row1 = $resultat->fetch_assoc()) {
                                    if ($doc_type_name != $row1['Doc_Type']) {
                                        echo '<OPTION value ="' . $row1['Doc_Type'] . '">' . $row1['Doc_Type'] . '</option><br/>';
                                    }
                                }
                                mysqli_close($mysqli);
                                ?>
                            </SELECT>
                        </div>

                        <!--- création du bouton Valider dans la column "Type" qui va envoyer les données du formulaire dans la base de données --->
                        <!--- reprise de la function dans le onclick="" pour permettre de ne pas valider si les champs requis ne sont pas remplis (voir en haut de la page dans le <script>) --->
                        <input onclick="return chkChange()" type="submit" class="btn grey" style="font-size:7pt; width:45px;height:15px;vertical-align:middle;text-align:center" name="change_form" value="Change" title="Change the supply type" />
                    </td>
					
					<?php
					echo '<td style="width:6.2%; border-right: 0.25px solid black;text-align:center">';
					echo '<div id="Table_results" style="font-size:95%;margin-top:-5px;margin-bottom:3px;">Dynamisation</div>';
					echo '<div id="Filter">
							<SELECT name="dyn_rule" id="dyn_rule" type="submit" size="1" style="font-size:95%;margin-left:-14px;width:70%;vertical-align:middle;margin-top:-5px">';
                    include('../REL_Connexion_DB.php');
                     
                    $query_2 = 'SELECT *
                    FROM tbl_q_dynamisation_rules 
                    ORDER BY Code ASC';
                    $resultat_2 = $mysqli->query($query_2);
                    $i = 1;
					
                    while ($ligne_2 = $resultat_2->fetch_assoc()) 
						{
							if ($ligne_2['Code'] == "No")
							{
								echo '<OPTION value ="' . $ligne_2['Code'] . '" Title="' . $ligne_2['Description'] . '" SELECTED>' . $ligne_2['Code'] . '</option><br/>';
							} else {
								echo '<OPTION value ="' . $ligne_2['Code'] . '" Title="' . $ligne_2['Description'] . '">' . $ligne_2['Code'] . '</option><br/>';
							}
						}
                    mysqli_close($mysqli);
                    echo '</div></td>';
					
					
					?>
					

                    <td>
                        <!--- création du bouton Valider dans le champ "Validation" qui va envoyer les données du formulaire dans la base de données --->
                        <!--- reprise de la function dans le onclick="" pour permettre de ne pas valider si les champs requis ne sont pas remplis (voir en haut de la page dans le <script>)--->
                        <input onclick="return chkName()" type="submit" class="btn blue2" style="font-size:7pt; width:50px;height:15px;vertical-align:middle;text-align:center" name="valid_form" value="Sign" title="Sign off the current drawing" />
                    </td>
                </tr>

            </table>
        </div>
    </form>

</body>

</html>