<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_Q_Main_Form_styles.css">
    <link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

    <title>
    </title>

</head>

<body>
    <?php

    // Vérification des valeurs
    if (isset($_POST['valid_form']) && isset($_POST['User_Choice']) && ($_POST['User_Choice'] != '%')) {

        // creation de la variable $resultat_req_doc regroupant les $doc_req
        $query_2 = 'SELECT *
                        FROM tbl_q_doc_requirements 
                        ORDER BY Code ASC';
        include('../REL_Connexion_DB.php');
        $resultat_2 = $mysqli->query($query_2);
        $i = 0;
        $resultat_req_doc = "";
        while ($row = $resultat_2->fetch_assoc()) {
            $req_name = 'doc_req_' . $row['Code'];
            if (isset($_POST[$req_name])) {
                if ($resultat_req_doc == "") {
                    $resultat_req_doc = $_POST[$req_name];
                } else {
                    $resultat_req_doc = $resultat_req_doc . ";" . $_POST[$req_name];
                }
            }
        }
        mysqli_close($mysqli);
        //print_r($resultat_req_doc);

        // creation de la variable $resultat_ins_type regroupant les $inspection
        $query_2 = 'SELECT *
                        FROM tbl_q_inspection_type
                        ORDER BY Code ASC';
        include('../REL_Connexion_DB.php');
        $resultat_2 = $mysqli->query($query_2);
        $i = 0;
        $resultat_ins_type = "";
        while ($row = $resultat_2->fetch_assoc()) {
            $insp_name = 'inspection_' . $row['Code'];
            if (isset($_POST[$insp_name])) {
                if ($resultat_ins_type == "") {
                    $resultat_ins_type = $_POST[$insp_name];
                } else {
                    $resultat_ins_type = $resultat_ins_type . ";" . $_POST[$insp_name];
                }
            }
        }
        mysqli_close($mysqli);
        //print_r($resultat_ins_type);

        // creation de la variable $resultat_dynam regroupant les $dynamisation
        // $query_2 = 'SELECT *
                        // FROM tbl_q_dynamisation_rules
                        // ORDER BY Code ASC';
        // include('../REL_Connexion_DB.php');
        // $resultat_2 = $mysqli->query($query_2);
        // $i = 0;
        // $resultat_dynam = "";
        // while ($row = $resultat_2->fetch_assoc()) {
            // $dynam_name = 'dynam_rule';
            // if ($resultat_dynam == "") {
                // $resultat_dynam = $_POST[$dynam_name];
            // }
        // }

        // fermeture de la connexion à la bdd
        //mysqli_close($mysqli);


        // création d'autres variables
        //-----------------------------
        $id = $_POST['ID'];
		$resultat_dynam = $_POST['dyn_rule'];
        $user = $_POST['User_Choice'];
        $date_quality = date("Y-m-d");
		$control_routing = $_POST['Control_Routing'];

        // connexion à la bdd
        include('../REL_Connexion_DB.php');

        // Si le textarea dans REL_Q_Item.php n'est pas vide alors ont afficher "Product : + le message" sinon si il n'y a pas de message alors on affiche dans la bdd "Product : none"
        if ($_POST['comment'] != "") {
            $v = 'Quality :' . htmlspecialchars($_POST['comment'], ENT_QUOTES);
        } else {
            $v = "Quality: none";
        }

        // Requete qui permet de voir le champ General_Comments dans la bdd 
        $query_3 = 'SELECT General_Comments
                        FROM tbl_released_drawing
                        WHERE ID ="' . $id . '";';

        // Lancement de la requete
        $resultat = $mysqli->query($query_3);

        // On affiche notre message et à la ligne on laisse l'ancien message
        while ($row = $resultat->fetch_assoc()) {
            $v = $v . ' \r\n ' . $row['General_Comments'];
        }
        //-----------------------

        // On modifie la base de donnée pour afficher le message qui a été ecrit + nous ce qui a été selectionné
        $query_2 = 'UPDATE tbl_released_drawing 
                        SET Q_Inspection="' . $resultat_ins_type . '",
                            Q_Dynamization="' . $resultat_dynam . '",
                            Q_Doc_Req="' . $resultat_req_doc . '",
							Q_Control_Routing="'. $control_routing .'",
                            VISA_Quality="' . $user . '",
                            DATE_Quality="' . $date_quality . '",
                            General_Comments="' . $v . '"
                            WHERE ID ="' . $id . '";';

        // On lance la requete
        $resultat = $mysqli->query($query_2);

        // Recherche de la reference pour rappel dans la confirmation
        $query_ref = 'SELECT Reference, Ref_Rev
                        FROM tbl_released_drawing
                        WHERE ID ="' . $id . '";';
        $resultat = $mysqli->query($query_ref);
        while ($row = $resultat->fetch_assoc()) {
            $ref = $row['Reference'];
			$ref_rev = $row['Ref_Rev'];
        }

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message à l'utilisateur que la mise à jour à bien été faite.
        echo '<div id="confirmation_message">The reference '  .  $ref . ' rev ' . $ref_rev . ' has been pushed out of the <i>Quality Review</i> basket</div>';
    }


    if (isset($_POST['change_form'])) {
        //Connexion à BD
        include('../REL_Connexion_DB.php');

        // création des variables
        $id = $_POST['ID'];
        $doc_type = $_POST['doc_type_change'];
        $proc_type = "";
        $date_quality = "";
        $user_none = "";

        $v = "Quality : change of supply to " . $doc_type;

        // On parcours le champ General_Comments de la table released_drawing en fonction de l'id
        $query_3 = 'SELECT General_Comments
                        FROM tbl_released_drawing
                        WHERE ID ="' . $id . '";';

        // Lancement de la requete
        $resultat = $mysqli->query($query_3);

        // On affiche notre message et à la ligne on laisse l'ancien message
        while ($row = $resultat->fetch_assoc()) {
            $v = $v . ' \r\n ' . $row['General_Comments'];
        }

        // On modifie la base de donnée pour afficher le message qui a été ecrit + tout ce qui a été selectionné
        $query_2 = 'UPDATE tbl_released_drawing 
                        SET Doc_Type="' . $doc_type . '",
                            Proc_Type="' . $proc_type . '",                            
                            VISA_Quality="' . $user_none . '",
                            DATE_Quality="' . $date_quality . '",
                            General_Comments="' . $v . '"
                            WHERE ID ="' . $id . '";';
        //print_r($query_2);

        // On lance la requete
        $resultat = $mysqli->query($query_2);

        // Recherche de la reference pour rappel dans la confirmation
        $query_doc_type = 'SELECT Doc_Type
                        FROM tbl_released_drawing
                        WHERE ID ="' . $id . '";';
        $resultat = $mysqli->query($query_doc_type);
        while ($row = $resultat->fetch_assoc()) {
            $doc_type = $row['Doc_Type'];
        }

        // on ferme la connexion
        mysqli_close($mysqli);

        // Message à l'utilisateur que la mise à jour à bien été faite.
        echo '<div id="confirmation_message">The type '  .  $doc_type . ' has been change of the <i>Quality Review</i> basket</div>';
    }
    ?>
</body>

</html>