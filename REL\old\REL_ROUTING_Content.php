<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>

<meta http-equiv="X-UA-Compatible" content="IE=edge" />

<link rel="stylesheet" type="text/css" href="REL_ROUTING_Main_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<head>

</head>

<body>

    <?php

    if (isset($_GET['ID']) && (($_GET['Act']) == 'Del')) //
    {

        //Connexion BD
        include('../REL_Connexion_DB.php');

        #FOR DEBUGGING ONLY
        #print_r($_POST);

        //On recupere l'ID du plan
        $ROUTING_ID = $_GET['ID'];

        //On prépare la commande sql d'insertion
        //Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
        $sql_1 = 'DELETE FROM tbl_manuf_routing WHERE ID like "' . $ROUTING_ID . '"';
        // On lance la requete
        $resultat = $mysqli->query($sql_1);

        // on ferme la connexion
        mysqli_close($mysqli);
    }
    ?>

    <form enctype="multipart/form-data" action="" method="post">


        <?php

        include('../REL_Connexion_DB.php');

        $query_1 = 'SELECT *
        FROM tbl_manuf_routing
        WHERE ID_Released_Drawing like "' . $_GET['ID'] . '"';

        $resultat = $mysqli->query($query_1);

        echo '<table border="1">';
        echo '<th style="width:3.2%;background-color: rgb(16, 112, 177);"><div id="Table_results">Routing_Title</div></th>';
        echo '<th style="width:9.6%;background-color: rgb(16, 112, 177);"><div id="Table_results">Routing_Group</div></th>';
        echo '<th style="width:1.2%;background-color: rgb(16, 112, 177);"><div id="Table_results">Routing_Count</div></th>';
        echo '<th style="width:9.6%;background-color: rgb(16, 112, 177);"><div id="Table_results">Routing_Shop</div></th>';
        echo '<th style="width:1.2%;background-color: rgb(16, 112, 177);"><div id="Table_results">Routing_Item</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Routing_Operation</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Routing_Workcenter</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Time_Start</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Time_Preparation</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Setup</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Time_Unit</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Time_Cycle</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Time_End</div></th>';
        echo '<th style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Item_instructions</div></th>';

        while ($ligne = $resultat->fetch_assoc()) {

            echo '<tr>';
            echo '<td style="width:3.2%;" ><div id="Table_results">' . htmlspecialchars($ligne['Routing_Title'], ENT_QUOTES) . '</div></td>';
            // ID recuperé pour envoi avec le formulaire - Non visible dans la page
            echo '<input type="text" size=20 name="ID" style="height:3pt;width:3pt;" hidden readonly value="' . $_GET['ID'] . '"></div>';
            //
            echo '<td  style="width:9.6%;"><div id="Table_results">' . htmlspecialchars($ligne['Routing_Group'], ENT_QUOTES)  . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Routing_Count'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:9.6%;;"><div id="Table_results">' . htmlspecialchars($ligne['Routing_Shop'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Routing_Item'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Routing_Operation'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . $ligne['Routing_Workcenter'] . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Time_Hold_Start'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Time_Preparation'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Time_Setup'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Time_Unit'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Time_Cycle'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['Time_Hold_End'], ENT_QUOTES) . '</div></td>';
            echo '<td  style="width:8%;" ><div id="Table_results">' . htmlspecialchars($ligne['Item_Instructions'], ENT_QUOTES) . '</div></td>';

            echo '<td>';
            echo '<input type="submit" class="btn red" style="border:1px white solid ;width:20px;font-family:\'wingdings 2\';font-size:10px;" value="3" title="Delete the related row" onClick="window.location.href=\'REL_ROUTING_Content.php?ID=' . $ligne['ID'] . '&Act=Del\'">';
            echo '</td>';

            echo '</tr>';
        }
        echo '</table>';
        mysqli_close($mysqli);
        ?>
        <!------------------------------>



    </form>




</body>

</html>