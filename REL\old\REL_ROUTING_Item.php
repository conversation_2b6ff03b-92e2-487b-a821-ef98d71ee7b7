<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<meta http-equiv="X-UA-Compatible" content="IE=edge" />
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta charset="utf-8" />

<link rel="stylesheet" type="text/css" href="REL_ROUTING_Main_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<head>

    <script>
        // function qui permet de na pas valider et envoyer les données dans la bdd si le nom n'est pas donné
        function chkName() {
            const visa = document.getElementById("User_Choice");
            if (visa.value == "" || visa.value == "%") {
                alert("Please indicate your name prior to validate");
                return false;
            }
            var res = confirm("Etes vous sur de vouloir valider ?");
            if (res == false) {
                return false;
            }
        }

        function chkchamp() {
            var res = confirm("Etes vous sur de vouloir valider ?");
            if (res == false) {
                return false;
            }
            //var title = document.getElementById('Routing_Title').value;
        }
    </script>

    <title></title>

</head>

<body>

    <form enctype="multipart/form-data" action="" method="post">
        <div class="format">
            <table id="t03">
                <tr>
                    <td colspan="12" style="width:80%">
                        <?php
                        $query_1 = 'SELECT *
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE (tbl_released_drawing.VISA_ROUTING like ""
                                AND tbl_released_drawing.VISA_Prod not like ""
                                AND tbl_released_drawing.Proc_Type like "E")
                                AND tbl_released_drawing.ID like "' . $_GET['ID'] . '"
                                ORDER BY tbl_released_drawing.reference DESC';

                        include('../REL_Connexion_DB.php');
                        $resultat = $mysqli->query($query_1);
                        while ($ligne = $resultat->fetch_assoc()) {
                            echo '<table border="1">';

                            echo '<tr>';
                            echo '<td style="width:3.2%;background-color: rgb(16, 112, 177);"><div id="Table_results">Pack #</div></td>';
                            echo '<td style="width:9.6%;background-color: rgb(16, 112, 177);"><div id="Table_results">Reference</div></td>';
                            echo '<td style="width:1.2%;background-color: rgb(16, 112, 177);"><div id="Table_results">Ref_Rev</div></td>';
                            echo '<td style="width:9.6%;background-color: rgb(16, 112, 177);"><div id="Table_results">Prod_Draw</div></td>';
                            echo '<td style="width:1.2%;background-color: rgb(16, 112, 177);"><div id="Table_results">Prod_Draw_Rev</div></td>';
                            echo '<td style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">Requestor_Comments</div></td>';
                            echo '<td style="width:8%;background-color: rgb(16, 112, 177);"><div id="Table_results">General_Comments</div></td>';
                            echo '</tr>';

                            echo '<tr>';
                            echo '<td style="width:3.2%;" ><div id="Table_results">' . $ligne['Rel_Pack_Num'] . '</div></td>';
                            // ID recuperé pour envoi avec le formulaire - Non visible dans la page
                            echo '<input type="text" size=20 name="ID" style="height:3pt;width:3pt;" hidden readonly value="' . $_GET['ID'] . '"></div>';
                            //

                            $reference = $ligne['Reference'];
                            $ref_rev = $ligne['Ref_Rev'];

                            echo '<td  style="width:9.6%;"><div id="Table_results">' . $ligne['Reference']  . '</div></td>';
                            echo '<td  style="width:1.2%;"><div id="Table_results">' . $ligne['Ref_Rev'] . '</div></td>';
                            echo '<td  style="width:9.6%;;"><div id="Table_results">' . $ligne['Prod_Draw'] . '</div></td>';
                            echo '<td  style="width:1.2%;"><div id="Table_results">' . $ligne['Prod_Draw_Rev'] . '</div></td>';
                            echo '<td  style="width:8%;" ><div id="Table_results">';
                            // Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
                            $nbre_lignes = substr_count(nl2br($ligne['Requestor_Comments']), "\n");
                            $nmax = 20;
                            if ((strlen($ligne['Requestor_Comments']) > $nmax)) {
                                echo htmlspecialchars(substr(nl2br($ligne['Requestor_Comments']), 0, $nmax), ENT_QUOTES);
                                echo '<div class="dropdown">';
                                echo '<span>[...]</span>';
                                echo '<div class="dropdown-content">';
                                echo '<p>' . htmlspecialchars(nl2br($ligne['Requestor_Comments']), ENT_QUOTES) . '</p>';
                                echo '</div>';
                                echo '</div>';
                            } else {
                                echo htmlspecialchars(substr(nl2br($ligne['Requestor_Comments']), 0, $nmax), ENT_QUOTES);
                            }
                            echo '</div>';
                            echo '</td>';
                            echo '<td rowspan="2" style="width:8%;"><div id="Table_results">';
                            // Si la longueur max est dépassée alors le message est coupé mais il est stocké dans une bulle représenté comme ceci = [...] et si nous mettons notre souris dessus nous pouvons voir le msg entier
                            $nbre_lignes = substr_count(nl2br($ligne['General_Comments']), "\n");
                            $nmax = 20;
                            if ((strlen($ligne['General_Comments']) > $nmax)) {
                                echo htmlspecialchars(substr(nl2br($ligne['General_Comments']), 0, $nmax), ENT_QUOTES);
                                echo '<div class="dropdown">';
                                echo '<span>[...]</span>';
                                echo '<div class="dropdown-content">';
                                echo '<p>' . htmlspecialchars(nl2br($ligne['General_Comments']), ENT_QUOTES) . '</p>';
                                echo '</div>';
                                echo '</div>';
                            } else {
                                echo htmlspecialchars(substr(nl2br($ligne['General_Comments']), 0, $nmax), ENT_QUOTES);
                            }
                            echo '</div>';
                            echo '</td>';
                            echo '</tr>';
                            echo '</table>';
                        ?>
                    </td>
                    <td colspan="3">
                    <?php

                            echo '<table>';
                            echo '<tr>';
                            echo '<td style="width:1.2%;"><div id="Table_results">VISA_ROUTING</div></td>';
                            echo '<td  style="width:1.2%;"><div id="Table_results">' . htmlspecialchars($ligne['VISA_ROUTING'], ENT_QUOTES) . '</div></td>';
                            echo '</tr>';
                            echo '<tr>';
                            echo '<td style="width:1.2%;"><div id="Table_results">DATE_ROUTING</div></td>';
                            echo '<td  style="width:1.2%;"><div id="Table_results">' . $ligne['DATE_ROUTING'] . '</div></td>';
                            echo '</tr>';
                            echo '</table>';
                        }
                        mysqli_close($mysqli);
                    ?>
                    </td>
                </tr>

                <tr>
                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Routing_Title </br>
                            <p id="bonjour">
                                <input style="width:90%;" type="text" id="Routing_Title" name="Routing_Title" value="" size="2">
                            </p>
                        </div>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Routing_Group </br>
                            <input style="width:90%;" type="text" id="Routing_Group" name="Routing_Group" value="" size="2">
                        </div>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Routing_Count </br>
                            <input style="width:90%;" type="text" id="Routing_Count" name="Routing_Count" value="" size="2">
                        </div>
                    </td style="width:6%;">

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Routing_Shop </br>
                            <input style="width:90%;" type="text" id="Routing_Shop" name="Routing_Shop" value="" size="2">
                        </div>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Routing_Item </br>
                            <input style="width:90%;" type="text" id="Routing_Item" name="Routing_Item" size="2">
                        </div>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Routing_Ope </br>
                            <input style="width:90%;" type="text" id="Routing_Operation" name="Routing_Operation" size="2">
                        </div>
                    </td>

                    <td colspan="3" style="width:4%;">
                        <div id="FilterTitle">
                            WorkCenter
                            <SELECT name="WorkCenter" id="WorkCenter" type="submit" size="1" style="width:90%;">
                                <option value="%"></option>
                                <?php

                                // connexion à la bdd
                                include('../REL_Connexion_DB.php');

                                $requete = 'SELECT DISTINCT tbl_routing_mach.Workcenter
                                FROM tbl_routing_mach';

                                // Lancement de la requete
                                $resultat = $mysqli->query($requete);

                                while ($row = $resultat->fetch_assoc()) {
                                    echo '<OPTION value ="' . $row['Workcenter'] . '">' . $row['Workcenter'] . '</option><br/>';
                                }
                                mysqli_close($mysqli);
                                ?>
                            </SELECT>
                        </div>
                    </td>
                </tr>
                <tr>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Time_Start </br>
                            <input style="width:90%;" type="text" id="Time_Hold_Start" name="Time_Hold_Start" size="2">
                        </div>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Time_Prepa </br>
                            <input style="width:90%;" type="text" id="Time_Preparation" name="Time_Preparation" size="2">
                        </div>
                    </td>


                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Time_Setup </br>
                            <input style="width:90%;" type="text" id="Time_Setup" name="Time_Setup" size="2">
                        </div>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Time_Unit </br>
                            <input style="width:90%;" type="text" id="Time_Unit" name="Time_Unit" size="2">
                        </div>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Time_Cycle</br>
                            <input style="width:90%;" type="text" id="Time_Cycle" name="Time_Cycle" size="2">
                        </div>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <div id="FilterTitle">
                            Time_End</br>
                            <input style="width:90%;" type="text" id="Time_Hold_End" name="Time_Hold_End" size="2">
                        </div>
                    </td>

                    <td colspan="2" colspan="2" style="width:4%;">
                        <textarea id="Item_Instructions" style="font-family:Tahoma;font-size:8pt;height:50px;width:120%;vertical-align:middle; margin-top:10px;" name="Item_Instructions"></textarea>
                    </td>

                    <td colspan="2" style="width:4%;">
                        <!--- création du bouton Valider dans le champ "Validation" qui va envoyer les données du formulaire dans la base de données --->
                        <!--- reprise de la function dans le onclick="" pour permettre de ne pas valider si les champs requis ne sont pas remplis (voir en haut de la page dans le <script>)--->
                        <input onclick="return chkchamp()" type="submit" class="btn blue2" style="font-size:7pt; margin-top:10px;width:55%;height:15px;vertical-align:middle;text-align:center" name="valid_fields" value="Add" title="Sign off the current drawing" />
                    </td>
                </tr>
            </table>

            <?php

            //print_r($reference);

            $query_1 = 'SELECT ID_Released_Drawing
                FROM tbl_manuf_routing
                WHERE Reference like "' . $reference . '"';

            //print_r($reference);

            include('../REL_Connexion_DB.php');
            $resultat = $mysqli->query($query_1);
            $rowcount = mysqli_num_rows($resultat);

            //print_r($rowcount);

            if ($rowcount == 0) {
                echo '<p id="frame">';
                echo '<iframe alt="ok" src="REL_ROUTING_Content.php?ID=' . $_GET['ID'] . '&Act=" name="affiche_valid" id="affiche_valid" frameborder="0" width="100%"></iframe>';
                echo '</p>';
            } else {
                echo '<p id="frame">';
                echo '<iframe alt="ok" src="REL_ROUTING_Content.php?ID=' . $_GET['ID'] . '&Act=" name="affiche_valid" id="affiche_valid" frameborder="0" width="100%"></iframe>';
                echo '</p>';
            }

            mysqli_close($mysqli);

            ?>

            <textarea id="comment" style="font-family:Tahoma;font-size:8pt;height:85px;width:100%;vertical-align:middle; margin-top:10px;" name="comment"></textarea>

            <!--- Récupération des données de la table de "tbl_user" dans la base de données pour le champ Fullname -->
            <!--- Ce code permet d'afficher les prénom de toutes les personnes de la table tbl_user dans une combobox --->
            <div id="">
                <SELECT name="User_Choice" id="User_Choice" type="submit" size="1" style="width:30%;font-size:7.5pt;height:17px;margin-top:10px;">
                    <option value="%"></option>
                    <?php

                    // connexion à la bdd
                    include('../SCM_Connexion_DB.php');

                    // requète sql qui permet de selectionner le nom des personnes qui appartiennent au department "Quality"
                    $requete = 'SELECT DISTINCT tbl_user.Fullname, tbl_user.Department
                                FROM tbl_user';

                    // Lancement de la requete
                    $resultat = $mysqli_scm->query($requete);

                    // condition qui permet de parcourir tout le champs Fullname et recuperer ceux qui appartiennet au department Quality et de les afficher dans la combobox
                    while ($row = $resultat->fetch_assoc()) {
                        echo '<OPTION value ="' . $row['Fullname'] . '">' . $row['Fullname'] . '</option><br/>';
                    }
                    mysqli_close($mysqli_scm);
                    ?>
                </SELECT>
            </div>

            <!--- création du bouton Valider dans le champ "Validation" qui va envoyer les données du formulaire dans la base de données --->
            <!--- reprise de la function dans le onclick="" pour permettre de ne pas valider si les champs requis ne sont pas remplis (voir en haut de la page dans le <script>)--->
            <input onclick="return chkName()" type="submit" class="btn green" style="float:left; font-size:7pt; margin-top:10px; width:50px;height:15px;vertical-align:middle;text-align:center" name="valid_form" value="Valid" title="Sign off the current drawing" />
        </div>
    </form>

    <?php
    if (isset($_POST['valid_fields'])) {
        //Connexion à BD
        include('../REL_Connexion_DB.php');

        // Preparation des variables
        $routing_title = $_POST['Routing_Title'];
        $routing_group = $_POST['Routing_Group'];
        $routing_count = $_POST['Routing_Count'];
        $routing_shop = $_POST['Routing_Shop'];
        $routing_item = $_POST['Routing_Item'];
        $routing_operation = $_POST['Routing_Operation'];

        $routing_workcenter = $_POST['WorkCenter'];
        if ($routing_workcenter == "%") {
            $routing_workcenter = "";
        }
        $time_start = $_POST['Time_Hold_Start'];
        if ($time_start == "") {
            $time_start = "-";
        }
        $time_preparation = $_POST['Time_Preparation'];
        if ($time_preparation == "") {
            $time_preparation = "-";
        }
        $time_setup = $_POST['Time_Setup'];
        if ($time_setup == "") {
            $time_setup = "-";
        }
        $time_unit = $_POST['Time_Unit'];
        if ($time_unit == "") {
            $time_unit = "-";
        }
        $time_cycle = $_POST['Time_Cycle'];
        if ($time_cycle == "") {
            $time_cycle = "-";
        }
        $time_end = $_POST['Time_Hold_End'];
        if ($time_end == "") {
            $time_end = "-";
        }
        $item_instructions = $_POST['Item_Instructions'];

        if ($item_instructions != "") {
            $v = 'Routing :' . htmlspecialchars($_POST['Item_Instructions'], ENT_QUOTES);
        } else {
            $v = "Routing: none";
        }

        $date_routing_rev = date("Y-m-d");
        $routing_num = "";
        $routing_rev = "";
        $author = "";
        $reference = $reference;
        $ref_rev = $ref_rev;
        $id = "";
        $id_released_drawing = $_POST['ID'];

        // Requete qui permet de voir le champ Item_Instructions dans la bdd 
        $query_3 = 'SELECT Item_Instructions
                                FROM tbl_manuf_routing
                                WHERE ID ="' . $id_released_drawing . '";';

        // Lancement de la requete
        $resultat = $mysqli->query($query_3);

        // On affiche notre message et à la ligne on laisse l'ancien message
        while ($row = $resultat->fetch_assoc()) {
            $v = $v . ' \r\n ' . $row['Item_Instructions'];
        }
        //On prépare la commande sql d'insertion
        $sql_1 = 'INSERT INTO tbl_manuf_routing VALUES (
                    "' . $id . '",
                    "' . $routing_num . '",
                    "' . $routing_rev . '",
                    "' . $author . '",
                    "' . $date_routing_rev . '",
                    "' . $id_released_drawing . '",
                    "' . $reference . '",
                    "' . $ref_rev . '",
                    "' . $routing_title . '",
                    "' . $routing_group . '",
                    "' . $routing_count . '",
                    "' . $routing_shop . '",
                    "' . $routing_item . '",
                    "' . $routing_operation . '",
                    "' . $routing_workcenter . '",
                    "' . $time_start . '",
                    "' . $time_preparation . '",
                    "' . $time_setup . '",
                    "' . $time_unit . '",
                    "' . $time_cycle . '",
                    "' . $time_end . '",
                    "' . $item_instructions . '"
                    )';

        // On lance la requete
        $resultat = $mysqli->query($sql_1);
        // on ferme la connexion
        mysqli_close($mysqli);
        // Message à l'utilisateur que la mise à jour à bien été faite.
        echo '<div id="confirmation_message">The type '  .  $routing_title . ' has been change of the <i>Routing Review</i> basket</div>';
    }

    if (isset($_POST['valid_form']) && isset($_POST['User_Choice']) && ($_POST['User_Choice'] != '%')) {

        // création des variables
        //-----------------------
        $id = $_POST['ID'];
        $user = $_POST['User_Choice'];
        $date_routing = date("Y-m-d");

        //Connexion à BD
        include('../REL_Connexion_DB.php');

        // Preparation Commentaire
        if ($_POST['comment'] != "") {
            $v = 'Routing :' . htmlspecialchars($_POST['comment'], ENT_QUOTES);
        } else {
            $v = "Routing : none";
        }

        $query_3 = 'SELECT General_Comments
                        FROM tbl_released_drawing
                        WHERE ID ="' . $id . '";';

        // on lance la requete
        $resultat = $mysqli->query($query_3);

        while ($row = $resultat->fetch_assoc()) {
            $v = $v . ' \r\n ' . $row['General_Comments'];
        }
        //-----------------------

        $query = 'UPDATE tbl_released_drawing 
                            SET DATE_ROUTING="' . $date_routing . '",
                                VISA_ROUTING="' . $user . '",
                                General_Comments="' . $v . '"
                                WHERE ID ="' . $id . '";';

        // On lance la requete
        $resultat = $mysqli->query($query);


        // Recherche de la reference pour rappel dans la confirmation
        $query_2 = 'SELECT Reference
                            FROM tbl_released_drawing
                            WHERE ID ="' . $id . '";';
        $resultat = $mysqli->query($query_2);
        while ($row = $resultat->fetch_assoc()) {
            $ref = $row['Reference'];
        }

        // Message à l'utilisateur que la mise à jour à bien été faite.
        echo '<div id="confirmation_message"> The reference ' .  $ref . ' has been removed from the <i>Finance Review</i> basket </div>';

        // on ferme la connexion
        mysqli_close($mysqli);
    }
    ?>

</body>

</html>