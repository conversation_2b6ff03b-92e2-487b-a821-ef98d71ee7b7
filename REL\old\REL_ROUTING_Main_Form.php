<?php
    require('login.php');
    login(explode("\\", $_SERVER['REMOTE_USER']));
?>


<!DOCTYPE html>
<html lang="fr">

<head>
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="REL_ROUTING_Main_Form_styles.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

    <title>
        REL Pack - Routing Review
    </title>

    <script>
        function frame_update(obj) {
            cel_row_data = 0;
            document.getElementById("main_frame").innerHTML = '<iframe alt="ok" src="REL_ROUTING_Item.php?ID=' + obj.cells[cel_row_data].textContent + '" name="Main_target" id="Main_target" scrolling="no" frameborder="0" height="100%" width="100%"></iframe>';
        }
    </script>

</head>

<body>


    <form enctype="multipart/form-data" action="" method="post">

        <table id="t01" border="0" style="vertical-align:top;">

            <tr>
                <td colspan="4">
                    <div id="Title">
                        Routing
                    </div>
                </td>
                <td>
                </td>
                <td>
                    <img src="\Common_Resources\scm_logo.png" height="45" width="100px">
                </td>
            </tr>

            <tr>
                <td style="width:10%;vertical-align:top;text-align:right; background-color:rgb(206,206,206)">
                    <div id="FilterTitle">
                        Package #
                        <!-- Titre -->
                    </div>

                    <!--- Récupération des données de la table tbl_released_drawing de "Rel_Pack_Num" dans la base de données pour le champ Pack-->
                    <div id="Filter">
                        <SELECT name="Rel_Pack_Num_Choice" type="submit" style="font-size:9pt;">
                            <option value="%"></option>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_drawing.Rel_Pack_Num 
                                            FROM tbl_released_drawing 
                                            WHERE (tbl_released_drawing.VISA_ROUTING like ""
                                              AND tbl_released_drawing.VISA_Prod not like ""
                                              AND tbl_released_drawing.Proc_Type like "E")
                                            ORDER BY tbl_released_drawing.Rel_Pack_Num DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<OPTION value ="' . $row['Rel_Pack_Num'] . '">' . $row['Rel_Pack_Num'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                        <!--</datalist>-->
                    </div>
                    </br>
                    <div id="FilterTitle">
                        Project
                        <!---Titre --->
                    </div>

                    <!--- Récupération des données de la table tbl_released_package de "Project" dans la base de données pour le champ Project-->
                    <div id="Filter">
                        <SELECT name="Project_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_package.Project 
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE (tbl_released_drawing.VISA_ROUTING like ""
                                              AND tbl_released_drawing.VISA_Prod not like ""
                                              AND tbl_released_drawing.Proc_Type like "E")
                                ORDER BY tbl_released_package.Project DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<OPTION value ="' . $row['Project'] . '">' . $row['Project'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                    </br>
                    <div id="FilterTitle">
                        Reference
                        <!-- Titre -->
                    </div>
                    <!--- Création d'une zone de texte -->
                    <div id="Filter">
                        <input type="text" size=20 name="Reference_Choice" style="font-size:8pt;height:9pt;width:100pt;">
                    </div>
                    </br>
                    <div id="FilterTitle">
                        Ref_Rev
                        <!-- Titre -->
                    </div>
                    <!--- Création d'une zone de texte -->
                    <div id="Filter">
                        <input type="text" size=20 name="Ref_Rev" style="font-size:8pt;height:9pt;width:20pt;">
                    </div>
                    </br>
                    <div id="FilterTitle">
                        EX
                        <!---Titre --->
                    </div>

                    <!--- Récupération des données de la table tbl_released_package de "Project" dans la base de données pour le champ Project-->
                    <div id="Filter">
                        <SELECT name="ex_Choice" type="submit" size="1" style="width:80px;font-size:9pt;height:17px">
                            <OPTION value="%"></OPTION>
                            <?php
                            include('../REL_Connexion_DB.php');
                            $requete = 'SELECT DISTINCT tbl_released_package.Ex 
                                FROM tbl_released_package 
                                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                                WHERE (tbl_released_drawing.VISA_ROUTING like ""
                                              AND tbl_released_drawing.VISA_Prod not like ""
                                              AND tbl_released_drawing.Proc_Type like "E") 
                                ORDER BY tbl_released_package.Project DESC';
                            $resultat = $mysqli->query($requete);
                            while ($row = $resultat->fetch_assoc()) {
                                echo '<OPTION value ="' . $row['Ex'] . '">' . $row['Ex'] . '</option><br/>';
                            }
                            mysqli_close($mysqli);
                            ?>
                        </SELECT>
                    </div>
                    </br>
                    <!--- Les boutons en bout de ligne "Apply Filters" et "Reset" --->
                    <input onclick="" type="submit" value="Apply Filters" title="Apply the picked filters to the list below " Name="Filter" style="font-size:7pt;margin-bottom:3pt;margin-right:3pt" />
                    <br>
                    <input onclick="" type="submit" value="Reset" title="Reset all the previously-applied filters" Name="Reset_Filter" style="font-size:7pt;margin-bottom:3pt" />

                </td>
                <!--- Vérification des valeurs --->
                <?php

                if (isset($_POST['Rel_Pack_Num_Choice']) == false) {
                    $rel_pack_num_choice = "%";
                } else {
                    $rel_pack_num_choice = $_POST['Rel_Pack_Num_Choice'];
                }

                if (isset($_POST['Project_Choice']) == false) {
                    $project_choice = "%";
                } else {
                    $project_choice = $_POST['Project_Choice'];
                }

                if (isset($_POST['Reference_Choice']) == false) {
                    $reference_choice = "%";
                } else {
                    if (strlen($_POST['Reference_Choice']) > 0) {
                        $reference_choice = str_replace("*", "%", $_POST['Reference_Choice']);
                    } else {
                        $reference_choice = "%";
                    }
                }

                if (isset($_POST['Ref_Rev']) == false) {
                    $Ref_Rev = "%";
                } else {
                    if (strlen($_POST['Ref_Rev']) > 0) {
                        $Ref_Rev = str_replace("*", "%", $_POST['Ref_Rev']);
                    } else {
                        $Ref_Rev = "%";
                    }
                }


                echo '<td style="width:20%;vertical-align:top;border-right:0.5px black solid;border-left:0.5px black solid">';

                // Création des filtres
                $query_1 = 'SELECT *
                FROM tbl_released_package 
                INNER JOIN  tbl_released_drawing ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE  ( tbl_released_drawing.VISA_ROUTING like ""
                    AND tbl_released_drawing.VISA_Prod not like ""
                    AND tbl_released_drawing.Proc_Type like "E") 
                    AND tbl_released_drawing.Rel_Pack_Num like "' . $rel_pack_num_choice . '"
                    AND tbl_released_drawing.Reference like "' . $reference_choice . '"
                    AND tbl_released_drawing.Ref_Rev like "' . $Ref_Rev . '"
                    AND tbl_released_package.Project like "' . $project_choice . '"';
                //print_r($query_1);
                include('../REL_Connexion_DB.php');
                $resultat = $mysqli->query($query_1);
                $rowcount = mysqli_num_rows($resultat);
                print_r($rowcount);

                /*if (isset($_POST['Rel_Pack_Num_Choice'])) {
                    echo 'Release #: ' . $_POST['Rel_Pack_Num_Choice'] . '&nbsp&nbsp&nbsp';
                }
                if (isset($_POST['Project_Choice'])) {
                    echo 'Project: ' . $_POST['Project_Choice'] . '&nbsp&nbsp&nbsp';
                }
                if (isset($_POST['Reference_Choice'])) {
                    echo 'Reference: ' . $_POST['Reference_Choice'] . '&nbsp&nbsp&nbsp';
                }
                if (isset($_POST['Ref_Rev'])) {
                    echo 'Ref_Rev: ' . $_POST['Ref_Rev'] . '&nbsp&nbsp&nbsp';
                }*/
                //print_r($resultat);



                echo '<table id="t02" style="height:100px;">';
                echo '<th style="background-color: rgb(16, 112, 177);">Package #</th>';
                echo '<th style="background-color: rgb(16, 112, 177);">Project</th>';
                echo '<th style="background-color: rgb(16, 112, 177);">Reference</th>';
                echo '<th style="background-color: rgb(16, 112, 177);">Rev</th>';
                echo '<th style="background-color: rgb(16, 112, 177);">Proc_Type</th>';
                echo '<th style="background-color: rgb(16, 112, 177);">Material_Type</th>';

                while ($row = $resultat->fetch_assoc()) {

                    echo '<tr onclick="frame_update(this)" >';
                    echo '<td hidden>';
                    echo $row['ID'];
                    echo '</td>';
                    echo '<td style="">';
                    echo '<a target ="_blank" href="REL_Pack_Overview.php?ID='.$row['Rel_Pack_Num'].'"> '.$row['Rel_Pack_Num'].'</a>';
                    echo '</td>';
                    echo '<td style="padding:5px;">';
                    echo $row['Project'];
                    echo '</td>';
                    echo '<td style="padding:5px;">';
                    echo $row['Reference'];
                    echo '</td>';
                    echo '<td style="padding:5px;">';
                    echo $row['Ref_Rev'];
                    echo '</td>';
                    echo '<td style="padding:5px;">';
                    echo $row['Proc_Type'];
                    echo '</td>';
                    echo '<td style="padding:5px;">';
                    echo $row['Material_Type'];
                    echo '</td>';
                    echo '</tr>';
                }
                echo '</table>';
                echo '</td>';

                echo '<td colspan="2" style="width:80%">';
                //création du tableau dans une iframe -->
                echo '<p id="main_frame" style="height:2080px;">';
                echo '<iframe alt="ok" src="" name="Main_target" id="Main_target" frameborder="0" width="100%"></iframe>';
                echo '</p>';
                echo '</td>';
                mysqli_close($mysqli);
                ?>
            </tr>
        </table>
    </form>
</body>

</html>