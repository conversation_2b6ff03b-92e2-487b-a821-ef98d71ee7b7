body {
  background-color: rgb(255, 255, 255);
  color: black;
  font-size: 8pt;
  font-family: Tahoma, sans-serif;
  font-weight: normal;
}

div.format {
  /*background-color: lightgrey;*/
}

@media screen and (min-width: 1370px) {
  div.format {
    font-size:9pt;
  }
}

@media screen and (max-width: 1369px) {
  div.format {
    font-size:8pt;
  }
}

#Title {
  font-size: 12pt;
  font-weight: bolder;
  border-bottom: 1px solid black;
  font-variant: small-caps;
  background: linear-gradient(90deg, #e9edef, white);
}

#Filter {
  margin-left: 2px;
  margin-right: 2px;
  margin-top: 2px;
  text-align: left;
  vertical-align: middle;
}

#FilterTitle {
  /* font-size: 7.5pt;*/
  margin-left: 2px;
  margin-right: 2px;
  text-align: left;
  vertical-align: middle;
  background: transparent;
  font-weight: bold;
}

#confirmation_message {
  font-weight: bold;
  font-family: Tahoma, sans-serif;
  text-align: center;
  margin-top: 25px;
}

div#Result_info {
  text-indent: 10px;
  margin-left: 8px;
  margin-bottom: 8px;
  text-align: justify;
}

#t01 {
  border-collapse: collapse;
  vertical-align: middle;
  table-layout: fixed;
}

#t01 td {
  text-align: left;
}

#t02 {
  border-collapse: collapse;
  width: 100%;
  overflow-y: auto;
}

#t02 th {
  border: 0.5px solid black;
  background-color: rgb(27, 79, 114);
  color: white;
  font-family: Arial, Helvetica, sans-serif;
  text-align: center;
}

#t02 td {
  text-align: center;
  vertical-align: middle;
}
#t02 tr {
  height: 20px;
}

#t02 tr:hover {
  background-color: rgb(76, 126, 160);
  color: white;
  cursor: pointer;
}

div#Table_results {
  text-align: center;
  color: black;
}

#t03 {
  table-layout: fixed;
  border-collapse: collapse;
  width: 100vw;
  border-top: 0.25px solid black;
  border-bottom: 0.25px solid black;
  margin-top: -10px;
  margin-left: -9px;
  height: 78px;
}

#t03 tr {
  /*height:2vw;*/
}

#t03 td {
  text-align: center;
  vertical-align: middle;
  /*border-right: 0.25px solid black;*/
}

