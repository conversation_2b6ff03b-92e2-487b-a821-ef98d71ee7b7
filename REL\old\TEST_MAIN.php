<?php
	// require('login.php');
	// login(explode("\\", $_SERVER['REMOTE_USER']));
?>

<!DOCTYPE html>
<html translate="no">

<head>
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">

<link rel="stylesheet" type="text/css" href="REL_Welcome_styles.css">
<link rel="stylesheet" type="text/css" href="REL_vertical_tab.css">
<link rel="stylesheet" type="text/css" href="\Common_Resources\REL_Button_styles.css">
<!-- <link rel="stylesheet" type="text/css" href="\Common_Resources\icon.min.css"> -->
<link rel="stylesheet" type="text/css" href="REL_Dropdown_Item_Style.css">

<link rel="icon" type="image/png" href="\Common_Resources\logo_scm_TS_admin.png" />


	<script>
	
		// COUNT DECREMENTATION 
		// --------------------
		function count_update(control_name)
		{

			if (control_name!="")
			{
				let curr=parseInt(document.getElementById(control_name).innerHTML);
				curr--;
				if (curr>=0)
				{
					document.getElementById(control_name).innerHTML=curr;
				} else {
					window.parent.document.getElementById(control_name).innerHTML=0;
				}
			}
		}
		// --------------------------
	
		// GESTION DU BOUTON DE MASQUAGE/AFFFICHAGE DE LA BANNIERE DE GAUCHE
		// ------------------------------------------------------------------
		function banner_toggle_action() {
			if (document.getElementById("left_banner").style.display != "none") {
				document.getElementById("left_banner").style.display = "none";
				document.getElementById("main_frame_id").setAttribute("class", "main_frame_extended");
				document.getElementById("left_banner_section").style.display = "inline";
				//document.getElementById("button_5_reduced").style.display="inline";
			} else {
				document.getElementById("left_banner").style.display = "inline";
				document.getElementById("main_frame_id").setAttribute("class", "main_frame_reduced");
				document.getElementById("left_banner_section").style.display = "none";
				//document.getElementById("button_5_reduced").style.display="none";
			}
		}
		// -------------------------------------------------------------------

		//
 
		function title_toggle_action() {

			if (document.getElementById("title_banner").style.display != "none") {
				document.getElementById("title_banner").style.display = "none";
				document.getElementById("title_toggle").style.top = 0;
				document.getElementById("title_toggle").style.left = "calc(100% - 102px)";
				document.getElementById("logo_scm").style.height = "30px";
				document.getElementById("main_frame_id").style.height = "100%";
				document.getElementById("main_frame_id").style.height = "calc(100% - 32px)";
				document.getElementById("main_frame_id").style.top = "30px";
				document.getElementById("banner_toggle").style.top = "0px";
				document.getElementById("title_banner_reduced").style.display = "block";

			} else {
				document.getElementById("title_banner").style.display = "block";
				document.getElementById("banner_toggle").style.top = "68px";
				document.getElementById("logo_scm").style.display = "block";
				document.getElementById("logo_scm").style.height = "68px";
				document.getElementById("main_frame_id").style.height = "calc(100% - 70px)";
				document.getElementById("main_frame_id").style.top = "68px";
				document.getElementById("title_banner_reduced").style.display = "none";
				document.getElementById("title_toggle").style.left = "calc(100% - " + document.getElementById("logo_scm").width + "px - " + document.getElementById("title_toggle").style.width + ")";
				document.getElementById("title_toggle").style.top = "calc(68px - " + document.getElementById("title_toggle").style.width + ")";
			}
		}


		// FONCTION AU CHARGEMENT DE LA PAGE
		// ---------------------------------
		function welcome_page_loading() {
			//document.getElementById("main_iframe").src="REL_Overview_form.php";
		}
		// ---------------------------------


		// MISE EN GRAS DU MENU SUR LEQUEL L'UTILISATEUR CLIQUE
		//-----------------------------------------------------
		function focus_in(el) {
			let i = 1;
			const list_button = document.getElementsByClassName("w3-bar-item w3-button");
			while (i <= list_button.length) {
				document.getElementById("button_" + i).style.fontWeight = 100;
				i++;
			}
			el.style.fontWeight = 700;
		}


		// VERIFICATION DU PASSWORD RENTRE POUR ALLER DANS LA PAGE ADMINISTRATION
		// ----------------------------------------------------------------------
		function Admin_PWD_Check() {
			const xhttp = new XMLHttpRequest();
			xhttp.onload = function() {
				var mdp = prompt("Password", "");
				if (mdp != "") {
					pwd = this.responseText.trim();
					if (mdp == pwd) {
						const url = "REL_Admin_Form.php";
						window.open(url);
					} else if (mdp != "" && mdp != null) {
						window.alert("Incorrect Password!");
					} else {}
				}
			}
			xhttp.open("GET", "REL_Admin_PWD.php");
			xhttp.send();
		}

		function reduced_banner_target(page_url) {
			document.getElementById("main_frame_id").src = page_url;
		}

		//Affecte la nouvelle image lorsque la souris survole l'élément
		function passageDeLaSouris(element) {
			element.setAttribute('src', '/Common_Resources/cadena-icon-open.png');
		}
		//Affecte l'image de départ lorsque la souris ne survole plus l'élément
		function departDeLaSouris(element) {
			element.setAttribute('src', "/Common_Resources/cadena-icon-close.png");
		}
	</script>
</head>



<title>RELEASE Process - Welcome </title>

<?php
// DEFINITION DE LA CONDITION D'ENTREE DANS CETTE PAGE
include('REL_Workflow_Conditions.php');
?>


<?php

$query_be = 'SELECT  count(*) as "counter"
                FROM tbl_released_package
                INNER JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE 
					' . $BE_1_condition . ' OR ' . $BE_2_condition . ' OR ' . $BE_3_condition;

$query_product = 'SELECT  count(*) as "counter"
                FROM tbl_released_package
                LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE 
					' . $Product_Conditions;

$query_inven = 'SELECT  count(*) as "counter"
                FROM tbl_released_package
                LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
                WHERE 
					' . $Inventory_Conditions;

$query_qual = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Quality_Conditions;

$query_metro = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $METRO_Conditions;

$query_q_prod = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Q_PROD_Conditions;

$query_assy = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Prod_ASSY_Conditions;

$query_method = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Method_Conditions;

$query_mach = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Prod_MACH_Conditions;

$query_mold = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Prod_MOLD_Conditions;

$query_supply = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Supply_Conditions;

$query_assy_routing = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $MOF_Conditions;

$query_gid_1 = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $GID_1_Conditions;

$query_gid_2 = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $GID_2_Conditions;

$query_fin = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Finance_Conditions;

$query_pur = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $PUR_1_RFQ_Conditions . ' OR ' . $PUR_2_PRISDANS_Conditions . ' OR ' . $PUR_3_Conditions;

$query_labo = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $LABO_Conditions;

$query_routing_entry = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $ROUTING_ENTRY_Conditions;

$query_project = 'SELECT  count(*) as "counter"
				FROM tbl_released_package
				LEFT JOIN  tbl_released_drawing  ON tbl_released_drawing.Rel_Pack_Num=tbl_released_package.Rel_Pack_Num
				WHERE 
					' . $Project_Conditions;

include('../REL_Connexion_DB.php');

$resultat = $mysqli->query($query_be);
$be_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$be_counter = $row['counter'];
}

$resultat = $mysqli->query($query_product);
$product_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$product_counter = $row['counter'];
}

$resultat = $mysqli->query($query_inven);
$inven_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$inven_counter = $row['counter'];
}

$resultat = $mysqli->query($query_qual);
$qual_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$qual_counter = $row['counter'];
}

$resultat = $mysqli->query($query_metro);
$metro_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$metro_counter = $row['counter'];
}

$resultat = $mysqli->query($query_q_prod);
$q_prod_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$q_prod_counter = $row['counter'];
}

$resultat = $mysqli->query($query_assy);
$assy_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$assy_counter = $row['counter'];
}

$resultat = $mysqli->query($query_method);
$method_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$method_counter = $row['counter'];
}

$resultat = $mysqli->query($query_mach);
$mach_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$mach_counter = $row['counter'];
}

$resultat = $mysqli->query($query_mold);
$mold_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$mold_counter = $row['counter'];
}

$resultat = $mysqli->query($query_supply);
$supply_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$supply_counter = $row['counter'];
}

$resultat = $mysqli->query($query_assy_routing);
$assy_routing_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$assy_routing_counter = $row['counter'];
}

$resultat = $mysqli->query($query_gid_1);
$gid_1_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$gid_1_counter = $row['counter'];
}

$resultat = $mysqli->query($query_gid_2);
$gid_2_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$gid_2_counter = $row['counter'];
}

$resultat = $mysqli->query($query_fin);
$fin_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$fin_counter = $row['counter'];
}

$resultat = $mysqli->query($query_pur);
$pur_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$pur_counter = $row['counter'];
}

$resultat = $mysqli->query($query_labo);
$labo_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$labo_counter = $row['counter'];
}


$resultat = $mysqli->query($query_routing_entry);
$routing_entry = 0;
while ($row = $resultat->fetch_assoc()) {
	$routing_entry = $row['counter'];
}

$resultat = $mysqli->query($query_project);
$project_counter = 0;
while ($row = $resultat->fetch_assoc()) {
	$project_counter = $row['counter'];
}

mysqli_close($mysqli);


?>

<body onload="welcome_page_loading()">

	<!-- TITRE PRINCIPALE & BANNIERE -->
	<!--------------------------------->
	<input type="submit" id="title_toggle" name="title_toggle" class="btn blue2" style="border-radius:1px;font-size:11;position:absolute;z-index:99;left:calc(100% - 193px);top:38px;width:30px;height:30px;text-align:center;" value="&#9776" title="title banner toggle" onclick="title_toggle_action()" />
	<div id="title_banner" class="w3-container w3-blue-scm1">
		<h1>Release Process Tool</h1>
	</div>
	<div hidden id="title_banner_reduced" class="w3-container w3-blue-scm1" style="height:30px">
		<div id="reduced_title">Release Process Tool</div>
	</div>
	<a Target="main_iframe" href="REL_Overview_Form.php"><img id="logo_scm" src="\Common_Resources\scm_logo.png" height="68px" style="position: absolute; top:0px; right:0px; filter:grayscale(20%)"></a>

	<!--------------------------------->


	<!-- BANNIERE GAUCHE REDUITE -->
	<!------------------------------>
	<input type="submit" id="banner_toggle" name="banner_toggle" class="btn grey" style="border-radius:1px;font-size:11;position:absolute;z-index:99;top:68px;width:30px;height:30px;text-align:center" value="|||" title="toggle left banner" onclick="banner_toggle_action()" />
	<div hidden id="left_banner_section" class="w3-sidebar w3-light-grey w3-bar-block" style="width:30px">
		<!-- ICONE POUR ACCES VERS LES AUTRES PAGES -- A FINALISER -->
		<input onclick="reduced_banner_target('REL_Drawing_Status.php')" type="image" src="\Common_Resources\search_icon.png" class="" style="position:absolute;z-index:99;top:35px;width:27px;margin-left:3px;text-align:center" title="Status" />
		<input onclick="reduced_banner_target('REL_BE_Form.php')" type="image" src="\Common_Resources\engineering2_icon.png" class="" style="position:absolute;z-index:99;top:70px;width:27px;margin-left:3px;text-align:center" title="Engineering" />
		<input onclick="reduced_banner_target('REL_PRODUCT_Main_Form.php')" type="image" src="\Common_Resources\product_icon.png" class="" style="position:absolute;z-index:99;top:109px;width:27px;margin-left:3px;text-align:center" title="Product Management" />
		<!--<input  onclick="reduced_banner_target('REL_BE_Form.php')" type="submit" class="btn grey" style="font-size:11;position:absolute;z-index:99;top:70px;width:30px;height:30px;text-align:center"  value="E" title="Engineering" />-->
		<input onclick="reduced_banner_target('REL_Q_Main_Form.php')" type="image" src="\Common_Resources\qualite_icon.png" type="submit" class="" style="position:absolute;z-index:99;top:150px;width:27px;margin-left:3px;text-align:center" width="100%" value="Q" title="Quality" />
		<input onclick="reduced_banner_target('REL_Project_Main_Form.php')" type="image" src="\Common_Resources\project_icon.png" type="submit" class="" style="position:absolute;z-index:99;top:190px;width:27px;margin-left:3px;text-align:center" width="100%" value="P" title="Project" />
		<input onclick="reduced_banner_target('REL_METRO_Main_Form.php')" type="image" src="\Common_Resources\metro_icon.png" type="submit" class="" style="position:absolute;z-index:99;top:230px;width:27px;margin-left:3px;text-align:center" width="100%" value="Metro" title="Metro" />
		<input onclick="reduced_banner_target('REL_PROD_ASSY_Main_Form.php')" type="image" src="\Common_Resources\assemblage_icon.png" class="" style="position:absolute;z-index:99;top:270px;width:27px;margin-left:3px;text-align:center" value="A" title="Assemblage" />
		<input onclick="reduced_banner_target('REL_METH_Main_Form.php')" type="image" src="\Common_Resources\method_icon.jpg" class="" style="position:absolute;z-index:99;top:310px;width:27px;margin-left:3px;text-align:center" value="M" title="Method" />
		<input onclick="reduced_banner_target('REL_PROD_MACH_Main_Form.php')" type="image" src="\Common_Resources\machining-icon.png" class="" style="position:absolute;z-index:99;top:355px;width:27px;margin-left:3px;text-align:center" value="M" title="Machining" />
		<input onclick="reduced_banner_target('REL_PROD_MOLD_Main_Form.php')" type="image" src="\Common_Resources\molding_icon.png" class="" style="position:absolute;z-index:99;top:400px;width:27px;margin-left:3px;text-align:center" value="Mold" title="Molding" />
		<input onclick="reduced_banner_target('REL_PUR_Main_Form.php')" type="image" src="\Common_Resources\achat_icon.png" class="" style="position:absolute;z-index:99;top:445px;width:27px;margin-left:3px;text-align:center" value="Achat" title="Purchasing" />
		<input onclick="reduced_banner_target('REL_SUPPLY_Main_Form.php')" type="image" src="\Common_Resources\supply_icon.png" class="" style="position:absolute;z-index:99;top:490px;width:27px;margin-left:3px;text-align:center" value="supply" title="Supply" />
		<input onclick="reduced_banner_target('REL_MOF_Main_Form.php')" type="image" src="\Common_Resources\mof_icon.png" class="" style="position:absolute;z-index:99;top:535px;width:27px;margin-left:3px;text-align:center" value="MOF" title="Assembly Routing" />
		<input onclick="reduced_banner_target('REL_GID_Main_Form.php')" type="image" src="\Common_Resources\icon_sap.png" class="" style="position:absolute;z-index:99;top:578px;width:27px;margin-left:3px;text-align:center" value="GID" title="Core Data" />
		<input onclick="reduced_banner_target('REL_GID_2_Main_Form.php')" type="image" src="\Common_Resources\icon_sap.jpg" class="" style="position:absolute;z-index:99;top:608px;width:27px;margin-left:3px;text-align:center" value="GID 2" title="Prod Data" />
		<input onclick="reduced_banner_target('REL_LABO_Main_Form.php')" type="image" src="\Common_Resources\labo_icon.png" class="" style="position:absolute;z-index:99;top:635px;width:27px;margin-left:3px;text-align:center" value="LABO" title="Laboratory routing review" />
		<input onclick="reduced_banner_target('REL_FIN_Main_Form.php')" type="image" src="\Common_Resources\finance_icon.png" class="" style="position:absolute;z-index:99;top:677px;width:27px;margin-left:3px;text-align:center" value="F" title="Costing" />
		<input Target="_blank" onclick="Admin_PWD_Check()" type="image" src="\Common_Resources\cadena-icon-close.png" onmouseover="passageDeLaSouris(this)" onmouseout="departDeLaSouris(this)" class="" style="position:absolute;z-index:99;bottom:70px;width:27px;margin-left:3px;text-align:center" value="Ad" title="Administrateur" />
	</div>
	<!------------------------------>


	<!-- SIDEBAR LEFT -->
	<!------------------>
	<div id="left_banner" class="w3-sidebar w3-light-grey w3-bar-block" style="width:13%;">
		<h5 class="w3-bar-item" style="margin-top:20px;margin-bottom:-10px">Process Overview</h5>
		<?php $i = 1;
		$var_but = "button_" . $i; ?>
		<!--<a Target="main_iframe" href="REL_Overview_Form.php" id="button_1" class="w3-bar-item w3-button" style="font-size:12px" onclick="focus_in(this)">Overview</a>-->
		<!--<a Target="main_iframe" href="REL_Package_Status.php" id="button_1" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">Package Followup</a>-->
		<a Target="main_iframe" href="REL_Drawing_Status.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">Reference Followup</a>
		
		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<h5 class="w3-bar-item" style="margin-top:-10px;margin-bottom:-10px">Department</h5>
		<a Target="main_iframe" href="REL_BE_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Engineering</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $be_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_PRODUCT_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Product Management</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $product_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_Inven_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Inventory</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $inven_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_Q_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Quality</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $qual_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_Project_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Project</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $project_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_METRO_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Metrology</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $metro_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_Q_PROD_Main.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Quality prod</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $q_prod_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_PROD_ASSY_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Assembly</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $assy_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_METH_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Assy. Methods</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $method_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_PROD_MACH_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Machining</td>
					<td id="id_count_fin"  style="text-align:right;font-size:8pt"> <?php echo $mach_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_Prod_MOLD_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Molding</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $mold_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_PUR_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Purchasing</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $pur_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_SUPPLY_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Logistics</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $supply_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_MOF_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px; margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Assy. Routings</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $assy_routing_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_GID_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>SAP Core Data</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $gid_1_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_GID_2_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>SAP Prod Data</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $gid_2_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_LABO_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Laboratory</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $labo_counter ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="REL_Saisie_Gamme_Main_Form.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Routing Entry</td>
					<td  style="text-align:right;font-size:8pt"> <?php echo $routing_entry	 ?></td>
				</tr>
			</table>
		</a>

		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>
		<a Target="main_iframe" href="TEST.php" id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="focus_in(this)">
			<table id="t_counter">
				<tr>
					<td>Costing</td>
					<td style="text-align:right;font-size:8pt"> <?php echo $fin_counter ?></td>
				</tr>
			</table>
		</a>
		<?php $i = $i + 1;
			$var_but = "button_" . $i; ?>
		<a id="<?php echo $var_but; ?>" class="w3-bar-item w3-button" style="font-size:12px; margin-left:2px;margin-top:-6px" onclick="Admin_PWD_Check()">
				<table id="t_counter">
					<tr>
						<td>Admin</td>
						<td style="text-align:right;font-size:8pt"></td>
					</tr>
				</table>
			</a>
		<?php $i = $i + 1;
		$var_but = "button_" . $i; ?>

	</div>


	<!-- PAGE CONTENT -->
	<!------------------>
	<div style="margin-left:13%;">

		<div class="w3-container">
			<iframe name="main_iframe" id="main_frame_id" class="main_frame_reduced" src="TEST.php" style="background-image: url(/Common_Resources/logo_scm_zoom_in_transparent.jpg);background-repeat: no-repeat;background-position: right bottom;margin-left:-15px;background-color:transparent" frameborder=0 scrolling=yes>
		</div>

	</div>

</body>

</html>