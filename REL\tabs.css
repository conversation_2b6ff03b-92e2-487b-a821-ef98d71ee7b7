/*
 CSS Document
 (c) By Xul.fr
 Freeware
*/

#content
{
    /*margin-left:24px;
    margin-right:24px;*/
}

#tabs ul
{
    font: normal 11px arial, sans, sans-serif;
    /*border-bottom: 1px solid gray;*/
    margin: 0;
    padding-left:0;
    padding-right:0;
    /*padding-bottom: 26px;*/

}

#tabs ul li 
{
    display: inline;
    float: left;
    height: 20px;
    min-width:80px;
    text-align:center;
    padding:0;
    margin: 1px 0px 0px 0px;
    border: 1px solid grey;

}

#tabs ul li.selected 
{
    border-bottom: 1px solid #fff;
    background-color: #fff;
}


#tabs ul li a 
{
    float: left;
    color: rgb(100, 102, 105);
    text-decoration: none;
    padding: 4px;
    text-align:center;
    background-color:#eee;
    min-width:80px;
    border-bottom: 1px solid gray;
}

#tabs ul li a.selected
{
    color: rgb(255, 255, 255);
    font-weight:bolder;
    background-color: rgb(16, 112, 177);
    border-bottom: 1px solid #fff;
}

#tabs ul li a:hover
{
    color: #000;
    font-weight:bold;
    /*background-color: #fff;*/
}

#container 
{

    background: transparent;
    height:calc(100vh - 90px);
    width:100%;
    padding:0;
    margin:0;
    left:0;
    top:0;

}

