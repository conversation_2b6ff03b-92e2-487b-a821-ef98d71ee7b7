-- MySQL dump 10.13  Distrib 5.7.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_scm
-- ------------------------------------------------------
-- Server version	5.7.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_department`
--

DROP TABLE IF EXISTS `tbl_department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_department` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Department` text,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=16 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_department`
--

LOCK TABLES `tbl_department` WRITE;
/*!40000 ALTER TABLE `tbl_department` DISABLE KEYS */;
INSERT INTO `tbl_department` VALUES (1,'Engineering'),(2,'Operation'),(3,'Metrology'),(4,'Quality'),(5,'Laboratory'),(6,'Project'),(7,'Assembly'),(8,'Machinning'),(9,'Industrialization'),(10,'Logistic'),(11,'Purchasing'),(12,'Inside Sales'),(13,'Molding'),(14,'Customer'),(15,'Finance');
/*!40000 ALTER TABLE `tbl_department` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_department_not_used`
--

DROP TABLE IF EXISTS `tbl_department_not_used`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_department_not_used` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Department` text,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=15 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_department_not_used`
--

LOCK TABLES `tbl_department_not_used` WRITE;
/*!40000 ALTER TABLE `tbl_department_not_used` DISABLE KEYS */;
INSERT INTO `tbl_department_not_used` VALUES (1,'Engineering'),(2,'Operation'),(3,'Metrology'),(4,'Quality'),(5,'Laboratory'),(6,'Project'),(7,'Assembly'),(8,'Machinning'),(9,'Industrialization'),(10,'Logistic'),(11,'Purchasing'),(12,'Inside Sales'),(13,'Molding'),(14,'Customer');
/*!40000 ALTER TABLE `tbl_department_not_used` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_eccn`
--

DROP TABLE IF EXISTS `tbl_eccn`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_eccn` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `ECCN` varchar(10) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_eccn`
--

LOCK TABLES `tbl_eccn` WRITE;
/*!40000 ALTER TABLE `tbl_eccn` DISABLE KEYS */;
INSERT INTO `tbl_eccn` VALUES (1,'NOCLASS'),(2,'8A002.c');
/*!40000 ALTER TABLE `tbl_eccn` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_ex`
--

DROP TABLE IF EXISTS `tbl_ex`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_ex` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Ex` text NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_ex`
--

LOCK TABLES `tbl_ex` WRITE;
/*!40000 ALTER TABLE `tbl_ex` DISABLE KEYS */;
INSERT INTO `tbl_ex` VALUES (1,'ATEX'),(2,'IECEX'),(3,'CSA'),(4,'EX'),(5,'NO');
/*!40000 ALTER TABLE `tbl_ex` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_ex_not_used`
--

DROP TABLE IF EXISTS `tbl_ex_not_used`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_ex_not_used` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Ex` text NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_ex_not_used`
--

LOCK TABLES `tbl_ex_not_used` WRITE;
/*!40000 ALTER TABLE `tbl_ex_not_used` DISABLE KEYS */;
INSERT INTO `tbl_ex_not_used` VALUES (1,'ATEX'),(2,'IECEX'),(3,'CSA'),(4,'EX'),(5,'NO');
/*!40000 ALTER TABLE `tbl_ex_not_used` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_fxxx`
--

DROP TABLE IF EXISTS `tbl_fxxx`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_fxxx` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `fxxx_ref` tinytext NOT NULL,
  `fxxx_description` tinytext NOT NULL,
  `Thickness_Min` int(11) NOT NULL,
  `Thickness_Max` int(11) NOT NULL,
  `Thickness_Unit` tinytext NOT NULL,
  `Density` int(11) NOT NULL,
  `Status` tinytext NOT NULL,
  `fxxx_rohs` tinytext NOT NULL,
  `fxxx_reach` tinytext NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_fxxx`
--

LOCK TABLES `tbl_fxxx` WRITE;
/*!40000 ALTER TABLE `tbl_fxxx` DISABLE KEYS */;
INSERT INTO `tbl_fxxx` VALUES (1,'FMME 3046','Inconel 718',0,0,'',0,'ACTIVE','',''),(2,'FMME 3047','Titanium Grade 5',0,0,'',0,'ACTIVE','',''),(3,'FMME 3032','316L',0,0,'',0,'ACTIVE','',''),(4,'FMTS 05587','Xylan 1424',20,38,'um',0,'ACTIVE','','');
/*!40000 ALTER TABLE `tbl_fxxx` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_hts`
--

DROP TABLE IF EXISTS `tbl_hts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_hts` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `HTS` varchar(10) NOT NULL,
  `Description` text NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_hts`
--

LOCK TABLES `tbl_hts` WRITE;
/*!40000 ALTER TABLE `tbl_hts` DISABLE KEYS */;
INSERT INTO `tbl_hts` VALUES (1,'8544700090',''),(2,'8536700010',''),(3,'7326909890',''),(4,'8536699099',''),(5,'8547200090',''),(6,'8538909999','');
/*!40000 ALTER TABLE `tbl_hts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_product_range_notused`
--

DROP TABLE IF EXISTS `tbl_product_range_notused`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_product_range_notused` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Product_Range` tinytext NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=35 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_product_range_notused`
--

LOCK TABLES `tbl_product_range_notused` WRITE;
/*!40000 ALTER TABLE `tbl_product_range_notused` DISABLE KEYS */;
INSERT INTO `tbl_product_range_notused` VALUES (1,'9316'),(2,'OFS'),(15,'EDBC'),(16,'6kV1600A'),(5,'EFS'),(6,'DLS'),(32,'WG'),(27,'376'),(9,'HydraElectric'),(10,'Showet'),(11,'MOD'),(12,'MSD'),(13,'MPD'),(17,'6kV400A'),(18,'18kV400A'),(19,'18kV900A'),(25,'DS3003'),(33,'ODBC');
/*!40000 ALTER TABLE `tbl_product_range_notused` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_project`
--

DROP TABLE IF EXISTS `tbl_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_project` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `OTP` varchar(6) NOT NULL,
  `Title` varchar(30) NOT NULL,
  `Project_Manager` varchar(30) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=146 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_project`
--

LOCK TABLES `tbl_project` WRITE;
/*!40000 ALTER TABLE `tbl_project` DISABLE KEYS */;
INSERT INTO `tbl_project` VALUES (1,'P08002','DO-JIP-3pin OFS',''),(3,'P08004','ORMEN LANGE 6kV 1600A',''),(4,'P08005','DS3003',''),(5,'P09001','JSM 18KV 6KV 400A',''),(6,'P09002','Shell Princess Vertical OFS',''),(7,'P09003','MORVIN 2',''),(9,'P09005','SULZER P6W400 Motor Penetrator',''),(11,'P09007','JSM - 15KPSI Penetrator',''),(14,'P09010','P6-3W250',''),(16,'P09012','Tordis redesign P6 -400A',''),(17,'P09013','P6-P18 Gap Closing EXXON',''),(18,'P09014','C-Seals Qualification',''),(19,'P10001','JSM AKER 6KV-400A',''),(20,'P10002','GOLIAT',''),(21,'P10003','DS3003 Upgraded 1000 matings',''),(22,'P10008','SBB',''),(23,'P10009','HV DC GAP Closing',''),(24,'P10012','JSM FRAMO 15KPSI PENETRATOR',''),(25,'P10013','Crimp test 20AWG cable wire',''),(26,'P10022','DS3003 Pipeline Repair System',''),(28,'P10029','SHELL LINNORM',''),(29,'P10031','ISLAY PROJECT',''),(30,'P10036','HAMMERFEST Strøm Project',''),(31,'P10040','OL 18kV BLIND CAPS',''),(32,'P11003','NJORD NORTH West Flank',''),(34,'P11006','PAPA TERRA PROJECT',''),(36,'P11010','6 PIN ofs',''),(38,'P11013','EMS project',''),(40,'P11015','ASGARD SERIAL PROJECT',''),(41,'P11016','EKOFISK PROJECT',''),(42,'P11017','SKULD PROJECT',''),(43,'P11018','Shell HPHT Connector',''),(44,'P11019','SHELL OL FEED U0',''),(45,'P11020','LYELL',''),(47,'P11022','CHEVRON P18SW900- 3000m',''),(48,'P11023','CAMERON New SCM',''),(49,'P12001','IN WELL G2',''),(50,'P12002','PIPE HEATING DEMONSTRATOR',''),(51,'P12003','DS-DO Qualification CAMERON',''),(52,'P12004','BRYNHILD',''),(53,'P12005','KINOSIS HEATED PIPE',''),(54,'P12006','NE PAS UTILISER',''),(55,'P12007','ICS 9316 DR',''),(56,'P12008','CAMERON STABPLATE',''),(57,'P12009','AMB Subsea Side Project',''),(58,'P12010','DUCO ALUMINIUM CABLE',''),(59,'P12011','EFS DRY MATE PLUG (SAV 12-041)',''),(60,'P12012','GSC Gullfaks',''),(61,'P12013','THALES 2,6kV Splash Zone',''),(62,'P12014','S2M 9316 Stycast',''),(64,'P13001','GOLDEN EAGLE',''),(65,'P13002','SHELL STONES',''),(66,'P13003','NE PAS UTILISER',''),(67,'P13004','DEH STATOIL',''),(68,'P13005','UTSIRA SAV13-044',''),(69,'P13006','JULIA',''),(70,'P13007','DIEGA - CARLA',''),(71,'P13008','9316 IECEx_STONES',''),(72,'P13009','9316 IECEx STONES (BMT)',''),(73,'P13010','FMC EGINA',''),(74,'P13011','PRISMER ALSTOM',''),(75,'P14001','JSM II EFS',''),(76,'P14002','Energies renouvelables',''),(77,'P14003','UTSIRA POWET',''),(78,'P14004','MOHO',''),(79,'P14005','PiP Bulkhead Penetrators',''),(80,'P14006','KING',''),(81,'P14007','CRYOSTAR - 9316 HPBT',''),(82,'P15001','DS4001 EWS',''),(83,'P15002','VXT EFS 1 Pin - FEED STUDY',''),(84,'P15003','EHTF - Subsea 7- Wet Mate',''),(85,'P15004','16_EX_CERTIFICATE_M&D',''),(86,'P15005','SS7 - EHTF - Dual Barrier',''),(87,'P15006','DS3003 G3 Range',''),(88,'P15007','DO3000 R - Renewable version',''),(89,'P15008','6 FO VOFS Baker Hughes',''),(90,'P15009','TVEX',''),(91,'P15010','SHOSHIN FEPS Tidal',''),(92,'P15012','MEMBRANE GEL (TECHNO)',''),(93,'P16001','HEFS HYBRID PENETRATOR',''),(94,'P16002','6KV 400A Direct Term',''),(95,'P16003','AGV',''),(96,'P16004','9316 ATEX Certification',''),(97,'P16005','EFS Humidity exposure',''),(98,'P16006','Annular ILC',''),(99,'P16007','Minesto Deep Green',''),(100,'P16008','Greater Enfield FPSO',''),(101,'P17001','GE Ifokus',''),(103,'P17003','6 PIN Feed Study',''),(104,'P17004','EHTF - EDBC New heating cable',''),(106,'P17006','9316 Sustaining',''),(107,'P17007','JANSZ','JADAUD S.'),(108,'P17008','Alta Gohta',''),(109,'P18001','TE MOG Support',''),(110,'P18002','ILC DCNS',''),(111,'P18003','Star-End Termination Subsea 7','LEDU M.'),(112,'P18004','FENJA ETH-PiP Optic','JEAN J.'),(113,'P18005','Optical Quick Connector PiP',''),(114,'P18006','Shell Stones - OSI','BELLON F.'),(115,'P18007','AErfugl','LEDU M.'),(116,'P18008','DUSUP - GE Ceramic Penetrator','JADAUD S.'),(117,'P18009','EHTF Splice','LEDU M.'),(118,'P18010','EHTF BP Manuel','LEDU M.'),(119,'P18011','48 FO Thales','BAUER M.'),(120,'P19001','FMC BP ThunderHorse','LEDU M.'),(121,'P19002','FMC BP Atlantis','LEDU M.'),(122,'P19003','KRAFLA','JADAUD S.'),(123,'P19004','MultiPin VOFS 10K-250F','LEDU J.'),(124,'P19005','ASGARD Phase 2','JADAUD S.'),(125,'P19006','ILC Optic -TFMC Brazil',''),(126,'P19007','XoM Payara OFS','LEDU M.'),(127,'P19008','MSD Vincent',''),(128,'P19009','TFMC Electrical Splice','MADELIN A.'),(129,'P19010','ILC SAFRAN','BAUER M.'),(130,'P20001','ABB Internal Jumper','JADAUD S.'),(131,'P20002','9316 HT Double Marking','BAUER M.'),(132,'P20003','AErfugl phase 2','LEDU M.'),(133,'P20004','SEPDU',''),(134,'P20005','THRT Payara',''),(135,'P20006','JANSZ Execution','JADAUD S.'),(136,'P20007','HCRAW Cable Terminaison','BAUER M.'),(137,'P21001','KRAFLA - AMB penetrator','JEAN J.'),(138,'P21002','KRAFLA - HV Penetrator','JEAN J.'),(142,'STAND','STANDARD',''),(143,'P21006','P4 3W100','MADELIN A.'),(144,'P21007','Aasgard 2 MAN AMB','JEAN.J'),(145,'P21005','JANSZ Execution - AMB','JEAN.J');
/*!40000 ALTER TABLE `tbl_project` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_unit`
--

DROP TABLE IF EXISTS `tbl_unit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_unit` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Unit` tinytext NOT NULL,
  `Unit_Type` tinytext NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_unit`
--

LOCK TABLES `tbl_unit` WRITE;
/*!40000 ALTER TABLE `tbl_unit` DISABLE KEYS */;
INSERT INTO `tbl_unit` VALUES (1,'PC','Piece'),(2,'kg','Weight'),(3,'dm','Length'),(4,'m','Length'),(5,'g','Weight'),(6,'mm²','Surface');
/*!40000 ALTER TABLE `tbl_unit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_unit_not_used`
--

DROP TABLE IF EXISTS `tbl_unit_not_used`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_unit_not_used` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Unit` tinytext NOT NULL,
  `Unit_Type` tinytext NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_unit_not_used`
--

LOCK TABLES `tbl_unit_not_used` WRITE;
/*!40000 ALTER TABLE `tbl_unit_not_used` DISABLE KEYS */;
INSERT INTO `tbl_unit_not_used` VALUES (1,'PC','Piece'),(2,'kg','Weight'),(3,'dm','Length'),(4,'m','Length'),(5,'g','Weight'),(6,'mm²','Surface');
/*!40000 ALTER TABLE `tbl_unit_not_used` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_user`
--

DROP TABLE IF EXISTS `tbl_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_user` (
  `Key_User` int(11) NOT NULL AUTO_INCREMENT,
  `TE_ID` tinytext NOT NULL,
  `Fullname` tinytext NOT NULL,
  `Email` tinytext NOT NULL,
  `ID_PC` tinytext NOT NULL,
  `Department` tinytext NOT NULL,
  PRIMARY KEY (`Key_User`)
) ENGINE=MyISAM AUTO_INCREMENT=149 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_user`
--

LOCK TABLES `tbl_user` WRITE;
/*!40000 ALTER TABLE `tbl_user` DISABLE KEYS */;
INSERT INTO `tbl_user` VALUES (43,'TE180249','CHATAIN A.','<EMAIL>','','Engineering'),(41,'TE180160','BAUER M.','<EMAIL>','FRK28NBO745Q453$','Engineering'),(17,'TE180431','JADAUD S.','<EMAIL>','','Project'),(4,'TE180197','BONIN R.','<EMAIL>','','Quality'),(13,'TE180338','EON C.','<EMAIL>','','Quality'),(42,'TE180207','BOURRET F.','<EMAIL>','','Assembly'),(54,'TE180447','LAGATHU C.','<EMAIL>','','Engineering'),(45,'TE180320','DORANGE N.','<EMAIL>','','Engineering'),(63,'TE466256','DUNORD N.','<EMAIL>','','Machinning'),(47,'TE180347','FARIAULT N.','<EMAIL>','','Engineering'),(72,'TE421736','BELLET N.','<EMAIL>','','Laboratory'),(61,'TE180566','PEIGNE G.','<EMAIL>','','Industrialization'),(64,'TE183530','LERUEZ PL.','<EMAIL>','','Assembly'),(65,'TE180451','LANGLOIS J.','<EMAIL>','','Industrialization'),(66,'TE180398','GUITTET Y.','<EMAIL>','','Engineering'),(67,'TE417418','LACROIX N.','<EMAIL>','','Engineering'),(68,'TE180563','PASCAUD S.','<EMAIL>','','Machinning'),(146,'TE476786','LEGEAY J.','<EMAIL>','','Inside Sales'),(70,'TE426193','LELONG T.','<EMAIL>','','Engineering'),(73,'TE180183','BLANCHE J.','<EMAIL>','','Laboratory'),(74,'TE180193','BOIVIN G.','<EMAIL>','','Laboratory'),(75,'TE180201','BOUCHENOIRE S.','<EMAIL>','','Purchasing'),(147,'TE504721','GOUJAT N.','<EMAIL>','','Purchasing'),(145,'TE504722','BREGENT M.','<EMAIL>','','Engineering'),(143,'TE483912','MADELIN A.','<EMAIL>','','Project'),(139,'TE221445','BETTIS J.','<EMAIL>','','Katy'),(138,'TE183422','GUICHARDON L.','<EMAIL>','','Molding'),(137,'TE180292','CRIBIER JF.','<EMAIL>','','Operation'),(85,'TE183479','GOURDOU JF.','<EMAIL>','','Laboratory'),(134,'TE180220','BRIER F.','<EMAIL>','','Quality'),(133,'TE180172','BELLON F.','<EMAIL>','','Quality'),(132,'TE180332','DURAND S.','<EMAIL>','','Metrology'),(131,'TE180617','RIGUET W.','<EMAIL>','','Purchasing'),(130,'TE180171','BELLANGER F.','<EMAIL>','','Inside Sales'),(129,'TE183439','CISSE N.','<EMAIL>','','Assembly'),(128,'TE180296','DARONDEAU A.','<EMAIL>','','Assembly'),(127,'TE401483','MARCAIS.C','<EMAIL>','','Assembly'),(124,'TE424288','JEAN.J','<EMAIL>','','Quality'),(125,'TE183456','JANVIER.I','<EMAIL>','','Assembly'),(126,'TE415242','VILLETTE.C','<EMAIL>','','Quality'),(100,'TE180193','BOIVIN G.','<EMAIL>','','Laboratory'),(102,'TE180242','CASSEGRAIN O.','<EMAIL>','','Ourchasing'),(103,'TE180262','CHEVRIER F.','<EMAIL>','','Method'),(104,'TE183527','COTOC A.','<EMAIL>','','Laboratory'),(105,'TE180286','CRAPIS A.','<EMAIL>','','Engineering'),(144,'TE415298','KAROU S.','<EMAIL>','','Quality'),(107,'TE400321','DAVID Gr.','<EMAIL>','','Laboratory'),(108,'TE378346','DERET V.','<EMAIL>','','Laboratory'),(136,'TE180531','MEDARD M.','<EMAIL>','','Production'),(110,'TE180379','GOT G.','<EMAIL>','','Engineering'),(135,'TE390704','ROUILLARD N.','<EMAIL>','','Industrialization'),(148,'TE506816','ROBICHON P.','<EMAIL>','','Quality'),(113,'TE371566','LEDU M.','<EMAIL>','','Project'),(141,'TE180440','JULIEN S.','<EMAIL>','','Engineering'),(115,'TE180515','MARAIS J.','<EMAIL>','','Method'),(116,'TE180560','PARAT P.','<EMAIL>','','Industrialization'),(117,'TE180562','PARME R.','<EMAIL>','','Engineering'),(118,'TE183474','PAULMERY E.','<EMAIL>','','Laboratory'),(119,'TE376107','PERES T.','<EMAIL>','','Method'),(120,'TE180571','PERNET G.','<EMAIL>','','Engineering'),(121,'TE183512','REVAUD E.','<EMAIL>','','Laboratory'),(122,'TE180675','TISON L.','<EMAIL>','','Method'),(123,'TE180704','WENTS G.','<EMAIL>','','Molding');
/*!40000 ALTER TABLE `tbl_user` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_user_notused`
--

DROP TABLE IF EXISTS `tbl_user_notused`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_user_notused` (
  `Key_User` int(11) NOT NULL AUTO_INCREMENT,
  `TE_ID` tinytext NOT NULL,
  `Fullname` tinytext NOT NULL,
  `Email` tinytext NOT NULL,
  `password` varchar(100) NOT NULL,
  `ID_PC` tinytext NOT NULL,
  `Department` tinytext NOT NULL,
  `Dpt_DMO` varchar(40) CHARACTER SET utf8 COLLATE utf8_swedish_ci NOT NULL,
  PRIMARY KEY (`Key_User`)
) ENGINE=MyISAM AUTO_INCREMENT=74 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_user_notused`
--

LOCK TABLES `tbl_user_notused` WRITE;
/*!40000 ALTER TABLE `tbl_user_notused` DISABLE KEYS */;
INSERT INTO `tbl_user_notused` VALUES (43,'TE180249','CHATAIN A.','<EMAIL>','','','Engineering','Engineering'),(41,'TE180160','BAUER M.','<EMAIL>','','FRK28NBO745Q453$','Engineering','Engineering'),(17,'TE180431','JADAUD S.','<EMAIL>','','','Project','Project'),(4,'TE180197','BONIN R.','<EMAIL>','','','Quality','Quality'),(13,'TE180338','EON C.','<EMAIL>','','','Quality','Quality'),(42,'TE180207','BOURRET F.','<EMAIL>','','','Assembly','Assembly'),(54,'TE180447','LAGATHU C.','<EMAIL>','','','Engineering','Engineering'),(45,'TE180320','DORANGE N.','<EMAIL>','','','Engineering','Engineering'),(63,'TE466256','DUNORD N.','<EMAIL>','','','Machinning','Machinning'),(47,'TE180347','FARIAULT N.','<EMAIL>','','','Engineering','Engineering'),(62,'TEXXXXXX','DUPONT M.','<EMAIL>','','','Engineering','Engineering'),(61,'TE180566','PEIGNE G.','<EMAIL>','','','Industrialization','Industrialization'),(64,'TE183530','LERUEZ PL.','<EMAIL>','','','Assembly','Assembly'),(65,'TE180451','LANGLOIS J.','<EMAIL>','','','Industrialization','Industrialization'),(66,'TE180398','GUITTET Y.','<EMAIL>','','','Engineering','Engineering'),(67,'TE417418','LACROIX N.','<EMAIL>','','','Engineering','Engineering'),(68,'TE180563','PASCAUD S.','<EMAIL>','','','Machinning','Machinning'),(69,'TE400701','PLUMER A.','<EMAIL>','','','Engineering','Engineering'),(70,'TE426193','LELONG T.','<EMAIL>','','','Engineering','Engineering');
/*!40000 ALTER TABLE `tbl_user_notused` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-08-01 16:56:02
