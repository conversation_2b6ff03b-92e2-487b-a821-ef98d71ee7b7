-- MySQL dump 10.13  Distrib 5.7.36, for Win64 (x86_64)
--
-- Host: localhost    Database: db_scm
-- ------------------------------------------------------
-- Server version	5.7.36

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Table structure for table `tbl_department`
--

DROP TABLE IF EXISTS `tbl_department`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_department` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Department` text,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=20 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_department`
--

LOCK TABLES `tbl_department` WRITE;
/*!40000 ALTER TABLE `tbl_department` DISABLE KEYS */;
INSERT INTO `tbl_department` VALUES (1,'Engineering'),(2,'Operation'),(3,'Metrology'),(4,'Quality'),(5,'Laboratory'),(6,'Project'),(7,'Assembly'),(8,'Machining'),(9,'Industrialization'),(10,'Logistic'),(11,'Purchasing'),(12,'Inside Sales'),(13,'Molding'),(14,'Customer'),(15,'Finance'),(18,'Industry'),(17,'Supply Chain'),(19,'Aerospace');
/*!40000 ALTER TABLE `tbl_department` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_division`
--

DROP TABLE IF EXISTS `tbl_division`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_division` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Division` varchar(45) NOT NULL,
  `Description` varchar(255) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=4 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_division`
--

LOCK TABLES `tbl_division` WRITE;
/*!40000 ALTER TABLE `tbl_division` DISABLE KEYS */;
INSERT INTO `tbl_division` VALUES (1,'Energy','Renewable, Nuclear, Oil&Gas'),(2,'Industrial','Railway, Defense'),(3,'Aerospace','Aerospace, Marine');
/*!40000 ALTER TABLE `tbl_division` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_eccn`
--

DROP TABLE IF EXISTS `tbl_eccn`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_eccn` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `ECCN` varchar(10) NOT NULL,
  `Description` varchar(100) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_eccn`
--

LOCK TABLES `tbl_eccn` WRITE;
/*!40000 ALTER TABLE `tbl_eccn` DISABLE KEYS */;
INSERT INTO `tbl_eccn` VALUES (1,'NOCLASS','Out of any military product/market'),(2,'8A002.c','Dual Use Products'),(3,'8A992.e',''),(4,'ML10.A','Military application only');
/*!40000 ALTER TABLE `tbl_eccn` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_ex`
--

DROP TABLE IF EXISTS `tbl_ex`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_ex` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Ex` text NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_ex`
--

LOCK TABLES `tbl_ex` WRITE;
/*!40000 ALTER TABLE `tbl_ex` DISABLE KEYS */;
INSERT INTO `tbl_ex` VALUES (1,'ATEX'),(2,'IECEX'),(3,'CSA'),(4,'EX'),(5,'NO');
/*!40000 ALTER TABLE `tbl_ex` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_fxxx`
--

DROP TABLE IF EXISTS `tbl_fxxx`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_fxxx` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `fxxx_ref` tinytext NOT NULL,
  `fxxx_description` tinytext NOT NULL,
  `Thickness_Min` int(11) NOT NULL,
  `Thickness_Max` int(11) NOT NULL,
  `Thickness_Unit` tinytext NOT NULL,
  `Density` int(11) NOT NULL,
  `Status` tinytext NOT NULL,
  `fxxx_rohs` tinytext NOT NULL,
  `fxxx_reach` tinytext NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=625 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_fxxx`
--

LOCK TABLES `tbl_fxxx` WRITE;
/*!40000 ALTER TABLE `tbl_fxxx` DISABLE KEYS */;
INSERT INTO `tbl_fxxx` VALUES (1,'FMME 1001','Laiton / CuZn35Pb2',0,0,'',0,'ACTIVE','',''),(2,'FMME 1002','Laiton / CuZn39Pb1',0,0,'',0,'ACTIVE','',''),(3,'FMME 1003','Laiton / CuZn10',0,0,'',0,'ACTIVE','',''),(4,'FMME 1004','Laiton / CuZn35',0,0,'',0,'ACTIVE','',''),(5,'FMME 1006','Laiton / CuZn36',0,0,'',0,'ACTIVE','',''),(6,'FMME 1008','Laiton / CuZn39Pb2',0,0,'',0,'ACTIVE','',''),(7,'FMME 1009','Laiton / CuZn36x502',0,0,'',0,'ACTIVE','',''),(8,'FMME 1011','Laiton / CuZn40',0,0,'',0,'ACTIVE','',''),(9,'FMME 1013','Laiton / CuZn40MnNiAl',0,0,'',0,'ACTIVE','',''),(10,'FMME 1014','Laiton / CuZn40Pb3',0,0,'',0,'ACTIVE','',''),(11,'FMME 1015','Laiton / CuZn36Pb3',0,0,'',0,'ACTIVE','',''),(12,'FMME 1016','Laiton / CuZn37Pb0,5',0,0,'',0,'ACTIVE','',''),(13,'FMME 1017','Laiton / CuNi1Pb1P (C97)',0,0,'',0,'ACTIVE','',''),(14,'FMME 1019','Bronze d\'aluminium / CuAl9Ni3Fe2',0,0,'',0,'ACTIVE','',''),(15,'FMME 1020','Laiton / CuZn33',0,0,'',0,'ACTIVE','',''),(16,'FMME 1021','Laiton / CuZn36Pb1',0,0,'',0,'ACTIVE','',''),(17,'FMME 1022','Bronze d\'aluminium / CuAl9Fe3',0,0,'',0,'ACTIVE','',''),(18,'FMME 1023','Bronze d\'aluminium / CuAl10Fe',0,0,'',0,'ACTIVE','',''),(19,'FMME 1024','Laiton / CuZn40Pb2',0,0,'',0,'ACTIVE','',''),(20,'FMME 1025','Bronze d\'aluminium / CuAl10Ni5Fe4',0,0,'',0,'ACTIVE','',''),(21,'FMME 1026','Laiton / CuZn39Pb2',0,0,'',0,'ACTIVE','',''),(22,'FMME 2000','Cuivreux / Cu/a2',0,0,'',0,'ACTIVE','',''),(23,'FMME 2001','Cuivre au béryllium / CuBe1,9',0,0,'',0,'ACTIVE','',''),(24,'FMME 2003','Cuivreux / CuTe',0,0,'',0,'ACTIVE','',''),(25,'FMME 2005','Cuivreux / Cu/a1',0,0,'',0,'ACTIVE','',''),(26,'FMME 2007','Cuivreux / Cu/b1',0,0,'',0,'ACTIVE','',''),(27,'FMME 2008','Cuivreux / Cu/b2',0,0,'',0,'ACTIVE','',''),(28,'FMME 2009','Cuivreux / Cu/c1',0,0,'',0,'ACTIVE','',''),(29,'FMME 2010','Cuivreux / Cu/c2',0,0,'',0,'ACTIVE','',''),(30,'FMME 2011','Bronze d\'aluminium / CuAl9',0,0,'',0,'ACTIVE','',''),(31,'FMME 2030','Cuivreux / CuCr',0,0,'',0,'ACTIVE','',''),(32,'FMME 2031','Cuivreux / CuCr1Zr',0,0,'',0,'ACTIVE','',''),(33,'FMME 2032','Cuivreux / K55',0,0,'',0,'ACTIVE','',''),(34,'FMME 2033','Cuivreux / CuSn10',0,0,'',0,'ACTIVE','',''),(35,'FMME 3004','Acier / E24-2',0,0,'',0,'ACTIVE','',''),(36,'FMME 3010','Acier / XC38',0,0,'',0,'ACTIVE','',''),(37,'FMME 3012','Acier / XC70',0,0,'',0,'ACTIVE','',''),(38,'FMME 3013','Acier / XC80',0,0,'',0,'ACTIVE','',''),(39,'FMME 3015','Acier / CC45',0,0,'',0,'ACTIVE','',''),(40,'FMME 3016','Acier / 55 Ni Cr Mo V 7',0,0,'',0,'ACTIVE','',''),(41,'FMME 3017','Inox 304 L / Z3 CN 19.09',0,0,'',0,'ACTIVE','',''),(42,'FMME 3018','Inox 304 / Z6 CN 18-09',0,0,'',0,'ACTIVE','',''),(43,'FMME 3019','Inox / Z6 CNDT 17-12',0,0,'',0,'ACTIVE','',''),(44,'FMME 3020','Inox / Z10 CNF 18-09',0,0,'',0,'ACTIVE','',''),(45,'FMME 3023','Inox 316 / Z6 CND 17-11',0,0,'',0,'ACTIVE','',''),(46,'FMME 3024','Inox / Z30 C13',0,0,'',0,'ACTIVE','',''),(47,'FMME 3028','Inox / C 100',0,0,'',0,'ACTIVE','',''),(48,'FMME 3032','Inox 316L / NORSOK',0,0,'',0,'ACTIVE','',''),(49,'FMME 3034','Inox / Z10 CN 18-09',0,0,'',0,'ACTIVE','',''),(50,'FMME 3035','Inox / Z8 C17',0,0,'',0,'ACTIVE','',''),(51,'FMME 3036','Acier / S300Pb',0,0,'',0,'ACTIVE','',''),(52,'FMME 3037','Acier / ET G 88',0,0,'',0,'ACTIVE','',''),(53,'FMME 3038','Cupro - nickel / ARCAP AP1D',0,0,'',0,'ACTIVE','',''),(54,'FMME 3039','Inox / ETG100',0,0,'',0,'ACTIVE','',''),(55,'FMME 3040','Acier / 100 C 6',0,0,'',0,'ACTIVE','',''),(56,'FMME 3041','Acier / 25CD4',0,0,'',0,'ACTIVE','',''),(57,'FMME 3042','Acier / 35 NCD 16',0,0,'',0,'ACTIVE','',''),(58,'FMME 3043','Acier pour nitruration / 40 CAD 6-12',0,0,'',0,'ACTIVE','',''),(59,'FMME 3044','Inconel X750 TT3 / NC15TNbA',0,0,'',0,'ACTIVE','',''),(60,'FMME 3045','Super Duplex - Norsok / Z 3 CNDU 25.07 Az (UNS 32550)',0,0,'',0,'ACTIVE','',''),(61,'FMME 3046','Inconel 718 / NC 19FeNb',0,0,'',0,'ACTIVE','',''),(62,'FMME 3047','TITANE grade 5 / Ti 6AI 4V Alpha beta alloy',0,0,'',0,'ACTIVE','',''),(63,'FMME 3048','Inox Super Duplex / X2CrNiMoN 25-7-4',0,0,'',0,'ACTIVE','',''),(65,'FMME 3049','Acier pour ressort / SM',0,0,'',0,'ACTIVE','',''),(66,'FMME 3050','Inox / AISI 410',0,0,'',0,'ACTIVE','',''),(67,'FMME 3051','Inox duplex / X2 CrNiMoN 22-5-3',0,0,'',0,'ACTIVE','',''),(68,'FMME 3052','Inconel 600 / NC 15 Fe',0,0,'',0,'ACTIVE','',''),(69,'FMME 3053','Acier / AISI 8630',0,0,'',0,'ACTIVE','',''),(70,'FMME 3054','Nickel - Cobalt / MP35N',0,0,'',0,'ACTIVE','',''),(71,'FMME 3055','Acier austénitique / A4',0,0,'',0,'ACTIVE','',''),(72,'FMME 3056','Acier austénitique / A2',0,0,'',0,'ACTIVE','',''),(73,'FMME 3057','Alliage fer-nickel-cobalt dilver P1 - KOVAR / P1',0,0,'',0,'ACTIVE','',''),(74,'FMME 3058','Acier à ressort / FDSiCr',0,0,'',0,'ACTIVE','',''),(75,'FMME 3059 NORSOK','Inconel 625 / NC 22DNb',0,0,'',0,'ACTIVE','',''),(76,'FMME 3060','Nickel alloy / Hastelloy C-276',0,0,'',0,'ACTIVE','',''),(77,'FMME 3061 NORSOK','Acier austénitique / 254 SMO',0,0,'',0,'ACTIVE','',''),(78,'FMME 3062','Acier / XC75',0,0,'',0,'ACTIVE','',''),(79,'FMME 3063','Alliage Cobalt / Elgiloy',0,0,'',0,'ACTIVE','',''),(80,'FMME 3064 NORSOK','Titane Grade 2 / Grade 2',0,0,'',0,'ACTIVE','',''),(81,'FMME 3065','Inox / X2CrNiMo 19-11-2 (316 L moulé)',0,0,'',0,'ACTIVE','',''),(82,'FMME 3066','Inox / AL6XN',0,0,'',0,'ACTIVE','',''),(83,'FMME 3067','Superduplex US / X2CrNiMoN 25-7-4',0,0,'',0,'ACTIVE','',''),(84,'FMME 3068','Inconel 718 US / NC 19FeNb',0,0,'',0,'ACTIVE','',''),(85,'FMME 3069','F6NM / ASTM A182-F6NM',0,0,'',0,'ACTIVE','',''),(86,'FMME 3070','Inconel 825 / ',0,0,'',0,'ACTIVE','',''),(87,'FMME 3071','Superduplex certif 3.2 / Z 3 CNDU 25.07 Az',0,0,'',0,'ACTIVE','',''),(88,'FMME 3072','Inconel 825 en tube / ',0,0,'',0,'ACTIVE','',''),(89,'FMME 3072-X',' / ',0,0,'',0,'ACTIVE','',''),(90,'FMME 3073','ACIER / 40 CMD8',0,0,'',0,'ACTIVE','',''),(91,'FMME 3074','Acier inoxydable / Z6 CNU 17-04',0,0,'',0,'ACTIVE','',''),(92,'FMME 3075','Acier inoxydable / Z6 CND 16.05.01',0,0,'',0,'ACTIVE','',''),(93,'FMME 3076','Inox / ',0,0,'',0,'ACTIVE','',''),(94,'FMME 3077-X','Inconel 725 / Inconel 725',0,0,'',0,'ACTIVE','',''),(95,'FMME 3078','Acier carbone / API 5L X65',0,0,'',0,'ACTIVE','',''),(96,'FMME 3079','TI 6246 / TI 6246',0,0,'',0,'ACTIVE','',''),(97,'FMME 3081','Inox Super Duplex / X2CrNiMoCuWN 25.7.4',0,0,'',0,'ACTIVE','',''),(98,'FMME 3082','Inox 316L / Non Norsok\"',0,0,'',0,'ACTIVE','',''),(99,'FMME 3084','Inco 625 (FMME 3059 devient FMME 3084) / Inconel 625',0,0,'',0,'ACTIVE','',''),(100,'FMME 3085','254 SMO (FMME 3061 devient FMME 3085) / 254 SMO',0,0,'',0,'ACTIVE','',''),(101,'FMME 3086','Titane grade 2 (FMME 3064 devient FMME 3086) / Titane grade 2',0,0,'',0,'ACTIVE','',''),(102,'FMME 3087','Inconel (N08028) / Sanicro28',0,0,'',0,'ACTIVE','',''),(103,'FMME 3088','Acier visserie classe 8.8 / ',0,0,'',0,'ACTIVE','',''),(104,'FMME 3089','Acier visserie classe 10.8 / ',0,0,'',0,'ACTIVE','',''),(105,'FMME 3090','Acier visserie classe 12.9 / ',0,0,'',0,'ACTIVE','',''),(106,'FMME 3091','Nickel - Cobalt / MP35N',0,0,'',0,'ACTIVE','',''),(107,'FMME 3093','Inox / AISI 301',0,0,'',0,'ACTIVE','',''),(108,'FMME 3094','Acier / XC35',0,0,'',0,'ACTIVE','',''),(109,'FMME 3095','Acier inoxydable / Inox 201',0,0,'',0,'ACTIVE','',''),(110,'FMME 3096','Acier inoxydable / 17-7 PH (AISI 631)',0,0,'',0,'ACTIVE','',''),(111,'FMME 3097','Acier inoxydable / Maraging C300',0,0,'',0,'ACTIVE','',''),(112,'FMME 3098','Acier carbone / ASTM 694 F60',0,0,'',0,'ACTIVE','',''),(113,'FMME 3099','Acier / S235',0,0,'',0,'ACTIVE','',''),(114,'FMME 3100','Super Duplex / Z3 CNDU 25.07 Az (UNS 32760)',0,0,'',0,'ACTIVE','',''),(115,'FMME 3101','Duplex Springflex / X2CrNiMoN 22-5-3 (UNS S32205)',0,0,'',0,'ACTIVE','',''),(116,'FMME 3102','Acier inoxydable / PH 15-7 Mo',0,0,'',0,'ACTIVE','',''),(117,'FMME 4002','Aluminium / 1050 A (A5)',0,0,'',0,'ACTIVE','',''),(118,'FMME 4003','Aluminium / 1070 A (A7)',0,0,'',0,'ACTIVE','',''),(119,'FMME 4004','Aluminium / A-G3T',0,0,'',0,'ACTIVE','',''),(120,'FMME 4005','Aluminium / Al S7 Mg 42000 (ex A-S7G)',0,0,'',0,'ACTIVE','',''),(121,'FMME 4006','Aluminium / A-S9G',0,0,'',0,'ACTIVE','',''),(122,'FMME 4007','Aluminium / Al Si 8Cu 3.5 (ex A-S9U3)',0,0,'',0,'ACTIVE','',''),(123,'FMME 4008','Aluminium / A-S10G',0,0,'',0,'ACTIVE','',''),(124,'FMME 4009','Aluminium / A-S12U',0,0,'',0,'ACTIVE','',''),(125,'FMME 4010','Aluminium / Al Si 12 (b) 44100 (ex A-S13)',0,0,'',0,'ACTIVE','',''),(126,'FMME 4011','Aluminium / 2017 A (ex A-U4G)',0,0,'',0,'ACTIVE','',''),(127,'FMME 4012','Aluminium / 2030 (A-U4Pb)',0,0,'',0,'ACTIVE','',''),(128,'FMME 4013','Aluminium / A-U5GT',0,0,'',0,'ACTIVE','',''),(129,'FMME 4014','Aluminium / 7049A (A-Z8GU)',0,0,'',0,'ACTIVE','',''),(130,'FMME 4015','Aluminium / 5083 (A-G4.5MC)',0,0,'',0,'ACTIVE','',''),(131,'FMME 4016','Aluminium / 6061',0,0,'',0,'ACTIVE','',''),(132,'FMME 4018','Aluminium / 5056 (A-G5M)',0,0,'',0,'ACTIVE','',''),(133,'FMME 4019','Aluminium / 6005A (ASG0,5)',0,0,'',0,'ACTIVE','',''),(134,'FMME 4020','Aluminium / 6060T5',0,0,'',0,'ACTIVE','',''),(135,'FMME 4021','Aluminium / 6181 T6',0,0,'',0,'ACTIVE','',''),(136,'FMME 4022','Aluminium / 5754 (AG3M)',0,0,'',0,'ACTIVE','',''),(137,'FMME 4023','Aluminium / 6005 AT6',0,0,'',0,'ACTIVE','',''),(138,'FMME 4024','Aluminium / 5086 H111',0,0,'',0,'ACTIVE','',''),(139,'FMME 5003','Fonte / FGS 400-15',0,0,'',0,'ACTIVE','',''),(140,'FMME 6001','Zinc / ZAMAK 3',0,0,'',0,'ACTIVE','',''),(141,'FMME 7001','Etain / 1 RA Di-85-3 Sn 96-Ag',0,0,'',0,'ACTIVE','',''),(142,'FMME 9001','Alumine 99,5 % / Al2O3',0,0,'',0,'ACTIVE','',''),(143,'FMME 9002','Alumine 97 % / Al2O3',0,0,'',0,'ACTIVE','',''),(144,'FMME 10010','C.N. / Caoutchouc naturel',0,0,'',0,'ACTIVE','',''),(145,'FMME 20010','Nitrile (NBR) / Nitrile',0,0,'',0,'ACTIVE','',''),(146,'FMME 20020','Nitrile (NBR) / Butadiène nitrile 70 shores',0,0,'',0,'ACTIVE','',''),(147,'FMME 20030','\"Nitrile (NBR) / Butadiène nitrile 90 shores ref ECORUBBER-1 code matière 26\"',0,0,'',0,'ACTIVE','',''),(148,'FMME 21010','Nitrile (NBR) / HNBR 70 IRHD (Elast-O-Lion 170)',0,0,'',0,'ACTIVE','',''),(149,'FMME 21011','Nitrile (NBR) / H-NBR 70 Shore (moulage)',0,0,'',0,'ACTIVE','',''),(150,'FMME 31010','Chloroprène / Polychloroprène / Chloroprène',0,0,'',0,'ACTIVE','',''),(151,'FMME 31011','Chloroprène / Polychloroprène / Polychloroprène',0,0,'',0,'ACTIVE','',''),(152,'FMME 31012','Chloroprène / Polychloroprène / Polychloroprène CR',0,0,'',0,'ACTIVE','',''),(153,'FMME 31013','Chloroprène / Polychloroprène / Elastomère - Polychloroprène 31B7',0,0,'',0,'ACTIVE','',''),(154,'FMME 31014','Chloroprène / Polychloroprène / Elastomère - Polychloroprène 31B6',0,0,'',0,'ACTIVE','',''),(155,'FMME 70015','Chloroprène / Polychloroprène / Polychloroprène 3610 B1 C3 F3',0,0,'',0,'ACTIVE','',''),(156,'FMME 70017','Néoprène / Néoprène cellulaire',0,0,'',0,'ACTIVE','',''),(157,'FMME 31015','Néoprène / Néoprène 3507',0,0,'',0,'ACTIVE','',''),(158,'FMME 31016','Néoprène / Néoprène 3510',0,0,'',0,'ACTIVE','',''),(159,'FMME 31017','Néoprène 80 Shore A / Néoprène 3841N',0,0,'',0,'ACTIVE','',''),(160,'FMME 41010','Propylène / Ethylène / Ethylène propylène',0,0,'',0,'ACTIVE','',''),(161,'FMME 41040','Propylène / Ethylène / Aflas 75 shores - FEPM',0,0,'',0,'ACTIVE','',''),(162,'FMME 50050','Gel silicone / Gel silicone Dow Corning 3-4207 ',0,0,'',0,'ACTIVE','',''),(163,'FMME 50060','Gel silicone / Dow Corning 577',0,0,'',0,'ACTIVE','',''),(164,'FMME 50110','Gel silicone / WACKER Silgel 616',0,0,'',0,'ACTIVE','',''),(165,'FMME 60010','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Flurorocarbone',0,0,'',0,'ACTIVE','',''),(166,'FMME 60020','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Viton réf. VT 70 (victoria)',0,0,'',0,'ACTIVE','',''),(167,'FMME 60030','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Viton 55/60 réf. 6559 (SGM)',0,0,'',0,'ACTIVE','',''),(168,'FMME 60040','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Flurorocarbone réf. DF801 (joint français)',0,0,'',0,'ACTIVE','',''),(169,'FMME 60050','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Viton 70 shores gris 67Q10G',0,0,'',0,'ACTIVE','',''),(170,'FMME 60051','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Viton E 64 C8 80 shores',0,0,'',0,'ACTIVE','',''),(171,'FMME 60060','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone V4C7 noir (Le Joint Français)',0,0,'',0,'ACTIVE','',''),(172,'FMME 60070','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone Addix Réf. 6703 noir',0,0,'',0,'ACTIVE','',''),(173,'FMME 60071','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone Addix Réf. 6702 noir',0,0,'',0,'ACTIVE','',''),(174,'FMME 60072','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone Addix Réf. 6711',0,0,'',0,'ACTIVE','',''),(175,'FMME 60080','\"Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone FPM 75 shores code matière 02\"',0,0,'',0,'ACTIVE','',''),(176,'FMME 60090','\"Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone FPM 90 shores code matière 04\"',0,0,'',0,'ACTIVE','',''),(177,'FMME 60100','\"Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Kalrez code matière 06\"',0,0,'',0,'ACTIVE','',''),(178,'FMME 60101','\"Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / KalrezCode matière 78\"',0,0,'',0,'ACTIVE','',''),(179,'FMME 60110','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone 90Sh M-710 (FPMFKM)',0,0,'',0,'ACTIVE','',''),(180,'FMME 60130','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone 67G15 (73 shores)',0,0,'',0,'ACTIVE','',''),(181,'FMME 60140','\"Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Ecorubber2-75A-B-Perox FKM 75Sh résistance chimique élevée code matière 13\"',0,0,'',0,'ACTIVE','',''),(182,'FMME 60150','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbon FKM 78 shore basse température',0,0,'',0,'ACTIVE','',''),(183,'FMME 60160','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbon FKM 70 shore basse température',0,0,'',0,'ACTIVE','',''),(184,'FMME 60170','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / FKM 6801: 80 Sh Addix ED resistant',0,0,'',0,'ACTIVE','',''),(185,'FMME 60180','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / FKM 6901: 90 Sh Addix ED resistant',0,0,'',0,'ACTIVE','',''),(186,'FMME 60190','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone 67C10 (70 shores)',0,0,'',0,'ACTIVE','',''),(187,'FMME 60200','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone 70 shores',0,0,'',0,'ACTIVE','',''),(188,'FMME 60110','\"Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Viton 90 shores M-710 code matière 08\"',0,0,'',0,'ACTIVE','',''),(189,'FMME 60120','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone 67GX15 (78 shores)',0,0,'',0,'ACTIVE','',''),(190,'FMME 60210','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / Fluorocarbone E6722',0,0,'',0,'ACTIVE','',''),(191,'FMME 60230','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / FFKM 6791',0,0,'',0,'ACTIVE','',''),(192,'FMME 60240','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / FFKM 6790',0,0,'',0,'ACTIVE','',''),(193,'FMME 60250','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / FPM 70sh',0,0,'',0,'ACTIVE','',''),(194,'FMME 60260','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / FFKM (Perlast ICE G90LT)',0,0,'',0,'ACTIVE','',''),(195,'FMME 60220','Fluorocarbone  (Viton ,Perfluoré ,FKM,…) / FKM LR8912/90',0,0,'',0,'ACTIVE','',''),(196,'FMME 50070',' Fluorosilicone / Silicone fluoré chargé cuivre argenté GT 1007',0,0,'',0,'ACTIVE','',''),(197,'FMME 61020',' Fluorosilicone / Fluorisilicone 8000 (n 8653 SGM)',0,0,'',0,'ACTIVE','',''),(198,'FMME 61030',' Fluorosilicone / Fluorisilicone 8623 SGM',0,0,'',0,'ACTIVE','',''),(199,'FMME 61040',' Fluorosilicone / Fluorisilicone 8701 SGM',0,0,'',0,'ACTIVE','',''),(200,'FMME 61060',' Fluorosilicone / Fluorosilicone gris 70 shores 67D05G (STACEM)',0,0,'',0,'ACTIVE','',''),(201,'FMME 61070',' Fluorosilicone / Fluorosilicone LS63U marron',0,0,'',0,'ACTIVE','',''),(202,'FMME 61080','\" Fluorosilicone / Fluorosilicone bleu 60 shorescode matière 17\"',0,0,'',0,'ACTIVE','',''),(203,'FMME 61081',' Fluorosilicone / Fluorosilicone bleu 60 shores (Stacem 66D05)',0,0,'',0,'ACTIVE','',''),(204,'FMME 61090','\" Fluorosilicone / Fluorosilicone H.T. 60 shores F60Acode matière 18\"',0,0,'',0,'ACTIVE','',''),(205,'FMME 61100',' Fluorosilicone / Silicone fluoré isolant (Gételec GT 67)',0,0,'',0,'ACTIVE','',''),(206,'FMME 61110',' Fluorosilicone / Silicone isolant (Gételec GT 60)',0,0,'',0,'ACTIVE','',''),(207,'FMME 61140',' Fluorosilicone / Mélange silicone / silicone fuloré 65 shoreA',0,0,'',0,'ACTIVE','',''),(208,'FMME 61190',' Fluorosilicone / Silicone isolant fluoré GT 47',0,0,'',0,'ACTIVE','',''),(209,'FMME 61200','Borsil / Borsil 150-20',0,0,'',0,'ACTIVE','',''),(210,'FMME 50010','Silicone / Silicone conducteur / Silicone',0,0,'',0,'ACTIVE','',''),(211,'FMME 50011','Silicone / Silicone conducteur / Polasheet (joint conducteur)',0,0,'',0,'ACTIVE','',''),(212,'FMME 50080','Silicone / Silicone conducteur / Silicone isolant non fluoré GT 50',0,0,'',0,'ACTIVE','',''),(213,'FMME 50090','Silicone / Silicone conducteur / Silicone conducteur noir 60 shores - Addix 7660',0,0,'',0,'ACTIVE','',''),(214,'FMME 62020','Silicone / Silicone conducteur / Silicone fluoré conducteur (BL10007-70)',0,0,'',0,'ACTIVE','',''),(215,'FMME 62021','Silicone / Silicone conducteur / Silicone fluoré chargé carbone (conducteur)',0,0,'',0,'ACTIVE','',''),(216,'FMME 62022','Silicone / Silicone conducteur / Silicone fluoré chargé carbone (conducteur) BL 10007-60',0,0,'',0,'ACTIVE','',''),(217,'FMME 62030','Silicone / Silicone conducteur / Silicone conducteur (BL10000)',0,0,'',0,'ACTIVE','',''),(218,'FMME 62040','Silicone / Silicone conducteur / Silicone conducteur chargé cuivre argenté (7664 Silvershield)',0,0,'',0,'ACTIVE','',''),(219,'FMME 62041','Silicone / Silicone conducteur / Silicone conducteur chargé aluminium argenté (8666 Addix)',0,0,'',0,'ACTIVE','',''),(220,'FMME 62042','Silicone / Silicone conducteur / Silicone conducteur chargé aluminium argenté (7665 Addix)',0,0,'',0,'ACTIVE','',''),(221,'FMME 62043','Silicone / Silicone conducteur / Silicone fluoré 8408',0,0,'',0,'ACTIVE','',''),(222,'FMME 62044','Silicone / Silicone isoalnt / Silicone isolant 40sh à mouler',0,0,'',0,'ACTIVE','',''),(223,'FMME 62045','Silicone / Silicone conducteur / ',0,0,'',0,'ACTIVE','',''),(224,'FMME 70012','Silicone / Silicone conducteur / Silicone cellulaire',0,0,'',0,'ACTIVE','',''),(225,'FMME 861','Silicone / Silicone conducteur / Silicone MS 55 PP.0000SI-861',0,0,'',0,'ACTIVE','',''),(226,'FMME 61150','\"Silicone / Silicone conducteur / Silicone isolant gris foncé 70±5 ShoreMélange Stacem 57DR05N / Bilingue code matière 80\"',0,0,'',0,'ACTIVE','',''),(227,'FMME 61160','\"Silicone / Silicone conducteur / Silicone isolant gris foncé 88±5 Shore Mélange Stacem 59DR05N / Bilingue code matière 81\"',0,0,'',0,'ACTIVE','',''),(228,'FMME 62011','\"Silicone / Silicone conducteur / Silicone conducteur noir 70 Sh Mélange Stacem 57T05N ind.D / Bilingue code matière 82 (idem FMEL 62010)\"',0,0,'',0,'ACTIVE','',''),(229,'FMME 62050','\"Silicone / Silicone conducteur / Silicone conducteur noir 67±5 Shore Mélange Stacem 56T05N / Bilingue code matière 78\"',0,0,'',0,'ACTIVE','',''),(230,'FMME 52010','Silicone / Silicone conducteur / Silicone BS F 152, 153,159',0,0,'',0,'ACTIVE','',''),(231,'FMME 50091','\"Silicone / Silicone conducteur / Mélange silicone semi conducteur noir 60 shA ref 7631\"',0,0,'',0,'ACTIVE','',''),(232,'FMME 50092','Silicone / Silicone conducteur / Silicone bleu 40 shA ref 7415',0,0,'',0,'ACTIVE','',''),(233,'FMME 50100','Silicone / Silicone conducteur / Silicone 59D05 90 sh',0,0,'',0,'ACTIVE','',''),(234,'FMME 70010','Autres  / Polyéthylène Chloro Sulfone',0,0,'',0,'ACTIVE','',''),(235,'FMME 70011','Autres  / Mélange 976 T',0,0,'',0,'ACTIVE','',''),(236,'FMME 70018','Autres  / Elastomère NR rouge pour isolation électrique',0,0,'',0,'ACTIVE','',''),(237,'FMME 70020','Autres  / Joint polyurethane adhesif (Ref 5200 white)',0,0,'',0,'ACTIVE','',''),(238,'FMME 80001','Autres  / Cuir',0,0,'',0,'ACTIVE','',''),(239,'FMME 62060','Autres  / Enduction conductrice ADDIX End7001',0,0,'',0,'ACTIVE','',''),(240,'FTMP 102','GD00PDAP-102 - Diallyle phtalate',0,0,'',0,'ACTIVE','',''),(241,'FTMP 111','Néonite EG 60 - Polyepoxyde',0,0,'',0,'ACTIVE','',''),(242,'FTMP 112','Araldite NU 514 K - Polyepoxyde',0,0,'',0,'ACTIVE','',''),(243,'FTMP 113','\"Resine Polyuréthane de coulée  - Isocyanate UR 3450 Polyol UR 3460\"',0,0,'',0,'ACTIVE','',''),(244,'FTMP 121','Bakelite PF 31 - Bakelite',0,0,'',0,'ACTIVE','',''),(245,'FTMP 124','GD0000PF-124 - Phénoplaste',0,0,'',0,'ACTIVE','',''),(246,'FTMP 125','Bakelite - Phénoplaste 31-9005-S1',0,0,'',0,'ACTIVE','',''),(247,'FTMP 126','GD0000PF-126 - Phénoplaste',0,0,'',0,'ACTIVE','',''),(248,'FTMP 127','\"Résine phenolique - Phénol Formol\"',0,0,'',0,'ACTIVE','',''),(249,'FTMP 131','\"Résine de coulée - Stycast 2651 remplacée par Axson  RE 22891 et Durapot 861 Catalyst 9, Catalyst 11 ou Catalyst 23 LV\"',0,0,'',0,'ACTIVE','',''),(250,'FTMP 132','Thermodurcissable - polyester pré imprégné - SMC 35 LP 1391 R noir',0,0,'',0,'ACTIVE','',''),(251,'FTMP 133','Thermodurcissable - polyester pré imprégné - VYNCOLIT 2923 W BLACK',0,0,'',0,'ACTIVE','',''),(252,'FTMP 134','Colle polyurethane - DELO-PUR 9694',0,0,'',0,'ACTIVE','',''),(253,'FTMP 301','GPPBTPFV-301 - Polybutylene terephtalate',0,0,'',0,'ACTIVE','',''),(254,'FTMP 302','GP000PBT-302 - Polybutylene terephtalate',0,0,'',0,'ACTIVE','',''),(255,'FTMP 305','GD0000UP-305 - Polyester',0,0,'',0,'ACTIVE','',''),(256,'FTMP 401','Polyamide 12 - Grilamid LV-3H',0,0,'',0,'ACTIVE','',''),(257,'FTMP 403','Polyamide 12 - AZM30 noir T6L',0,0,'',0,'ACTIVE','',''),(258,'FTMP 404','GP00PA12-404 - Rilson AMN0 naturel',0,0,'',0,'ACTIVE','',''),(259,'FTMP 405','Polyamide 12 - AUM30 noir',0,0,'',0,'ACTIVE','',''),(260,'FTMP 407','Polyamide 12 - GP00PA12-408',0,0,'',0,'ACTIVE','',''),(261,'FTMP 408','\"Polyamide 12 - Grilamid - Vestamid - Rilsan A Pour usinage\"',0,0,'',0,'ACTIVE','',''),(262,'FTMP 421','Polyamide 11 - BMN noir W3',0,0,'',0,'ACTIVE','',''),(263,'FTMP 422','Polyamide 11 - BMN noir 90',0,0,'',0,'ACTIVE','',''),(264,'FTMP 441','PA6 - AKULON S223 E - Naturel',0,0,'',0,'ACTIVE','',''),(265,'FTMP 443','PA6 - AKULON Naturel F 223 D',0,0,'',0,'ACTIVE','',''),(266,'FTMP 444','PA6-66 - Grivory',0,0,'',0,'ACTIVE','',''),(267,'FTMP 446','PA6 - Grilon BGM-65 X V0',0,0,'',0,'ACTIVE','',''),(268,'FTMP 447','PA6 - Grilon A23HV0',0,0,'',0,'ACTIVE','',''),(269,'FTMP 448','PA6 - Sicoamide 6 (PA6FV015)',0,0,'',0,'ACTIVE','',''),(270,'FTMP 451','PA6 - Sicoamide PA6 MPX',0,0,'',0,'ACTIVE','',''),(271,'FTMP 452','PA6 - Sicoamide 6 (PA6FV030)',0,0,'',0,'ACTIVE','',''),(272,'FTMP 453','PA6 - Grilon BGM-40/2 X',0,0,'',0,'ACTIVE','',''),(273,'FTMP 454','PA6 - Sicoamide 6 naturel (PA6FV030)',0,0,'',0,'ACTIVE','',''),(274,'FTMP 455','PA6 - Grilon PV15H naturel',0,0,'',0,'ACTIVE','',''),(275,'FTMP 456','PA66 - Grilon TS VO 8834',0,0,'',0,'ACTIVE','',''),(276,'FTMP 457','PA66-PA6 30%FV - Grilon TSG-30/4 V0',0,0,'',0,'ACTIVE','',''),(277,'FTMP 472','PA66  - Technyl A 20 noir 25',0,0,'',0,'ACTIVE','',''),(278,'FTMP 474','\"PA66  - Maranyl A - Ultramide A - Zytel Technyl Pour usinage\"',0,0,'',0,'ACTIVE','',''),(279,'FTMP 475','PA66.50 - Staramide 66 G 50 V S3/3/039/DV',0,0,'',0,'ACTIVE','',''),(280,'FTMP 491','PA66 - Technyl B261 V30',0,0,'',0,'ACTIVE','',''),(281,'FTMP 492','PA66 - Rhodia Technyl A20 V25',0,0,'',0,'ACTIVE','',''),(282,'FTMP 494','GPPA66FV-494 - Polyamide 66',0,0,'',0,'ACTIVE','',''),(283,'FTMP 495','PAA66 - Radiflam ARV250 AF 333 noir',0,0,'',0,'ACTIVE','',''),(284,'FTMP 496','PA66 - Latamide 66 G / 20-VO naturel 0023',0,0,'',0,'ACTIVE','',''),(285,'FTMP 497','GPPA66FV-497 - Polyarylamide',0,0,'',0,'ACTIVE','',''),(286,'FTMP 498','PA66 - Polyamide 6/6 FV 30%',0,0,'',0,'ACTIVE','',''),(287,'FTMP 499','Ultramid A3U40G5 - PA66-GF25-FR',0,0,'',0,'ACTIVE','',''),(288,'FTMP 503','GP000PVC-503 - Polychlorure de vinyle',0,0,'',0,'ACTIVE','',''),(289,'FTMP 504','GP000PVC-504 - Polychlorure de vinyle',0,0,'',0,'ACTIVE','',''),(290,'FTMP 505','GP000PVC-505 - Polychlorure de vinyle',0,0,'',0,'ACTIVE','',''),(291,'FTMP 511','GP000PVC-511 - Polychlorure de vinyle',0,0,'',0,'ACTIVE','',''),(292,'FTMP 512','PVC Rigide (polymère de vinylchloride) - Chlorure de polyvinyle ROUGE ',0,0,'',0,'ACTIVE','',''),(293,'FTMP 552','GP0000PE-552 - Polyethylene basse densité',0,0,'',0,'ACTIVE','',''),(294,'FTMP 603','GP0000PE-603 - Makrolon 1805/2805 farbe 70394 ',0,0,'',0,'ACTIVE','',''),(295,'FTMP 604','GP0000PE-604 - Makrolon 6485 naturel',0,0,'',0,'ACTIVE','',''),(296,'FTMP 605','GP0000PC-605 - Makrolon 8035',0,0,'',0,'ACTIVE','',''),(297,'FTMP 606','\"Polycarbonate PC - Lexan - Makrolon - Axxis Pour usinage\"',0,0,'',0,'ACTIVE','',''),(298,'FTMP 651','YGP00PCFV-651-0000 - Polycarbonate Lexan 500R',0,0,'',0,'ACTIVE','',''),(299,'FTMP 652','Polycarbonate PC - Polycarbonate Lexan 500R naturel',0,0,'',0,'ACTIVE','',''),(300,'FTMP 653','GP00PCFV-653 - Makrolon 6485 noir',0,0,'',0,'ACTIVE','',''),(301,'FTMP 655','Polycarbonate PC FV   - ',0,0,'',0,'ACTIVE','',''),(302,'FTMP 656','Polycarbonate PC FV 30% - (Lexan - Makrolon - Axxis) pour usinage',0,0,'',0,'ACTIVE','',''),(303,'FTMP 761',' - ',0,0,'',0,'ACTIVE','',''),(304,'FTMP 762','Peek injecté - Peek 1000',0,0,'',0,'ACTIVE','',''),(305,'FTMP 763',' - ',0,0,'',0,'ACTIVE','',''),(306,'FTMP 764','\"Résine - SikaBiresin® RE 891-98 Resine / SikaBiresin® RE 203 Durcisseur\"',0,0,'',0,'ACTIVE','',''),(307,'FTMP 765','Résine - DURAPOT 861',0,0,'',0,'ACTIVE','',''),(308,'FTMP 766',' - ',0,0,'',0,'ACTIVE','',''),(309,'FTMP 767',' - ',0,0,'',0,'ACTIVE','',''),(310,'FTMP 768',' - ',0,0,'',0,'ACTIVE','',''),(311,'FTMP 803',' - ',0,0,'',0,'ACTIVE','',''),(312,'FTMP 830',' - ',0,0,'',0,'ACTIVE','',''),(313,'FTMP 831','Techtron HPV - PPS bleu fonçé - Indice D non diffusé',0,0,'',0,'ACTIVE','',''),(314,'FTMP 862','Santoprène (Thermo plastique vulcanisat) - Santoprène 101-55',0,0,'',0,'ACTIVE','',''),(315,'FTMP 864',' - ',0,0,'',0,'ACTIVE','',''),(316,'FTMP 867',' - ',0,0,'',0,'ACTIVE','',''),(317,'FTMP 868',' - ',0,0,'',0,'ACTIVE','',''),(318,'FTMP 873',' - ',0,0,'',0,'ACTIVE','',''),(319,'FTMP 874',' - ',0,0,'',0,'ACTIVE','',''),(320,'FTMP 875',' - ',0,0,'',0,'ACTIVE','',''),(321,'FTMP 876',' - ',0,0,'',0,'ACTIVE','',''),(322,'FTMP 877','Multiflex - ',0,0,'',0,'ACTIVE','',''),(323,'FTMP 878',' - ',0,0,'',0,'ACTIVE','',''),(324,'FTMP 879',' - ',0,0,'',0,'ACTIVE','',''),(325,'FTMP 880',' - ',0,0,'',0,'ACTIVE','',''),(326,'FTMP 881','Courbhane 99 shoreA - ',0,0,'',0,'ACTIVE','',''),(327,'FTMP 882','Courbhane 95 shoreA - ',0,0,'',0,'ACTIVE','',''),(328,'FTMP 920',' - ',0,0,'',0,'ACTIVE','',''),(329,'FTMP 922',' - ',0,0,'',0,'ACTIVE','',''),(330,'FTMP 930',' - ',0,0,'',0,'ACTIVE','',''),(331,'FTMP 931',' - ',0,0,'',0,'ACTIVE','',''),(332,'FTMP 932','PTFE chargé 25% carbone - ',0,0,'',0,'ACTIVE','',''),(333,'FTMP 933','Téflon chargé verre M-710 - Turcon T42 (Trelleborg)',0,0,'',0,'ACTIVE','',''),(334,'FTMP 934','Téflon chargé carbone M-710 - Turcon T12 (Trelleborg)',0,0,'',0,'ACTIVE','',''),(335,'FTMP 935','FEP  - FEP',0,0,'',0,'ACTIVE','',''),(336,'FTMP 936','PTFE GF - ',0,0,'',0,'ACTIVE','',''),(337,'FTMP 937','PTFE + 25% Graphite Norsok - PTFE + 25% Graphite Norsok M-630 / P22',0,0,'',0,'ACTIVE','',''),(338,'FTMP 950','Ultem 2300 - SABIC (fabricant)',0,0,'',0,'ACTIVE','',''),(339,'FTMP 951',' - ',0,0,'',0,'ACTIVE','',''),(340,'FTMP 952',' - ',0,0,'',0,'ACTIVE','',''),(341,'FTMP 953',' - ',0,0,'',0,'ACTIVE','',''),(342,'FTMP 954',' - ',0,0,'',0,'ACTIVE','',''),(343,'FTMP 955',' - ',0,0,'',0,'ACTIVE','',''),(344,'FTMP 956',' - ',0,0,'',0,'ACTIVE','',''),(345,'FTMP 957',' - ',0,0,'',0,'ACTIVE','',''),(346,'FTMP 958','\"POM C - Polyacetal (ISO: polyoxymethyylène ou polyformaldéhyde)\"',0,0,'',0,'ACTIVE','',''),(347,'FTMP 959','PEEK 450 G - Poly Ether Ether Ketone',0,0,'',0,'ACTIVE','',''),(348,'FTMP 960','PEEK 450 GL30 - Poly Ether Ether Ketone chargé verre',0,0,'',0,'ACTIVE','',''),(349,'FTMP 961','PTFE + Turcon - Turcon T05',0,0,'',0,'ACTIVE','',''),(350,'FTMP 962','G10 - Epoxy + fibre de verre',0,0,'',0,'ACTIVE','',''),(351,'FTMP 968','POM C - POM C stabilisé',0,0,'',0,'ACTIVE','',''),(352,'FTMP 969',' - ',0,0,'',0,'ACTIVE','',''),(353,'FTMP 970','Intercomposite - ',0,0,'',0,'ACTIVE','',''),(354,'FTMP 971','PEEK ST - Poly Ether Ether Ketone Haute température',0,0,'',0,'ACTIVE','',''),(355,'FTMP 972','Résine  - Duralco 4538',0,0,'',0,'ACTIVE','',''),(356,'FTMP 973','Colle  - Colle Epoxy EPO-TEK 353 ND',0,0,'',0,'ACTIVE','',''),(357,'FTMP 974','Colle - Colle UV PERMABOND UV7141',0,0,'',0,'ACTIVE','',''),(358,'FTMP 975','Colle - Colle UV DSM 950-200',0,0,'',0,'ACTIVE','',''),(359,'FTMP 976','PTFE     - Fluolion',0,0,'',0,'ACTIVE','',''),(360,'FTMP 977','Ultem 2300 - Ultem 2300 pour usinage',0,0,'',0,'ACTIVE','',''),(361,'FTMP 978','PEEK 450 G 3.1 - PEEK 450 G avec fourniture d\'un certificat 3.1',0,0,'',0,'ACTIVE','',''),(362,'FTMP 979','PEEK 450 GL 30 3.1 - PEEK 450 GL 30 avec fourniture d\'un certificat 3.1',0,0,'',0,'ACTIVE','',''),(363,'FTMP 980','VESPEL SCP-5000 - Polyimide (PI)',0,0,'',0,'ACTIVE','',''),(364,'FTMP 981','TORLON 4203L - Polyamide-imide (PAI)',0,0,'',0,'ACTIVE','',''),(365,'FTMP 982',' - Polybenzimidazole (PBI)',0,0,'',0,'ACTIVE','',''),(366,'FTMP 983','PMMA incolore - PMMA incolore',0,0,'',0,'ACTIVE','',''),(367,'FTMP 984','Colle Epoxy  - Permabond ES569',0,0,'',0,'ACTIVE','',''),(368,'FTMP 985','Polycarbonate PC pour Usinage - Lexan Resin 141-R',0,0,'',0,'ACTIVE','',''),(369,'FTMP 1000',' - ',0,0,'',0,'ACTIVE','',''),(370,'FTMP 1050',' - ',0,0,'',0,'ACTIVE','',''),(371,'FTMP 1051',' - ',0,0,'',0,'ACTIVE','',''),(372,'FTMP 1052',' - ',0,0,'',0,'ACTIVE','',''),(373,'FTMP 1053',' - ',0,0,'',0,'ACTIVE','',''),(374,'FTMP 1054',' - ',0,0,'',0,'ACTIVE','',''),(375,'FTMP 1055',' - ',0,0,'',0,'ACTIVE','',''),(376,'FTMP 1056',' - ',0,0,'',0,'ACTIVE','',''),(377,'FTMP 1057','Nylatron - Nylatron® 66 SA FR',0,0,'',0,'ACTIVE','',''),(378,'FTMP 2000','Accura Xtreme  - Accura Xtreme / Quickparts',0,0,'',0,'ACTIVE','',''),(379,'FMTS 1010','ARGENTURE SUR CUIVRE',0,0,'',0,'ACTIVE','',''),(380,'FMTS 1011','ARGENTURE SUR PEEK ',0,0,'',0,'ACTIVE','',''),(381,'FMTS 1012','ARGENTURE SUR INOX',0,0,'',0,'ACTIVE','',''),(382,'FMTS 1013','PRE-DORURE + ARGENTURE SUR CUIVRE',0,0,'',0,'ACTIVE','',''),(383,'FMTS 1015','PRE-DORURE + ARGENTURE SUR CUIVRE',0,0,'',0,'ACTIVE','',''),(384,'FMTS 1017','PRE-NICKELAGE+ ARGENTURE SUR CUIVRE',0,0,'',0,'ACTIVE','',''),(385,'FMTS 1020','DORURE 0,4 à 2',0,0,'',0,'ACTIVE','',''),(386,'FMTS 1021','DORURE 0,4 ',0,0,'',0,'ACTIVE','',''),(387,'FMTS 1022','DEPOT ELECTROLYTIQUE D\'OR',0,0,'',0,'ACTIVE','',''),(388,'FMTS 1023','DEPOT ELECTROLYTIQUE D\'OR / COBALT',0,0,'',0,'ACTIVE','',''),(389,'FMTS 1024','DEPOT ELECTROLYTIQUE D\'OR / Ni',0,0,'',0,'ACTIVE','',''),(390,'FMTS 1025','DORURE SUR INOX',0,0,'',0,'ACTIVE','',''),(391,'FMTS 1026','Dorure code 822',0,0,'',0,'ACTIVE','',''),(392,'FMTS 1027','Dorure code 823',0,0,'',0,'ACTIVE','',''),(393,'FMTS 1028','PRE-DORURE + ARGENTURE + DORURE SUR CUIV.',0,0,'',0,'ACTIVE','',''),(394,'FMTS 1029','DEPOT ELECTROLYTIQUE D\'OR 0,2 µm',0,0,'',0,'ACTIVE','',''),(395,'FMTS 1030','CADMIAGE',0,0,'',0,'ACTIVE','',''),(396,'FMTS 1031','CADMIAGE VERT OLIVE',0,0,'',0,'ACTIVE','',''),(397,'FMTS 1032','CADMIAGE',0,0,'',0,'ACTIVE','',''),(398,'FMTS 1033','REVETEMENT CADMIUM BLANC',0,0,'',0,'ACTIVE','',''),(399,'FMTS 1034','REVETEMENT CADMIUM BLANC',0,0,'',0,'ACTIVE','',''),(400,'FMTS 1035','CADMIAGE NOIR SUR ALUMINIUM',0,0,'',0,'ACTIVE','',''),(401,'FMTS 1036','FLASH D\'OR',0,0,'',0,'ACTIVE','',''),(402,'FMTS 1040','ETAMAGE',0,0,'',0,'ACTIVE','',''),(403,'FMTS 1050','NICKEL BRILLANT ELECTROLITIQUE 10 µm SUR CUIVRE',0,0,'',0,'ACTIVE','',''),(404,'FMTS 1051','NICKEL MAT ELECTROLITIQUE 10 µm SUR CUIVRE',0,0,'',0,'ACTIVE','',''),(405,'FMTS 1052','NICKEL BRILLANT ELECTROLITIQUE 3 µm SUR CUIVRE',0,0,'',0,'ACTIVE','',''),(406,'FMTS 1053','NICKELAGE SUR PEEK ',0,0,'',0,'ACTIVE','',''),(407,'FMTS 1054','NICKELAGE SUR PEEK ',0,0,'',0,'ACTIVE','',''),(408,'FMTS 1060','ZINC / NICKEL',0,0,'',0,'ACTIVE','',''),(409,'FMTS 1061','ZINGAGE NOIR ELECTROLYTIQUE SOUS-COUCHE NICKEL',0,0,'',0,'ACTIVE','',''),(410,'FMTS 1062','DEPOT ELECTROLYTIQUE DE ZINC PASSIVE INCOLORE',0,0,'',0,'ACTIVE','',''),(411,'FMTS 1063','DACROMET 500 B',0,0,'',0,'ACTIVE','',''),(412,'FMTS 1064','DEPOT ELECTROLYTIQUE DE ZINC NICKEL',0,0,'',0,'ACTIVE','',''),(413,'FMTS 1065','PASSIV. NOIRE A L\'ARGENT SUR ZINC ELECTROLITHIQUE',0,0,'',0,'ACTIVE','',''),(414,'FMTS 1066','BRONZE BLANC',0,0,'',0,'ACTIVE','',''),(415,'FMTS 1067','PASSIVATION DECONTAMINATION SUR INOX',0,0,'',0,'ACTIVE','',''),(416,'FMTS 1068','Satiné',0,0,'',0,'ACTIVE','',''),(417,'FMTS 1069','Boruration /  Boronizing',0,0,'',0,'ACTIVE','',''),(418,'FMTS 1070','Microseal (LUBRIFIANT SEC AU BISULFURE DE MOLYBDENE MOS2)',0,0,'',0,'ACTIVE','',''),(419,'FMTS 2010','NICKEL CHIMIQUE 25 µm sur AG5M',0,0,'',0,'ACTIVE','',''),(420,'FMTS 2011','NICKEL CHIMIQUE',0,0,'',0,'ACTIVE','',''),(421,'FMTS 2012','THERMOZINC',0,0,'',0,'ACTIVE','',''),(422,'FMTS 2013','NICKEL CHIMIQUE 25 µm sur AU4G',0,0,'',0,'ACTIVE','',''),(423,'FMTS 2014','NICKEL CHIMIQUE 7 µm sur ALUMINIUMS',0,0,'',0,'ACTIVE','',''),(424,'FMTS 2015','NICKEL CHIMIQUE 25 µm sur ALUMINIUMS',0,0,'',0,'ACTIVE','',''),(425,'FMTS 2016','DEPÔTS CHIMIQUE ET ELECTROLYTIQUE DE NICKEL',0,0,'',0,'ACTIVE','',''),(426,'FMTS 5001','VERNIS DE GLISSEMENT MOLYkOTE D-7405',0,0,'',0,'ACTIVE','',''),(427,'FMTS 5002','VERNIS ACRYLIQUE POLYURETHANE  DORE / ALODINE1200',0,0,'',0,'ACTIVE','',''),(428,'FMTS 5003','PEINTURE BRAI EPOXY NOIR (sans solvant Epimastic LC)',0,0,'',0,'ACTIVE','',''),(429,'FMTS 5004','PEINTURE BRAI EPOXY NOIR (Immersion industrielle)',0,0,'',0,'ACTIVE','',''),(430,'FMTS 5005','VERNIS ACRYLIQUE POLYURETHANE  NOIR / ALODINE1200',0,0,'',0,'ACTIVE','',''),(431,'FMTS 5006','VERNISSAGE HERMETIQUE ELECTROFUGE 200',0,0,'',0,'ACTIVE','',''),(432,'FMTS 5007','Vernis de glissement – Lubrifiant sec MOLYKOTE D-3484',0,0,'',0,'ACTIVE','',''),(433,'FMTS 5052','PEINTURE  EPOXY RAL 7035',0,0,'',0,'ACTIVE','',''),(434,'FMTS 5501','VERNIS ANTIFLASH ROUGE BRIQUE',0,0,'',0,'ACTIVE','',''),(435,'FMTS 5502','PEINTURE VERT MARTELE',0,0,'',0,'ACTIVE','',''),(436,'FMTS 5503','VERNIS D\' IMPREGNATION',0,0,'',0,'ACTIVE','',''),(437,'FMTS 5504','PEINTURE EPOXY ROUGE VERMILLON (ROUGE CKB)',0,0,'',0,'ACTIVE','',''),(438,'FMTS 5505','PEINTURE  EPOXY RAL 1006',0,0,'',0,'ACTIVE','',''),(439,'FMTS 5506','PEINTURE EPOXY GRIS CEA (BAC)',0,0,'',0,'ACTIVE','',''),(440,'FMTS 5507','PEINTURE  EPOXY RAL 5012',0,0,'',0,'ACTIVE','',''),(441,'FMTS 5508','PEINTURE EPOXY RAL 1016',0,0,'',0,'ACTIVE','',''),(442,'FMTS 5509','PEINTURE EPOXY RAL 6018',0,0,'',0,'ACTIVE','',''),(443,'FMTS 5510','PEINTURE CONDUCTRICE',0,0,'',0,'ACTIVE','',''),(444,'FMTS 5511','PEINTURE VERT OTAN',0,0,'',0,'ACTIVE','',''),(445,'FMTS 5512','PEINTURE SABLE DU DESERT',0,0,'',0,'ACTIVE','',''),(446,'FMTS 5513','PEINTURE EPOXY RAL 9005 (sur alliage d\'aluminium)',0,0,'',0,'ACTIVE','',''),(447,'FMTS 5514','PEINTURE EPOXY RAL 8007',0,0,'',0,'ACTIVE','',''),(448,'FMTS 5515','PEINTURE EPOXY RAL 7022',0,0,'',0,'ACTIVE','',''),(449,'FMTS 5516','PEINTURE EPOXY RAL 8019',0,0,'',0,'ACTIVE','',''),(450,'FMTS 5517','PEINTURE POLYURETHANE RAL 3020',0,0,'',0,'ACTIVE','',''),(451,'FMTS 5518','PEINTURE EPOXY RAL 9011 ',0,0,'',0,'ACTIVE','',''),(452,'FMTS 5519','PEINTURE EPOXY RAL 7016 ',0,0,'',0,'ACTIVE','',''),(453,'FMTS 5520','PEINTURE EPOXY RAL 7024 ',0,0,'',0,'ACTIVE','',''),(454,'FMTS 5521','PEINTURE EPOXY RAL 7001 ',0,0,'',0,'ACTIVE','',''),(455,'FMTS 5522','PEINTURE EPOXY RAL 2008 ',0,0,'',0,'ACTIVE','',''),(456,'FMTS 5523','PEINTURE EPOXY RAL 7031 ',0,0,'',0,'ACTIVE','',''),(457,'FMTS 5524','PEINTURE EPOXY RAL 7012',0,0,'',0,'ACTIVE','',''),(458,'FMTS 5525','PEINTURE EPOXY RAL 5013 ',0,0,'',0,'ACTIVE','',''),(459,'FMTS 5526','PEINTURE BLEU OTAN',0,0,'',0,'ACTIVE','',''),(460,'FMTS 5528','PEINTURE EPOXY RAL 5002 ',0,0,'',0,'ACTIVE','',''),(461,'FMTS 5529','PEINTURE POLYURETHANE RAL 7024 ',0,0,'',0,'ACTIVE','',''),(462,'FMTS 5530','REVETEMENT TEFLON',0,0,'',0,'ACTIVE','',''),(463,'FMTS 5531','PEINTURE EPOXY RAL 7021 ',0,0,'',0,'ACTIVE','',''),(464,'FMTS 5532','PEINTURE ANTI-FLASH SANS ALODINE ',0,0,'',0,'ACTIVE','',''),(465,'FMTS 5533','RESSUAGE',0,0,'',0,'ACTIVE','',''),(466,'FMTS 5534','PEINTURE POLYURETHANE RAL 3001',0,0,'',0,'ACTIVE','',''),(467,'FMTS 5535','PEINTURE POLYURETHANE RAL DESIGN 0506080',0,0,'',0,'ACTIVE','',''),(468,'FMTS 5536','PEINTURE EPOXY RAL 9005 (sur pièce en bronze)',0,0,'',0,'ACTIVE','',''),(469,'FMTS 5537','PEINTURE EPOXY RAL 9011 (3 couches - ép 90 µm)',0,0,'',0,'ACTIVE','',''),(470,'FMTS 5538','PEINTURE EPOXY ROUGE VERMILLON (ROUGE CKB)(3 couches - ép 90 µm)',0,0,'',0,'ACTIVE','',''),(471,'FMTS 5539','Ceramic Epoxy Coating suivant spec FMC C80129 REVETEMENT CERAMIQUE',0,0,'',0,'ACTIVE','',''),(472,'FMTS 5540','Laque Epoxy rouge',0,0,'',0,'ACTIVE','',''),(473,'FMTS 5541','VERNIS ACRYLIQUE POLYURETHANE  NOIR / SURTEC650',0,0,'',0,'ACTIVE','',''),(474,'FMTS 5542','Revêtement anti-friction MOLYKOTE 3400A Lead-free / SURTEC650',0,0,'',0,'ACTIVE','',''),(475,'FMTS 5543','Traitement thermique enverrage',0,0,'',0,'ACTIVE','',''),(476,'FMTS 5545','PEINTURE EPOXY RAL 7022/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(477,'FMTS 5546','Laque polyuréthane bi-composant rouge/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(478,'FMTS 5547','Laque polyuréthane bi-composant bleue/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(479,'FMTS 5548','Laque polyuréthane bi-composant noire/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(480,'FMTS 5549','Laque polyuréthane bi-composant blanche/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(481,'FMTS 5550','Laque polyuréthane bi-composant jaune/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(482,'FMTS 5551','PEINTURE EPOXY RAL 1016/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(483,'FMTS 5552','PEINTURE EPOXY RAL 7012/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(484,'FMTS 5553','VERNIS ANTIFLASH ROUGE BRIQUE/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(485,'FMTS 5554','PEINTURE EPOXY GRIS CEA (BAC) / SURTEC 650',0,0,'',0,'ACTIVE','',''),(486,'FMTS 5555','PEINTURE EPOXY RAL 8019 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(487,'FMTS 5556','PEINTURE EPOXY RAL 7031 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(488,'FMTS 5557','PEINTURE EPOXY ROUGE VERMILLON (ROUGE CKB)/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(489,'FMTS 5558','PEINTURE EPOXY RAL 5013 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(490,'FMTS 5559','PEINTURE EPOXY RAL 7024  / SURTEC 650',0,0,'',0,'ACTIVE','',''),(491,'FMTS 5560','PEINTURE EPOXY RAL 7021 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(492,'FMTS 5561','PEINTURE EPOXY ROUGE VERMILLON (ROUGE CKB)(3 couches - ep 90 µm)/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(493,'FMTS 5562','\"EINTURE SABLE DU DESERT /SURTEC 650\"',0,0,'',0,'ACTIVE','',''),(494,'FMTS 5563','VERNIS ACRYLIQUE POLYURETHANE  DORE  / SURTEC 650',0,0,'',0,'ACTIVE','',''),(495,'FMTS 5564','PEINTURE POLYURETHANE RAL DESIGN 0506080 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(496,'FMTS 5565','PEINTURE EPOXY RAL 6018  / SURTEC 650',0,0,'',0,'ACTIVE','',''),(497,'FMTS 5566','Laque Epoxy rouge  / SURTEC 650',0,0,'',0,'ACTIVE','',''),(498,'FMTS 5567','PEINTURE POLYURETHANE RAL 3001  / SURTEC 650',0,0,'',0,'ACTIVE','',''),(499,'FMTS 5568','PEINTURE EPOXY RAL 2008 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(500,'FMTS 5569','PEINTURE POLYURETHANE RAL 3020 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(501,'FMTS 5570','PEINTURE EPOXY RAL 7016 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(502,'FMTS 5571','PEINTURE EPOXY RAL 5002 / SURTEC 650',0,0,'',0,'ACTIVE','',''),(503,'FMTS 5572','PEINTURE  EPOXY RAL 7035/ SURTEC 650',0,0,'',0,'ACTIVE','',''),(504,'FMTS 5573','PEINTURE EPOXY RAL 9011 ',0,0,'',0,'ACTIVE','',''),(505,'FMTS 5575','PEINTURE POLYURETHANE RAL 9023  / SURTEC 650',0,0,'',0,'ACTIVE','',''),(506,'FMTS 5576','PEINTURE PU GRISE',0,0,'',0,'ACTIVE','',''),(507,'FMTS 5587','PTFE Xylan',0,0,'',0,'ACTIVE','',''),(508,'FMTS 12010','ALODINE 1200',0,0,'',0,'ACTIVE','',''),(509,'FMTS 12011','OXYDATION ANODIQUE CHROMIQUE',0,0,'',0,'ACTIVE','',''),(510,'FMTS 12012','OXYDATION ANODIQUE SULFURIQUE',0,0,'',0,'ACTIVE','',''),(511,'FMTS 12013','ALODINE 1500',0,0,'',0,'ACTIVE','',''),(512,'FMTS 12015','Conversion CR3',0,0,'',0,'ACTIVE','',''),(513,'FMTS 12016','PHOSPHATATION ZINC',0,0,'',0,'ACTIVE','',''),(514,'FMTS 12017','TRAITEMENT PARYLENE',0,0,'',0,'ACTIVE','',''),(515,'FMTS 12018','METALLISATION SUR CERAMIQUE',0,0,'',0,'ACTIVE','',''),(516,'FMTS STD 550-1-35','\"DORURE DES CONTACTS EN FERRO-NICKEL POUR LES BOITIERS HERMETIQUES\"',0,0,'',0,'ACTIVE','',''),(517,'FMPV 5000','Diluant pour Vernis de glissement / MOLYCOTE Q5-7414',0,0,'',0,'ACTIVE','',''),(518,'FMPV 5001','Vernis de glissement / MOLYCOTE Q5-7405',0,0,'',0,'ACTIVE','',''),(519,'FMPV 5002','Vernis de glissement / MOLYCOTE D-7405',0,0,'',0,'ACTIVE','',''),(520,'FMPV 5003','Diluant pour Vernis de glissement / MOLYCOTE 7415 THINNER',0,0,'',0,'ACTIVE','',''),(521,'FMPV 5004','Vernis de glissement / MOLYCOTE D-3484',0,0,'',0,'ACTIVE','',''),(522,'FMPV 5007','Peinture primaire / ',0,0,'',0,'ACTIVE','',''),(523,'FMPV 5008','Laque époxy gris / EFIPOX EPX-L78 16057',0,0,'',0,'ACTIVE','',''),(524,'FMPV 5008-1','Durcisseur époxy / D 780 000',0,0,'',0,'ACTIVE','',''),(525,'FMPV 5008-2','Diluant époxy / S 780 000',0,0,'',0,'ACTIVE','',''),(526,'FMPV 5014','Vernis acylique polyuréthane doré / EFITAN FOUR V76 1128',0,0,'',0,'ACTIVE','',''),(527,'FMPV 5015','Peinture / EFILYD M59 19023',0,0,'',0,'ACTIVE','',''),(528,'FMPV 5016','Vernis acylique polyuréthane noir / EFITAN FOUR V76 16001',0,0,'',0,'ACTIVE','',''),(529,'FMPV 5021','Apprêt polyuréthane / EFITAN PUA-A72… bi-composants',0,0,'',0,'ACTIVE','',''),(530,'FMPV 5022','Diluant de nettoyage / EFINET NO1 00000',0,0,'',0,'ACTIVE','',''),(531,'FMPV 5027','Vernis isolant antiflash / EFILYD V49 10201',0,0,'',0,'ACTIVE','',''),(532,'FMPV 5036','Peinture / EFIPOX EPX-L78 18033',0,0,'',0,'ACTIVE','',''),(533,'FMPV 5040','Vernis d\'imprégnation / EFILUX V01 11522',0,0,'',0,'ACTIVE','',''),(534,'FMPV 5040-1','Diluant pour vernis d\'imprégnation / S01 00000',0,0,'',0,'ACTIVE','',''),(535,'FMPV 5047-1','Durcisseur pour laque polyuréthane / P.U.A. D72 00000',0,0,'',0,'ACTIVE','',''),(536,'FMPV 5047-2','Diluant pour laque polyuréthane / P.U.A. S72 00000',0,0,'',0,'ACTIVE','',''),(537,'FMPV 5048','Primaire / EP 201 C',0,0,'',0,'ACTIVE','',''),(538,'FMPV 5053','Peinture époxy / RAL 7035',0,0,'',0,'ACTIVE','',''),(539,'FMPV 5055','Vernis de glissement Aérosol / MOLYCOTE 321 R',0,0,'',0,'ACTIVE','',''),(540,'FMPV 5060','Peinture époxy / RAL 5012',0,0,'',0,'ACTIVE','',''),(541,'FMPV 5061','Peinture époxy / RAL 1016',0,0,'',0,'ACTIVE','',''),(542,'FMPV 5062','Peinture époxy / RAL 6018',0,0,'',0,'ACTIVE','',''),(543,'FMPV 5063','Peinture époxy / RAL 1006',0,0,'',0,'ACTIVE','',''),(544,'FMPV 5065','Peinture conductrice / EFIRYL A74 26005',0,0,'',0,'ACTIVE','',''),(545,'FMPV 5067','Peinture polyuréthane / EFITAN PUA L72 24203',0,0,'',0,'ACTIVE','',''),(546,'FMPV 5069','Peinture / EFITAN PUA L72 32109',0,0,'',0,'ACTIVE','',''),(547,'FMPV 5070','Peinture époxy / RAL 9005',0,0,'',0,'ACTIVE','',''),(548,'FMPV 5071','Peinture époxy / RAL 8007',0,0,'',0,'ACTIVE','',''),(549,'FMPV 5073','Peinture époxy / RAL 7022',0,0,'',0,'ACTIVE','',''),(550,'FMPV 5074','Peinture époxy / RAL 8019',0,0,'',0,'ACTIVE','',''),(551,'FMPV 5075','Peinture époxy / RAL 3020',0,0,'',0,'ACTIVE','',''),(552,'FMPV 5076','Peinture époxy / RAL 7024',0,0,'',0,'ACTIVE','',''),(553,'FMPV 5077','Peinture époxy / RAL 9011',0,0,'',0,'ACTIVE','',''),(554,'FMPV 5078','Peinture époxy / RAL 7016',0,0,'',0,'ACTIVE','',''),(555,'FMPV 5079','Peinture époxy / RAL 7001',0,0,'',0,'ACTIVE','',''),(556,'FMPV 5080','Peinture époxy / RAL 2008',0,0,'',0,'ACTIVE','',''),(557,'FMPV 5081','Peinture époxy / RAL 7031',0,0,'',0,'ACTIVE','',''),(558,'FMPV 5082','Peinture époxy / RAL 7012',0,0,'',0,'ACTIVE','',''),(559,'FMPV 5083','Laque époxy bi-composants / EFIPOX EPX L78 15032',0,0,'',0,'ACTIVE','',''),(560,'FMPV 5084','Apprêt glycérophtalique / EFILYD GSA A49 28013',0,0,'',0,'ACTIVE','',''),(561,'FMPV 5085','Vernis fongicide / ',0,0,'',0,'ACTIVE','',''),(562,'FMPV 5086','Laque époxy / EFIPOX EPX - L 78 25169',0,0,'',0,'ACTIVE','',''),(563,'FMPV 5088','Peinture époxy / RAL 5002',0,0,'',0,'ACTIVE','',''),(564,'FMPV 5089','Peinture époxy / RAL 2004',0,0,'',0,'ACTIVE','',''),(565,'FMPV 5090','Peinture polyuréthane / RAL 3020',0,0,'',0,'ACTIVE','',''),(566,'FMPV 5091','Peinture polyuréthane / RAL 7024',0,0,'',0,'ACTIVE','',''),(567,'FMPV 5092','Apprêt polyuréthane / EFITAN PUA A72 26012',0,0,'',0,'ACTIVE','',''),(568,'FMPV 5093','Apprêt époxy bi-composants / EFIPOX EPX A78 26472',0,0,'',0,'ACTIVE','',''),(569,'FMPV 5093-1','Durcisseur pour apprêt époxy / D78 100000',0,0,'',0,'ACTIVE','',''),(570,'FMPV 5094','Durcisseur / P.U.A. NJD75 00000',0,0,'',0,'ACTIVE','',''),(571,'FMPV 5095','Peinture époxy / RAL 7021',0,0,'',0,'ACTIVE','',''),(572,'FMPV 5096','Peinture conductrice / ECRYL 1',0,0,'',0,'ACTIVE','',''),(573,'FMPV 5097','Peinture polyuréthane / EFITAN PUA A72 23020 (RAL 3001)',0,0,'',0,'ACTIVE','',''),(574,'FMPV 5098','Peinture polyuréthane / PUA ORANGE DESIGN 0506080 L72 11069',0,0,'',0,'ACTIVE','',''),(575,'FMPV 5099','Enduction silicone isolant / DOW CORNING SYLGARD 577',0,0,'',0,'ACTIVE','',''),(576,'FMPV 5100','Laque Epoxy / RAL 3000',0,0,'',0,'ACTIVE','',''),(577,'FMPV 5101','Laque polyuréthane bi-composant / RAL 3020',0,0,'',0,'ACTIVE','',''),(578,'FMPV 5102','Laque polyuréthane bi-composant / RAL 5005',0,0,'',0,'ACTIVE','',''),(579,'FMPV 5103','Laque polyuréthane bi-composant / RAL 9005',0,0,'',0,'ACTIVE','',''),(580,'FMPV 5104','Laque polyuréthane bi-composant / RAL 9003',0,0,'',0,'ACTIVE','',''),(581,'FMPV 5105','Laque polyuréthane bi-composant / RAL 1023',0,0,'',0,'ACTIVE','',''),(582,'FMPV 5106','Peinture silicone LSR 2345/07 / Silopren LSR 2345-07',0,0,'',0,'ACTIVE','',''),(583,'FMPV 5107','Laque polyuréthane bi-composant / RAL 7012',0,0,'',0,'ACTIVE','',''),(584,'FMPV 5108','Apprêt époxy-polyamide / ',0,0,'',0,'ACTIVE','',''),(585,'FMPV 5109','Apprêt époxy-polyamide / ',0,0,'',0,'ACTIVE','',''),(586,'FMPV 5110','Encre de marquage verte / TP101319 (ink) + TP120108 VDS 1015 (thinner)',0,0,'',0,'ACTIVE','',''),(587,'FMPV 5111','Encre de marquage rouge / TP110230 (ink) + TP120106 VDS 380 (thinner)',0,0,'',0,'ACTIVE','',''),(588,'FMPV 5112','Encre de marquage jaune / TP100603 (ink) + TP120102 VD 2 (thinner)',0,0,'',0,'ACTIVE','',''),(589,'FMLU 0001','Kluberalfa XZ 3-1',0,0,'',0,'ACTIVE','',''),(590,'FMLU 0002','Kluberalfa DH 3-100',0,0,'',0,'ACTIVE','',''),(591,'FMLU 0003','Graisse silicone LUBRILOG PE 1283',0,0,'',0,'ACTIVE','',''),(592,'FMLU 0004','Graisse Lanoline CODEX',0,0,'',0,'ACTIVE','',''),(593,'FMLU 0005','Midel 7131',0,0,'',0,'ACTIVE','',''),(594,'FMLU 0006','Molykote G-N PLUS',0,0,'',0,'ACTIVE','',''),(595,'FMLU 0007','Huile vaseline Finavestan A 306 B',0,0,'',0,'ACTIVE','',''),(596,'FMLU 0008','Graisse silicone Dow Corning DC4',0,0,'',0,'ACTIVE','',''),(597,'FMLU 0009','Graisse silicone ORAPI',0,0,'',0,'ACTIVE','',''),(598,'FMLU 0010','Mélange pour dénudage chimique',0,0,'',0,'ACTIVE','',''),(599,'FMLU 0011','Ethanol',0,0,'',0,'ACTIVE','',''),(600,'FMLU 0012','Hydroxyde de potassium KOH',0,0,'',0,'ACTIVE','',''),(601,'FMLU 0013','Graisse molycote TP 42',0,0,'',0,'ACTIVE','',''),(602,'FMLU 0014','Graisse contactal HPG FOSECO',0,0,'',0,'ACTIVE','',''),(603,'FMLU 0015','Loctite 577',0,0,'',0,'ACTIVE','',''),(604,'FMLU 0016','DOW CORNING 732',0,0,'',0,'ACTIVE','',''),(605,'FMLU 0017','Xiameter (R) PMX-200 silicone fluid 1000CS',0,0,'',0,'ACTIVE','',''),(606,'FMLU 0018','Huile Envirotemp 374 0019 00 00 000',0,0,'',0,'ACTIVE','',''),(607,'FMLU 0019','Graisse molycote HCS Plus',0,0,'',0,'ACTIVE','',''),(608,'FMLU 0020','Graisse molycote G-Rapid Plus',0,0,'',0,'ACTIVE','',''),(609,'FMLU 0021','Xiameter (R) PMX-200 silicone fluid 100CS',0,0,'',0,'ACTIVE','',''),(610,'FMLU 0024','Mirasil PTM',0,0,'',0,'ACTIVE','',''),(611,'FMLU 0025','Gel optique SpectraSyn4',0,0,'',0,'ACTIVE','',''),(612,'FMLU 0026','Graisse Kluber Barrierta I EL-102',0,0,'',0,'ACTIVE','',''),(613,'FMLU 0027','Graisse Wacker Powersil Paste AP',0,0,'',0,'ACTIVE','',''),(614,'FMLU 0028','Huile Wacker Powersil Fluid TR50',0,0,'',0,'ACTIVE','',''),(615,'FMLU 0029','Graisse PFPE',0,0,'',0,'ACTIVE','',''),(616,'FMLU 0030','',0,0,'',0,'ACTIVE','',''),(617,'FMLU 0031','',0,0,'',0,'ACTIVE','',''),(618,'FMLU 0032','',0,0,'',0,'ACTIVE','',''),(619,'FMLU 0033','',0,0,'',0,'ACTIVE','',''),(620,'FMLU 0034','',0,0,'',0,'ACTIVE','',''),(621,'FMLU 0035','',0,0,'',0,'ACTIVE','',''),(622,'FMTT 50001','Recuit de stabilisation - Peek',0,0,'',0,'ACTIVE','',''),(623,'FMTT 50002','Recuit - Laiton',0,0,'',0,'ACTIVE','',''),(624,'FMTT 50003','Recuit - Cuivre',0,0,'',0,'ACTIVE','','');
/*!40000 ALTER TABLE `tbl_fxxx` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_hts`
--

DROP TABLE IF EXISTS `tbl_hts`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_hts` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `HTS` varchar(10) NOT NULL,
  `Description` text NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_hts`
--

LOCK TABLES `tbl_hts` WRITE;
/*!40000 ALTER TABLE `tbl_hts` DISABLE KEYS */;
INSERT INTO `tbl_hts` VALUES (1,'8544700090',''),(2,'8536700010',''),(3,'7326909890',''),(4,'8536699099',''),(5,'8547200090',''),(6,'8538909999',''),(7,'8535900089','Product with U>1000V');
/*!40000 ALTER TABLE `tbl_hts` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_product_code`
--

DROP TABLE IF EXISTS `tbl_product_code`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_product_code` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Code` varchar(45) NOT NULL,
  `Description` varchar(100) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=2 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_product_code`
--

LOCK TABLES `tbl_product_code` WRITE;
/*!40000 ALTER TABLE `tbl_product_code` DISABLE KEYS */;
INSERT INTO `tbl_product_code` VALUES (1,'Y23B','SCM');
/*!40000 ALTER TABLE `tbl_product_code` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_product_range_notused`
--

DROP TABLE IF EXISTS `tbl_product_range_notused`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_product_range_notused` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Product_Range` tinytext NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=35 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_product_range_notused`
--

LOCK TABLES `tbl_product_range_notused` WRITE;
/*!40000 ALTER TABLE `tbl_product_range_notused` DISABLE KEYS */;
INSERT INTO `tbl_product_range_notused` VALUES (1,'9316'),(2,'OFS'),(15,'EDBC'),(16,'6kV1600A'),(5,'EFS'),(6,'DLS'),(32,'WG'),(27,'376'),(9,'HydraElectric'),(10,'Showet'),(11,'MOD'),(12,'MSD'),(13,'MPD'),(17,'6kV400A'),(18,'18kV400A'),(19,'18kV900A'),(25,'DS3003'),(33,'ODBC');
/*!40000 ALTER TABLE `tbl_product_range_notused` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_project`
--

DROP TABLE IF EXISTS `tbl_project`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_project` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `OTP` varchar(6) NOT NULL,
  `Title` varchar(30) NOT NULL,
  `Project_Manager` varchar(30) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=160 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_project`
--

LOCK TABLES `tbl_project` WRITE;
/*!40000 ALTER TABLE `tbl_project` DISABLE KEYS */;
INSERT INTO `tbl_project` VALUES (1,'P08002','DO-JIP-3pin OFS',''),(3,'P08004','ORMEN LANGE 6kV 1600A',''),(4,'P08005','DS3003',''),(5,'P09001','JSM 18KV 6KV 400A',''),(6,'P09002','Shell Princess Vertical OFS',''),(7,'P09003','MORVIN 2',''),(9,'P09005','SULZER P6W400 Motor Penetrator',''),(11,'P09007','JSM - 15KPSI Penetrator',''),(14,'P09010','P6-3W250',''),(16,'P09012','Tordis redesign P6 -400A',''),(17,'P09013','P6-P18 Gap Closing EXXON',''),(18,'P09014','C-Seals Qualification',''),(19,'P10001','JSM AKER 6KV-400A',''),(20,'P10002','GOLIAT',''),(21,'P10003','DS3003 Upgraded 1000 matings',''),(22,'P10008','SBB',''),(23,'P10009','HV DC GAP Closing',''),(24,'P10012','JSM FRAMO 15KPSI PENETRATOR',''),(25,'P10013','Crimp test 20AWG cable wire',''),(26,'P10022','DS3003 Pipeline Repair System',''),(28,'P10029','SHELL LINNORM',''),(29,'P10031','ISLAY PROJECT',''),(30,'P10036','HAMMERFEST Strøm Project',''),(31,'P10040','OL 18kV BLIND CAPS',''),(32,'P11003','NJORD NORTH West Flank',''),(34,'P11006','PAPA TERRA PROJECT',''),(36,'P11010','6 PIN ofs',''),(38,'P11013','EMS project',''),(40,'P11015','ASGARD SERIAL PROJECT',''),(41,'P11016','EKOFISK PROJECT',''),(42,'P11017','SKULD PROJECT',''),(43,'P11018','Shell HPHT Connector',''),(44,'P11019','SHELL OL FEED U0',''),(45,'P11020','LYELL',''),(47,'P11022','CHEVRON P18SW900- 3000m',''),(48,'P11023','CAMERON New SCM',''),(49,'P12001','IN WELL G2',''),(50,'P12002','PIPE HEATING DEMONSTRATOR',''),(51,'P12003','DS-DO Qualification CAMERON',''),(52,'P12004','BRYNHILD',''),(53,'P12005','KINOSIS HEATED PIPE',''),(54,'P12006','NE PAS UTILISER',''),(55,'P12007','ICS 9316 DR',''),(56,'P12008','CAMERON STABPLATE',''),(57,'P12009','AMB Subsea Side Project',''),(58,'P12010','DUCO ALUMINIUM CABLE',''),(59,'P12011','EFS DRY MATE PLUG (SAV 12-041)',''),(60,'P12012','GSC Gullfaks',''),(61,'P12013','THALES 2,6kV Splash Zone',''),(62,'P12014','S2M 9316 Stycast',''),(64,'P13001','GOLDEN EAGLE',''),(65,'P13002','SHELL STONES',''),(66,'P13003','NE PAS UTILISER',''),(67,'P13004','DEH STATOIL',''),(68,'P13005','UTSIRA SAV13-044',''),(69,'P13006','JULIA',''),(70,'P13007','DIEGA - CARLA',''),(71,'P13008','9316 IECEx_STONES',''),(72,'P13009','9316 IECEx STONES (BMT)',''),(73,'P13010','FMC EGINA',''),(74,'P13011','PRISMER ALSTOM',''),(75,'P14001','JSM II EFS',''),(76,'P14002','Energies renouvelables',''),(77,'P14003','UTSIRA POWET',''),(78,'P14004','MOHO',''),(79,'P14005','PiP Bulkhead Penetrators',''),(80,'P14006','KING',''),(81,'P14007','CRYOSTAR - 9316 HPBT',''),(82,'P15001','DS4001 EWS',''),(83,'P15002','VXT EFS 1 Pin - FEED STUDY',''),(84,'P15003','EHTF - Subsea 7- Wet Mate',''),(85,'P15004','16_EX_CERTIFICATE_M&D',''),(86,'P15005','SS7 - EHTF - Dual Barrier',''),(87,'P15006','DS3003 G3 Range',''),(88,'P15007','DO3000 R - Renewable version',''),(89,'P15008','6 FO VOFS Baker Hughes',''),(90,'P15009','TVEX',''),(91,'P15010','SHOSHIN FEPS Tidal',''),(92,'P15012','MEMBRANE GEL (TECHNO)',''),(93,'P16001','HEFS HYBRID PENETRATOR',''),(94,'P16002','6KV 400A Direct Term',''),(95,'P16003','AGV',''),(96,'P16004','9316 ATEX Certification',''),(97,'P16005','EFS Humidity exposure',''),(98,'P16006','Annular ILC',''),(99,'P16007','Minesto Deep Green',''),(100,'P16008','Greater Enfield FPSO',''),(101,'P17001','GE Ifokus',''),(103,'P17003','6 PIN Feed Study',''),(104,'P17004','EHTF - EDBC New heating cable',''),(106,'P17006','9316 Sustaining',''),(107,'P17007','JANSZ','JADAUD S.'),(108,'P17008','Alta Gohta',''),(109,'P18001','TE MOG Support',''),(110,'P18002','ILC DCNS',''),(111,'P18003','Star-End Termination Subsea 7','LEDU M.'),(112,'P18004','FENJA ETH-PiP Optic','JEAN J.'),(113,'P18005','Optical Quick Connector PiP',''),(114,'P18006','Shell Stones - OSI','BELLON F.'),(115,'P18007','AErfugl','LEDU M.'),(116,'P18008','DUSUP - GE Ceramic Penetrator','JADAUD S.'),(117,'P18009','EHTF Splice','LEDU M.'),(118,'P18010','EHTF BP Manuel','LEDU M.'),(119,'P18011','48 FO Thales','BAUER M.'),(120,'P19001','FMC BP ThunderHorse','LEDU M.'),(121,'P19002','FMC BP Atlantis','LEDU M.'),(122,'P19003','KRAFLA','JADAUD S.'),(123,'P19004','MultiPin VOFS 10K-250F','LEDU J.'),(124,'P19005','ASGARD Phase 2','JADAUD S.'),(125,'P19006','ILC Optic -TFMC Brazil',''),(126,'P19007','XoM Payara OFS','LEDU M.'),(127,'P19008','MSD Vincent',''),(128,'P19009','TFMC Electrical Splice','MADELIN A.'),(129,'P19010','ILC SAFRAN','BAUER M.'),(130,'P20001','ABB Internal Jumper','JADAUD S.'),(131,'P20002','9316 HT Double Marking','BAUER M.'),(132,'P20003','AErfugl phase 2','LEDU M.'),(133,'P20004','SEPDU',''),(134,'P20005','THRT Payara',''),(135,'P20006','JANSZ Execution','JADAUD S.'),(136,'P20007','HCRAW Cable Terminaison','BAUER M.'),(137,'P21001','KRAFLA - AMB penetrator','JEAN J.'),(138,'P21002','KRAFLA - HV Penetrator','JEAN J.'),(142,'STAND','STANDARD',''),(143,'P21006','P4 3W100','MADELIN A.'),(144,'P21007','Aasgard 2 MAN AMB','JEAN.J'),(145,'P21005','JANSZ Execution - AMB','JEAN.J'),(146,'P21008','SAV Thales ILC','MADELIN A.'),(147,'P22001','Bay du Nord - MPD','MADELIN A.'),(148,'P22002','EHTF Splice ITP','MADELIN A.'),(149,'P22003','Bay du Nord - MOD','FOURNIER F.'),(150,'P22004','LOPRO','BOURRET F.'),(151,'P22005','Aasgard Module 5','JADAUD S.'),(152,'P22006','Pack Battery','MADELIN A.'),(154,'P22007','HV Star End','MADELIN A.'),(155,'P22008','66kV Renewable',''),(156,'P22009','VESA',''),(157,'P22010','HCRAW','MADELIN A.'),(158,'P22011','HDCAM','MADELIN A.'),(159,'P22012','DRIFTWOOD BHT','JEAN.J');
/*!40000 ALTER TABLE `tbl_project` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_rdo`
--

DROP TABLE IF EXISTS `tbl_rdo`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_rdo` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `RDO` varchar(10) NOT NULL,
  `Description` varchar(100) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_rdo`
--

LOCK TABLES `tbl_rdo` WRITE;
/*!40000 ALTER TABLE `tbl_rdo` DISABLE KEYS */;
INSERT INTO `tbl_rdo` VALUES (1,'0578','Offshore & NOCLASS'),(2,'1229','Offshore & Dual Use '),(3,'1226','Industrial & Dual use or Classified'),(4,'0576','Industrial and NO CLASS');
/*!40000 ALTER TABLE `tbl_rdo` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_sap_fxxx`
--

DROP TABLE IF EXISTS `tbl_sap_fxxx`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_sap_fxxx` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Code` varchar(45) NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=387 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_sap_fxxx`
--

LOCK TABLES `tbl_sap_fxxx` WRITE;
/*!40000 ALTER TABLE `tbl_sap_fxxx` DISABLE KEYS */;
INSERT INTO `tbl_sap_fxxx` VALUES (1,'YFMME-2009-0DIA012'),(2,'YFMME-2009-0DIA020'),(3,'YFMME-2009-0DIA025'),(4,'YFMME-2009-0DIA040'),(5,'YFMME-3016-0DIA020'),(6,'YFMME-3016-0DIA030'),(7,'YFMME-3016-0DIA040'),(8,'YFMME-3016-0DIA050'),(9,'YFMME-3016-0DIA060'),(10,'YFMME-3016-0DIA070'),(11,'YFMME-3016-0DIA080'),(12,'YFMME-3016-0DIA100'),(13,'YFMME-3017-0DIA220'),(14,'YFMME-3032-0DIA004'),(15,'YFMME-3032-0DIA005'),(16,'YFMME-3032-0DIA008'),(17,'YFMME-3032-0DIA010'),(18,'YFMME-3032-0DIA015'),(19,'YFMME-3032-0DIA020'),(20,'YFMME-3032-0DIA022'),(21,'YFMME-3032-0DIA025'),(22,'YFMME-3032-0DIA030'),(23,'YFMME-3032-0DIA035'),(24,'YFMME-3032-0DIA040'),(25,'YFMME-3032-0DIA045'),(26,'YFMME-3032-0DIA050'),(27,'YFMME-3032-0DIA055'),(28,'YFMME-3032-0DIA060'),(29,'YFMME-3032-0DIA065'),(30,'YFMME-3032-0DIA070'),(31,'YFMME-3032-0DIA075'),(32,'YFMME-3032-0DIA080'),(33,'YFMME-3032-0DIA090'),(34,'YFMME-3032-0DIA100'),(35,'YFMME-3032-0DIA110'),(36,'YFMME-3032-0DIA120'),(37,'YFMME-3032-0DIA130'),(38,'YFMME-3032-0DIA140'),(39,'YFMME-3032-0DIA150'),(40,'YFMME-3032-0DIA180'),(41,'YFMME-3032-0DIA200'),(42,'YFMME-3032-0DIA220'),(43,'YFMME-3032-0DIA240'),(44,'YFMME-3032-0DIA250'),(45,'YFMME-3032-0DIA280'),(46,'YFMME-3032-0DIA285'),(47,'YFMME-3032-0DIA300'),(48,'YFMME-3032-0DIA350'),(49,'YFMME-3032-0DIA370'),(50,'YFMME-3032-1DIA008'),(51,'YFMME-3032-D038EP2'),(52,'YFMME-3032-D054EP2'),(53,'YFMME-3032-D92-D91'),(54,'YFMME-3038-0DIA050'),(55,'YFMME-3038-D0007H9'),(56,'YFMME-3040-0DIA002'),(57,'YFMME-3040-0DIA010'),(58,'YFMME-3040-0DIA012'),(59,'YFMME-3040-0DIA018'),(60,'YFMME-3040-0DIA030'),(61,'YFMME-3040-0DIA035'),(62,'YFMME-3040-0DIA050'),(63,'YFMME-3040-0DIA060'),(64,'YFMME-3044-0DIA030'),(65,'YFMME-3044-0DIA040'),(66,'YFMME-3044-0DIA050'),(67,'YFMME-3044-0DIA060'),(68,'YFMME-3044-0DIA070'),(69,'YFMME-3044-0DIA100'),(70,'YFMME-3045-0DIA020'),(71,'YFMME-3045-0DIA025'),(72,'YFMME-3045-0DIA030'),(73,'YFMME-3045-0DIA035'),(74,'YFMME-3045-0DIA040'),(75,'YFMME-3045-0DIA045'),(76,'YFMME-3045-0DIA050'),(77,'YFMME-3045-0DIA055'),(78,'YFMME-3045-0DIA060'),(79,'YFMME-3045-0DIA070'),(80,'YFMME-3045-0DIA075'),(81,'YFMME-3045-0DIA080'),(82,'YFMME-3045-0DIA090'),(83,'YFMME-3045-0DIA100'),(84,'YFMME-3045-0DIA110'),(85,'YFMME-3045-0DIA120'),(86,'YFMME-3045-0DIA130'),(87,'YFMME-3045-0DIA140'),(88,'YFMME-3045-0DIA150'),(89,'YFMME-3045-0DIA160'),(90,'YFMME-3045-0DIA177'),(91,'YFMME-3045-0DIA180'),(92,'YFMME-3045-0DIA190'),(93,'YFMME-3045-0DIA200'),(94,'YFMME-3045-0DIA210'),(95,'YFMME-3045-0DIA228'),(96,'YFMME-3045-0DIA250'),(97,'YFMME-3045-0DIA279'),(98,'YFMME-3045-0DIA304'),(99,'YFMME-3045-0DIA306'),(100,'YFMME-3045-D279X70'),(101,'YFMME-3045-D279X80'),(102,'YFMME-3045-D355X30'),(103,'YFMME-3045-D355X40'),(104,'YFMME-3045-D505X1I'),(105,'YFMME-3045-D610X1I'),(106,'YFMME-3046-0D114-3'),(107,'YFMME-3046-0DIA005'),(108,'YFMME-3046-0DIA010'),(109,'YFMME-3046-0DIA020'),(110,'YFMME-3046-0DIA030'),(111,'YFMME-3046-0DIA035'),(112,'YFMME-3046-0DIA040'),(113,'YFMME-3046-0DIA045'),(114,'YFMME-3046-0DIA055'),(115,'YFMME-3046-0DIA060'),(116,'YFMME-3046-0DIA070'),(117,'YFMME-3046-0DIA100'),(118,'YFMME-3046-0DIA110'),(119,'YFMME-3046-0DIA180'),(120,'YFMME-3046-0DIA190'),(121,'YFMME-3046-0DIA200'),(122,'YFMME-3047-0D15-H9'),(123,'YFMME-3047-0DIA016'),(124,'YFMME-3047-0DIA020'),(125,'YFMME-3047-0DIA040'),(126,'YFMME-3047-0DIA060'),(127,'YFMME-3047-0DIA065'),(128,'YFMME-3047-0DIA070'),(129,'YFMME-3047-0DIA075'),(130,'YFMME-3047-0DIA080'),(131,'YFMME-3047-0DIA150'),(132,'YFMME-3047-0DIA180'),(133,'YFMME-3048-0DIA020'),(134,'YFMME-3048-0DIA025'),(135,'YFMME-3048-0DIA030'),(136,'YFMME-3048-0DIA035'),(137,'YFMME-3048-0DIA040'),(138,'YFMME-3048-0DIA050'),(139,'YFMME-3048-0DIA055'),(140,'YFMME-3048-0DIA060'),(141,'YFMME-3048-0DIA080'),(142,'YFMME-3048-0DIA085'),(143,'YFMME-3048-0DIA090'),(144,'YFMME-3048-0DIA100'),(145,'YFMME-3048-0DIA130'),(146,'YFMME-3048-0DIA140'),(147,'YFMME-3048-0DIA175'),(148,'YFMME-3048-0DIA180'),(149,'YFMME-3048-0DIA200'),(150,'YFMME-3048-D210X65'),(151,'YFMME-3050-0DIA090'),(152,'YFMME-3050-0DIA215'),(153,'YFMME-3050-0DIA280'),(154,'YFMME-3051-0DIA180'),(155,'YFMME-3053-0DIA290'),(156,'YFMME-3057-0DIA004'),(157,'YFMME-3057-0DIA006'),(158,'YFMME-3057-0DIA040'),(159,'YFMME-3057-0DIA070'),(160,'YFMME-3059-0D228-6'),(161,'YFMME-3059-0DIA020'),(162,'YFMME-3059-0DIA030'),(163,'YFMME-3059-0DIA040'),(164,'YFMME-3059-0DIA100'),(165,'YFMME-3059-0DIA150'),(166,'YFMME-3059-0DIA170'),(167,'YFMME-3059-0DIA240'),(168,'YFMME-3059-FIL-D09'),(169,'YFMME-3059-FIL-D12'),(170,'YFMME-3061-0D025-4'),(171,'YFMME-3061-0D306-8'),(172,'YFMME-3061-0D357-6'),(173,'YFMME-3061-0DIA041'),(174,'YFMME-3061-0DIA047'),(175,'YFMME-3061-0DIA050'),(176,'YFMME-3061-0DIA057'),(177,'YFMME-3061-0DIA063'),(178,'YFMME-3061-0DIA076'),(179,'YFMME-3061-0DIA085'),(180,'YFMME-3061-0DIA088'),(181,'YFMME-3061-0DIA090'),(182,'YFMME-3061-0DIA103'),(183,'YFMME-3061-0DIA110'),(184,'YFMME-3061-0DIA115'),(185,'YFMME-3061-0DIA139'),(186,'YFMME-3061-0DIA152'),(187,'YFMME-3061-0DIA177'),(188,'YFMME-3061-0DIA180'),(189,'YFMME-3061-0DIA190'),(190,'YFMME-3061-0DIA203'),(191,'YFMME-3061-0DIA210'),(192,'YFMME-3061-0DIA230'),(193,'YFMME-3061-0DIA254'),(194,'YFMME-3061-0DIA279'),(195,'YFMME-3061-0DIA300'),(196,'YFMME-3061-0DIA325'),(197,'YFMME-3061-0DIA370'),(198,'YFMME-3061-0DIA408'),(199,'YFMME-3061-D203X60'),(200,'YFMME-3061-D254X50'),(201,'YFMME-3061-D335X40'),(202,'YFMME-3064-0DIA030'),(203,'YFMME-3064-0DIA040'),(204,'YFMME-3064-0DIA045'),(205,'YFMME-3064-0DIA050'),(206,'YFMME-3064-0DIA055'),(207,'YFMME-3064-0DIA060'),(208,'YFMME-3064-0DIA090'),(209,'YFMME-3064-0DIA120'),(210,'YFMME-3064-0DIA130'),(211,'YFMME-3064-0DIA180'),(212,'YFMME-3064-0DIA240'),(213,'YFMME-3067-0DIA020'),(214,'YFMME-3067-0DIA040'),(215,'YFMME-3068-0DIA010'),(216,'YFMME-3068-0DIA020'),(217,'YFMME-3068-0DIA030'),(218,'YFMME-3068-0DIA035'),(219,'YFMME-3068-0DIA040'),(220,'YFMME-3068-0DIA045'),(221,'YFMME-3068-0DIA055'),(222,'YFMME-3068-0DIA110'),(223,'YFMME-3068-0DIA200'),(224,'YFMME-3070-0DIA012'),(225,'YFMME-3070-0DIA020'),(226,'YFMME-3070-0DIA040'),(227,'YFMME-3070-0DIA200'),(228,'YFMME-3077-1-0D130'),(229,'YFMME-3077-1-0D165'),(230,'YFMME-3077-1-0D170'),(231,'YFMME-3077-1DIA030'),(232,'YFMME-3077-1DIA040'),(233,'YFMME-3077-1DIA050'),(234,'YFMME-3077-1DIA060'),(235,'YFMME-3077-1DIA090'),(236,'YFMME-3078-0DIA150'),(237,'YFMME-3079-0DIA050'),(238,'YFMME-3079-0DIA060'),(239,'YFMME-3079-0DIA065'),(240,'YFMME-3082-0DIA060'),(241,'YFMME-3082-0DIA070'),(242,'YFMME-3083-0DIA025'),(243,'YFMME-3083-0DIA030'),(244,'YFMME-3083-0DIA035'),(245,'YFMME-3083-0DIA090'),(246,'YFMME-3084-0DIA020'),(247,'YFMME-3084-0DIA150'),(248,'YFMME-3084-0DIA170'),(249,'YFMME-3085-0DIA076'),(250,'YFMME-3085-0DIA203'),(251,'YFMME-3086-0DIA015'),(252,'YFMME-3086-0DIA120'),(253,'YFMME-3091-0D00060'),(254,'YFMME-3091-0D12-70'),(255,'YFMME-3091-0D44-45'),(256,'YFMME-3091-0DIA020'),(257,'YFMME-3097-0DIA020'),(258,'YFMME-4002-0DIA025'),(259,'YFMME-4002-0DIA035'),(260,'YFMME-4002-0DIA040'),(261,'YFMME-4002-0DIA050'),(262,'YFMME-4016-0DIA020'),(263,'YFMME-4016-0DIA025'),(264,'YFMME-4016-0DIA030'),(265,'YFMME-4016-0DIA035'),(266,'YFMME-4016-0DIA040'),(267,'YFMME-4016-0DIA045'),(268,'YFMME-4016-0DIA050'),(269,'YFMME-4016-0DIA056'),(270,'YFMME-4016-0DIA060'),(271,'YFMME-4016-0DIA065'),(272,'YFMME-4016-0DIA070'),(273,'YFMME-4016-0DIA080'),(274,'YFMME-4016-0DIA085'),(275,'YFMME-4016-0DIA090'),(276,'YFMME-4016-0DIA100'),(277,'YFMME-4016-0DIA110'),(278,'YFMME-4016-0DIA125'),(279,'YFMME-4016-0DIA140'),(280,'YFMME-4016-0DIA150'),(281,'YFMME-4016-0DIA180'),(282,'YFMME-4022-0DIA035'),(283,'YFMME3016-90X90X24'),(284,'YFMME3045-D333X140'),(285,'YFMME-3059-FIL-D12'),(286,'YFMME-3059-FIL-D09'),(287,'YFMEL-60190-000000'),(288,'YFMEL-60160-000000'),(289,'YFMEL-60150-000000'),(290,'YFMEL-60130-000000'),(291,'YFMEL-60120-000000'),(292,'FTMP-960-D150'),(293,'FTMP-974'),(294,'FTMP-978-D20'),(295,'FTMP-978-D25'),(296,'FTMP-978-D50'),(297,'FTMP-979-D30'),(298,'YFTMP-131-00000000'),(299,'YFTMP-441-0DIA0110'),(300,'YFTMP-768-00000000'),(301,'YFTMP-874-00000000'),(302,'YFTMP-931-000-D110'),(303,'YFTMP-932-000-D110'),(304,'YFTMP-958-000-D020'),(305,'YFTMP-958-000-D100'),(306,'YFTMP-958-000-D120'),(307,'YFTMP-958-000-D200'),(308,'YFTMP-958-0DIA0040'),(309,'YFTMP-958-0DIA0090'),(310,'YFTMP-958-A-DIA050'),(311,'YFTMP-958-A-DIA150'),(312,'YFTMP-958-A-DIA200'),(313,'YFTMP-958-A-DIA250'),(314,'YFTMP-958-C-DIA050'),(315,'YFTMP-958-D-DIA085'),(316,'YFTMP-958-D-DIA250'),(317,'YFTMP-958-D-DIA300'),(318,'YFTMP-958-D-DIA350'),(319,'YFTMP-958-D280X154'),(320,'YFTMP-958-D320X099'),(321,'YFTMP-958-D320X115'),(322,'YFTMP-958-G-DIA040'),(323,'YFTMP-959-0DIA0006'),(324,'YFTMP-959-0DIA0010'),(325,'YFTMP-959-0DIA0012'),(326,'YFTMP-959-0DIA0016'),(327,'YFTMP-959-0DIA0020'),(328,'YFTMP-959-0DIA0025'),(329,'YFTMP-959-0DIA0030'),(330,'YFTMP-959-0DIA0035'),(331,'YFTMP-959-0DIA0036'),(332,'YFTMP-959-0DIA0040'),(333,'YFTMP-959-0DIA0045'),(334,'YFTMP-959-0DIA0050'),(335,'YFTMP-959-0DIA0060'),(336,'YFTMP-959-0DIA0070'),(337,'YFTMP-959-0DIA0080'),(338,'YFTMP-959-0DIA0085'),(339,'YFTMP-959-0DIA0090'),(340,'YFTMP-959-0DIA0100'),(341,'YFTMP-959-0DIA0125'),(342,'YFTMP-959-0DIA0130'),(343,'YFTMP-959-0DIA0150'),(344,'YFTMP-959-0DIA0180'),(345,'YFTMP-959-0DIA06-R'),(346,'YFTMP-959-0DIA08-R'),(347,'YFTMP-959-0DIA10-R'),(348,'YFTMP-959-0DIA15-R'),(349,'YFTMP-959-0DIA20-R'),(350,'YFTMP-959-D200-E60'),(351,'YFTMP-960-0DIA0015'),(352,'YFTMP-960-0DIA0030'),(353,'YFTMP-960-0DIA0040'),(354,'YFTMP-960-0DIA0050'),(355,'YFTMP-960-0DIA0060'),(356,'YFTMP-960-0DIA0080'),(357,'YFTMP-960-0DIA0085'),(358,'YFTMP-960-0DIA0100'),(359,'YFTMP-960-0DIA0125'),(360,'YFTMP-960-0DIA20-R'),(361,'YFTMP-960-D200-E60'),(362,'YFTMP-971-0DIA0020'),(363,'YFTMP-971-0DIA0040'),(364,'YFTMP-972-00000000'),(365,'YFTMP-977-0DIA0175'),(366,'YFTMP-978-0DIA0036'),(367,'YFTMP-978-0DIA0060'),(368,'YFTMP-978-0DIA0080'),(369,'YFTMP-978-0DIA0090'),(370,'YFTMP-978-0DIA0100'),(371,'YFTMP-978-0DIA0140'),(372,'YFTMP-978-0DIA0150'),(373,'YFTMP-978-0DIA0165'),(374,'YFTMP-978-0DIA0180'),(375,'YFTMP-978-0DIA0200'),(376,'YFTMP-979-0DIA0040'),(377,'YFTMP-979-0DIA0060'),(378,'YFTMP-979-0DIA0080'),(379,'YFTMP-979-0DIA0090'),(380,'YFTMP-979-0DIA0100'),(381,'YFTMP-979-0DIA0110'),(382,'YFTMP-979-0DIA0150'),(383,'YFTMP-979-0DIA0165'),(384,'YFTMP-979-0DIA0180'),(385,'YFTMP-979-0DIA20-R'),(386,'YFTMP-980-0DIA06-R');
/*!40000 ALTER TABLE `tbl_sap_fxxx` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_unit`
--

DROP TABLE IF EXISTS `tbl_unit`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_unit` (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `Unit` tinytext NOT NULL,
  `Unit_Type` tinytext NOT NULL,
  PRIMARY KEY (`ID`)
) ENGINE=MyISAM AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_unit`
--

LOCK TABLES `tbl_unit` WRITE;
/*!40000 ALTER TABLE `tbl_unit` DISABLE KEYS */;
INSERT INTO `tbl_unit` VALUES (1,'PC','Piece'),(2,'kg','Weight'),(3,'dm','Length'),(4,'m','Length'),(5,'g','Weight'),(6,'mm²','Surface');
/*!40000 ALTER TABLE `tbl_unit` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `tbl_user`
--

DROP TABLE IF EXISTS `tbl_user`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `tbl_user` (
  `Key_User` int(11) NOT NULL AUTO_INCREMENT,
  `TE_ID` tinytext NOT NULL,
  `Fullname` tinytext NOT NULL,
  `Email` tinytext NOT NULL,
  `ID_PC` tinytext NOT NULL,
  `Department` tinytext NOT NULL,
  PRIMARY KEY (`Key_User`)
) ENGINE=MyISAM AUTO_INCREMENT=182 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `tbl_user`
--

LOCK TABLES `tbl_user` WRITE;
/*!40000 ALTER TABLE `tbl_user` DISABLE KEYS */;
INSERT INTO `tbl_user` VALUES (2,'RAVINENC','AVINENC R.','<EMAIL>','','IT'),(3,'MBAUER','BAUER M.','<EMAIL>','','Engineering'),(5,'FBELLANGER','BELLANGER F.','<EMAIL>','','Inside Sales'),(7,'NBELLET','BELLET N.','<EMAIL>','','Laboratory'),(8,'FBELLON','BELLON F.','<EMAIL>','','Quality'),(11,'ABIGOT','BIGOT A.','<EMAIL>','','Laboratory'),(12,'JBLANCHE','BLANCHE J.','<EMAIL>','','Laboratory'),(14,'GBOIVIN','BOIVIN G.','<EMAIL>','','Laboratory'),(15,'RBONIN','BONIN R.','<EMAIL>','','Quality'),(16,'MBONNIN','BONNIN M.','<EMAIL>','','Engineering'),(17,'SBOUCHENOIRE','BOUCHENOIRE S.','<EMAIL>','','Supply Chain'),(18,'FBOURRET','BOURRET F.','<EMAIL>','','Operation'),(19,'MBREGENT','BREGENT M.','<EMAIL>','','Engineering'),(20,'FBRIER','BRIER F.','<EMAIL>','','Quality'),(22,'NCARREAU','CARREAU N.','<EMAIL>','','HR'),(23,'OCASSEGRAIN','CASSEGRAIN O.','<EMAIL>','','Supply Chain'),(24,'FCERISIER','CERISIER F.','<EMAIL>','','Maintenance'),(175,'PPARAT','PARAT P.','<EMAIL>','','Industrialization'),(26,'PCHARRON','CHARRON P.','<EMAIL>','','Logistic'),(27,'ACHATAIN','CHATAIN A.','<EMAIL>','','Engineering'),(30,'LCHESNE','CHESNE L.','<EMAIL>','','Quality'),(31,'FCHEVRIER','CHEVRIER F.','<EMAIL>','','Method'),(32,'CCHOISNET','CHOISNET C.','<EMAIL>','','Quality'),(33,'FCHOULLIKH','CHOULLIKH F.','<EMAIL>','','Maintenance'),(34,'NCISSE','CISSE N.','<EMAIL>','','Assembly'),(35,'BCOATE','COATE B.','<EMAIL>','','Logistic'),(39,'ACOTOC','COTOC A.','<EMAIL>','','Laboratory'),(41,'ACRAPIS','CRAPIS A.','<EMAIL>','','IT'),(42,'CCRENAIS','CRENAIS C.','<EMAIL>','','Logistic'),(43,'JCRIBIER','CRIBIER J.','<EMAIL>','','Direction'),(45,'GDANGELO','D\'ANGELO G.','gd\'<EMAIL>','','IT'),(46,'SDANGEARD','DANGEARD S.','<EMAIL>','','Logistic'),(47,'ADARONDEAU','DARONDEAU A.','<EMAIL>','','Assembly'),(48,'GDAVID','DAVID G.','<EMAIL>','','Laboratory'),(49,'EDEAL','DEAL E.','<EMAIL>','','Laboratory'),(50,'SDENIS','DENIS S.','<EMAIL>','','Marketing'),(51,'VDERET','DERET V.','<EMAIL>','','Laboratory'),(53,'CDESHAYES','DESHAYES C.','<EMAIL>','','Quality'),(57,'WDONNE','DONNE W.','<EMAIL>','','Quality'),(58,'NDORANGE','DORANGE N.','<EMAIL>','','Engineering'),(59,'EDOUVINET','DOUVINET E.','<EMAIL>','','Finance'),(62,'SDURAND','DURAND S.','<EMAIL>','','Metrology'),(63,'CEON','EON C.','<EMAIL>','','Metrology'),(64,'NFARIAULT','FARIAULT N.','<EMAIL>','','Engineering'),(67,'FFOURNIER','FOURNIER F.','<EMAIL>','','Logistic'),(68,'NFOUSSIER DRANNE','FOUSSIER DRANNE N.','nfoussier <EMAIL>','','Metrology'),(69,'SFREMONT','FREMONT S.','<EMAIL>','','Metrology'),(74,'GGOT','GOT G.','<EMAIL>','','Engineering'),(75,'NGOUJAT','GOUJAT N.','<EMAIL>','','Purchasing'),(76,'JGOURDOU','GOURDOU J.','<EMAIL>','','Laboratory'),(77,'LGUICHARDON','GUICHARDON L.','<EMAIL>','','Molding'),(78,'YGUITTET','GUITTET Y.','<EMAIL>','','Engineering'),(79,'NHAMME','HAMME N.','<EMAIL>','','Metrology'),(80,'JHOCHART','HOCHART J.','<EMAIL>','','Maintenance'),(81,'SHOUDAYER','HOUDAYER S.','<EMAIL>','','Logistic'),(181,'JFGALIPAUD','GALIPAUD J.','<EMAIL>','','Engineering'),(86,'SJADAUD','JADAUD S.','<EMAIL>','','Project'),(89,'FJARRIER','JARRIER F.','<EMAIL>','','Field Service'),(90,'JJEAN','JEAN J.','<EMAIL>','','Project'),(91,'SJULIEN','JULIEN S.','<EMAIL>','','Engineering'),(92,'FJUST','JUST F.','<EMAIL>','','Logistic'),(93,'SKAROU','KAROU S.','<EMAIL>','','Quality'),(94,'FKLEINDIENST','KLEINDIENST F.','<EMAIL>','','Direction'),(95,'NLACROIX','LACROIX N.','<EMAIL>','','Engineering'),(96,'CLAGATHU','LAGATHU C.','<EMAIL>','','Engineering'),(97,'FLAMY','LAMY F.','<EMAIL>','','GID'),(98,'JLANGLOIS','LANGLOIS J.','<EMAIL>','','Industrialization'),(101,'CLEBOULEUX','LEBOULEUX C.','<EMAIL>','','Logistic'),(102,'FLECHARTIER','LECHARTIER F.','<EMAIL>','','Laboratory'),(103,'JLEGEAY','LEGEAY J.','<EMAIL>','','Product Management'),(104,'TLELONG','LELONG T.','<EMAIL>','','Engineering'),(105,'CLELOUP','LELOUP C.','<EMAIL>','','Quality'),(108,'MLOUIS','LOUIS M.','<EMAIL>','','Supply Chain'),(109,'AMADELIN','MADELIN A.','<EMAIL>','','Project'),(112,'JMARAIS','MARAIS J.','<EMAIL>','','Method'),(114,'GMARTIN','MARTIN G.','<EMAIL>','','Laboratory'),(115,'LMARTINEAU','MARTINEAU L.','<EMAIL>','','HR'),(116,'MMEDARD','MEDARD M.','<EMAIL>','','Supply Chain'),(118,'SMONFORTE SANCHEZ','MONFORTE SANCHEZ S.','smonforte <EMAIL>','','Supply Chain'),(119,'AMONTEIRO','MONTEIRO A.','<EMAIL>','','Metrology'),(180,'MROSSI','ROSSI M.','<EMAIL>','','Finance'),(122,'MNICOL','NICOL M.','<EMAIL>','','Maintenance'),(124,'PPARAT','PARAT P.','<EMAIL>','','Engineering'),(125,'RPARME','PARME R.','<EMAIL>','','Engineering'),(126,'SPASCAUD','PASCAUD S.','<EMAIL>','','Method'),(127,'EPAULMERY','PAULMERY E.','<EMAIL>','','Laboratory'),(128,'GPEIGNE','PEIGNE G.','<EMAIL>','','Engineering'),(129,'TPERES','PERES T.','<EMAIL>','','Method'),(130,'LPEREZ','PEREZ L.','<EMAIL>','','Quality'),(131,'GPERNET','PERNET G.','<EMAIL>','','Engineering'),(132,'VPICHARD','PICHARD V.','<EMAIL>','','Operation'),(174,'NROUILLARD','ROUILLARD N.','<EMAIL>','','Molding'),(134,'PPICHON','PICHON P.','<EMAIL>','','Quality'),(136,'CPOULAIN','POULAIN C.','<EMAIL>','','Engineering'),(137,'APREAU','PREAU A.','<EMAIL>','','Method'),(138,'LPRENANT','PRENANT L.','<EMAIL>','','Machining'),(179,'JMARAIS','MARAIS J.','<EMAIL>','','Engineering'),(178,'SGUILLARD','GUILLARD S.','<EMAIL>','','Engineering'),(177,'TLELONG','LELONG T.','<EMAIL>','','Purchasing'),(144,'EREVAUD','REVAUD E.','<EMAIL>','','Laboratory'),(173,'VPERCIVAL','PERCIVAL V.','<EMAIL>','','Project'),(146,'WRIGUET','RIGUET W.','<EMAIL>','','Purchasing'),(176,'GPEIGNE','PEIGNE G.','<EMAIL>','','Industrialization'),(149,'PROBICHON','ROBICHON P.','<EMAIL>','','Quality'),(150,'AROSELEUR','ROSELEUR A.','<EMAIL>','','Finance'),(151,'MROSSI','ROSSI M.','<EMAIL>','','GID'),(152,'NROUILLARD','ROUILLARD N.','<EMAIL>','','Industrialization'),(153,'PSAINTOT','SAINTOT P.','<EMAIL>','','Laboratory'),(154,'LSALE','SALE L.','<EMAIL>','','Laboratory'),(155,'MSAVIGNARD','SAVIGNARD M.','<EMAIL>','','Inside Sales'),(156,'SSHARP','SHARP S.','<EMAIL>','','HR'),(172,'STOURNIER','TOURNIER S.','<EMAIL>','','Marketing'),(160,'LTISON','TISON L.','<EMAIL>','','Method'),(161,'MVAUTEY','VAUTEY M.','<EMAIL>','','Maintenance'),(162,'CVILLETTE','VILLETTE C.','<EMAIL>','','Quality'),(171,'YGUITTET','GUITTET Y.','<EMAIL>','','GID'),(164,'MVRIGNAUD','VRIGNAUD M.','<EMAIL>','','Quality'),(165,'GWENTS','WENTS G.','<EMAIL>','','Molding'),(166,'SZIANE','ZIANE S.','<EMAIL>','','Laboratory'),(167,'SJULIEN','JULIEN S.','<EMAIL>','','GID'),(168,'MBAUER','BAUER M.','<EMAIL>','','Product Management'),(169,'MBONNIN','BONNIN M.','<EMAIL>','','Product Management'),(170,'SPASCAUD','PASCAUD S.','<EMAIL>','','Machining');
/*!40000 ALTER TABLE `tbl_user` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2022-12-12 16:48:57
