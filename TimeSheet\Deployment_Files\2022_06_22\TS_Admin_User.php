﻿<html>
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="TS_Admin_Styles.css">
	<link rel="stylesheet" type="text/css" href="Common_Button_styles.css">

<script>	
	
	// MISE A JOUR DES INFO LIE A L'UTILISATEUR CHOISI
	// -----------------------------------------------
	function SAP_CIR_Auto() {
		
	  const xhttp = new XMLHttpRequest();
	  xhttp.onload = function() {
		const raw_result=this.responseText.trim();
		split_result=raw_result.split("/");
	
		if (split_result[0]==true)
		{
			document.getElementById("CIR").checked = true;
		} else {
			document.getElementById("CIR").checked = false;
		}
		if (split_result[1]==true)
		{
			document.getElementById("SAP").checked = true;
		} else {
			document.getElementById("SAP").checked = false;
		}
		
		
	  }
	  const workcenter=document.getElementById("Workcenter").value;
	  xhttp.open("GET", "TS_Admin_User_Workcenter_Auto.php?Workcenter="+ workcenter);
	  xhttp.send();
	}
	
	
	
	// RECUPERATION DES VALEURS CHOISIES PAR L'UTILISATEUR POUR MODIFICATION/SUPPRESSION
	function frame_update(obj) 
	{
		// RECUPERATION DE LA PHASE (CODE) ET DU TITRE ASSOCIE CHOISI PAR L'UTILISATEUR
		//window.parent.document.getElementById("picked_phase").value=obj.cells[2].textContent.trim();
		//const picked_phase_val=document.querySelector('input[name="Picked_user"]:checked').value;
		document.getElementById("ID_db").value=obj.cells[1].textContent.trim();
		document.getElementById("Fullname").value=obj.cells[2].textContent.trim();
		document.getElementById("SCM_ID").value=obj.cells[3].textContent.trim();
		document.getElementById("Department").value=obj.cells[4].textContent.trim();
		const wkrctr=obj.cells[5].textContent.trim();
		document.getElementById("Workcenter").value=wkrctr.substring(0,wkrctr.indexOf(" -"));
		document.getElementById("email").value=obj.cells[6].textContent.trim();

		if (obj.cells[7].textContent.trim()=="X")
		{
			document.getElementById("CIR").checked=true;
		} else {
			document.getElementById("CIR").checked=false;
		}
		
		if (obj.cells[8].textContent.trim()=="X")
		{
			document.getElementById("SAP").checked=true;
		} else {
			document.getElementById("SAP").checked=false;
		}

		
		// SELECTION DU BOUTON RADIO ASSOCIE A LA LIGNE DU TABLEAU CHOISI
		const indx="Radio_Picked_User_" + obj.cells[1].textContent.trim();
		document.querySelector('input[name="Picked_User"]:checked');
		document.getElementById(indx).checked = true;
		
	}
	
	// copie des adresses emails dans le presse papier
	// function copy_to_clipboard()
	// {
		// var table_user_count = document.getElementById("t04").rows.length;
		// var table_user = document.getElementById("t04");
		// var i=1;
		// var email_list="";

		// do
		// {
			// if(email_list=="")
			// {
				// email_list= table_user.rows[i].cells[6].textContent.trim() + ";";
			// } else {
				// email_list= email_list + table_user.rows[i].cells[6].textContent.trim() + ";";
			// }
			
			// i=i+1;
		// } while (i<=(table_user_count-1))
		// navigator.clipboard.writeText(email_list);
		// alert("Liste des emails disponibles dans votre presse-papier !");
	// }
	
</script>
</head>

<!----------------------------->
<!-- SUPPRESSION UTILISATEUR -->
<!----------------------------->
<?php 

$msg_conf="";

if (isset ($_POST['Delete_user']) && isset($_POST['ID_db']) && ($_POST['ID_db'])>0)
{
	//Préparation des inputs de l'utilisateur
	$id=$_POST['ID_db'];
	$fullname=$_POST['Fullname'];

	//Connexion à la Base
	include('../TimeSheet_Connexion_DB.php');
	
	//Préparation de la requete SQL
	$sql = 'DELETE FROM tbl_user WHERE ID = "'.$id.'";';
	
	// Query execution
	$result = $mysqli_ts->query($sql);
	mysqli_close($mysqli_ts);
	
	// Message confirmation
	$msg_conf='</br> Utilisateur '.$fullname.' supprimé.</br>';
	
}

?>

<!-------------------------->
<!-- CREATION UTILISATEUR -->
<!-------------------------->
 <?php
if (isset($_POST['Create_user']) && (isset ($_POST['SCM_ID'])) && (($_POST['SCM_ID'])!="") && (isset($_POST['Fullname']))  && (($_POST['Fullname'])!="")  && (isset($_POST['email']))  && (($_POST['email'])!="")  && (($_POST['ID_db'])==0))
{
	
	//Connexion à BD
	include('../TimeSheet_Connexion_DB.php');

	//On récupère les valeurs entrées par l'utilisateur :
	$SCM_ID=$_POST['SCM_ID'];
	$Fullname=$_POST['Fullname'];
	$Department=$_POST['Department'];
	$Workcenter=$_POST['Workcenter'];
	$email=$_POST['email'];
	if (isset($_POST['CIR']))
	{
		$cir=1;
	} else{
		$cir=0;
	}
	if (isset($_POST['SAP']))
	{
		$sap=1;
	} else{
		$sap=0;
	}

	
	//On prépare la commande sql d'insertion
	//Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
	$sql_1 = 'INSERT INTO tbl_user VALUES ("0","'.$Fullname.'","'.$SCM_ID.'","'.$Department.'","'.$Workcenter.'","'.$email.'","'.$cir.'","'.$sap.'");'; 	
	
	$resultat = $mysqli_ts->query($sql_1); 

	// on ferme la connexion
	mysqli_close($mysqli_ts);
	
	// Message confirmation
	$msg_conf='</br> Nouvel utilisateur '.$Fullname.' créé!</br>';
		
} 
?>


<!------------------------>
<!-- UPDATE UTILISATEUR -->
<!------------------------>
 <?php
	
if (isset($_POST['ID_db']) && isset($_POST['Update_user']) && isset ($_POST['SCM_ID']) && ($_POST['SCM_ID'])!="" && isset($_POST['Fullname']) && ($_POST['Fullname'])!="" && (isset($_POST['email']))  && ($_POST['email'])!="")
	
{
	if ($_POST['ID_db']<>"")
	{
	
	//Connexion à BD
	include('../TimeSheet_Connexion_DB.php');

	//On récupère les valeurs entrées par l'utilisateur :
	// Get user input values and prepare them for SQL query
	$ID=$_POST['ID_db'];
	$SCM_ID=$_POST['SCM_ID'];
	$Fullname=$_POST['Fullname'];
	$Department=$_POST['Department'];
	if (isset($_POST['Workcenter']))
	{
		$Workcenter=$_POST['Workcenter'];
	} else{
		$Workcenter="";
	}
	
	$email=$_POST['email'];
	if (isset($_POST['CIR']))
	{
		$cir=1;
	} else{
		$cir=0;
	}
	if (isset($_POST['SAP']))
	{
		$sap=1;
	} else{
		$sap=0;
	}
	
	//On prépare la commande sql d'insertion
	//Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
	$sql_1 = 'UPDATE tbl_user
			  SET 
				Fullname="'.$Fullname.'",
				SCM_ID = "'.$SCM_ID.'",
				Department = "'.$Department.'",
				Workcenter = "'.$Workcenter.'",
				email = "'.$email.'",
				CIR = "'.$cir.'",
				SAP = "'.$sap.'"
			  WHERE ID like "'.$ID.'";';

	$resultat = $mysqli_ts->query($sql_1); 

	// on ferme la connexion
	mysqli_close($mysqli_ts);
	
	// Message confirmation
	$msg_conf='</br> Utilisateur '.$Fullname.' mis à jour !</br>';

	}
} 
?>



<body>

<form name="name_form" method="post" action="" enctype="multipart/form-data">

<table border=0>

<tr>
	<td colspan=3 style="font-family:Arial">
		<div id="Body">
			<b>Gestion Utilisateurs </b>
		</div>
	</td>

</tr>

<tr>
	<td colspan=3>
		<div id="FilterTitle">
			Remplissez les champs pour créer un nouvel utilisateur, ou mettez à jour les informations de l'utilisateur que vous avez sélectionnées:
		</div>
	</td>
</tr>
<tr>
	<td>
		<div id="FilterTitle_User">
			Identifiant SCM<font color=red> *</font>:
		</div>
	</td>
	<td colspan=2>
		<div id="InpBox_User">
			<input type="text" style="font-size:12px;height:20px" size=16 name="SCM_ID" id="SCM_ID" placeholder="XXXXX" title="SCM ID - Mandatory">
		</div>
	</td>
</tr>
<tr>
	<td>
		<div id="FilterTitle_User">
			Nom<font color=red> *</font>:
		</div>
	</td>
	<td colspan=2>
		<div id="InpBox_User">
			<input type="text" style="font-size:12px;height:20px" size=16 name="Fullname" id="Fullname" placeholder="DUPONT M." title="Nom de famille en majuscule, espace, premiere lettre du prenom en majuscule, point - Mandatory">
		</div>
	</td>
</tr>
<tr>
	<td>
		<div id="FilterTitle_User">
			Addresse Email<font color=red> *</font>:
		</div>
	</td>
	<td colspan=2>
		<div id="InpBox_User">
			<input type="email" style="font-size:12px;height:20px" size=26 name="email" id="email" title="full email address - Mandatory" placeholder="<EMAIL>">
		</div>
	</td>
</tr>
<tr>
	<td>
		<div id="FilterTitle_User">
			Departement:
		</div>
	</td>
	<td colspan=2>
		<div id="InpBox_User">
		<select type="submit" style="font-size:12px;width:130px" name="Department"  id="Department" title="Département de rattachement de l'utilisateur"> 
		<option value=""></option>
		<!--LISTE DEROULANTE DYNAMIQUE-->
		<!------------------------------>
		<?php
			include('../TimeSheet_Connexion_DB.php');
			$requete = "SELECT DISTINCT Department FROM tbl_department ORDER BY Department ASC;";
			$resultat = $mysqli_ts->query($requete);
			while ($row = $resultat->fetch_assoc())
			{
				echo'<option value ="'.$row['Department'].'">'.$row['Department'].'</option><br/>'; 
			}
			mysqli_close($mysqli_ts);
		?>
		<!------------------------------>
		</select>
		</div>
	</td>
</tr>

	<tr hidden>
		<td>
			<div id="InpBox_User">
				<input type="text" style="font-size:12px" size=26 name="ID_db" id="ID_db" title="ID in the database of the given user" >
			</div>
		</td>
	</tr>
	
<tr>
	<td>
		<div id="FilterTitle_User">
			Poste de travail:
		</div>
	</td>
	<td colspan=2>
		<div id="InpBox_User">
			<select type="submit" onchange="SAP_CIR_Auto()" style="font-size:12px" name="Workcenter" id="Workcenter" title="Workcenter de l'utilisateur"> 
			<option value=""></option>
			<!--LISTE DEROULANTE DYNAMIQUE-->
			<!------------------------------>
			<?php
				include('../TimeSheet_Connexion_DB.php');
				$requete = "SELECT DISTINCT Workcenter, Description FROM tbl_workcenter ORDER BY workcenter ASC;";
				$resultat = $mysqli_ts->query($requete);
				while ($row = $resultat->fetch_assoc())
				{
					echo'<option value ="'.$row['Workcenter'].'">'.$row['Workcenter'].' - '.$row['Description'].'</option><br/>'; 
				}
				mysqli_close($mysqli_ts);
			?>
			<!------------------------------>
			</select>
		</div>
	</td>
</tr>
<tr>

<tr>
	<td>
		<div id="FilterTitle_User">
			Intégration pour le crédit d'impôt recherche/innovation ?:
		</div>
	</td>
	<td style="text-align:center">
		<input type="checkbox" id="CIR" name="CIR">
	</td>
	<td rowspan=2 style="border-left:1px solid black">
		<div id="InpBox_User">
			A ne modifier que si necessaire
		</div>
	</td>
</tr>
<tr>
	<td>
		<div id="FilterTitle_User">
			Intégration dans SAP ?:
		</div>
	</td>
	<td style="text-align:center">
		<input type="checkbox" id="SAP" name="SAP">
	</td>
	</tr>
	<tr>
	<td>
	</td>
	<td>
	</td>
	<td>
		<div id="FilterTitle_User" style="font-style:italic">
			<?php if (isset ($_POST['Delete_user']) || isset($_POST['Create_user']) || isset($_POST['Update_user'])) { echo $msg_conf; }?>
		</div>
	</td>
	<td>
		<input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue2" name="Create_user" value="Créer" title="Créer le nouvel utilsateur avec les informations renseignées."/>
		<input type="submit" style="text-align:center;vertical-align:middle;width:80px;height:20px" class="btn blue" name="Update_user" value="Mise à jour" title="Mets à jour les informations renseignées dans les champs"/>
	</td>
	<!--<td>
		<input type="submit" class="btn green" name="Email_Copy" value="Copie emails" style="text-align:center;vertical-align:middle;width:80px;height:20px" title="Copie tous les emails dans le presse papier" onclick="copy_to_clipboard()"/></div>
	</td>-->
	<td style="text-align:right; width:200px;">
		<input type="submit" class="btn red" name="Delete_user" value="Supprimer" style="text-align:center;vertical-align:middle;width:80px;height:20px" title="Supprime l'utilisateur sélectionné dans le tableau ci-dessous"/></div>
	</td>
</tr>

</table>

</form>
<!--------------------------------->
<!-- AFFICHAGE TABLE UTILISATEUR -->
<!--------------------------------->
<table id="t04" border="1" style="margin-left:20px; margin-top: 20px; max-width:95%;vertical-align:center">

	<tr>
		<th style="width:3%">
			O
		</th>
		<th  style="width:3%">
			ID
		</th>
		<th style="width:16%">
			Nom
		</th>
		<th style="width:15%">
			SCM ID
		</th>
		<th style="width:14%">
			Departement
		</th>
		<th style="width:25%">
			Centre de coût
		</th>
		<th>
			Email
		</th>
		<th style="width:3%" Title="Heures de l'utilisateur intégrée dans le CIR/CII si coché">
			CI
		</th>
		<th style="width:3%" Title="Heures de l'utilisateur intégrée dans SAP si coché">
			SAP
		</th>
	</tr>

	<?php

		include('../TimeSheet_Connexion_DB.php');
		$requete_auto = 'SELECT *, tbl_user.ID as ID_USER, tbl_user.CIR as CIR_USER, tbl_user.SAP as SAP_USER
						 FROM tbl_user
						 LEFT JOIN tbl_workcenter ON tbl_user.Workcenter = tbl_workcenter.Workcenter
						 ORDER BY Fullname ASC;
						';

		$resultat = $mysqli_ts->query($requete_auto);
		$rowcount=mysqli_ts_num_rows($resultat);

		while ($row = $resultat->fetch_assoc())
		{
			
			echo '
			<tr onclick="frame_update(this)">
				<td>
					<input type="radio" style="vertical-align:middle" id="Radio_Picked_User_'.$row['ID_USER'].'" name="Picked_user" value="'.$row['ID_USER'].'">
				</td>
				<td>
					'.$row['ID_USER'].'
				</td>
				<td>
					'.$row['Fullname'].'
				</td>
				<td>
					'.$row['SCM_ID'].'
				</td>
				<td>
					'.$row['Department'].'
				</td>
				<td>
					'.$row['Workcenter'].' - '.$row['Description'].'
				</td>
				<td>
					'.$row['Email'].'
				</td>
				<td>';
					if ($row['CIR_USER']==1)
					{
						echo 'X';
					} else {
						echo '';
					}
				echo '
				</td>
				<td>';
				if ($row['SAP_USER']==1)
				{
					echo 'X';
				} else {
					echo '';
				}
				echo '
				</td>
			</tr>';		
		}

		$mysqli_ts->close();

?>
	

	
</table>
			
</body>
</html>
