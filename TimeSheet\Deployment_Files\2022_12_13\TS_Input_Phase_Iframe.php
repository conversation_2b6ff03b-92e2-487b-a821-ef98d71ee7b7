<!DOCTYPE html>
<html>
<head>
<meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="TS_Input_Styles.css">

<script>

	// NON UTILISEE - POUR EXEMPLE SEULEMENT
	function phase_selection() {

		const picked_phase_val=document.querySelector('input[name="Picked_Phase"]:checked').value;
		const cel_row_data = 3;
		const picked_phase_title=obj.cells[cel_row_data].textContent;
		window.parent.document.getElementById("picked_phase").value=picked_phase_val;		

		}
	//---------------------------------------
	
	
	
	// RECUPERATION DES VALEURS CHOISIES PAR L'UTILISATEUR
	function frame_update(obj) 
	{
		
		// RECUPERATION DE LA PHASE (CODE) ET DU TITRE ASSOCIE CHOISI PAR L'UTILISATEUR
		window.parent.document.getElementById("picked_phase").value=obj.cells[2].textContent.trim();
		window.parent.document.getElementById("picked_phase_title").value=obj.cells[3].textContent.trim();

		// MISE A JOUR DE L'ACTIVITE LORS DU CHANGEMENT DE LA PHASE CHOISIE
		if (window.parent.document.getElementById("workcenter_select").value!="none")
		{
			window.parent.activity_autofill_function();
		}
		
		// SELECTION DU BOUTON RADIO ASSOCIE A LA LIGNE DU TABLEAU CHOISI
		const indx="Radio_Picked_Phase_" + obj.cells[0].textContent.trim();
		document.querySelector('input[name="Picked_Phase"]:checked');
		document.getElementById(indx).checked = true;
		
	}
	

	
</script>
</head>

<body>

<table title="Phase(s) ouverte(s) aux imputations pour le projet selectionnée" id="t04" border="1" style="width:100.5%" style="border:1px solid black;font-size:11px">

	<tr>
		<th hidden>
			ID
		</th>
		<th style="width:5%">
			O
		</th>
		<th style="width:20%">
			Code WBS
		</th>
		<th style="width:55%">
			Title
		</th>
		<th style="width:20%">
			Status
		</th>
	</tr>

	<?php

	if (isset($_GET['OTP']) && $_GET['OTP']!="")
	{
		$otp=$_GET['OTP'];

		include('../TimeSheet_Connexion_DB.php');
				$requete_auto = 'SELECT * 
						 FROM tbl_project 
						 WHERE 
							length(Project_Code)=13 
						AND left(Project_Code,7)like "'.$otp.'"  
						AND Level like "03"
						AND Status not like "%AALK%" AND Status not like "%IMBL%"
						AND Status not like "%LKD%" AND Status not like "%BLOQ%"
						AND Status not like "%TECO%" AND Status not like "%TCLO%"
						AND (Status like "%REL%" OR Status like "%REL %" OR Status like "%LANC%" OR Status like "%LANC %")
						';
						//AND Status not like "%PREL%" AND Status not like "%LNCP%"

		$resultat = $mysqli_ts->query($requete_auto);
		$rowcount=mysqli_ts_num_rows($resultat);
		if ($rowcount>=1)
		{
		$i=1;
		while ($row = $resultat->fetch_assoc())
		{
			
			if (strlen($i)==1)
			{
				$j='0'.$i;
			} else {
					$j=$i;
					}
			
			echo '
			<tr onclick="frame_update(this)">
			<td hidden>
				'.$j.'
			</td>
			<td>
				<input type="radio" id="Radio_Picked_Phase_'.$j.'" name="Picked_Phase" value="'.$row['Project_Code'].'">
			</td>
			<td>
				'.$row['Project_Code'].'
			</td>
			<td>
				'.$row['Description'].'
			</td>
			<td>
				'.$row['Status'].'
			</td>
			</tr>';	
		$i=$i+1;		
		}

		$mysqli_ts->close();
		
		}
	}
?>
	
</table>

</body>
</html>
