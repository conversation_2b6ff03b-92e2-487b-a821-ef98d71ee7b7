<html>
<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta charset="utf-8" />

    <link rel="stylesheet" type="text/css" href="TS_Admin_Styles.css">
	<link rel="stylesheet" type="text/css" href="Common_Button_styles.css">

<script>		
	
	
	// RECUPERATION DES VALEURS CHOISIES PAR L'UTILISATEUR POUR MODIFICATION/SUPPRESSION
	function frame_update(obj) 
	{
		// RECUPERATION DE LA PHASE (CODE) ET DU TITRE ASSOCIE CHOISI PAR L'UTILISATEUR
		//window.parent.document.getElementById("picked_phase").value=obj.cells[2].textContent.trim();
		//const picked_phase_val=document.querySelector('input[name="Picked_user"]:checked').value;
		document.getElementById("ID_Comment").value=obj.cells[0].textContent.trim();
		document.getElementById("ID_Project").value=obj.cells[1].textContent.trim();
		document.getElementById("WBS_Project").value=obj.cells[3].textContent.trim();
		document.getElementById("Project_Title").value=obj.cells[4].textContent.trim();
		document.getElementById("WBS_Phase").value=obj.cells[5].textContent.trim();
		document.getElementById("Phase_Title").value=obj.cells[6].textContent.trim();
		document.getElementById("PM_Comment").value=obj.cells[7].textContent.trim();
		
		// SELECTION DU BOUTON RADIO ASSOCIE A LA LIGNE DU TABLEAU CHOISI
		const indx="Radio_Picked_" + obj.cells[1].textContent.trim();
		document.querySelector('input[name="Picked_ID"]:checked');
		document.getElementById(indx).checked = true;
	}

	
</script>
</head>


<!------------------------------------->
<!-- AJOUTE COMMENTAIRE POUR 1 PHASE -->
<!------------------------------------->
 <?php

$msg_conf="";

if (isset($_POST['Add_Comment']) && $_POST['PM_Comment']!="" && $_POST['WBS_Phase']!="" && $_POST['ID_Comment']=="")
{
	//Connexion à BD
	include('../TimeSheet_Connexion_DB.php');

	//On récupère les valeurs entrées par l'utilisateur :
	// Get user input values and prepare them for SQL query
	$comment=$_POST['PM_Comment'];
	$wbs=$_POST['WBS_Phase'];
	
	//On prépare la commande sql d'insertion
	//Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
	$sql_1 = 'INSERT INTO tbl_phase_comments
			  VALUES
				("0","'.$wbs.'","'.$comment.'")';

	$resultat = $mysqli_ts->query($sql_1); 

	// on ferme la connexion
	mysqli_close($mysqli_ts);
	
	// Message confirmation
	$msg_conf='Commentaire ajouté !';

} 
?>

<!------------------------------------->
<!-- UPDATE COMMENTAIRE POUR 1 PHASE -->
<!------------------------------------->
 <?php

if (isset($_POST['Update_Comment']) && $_POST['ID_Comment']!="")
	
{

	//Connexion à BD
	include('../TimeSheet_Connexion_DB.php');

	//On récupère les valeurs entrées par l'utilisateur :
	// Get user input values and prepare them for SQL query
	$id=$_POST['ID_Comment'];
	$comment=$_POST['PM_Comment'];
	
	//On prépare la commande sql d'insertion
	//Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
	$sql_1 = 'UPDATE tbl_phase_comments
			  SET 
				Comment="'.$comment.'"
			  WHERE ID like "'.$id.'";';
	
	$resultat = $mysqli_ts->query($sql_1); 

	// on ferme la connexion
	mysqli_close($mysqli_ts);
	
	// Message confirmation
	$msg_conf='Commentaire mis à jour !';

} 
?>

<!------------------------------------------>
<!-- SUPPRESSION COMMENTAIRE POUR 1 PHASE -->
<!------------------------------------------>
 <?php

if (isset($_POST['Delete_Comment']) && ($_POST['ID_Comment'])!="")
	
{

	//Connexion à BD
	include('../TimeSheet_Connexion_DB.php');

	//On récupère les valeurs entrées par l'utilisateur :
	// Get user input values and prepare them for SQL query
	$id=$_POST['ID_Comment'];
	$comment=$_POST['PM_Comment'];
	
	//On prépare la commande sql d'insertion
	//Dans my.ini de mysql, supprimer le parametre NO_ZERO_DATE du mode strict
	$sql_1 = 'DELETE FROM tbl_phase_comments
			  WHERE ID like "'.$id.'";';
	
	$resultat = $mysqli_ts->query($sql_1); 

	// on ferme la connexion
	mysqli_close($mysqli_ts);
	
	// Message confirmation
	$msg_conf='Commentaire effacé !';

} 
?>




<body>

<form name="name_form" method="post" action="" enctype="multipart/form-data">

<table border=0	 style="width:95%">





<!--<tr>
	<td colspan=7>
		<hr>
	</td>
</tr>-->








<tr>
	<td colspan=4>
		<div id="FilterTitle" style="font-weight:bold">
			Table des phases ouvertes par projet
		</div>
	</td>

</tr>
<tr hidden>
	<td>
		<div id="InpBox_User">
			<input type="text" style="font-size:12px" size=5 name="ID_Comment" id="ID_Comment" title="ID comment" readonly >
			<input type="text" style="font-size:12px" size=5 name="ID_Project" id="ID_Project" title="ID tbl_project" readonly>
		</div>
	</td>
	
</tr>
<?php 
	if (isset($_GET['root']))
		{
		if ($_GET['root']=="admin")
			{
				echo '
					<tr>
						<td>
							<div id="FilterTitle_User">
								WBS et Titre du Projet :
							</div>
						</td>
						<td colspan=1 >
							<div id="InpBox_User">
								<input type="text" style="font-size:12px;height:20px" size=8 name="WBS_Project" id="WBS_Project" placeholder="P-XXXXX" title="Code WBS du projet auquel appartient la phase choisie" readonly>
								<input type="text" style="font-size:12px;height:20px" size=41 name="Project_Title" id="Project_Title" placeholder="" title="Titre du projet auquel appartient la phase choisie" readonly>
							</div>
						</td>
						<td style="text-align:left">
							<input type="submit" onclick="" style="text-align:center;vertical-align:middle;width:180px;height:20px" class="btn blue2" name="Add_Comment" value="Ajout Commentaire" title="Créer le nouveau commentaire avec les informations renseignées."/>
						
						</td>
						<td>
						</td>
					</tr>

					<tr>
						<td>
							<div id="FilterTitle_User">
								WBS et Titre de la Phase :
							</div>
						</td>
						<td >
							<div id="InpBox_User">
								<input type="text" style="font-size:12px;height:20px" size=12 name="WBS_Phase" id="WBS_Phase" title="Code WBS de la phase choisie" placeholder="P-XXXXX-XX-XX" readonly>
								<input type="text" style="font-size:12px;height:20px" size=37 name="Phase_Title" id="Phase_Title" placeholder="" title="Titre de la phase choisie" readonly>
							</div>
						</td>
						<td style="text-align:left">

							<input type="submit" style="text-align:center;vertical-align:middle;width:180px;height:20px" class="btn blue" name="Update_Comment" value="Mise à jour Commentaire" title="Mets à jour les informations renseignées dans les champs"/>

						</td>
						<td>
						</td>
					</tr>


					<tr>
						<td>
							<div id="FilterTitle_User">
								Commentaire du chef de projet :
							</div>
						</td>
						<td colspan=1>
							<div id="InpBox_User">
								<input type="text" style="font-size:12px;height:20px" size=55 name="PM_Comment" id="PM_Comment" title="Commentaire associé à la phase choisie" placeholder="">
							</div>
						</td>
						<td style="text-align:left">
							<input type="submit" class="btn red" name="Delete_Comment" value="Suppression Commentaire" style="text-align:center;vertical-align:middle;width:180px;height:20px" title="Supprime le commentaire sélectionné dans le tableau ci-dessous"/>

						</td>
						<td>
						</td>
					</tr>
					<tr>
						<td colspan=2 >
							<div id="InpBox_User" style="text-align:right">
								Les commentaires et les mises à jour associées sont instantanément visible par l\'ensemble des utilisateurs apres qu\'ils aient "rafraichis" leur page de navigateur web. 
							</div>
						</td>
						<td>
							<div id="FilterTitle_User" style="font-style:italic">
								'.$msg_conf.'
							</div>
						</td>
					</tr>
				';
			}
		}
	?>

</form>


<!-------------------------------------------->
<!-- AFFICHAGE TABLE IMPUTATION HORS PROJET -->
<!-------------------------------------------->
<tr>

<td colspan=4 >
<table id="t04" border="1" style="margin-left:20px; margin-top: 10px; max-width:95%; vertical-align:middle">

	<tr>
		<th hidden style="width:3%;  vertical-align:middle">
			ID
		</th>
		<th hidden style="width:3%;  vertical-align:middle">
			ID2
		</th>
		<th style="width:3%;  vertical-align:middle">
			O
		</th>
		<th  style="width:100px">
			WBS Projet
		</th>
		<th style="width:250px">
			Titre Projet
		</th>
		<th style="width:120px">
			WBS Phase
		</th>
		<th style="width:350px">
			Titre Phase
		</th>
		<th>
			Commentaires
		</th>
	</tr>

	<?php

		include('../TimeSheet_Connexion_DB.php');
		$requete_auto = 'SELECT 
							`COMMENT_TABLE`.`ID` AS "ID_COMMENT",
							`t1`.`ID` as "ID_PROJECT",
							left(`t1`.`Project_Code`,7) as  `WBS_PROJECT`,
							`t2`.`Description` as "PROJECT_TITLE",
							`t1`.`Project_Code` as `WBS_PHASE` , 
							`t1`.`Description` as "PHASE_TITLE", 
							`t1`.`Status` as "STATUS",
							COMMENT_TABLE.COMMENTS as "COMMENTS"
						FROM 
							`tbl_project` as `t1` 
						LEFT OUTER JOIN `tbl_project` as `t2` ON left( `t2`.`Project_Code`,7) = left( `t1`.`Project_Code`,7) 
						LEFT JOIN (Select tbl_phase_comments.ID as ID, tbl_phase_comments.Comment as COMMENTS, tbl_phase_comments.WBS as WBS from tbl_phase_comments ) COMMENT_TABLE
												ON COMMENT_TABLE.WBS = `t1`.`Project_Code`
						WHERE 
							length(`t1`.`Project_Code`)=13
						AND `t1`.`Level` like "03" 
						AND `t1`.`Status` not like "%AALK%" 
						AND `t1`.`Status` not like "%IMBL%" 
						AND `t1`.`Status` not like "%LKD%" 
						AND `t1`.`Status` not like "%BLOQ%" 
						AND `t1`.`Status` not like "%TECO%" 
						AND `t1`.`Status` not like "%TCLO%" 
						AND (
							 `t1`.`Status` like "%REL%" 
						  OR `t1`.`Status` like "%REL %"
						  OR `t1`.`Status` like "%LANC%" 
						  OR `t1`.`Status` like "%LANC %"
						  ) 
						AND 
							`t2`.`Level`="00"
						ORDER BY 
							`WBS_PROJECT` DESC,
							`WBS_PHASE` DESC ;
						';

		$resultat = $mysqli_ts->query($requete_auto);
		$rowcount=mysqli_num_rows($resultat);
		$previous_otp="";
		while ($row = $resultat->fetch_assoc())
		{
			
			echo '
				<tr onclick="frame_update(this)">
					<td hidden>
						'.$row['ID_COMMENT'].'
					</td>
					<td hidden>
						'.$row['ID_PROJECT'].'
					</td>
					<td>
						<input type="radio" style="vertical-align:middle" id="Radio_Picked_'.$row['ID_PROJECT'].'" name="Picked_ID" value="'.$row['ID_PROJECT'].'">
					</td>
				';
				
			if ($previous_otp!="" && $previous_otp==$row['WBS_PROJECT'])
			{
				$styl="color:white;border-top:1PX white solid";
				$pm_val="";
			} else {
				$styl="";
				$pm_val="";
			}
			
			$previous_otp=$row['WBS_PROJECT'];
			echo '<td style="'.$styl.'">'.$row['WBS_PROJECT'].'</td>';
			echo '
				</td>
				<td style="'.$styl.'">'.$row['PROJECT_TITLE'].'
				</td>
				<td>
					'.$row['WBS_PHASE'].'
				</td>
				<td>
					'.$row['PHASE_TITLE'].'
				</td>
				<td>
					'.$row['COMMENTS'].'
				</td>
			</tr>';		
		}

		$mysqli_ts->close();

?>
	



</table>


</td>
</tr>
</table>	

</body>
</html>

