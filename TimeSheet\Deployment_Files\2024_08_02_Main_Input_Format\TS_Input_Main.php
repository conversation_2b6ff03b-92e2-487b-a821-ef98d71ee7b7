﻿<!DOCTYPE html>
<html>

<head>
	<meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!--<meta name="viewport" content="width=device-width, initial-scale=1">-->
    <meta charset="utf-8" />
	
	<link rel="stylesheet" type="text/css" href="TS_vertical_tab.css">
    <link rel="stylesheet" type="text/css" href="TS_Input_Styles.css">
	
	<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
	<link rel="stylesheet" type="text/css" href="Common_Button_styles.css">
	
<link rel="icon" type="image/png" href="/Common_Resources/icon_TS.png" />

	
</head>

<title>TimeSheet Tool</title>

<script>


	// MISE A JOUR DES INFO LIE A L'UTILISATEUR CHOISI
	// -----------------------------------------------
	function User_Info_Auto_Update() {
		
	  const xhttp = new XMLHttpRequest();
	  xhttp.onload = function() {
		const raw_result=this.responseText.trim();
		split_result=raw_result.split("/");
		document.getElementById("fullname_select").value = split_result[0];
		document.getElementById("workcenter_select").value = split_result[1];
		document.getElementById("workcenter_title").value = split_result[2];
		activity_autofill_function();
		
		
		// MISE A JOUR DE LA LISTE DES IMPUTATIONS POUR L'UTILISATEUR CHOISI
		document.getElementById("Overview_Iframe").src='TS_Input_Overview_Iframe.php?act=&ID=&fullname=' + document.getElementById('fullname_select').value + '&period=' + document.getElementById('Period_ID').value ;
		
	  }
	  
	  xhttp.open("GET", "TS_Fullname_auto.php?SCM_ID="+document.getElementById("scm_id").value + "&root=userid" + "&workcenter=" + document.getElementById("workcenter_select").value);
	  xhttp.send();
	}
	
	
	// MISE A JOUR DES INFO ASSOCIEES AU WORKCENTER CHOISI
	// ---------------------------------------------------
	function Workcenter_Info_Update() {
	  const xhttp = new XMLHttpRequest();
	  xhttp.onload = function() {						// MISE A JOUR DU TITRE DU WORKCENTER SI UN WORCENTER SPECIFIQUE A ETE SELECTIONNE 
		document.getElementById("workcenter_title").value = this.responseText.trim();
		activity_autofill_function();
	  }
	  
	  xhttp.open("GET", "TS_Fullname_auto.php?SCM_ID="+document.getElementById("scm_id").value + "&root=workcenter" + "&workcenter=" + document.getElementById("workcenter_select").value);
	  xhttp.send();
	}


	function list_update()
	{
		 document.getElementById("activity_autofill").value="";
		 document.getElementById("picked_phase").value="";
		 document.getElementById("picked_phase_title").value="";
		
		Project_Info_Autofill();
		Phase_Info_Autofill();
		activity_autofill_function();
	}
	

	function Phase_Info_Autofill()
	{
		 document.getElementById("Phase_Iframe").src = 'TS_Input_Phase_Iframe.php?OTP='+document.getElementById("project_select").value;
		 
	}

	function Project_Info_Autofill()
	{
		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				document.getElementById("project_title").value = this.responseText.trim();
			}
		  xhttp.open("GET", "TS_Project_auto.php?OTP="+document.getElementById("project_select").value);
		  xhttp.send();	
	}
	
	

	
	// MISE A JOUR DES INFO LIE A L'UTILISATEUR CHOISI
	// -----------------------------------------------
	function activity_autofill_function() {
	
	const workcenter=document.getElementById("workcenter_select").value;
	const phase=document.getElementById("picked_phase").value;
	const project=document.getElementById("project_select").value;
	//alert(workcenter + '    ' + phase  + '    ' + project);
	if (workcenter!="" && phase!="" && project!="")
		{
		  const xhttp = new XMLHttpRequest();
		  xhttp.onload = function() {
			document.getElementById("activity_autofill").value = this.responseText.trim();
		  }
		  
		  xhttp.open("GET", "TS_Activity_auto.php?workcenter="+ workcenter + "&picked_phase=" + phase + "&project=" + project);
		  xhttp.send();
		} else {
			document.getElementById("activity_autofill").value="";
			}
	}

	
	// VERIFICATION DU PASSWORD RENTRE POUR ALLER DANS LA PAGE ADMINISTRATION
	// ----------------------------------------------------------------------
	function Admin_PWD_Check()
	{
		const xhttp = new XMLHttpRequest();
		  xhttp.onload = function()
			{
				var mdp=prompt("Password","");
				if (mdp!="")
				{
					pwd= this.responseText.trim();
					if (mdp == pwd)
					{
						const url="TS_Admin_Form.php";
						window.open(url);
					} else if (mdp!="" && mdp!=null)
							{
							window.alert("Incorrect Password!");
							} else {
							}
				}
			}
		  xhttp.open("GET", "TS_Admin_PWD.php");
		  xhttp.send();	
	}
	
	function pass_display()
	{
		if (document.getElementById("pass_input")!="")
		{
			document.getElementById("pass_zone").hidden=false;
			document.getElementById("admin_pass_button").hidden=true;
			document.getElementById("pass_input").focus();
		}
	}
	// ----------------------------------------------------------------------
	
	// OUVERTURE DE LA BONNE PRATIQUE D'AIDE
	// -------------------------------------
	function help_opening()
	{
		var url=window.open("Resources/FIN-01_A_Regles_Collecte_Heures_Projets.pdf");
		setTimeout(function(){ url.document.title = 'Bonne Pratique FIN-01'; }, 200);
	}
	
	
	
	// VERIFICATION QUE L'OUTIL EST OUVERT POUR LES IMPUTATIONS
	// --------------------------------------------------------
	function tool_openning_period_check()
	{
		const closure_date=new Date(document.getElementById("Closure_Date").value);
		const opening_date=new Date(document.getElementById("Opening_Date").value);
		const today=new Date();
		
		closure_date.setHours(23); // Cloture au jour J @ 23h
		opening_date.setHours(1);  // Ouverture au jour J @ 1h du matin
		
		if (today>=opening_date && today<=closure_date)
		{
			document.getElementById("Add_Time_Iframe").style.display = "block";
			document.getElementById("opened_tool_message").style.display = "none"
		} else {			
			document.getElementById("Add_Time_Iframe").style.display = "none";
			document.getElementById("opened_tool_message").style.display = "block"
		}
		
	}
	
	
	// OUVERTURE DE LA PAGE CONTENANT TOUTES LES PHASES OUVERTES ET PERMETTANT D'AJOUTER DES COMMENTAIRES
	function project_phase_comment()
	{
		window.open("TS_Phase_Comments.php?root=")
	}

</script>

<body>

<div class="w3-container w3-blue-grey">
	<h1>Outil d'Imputation</h1>
</div>

<img src="\Common_Resources\Logo_SCM_2024_W_1.png" height="62px" style="background-color:transparent;position: absolute; top:2px; padding-right:4px; right:0px;filter:grayscale(20%)" >

<form name="main_imput" method="post" action="" enctype="multipart/form-data">
<table id="t01" border=0 >
	<tr>
		<td style="background-color:#f1f1f1; vertical-align:top; width:140px; ">
			<table id="t02" border=0>
				<tr>
					<td style="font-size:11px;padding-top:10px;">
						<b>Identifiant SCM</b>
					</td>
				</tr>
				<tr>
					<td>
					<select id="scm_id" name="scm_id" style="font-size:11px;" onchange="User_Info_Auto_Update()" title="Identifiant SCM <premiere lettre du prénom><nom de famille>">
						<option value="none" SELECTED></option>
						<?php
							include('../TimeSheet_Connexion_DB.php');
							$requete = 'SELECT SCM_ID FROM tbl_user order by SCM_ID ASC';
							$resultat = $mysqli_ts->query($requete);
							while ($row = $resultat->fetch_assoc())
							{
								echo'<option value ="'.$row['SCM_ID'].'" >'.$row['SCM_ID'].'</option><br/>';
							}
							$mysqli_ts->close();
							?>
						</select>
					</td>
				</tr>
				<tr>
					<td>
						<input type="text" id="fullname_select" name="fullname_select" style="text-align:center;width:100px;font-size:10px;background-color:transparent; border:none" readonly>
					</td>
				</tr>
				<tr>
					<td style="font-size:11px;padding-top:5px">
						<b>Poste de travail</b>
					</td>
				</tr>
				<tr>
					<td>
						<select name="workcenter_select" id="workcenter_select" style="font-size:11px;background-color:transparent; width:77px;" onchange="Workcenter_Info_Update()"  title="Poste de travail sur lequel l'imputation sera faite - Laisser par défaut la valeur donnée par l'outil">
							<option value="none" SELECTED></option>
							<?php
							include('../TimeSheet_Connexion_DB.php');
							$requete = 'SELECT DISTINCT Workcenter, Description FROM tbl_workcenter ';
							$resultat = $mysqli_ts->query($requete);
							while ($row = $resultat->fetch_assoc())
							{
								echo'<option value ="'.$row['Workcenter'].'">'.$row['Workcenter'].' - '.$row['Description'].'</option><br/>';
							}
							$mysqli_ts->close();
							?>
						</select>
					</td>
				</tr>
				<tr>
					<td>
						<textarea id="workcenter_title" name="workcenter_title" style="text-align:center;width:100px;font-size:9px;background-color:transparent; border:none; padding-bottom:10px;resize: none;" readonly></textarea>
					</td>
				</tr>
				<tr>
					<td>
						<hr style="width:100%">
					</td>
				</tr>
				
				<?php
					include('../TimeSheet_Connexion_DB.php');
					$requete = 'SELECT DISTINCT * FROM tbl_period ';
					$resultat = $mysqli_ts->query($requete);
					while ($row = $resultat->fetch_assoc())
					{
						echo '<tr>
								<td style="font-size:11px;min-width:120px; padding-top:0px; " title="Période pour laquelle les imputations sont ouvertes.">
									<b>Période</b>
									<input type="text" id="Period_ID" name="Period_ID" style="text-align:center;font-size:11px;width:100px;background-color:transparent; border:none;"   readonly value="'.$row['Month'].' '.$row['Year'].'">
								</td>
							</tr>
							<tr>
								<td style="font-size:11px;min-width:120px;padding-top:5px" title="Premiere jour de la période en cours d\'imputation" readonly>
									<b>Début</b>

									<input type="text" id="Start_Date_ID" name="Start_Date_ID" style="text-align:center;font-size:11px;;width:100px;background-color:transparent; border:none;"  value="'.$row['Start_Date'].'">
								</td>
							</tr>
							<tr>
								<td style="font-size:11px;min-width:120px;padding-top:5px" title="Dernier jour de la période en cours d\'imputation">
									<b>Fin</b>

									<input type="text" id="End_Date_ID" name="End_Date_ID" style="text-align:center;font-size:11px;;width:100px;background-color:transparent; border:none; "  readonly onchange="workingDaysBetweenDates()" value="'.$row['End_Date'].'">
								</td>
							</tr>
							<tr>
								<td title="Nombre indicatif de jours ouvrables sur la période d\'imputation" style="padding-top:5px;font-size:11px;vertical-align:bottom;min-width:120px;">
									<b>Jours Ouvrables</b>
									<input type="text" id="Working_Day_ID" name="Working_Day_ID" style="text-align:center;font-size:11px;;width:100px;background-color:transparent; border:none;" readonly value="'.$row['Working_Days'].'">
								</td>
							</tr>
							<tr>
								<td title="Année fiscale correspondant à la période d\'imputation" style="font-size:11px;min-width:120px;padding-top:5px">
									<b>Année Fiscale</b>
									<input type="text" id="FY_ID" name="FY_ID" style="text-align:center;font-size:11px;width:100px;background-color:transparent; border:none" readonly value="'.$row['Fiscal_Year'].'">
								</td>
							</tr>
							<tr>
								<td title="Date à laquelle l\'outil d\'imputaion est ouvert" style="font-size:11px;min-width:120px;padding-top:15px">
									<b>Date d\'Ouverture</b>
									<input type="text" id="Opening_Date" name="Opening_Date" style="text-align:center;font-size:11px;width:100px;background-color:transparent; border:none" readonly value="'.$row['Tool_Opening_Date'].'">
								</td>
							</tr>
							<tr>
								<td title="Date à laquelle l\'outil d\'imputaion sera fermé" style="font-size:11px;min-width:120px;padding-top:5px; padding-bottom:30px;">
									<b>Date de Clôture</b>
									<input type="text" id="Closure_Date" name="Closure_Date" style="text-align:center;font-size:11px;width:100px;background-color:transparent; border:none" readonly value="'.$row['Tool_Closure_Date'].'">
								</td>
							</tr>
							<tr>
							<td style="height:35px;vertical-align:bottom;text-align:left">
									<div style="text-indent:10px;font-size:9px;font-color:grey;font-style:italic">
										<img type="submit" class="btn transp" src="\Common_Resources\icon_list.png" style="height:20px;width:20px;filter:grayscale(20%)" onclick="project_phase_comment()">
										Phases Ouvertes
									</div>
								</td>
								
								
							
							</tr>
							<tr>
								<td style="height:35px;vertical-align:bottom;text-align:left">
									<div style="text-indent:10px;font-size:9px;font-color:grey;font-style:italic">
										<img type="submit" class="btn transp" src="\Common_Resources\icon_info.png" style="height:25px;width:25px;filter:grayscale(20%)" onclick="help_opening()">
										Bonne Pratique
									</div>
								</td>
							</tr>
							<tr>
								<td style="height:35px;vertical-align:bottom;text-align:left;margin-bottom:30px">
									<div style="text-indent:10px;font-size:9px;font-color:grey;font-style:italic">
										<img type="submit" class="btn transp" src="\Common_Resources\Admin_Cadena.png" style="height:25px;width:25px;filter:grayscale(20%)" onclick="Admin_PWD_Check()">
										Admin
									</div>
								</td>
							</tr>';
					}
					$mysqli_ts->close();
				?>
	
			</table>
		</td>

		<td style="width:100%; vertical-align:top;padding-bottom:-20px">
			<table id="t03" border=0 style="width:100%">		
				<tr>
					<td title="Code projet" style="text-align:left; width:9%;padding-top:10px;padding-left:10px;vertical-align:top">
						<div  style="font-size:11px;margin-bottom:3px">
							<b>Projet</b>:
						</div>
						<select name="project_select" id="project_select" size=19  style="font-size:11px;background-color:transparent;width:87%;min-width:70px;text-align:center" onchange="list_update()">
							
							<?php
								include('../TimeSheet_Connexion_DB.php');
								$requete = 'SELECT DISTINCT Project_Code 
											FROM tbl_project 
											WHERE 
												Level like "00"  
											AND LENGTH(Project_Code)=7 
											AND Left(Project_Code,2) like "P-" 
											ORDER BY Project_Code DESC';
								$resultat = $mysqli_ts->query($requete);
								while ($row = $resultat->fetch_assoc())
								{
									echo'<option value ="'.$row['Project_Code'].'">'.$row['Project_Code'].'</option><br/>';
								}
								$mysqli_ts->close();
							?>
						</select>
					</td>

					<td colspan=2 title="Phase(s) ouverte(s) aux imputations pour le projet selectionnée" style="padding-top:7px; text-align:left; vertical-align:top;padding-left:5px;">
						<div style="font-size:11px;margin-bottom:7px;">
							<b>Phase</b> pour le projet  
						
						<input type="text" name="project_title" id="project_title" style="width:350px;background-color:transparent;border:none; text-align:left;font-size:9pt" readonly>

							
							
						</div>
						
						<iframe  src="TS_Input_Phase_Iframe.php" id="Phase_Iframe" scrolling="yes" frameborder="0" height="271px" width="85%" style="margin-top:-4px;border:1px solid black"></iframe>
					</td>
				</tr>
				<tr>
					<td colspan=2 style="text-align:left">
			
						<input type="text" name="picked_phase" id="picked_phase" style="border:none; height:10px; font-size:9px;color:grey; font-style:italic; width:100px" readonly onchange="activity_autofill_function()" hidden>
						<input type="text" name="picked_phase_title" id="picked_phase_title" style="border:none; height:10px; font-size:9px;color:grey; font-style:italic; width:160px" hidden>
						
						<iframe src="TS_Action_Add_Time.php" id="Add_Time_Iframe" name="Add_Time_Iframe" scrolling="no" frameborder="0" height="40px" width="400px" style=""></iframe>
						<div id="opened_tool_message" style="text-align:center;color:rgb(0 , 105,180);font-weight:bold;font-variant:small-caps;background-color:rgb(253 , 195 , 0);border-radius:10px">LES IMPUTATIONS SONT CLOTUREES POUR LE MOIS EN COURS.</div>
						<script>tool_openning_period_check()</script>
						

					</td>
					<td style="font-size:11px;margin-top:5px; text-align:center;padding-left:5px;">
						<b>Activité</b>

						<input type="text" name="activity_autofill" id="activity_autofill" style="width:150px;background-color:transparent;border:none;font-color:grey; text-align:left;font-size:9pt;font-style:italic" readonly>
					</td>
				</tr>

				<tr>
					<td colspan=5>
						<hr style="width:80%; margin-top:10px; margin-left:143px">
					</td>
				</tr>
				<tr>
					<td colspan=2 style="font-size:11px;text-align:left;padding-left:8px;";>
						<b>Ensemble des imputations pour la période en cours<b>
					</td>
				</tr>
				<tr>
					<td colspan=5 title="Résumé des imputations pour la période en cours et l'utilisateur choisi" style="text-align:left; height:180px">
						<iframe src="" id="Overview_Iframe" scrolling="yes" frameborder="0" height="100%" width="88%" ></iframe>
					</td>
				</tr>
			</table>
		</td>			
	</tr>
	<tr>
		<td colspan=3 style="padding-left:0px; padding-right:10px;">
			<div id="footer">SCM - Systèmes et Connectique du Mans - Time Sheet Tool - <a href="Deployment_Files/Deployment_Log.txt" target="#">v1.3</a></div>
		</td>
	</tr>
</table>


</form>
</body>
</html>
