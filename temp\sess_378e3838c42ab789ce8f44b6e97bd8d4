_sf2_attributes|a:1:{s:14:"_security_main";s:4651:"O:74:"Symfony\Component\Security\Core\Authentication\Token\PreAuthenticatedToken":3:{i:0;N;i:1;s:4:"main";i:2;a:5:{i:0;O:15:"App\Entity\User":31:{s:19:" App\Entity\User id";i:147;s:22:" App\Entity\User email";s:22:"<EMAIL>";s:25:" App\Entity\User username";s:8:"eerdmann";s:20:" App\Entity\User nom";s:7:"<PERSON>rd<PERSON>";s:23:" App\Entity\User prenom";s:4:"Enzo";s:22:" App\Entity\User roles";a:1:{i:0;s:22:"ROLE_USER_INFORMATIQUE";}s:25:" App\Entity\User password";s:9:"LeMans72!";s:24:" App\Entity\User manager";s:14:"C<PERSON><PERSON> Antoine";s:22:" App\Entity\User titre";s:31:"Alternant en Développement Web";s:26:" App\Entity\User isManager";b:0;s:28:" App\Entity\User departement";s:12:"Informatique";s:40:" App\Entity\User documentStatusHistories";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:22:" App\Entity\User visas";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:33:" App\Entity\User releasedPackages";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:29:" App\Entity\User commentaires";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:30:" App\Entity\User verifPackages";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:30:" App\Entity\User validPackages";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:26:" App\Entity\User documents";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:21:" App\Entity\User dMOs";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:31:" App\Entity\User dMos_Eng_Owner";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:38:" App\Entity\User Last_Modificator_Dmos";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:25:" App\Entity\User projects";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:24:" App\Entity\User imputes";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:27:" App\Entity\User imputation";N;s:19:" App\Entity\User ci";N;s:20:" App\Entity\User sap";N;s:27:" App\Entity\User workCenter";N;s:30:" App\Entity\User managedPlaces";a:0:{}s:28:" App\Entity\User preferences";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}s:29:" App\Entity\User lastLdapSync";O:8:"DateTime":3:{s:4:"date";s:26:"2025-06-30 10:49:20.000000";s:13:"timezone_type";i:3;s:8:"timezone";s:12:"Europe/Paris";}s:30:" App\Entity\User qualDocuments";O:33:"Doctrine\ORM\PersistentCollection":2:{s:13:" * collection";O:43:"Doctrine\Common\Collections\ArrayCollection":1:{s:53:" Doctrine\Common\Collections\ArrayCollection elements";a:0:{}}s:14:" * initialized";b:0;}}i:1;b:1;i:2;N;i:3;a:0:{}i:4;a:2:{i:0;s:22:"ROLE_USER_INFORMATIQUE";i:1;s:9:"ROLE_USER";}}}";}_sf2_meta|a:3:{s:1:"u";i:1751360574;s:1:"c";i:1751352243;s:1:"l";i:0;}